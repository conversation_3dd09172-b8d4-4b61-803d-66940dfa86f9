/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* init.go - File Description */
/*
Modification History
--------------------
2022/5/28, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package main

import (
	"context"
	"log"
	"os"

	"github.com/urfave/cli/v2"

	"icode.baidu.com/baidu/scs/x1-task/cmd/metaservercmds/handlers"
	"icode.baidu.com/baidu/scs/x1-task/cmd/metaservercmds/resource"
)

func runCmds(ctx context.Context) error {
	app := cli.NewApp()
	app.EnableBashCompletion = true
	app.Flags = []cli.Flag{
		&cli.StringFlag{
			Name:        "app-config",
			Usage:       "app config file",
			Destination: &resource.AppConfig,
			DefaultText: "./conf/app.toml",
		},
	}
	flags := []cli.Flag{
		&cli.BoolFlag{
			Name:        "use-raw-commands",
			Usage:       "use raw commands instead of using db data",
			Destination: &resource.UseRawArgs,
		},
		&cli.StringFlag{
			Name:        "metaserver-address",
			Usage:       "metaserver address[ip:port]; used when --use-raw-commands is true",
			Destination: &resource.MetaserverAddress,
			EnvVars: []string{
				"METASERVER_ADDRESS",
			},
		},
		&cli.StringFlag{
			Name:        "metaserver-auth",
			Usage:       "metaserver auth; used when --use-raw-commands is true",
			Destination: &resource.MetaserverAuth,
			EnvVars: []string{
				"METASERVER_AUTH",
			},
		},
	}
	app.Commands = []*cli.Command{
		{
			Name:      "maset",
			Category:  "MetaServerCommands",
			Usage:     "bind cluster to app",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [app_id] [cluster_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "maget",
			Category:  "MetaServerCommands",
			Usage:     "get app bindings",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [app_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "madel",
			Category:  "MetaServerCommands",
			Usage:     "del app bindings",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [app_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mpset",
			Category:  "MetaServerCommands",
			Usage:     "set proxy",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [proxy_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mpget",
			Category:  "MetaServerCommands",
			Usage:     "get proxy",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [proxy_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mpdel",
			Category:  "MetaServerCommands",
			Usage:     "delete proxy",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [proxy_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcset",
			Category:  "MetaServerCommands",
			Usage:     "add cluster",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id], [proxy_id], [port], [flags_master_failover], [flags_master_read], [flags_slave_read]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcget",
			Category:  "MetaServerCommands",
			Usage:     "get cluster detail",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id]",
			Action:    handlers.McgetHandler,
		},
		{
			Name:      "mcdel",
			Category:  "MetaServerCommands",
			Usage:     "delete cluster",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcflagset",
			Category:  "MetaServerCommands",
			Usage:     "set cl",
			Flags:     flags,
			ArgsUsage: "[application.app_id] [flag] [flag val] | --use-raw-commands [cluster_id] [flag] [flag val]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcshrink",
			Category:  "MetaServerCommands",
			Usage:     "shrink cluster",
			Flags:     flags,
			ArgsUsage: "[application.app_id] [count] | --use-raw-commands [cluster_id] [count]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcbalaceset",
			Category:  "MetaServerCommands",
			Usage:     "set auto balance flag",
			Flags:     flags,
			ArgsUsage: "[application.app_id] [flag] | --use-raw-commands [cluster_id] [flag]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mccheckscalestatus",
			Category:  "MetaServerCommands",
			Usage:     "check scale status",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id] ",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mccheckscalestatus",
			Category:  "MetaServerCommands",
			Usage:     "check scale status",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id] ",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcinit",
			Category:  "MetaServerCommands",
			Usage:     "init cluster slot distribution",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id] ",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mslotupdate",
			Category:  "MetaServerCommands",
			Usage:     "force update cluster slot distribution",
			Flags:     flags,
			ArgsUsage: "[application.app_id] [slot distribution]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcinit",
			Category:  "MetaServerCommands",
			Usage:     "init cluster slot distribution",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id] ",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mcdel",
			Category:  "MetaServerCommands",
			Usage:     "delete cluster",
			Flags:     flags,
			ArgsUsage: "[application.app_id] | --use-raw-commands [cluster_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "msset",
			Category:  "MetaServerCommands",
			Usage:     "set shard",
			Flags:     flags,
			ArgsUsage: "[cluster.cluster_id] | --use-raw-commands [shard_id] [cluster_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "msget",
			Category:  "MetaServerCommands",
			Usage:     "get shard",
			Flags:     flags,
			ArgsUsage: "[cluster.cluster_id] | --use-raw-commands [shard_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "msdel",
			Category:  "MetaServerCommands",
			Usage:     "delete shard",
			Flags:     flags,
			ArgsUsage: "[cluster.cluster_id] | --use-raw-commands [shard_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "msmasterchange",
			Category:  "MetaServerCommands",
			Usage:     "change shard master",
			Flags:     flags,
			ArgsUsage: "[cluster.cluster_id] [node.node_id] | --use-raw-commands [shard_id] [redis_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "msdel",
			Category:  "MetaServerCommands",
			Usage:     "delete shard",
			Flags:     flags,
			ArgsUsage: "[cluster.cluster_id] | --use-raw-commands [shard_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mpiset",
			Category:  "MetaServerCommands",
			Usage:     "set proxy instance",
			Flags:     flags,
			ArgsUsage: "[proxy.proxy_id] | --use-raw-commands [proxy_inst_id] [proxy_id] [floating_ip,fix_ip] [cdn]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mpiget",
			Category:  "MetaServerCommands",
			Usage:     "get proxy instance",
			Flags:     flags,
			ArgsUsage: "[proxy.proxy_id] | --use-raw-commands [proxy_inst_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mpidel",
			Category:  "MetaServerCommands",
			Usage:     "delete proxy instance",
			Flags:     flags,
			ArgsUsage: "[proxy.proxy_id] | --use-raw-commands [proxy_inst_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mriset",
			Category:  "MetaServerCommands",
			Usage:     "set redis instance",
			Flags:     flags,
			ArgsUsage: "[node.node_id] | --use-raw-commands [redis_id] [node_id] [floating_ip,fix_ip] [port] [role] [priority] [cdn]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mriget",
			Category:  "MetaServerCommands",
			Usage:     "get redis instance",
			Flags:     flags,
			ArgsUsage: "[node.node_id] | --use-raw-commands [redis_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "mridel",
			Category:  "MetaServerCommands",
			Usage:     "delete redis instance",
			Flags:     flags,
			ArgsUsage: "[node.node_id] | --use-raw-commands [redis_id]",
			Action:    handlers.PrintHandler,
		},
		{
			Name:      "process_initcluster",
			Category:  "ProcessCommands",
			Usage:     "process init cluster",
			Flags:     flags,
			ArgsUsage: "[application.app_id]",
			Action:    handlers.ProcessInitCluster,
		},
		{
			Name:      "find-metaserer",
			Category:  "MetaServer Management",
			Usage:     "find metaserver and create a metaserver cluster in db",
			Flags:     flags,
			ArgsUsage: "[name] [type: scs|scs_slot] [address: ip:port] [auth]",
			Action:    handlers.FindMetaserverHandler,
		},
		{
			Name:      "list-metaserer",
			Category:  "MetaServer Management",
			Usage:     "list metaservers in b",
			Flags:     flags,
			ArgsUsage: "[name] [type: scs|scs_slot] [address: ip:port] [auth]",
			Action:    handlers.PrintHandler,
		},
	}
	err := app.RunContext(ctx, os.Args)
	if err != nil {
		log.Fatal(err)
	}
	return nil
}
