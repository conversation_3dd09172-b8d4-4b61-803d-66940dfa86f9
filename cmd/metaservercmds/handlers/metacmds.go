/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* metacmds.go - File Description */
/*
Modification History
--------------------
2022/5/29, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package handlers

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/urfave/cli/v2"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/cmd/metaservercmds/resource"
	pmetaserver "icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
)

func PrintHandler(c *cli.Context) error {
	fmt.Println("cmd name:", c.Command.Name)
	fmt.Println("UseRawArgs:", resource.UseRawArgs)
	fmt.Println("All Args", c.Args().Slice())
	return nil
}

func rawHandler(c *cli.Context) error {
	chunks := strings.Split(resource.MetaserverAddress, ":")
	port, err := strconv.Atoi(chunks[1])
	if err != nil {
		return err
	}
	cli := single_redis.NewClient(chunks[0], port, single_redis.WithPassword(resource.MetaserverAuth))
	defer cli.Close()
	cmds := []any{c.Command.Name}
	for _, arg := range c.Args().Slice() {
		cmds = append(cmds, arg)
	}
	ret := cli.Do(c.Context, cmds...)
	if ret.Err() != nil {
		return ret.Err()
	}
	fmt.Printf("Cmd: %s, Args: %v, Result:\n", c.Command.Name, c.Args().Slice())
	fmt.Println(ret.String())
	return nil
}

func getMetaCli(ctx context.Context, app *x1model.Application) (*metaserver.MetaserverClient, error) {
	metaName := app.LocalMetaserver
	if len(app.AppGroupID) != 0 {
		metaName = app.GlobalMetaserver
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, metaName)
	if err != nil {
		return nil, err
	}
	return metaserver.GetMetaserverClient(ctx, metaCluster)
}

func McgetHandler(c *cli.Context) error {
	if resource.UseRawArgs {
		return rawHandler(c)
	}
	var appId string = c.Args().Get(0)
	app, err := x1model.ApplicationGetByAppId(c.Context, appId)
	if err != nil {
		return err
	}
	metaCli, err := getMetaCli(c.Context, app)
	if err != nil {
		return err
	}
	ret, err := metaCli.GetCluster(c.Context, app.AppShortID)
	if err != nil {
		return err
	}
	fmt.Printf("Cmd: %s, Args: %v, Result:\n", c.Command.Name, c.Args().Slice())
	fmt.Println(base_utils.Format(ret))
	return nil
}

func ProcessInitCluster(c *cli.Context) error {
	return pmetaserver.ProcessInitClusterLocal(c.Context, &workflow.TaskExecUnit{
		Entity: c.Args().Get(0),
	})
}
