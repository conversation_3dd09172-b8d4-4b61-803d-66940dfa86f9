/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* handlers.go - File Description */
/*
Modification History
--------------------
2022/5/28, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package handlers

import (
	"context"
	"errors"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/urfave/cli/v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
)

func getRegionAndZone(ctx context.Context, ip string) (string, string, error) {
	cmd := exec.Command("whoami")
	output, err := cmd.Output()
	if err != nil {
		return "", "", err
	}
	chunks := strings.Split(string(output), " ")
	host := chunks[len(chunks)-1]
	chunks = strings.Split(host, ".")
	azone := chunks[len(chunks)-4]
	regions := []string{"bj", "gz", "sz", "bd", "hkg", "wh", "sin"}
	for _, region := range regions {
		if strings.HasPrefix(azone, region) {
			return region, azone, nil
		}
	}
	return "", "", fmt.Errorf("unknown region and zone for ip: %s", ip)
}

func FindMetaserverHandler(c *cli.Context) error {
	ctx := c.Context
	name := c.Args().Get(0)
	metaType := c.Args().Get(1)
	address := c.Args().Get(2)
	auth := c.Args().Get(3)
	chunks := strings.Split(address, ":")
	port, err := strconv.Atoi(chunks[1])
	if err != nil {
		return err
	}

	metaCluster := &x1model.MetaCluster{
		MetaClusterID: name,
		Name:          name,
		Desc:          "",
		Status:        "inuse",
		Type:          metaType,
		UserId:        "",
		Engine:        "",
		Region:        "",
		EngineVersion: "",
		Entrance:      address,
		CurMaster:     "",
		Quorum:        0,
		CreatedAt:     time.Time{},
		UpdatedAt:     time.Time{},
		Password:      auth,
	}
	curMaster := &x1model.MetaNode{
		MetaNodeID:      metaCluster.MetaClusterID + ".0",
		MetaClusterID:   metaCluster.MetaClusterID,
		Status:          "inuse",
		Role:            "master",
		Engine:          "",
		EngineVersion:   "",
		Port:            port,
		Region:          "",
		LogicZone:       "",
		Azone:           "",
		VpcId:           "",
		SubnetId:        "",
		Basedir:         "",
		Datadir:         "",
		Ip:              "",
		FloatingIP:      "",
		IPv6:            "",
		ResourceOrderId: "",
		ResourceId:      "",
		CreateTime:      time.Time{},
		UpdateTime:      time.Time{},
	}
	metaCluster.CurMaster = curMaster.MetaNodeID
	metaCluster.MetaNodes = append(metaCluster.MetaNodes, curMaster)

	cli := single_redis.NewClient(chunks[0], port, single_redis.WithPassword(auth))
	info, err := single_redis.GetInfo(ctx, cli)
	if err != nil {
		return err
	}

	// 通过从节点获取主meta的真实ip信息
	if info.Role == "master" {
		metaCluster.CurMaster = address
		if len(info.Slaves) == 0 {
			return errors.New("no slave found")
		}
		cli = single_redis.NewClient(info.Slaves[0].IP, info.Slaves[0].Port, single_redis.WithPassword(auth))
	}
	info, err = single_redis.GetInfo(ctx, cli)
	curMaster.Ip = info.MasterHost
	curMaster.Port = info.MasterPort
	curMaster.Region, curMaster.Azone, err = getRegionAndZone(ctx, curMaster.Ip)
	if err != nil {
		return err
	}

	cli = single_redis.NewClient(curMaster.Ip, curMaster.Port, single_redis.WithPassword(auth))
	info, err = single_redis.GetInfo(ctx, cli)
	idx := 1
	for _, slave := range info.Slaves {
		slaveNode := &x1model.MetaNode{
			MetaNodeID:      metaCluster.MetaClusterID + "." + strconv.Itoa(idx),
			MetaClusterID:   metaCluster.MetaClusterID,
			Status:          "inuse",
			Role:            "slave",
			Engine:          "",
			EngineVersion:   "",
			Port:            slave.Port,
			Region:          "",
			LogicZone:       "",
			Azone:           "",
			VpcId:           "",
			SubnetId:        "",
			Basedir:         "",
			Datadir:         "",
			Ip:              slave.IP,
			FloatingIP:      "",
			IPv6:            "",
			ResourceOrderId: "",
			ResourceId:      "",
			CreateTime:      time.Time{},
			UpdateTime:      time.Time{},
		}
		slaveNode.Region, slaveNode.Azone, err = getRegionAndZone(ctx, slaveNode.Ip)
		if err != nil {
			return err
		}
		idx++
	}
	if err := x1model.MetaClustersSave(ctx, []*x1model.MetaCluster{metaCluster}); err != nil {
		return err
	}
	return nil
}
