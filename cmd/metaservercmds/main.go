/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* main.go - File Description */
/*
Modification History
--------------------
2022/5/28, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package main

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-task/bootstrap"
)

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	bootstrap.MustLoadAppConfig("./conf/app.toml")
	bootstrap.MustInit(ctx)
	defer bootstrap.BeforeShutdown()
	runCmds(ctx)
}
