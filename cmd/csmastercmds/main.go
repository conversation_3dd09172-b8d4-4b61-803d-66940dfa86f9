/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/02/11, by <PERSON><PERSON>(<EMAIL>), create
*/
/*
DESCRIPTION
Base Dao
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"

	"icode.baidu.com/baidu/scs/x1-task/bootstrap"
)

var (
	// 通用参数
	appConfig string
	appId     string
	userId    string

	// acl 参数
	aclCmd      = flag.NewFlagSet("acl", flag.ExitOnError)
	aclAction   = aclCmd.String("aclaction", "", "acl action create|modify_passwd|modify_authority|modify_extra")
	aclUserName = aclCmd.String("acluser", "", "acl user name")
	aclUserType = aclCmd.Int("acltype", 1, "acl user type; 1 admin, 2 readonly")
	aclPassword = aclCmd.String("aclpassword", "", "acl user password, must encrypt by rsa")
	aclComment  = aclCmd.String("aclcomment", "", "comment")

	// upgrade 参数
	upgradeCmd           = flag.NewFlagSet("upgrade", flag.ExitOnError)
	upgradeKernalVersion = upgradeCmd.String("tversion", "", "targe kernal version for upgrade")

	// auth 参数
	modifyClientAuthCmd    = flag.NewFlagSet("clientauth", flag.ExitOnError)
	modifyClientAuthPasswd = modifyClientAuthCmd.String("password", "", "password must encrypt by rsa")

	// restart 参数
	restartCmd = flag.NewFlagSet("restart", flag.ExitOnError)
)

func setCommonFlags() {
	for _, fs := range []*flag.FlagSet{aclCmd, upgradeCmd, modifyClientAuthCmd, restartCmd} {
		fs.StringVar(
			&appConfig,
			"appconfig",
			"./conf/app.toml",
			"app config",
		)
		fs.StringVar(
			&appId,
			"appid",
			"",
			"app id",
		)
		fs.StringVar(
			&userId,
			"userid",
			"",
			"user id",
		)
	}
}

func initAll(ctx context.Context, appConfig string) {
	bootstrap.MustLoadAppConfig(appConfig)
	bootstrap.MustInit(ctx)
}

func main() {
	setCommonFlags()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	defer bootstrap.BeforeShutdown()

	switch os.Args[1] {
	case "acl":
		aclCmd.Parse(os.Args[2:])
		initAll(ctx, appConfig)
		if err := csmaster.CsmasterOp().CsmasterAclActions(ctx, &csmaster.CsmasterAclActionsParams{
			UserID:    userId,
			AppID:     appId,
			AclAction: *aclAction,
			UserName:  *aclUserName,
			UserType:  *aclUserType,
			Password:  *aclPassword,
			Extra:     *aclComment,
		}); err != nil {
			fmt.Println(err.Error())
		} else {
			fmt.Println("Success")
		}
	case "upgrade":
		upgradeCmd.Parse(os.Args[2:])
		initAll(ctx, appConfig)
		if err := csmaster.CsmasterOp().CsmasterSetExpectVersion(ctx, &csmaster.CsmasterSetExpectVersionParams{
			UserID:        userId,
			AppID:         appId,
			UpdateType:    csmaster.CsmasterSetExpectVersionLatest,
			KernelVersion: *upgradeKernalVersion,
		}); err != nil {
			fmt.Println(err.Error())
		} else {
			fmt.Println("Success")
		}
	case "restart":
		restartCmd.Parse(os.Args[2:])
		initAll(ctx, appConfig)
		if err := csmaster.CsmasterOp().CsmasterSetExpectVersion(ctx, &csmaster.CsmasterSetExpectVersionParams{
			UserID:     userId,
			AppID:      appId,
			UpdateType: csmaster.CsmasterSetExpectVersionRelaunch,
		}); err != nil {
			fmt.Println(err.Error())
		} else {
			fmt.Println("Success")
		}
	case "clientauth":
		modifyClientAuthCmd.Parse(os.Args[2:])
		initAll(ctx, appConfig)
		if err := csmaster.CsmasterOp().CsmasterModifyClientAuth(ctx, &csmaster.CsmasterModifyClientAuthParam{
			UserID:   userId,
			AppID:    appId,
			Password: *modifyClientAuthPasswd,
		}); err != nil {
			fmt.Println(err.Error())
		} else {
			fmt.Println("Success")
		}
	default:
		fmt.Println("use acl|upgrade|restart|clientauth")
	}
}
