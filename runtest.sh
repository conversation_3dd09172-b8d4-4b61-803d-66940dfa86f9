#!/bin/bash

ROOT_PATH="$(cd $(dirname $0);pwd)"
cd "${ROOT_PATH}"

UNITTEST_ENV="${ROOT_PATH}/unittest-env"
REDIS_BIN_NAME="redis-server-$(cat /proc/sys/kernel/random/uuid | md5sum | cut -c 1-9)"
REDIS_PORT="9889"

[[ -d "${UNITTEST_ENV}" ]] || mkdir -p "${UNITTEST_ENV}"

if [[ ! -d "${UNITTEST_ENV}/output" ]]; then
    wget -O "${UNITTEST_ENV}/output.tar.gz" --no-check-certificate --header "IREPO-TOKEN:8cbc4b4f-3d40-4e9e-9938-a23fd6d10130" "https://irepo.baidu-int.com/rest/prod/v3/baidu/third-party/redis/releases/3.0.10.2/files"
    tar -zxvf "${UNITTEST_ENV}/output.tar.gz" -C "${UNITTEST_ENV}/"
fi

cp "${UNITTEST_ENV}/output/bin/redis-server" "${UNITTEST_ENV}/${REDIS_BIN_NAME}"
chmod +x "${UNITTEST_ENV}/${REDIS_BIN_NAME}"
"${UNITTEST_ENV}/${REDIS_BIN_NAME}" --port "${REDIS_PORT}" &
mkdir log_ut

make test

ps -ef | grep "${REDIS_BIN_NAME}" | grep -v grep | awk '{print $2}' | xargs kill -9
