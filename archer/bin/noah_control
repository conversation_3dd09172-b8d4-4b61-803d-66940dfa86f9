#!/bin/bash
RUN_BIN="start.sh"
NOT_CHECK=0
RETRY_LIMIT=15
cd `dirname $0`
work_dir=$( dirname `pwd`)
work_dir_physical=$( dirname `pwd -P`)
echo "work_dir: ${work_dir}"
bin_exe="${work_dir}/bin/${RUN_BIN}"
core_bin="x1-task"
core_bin_physical_path="${work_dir_physical}/bin/${core_bin}"
pid_file="${work_dir}/${core_bin}.pid"

echo "bin_exe: ${bin_exe}"
echo "pid_file: ${pid_file}"

start()
{
    echo "Start Process"
    if [ -f "${pid_file}" ]; then
        pid=`cat ${pid_file} | head -n 1`
        if [ ! -d "/proc/${pid}" ]; then
            echo "${core_bin}.pid exist, pid: ${pid}. The process isn't running, remove pid file."
            rm -f ${pid_file}
        else
            if [ -h "/proc/${pid}/exe" ]; then
                pid_path=`readlink -f /proc/${pid}/exe`
                if [ "X${pid_path}" == "X${core_bin_physical_path}" ]; then
                    echo "${core_bin}.pid exist, pid[${pid}]. The process[${pid_path}] is running!"
                    exit 2
                else
                    echo "${core_bin}.pid exist, pid[${pid}]. The process[${pid_path}] is running, but not ${core_bin_physical_path}, remove pid file."
                    rm -f ${pid_file}
                fi
            fi
        fi
    fi

    mkdir -p ${work_dir}/log
#    nohup ${bin_exe} ${IDC} </dev/null &>${work_dir}/${core_bin}.out & sleep 1
    nohup ${bin_exe} </dev/null &>${work_dir}/${core_bin}.out &
    sleep 3

    if [[ ${NOT_CHECK} -eq 1 ]];then
        return 0
    fi

    ((retry_count=0))
    while [[ retry_count -lt ${RETRY_LIMIT} ]];do
        sleep 1
        if [ -f "${pid_file}" ]; then
            pid=`cat ${pid_file} | head -n 1`
            if [ ! -d "/proc/${pid}" ]; then
                echo "${core_bin}.pid exist, pid: ${pid}. The process isn't running, wait and check again."
            else
                if [ -h "/proc/${pid}/exe" ]; then
                    pid_path=`readlink -f /proc/${pid}/exe`
                    if [ "X${pid_path}" == "X${core_bin_physical_path}" ]; then
                        echo "Start successfully! ${core_bin}.pid exist, pid[${pid}]. The process[${pid_path}] start running!"
                        return 0
                    else
                        echo "${core_bin}.pid exist, pid[${pid}]. The process[${pid_path}] is running, but not ${core_bin_physical_path}, remove pid file."
                        rm -f ${pid_file}
                    fi
                fi
            fi
        fi
        ((retry_count=retry_count+1))
    done

    echo "Not found ${pid_file} or target process is not running. Start failed."
    exit 1
}

clean()
{
    # check if process is running
    echo "" > ${work_dir}/${core_bin}.out
    rm -rf ${work_dir}/log/*
    echo "Clean successfully!"
}

stop()
{
    echo "Stop Process"
    if [ ! -f "${pid_file}" ]; then
        echo "${pid_file} not exist, Already Stopped!"
        return 0
    fi
    pid=`cat ${pid_file} | head -n 1`
    if [ ! -d "/proc/${pid}" ]; then
        echo "${core_bin}.pid exist, pid: ${pid}. The process isn't running, remove pid file."
        rm -f ${pid_file}
        return 0
    fi
    if [ -h "/proc/${pid}/exe" ]; then
        pid_path=`readlink -f /proc/${pid}/exe`
        if echo "${core_bin_physical_path}" | grep "${pid_path}" ;then
            echo "${core_bin}.pid exist, pid[${pid}]. The process[${pid_path}] is running!"
            kill -9 `cat ${pid_file}`
            while [ -d "/proc/${pid}" ];do
                sleep 0.1
                echo "wait process exit"
            done
            echo "Stop successfully!"
            rm ${pid_file}
            return 0
        else
            echo "${core_bin}.pid exist, pid[${pid}]. The process[${pid_path}] is running, but not ${core_bin_physical_path}, remove pid file."
            rm -f ${pid_file}
        fi
    fi
}

showpid()
{
    if [ -f ${pid_file} ];then
        pid=`cat ${pid_file}`
        echo $pid
        return $pid
    else
        return
    fi
}

replace()
{
    src=${bin_exe}
    dest=${bin_exe}.new
    if [[ x$2 != x"" ]];then
        dest=${1}
    fi

    if [[ -f ${dest} ]];then
        stop
        if [[ $? != 0 ]];then
            echo "replicing failed"
            return -1
        fi
        mv ${src} ${src}.bak.$(date +"%Y%m%d%H%M%S")
        mv ${dest} ${src}
        start
        return
    else
        echo "Can not found ${dest} binary file for update"
        return
    fi
}

status(){
    process_num=`ps aux | grep ${bin_exe}|grep -v grep| wc -l`;
    if [ "${process_num}" -gt "0" ]; then
        echo "${core_bin} still running"
        return 0
    else
        return 2
    fi
}
if [[ x$2 == x"--not_check" ]];then
    NOT_CHECK=1
fi

case C"$1" in
    C)
        start
        ;;
    Cstart)
        start
        ;;
    Cstop)
        stop
        ;;
    Crestart)
        stop
        start
        ;;
    Cpid)
        showpid
        ;;
    Creplace)
        replace
        ;;
    Cclean)
       clean
        ;;
    Cstatus)
      status
        ;;
    C*)
        echo "Usage: $0 {start|stop|restart|clean|status|pid]}"
        ;;
esac