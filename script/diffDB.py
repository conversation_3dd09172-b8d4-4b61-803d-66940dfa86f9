#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# 对比两个数据库表结构不同并生成差异化的sql语句

作者: cuiyi01(<EMAIL>)
日期: May 23, 2019 at 2:04:52 PM
"""

import pymysql

HOST = "*************"
PORT = 8049
USER = "bce_scs_r"
PASSWD = "7xF_oRP8z1m"


def get_conn():
    """get connection
    """
    conn = pymysql.connect(host=HOST, port=PORT, user=USER, passwd=PASSWD, charset='utf8')
    return conn


def get_all_tables(conn, db_name):
    """get all tables
    """
    sql = "select table_name from information_schema.tables where table_schema='%s'" % db_name
    cursor = conn.cursor()
    cursor.execute(sql)
    tables = cursor.fetchall()
    cursor.close()
    return [table[0] for table in tables]


def get_table_desc(conn, db_name, table_name):
    """get table desc
    """
    ret = {}
    sql = "desc %s.%s" % (db_name, table_name)
    cursor = conn.cursor()
    cursor.execute(sql)
    desc = cursor.fetchall()
    for item in desc:
        ret[item[0]] = {
            "field": item[0],
            "type": item[1],
            "null": item[2],
            "key": item[3],
            "default": item[4],
        }
    cursor.close()
    return ret


def get_show_create_table(conn, db_name, table_name):
    """get show create table
    """
    sql = "show create table %s.%s" % (db_name, table_name)
    cursor = conn.cursor()
    cursor.execute(sql)
    desc = cursor.fetchall()
    cursor.close()
    return desc[0][1]


def diff_db(conn, newdb, olddb):
    """diff db
    """
    tables_new = get_all_tables(conn, newdb)
    tables_old = get_all_tables(conn, olddb)
    for table in tables_new:
        if table not in tables_old:
            print("\n%s;\n" % get_show_create_table(conn, newdb, table))
        else:
            fields_new = get_table_desc(conn, newdb, table)
            fields_old = get_table_desc(conn, olddb, table)
            for field in fields_new:
                if field not in fields_old:
                    print("alter table %s.%s add column %s %s%s default %s;"
                          % (olddb, table, fields_new[field]["field"], fields_new[field]["type"],
                             " not null" if fields_new[field]["null"] == "NO" else "",
                             "''" if fields_new[field]["default"] == "" else fields_new[field]["default"]))


if __name__ == "__main__":
    diff_db(get_conn(), "bce_scs", "bce_scs_edge")
