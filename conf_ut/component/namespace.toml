[SstDownloadConf]
DownloadParentDir = "/mnt/download_sst"
DiskUsagePercentThreshold = 80
# 100 * 1024 * 1024 * 1024
DiskFreeBytesThreshold = 107374182400
TrafficLimitMB = 60
CheckCRC32 = true
CheckMD5 = true
PresignedUrlExpiration = 7200
# 请求xagent单次下载最大slot数量
MaxSlotNum = 8
# 请求xagent单次下载最大文件size
MaxSstFileSize = 524288000
[SstCleanupConf]
Step = 10
[OperationConf]
ReplicationOffset = 100000
ConnectRetry = 2
ConnectTimeout = 500
ReadTimeout = 5000
WriteTimeout = 5000
[BosConf.IDC_SPEC.onlinebd]
BosEndpoint = "bd.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinebj]
BosEndpoint = "bj.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinesin]
BosEndpoint = "sin.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinebdfsg]
BosEndpoint = "fsg.bdbl.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinebjfsg]
BosEndpoint = "bjfsg.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinefsh]
BosEndpoint = "fsh.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinegz]
BosEndpoint = "gz.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinehkg]
BosEndpoint = "hkg.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinesu]
BosEndpoint = "su.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinesuvip]
BosEndpoint = "su.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlinewh]
BosEndpoint = "fwh.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.onlineyq]
BosEndpoint = "yq.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.bjtest]
BosEndpoint = "bj.bcebos.com"
BosListMaxKeys = 1000
[BosConf.IDC_SPEC.test]
BosEndpoint = "bj-bos-sandbox.baidu-int.com"
BosListMaxKeys = 2