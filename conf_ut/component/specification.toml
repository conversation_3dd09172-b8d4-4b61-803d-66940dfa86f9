[[SpecificationList]]
EngineList=["bdrpproxy"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=1
Name="proxy.n1.small"
CPUCount=1
MemoryCapacityInGB=1
RootDiskCapacityInGB=20
DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["bdrpproxy"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=4
Name="proxy.n1.medium"
CPUCount=2
MemoryCapacityInGB=4
RootDiskCapacityInGB=20
DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=1
Name="cache.n1.micro"
CPUCount=2
MemoryCapacityInGB=2
RootDiskCapacityInGB=20
DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=2
Name="cache.n1.small"
CPUCount=2
MemoryCapacityInGB=4
RootDiskCapacityInGB=20
DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=4
Name="cache.n1.medium"
CPUCount=2
MemoryCapacityInGB=8
RootDiskCapacityInGB=20
DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=4
Name="cache.n1.c4.medium"
CPUCount=4
MemoryCapacityInGB=8
RootDiskCapacityInGB=20
DataDiskCapacityInGB=20

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=8
Name="cache.n1.large"
CPUCount=2
MemoryCapacityInGB=16
RootDiskCapacityInGB=20
DataDiskCapacityInGB=24

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=8
Name="cache.n1.c4.large"
CPUCount=4
MemoryCapacityInGB=16
RootDiskCapacityInGB=20
DataDiskCapacityInGB=24

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=16
Name="cache.n1.xlarge"
CPUCount=2
MemoryCapacityInGB=32
RootDiskCapacityInGB=20
DataDiskCapacityInGB=48

[[SpecificationList]]
EngineList=["redis", "bdrpredis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone", "cluster"]
[SpecificationList.Specification]
AvailableVolume=16
Name="cache.n1.c4.xlarge"
CPUCount=4
MemoryCapacityInGB=32
RootDiskCapacityInGB=20
DataDiskCapacityInGB=48

[[SpecificationList]]
EngineList=["redis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone"]
[SpecificationList.Specification]
AvailableVolume=32
Name="cache.n1.2xlarge"
CPUCount=2
MemoryCapacityInGB=64
RootDiskCapacityInGB=20
DataDiskCapacityInGB=96

[[SpecificationList]]
EngineList=["redis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone"]
[SpecificationList.Specification]
AvailableVolume=32
Name="cache.n1.c4.2xlarge"
CPUCount=4
MemoryCapacityInGB=64
RootDiskCapacityInGB=20
DataDiskCapacityInGB=96

[[SpecificationList]]
EngineList=["redis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone"]
[SpecificationList.Specification]
AvailableVolume=64
Name="cache.n1.4xlarge"
CPUCount=4
MemoryCapacityInGB=64
RootDiskCapacityInGB=20
DataDiskCapacityInGB=192

[[SpecificationList]]
EngineList=["redis"]
StoreTypeList=["DRAM"]
AppTypeList=["standalone"]
[SpecificationList.Specification]
AvailableVolume=64
Name="cache.n1.c4.4xlarge"
CPUCount=4
MemoryCapacityInGB=64
RootDiskCapacityInGB=20
DataDiskCapacityInGB=192

[[SpecificationList]]
EngineList=["pegadb"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=64
Name="pega.g4s1.micro"
CPUCount=4
MemoryCapacityInGB=8
RootDiskCapacityInGB=20
DataDiskCapacityInGB=148

[[SpecificationList]]
EngineList=["pegadb"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=96
Name="pega.g4s1.small"
CPUCount=4
MemoryCapacityInGB=16
RootDiskCapacityInGB=20
DataDiskCapacityInGB=212

[[SpecificationList]]
EngineList=["pegadb"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=192
Name="pega.g4s1.medium"
CPUCount=4
MemoryCapacityInGB=32
RootDiskCapacityInGB=20
DataDiskCapacityInGB=404

[[SpecificationList]]
EngineList=["pegadb"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=384
Name="pega.g4s1.large"
CPUCount=4
MemoryCapacityInGB=64
RootDiskCapacityInGB=20
DataDiskCapacityInGB=788

[[SpecificationList]]
EngineList=["pegadb"]
StoreTypeList=["DRAM"]
AppTypeList=["cluster"]
[SpecificationList.Specification]
AvailableVolume=768
Name="pega.g4s1.xlarge"
CPUCount=4
MemoryCapacityInGB=128
RootDiskCapacityInGB=20
DataDiskCapacityInGB=1556



