##
# 以下是各种package配置
#
[Packages]

# test
[Packages.test]
    ## name
    Name = "xcache"
    ## repo
    Repo = "default_http"
    ## default version
    DefaultVersion = "0.0.1"
    ## static env vars
    [Packages.test.StaticEnv]
        test_var = "test"

# xcache
[Packages.xcache]
    ## name
    Name = "scs-package"
    ## repo
    Repo = "package_bos"
    ## default version
    DefaultVersion = "20220216190700"
    ## static env vars
    [Packages.xcache.StaticEnv]
        #### agent
        [Packages.xcache.StaticEnv.agent]
            container_type = "kvm"
            csmaster_ip = "*************"
            csmaster_port = 8997
            bcm_host = "bcm.xxx.com"
            bcm_port = 80
            accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
            secretkey = "8d310407bcd44b26919fe067ce73526a"
            iam_host = "iam.baidu.com"
            iam_port = 9090
            username = "username"
            password = "password"
        #### csagent
        [Packages.xcache.StaticEnv.csagent]
            csmonitor_ip = "scsmonitor.xxx.com"
            csmonitor_port = 8080
            connection_timeout_ms = 10000
        #### cron
        [Packages.xcache.StaticEnv.cron]
            bos_host = "bj-bos-sandbox.baidu-int.com"
            bucket_name = "scs-log-download"
            ak_encrypt = "43btIj0f4maOyZ4cgQJMR7Ic8+NTnSgutjR2DQjvftqyYy31PeVB46Nq9sDErZMi"
            sk_encrypt = "Mf6c9f7SaV8/YjqAP0Uca3F8o7DivaJA/TNgsR2r7SSRmjQ9mI5+AbHBNN1xnozR"
            log_transfer_addr = "http://gzhxy-y32-sandbox018.gzhxy.baidu.com"
            log_transfer_port = 8279
            log_transfer_url = "/transfer/slowlog"
            hotkey_transfer_url = "/transfer/hotkey"
            token = "paastest123"
