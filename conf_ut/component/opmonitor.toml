[IDC_SPEC.test]
Path="BAIDU_INF_BCE_SCS_SCS-sandbox_monitor"
Token="********************************"
[IDC_SPEC.test.InterfaceConf]
NameSuffix="proxysandbox.BCE.all"
GroupName="group.monitorsandbox.bce.all"
[[IDC_SPEC.test.BackendConfList]]
Engine="redis"
GroupName="group.monitorsandbox.bce.all"
NameSuffix="redissandbox.BCE.all"
[[IDC_SPEC.test.BackendConfList]]
Engine="pegadb"
GroupName="group.monitorsandbox.bce.all"
NameSuffix="redissandbox.BCE.all"
[IDC_SPEC.test.BnsInstanceConf]
RunUser="zhangxuepeng"
HealthCheckType="proc"
HealthCheckCmd =""
Disable=0
Status=0
DeployPathParentPath="/root/monitor/"
Port = {main = "1"}
Tag = {cpu = "x86"}

