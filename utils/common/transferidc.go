/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/11/17
 * File: transferidc.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package common TODO package function desc
package common

import "icode.baidu.com/baidu/gdp/env"

var IDCMap = map[string]string{
	"bjtest":      "bj",
	"onlinebj":    "bj",
	"onlinebd":    "bdbl",
	"onlinebdfsg": "bdfsg",
	"onlinebjfsg": "bjfsg",
	"onlinefsh":   "fsh",
	"onlinegz":    "gz",
	"onlinehkg":   "hkg",
	"onlinesu":    "su",
	"onlinesuvip": "su",
	"onlinewh":    "whgg",
	"onlineyq":    "yq",
	"onlinecd":    "cd",
	"onlinenj":    "nj",
}

func GetCsmasterIDC() string {
	idc, has := IDCMap[env.IDC()]
	if !has {
		return "unknow"
	}
	return idc
}
