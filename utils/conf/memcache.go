/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/10/23
 * File: memcache.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package conf TODO package function desc
package conf

type MemcacheConfStruct struct {
	Image *memcacheImageConf
}

type memcacheImageConf struct {
	Bcc string
}

var MemcacheConf *MemcacheConfStruct

func InitMemcacheConf() error {
	return LoadConf("scs", "memcache", &MemcacheConf)
}
