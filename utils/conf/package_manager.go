/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* package_manager.go */
/*
modification history
--------------------
2023/04/19 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package conf

type PackageManagerConf struct {
	Image   *pkgImageConf
	PkgConf *pkgConf
}

type pkgImageConf struct {
	IsolatedBcc   string
	UnIsolatedBcc string
}

type pkgConf struct {
	AgentBins      []string
	NeedExcutePkgs []string
	CorePkgs       []string
}

// PkgManConf definition
var PkgManConf *PackageManagerConf

func InitPkgManConf() error {
	return LoadConf("scs", "package_manager", &PkgManConf)
}

func IsAgentsBin(pkgName string) bool {
	for _, pkg := range PkgManConf.PkgConf.AgentBins {
		if pkg == pkgName {
			return true
		}
	}
	return false
}

func IsNeedExuctePkg(pkgName string) bool {
	for _, pkg := range PkgManConf.PkgConf.NeedExcutePkgs {
		if pkg == pkgName {
			return true
		}
	}
	return false
}

func IsCorePkg(pkgName string) bool {
	for _, pkg := range PkgManConf.PkgConf.CorePkgs {
		if pkg == pkgName {
			return true
		}
	}
	return false
}
