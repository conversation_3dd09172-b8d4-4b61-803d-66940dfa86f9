/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/25 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file scs_conf.go
 * <AUTHOR>
 * @date 2022/07/25 15:16:56
 * @brief scs conf
 *
 **/

package conf

// ScsConf definiton
type ScsConf struct {
	McpackUserList              []string
	IDCCODE                     int64  `toml:"IdcCode"`
	DataServerHost              string `toml:"DataServerHost"`
	DataserverPort              int    `toml:"DataServerPort"`
	DataServerPassword          string `toml:"DataServerPassword"`
	PackageVersion              string `toml:"PackageVersion"`
	KafkaAddr                   string `toml:"KafkaAddr"`
	AnalysisToolVersion         string `toml:"AnalysisToolVersion"`
	AnalysisTopN                int    `toml:"AnalysisTopN"`
	NoahBns                     string `toml:"NoahBns"`
	NoahEndpoint                string `toml:"NoahEndpoint"`
	DataServerEntranceHostTrans string `toml:"DataServerEntranceHostTrans"`
	DataServerEntrancePortTrans int    `toml:"DataServerEntrancePortTrans"`
	// 2024.Q4内网上云加快，原有自建dataserver负载吃紧，我们需要把dataserver迁移到BDRP，这里是BDRP的配置
	// 文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/HzSoP9gY6stHW1
	BDRPDataServerHost     string `toml:"BDRPDataServerHost"`
	BDRPDataserverPort     int    `toml:"BDRPDataserverPort"`
	BDRPDataServerPassword string `toml:"BDRPDataServerPassword"`
	// Trans结尾的是给A区使用的
	BDRPDataServerEntranceHostTrans string `toml:"BDRPDataServerEntranceHostTrans"`
	BDRPDataServerEntrancePortTrans int    `toml:"BDRPDataServerEntrancePortTrans"`
}

// ResizeConf definiton
type ResizeConf struct {
	AllowReplaceNodeWhenCdsResizeFails string `toml:"AllowReplaceNodeWhenCdsResizeFails"`
	ResizeTimeoutInSeconds             int    `toml:"ResizeTimeoutInSeconds"`
}

// XmasterHAConf definiton
type XmasterHAConf struct {
	DefaultMonitorRules string `toml:"DefaultMonitorRules"`
	PegadbMonitorRules  string `toml:"PegadbMonitorRules"`
	IdcName             string `toml:"IdcName"`
	XmasterEndpoint     string `toml:"XmasterEndpoint"`
}

type MachineEnvConf struct {
	Nameservers string `toml:"nameservers"`
}

type StoreTypeConf struct {
	LocalDiskNodeType []string `toml:"LocalDiskNodeType"`
}

// ScsMainConf definition
var ScsMainConf *ScsConf
var ResizeConfIns *ResizeConf
var XmasterHAConfIns *XmasterHAConf
var MachineEnvConfIns *MachineEnvConf
var StoreTypeConfIns *StoreTypeConf

// MustLoadScsConf retrun scs main conf
func MustLoadScsConf() error {
	if err := LoadConf("scs", "main", &ScsMainConf); err != nil {
		return err
	}
	if err := LoadConf("scs", "resize", &ResizeConfIns); err != nil {
		return err
	}
	if err := LoadConf("scs", "xmaster_ha", &XmasterHAConfIns); err != nil {
		return err
	}
	if err := LoadConf("scs", "machine_env", &MachineEnvConfIns); err != nil {
		return err
	}
	if err := LoadConf("scs", "store_type", &StoreTypeConfIns); err != nil {
		return err
	}
	return nil
}
