/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/04/11 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file check_sync_group.go
 * <AUTHOR>
 * @date 2023/04/11 17:40:07
 * @brief
 *
 **/

package utils

import "icode.baidu.com/baidu/scs/x1-base/model/x1model"

// CheckSyncGroupField 检查syncGroupID是否为空
// 如果为空，返回false, 否则返回true
func CheckSyncGroupField(app *x1model.Application) bool {
	if len(app.SyncGroupID) == 0 {
		return false
	}
	return true
}
