package remotessh

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"golang.org/x/crypto/ssh"
)

func ChangeBcmPushCycleBySSH(ctx context.Context, newCycle int, oldCycle int, rootPassword,
	resourceType, resourceID, floatingIP string) (string, error) {
	if newCycle != 5 && newCycle != 60 {
		return "", errors.New("newCycle is invalid")
	}
	sshConf := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.Password(rootPassword),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         2 * time.Minute,
	}
	sshCmd := fmt.Sprintf("sed -i '/cycle/s/%d/%d/' /root/agent/recover/bcm_conf.txt", oldCycle, newCycle)
	if resourceType == "container" {
		sshCmd = fmt.Sprintf("docker exec %s bash -c \"sed -i '/cycle/s/%d/%d/' /root/agent/recover/bcm_conf.txt\"", resourceID, oldCycle, newCycle)
	}
	rsp, err := RunCmdRemote(ctx, sshConf, floatingIP, "22", sshCmd)
	return strings.TrimSpace(rsp), err
}

func GetBcmPushCycleBySSH(ctx context.Context, rootPassword,
	resourceType, resourceID, floatingIP string) (string, error) {
	sshConf := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.Password(rootPassword),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         2 * time.Minute,
	}
	sshCmd := fmt.Sprintf("cat /root/agent/recover/bcm_conf.txt|grep cycle|awk '{print $2}'")
	if resourceType == "container" {
		sshCmd = fmt.Sprintf("docker exec %s bash -c \"cat /root/agent/recover/bcm_conf.txt|grep cycle|awk '{print \\$2}'\"", resourceID)
	}
	rsp, err := RunCmdRemote(ctx, sshConf, floatingIP, "22", sshCmd)
	return strings.TrimSpace(rsp), err
}
