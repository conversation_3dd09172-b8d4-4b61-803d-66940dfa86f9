package dataserver

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func Pick(ctx context.Context, appInfo *x1model.Application) *single_redis.SingleClient {
	if appInfo.DataserverType == "bdrp" {
		return resource.BDRPDataServerClient
	}
	return resource.DataServerClient
}
