/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/10/23
 * File: memcache_shrink.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/memcache"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
)

const (
	WorkflowModifyDelShardsMemcache            = "scs-modify-del-shards-memcache"
	StepModifyDelShardsMemcacheBuildMeta       = "scs-modify-del-shards-memcache-build-meta"
	StepModifyDelShardsMemcacheSetRs           = "scs-modify-del-shards-memcache-set-rs"
	StepModifyDelShardsMemcacheSetMcpackRs     = "scs-modify-del-shards-memcache-set-mcpack-rs"
	StepModifyDelShardsMemcacheDeleteOldNodes  = "scs-modify-del-shards-memcache-del-old-nodes"
	StepModifyDelShardsMemcacheRestartProxy    = "scs-modify-del-shards-memcache-restart-proxy"
	StepModifyDelShardsMemcacheCheckAllAlive   = "scs-modify-del-shards-memcache-check-alive"
	StepModifyDelShardsMemcacheSuccessCallback = "scs-modify-del-shards-memcache-succ-cb"
)

func init() {
	// Step1 修改x1元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheBuildMeta,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     memcache.ProcessBuildMetaForMCModifyShards,
		SuccessNextStep: StepModifyDelShardsMemcacheSetRs,
		ErrorNextStep:   StepModifyDelShardsMemcacheBuildMeta,
	})

	// Step2 解绑待删除的proxy rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheSetRs,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     blb.ProcessSetProxyRs,
		SuccessNextStep: StepModifyDelShardsMemcacheSetMcpackRs,
		ErrorNextStep:   StepModifyDelShardsMemcacheSetRs,
	})

	// Step3 解绑待删除的proxy rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheSetMcpackRs,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepModifyDelShardsMemcacheDeleteOldNodes,
		ErrorNextStep:   StepModifyDelShardsMemcacheSetMcpackRs,
	})

	// Step6 释放资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheDeleteOldNodes,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyDelShardsMemcacheRestartProxy,
		ErrorNextStep:   StepModifyDelShardsMemcacheDeleteOldNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheRestartProxy,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     memcache.ProcessRestartProxy,
		SuccessNextStep: StepModifyDelShardsMemcacheCheckAllAlive,
		ErrorNextStep:   StepModifyDelShardsMemcacheRestartProxy,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheCheckAllAlive,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     memcache.ProcessDeployMcForNewCreate,
		SuccessNextStep: StepModifyDelShardsMemcacheSuccessCallback,
		ErrorNextStep:   StepModifyDelShardsMemcacheCheckAllAlive,
	})

	// Step7 成功Callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsMemcacheSuccessCallback,
		Workflow:        WorkflowModifyDelShardsMemcache,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyDelShardsMemcacheSuccessCallback,
	})
}
