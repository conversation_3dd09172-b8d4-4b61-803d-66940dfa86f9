/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/23
 * File: memcache_create.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/memcache"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
)

// 正常任务
const (
	WorkflowCreateMemcache                              = "scs-create-memcache"
	StepCreateMemcacheBuildMeta                         = "scs-create-memcache-build-meta"
	StepCreateMemcacheFillSpec                          = "scs-create-memcache-fill-spec"
	StepCreateMemcacheCheckSubnetsIpv6                  = "scs-create-memcache-check-subnets-ipv6"
	StepCreateMemcacheCheckSubnetsEnoughIps             = "scs-create-memcache-check-subnets-enough-ips"
	StepCreateMemcacheCreateSecurityGroups              = "scs-create-memcache-create-security-groups"
	StepCreateMemcacheApplyResources                    = "scs-create-memcache-apply-resources"
	StepCreateMemcacheApplyResourcesCb                  = "scs-create-memcache-apply-resources-cb"
	StepCreateMemcacheDeploy                            = "scs-create-memcache-apply-deploy-redis"
	StepCreateMemcacheInitBlbAndEndpointInitBLB         = "scs-create-memcache-init-blb-and-endpoint-init-blb"
	StepCreateMemcacheInitBlbAndEndpointCreateAppDomain = "scs-create-memcache-init-blb-and-endpoint-create-app-domain"
	StepCreateMemcacheUpdateConfigUpdateSg              = "scs-create-memcache-update-config-update-sg"
	StepCreateMemcacheInitBlbRsSetRs                    = "scs-create-memcache-init-blb-rs-set-rs"
	StepCreateMemcacheInitBlbRsSetMcpackRs              = "scs-create-memcache-init-blb-rs-set-mcpack-rs"
	StepCreateMemcacheCreateCallback                    = "scs-create-memcache-create-cb"
)

// 回滚任务
const (
	StepCreateMemcacheRollbackCb               = "scs-create-memcache-rollback-callback"
	StepCreateMemcacheRollbackDeleteBLB        = "scs-create-memcache-rollback-delete-blb"
	StepCreateMemcacheRollbackReleaseResources = "scs-create-memcache-rollback-release-resource"
)

func init() {
	{
		// Step1 buildmeta
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheBuildMeta,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     memcache.ProcessBuildMetaForCreate,
			SuccessNextStep: StepCreateMemcacheFillSpec,
			ErrorNextStep:   StepCreateMemcacheBuildMeta,
		})

		// Step2 计算规格
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheFillSpec,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     memcache.ProcessFillMcSpec,
			SuccessNextStep: StepCreateMemcacheCheckSubnetsIpv6,
			ErrorNextStep:   StepCreateMemcacheFillSpec,
		})

		// Step3 检查子网是否为ipv6
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheCheckSubnetsIpv6,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     checksubnets.CheckIPV6,
			SuccessNextStep: StepCreateMemcacheCheckSubnetsEnoughIps,
			ErrorNextStep:   StepCreateMemcacheCheckSubnetsIpv6,
		})

		// Step4 检查子网中ip是否充足
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheCheckSubnetsEnoughIps,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     checksubnets.CheckEnoughIPs,
			SuccessNextStep: StepCreateMemcacheInitBlbAndEndpointInitBLB,
			ErrorNextStep:   StepCreateMemcacheCheckSubnetsEnoughIps,
		})

		// Step5 初始化blb
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheInitBlbAndEndpointInitBLB,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     blb.ProcessInitAppBLB,
			SuccessNextStep: StepCreateMemcacheInitBlbAndEndpointCreateAppDomain,
			ErrorNextStep:   StepCreateMemcacheInitBlbAndEndpointInitBLB,
		})

		// Step6 创建Domain
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheInitBlbAndEndpointCreateAppDomain,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     dns.ProcessCreateAppDomain,
			SuccessNextStep: StepCreateMemcacheCreateSecurityGroups,
			ErrorNextStep:   StepCreateMemcacheInitBlbAndEndpointCreateAppDomain,
		})

		// Step7 创建安全组
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheCreateSecurityGroups,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     securitygroup.ProcessInitSecurityGroupCluster,
			SuccessNextStep: StepCreateMemcacheApplyResources,
			ErrorNextStep:   StepCreateMemcacheCreateSecurityGroups,
		})

		// Step8 申请资源
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheApplyResources,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     applyresource.ProcessApplyMcBccResources,
			SuccessNextStep: StepCreateMemcacheApplyResourcesCb,
			ErrorNextStep:   StepCreateMemcacheApplyResources},
			workflow.WithStepTimeout(15*time.Minute),
		)

		// Step9 申请资源回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheApplyResourcesCb,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     callback.ProcessApplyResourceCallback,
			SuccessNextStep: StepCreateMemcacheUpdateConfigUpdateSg,
			ErrorNextStep:   StepCreateMemcacheApplyResourcesCb,
		})

		// Step10 注册安全组
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheUpdateConfigUpdateSg,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
			SuccessNextStep: StepCreateMemcacheInitBlbRsSetRs,
			ErrorNextStep:   StepCreateMemcacheUpdateConfigUpdateSg,
		})

		// Step11 绑定ProxyRS到blb
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheInitBlbRsSetRs,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     blb.ProcessSetProxyRs,
			SuccessNextStep: StepCreateMemcacheInitBlbRsSetMcpackRs,
			ErrorNextStep:   StepCreateMemcacheInitBlbRsSetRs,
		})

		// Step12 绑定ProxyRS mcpack协议到blb
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheInitBlbRsSetMcpackRs,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     blb.ProcessSetProxyRsForMcpack,
			SuccessNextStep: StepCreateMemcacheDeploy,
			ErrorNextStep:   StepCreateMemcacheInitBlbRsSetMcpackRs,
		})

		// Step15 部署/探活
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheDeploy,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     memcache.ProcessDeployMcForNewCreate,
			SuccessNextStep: StepCreateMemcacheCreateCallback,
			ErrorNextStep:   StepCreateMemcacheDeploy},
			workflow.WithStepTimeout(15*time.Minute),
		)

		// Step16 成功回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheCreateCallback,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     callback.ProcessCreateSuccessCb,
			SuccessNextStep: workflow.FinalStepSuccess,
			ErrorNextStep:   StepCreateMemcacheCreateCallback,
		})
	}
	{
		// Step-Error-01 创建失败回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheRollbackCb,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     callback.ProcessCreateErrorCb,
			SuccessNextStep: StepCreateMemcacheRollbackDeleteBLB,
			ErrorNextStep:   StepCreateMemcacheRollbackCb,
		})

		// Step-Error-02 回滚资源申请
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheRollbackDeleteBLB,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     blb.ProcessDelBLB,
			SuccessNextStep: StepCreateMemcacheRollbackReleaseResources,
			ErrorNextStep:   StepCreateMemcacheRollbackDeleteBLB,
		})

		// Step-Error-05 回滚BLB资源
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepCreateMemcacheRollbackReleaseResources,
			Workflow:        WorkflowCreateMemcache,
			StepProcess:     delresource.ProcessRollbackBccResources,
			SuccessNextStep: workflow.FinalStepError,
			ErrorNextStep:   StepCreateMemcacheRollbackReleaseResources,
		})
	}
}
