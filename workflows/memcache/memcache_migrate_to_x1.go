/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/11/16
 * File: memcache_migrate_to_x1.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/memcache"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
)

const (
	WorkflowMemcacheMigrateToX1           = "scs-workflow-mc-migratetox1"
	StepMemcacheMigrateToX1BeforeCheck    = "scs-step-mc-migratetox1-beforecheck"
	StepMemcacheMigrateToX1Execute        = "scs-workflow-mc-migratetox1-step-execute"
	StepMemcacheMigrateToX1FillSpec       = "scs-workflow-mc-migratetox1-step-fillspec"
	StepMemcacheMigrateToX1FillSpecCommit = "scs-workflow-mc-migratetox1-step-fillspec-commit"
	StepMemcacheMigrateToX1Callback       = "scs-workflow-mc-migratetox1-step-callback"
)

func init() {
	// Step-1 执行csmaster到x1框架的迁移前置检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMemcacheMigrateToX1BeforeCheck,
		Workflow:        WorkflowMemcacheMigrateToX1,
		StepProcess:     memcache.ProcessMigrateMcToX1PreCheck,
		SuccessNextStep: StepMemcacheMigrateToX1Execute,
		ErrorNextStep:   StepMemcacheMigrateToX1BeforeCheck,
	})

	// Step-1 执行csmaster到x1框架的迁移
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMemcacheMigrateToX1Execute,
		Workflow:        WorkflowMemcacheMigrateToX1,
		StepProcess:     memcache.ProcessMigrateMcToX1,
		SuccessNextStep: StepMemcacheMigrateToX1FillSpec,
		ErrorNextStep:   StepMemcacheMigrateToX1Execute,
	})

	// Step-2 填充规格信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMemcacheMigrateToX1FillSpec,
		Workflow:        WorkflowMemcacheMigrateToX1,
		StepProcess:     memcache.ProcessFillMcSpec,
		SuccessNextStep: StepMemcacheMigrateToX1FillSpecCommit,
		ErrorNextStep:   StepMemcacheMigrateToX1FillSpec,
	})

	// Step-3 填充规格信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMemcacheMigrateToX1FillSpecCommit,
		Workflow:        WorkflowMemcacheMigrateToX1,
		StepProcess:     specification.ProcessCommitSpec,
		SuccessNextStep: StepMemcacheMigrateToX1Callback,
		ErrorNextStep:   StepMemcacheMigrateToX1FillSpecCommit,
	})

	// Step-4 回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMemcacheMigrateToX1Callback,
		Workflow:        WorkflowMemcacheMigrateToX1,
		StepProcess:     callback.ProcessMigrateToX1Cb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMemcacheMigrateToX1Callback,
	})
}
