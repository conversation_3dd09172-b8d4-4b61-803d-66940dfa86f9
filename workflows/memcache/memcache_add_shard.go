/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><EMAIL>)
 * Date: 2023/10/23
 * File: memcache_expansion.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/memcache"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
)

const (
	WorkflowModifyAddShardsMemcache                   = "scs-modify-add-shards-memcache"
	StepModifyAddShardsMemcacheBuildMeta              = "scs-modify-add-shards-memcache-build-meta"
	StepModifyAddShardsMemcacheFillSpec               = "scs-modify-add-shards-memcache-fill-spec"
	StepModifyAddShardsMemcacheCheckSubnetsEnoughIps  = "scs-modify-add-shards-memcache-check-subnets-enough-ips"
	StepModifyAddShardsMemcacheApplyResources         = "scs-modify-add-shards-memcache-apply-resources"
	StepModifyAddShardsMemcacheApplyResourcesCallback = "scs-modify-add-shards-memcache-apply-resources-cb"
	StepModifyAddShardsMemcacheUpdateSecurityGroups   = "scs-modify-add-shards-memcache-update-security-groups"
	StepModifyAddShardsMemcacheRestartProxy           = "scs-modify-add-shards-memcache-restart-proxy"
	StepModifyAddShardsMemcacheDeployAndCheckAlive    = "scs-modify-add-shards-memcache-deploy-and-check-alive"
	StepModifyAddShardsMemcacheSetRs                  = "scs-modify-add-shards-memcache-set-rs"
	StepModifyAddShardsMemcacheSetMcpackRs            = "scs-modify-add-shards-memcache-set-mcpack-rs"
	StepModifyAddShardsMemcacheSuccessCallback        = "scs-modify-add-shards-memcache-succ-cb"
)

const (
	StepModifyAddShardsMemcacheRollbackReleaseResources = "scs-modify-add-shards-memcache-rollback-release-resource"
	StepModifyAddShardsMemcacheRollbackMeta             = "scs-modify-add-shards-memcache-rollback-meta"
	StepModifyAddShardsMemcacheRollbackCallback         = "scs-modify-add-shards-memcache-rollback-cb"
)

func init() {
	{
		// Step1 修改x1元数据库
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheBuildMeta,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     memcache.ProcessBuildMetaForMCModifyShards,
			SuccessNextStep: StepModifyAddShardsMemcacheFillSpec,
			ErrorNextStep:   StepModifyAddShardsMemcacheBuildMeta,
		})

		// Step2 填入规格信息
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheFillSpec,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     memcache.ProcessFillMcSpec,
			SuccessNextStep: StepModifyAddShardsMemcacheCheckSubnetsEnoughIps,
			ErrorNextStep:   StepModifyAddShardsMemcacheFillSpec,
		})

		// Step3 检查子网IP是否足够
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheCheckSubnetsEnoughIps,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     checksubnets.CheckEnoughIPs,
			SuccessNextStep: StepModifyAddShardsMemcacheApplyResources,
			ErrorNextStep:   StepModifyAddShardsMemcacheCheckSubnetsEnoughIps,
		})

		// Step4 申请资源
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheApplyResources,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     applyresource.ProcessApplyMcBccResources,
			SuccessNextStep: StepModifyAddShardsMemcacheApplyResourcesCallback,
			ErrorNextStep:   StepModifyAddShardsMemcacheApplyResources,
		})

		// Step5 申请资源回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheApplyResourcesCallback,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     callback.ProcessApplyResourceCallback,
			SuccessNextStep: StepModifyAddShardsMemcacheUpdateSecurityGroups,
			ErrorNextStep:   StepModifyAddShardsMemcacheApplyResourcesCallback,
		})

		// Step6 更新安全组
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheUpdateSecurityGroups,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
			SuccessNextStep: StepModifyAddShardsMemcacheRestartProxy,
			ErrorNextStep:   StepModifyAddShardsMemcacheUpdateSecurityGroups,
		})

		// Step7 部署/探活
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheRestartProxy,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     memcache.ProcessRestartProxy,
			SuccessNextStep: StepModifyAddShardsMemcacheDeployAndCheckAlive,
			ErrorNextStep:   StepModifyAddShardsMemcacheRestartProxy,
		})

		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheDeployAndCheckAlive,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     memcache.ProcessDeployMcForNewCreate,
			SuccessNextStep: StepModifyAddShardsMemcacheSetRs,
			ErrorNextStep:   StepModifyAddShardsMemcacheDeployAndCheckAlive},
			workflow.WithStepTimeout(time.Minute*15))

		// Step8 绑定新的proxy rs
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheSetRs,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     blb.ProcessSetProxyRsForModify,
			SuccessNextStep: StepModifyAddShardsMemcacheSetMcpackRs,
			ErrorNextStep:   StepModifyAddShardsMemcacheSetRs,
		})

		// Step9 绑定新的proxy rs(for mcpack)
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheSetMcpackRs,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     blb.ProcessSetProxyRsForMcpack,
			SuccessNextStep: StepModifyAddShardsMemcacheSuccessCallback,
			ErrorNextStep:   StepModifyAddShardsMemcacheSetMcpackRs,
		})

		// Step12 成功回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheSuccessCallback,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     callback.ProcessModifyNodesSuccCb,
			SuccessNextStep: workflow.FinalStepSuccess,
			ErrorNextStep:   StepModifyAddShardsMemcacheSuccessCallback,
		})
	}
	{
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheRollbackReleaseResources,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     delresource.ProcessRollbackBccResources,
			SuccessNextStep: StepModifyAddShardsMemcacheRollbackMeta,
			ErrorNextStep:   workflow.FinalStepError,
		})

		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheRollbackMeta,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     delresource.ProcessRollbackMeta,
			SuccessNextStep: StepModifyAddShardsMemcacheRollbackCallback,
			ErrorNextStep:   workflow.FinalStepError,
		})

		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepModifyAddShardsMemcacheRollbackCallback,
			Workflow:        WorkflowModifyAddShardsMemcache,
			StepProcess:     callback.ProcessModifyNodesErrorCb,
			SuccessNextStep: workflow.FinalStepError,
			ErrorNextStep:   StepModifyAddShardsMemcacheRollbackCallback,
		})
	}
}
