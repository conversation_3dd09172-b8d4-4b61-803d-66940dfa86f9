/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/10/23
 * File: memcache_reboot.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowReopenMemcache           = "scs-reopen-memcache"
	StepReopenMemcacheRebindRs       = "scs-reopen-memcache-rebind-rs"
	StepReopenMemcacheRebindMcpackRs = "scs-reopen-memcache-rebind-mcpack-rs"
	StepReopenMemcacheCallback       = "scs-reopen-memcache-cb"
)

func init() {
	// Step1 重新挂载所有rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenMemcacheRebindRs,
		Workflow:        WorkflowReopenMemcache,
		StepProcess:     blb.ProcessRebingProxyRs,
		SuccessNextStep: StepReopenMemcacheRebindMcpackRs,
		ErrorNextStep:   StepReopenMemcacheRebindRs,
	})

	// Step2 重新挂载所有rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenMemcacheRebindMcpackRs,
		Workflow:        WorkflowReopenMemcache,
		StepProcess:     blb.ProcessRebingProxyMcpackRs,
		SuccessNextStep: StepReopenMemcacheCallback,
		ErrorNextStep:   StepReopenMemcacheRebindMcpackRs,
	})

	// Step3 成功回调
	// 调用CsMaster的API，修改cluster状态为5
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenMemcacheCallback,
		Workflow:        WorkflowReopenMemcache,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepReopenMemcacheCallback,
	})
}
