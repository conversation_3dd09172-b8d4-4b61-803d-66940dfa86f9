/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/10/23
 * File: memcache_replace.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/memcache"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
)

const (
	WorkflowSelfHealMemcache                   = "scs-self-heal-memcache"
	StepSelfHealMemcacheBuildMeta              = "scs-self-heal-memcache-build-meta"
	StepSelfHealMemcacheFillSpec               = "scs-self-heal-memcache-fill-spec"
	StepSelfHealMemcacheCheckSubnetsEnoughIps  = "scs-self-heal-memcache-check-subnets-enough-ips"
	StepSelfHealMemcacheApplyResources         = "scs-self-heal-memcache-apply-resources"
	StepSelfHealMemcacheApplyResourcesCallback = "scs-self-heal-memcache-apply-resources-cb"
	StepSelfHealMemcacheDeleteOldNodes         = "scs-self-heal-memcache-del-old-nodes"
	StepSelfHealMemcacheUpdateSecurityGroups   = "scs-self-heal-memcache-update-security-groups"
	StepSelfHealMemcacheRestartProxy           = "scs-self-heal-memcache-restart-proxy"
	StepSelfHealMemcacheDeployAndCheckAlive    = "scs-self-heal-memcache-apply-deploy-and-check-alive"
	StepSelfHealMemcacheSetRs                  = "scs-self-heal-memcache-set-rs"
	StepSelfHealMemcacheSetMcpackRs            = "scs-self-heal-memcache-set-mcpack-rs"

	StepSelfHealMemcacheResetFailoverFlag = "scs-self-heal-memcache-reset-failover-flag"
	StepSelfHealMemcacheSuccessCallback   = "scs-self-heal-memcache-succ-cb"
)

const (
	StepSelfHealMemcacheRollbackReleaseResources = "scs-self-heal-memcache-rollback-release-resource"
	StepSelfHealMemcacheRollbackBuildMeta        = "scs-self-heal-memcache-rollback-build-meta"
	StepSelfHealMemcacheRollbackCallback         = "scs-self-heal-memcache-rollback-cb"
)

func init() {
	{
		// Step1 构建元数据，将需要删除的节点改为ToDelete、新加节点ToCreate
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheBuildMeta,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     memcache.ProcessBuildMetaForSelfHeal,
			SuccessNextStep: StepSelfHealMemcacheFillSpec,
			ErrorNextStep:   StepSelfHealMemcacheBuildMeta,
		})

		// Step2 将规格信息填入Cluster表或Interface表；
		// 同分片的node应当规格相同，Port相同，使用同样的部署集
		// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheFillSpec,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     memcache.ProcessFillMcSpec,
			SuccessNextStep: StepSelfHealMemcacheCheckSubnetsEnoughIps,
			ErrorNextStep:   StepSelfHealMemcacheFillSpec,
		})

		// Step3 检查子网ip是否充足
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheCheckSubnetsEnoughIps,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     checksubnets.CheckEnoughIPs,
			SuccessNextStep: StepSelfHealMemcacheApplyResources,
			ErrorNextStep:   StepSelfHealMemcacheCheckSubnetsEnoughIps,
		})

		// Step4 创建资源
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheApplyResources,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     applyresource.ProcessApplyMcBccResources,
			SuccessNextStep: StepSelfHealMemcacheApplyResourcesCallback,
			ErrorNextStep:   StepSelfHealMemcacheApplyResources},
			workflow.WithStepTimeout(15*time.Minute))

		// Step5 将创建好的资源同步至Csmaster
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheApplyResourcesCallback,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     callback.ProcessApplyResourceCallback,
			SuccessNextStep: StepSelfHealMemcacheDeleteOldNodes,
			ErrorNextStep:   StepSelfHealMemcacheApplyResourcesCallback,
		})

		// Step-11 删除节点
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheDeleteOldNodes,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     delresource.ProcessDeleteToDeleteNodes,
			SuccessNextStep: StepSelfHealMemcacheUpdateSecurityGroups,
			ErrorNextStep:   StepSelfHealMemcacheDeleteOldNodes,
		})

		// Step6 更新安全组规则
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheUpdateSecurityGroups,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
			SuccessNextStep: StepSelfHealMemcacheRestartProxy,
			ErrorNextStep:   StepSelfHealMemcacheUpdateSecurityGroups,
		})

		// Step7 部署与探活
		// 部署Redis
		// shangshuai
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheRestartProxy,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     memcache.ProcessRestartProxy,
			SuccessNextStep: StepSelfHealMemcacheDeployAndCheckAlive,
			ErrorNextStep:   StepSelfHealMemcacheRestartProxy,
		})

		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheDeployAndCheckAlive,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     memcache.ProcessDeployMcForNewCreate,
			SuccessNextStep: StepSelfHealMemcacheSetRs,
			ErrorNextStep:   StepSelfHealMemcacheDeployAndCheckAlive},

			workflow.WithStepTimeout(15*time.Minute))

		// Step-9 绑定BLB的rs
		// shangshuai
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheSetRs,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     blb.ProcessSetProxyRsForModify,
			SuccessNextStep: StepSelfHealMemcacheSetMcpackRs,
			ErrorNextStep:   StepSelfHealMemcacheSetRs,
		})

		// Step-9 绑定BLB的rs(for mcpack)
		// shangshuai
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheSetMcpackRs,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     blb.ProcessSetProxyRsForMcpack,
			SuccessNextStep: StepSelfHealMemcacheResetFailoverFlag,
			ErrorNextStep:   StepSelfHealMemcacheSetMcpackRs,
		})

		// Step-12 创建成功回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheResetFailoverFlag,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     handover.ProcessResetFailoveredShards,
			SuccessNextStep: StepSelfHealMemcacheSuccessCallback,
			ErrorNextStep:   StepSelfHealMemcacheResetFailoverFlag,
		})

		// Step-13 创建成功回调
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheSuccessCallback,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     callback.ProcessSelfHealingCb,
			SuccessNextStep: workflow.FinalStepSuccess,
			ErrorNextStep:   StepSelfHealMemcacheSuccessCallback,
		})
	}
	{
		// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheRollbackReleaseResources,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     delresource.ProcessRollbackBccResources,
			SuccessNextStep: StepSelfHealMemcacheRollbackBuildMeta,
			ErrorNextStep:   workflow.FinalStepError,
		})

		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheRollbackBuildMeta,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     delresource.ProcessRollbackMeta,
			SuccessNextStep: StepSelfHealMemcacheRollbackCallback,
			ErrorNextStep:   StepSelfHealMemcacheRollbackBuildMeta,
		})

		_ = workflow.AddStep(&workflow.AddStepParam{
			Name:            StepSelfHealMemcacheRollbackCallback,
			Workflow:        WorkflowSelfHealMemcache,
			StepProcess:     callback.ProcessSelfHealingCb,
			SuccessNextStep: workflow.FinalStepError,
			ErrorNextStep:   StepSelfHealMemcacheRollbackCallback,
		})
	}
}
