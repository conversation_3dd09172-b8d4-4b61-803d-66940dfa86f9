/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
 * Date: 2023/10/23
 * File: memcache_pause.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowPauseMemcacheApp        = "scs-pause-memcache"
	StepPauseMemcacheUnbindAllRs    = "scs-pause-memcache-unbind-all-rs"
	StepPauseMemcacheUnbindMcpackRs = "scs-pause-memcache-unbind-mcpack-rs"
	StepPauseMemcacheCallbacks      = "scs-pause-memcache-callbacks"
)

func init() {
	// Step1 摘除所有Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseMemcacheUnbindAllRs,
		Workflow:        WorkflowPauseMemcacheApp,
		StepProcess:     blb.ProcessUnbindAllProxys,
		SuccessNextStep: StepPauseMemcacheUnbindMcpackRs,
		ErrorNextStep:   StepPauseMemcacheUnbindAllRs,
	})

	// Step2 摘除所有Rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseMemcacheUnbindMcpackRs,
		Workflow:        WorkflowPauseMemcacheApp,
		StepProcess:     blb.ProcessUnbindAllProxysMcpackRs,
		SuccessNextStep: StepPauseMemcacheCallbacks,
		ErrorNextStep:   StepPauseMemcacheUnbindMcpackRs,
	})

	// Step3 成功回调
	// 调用CsMaster的API，修改cluster状态为8
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseMemcacheCallbacks,
		Workflow:        WorkflowPauseMemcacheApp,
		StepProcess:     callback.ProcessPauseCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPauseMemcacheCallbacks,
	})
}
