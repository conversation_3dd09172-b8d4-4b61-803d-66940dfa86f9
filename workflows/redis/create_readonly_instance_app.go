/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file create_readonly_instance_app.go
 * <AUTHOR>
 * @date 2022/05/16 19:49:43
 * @brief create readonly instance
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowCreateRoInstance                       = "scs-create-readonly-instance-app"
	StepCreateRoInstanceBuildMeta                  = "scs-create-readonly-instance-build-meta"
	StepCreateRoInstanceTaskStepCreateNodes        = "scs-create-readonly-instance-task-step-create-nodes"
	StepCreateRoInstanceCheckSubnetsEnoughIps      = "scs-create-readonly-instance-check-subnets-enough-ips"
	StepCreateRoInstanceApplyResource              = "scs-create-readonly-instance-apply-resource"
	StepCreateRoInstanceApplyResourceCallback      = "scs-create-readonly-instance-apply-resource-cb"
	StepCreateRoInstanceTaskStepDeployNodes        = "scs-create-readonly-instance-task-step-deploy-nodes"
	StepCreateRoInstanceInitMachineEnv             = "scs-create-readonly-instance-init-machine-env"
	StepCreateRoInstanceUpdateSecurityGroup        = "scs-create-readonly-instance-update-security-group"
	StepCreateRoInstanceDeployRoRedis              = "scs-create-readonly-instance-deploy-ro-redis"
	StepCreateRoInstanceTaskStepConfigNodes        = "scs-create-readonly-instance-task-step-config-nodes"
	StepCreateRoInstanceSetAcl                     = "scs-create-readonly-instance-deploy-set-acl"
	StepCreateRoInstanceUpdateConfig               = "scs-create-readonly-instance-update-config"
	StepCreateRoInstanceSetLocalSlaveOf            = "scs-create-readonly-instance-set-local-slaveof"
	StepCreateRoInstanceSetGlobalSlaveOf           = "scs-create-readonly-instance-set-global-slaveof"
	StepCreateRoInstanceTaskStepSync               = "scs-create-readonly-instance-task-step-sync"
	StepCreateRoInstanceCheckAllSync               = "scs-create-readonly-instance-check-all-sync"
	StepCreateRoInstanceInitBlb                    = "scs-create-readonly-instance-init-blb"
	StepCreateRoInstanceSetRs                      = "scs-create-readonly-instance-set-rs"
	StepCreateRoInstanceSetEntranceRs              = "scs-create-readonly-instance-set-entrance-rs"
	StepCreateRoInstanceCreateEndpoint             = "scs-create-readonly-instance-create-endpoint"
	StepCreateRoInstanceCreateRoDomain             = "scs-create-readonly-instance-create-ro-domain"
	StepCreateRoInstanceUpdateAppTopologyInXmaster = "scs-create-readonly-instance-update-app-topo-in-xmaster"
	StepCreateRoInstanceInitBcmResource            = "scs-create-readonly-instance-init-bcm-resource"
	StepCreateRoInstanceAddToBcmGroup              = "scs-create-readonly-instance-add-to-bcm-group"
	StepCreateRoInstanceInitOpMonitor              = "scs-create-readonly-instance-init-op-monitor"
	StepCreateRoInstanceUpdatePushHTGRPSlaveFlag   = "scs-create-readonly-instance-update-push-htg-slave-flag"
	StepCreateRoInstanceUpdatePushFlagAllTrue      = "scs-create-readonly-instance-update-push-flag-all-true"
	StepCreateRoInstanceTaskStepSuccess            = "scs-create-readonly-instance-task-step-success"
	StepCreateRoInstanceSuccessCallback            = "scs-create-readonly-instance-success-cb"
	StepCreateRoInstanceErrorCallback              = "scs-create-readonly-instance-error-cb"
	StepCreateRoInstanceTaskStepError              = "scs-create-readonly-instance-task-step-error"
	StepCreateRoInstanceRollbackEndpoint           = "scs-create-readonly-instance-rollback-endpoint"
	StepCreateRoInstanceRollbackBlb                = "scs-create-readonly-instance-rollback-blb"
	StepCreateRoInstanceRemoveFromBcmGroup         = "scs-create-readonly-instance-rollback-remove-from-group"
	StepCreateRoInstanceRollbackBcmResource        = "scs-create-readonly-instance-rollback-bcm-resource"
	StepCreateRoInstanceRollbackResource           = "scs-create-readonly-instance-rollback-resource"
	StepCreateRoInstanceRollbackMeta               = "scs-create-readonly-instance-rollback-meta"
)

func init() {
	// 注册删除SCS标准版实例创建只读实例WORKFLOW

	// Step-1 build meta
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceBuildMeta,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     buildmeta.ProcessBuildMetaForCreatingRoInstance,
		SuccessNextStep: StepCreateRoInstanceTaskStepCreateNodes,
		ErrorNextStep:   StepCreateRoInstanceBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateRoInstanceTaskStepCreateNodes,
		Workflow: WorkflowCreateRoInstance,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddReadonlyTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateRoInstanceCheckSubnetsEnoughIps,
		ErrorNextStep:   StepCreateRoInstanceTaskStepCreateNodes,
	})

	// 检查是否有足够的子网IP
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceCheckSubnetsEnoughIps,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     checksubnets.CheckEnoughIPsRorRo,
		SuccessNextStep: StepCreateRoInstanceApplyResource,
		ErrorNextStep:   StepCreateRoInstanceCheckSubnetsEnoughIps,
	})

	// 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceApplyResource,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepCreateRoInstanceApplyResourceCallback,
		ErrorNextStep:   StepCreateRoInstanceApplyResource,
	}, workflow.WithStepTimeout(15*time.Minute))

	// 资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceApplyResourceCallback,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepCreateRoInstanceTaskStepDeployNodes,
		ErrorNextStep:   StepCreateRoInstanceApplyResourceCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateRoInstanceTaskStepDeployNodes,
		Workflow: WorkflowCreateRoInstance,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddReadonlyTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateRoInstanceInitMachineEnv,
		ErrorNextStep:   StepCreateRoInstanceTaskStepDeployNodes,
	})

	// 初始化机器环境
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceInitMachineEnv,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepCreateRoInstanceUpdateSecurityGroup,
		ErrorNextStep:   StepCreateRoInstanceInitMachineEnv,
	})

	// 更新安全组规则
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceUpdateSecurityGroup,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandalone,
		SuccessNextStep: StepCreateRoInstanceDeployRoRedis,
		ErrorNextStep:   StepCreateRoInstanceUpdateSecurityGroup,
	})

	// 部署只读实例
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceDeployRoRedis,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepCreateRoInstanceTaskStepConfigNodes,
		ErrorNextStep:   StepCreateRoInstanceDeployRoRedis,
	},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateRoInstanceTaskStepConfigNodes,
		Workflow: WorkflowCreateRoInstance,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddReadonlyTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateRoInstanceSetAcl,
		ErrorNextStep:   StepCreateRoInstanceTaskStepConfigNodes,
	})

	// 只读实例设置初始auth,acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceSetAcl,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     acl.ProcessInitACLForRo,
		SuccessNextStep: StepCreateRoInstanceSetLocalSlaveOf,
		ErrorNextStep:   StepCreateRoInstanceSetAcl,
	})

	// Step-10 设置Local Redis的著丛关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceSetLocalSlaveOf,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     handover.ProcessLocalSlaveOf,
		SuccessNextStep: StepCreateRoInstanceSetGlobalSlaveOf,
		ErrorNextStep:   StepCreateRoInstanceSetLocalSlaveOf},
	)

	// Step-11 设置Global 只读实例的Redis的主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceSetGlobalSlaveOf,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepCreateRoInstanceUpdateConfig,
		ErrorNextStep:   StepCreateRoInstanceSetGlobalSlaveOf},
	)

	// 更新配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceUpdateConfig,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     config.ProcessUpdateConfigStandalone,
		SuccessNextStep: StepCreateRoInstanceTaskStepSync,
		ErrorNextStep:   StepCreateRoInstanceUpdateConfig,
	}, workflow.WithMaxReentry(2, StepCreateRoInstanceTaskStepSync))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateRoInstanceTaskStepSync,
		Workflow: WorkflowCreateRoInstance,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddReadonlyTask, timewindow.StepSync, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateRoInstanceCheckAllSync,
		ErrorNextStep:   StepCreateRoInstanceTaskStepSync,
	})

	// 检查数据同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceCheckAllSync,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepCreateRoInstanceInitBlb,
		ErrorNextStep:   StepCreateRoInstanceCheckAllSync},

		workflow.WithStepTimeout(15*time.Minute))

	// 初始化负载均衡
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceInitBlb,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     blb.ProcessInitRoAppBLB,
		SuccessNextStep: StepCreateRoInstanceSetRs,
		ErrorNextStep:   StepCreateRoInstanceInitBlb,
	})

	// 更新blb后端rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceSetRs,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     blb.ProcessSetReadonlyInstRs,
		SuccessNextStep: StepCreateRoInstanceSetEntranceRs,
		ErrorNextStep:   StepCreateRoInstanceSetRs,
	})

	// 更新entrance blb后端rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceSetEntranceRs,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepCreateRoInstanceCreateEndpoint,
		ErrorNextStep:   StepCreateRoInstanceSetEntranceRs,
	})

	// 创建 endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceCreateEndpoint,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     endpoint.ProcessCreateRoEndpoint,
		SuccessNextStep: StepCreateRoInstanceCreateRoDomain,
		ErrorNextStep:   StepCreateRoInstanceCreateEndpoint,
	})

	// 处理域名
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceCreateRoDomain,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     dns.ProcessCreateRoGroupDomain,
		SuccessNextStep: StepCreateRoInstanceUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepCreateRoInstanceCreateRoDomain,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceUpdateAppTopologyInXmaster,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepCreateRoInstanceInitBcmResource,
		ErrorNextStep:   StepCreateRoInstanceUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceInitBcmResource,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepCreateRoInstanceAddToBcmGroup,
		ErrorNextStep:   StepCreateRoInstanceInitBcmResource,
	})

	// 添加到bcm group
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceAddToBcmGroup,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     bcm.ProcessRoAddToGroup,
		SuccessNextStep: StepCreateRoInstanceInitOpMonitor,
		ErrorNextStep:   StepCreateRoInstanceAddToBcmGroup,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceInitOpMonitor,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepCreateRoInstanceUpdatePushHTGRPSlaveFlag,
		ErrorNextStep:   StepCreateRoInstanceInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceUpdatePushHTGRPSlaveFlag,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAll,
		SuccessNextStep: StepCreateRoInstanceUpdatePushFlagAllTrue,
		ErrorNextStep:   StepCreateRoInstanceUpdatePushHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceUpdatePushFlagAllTrue,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     pushflag.ProcessUpdatePushFlagAllTrue,
		SuccessNextStep: StepCreateRoInstanceTaskStepSuccess,
		ErrorNextStep:   StepCreateRoInstanceUpdatePushFlagAllTrue,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceTaskStepSuccess,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepCreateRoInstanceSuccessCallback,
		ErrorNextStep:   StepCreateRoInstanceTaskStepSuccess,
	})

	// 创建成功回调
	// 调用CsMaster的API，修改cluster状态为运行中
	// 清理node、proxy的status，置为运行中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceSuccessCallback,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     callback.ProcessCreateReadonlyInstSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateRoInstanceSuccessCallback,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为CACHE_CLUSTER_MODIFIED_FAILED让订单可以停止
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceErrorCallback,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     callback.ProcessCreateReadonlyInstErrorCb,
		SuccessNextStep: StepCreateRoInstanceTaskStepError,
		ErrorNextStep:   StepCreateRoInstanceErrorCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceTaskStepError,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),
		SuccessNextStep: StepCreateRoInstanceRollbackEndpoint,
		ErrorNextStep:   StepCreateRoInstanceTaskStepError,
	})

	// Step-Error 创建失败时，如果已经创建了服务网卡，需要删除
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceRollbackEndpoint,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     endpoint.ProcessCreatingRoErrorDeleteEndpoint,
		SuccessNextStep: StepCreateRoInstanceRollbackBlb,
		ErrorNextStep:   StepCreateRoInstanceRollbackBlb},

		workflow.WithMaxReentry(2, StepCreateRoInstanceRollbackBlb))
	// Step-Error-02 创建失败时，如果已经创建了blb，需要删除blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceRollbackBlb,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     blb.ProcessCreatingRoErrorDelBLB,
		SuccessNextStep: StepCreateRoInstanceRemoveFromBcmGroup,
		ErrorNextStep:   StepCreateRoInstanceRollbackBlb},

		workflow.WithMaxReentry(2, StepCreateRoInstanceRemoveFromBcmGroup))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceRemoveFromBcmGroup,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     bcm.ProcessRollbackRoAddToGroup,
		SuccessNextStep: StepCreateRoInstanceRollbackBcmResource,
		ErrorNextStep:   StepCreateRoInstanceRemoveFromBcmGroup,
	})

	// Step-Error-03 创建失败时，如果已经创建了BCM Resource,则删除
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceRollbackBcmResource,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     bcm.ProcessRollbackBcmResourceWithoutApp,
		SuccessNextStep: StepCreateRoInstanceRollbackResource,
		ErrorNextStep:   StepCreateRoInstanceRollbackBcmResource})

	// Step-Error-04 创建失败时，如果已经创建了资源，需要删除资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceRollbackResource,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     delresource.ProcessRollbackRoBccResources,
		SuccessNextStep: StepCreateRoInstanceRollbackMeta,
		ErrorNextStep:   StepCreateRoInstanceRollbackResource},

		workflow.WithMaxReentry(2, StepCreateRoInstanceRollbackMeta))

	// Step-Error-05 创建失败时，需要删除元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateRoInstanceRollbackMeta,
		Workflow:        WorkflowCreateRoInstance,
		StepProcess:     delresource.ProcessRollbackReadonlyResourcesMeta,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepCreateRoInstanceRollbackMeta},
		workflow.WithMaxReentry(2, workflow.FinalStepError))
}
