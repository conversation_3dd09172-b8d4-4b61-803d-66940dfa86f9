package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/availability"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowMigrateHAToXmaster                            = "scs-workflow-mig-ha-to-xm"
	StepMigrateHAToXmasterCheckHAServicer                 = "scs-workflow-mig-ha-to-xm-step-check-ha-servicer"
	StepMigrateHAToXmasterCheckAppStatus                  = "scs-workflow-mig-ha-to-xm-step-check-app-status"
	StepMigrateHAToXmasterCheckAppInstances               = "scs-workflow-mig-ha-to-xm-step-check-app-instances"
	StepMigrateHAToXmasterUpdateAppTopologyInXmaster      = "scs-workflow-mig-ha-to-xm-step-update-app-topo-in-xm"
	StepMigrateHAToXmasterWaitXmasterCheckAppHealthStatus = "scs-workflow-mig-ha-to-xm-step-wait-xm-check-app-health"
	StepMigrateHAToXmasterCheckAppHealthInXmaster         = "scs-workflow-mig-ha-to-xm-step-check-app-health-in-xm"
	StepMigrateHAToXmasterSwitchOffHAInCsmaster           = "scs-workflow-mig-ha-to-xm-step-switch-off-ha-in-csm"
	StepMigrateHAToXmasterSwitchOnHAInXmaster             = "scs-workflow-mig-ha-to-xm-step-switch-on-ha-in-xm"
	StepMigrateHAToXmasterSuccessCallback                 = "scs-workflow-mig-ha-to-xm-step-succ-cb"
	StepMigrateHAToXmasterRollbackSwitchOffHAInXmaster    = "scs-workflow-mig-ha-to-xm-step-rollback-switch-off-ha-in-xm"
	StepMigrateHAToXmasterRollbackSwitchOnHAInCsmaster    = "scs-workflow-mig-ha-to-xm-step-rollback-switch-on-ha-in-csm"
	StepMigrateHAToXmasterRollbackSuccessCallback         = "scs-workflow-mig-ha-to-xm-step-rollback-succ-cb"
)

const (
	WorkflowMigrateHAToCsmaster                          = "scs-workflow-migrate-ha-to-csm"
	StepMigrateHAToCsmasterCheckHAServicer               = "scs-workflow-migrate-ha-to-csm-step-check-ha-servicer"
	StepMigrateHAToCsmasterCheckAppStatus                = "scs-workflow-migrate-ha-to-csm-step-check-app-status"
	StepMigrateHAToCsmasterSwitchOffHAInXmaster          = "scs-workflow-migrate-ha-to-csm-step-switch-off-ha-in-xm"
	StepMigrateHAToCsmasterSwitchOnHAInCsmaster          = "scs-workflow-migrate-ha-to-csm-step-switch-on-ha-in-csm"
	StepMigrateHAToCsmasterSuccessCallback               = "scs-workflow-migrate-ha-to-csm-step-succ-cb"
	StepMigrateHAToCsmasterRollbackSwitchOffHAInCsmaster = "scs-workflow-migrate-ha-to-csm-step-rollback-switch-off-ha-in-csm"
	StepMigrateHAToCsmasterRollbackSwitchOnHAInXmaster   = "scs-workflow-migrate-ha-to-csm-step-rollback-switch-on-ha-in-xm"
	StepMigrateHAToCsmasterRollbackSuccessCallback       = "scs-workflow-migrate-ha-to-csm-step-rollback-succ-cb"
)

func init() {
	/**************************************迁移到xmaster*********************************************/

	// Step：检查当前托管状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterCheckHAServicer,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.IsAppUseXmaster,
		SuccessNextStep: StepMigrateHAToXmasterCheckAppStatus,
		ErrorNextStep:   workflow.FinalStepSuccess,
	})

	// Step：检查并更新app状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterCheckAppStatus,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.CheckAndUpdateAppStatus,
		SuccessNextStep: StepMigrateHAToXmasterCheckAppInstances,
		ErrorNextStep:   StepMigrateHAToXmasterCheckAppStatus,
	})

	// Step：检查集群节点是否满足迁移条件
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterCheckAppInstances,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.CheckAppInstances,
		SuccessNextStep: StepMigrateHAToXmasterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepMigrateHAToXmasterRollbackSuccessCallback,
	})

	// Step：刷新xmaster元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepMigrateHAToXmasterWaitXmasterCheckAppHealthStatus,
		ErrorNextStep:   StepMigrateHAToXmasterUpdateAppTopologyInXmaster,
	})

	// Step：等待xmaster检查app健康状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterWaitXmasterCheckAppHealthStatus,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.WaitXmasterCheckAppHealthStatus,
		SuccessNextStep: StepMigrateHAToXmasterCheckAppHealthInXmaster,
		ErrorNextStep:   StepMigrateHAToXmasterWaitXmasterCheckAppHealthStatus,
	})

	// Step：检查集群在xmaster的状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterCheckAppHealthInXmaster,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.CheckAppHealthInXmaster,
		SuccessNextStep: StepMigrateHAToXmasterSwitchOffHAInCsmaster,
		ErrorNextStep:   StepMigrateHAToXmasterCheckAppHealthInXmaster,
	})

	// Step：关闭csmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterSwitchOffHAInCsmaster,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.SwitchOffHAInCsmaster,
		SuccessNextStep: StepMigrateHAToXmasterSwitchOnHAInXmaster,
		ErrorNextStep:   StepMigrateHAToXmasterRollbackSwitchOnHAInCsmaster,
	})

	// Step：开启xmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterSwitchOnHAInXmaster,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.SwitchOnHAInXmaster,
		SuccessNextStep: StepMigrateHAToXmasterSuccessCallback,
		ErrorNextStep:   StepMigrateHAToXmasterRollbackSwitchOffHAInXmaster,
	})

	// Step：成功
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterSuccessCallback,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     callback.MigHAToXmasterSuccessCallback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateHAToXmasterSuccessCallback,
	})

	// Rollback Step：关闭xmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterRollbackSwitchOffHAInXmaster,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.SwitchOffHAInXmaster,
		SuccessNextStep: StepMigrateHAToXmasterRollbackSwitchOnHAInCsmaster,
		ErrorNextStep:   StepMigrateHAToXmasterRollbackSwitchOffHAInXmaster,
	})

	// Rollback Step：开启csmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterRollbackSwitchOnHAInCsmaster,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     availability.SwitchOnHAInCsmaster,
		SuccessNextStep: StepMigrateHAToXmasterRollbackSuccessCallback,
		ErrorNextStep:   StepMigrateHAToXmasterRollbackSwitchOnHAInCsmaster,
	})

	// Rollback Step：回滚成功
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToXmasterRollbackSuccessCallback,
		Workflow:        WorkflowMigrateHAToXmaster,
		StepProcess:     callback.MigHAToCsmasterSuccessCallback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateHAToXmasterRollbackSuccessCallback,
	})

	/**************************************迁移到csmaster*********************************************/

	// Step：检查当前托管状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterCheckHAServicer,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     availability.IsAppUseCsmaster,
		SuccessNextStep: StepMigrateHAToCsmasterCheckAppStatus,
		ErrorNextStep:   workflow.FinalStepSuccess,
	})

	// Step：检查并更新app状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterCheckAppStatus,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     availability.CheckAndUpdateAppStatus,
		SuccessNextStep: StepMigrateHAToCsmasterSwitchOffHAInXmaster,
		ErrorNextStep:   StepMigrateHAToCsmasterCheckAppStatus,
	})

	// Step：关闭xmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterSwitchOffHAInXmaster,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     availability.SwitchOffHAInXmaster,
		SuccessNextStep: StepMigrateHAToCsmasterSwitchOnHAInCsmaster,
		ErrorNextStep:   StepMigrateHAToCsmasterRollbackSwitchOnHAInXmaster,
	})

	// Step：开启csmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterSwitchOnHAInCsmaster,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     availability.SwitchOnHAInCsmaster,
		SuccessNextStep: StepMigrateHAToCsmasterSuccessCallback,
		ErrorNextStep:   StepMigrateHAToCsmasterRollbackSwitchOffHAInCsmaster,
	})

	// Step：成功
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterSuccessCallback,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     callback.MigHAToCsmasterSuccessCallback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateHAToCsmasterSuccessCallback,
	})

	// Rollback Step：关闭csmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterRollbackSwitchOffHAInCsmaster,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     availability.SwitchOffHAInCsmaster,
		SuccessNextStep: StepMigrateHAToCsmasterRollbackSwitchOnHAInXmaster,
		ErrorNextStep:   StepMigrateHAToCsmasterRollbackSwitchOffHAInCsmaster,
	})

	// Rollback Step：开启xmaster高可用开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterRollbackSwitchOnHAInXmaster,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     availability.SwitchOnHAInXmaster,
		SuccessNextStep: StepMigrateHAToCsmasterRollbackSuccessCallback,
		ErrorNextStep:   StepMigrateHAToCsmasterRollbackSwitchOnHAInXmaster,
	})

	// Rollback Step：回滚成功
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateHAToCsmasterRollbackSuccessCallback,
		Workflow:        WorkflowMigrateHAToCsmaster,
		StepProcess:     callback.MigHAToXmasterSuccessCallback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateHAToCsmasterRollbackSuccessCallback,
	})
}
