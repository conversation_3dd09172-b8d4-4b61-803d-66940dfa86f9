/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
执行标准版实例备份动作
// shangshuai

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"BackupParams" : [
		{
			NodeShortId: 1234,
			ObjectKey: "xxxx",
		},
		{
			NodeShortId: 1235,
			ObjectKey: "yyyy",
		}
	]
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
)

const (
	WorkflowScsBackupStandalone      = "scs-backup-standalone-app"
	StepScsBackupStandaloneBuildMeta = "scs-backup-standalone-app-build-meta"
	StepScsBackupStandaloneBackup    = "scs-backup-standalone-app-backup"
)

func init() {
	// Step-1 build备份任务meta
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepScsBackupStandaloneBuildMeta,
		Workflow:        WorkflowScsBackupStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForBackup,
		SuccessNextStep: StepScsBackupStandaloneBackup,
		ErrorNextStep:   StepScsBackupStandaloneBuildMeta})
	// Step-2 执行备份任务
	// 1. 通过XAgent向节点发送BGSAVE
	// 2. 将生成的rdb文件上传到BOS
	// 3. 调用Csmaster接口，通知备份完成
	// 开发量：
	// (1) BOS SDK，使用公开SDK即可
	// (2) 生成的rdb文件上传到BOS，将Agent中代码改造为可独立执行脚本
	// (3) csmaster需要2处改造
	//   (3.1) 创建备份记录同时，创建相关Task（BackupRecordManager::create_batch_records中添加）
	//   (3.2) 公开更新备份状态函数（BackupRecordManager::update_backup_record）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepScsBackupStandaloneBackup,
		Workflow:        WorkflowScsBackupStandalone,
		StepProcess:     backup.ProcessBackupRedis,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepScsBackupStandaloneBackup},

		workflow.WithStepTimeout(30*time.Minute))
}
