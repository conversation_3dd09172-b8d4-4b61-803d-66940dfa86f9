/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/29 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file create_unified_entrance.go
 * <AUTHOR>
 * @date 2022/12/29 16:10:41
 * @brief
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
)

const (
	WorkflowCreateUnifiedEntrance                   = "create-unified-entrance-app"
	StepCreateUnifiedEntranceBuildMeta              = "scs-create-unified-entrance-app-build-meta"
	StepCreateUnifiedEntranceInitBLB                = "scs-create-unified-entrance-init-blb"
	StepCreateUnifiedEntranceSetRs                  = "scs-create-unified-entrance-set-rs"
	StepCreateUnifiedEntranceCreateEndpoint         = "scs-create-unified-entrance-create-endpoint"
	StepCreateUnifiedEntranceCreateCallback         = "scs-create-unified-entrance-create-cb"
	StepCreateUnifiedEntranceRollbackCallback       = "scs-create-unified-entrance-rollback-callback"
	StepCreateUnifiedEntranceRollbackDeleteEndpoint = "scs-create-unified-entrance-rollback-delete-endpoint"
	StepCreateUnifiedEntranceRollbackDeleteBLB      = "scs-create-unified-entrance-rollback-delete-blb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceBuildMeta,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     buildmeta.ProcessBuildMetaForCreatingEntrance,
		SuccessNextStep: StepCreateUnifiedEntranceInitBLB,
		ErrorNextStep:   StepCreateUnifiedEntranceBuildMeta,
	})

	// Step-2 创建BLB for entrance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceInitBLB,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     blb.ProcessInitEntranceAppBLB,
		SuccessNextStep: StepCreateUnifiedEntranceSetRs,
		ErrorNextStep:   StepCreateUnifiedEntranceInitBLB,
	})

	// Step-3 绑定BLB的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceSetRs,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepCreateUnifiedEntranceCreateEndpoint,
		ErrorNextStep:   StepCreateUnifiedEntranceSetRs,
	})

	// Step-4 创建服务网卡
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceCreateEndpoint,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     endpoint.ProcessCreateEntranceEndpoint,
		SuccessNextStep: StepCreateUnifiedEntranceCreateCallback,
		ErrorNextStep:   StepCreateUnifiedEntranceCreateEndpoint,
	})

	// Step-5 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceCreateCallback,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateUnifiedEntranceCreateCallback,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceRollbackCallback,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     callback.ProcessCreateEntranceErrorCb,
		SuccessNextStep: StepCreateUnifiedEntranceRollbackDeleteEndpoint,
		ErrorNextStep:   StepCreateUnifiedEntranceRollbackCallback},

		workflow.WithMaxReentry(2, StepCreateUnifiedEntranceRollbackDeleteEndpoint))

	// Step-Error 创建失败时，如果创建了 endpoint, 则需要删除 endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceRollbackDeleteEndpoint,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     endpoint.ProcessDeleteEntranceEndpoint,
		SuccessNextStep: StepCreateUnifiedEntranceRollbackDeleteBLB,
		ErrorNextStep:   StepCreateUnifiedEntranceRollbackDeleteEndpoint},

		workflow.WithMaxReentry(2, StepCreateUnifiedEntranceRollbackDeleteBLB))

	// Step-Error-03 创建失败时，如果已经创建了blb，需要删除blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateUnifiedEntranceRollbackDeleteBLB,
		Workflow:        WorkflowCreateUnifiedEntrance,
		StepProcess:     blb.ProcessDeleteEntranceBlB,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepCreateUnifiedEntranceRollbackDeleteBLB},

		workflow.WithMaxReentry(2, workflow.FinalStepError))
}
