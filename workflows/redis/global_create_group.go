/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_create_group.go */
/*
modification history
--------------------
2022/05/24 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/precheck"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkFlowLocalCreateGroup                                = "scs-local-create-group"
	WorkFlowLocalCreateGroup_StepPreCheck                   = "scs-local-create-group-precheck"
	WorkFlowLocalCreateGroup_StepDisableFlashback           = "scs-local-create-group-disable-flashback"
	WorkFlowLocalCreateGroup_StepTurnOnPegaUseRsidPsync     = "scs-local-create-group-turn-on-pega-use-rsid-psync"
	WorkFlowLocalCreateGroup_StepRestartSlaves              = "scs-local-create-group-restart-all-slaves"
	WorkFlowLocalCreateGroup_StepCheckSync                  = "scs-local-create-group-check-sync"
	WorkFlowLocalCreateGroup_StepHandover                   = "scs-local-create-group-handover"
	WorkFlowLocalCreateGroup_StepRestartOldmasters          = "scs-local-create-group-restart-old-masters"
	WorkFlowLocalCreateGroup_StepBuildMeta                  = "scs-local-create-group-build-meta"
	WorkFlowLocalCreateGroup_StepRegisterNodes              = "scs-local-create-group-register-nodes"
	WorkFlowLocalCreateGroup_CbGMetaserver                  = "scs-local-create-group_cb_metaserver"
	WorkFlowLocalCreateGroup_StepUpdateGlobalSlot           = "scs-local-create-group-update-global-slot"
	WorkFlowLocalCreateGroup_StepRestartProxy               = "scs-local-create-group-restart-proxy"
	WorkFlowLocalCreateGroup_UpdateTlsConfIfNeeded          = "scs-local-create-group-update-tls-conf-if-needed"
	WorkFlowLocalCreateGroup_StepCleanMetaserver            = "scs-local-create-group-clean-metaserver"
	WorkFlowLocalCreateGroup_StepUpdateAppTopologyInXmaster = "scs-local-create-group-update-app-topo-in-xmaster"
	WorkFlowLocalCreateGroup_StepSuccessCb                  = "scs-local-create-group-success-cb"
)

func init() {
	// 0 前置检查，明显无需重试的情况快速失败
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepPreCheck,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     precheck.ProcessPrecheckForCreateGlobalGroup,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepDisableFlashback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepDisableFlashback,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOFAndCloseOpheader,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepTurnOnPegaUseRsidPsync,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepDisableFlashback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepTurnOnPegaUseRsidPsync,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     gmaster.TurnOnPegaUseRsidPsyncAndBuildMetaForRestart,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepRestartSlaves,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepTurnOnPegaUseRsidPsync,
	})

	// 后续3个processor目的是让必须重启生效的node参数生效
	// 分别为：重启从 主从切换 重启主
	// 注意，这里是一个trick，就是让这套重启流程放在build meta之前，所以app.group_id还是空的，可以理解为这里是在创建热活之前搞了一些前置流程
	// 因此在这些processor里会作为非热活的集群进行操作，包括会进行一次slaveof，所以不需要另外单独的slaveof步骤
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepRestartSlaves,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     restart.ProcessRestartForCreateGroup,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepCheckSync,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepRestartSlaves,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepCheckSync,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepHandover,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepCheckSync,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepHandover,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     handover.ProcessHandoverForCreateGroup,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepRestartOldmasters,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepHandover,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepRestartOldmasters,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     restart.ProcessRestartForCreateGroup,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepBuildMeta,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepRestartOldmasters,
	})
	// 把重启步骤前置的原因是，这个时候app里的app_group_id还没有写入，可以复用正常的切换processor

	// ① 查询global master，获取shard信息，将GlobalShardID写入数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepBuildMeta,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForCreateGlobalGroup,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepRegisterNodes,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepBuildMeta,
	})

	// ② 调用global-master-api，将redis、proxy信息注册至global master
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepRegisterNodes,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     gmaster.ProcessRegisterNodesToGlobalMasterAsLeader,
		SuccessNextStep: WorkFlowLocalCreateGroup_CbGMetaserver,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepRegisterNodes,
	})
	// ③ 回调csmaster，更新metaserver信息
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_CbGMetaserver,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     callback.ProcessCbGlobalMetaserverEntrance,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepUpdateGlobalSlot,
		ErrorNextStep:   WorkFlowLocalCreateGroup_CbGMetaserver,
	})
	// ④ 更新global metaserver的slot分布与原metaserver中一致
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepUpdateGlobalSlot,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     metaserver.ProcessGlobalUpdateSlots,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepRestartProxy,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepUpdateGlobalSlot,
	})
	// ⑤ 分批次重启proxy
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepRestartProxy,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     restart.ProcessUpgradeProxies,
		SuccessNextStep: WorkFlowLocalCreateGroup_UpdateTlsConfIfNeeded,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepRestartProxy,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_UpdateTlsConfIfNeeded,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     util.UpdateTLSConfIfNeeded,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepCleanMetaserver,
		ErrorNextStep:   WorkFlowLocalCreateGroup_UpdateTlsConfIfNeeded,
	})

	// ⑥ 删除原Metaserver中信息
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepCleanMetaserver,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     metaserver.ProcessDeleteClusterLocal,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepCleanMetaserver,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalCreateGroup_StepSuccessCb,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepUpdateAppTopologyInXmaster,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateGroup_StepSuccessCb,
		Workflow:        WorkFlowLocalCreateGroup,
		StepProcess:     callback.ProcessGlobalGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalCreateGroup_StepSuccessCb,
	})
}
