/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
执行修改密码、修改acl（NewMasterServiceImpl::acl_user_actions）
// shangshuai

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"AclParams"  ...
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowScsClusterCloseTls     = "scs-cluster-close-tls"
	StepCloseTlsClusterBuildMeta   = "scs-close-tls-cluster-app-build-meta"
	StepCloseTlsClusterDeliverConf = "scs-close-tls-cluster-app-deliver-conf"
	StepCloseTlsClusterCallback    = "scs-close-tls-cluster-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// 1、校验集群架构、版本
	// 2、cache_cluster   +  application   更新tls_port信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCloseTlsClusterBuildMeta,
		Workflow:        WorkflowScsClusterCloseTls,
		StepProcess:     buildmeta.ProcessBuildMetaCloseTls,
		SuccessNextStep: StepCloseTlsClusterDeliverConf,
		ErrorNextStep:   StepCloseTlsClusterBuildMeta,
	})

	// 下发tls配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCloseTlsClusterDeliverConf,
		Workflow:        WorkflowScsClusterCloseTls,
		StepProcess:     util.DeliverTLSConf,
		SuccessNextStep: StepCloseTlsClusterCallback,
		ErrorNextStep:   StepCloseTlsClusterDeliverConf},

		workflow.WithStepTimeout(15*time.Minute))

	/*
		成功回调:
			变更集群状态
			变更证书表状态为:inuse
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCloseTlsClusterCallback,
		Workflow:        WorkflowScsClusterCloseTls,
		StepProcess:     callback.ProcessOpenTlsSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCloseTlsClusterCallback,
	})
}
