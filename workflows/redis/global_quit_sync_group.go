/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/21 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_quit_sync_group.go
 * <AUTHOR>
 * @date 2022/11/21 19:50:48
 * @brief
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalQuitSyncGroup quit sync group workflow
const (
	WorkFlowLocalQuitSyncGroup                           = "scs-local-quit-sync-group"
	WorkFlowLocalQuitSyncGroupBlockMonitorInXmaster      = "scs-local-quit-sync-group-block-monitor-in-xmaster"
	WorkFlowLocalQuitSyncGroupCloseSyncConfig            = "scs-local-quit-sync-group-close-sync-config"
	WorkFlowLocalQuitSyncGroupDeleteAOFBackupPolicy      = "scs-local-quit-sync-group-delete-aof-backup-policy"
	WorkFlowLocalQuitSyncGroupCloseOpHeader              = "scs-local-quit-sync-group-close-op-header"
	WorkFlowLocalQuitSyncGroupDeleteSyncPoint            = "scs-local-quit-sync-group-delete-sync-point"
	WorkFlowLocalQuitSyncGroupDelSyncChannel             = "scs-local-quit-sync-group-del-sync-channel"
	WorkFlowLocalQuitSyncGroupStepKillSyncAgent          = "scs-local-quit-sync-group-kill-sync-agent"
	WorkFlowLocalQuitSyncGroupRebootBuildMeta            = "scs-local-quit-sync-group-reboot-build-meta"
	WorkFlowLocalQuitSyncGroupRebootUpgradeSlaves        = "scs-local-quit-sync-group-reboot-update-slaves"
	WorkFlowLocalQuitSyncGroupRebootCheckSync            = "scs-local-quit-sync-group-reboot-check-sync"
	WorkFlowLocalQuitSyncGroupRebootHandOver             = "scs-local-quit-sync-group-reboot-handover"
	WorkFlowLocalQuitSyncGroupRebootUpgradeMaster        = "scs-local-quit-sync-group-reboot-update-master"
	WorkFlowLocalQuitSyncGroupUpdateAppTopologyInXmaster = "scs-local-quit-sync-group-update-app-topo-in-xmaster"
	WorkFlowLocalQuitSyncGroupUnblockMonitorInXmaster    = "scs-local-quit-sync-group-unblock-monitor-in-xmaster"
	WorkFlowLocalQuitSyncGroupStepSuccessCb              = "scs-local-quit-sync-group-success-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupCloseSyncConfig,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupBlockMonitorInXmaster,
	})

	// 设置support_multi_active参数为no
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupCloseSyncConfig,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     syncgroup.ProcessCloseSyncConfig,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupDeleteAOFBackupPolicy,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupCloseSyncConfig,
	})

	// check 并删除 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupDeleteAOFBackupPolicy,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOF,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupCloseOpHeader,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupDeleteAOFBackupPolicy,
	})

	// 设置use-op-header参数为no
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupCloseOpHeader,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     syncgroup.ProcessCloseOpHeader,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupStepKillSyncAgent,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupCloseOpHeader,
	})

	// kill sync-agent
	// 顺序需要保持：kill-sync-agent -> delete sync-point -> delete sync channel
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupStepKillSyncAgent,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     syncgroup.ProcessKillSyncAgent,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupDeleteSyncPoint,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupStepKillSyncAgent},

		workflow.WithMaxReentry(2, WorkFlowLocalQuitSyncGroupDeleteSyncPoint))

	// delete sync point
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupDeleteSyncPoint,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     syncgroup.ProcessDelSyncPoint,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupDelSyncChannel,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupDeleteSyncPoint,
	})

	//	删除同步通道
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupDelSyncChannel,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     syncgroup.ProcessDelSyncChannel,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupRebootBuildMeta,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupDelSyncChannel},

		workflow.WithMaxReentry(2, WorkFlowLocalQuitSyncGroupRebootBuildMeta))

	// 重启集群（使用升级替代）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupRebootBuildMeta,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForSyncGroupUpgrade,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupRebootUpgradeSlaves,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupRebootBuildMeta,
	})

	// Step-1 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupRebootUpgradeSlaves,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupRebootCheckSync,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupRebootUpgradeSlaves},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-2 等待数据同步后、执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupRebootCheckSync,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupRebootHandOver,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupRebootCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupRebootHandOver,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     handover.ProcessHandoverClusterForRestarting,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupRebootUpgradeMaster,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupRebootHandOver,
	})

	// Step-4 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupRebootUpgradeMaster,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupRebootUpgradeMaster},

		workflow.WithStepTimeout(15*time.Minute))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalQuitSyncGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupUnblockMonitorInXmaster,
	})

	// 返回成功
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncGroupStepSuccessCb,
		Workflow:        WorkFlowLocalQuitSyncGroup,
		StepProcess:     callback.ProcessGlobalSyncQuitSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalQuitSyncGroupStepSuccessCb,
	})
}
