/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/08/02 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file shard_failover_standalone.go
 * <AUTHOR>
 * @date 2022/08/02 13:36:51
 * @brief 标准版切主
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowStandaloneFailover                       = "scs-standalone-failover"
	StepStandaloneFailoverFailover                   = "scs-standalone-failover-build-meta"
	StepStandaloneFailoverUpdateAzDeployInfo         = "scs-standalone-failover-update-azdeployinfo"
	StepStandaloneFailoverUpdateRoTopo               = "scs-standalone-failover-update-ro-topo"
	StepStandaloneFailoverBindRs                     = "scs-standalone-failover-bind-rs"
	StepStandaloneFailoverOldMasterClientKill        = "scs-standalone-failover-old-master-client-kill"
	StepStandaloneFailoverResetFlag                  = "scs-standalone-failover-reset-flag"
	StepStandaloneFailoverUpdateAppTopologyInXmaster = "scs-standalone-failover-update-app-topo-in-xmaster"
	StepStandaloneFailoverTryFixReplication          = "scs-standalone-failover-try-fix-replication"
)

func init() {
	// Step-01 进行故障切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverFailover,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     handover.ProcessHandoverStandaloneShardFromCsmaster,
		SuccessNextStep: StepStandaloneFailoverUpdateAzDeployInfo,
		ErrorNextStep:   StepStandaloneFailoverFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverUpdateAzDeployInfo,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     handover.ProcessHandoverUpdateAzDeployInfo,
		SuccessNextStep: StepStandaloneFailoverUpdateRoTopo,
		ErrorNextStep:   StepStandaloneFailoverUpdateAzDeployInfo},
		workflow.WithMaxReentry(3, StepStandaloneFailoverUpdateRoTopo))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverUpdateRoTopo,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     topology.ProcessTopoForRo,
		SuccessNextStep: StepStandaloneFailoverBindRs,
		ErrorNextStep:   StepStandaloneFailoverUpdateRoTopo},
		workflow.WithMaxReentry(1, StepStandaloneFailoverBindRs))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverBindRs,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     blb.ProcessFailoverSetStandaloneRs,
		SuccessNextStep: StepStandaloneFailoverOldMasterClientKill,
		ErrorNextStep:   StepStandaloneFailoverBindRs,
	})

	// Step-02 关闭旧主节点的客户端连接
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverOldMasterClientKill,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     handover.ProcessOldMasterClientKill,
		SuccessNextStep: StepStandaloneFailoverResetFlag,
		ErrorNextStep:   StepStandaloneFailoverOldMasterClientKill,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverResetFlag,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     handover.ProcessResetCsmasterSwitchFlag,
		SuccessNextStep: StepStandaloneFailoverUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepStandaloneFailoverResetFlag,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverUpdateAppTopologyInXmaster,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepStandaloneFailoverTryFixReplication,
		ErrorNextStep:   StepStandaloneFailoverUpdateAppTopologyInXmaster,
	})

	// 尝试检查并修复复制关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverTryFixReplication,
		Workflow:        WorkflowStandaloneFailover,
		StepProcess:     handover.ProcessTryFixReplication,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepStandaloneFailoverTryFixReplication,
	},
		// 修复复制关系，单次超时时间10s
		workflow.WithStepTimeout(10*time.Second),
		// 修复复制关系，最多重试5次，共执行6次; 实测发现，设置为5，一共执行了7次，故改成4
		workflow.WithMaxReentry(4, workflow.FinalStepSuccess),
	)
}
