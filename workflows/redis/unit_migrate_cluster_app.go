/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/11/09, by wang<PERSON><PERSON>(wang<PERSON><EMAIL>), unit-migrate
*/

/*
DESCRIPTION
对容器化集群进行集群版实例替换

Parameters

	{
	   "app_id" : "scs-gz-figylcdzkpag",
	   "self_heal_from_csmaster" : {
	      "node_short_ids" : [
	         75069
	      ]
	   }
	   // 指定 label 迁移
	   "resource_labels": [
	        "uuid=79b24f0d-50d9-4fab-8e03-202bd9688c5c"
	   ]
	}

	{
	   "app_id" : "scs-gz-dbvhpylpeqnp",
	   "self_heal_from_csmaster" : {
	      "proxy_short_ids" : [
	         74329
	      ]
	   },
	   // 指定 label 迁移
	   "resource_labels": [
	        "uuid=79b24f0d-50d9-4fab-8e03-202bd9688c5c"
	   ]
	}
*/
package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowUnitMigrateCluster = "scs-unit-migrate-cluster-app"
	// 1 构建元数据
	StepUnitMigrateClusterBuildMeta = "scs-unit-migrate-cluster-build-meta"
	StepUnitMigrateClusterFillSpec  = "scs-unit-migrate-cluster-fill-spec"
	// 2 申请资源前置检查
	StepUnitMigrateClusterCheckSubnetsEnoughIps = "scs-unit-migrate-cluster-check-subnets-enough-ips"
	// 3 申请资源
	StepUnitMigrateClusterApplyResources         = "scs-unit-migrate-cluster-apply-resources"
	StepUnitMigrateClusterApplyResourcesCallback = "scs-unit-migrate-cluster-apply-resources-cb"
	// 4 部署服务前置任务
	StepUnitMigrateClusterInitMachineEnv           = "scs-unit-migrate-cluster-init-machine-env"
	StepUnitMigrateClusterUpdateSecurityGroups     = "scs-unit-migrate-cluster-update-security-groups"
	StepUnitMigrateClusterUpdateGlobalTopoForProxy = "scs-unit-migrate-cluster-update-global-topo-for-proxy"
	StepUnitMigrateClusterMetaAddProxys            = "scs-unit-migrate-cluster-meta-add-proxys"
	// 5 部署服务
	StepUnitMigrateClusterDeployRedis           = "scs-unit-migrate-cluster-apply-deploy-redis"
	StepUnitMigrateClusterSlaveOfGlobal         = "scs-unit-migrate-cluster-slaveof-global"
	StepUnitMigrateClusterSlaveOfLocal          = "scs-unit-migrate-cluster-slaveof-local"
	StepUnitMigrateClusterUpdateTlsConfIfNeeded = "scs-unit-migrate-cluster-update-tls-conf-if-needed"
	StepUnitMigrateClusterDeployFilebeat        = "scs-unit-migrate-cluster-apply-deploy-filebeat"
	// 6 检查数据同步
	StepUnitMigrateClusterCheckShardSync = "scs-unit-migrate-cluster-check-shard-sync"
	// 7 注册信息
	StepUnitMigrateClusterUpdateGlobalTopo = "scs-unit-migrate-cluster-update-global-topo"
	StepUnitMigrateClusterMetaAddNodes     = "scs-unit-migrate-cluster-meta-add-nodes"
	StepUnitMigrateClusterSetRs            = "scs-unit-migrate-cluster-set-rs"
	StepUnitMigrateClusterSetMcpackRs      = "scs-unit-migrate-cluster-set-mcpack-rs"
	StepUnitMigrateClusterSetBnsInstance   = "scs-unit-migrate-cluster-set-bns-instance"
	// 8 删除旧节点及更新 flag
	StepUnitMigrateClusterMetaDelNodes               = "scs-unit-migrate-cluster-meta-del-nodes"
	StepUnitMigrateClusterPushMonitorHTGRPSlaveFlag  = "scs-unit-migrate-cluster-push-htgrp-slave-flag"
	StepUnitMigrateClusterPushFlag                   = "scs-unit-migrate-cluster-push-flag"
	StepUnitMigrateClusterDeleteOldNodes             = "scs-unit-migrate-cluster-del-old-nodes"
	StepUnitMigrateClusterResetFailoverFlag          = "scs-unit-migrate-cluster-reset-failover-flag"
	StepUnitMigrateClusterUpdateAppTopologyInXmaster = "scs-unit-migrate-cluster-update-app-topo-in-xmaster"
	StepUnitMigrateClusterInitOpMonitor              = "scs-unit-migrate-cluster-init-op-monitor"
	StepUnitMigrateClusterSuccessCallback            = "scs-unit-migrate-cluster-succ-cb"

	StepUnitMigrateClusterRollbackReleaseResources           = "scs-unit-migrate-cluster-rollback-release-resource"
	StepUnitMigrateClusterRollbackPushFlag                   = "scs-unit-migrate-cluster-rollback-push-flag"
	StepUnitMigrateClusterRollbackBuildMeta                  = "scs-unit-migrate-cluster-rollback-build-meta"
	StepUnitMigrateClusterRollbackCallback                   = "scs-unit-migrate-cluster-rollback-cb"
	StepUnitMigrateClusterRollbackUpdateAppTopologyInXmaster = "scs-unit-migrate-cluster-rollback-update-app-topo-in-xmaster"
)

func init() {
	// 1 构建元数据，将需要删除的节点改为ToDelete、新加节点ToCreate
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterBuildMeta,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForClusterUnitMigrate,
		SuccessNextStep: StepUnitMigrateClusterFillSpec,
		ErrorNextStep:   StepUnitMigrateClusterBuildMeta,
	})

	// 将规格信息填入Cluster表或Interface表；
	// 同分片的node应当规格相同，Port相同，使用同样的部署集
	// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterFillSpec,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepUnitMigrateClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepUnitMigrateClusterFillSpec,
	})
	// 2 申请资源前置检查, 检查子网ip是否充足
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterCheckSubnetsEnoughIps,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepUnitMigrateClusterApplyResources,
		ErrorNextStep:   StepUnitMigrateClusterCheckSubnetsEnoughIps,
	})

	// 3 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterApplyResources,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepUnitMigrateClusterApplyResourcesCallback,
		ErrorNextStep:   StepUnitMigrateClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, StepUnitMigrateClusterRollbackReleaseResources))

	// 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterApplyResourcesCallback,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepUnitMigrateClusterInitMachineEnv,
		ErrorNextStep:   StepUnitMigrateClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterInitMachineEnv,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepUnitMigrateClusterUpdateSecurityGroups,
		ErrorNextStep:   StepUnitMigrateClusterInitMachineEnv,
	})
	// 4 部署服务前置任务
	// 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterUpdateSecurityGroups,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: StepUnitMigrateClusterUpdateGlobalTopoForProxy,
		ErrorNextStep:   StepUnitMigrateClusterUpdateSecurityGroups,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterUpdateGlobalTopoForProxy,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     metaserver.ProcessAddGlobalProxys,
		SuccessNextStep: StepUnitMigrateClusterMetaAddProxys,
		ErrorNextStep:   StepUnitMigrateClusterUpdateGlobalTopoForProxy,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterMetaAddProxys,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     metaserver.ProcessAddProxys,
		SuccessNextStep: StepUnitMigrateClusterDeployRedis,
		ErrorNextStep:   StepUnitMigrateClusterMetaAddProxys,
	})

	// 5 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterDeployRedis,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepUnitMigrateClusterSlaveOfGlobal,
		ErrorNextStep:   StepUnitMigrateClusterDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterSlaveOfGlobal,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: StepUnitMigrateClusterSlaveOfLocal,
		ErrorNextStep:   StepUnitMigrateClusterSlaveOfGlobal,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterSlaveOfLocal,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     handover.ProcessLocalSlaveOf,
		SuccessNextStep: StepUnitMigrateClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepUnitMigrateClusterSlaveOfLocal,
	})

	// 包管理todo 检查能否加在这里
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterUpdateTlsConfIfNeeded,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     util.UpdateTLSConfIfNeededForNew,
		SuccessNextStep: StepUnitMigrateClusterDeployFilebeat,
		ErrorNextStep:   StepUnitMigrateClusterUpdateTlsConfIfNeeded,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterDeployFilebeat,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepUnitMigrateClusterCheckShardSync,
		ErrorNextStep:   StepUnitMigrateClusterDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// 检查对应分片的主从同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterCheckShardSync,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     syncredis.ProcessCheckShardHasSyncForClusterFailover,
		SuccessNextStep: StepUnitMigrateClusterUpdateGlobalTopo,
		ErrorNextStep:   StepUnitMigrateClusterCheckShardSync,
	})

	// metaserver
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterUpdateGlobalTopo,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepUnitMigrateClusterMetaAddNodes,
		ErrorNextStep:   StepUnitMigrateClusterUpdateGlobalTopo,
	})

	// metaserver添加节点
	// 对于主从版，仅设置主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterMetaAddNodes,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     metaserver.ProcessAddNodes,
		SuccessNextStep: StepUnitMigrateClusterSetRs,
		ErrorNextStep:   StepUnitMigrateClusterMetaAddNodes,
	})

	// Step-9 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterSetRs,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepUnitMigrateClusterSetMcpackRs,
		ErrorNextStep:   StepUnitMigrateClusterSetRs,
	})

	// Step-9 绑定BLB的rs(for mcpack)
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterSetMcpackRs,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepUnitMigrateClusterSetBnsInstance,
		ErrorNextStep:   StepUnitMigrateClusterSetMcpackRs,
	})

	// Step-9 绑定bns instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterSetBnsInstance,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: StepUnitMigrateClusterMetaDelNodes,
		ErrorNextStep:   StepUnitMigrateClusterSetBnsInstance,
	})

	// Step-10 metaserver删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterMetaDelNodes,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepUnitMigrateClusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepUnitMigrateClusterMetaDelNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepUnitMigrateClusterPushFlag,
		ErrorNextStep:   StepUnitMigrateClusterPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterPushFlag,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepUnitMigrateClusterDeleteOldNodes,
		ErrorNextStep:   StepUnitMigrateClusterPushFlag,
	})

	// Step-11 删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterDeleteOldNodes,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepUnitMigrateClusterResetFailoverFlag,
		ErrorNextStep:   StepUnitMigrateClusterDeleteOldNodes,
	})

	// Step-12 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterResetFailoverFlag,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     handover.ProcessResetFailoveredShards,
		SuccessNextStep: StepUnitMigrateClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepUnitMigrateClusterResetFailoverFlag,
	})

	// Step-13 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepUnitMigrateClusterInitOpMonitor,
		ErrorNextStep:   StepUnitMigrateClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterInitOpMonitor,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepUnitMigrateClusterSuccessCallback,
		ErrorNextStep:   StepUnitMigrateClusterInitOpMonitor,
	})

	// Step-13 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterSuccessCallback,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUnitMigrateClusterSuccessCallback,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterRollbackReleaseResources,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepUnitMigrateClusterRollbackPushFlag,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterRollbackPushFlag,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepUnitMigrateClusterRollbackBuildMeta,
		ErrorNextStep:   StepUnitMigrateClusterRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterRollbackBuildMeta,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepUnitMigrateClusterRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// 回滚节点状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterRollbackCallback,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: StepUnitMigrateClusterRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateClusterRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowUnitMigrateCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
