/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), self-healing
*/

/*
DESCRIPTION
进行标准版集群故障节点自愈

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"SelfHealingParams": [
		{
			"ShardID": 133,
			"NodeShortIDList": [12345, 12346]
		}
	]
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/appStateManage"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/availability"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowSelfHealStandalone                               = "scs-failover-standalone-app"
	StepSelfHealStandaloneUpdateAppStatus                    = "scs-self-heal-standalone-update-app-status"
	StepSelfHealStandaloneLockGroup                          = "scs-self-heal-standalone-lock-global-group"
	StepSelfHealStandaloneAddStartEvent                      = "scs-self-heal-standalone-add-start-event"
	StepSelfHealStandaloneBuildMeta                          = "scs-self-heal-standalone-app-build-meta"
	StepSelfHealStandaloneSetRoTopology                      = "scs-self-heal-standalone-app-set-ro-topology"
	StepSelfHealStandaloneFillSpec                           = "scs-self-heal-standalone-fill-spec"
	StepSelfHealStandaloneTryTrafficRemoval                  = "scs-self-heal-standalone-try-traffic-removal"
	StepSelfHealStandaloneTrafficRemovalCallback             = "scs-self-heal-standalone-traffic-removal-cb"
	StepSelfHealStandaloneCheckSubnetsEnoughIps              = "scs-self-heal-standalone-check-subnets-enough-ips"
	StepSelfHealStandaloneApplyResources                     = "scs-self-heal-standalone-apply-resources"
	StepSelfHealStandaloneApplyResourcesCallback             = "scs-self-heal-standalone-apply-resources-cb"
	StepSelfHealStandaloneInitMachineEnv                     = "scs-self-heal-standalone-init-machine-env"
	StepSelfHealStandaloneUpdateGlobalTopo                   = "scs-self-heal-standalone-update-global-topo"
	StepSelfHealStandaloneUpdateSecurityGroups               = "scs-self-heal-standalone-update-security-groups"
	StepSelfHealStandaloneSingleReplicaCorrect               = "scs-self-heal-standalone-single-replica-correct"
	StepSelfHealStandaloneDeployRedis                        = "scs-self-heal-standalone-apply-deploy-redis"
	StepSelfHealStandaloneSetAcl                             = "scs-self-heal-standalone-set-acl"
	StepSelfHealStandaloneSlaveOfGlobal                      = "scs-self-heal-standalone-slaveof-global"
	StepSelfHealStandaloneSlaveOfLocal                       = "scs-self-heal-standalone-slaveof-local"
	StepSelfHealStandaloneDeployFilebeat                     = "scs-self-heal-standalone-apply-deploy-Filebeat"
	StepSelfHealStandaloneSecondSetRoTopology                = "scs-self-heal-standalone-app-second-set-ro-topology"
	StepSelfHealStandaloneCheckShardSync                     = "scs-self-heal-standalone-check-shard-sync"
	StepSelfHealStandaloneSetRs                              = "scs-self-heal-standalone-set-rs"
	StepSelfHealStandaloneSetRoRs                            = "scs-self-heal-standalone-set-ro-rs"
	StepSelfHealStandaloneSetEntranceRs                      = "scs-self-heal-standalone-set-entrance-rs"
	StepSelfHealStandalonePushMonitorHTGRPSlaveFlag          = "scs-self-heal-standalone-push-htgrp-slave-flag"
	StepSelfHealStandalonePushFlag                           = "scs-self-heal-standalone-push-flag"
	StepSelfHealStandaloneGlobalDelNodes                     = "scs-self-heal-standalone-global-del-nodes"
	StepSelfHealStandaloneDeleteOldNodes                     = "scs-self-heal-standalone-del-old-nodes"
	StepSelfHealStandaloneResetFailoverFlag                  = "scs-self-heal-standalone-reset-failover-flag"
	StepSelfHealStandaloneUpdateAppTopologyInXmaster         = "scs-self-heal-standalone-update-app-topo-in-xmaster"
	StepSelfHealStandaloneInitOpMonitor                      = "scs-self-heal-standalone-init-op-monitor"
	StepSelfHealStandaloneReleaseGroup                       = "scs-self-heal-standalone-release-global-group"
	StepSelfHealStandaloneAddSuccessEvent                    = "scs-self-heal-standalone-add-success-event"
	StepSelfHealStandaloneSuccessCallback                    = "scs-self-heal-standalone-succ-cb"
	StepSelfHealStandaloneRollbackReleaseResources           = "scs-self-heal-standalone-rollback-release-resource"
	StepSelfHealStandaloneRollbackPushFlag                   = "scs-self-heal-standalone-rollback-push-flag"
	StepSelfHealStandaloneRollbackMeta                       = "scs-self-heal-standalone-rollback-meta"
	StepSelfHealStandaloneRollbackCallback                   = "scs-self-heal-standalone-rollback-cb"
	StepSelfHealStandaloneRollbackUpdateAppTopologyInXmaster = "scs-self-heal-standalone-rollback-update-app-topo-in-xmaster"
	StepSelfHealStandaloneAddFailEvent                       = "scs-self-heal-standalone-add-fail-event"
)

func init() {
	// Step-1 检查任务来源，若任务来自xmaster，则更新cache_cluster.status为17
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneUpdateAppStatus,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     appStateManage.UpdateAppStatusForSelfHealing,
		SuccessNextStep: StepSelfHealStandaloneLockGroup,
		ErrorNextStep:   StepSelfHealStandaloneUpdateAppStatus,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneLockGroup,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     gmaster.TryToGetGlobalSelfHealLock,
		SuccessNextStep: StepSelfHealStandaloneAddStartEvent,
		ErrorNextStep:   StepSelfHealStandaloneLockGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneAddStartEvent,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     event.ProcessSelfHealingStartEvent,
		SuccessNextStep: StepSelfHealStandaloneBuildMeta,
		ErrorNextStep:   StepSelfHealStandaloneAddStartEvent,
	})

	// Step-1 构建元数据，将需要删除的节点改为ToDelete、新加节点ToCreate
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneBuildMeta,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForSelfHealing,
		SuccessNextStep: StepSelfHealStandaloneSetRoTopology,
		ErrorNextStep:   StepSelfHealStandaloneBuildMeta,
	})

	// 更新热活组中的只读实例的主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSetRoTopology,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepSelfHealStandaloneFillSpec,
		ErrorNextStep:   StepSelfHealStandaloneSetRoTopology},

		workflow.WithMaxReentry(1, StepSelfHealStandaloneFillSpec))

	// Step-2 将规格信息填入Cluster表或Interface表；
	// 同分片的node应当规格相同，Port相同，使用同样的部署集
	// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneFillSpec,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepSelfHealStandaloneTryTrafficRemoval,
		ErrorNextStep:   StepSelfHealStandaloneFillSpec,
	})

	// 尝试创建提前摘流子任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneTryTrafficRemoval,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     availability.ProcessStandaloneCreateSubTaskForTrafficRemoval,
		SuccessNextStep: StepSelfHealStandaloneTrafficRemovalCallback,
		ErrorNextStep:   StepSelfHealStandaloneTryTrafficRemoval},
		workflow.WithMaxReentry(1, StepSelfHealStandaloneTrafficRemovalCallback),
	)

	// 检查提前摘流子任务结果
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneTrafficRemovalCallback,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     availability.ProcessTrafficRemovalCallback,
		SuccessNextStep: StepSelfHealStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepSelfHealStandaloneTrafficRemovalCallback,
	})

	// Step-4 检查子网ip是否充足
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneCheckSubnetsEnoughIps,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepSelfHealStandaloneApplyResources,
		ErrorNextStep:   StepSelfHealStandaloneCheckSubnetsEnoughIps,
	})

	// Step-6 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneApplyResources,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepSelfHealStandaloneApplyResourcesCallback,
		ErrorNextStep:   StepSelfHealStandaloneApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-6 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneApplyResourcesCallback,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepSelfHealStandaloneInitMachineEnv,
		ErrorNextStep:   StepSelfHealStandaloneApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneInitMachineEnv,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepSelfHealStandaloneUpdateGlobalTopo,
		ErrorNextStep:   StepSelfHealStandaloneInitMachineEnv,
	})

	// 更新标准版热活topo
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneUpdateGlobalTopo,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepSelfHealStandaloneUpdateSecurityGroups,
		ErrorNextStep:   StepSelfHealStandaloneUpdateGlobalTopo,
	})

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneUpdateSecurityGroups,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandalone,
		SuccessNextStep: StepSelfHealStandaloneSingleReplicaCorrect,
		ErrorNextStep:   StepSelfHealStandaloneUpdateSecurityGroups,
	})

	// // 更新只读实例的安全组规则
	// _ = workflow.AddStep(&workflow.AddStepParam{
	// 	Name:            StepSelfHealStandaloneUpdateRoSecurityGroups,
	// 	Workflow:        WorkflowSelfHealStandalone,
	// 	StepProcess:     securitygroup.ProcessRebuildRoSecurityGroupStandalone,
	// 	SuccessNextStep: StepSelfHealStandaloneDeployRedis,
	// 	ErrorNextStep:   StepSelfHealStandaloneUpdateRoSecurityGroups,
	// })

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSingleReplicaCorrect,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     topology.ProcessSingleReplicaMasterRedisCorrect,
		SuccessNextStep: StepSelfHealStandaloneDeployRedis,
		ErrorNextStep:   StepSelfHealStandaloneSingleReplicaCorrect})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneDeployRedis,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepSelfHealStandaloneSetAcl,
		ErrorNextStep:   StepSelfHealStandaloneDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-7 设置初始auth,acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSetAcl,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     acl.ProcessInitAclStandaloneForNewNode,
		SuccessNextStep: StepSelfHealStandaloneSlaveOfGlobal,
		ErrorNextStep:   StepSelfHealStandaloneSetAcl,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSlaveOfGlobal,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: StepSelfHealStandaloneSlaveOfLocal,
		ErrorNextStep:   StepSelfHealStandaloneSlaveOfGlobal,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSlaveOfLocal,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     handover.ProcessLocalSlaveOf,
		SuccessNextStep: StepSelfHealStandaloneDeployFilebeat,
		ErrorNextStep:   StepSelfHealStandaloneSlaveOfLocal,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneDeployFilebeat,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepSelfHealStandaloneSecondSetRoTopology,
		ErrorNextStep:   StepSelfHealStandaloneDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// 第二次更新热活组中的只读实例的主从关系
	// 注意: 存在场景：从节点都跪了，只下发了部分只读实例的自愈，
	// 导致只读实例slaveof 失败，影响后续流程自愈
	// 增加重试后，故障的只读实例会再次自愈，直到能成功slaveof
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSecondSetRoTopology,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepSelfHealStandaloneCheckShardSync,
		ErrorNextStep:   StepSelfHealStandaloneSecondSetRoTopology},

		workflow.WithMaxReentry(1, StepSelfHealStandaloneCheckShardSync))

	// 检查对应分片的主从同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneCheckShardSync,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     syncredis.ProcessCheckShardHasSyncForStandaloneFailover,
		SuccessNextStep: StepSelfHealStandaloneSetRs,
		ErrorNextStep:   StepSelfHealStandaloneCheckShardSync,
	})

	// Step-9 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSetRs,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     blb.ProcessUpdateStandaloneRsForSelfHeal,
		SuccessNextStep: StepSelfHealStandaloneSetRoRs,
		ErrorNextStep:   StepSelfHealStandaloneSetRs,
	})

	// Step-10 绑定只读组rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSetRoRs,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     blb.ProcessSetReadonlyGroupRs,
		SuccessNextStep: StepSelfHealStandaloneSetEntranceRs,
		ErrorNextStep:   StepSelfHealStandaloneSetRoRs,
	})

	// Step-10 绑定只读组rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSetEntranceRs,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepSelfHealStandalonePushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepSelfHealStandaloneSetEntranceRs,
	})

	// 推送热活从地域flag
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandalonePushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealStandalonePushFlag,
		ErrorNextStep:   StepSelfHealStandalonePushMonitorHTGRPSlaveFlag,
	})

	// 更新监控推送Flag
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandalonePushFlag,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealStandaloneGlobalDelNodes,
		ErrorNextStep:   StepSelfHealStandalonePushFlag,
	})

	// 标准版热活删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneGlobalDelNodes,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     metaserver.ProcessDelNodesForStandalone,
		SuccessNextStep: StepSelfHealStandaloneDeleteOldNodes,
		ErrorNextStep:   StepSelfHealStandaloneGlobalDelNodes,
	})

	// Step-10 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneDeleteOldNodes,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepSelfHealStandaloneResetFailoverFlag,
		ErrorNextStep:   StepSelfHealStandaloneDeleteOldNodes,
	})

	// 重置failover flag
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneResetFailoverFlag,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     handover.ProcessResetFailoveredShards,
		SuccessNextStep: StepSelfHealStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepSelfHealStandaloneResetFailoverFlag,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepSelfHealStandaloneInitOpMonitor,
		ErrorNextStep:   StepSelfHealStandaloneUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneInitOpMonitor,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepSelfHealStandaloneReleaseGroup,
		ErrorNextStep:   StepSelfHealStandaloneInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneReleaseGroup,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     gmaster.TryToReleaseGlobalSelfHealLock,
		SuccessNextStep: StepSelfHealStandaloneAddSuccessEvent,
		ErrorNextStep:   StepSelfHealStandaloneReleaseGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneAddSuccessEvent,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     event.ProcessSelfHealingSuccessEndEvent,
		SuccessNextStep: StepSelfHealStandaloneSuccessCallback,
		ErrorNextStep:   StepSelfHealStandaloneAddSuccessEvent,
	})

	// Step-11 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneSuccessCallback,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSelfHealStandaloneSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneRollbackReleaseResources,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepSelfHealStandaloneRollbackPushFlag,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneRollbackPushFlag,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealStandaloneAddFailEvent,
		ErrorNextStep:   StepSelfHealStandaloneRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneAddFailEvent,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     event.ProcessSelfHealingFailEndEvent,
		SuccessNextStep: StepSelfHealStandaloneRollbackMeta,
		ErrorNextStep:   StepSelfHealStandaloneAddFailEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneRollbackMeta,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepSelfHealStandaloneRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneRollbackCallback,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: StepSelfHealStandaloneRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSelfHealStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
