/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/13 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_join_standalone_group.go
 * <AUTHOR>
 * @date 2022/07/13 16:35:43
 * @brief join
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalJoinStandaloneGroup definition
const (
	WorkFlowLocalJoinStandaloneGroup                               = "scs-local-join-standalone-group"
	WorkFlowLocalJoinStandaloneGroupStepDisableFlashback           = "scs-local-join-standalone-group-disable-flashback"
	WorkFlowLocalJoinStandaloneGroupStepBlockMonitorInXmaster      = "scs-local-join-standalone-group-block-monitor-in-xmaster"
	WorkFlowLocalJoinStandaloneGroupStepBuildMeta                  = "scs-local-join-standalone-group-build-meta"
	WorkFlowLocalJoinStandaloneGroupStepRegisterNodes              = "scs-local-join-standalone-group-register-nodes"
	WorkFlowLocalJoinStandaloneGroupStepUpdateSg                   = "scs-local-join-standalone-group-update-sg"
	WorkFlowLocalJoinStandaloneGroupStepBindMasterNode             = "scs-local-join-standalone-group-bind-master-node"
	WorkFlowLocalJoinStandaloneGroupStepBindRs                     = "scs-local-join-standalone-group-bind-rs"
	WorkFlowLocalJoinStandaloneGroupStepBindEntranceRs             = "scs-local-join-standalone-group-bind-entrance-rs"
	WorkFlowLocalJoinStandaloneGroupPushMonitorHTGRPSlaveFlag      = "scs-local-join-standalone-group-push-htgrp-slave-flag"
	WorkFlowLocalJoinStandaloneGroupStepUpdateAppTopologyInXmaster = "scs-local-join-standalone-group-update-app-topo-in-xmaster"
	WorkFlowLocalJoinStandaloneGroupStepUnblockMonitorInXmaster    = "scs-local-join-standalone-group-unblock-monitor-in-xmaster"
	WorkFlowLocalJoinStandaloneGroupStepSuccessCb                  = "scs-local-join-standalone-group-success-cb"
)

func init() {
	// 关闭闪回功能
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepDisableFlashback,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOFAndCloseOpheader,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepBlockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepDisableFlashback,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepBuildMeta,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepBlockMonitorInXmaster,
	})

	// 查询global master，获取shard信息，将GlobalShardID写入数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepBuildMeta,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForJoinStandaloneGroup,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepRegisterNodes,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepBuildMeta,
	})
	// 调用global-master-api，将redis、proxy信息注册至global master
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepRegisterNodes,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     gmaster.ProcessRegisterStandaloneNodesToGlobalMasterAsSlave,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepUpdateSg,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepRegisterNodes,
	})
	// 更新security group白名单ip，将热活实例组中所有地域、所有redis、proxy的fixip加入；更新acl信息保证与globalmaster一致
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepUpdateSg,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandalone,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepBindMasterNode,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepUpdateSg,
	})

	// 调用global-master-api获取主redis信息，将slave挂载到对应主，并修改数据库中元数据信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepBindMasterNode,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepBindRs,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepBindMasterNode,
	})

	// 更新blb的绑定rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepBindRs,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     blb.ProcessUpdateStandaloneRs,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepBindEntranceRs,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepBindRs,
	})

	// 更新entrance blb的绑定rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepBindEntranceRs,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepBindEntranceRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepUnblockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupStepSuccessCb,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     callback.ProcessGlobalGeneralSuccessCb,
		SuccessNextStep: WorkFlowLocalJoinStandaloneGroupPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupStepSuccessCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinStandaloneGroupPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalJoinStandaloneGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAll,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalJoinStandaloneGroupPushMonitorHTGRPSlaveFlag,
	})

}
