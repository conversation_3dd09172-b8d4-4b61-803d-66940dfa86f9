/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建SCS标准版实例WORKFLOW

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"DestSpec": "cache.n1.small"
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/resize"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifySpecStandalone = "scs-modify-spec-standalone-app"

	StepModifyModifySpecStandaloneInitTimeWindowTask   = "scs-modify-spec-standalone-init-time-window-task"
	StepModifyModifySpecStandaloneGlobalInit           = "scs-modify-spec-standalone-global-init"
	StepModifySpecStandaloneAddStartEvent              = "scs-modify-spec-standalone-add-start-event"
	StepModifySpecStandaloneBuildMeta                  = "scs-modify-spec-app-build-meta"
	StepModifySpecStandaloneTaskStepTryResizeNodes     = "scs-modify-spec-standalone-task-step-try-resize-nodes"
	StepModifySpecStandaloneFillSpec                   = "scs-modify-spec-fill-spec"
	StepModifySpecStandaloneUpdateOpType               = "scs-modify-spec-update-op-type"
	StepModifySpecStandaloneResize                     = "scs-modify-spec-resize"
	StepModifySpecStandaloneTaskStepCreateNodes        = "scs-modify-spec-standalone-task-step-create-nodes"
	StepModifySpecStandaloneCheckSubnetsEnoughIps      = "scs-modify-spec-check-subnets-enough-ips"
	StepModifySpecStandaloneApplyResources             = "scs-modify-spec-apply-resources"
	StepModifySpecStandaloneApplyResourcesCallback     = "scs-modify-spec-apply-resources-cb"
	StepModifySpecStandaloneTaskStepDeployNodes        = "scs-modify-spec-standalone-task-step-deploy-nodes"
	StepModifySpecStandaloneInitMachineEnv             = "scs-modify-spec-init-machine-env"
	StepModifySpecStandaloneApplyGAddNodes             = "scs-modify-spec-apply-global-add-nodes"
	StepModifySpecStandaloneGlobalAfterApplyResource   = "scs-modify-spec-standalone-global-after-apply-resource"
	StepModifySpecStandaloneUpdateSecurityGroups       = "scs-modify-spec-update-security-groups"
	StepModifySpecStandaloneDeployRedis                = "scs-modify-spec-apply-deploy-redis"
	StepModifySpecStandaloneTaskStepConfigNodes        = "scs-modify-spec-standalone-task-step-config-nodes"
	StepModifySpecStandaloneSetAcl                     = "scs-modify-spec-set-acl"
	StepModifySpecStandaloneUpdateConfig               = "scs-modify-spec-update-config"
	StepModifySpecStandaloneInitTopology               = "scs-modify-spec-init-topology"
	StepModifySpecStandaloneTaskStepSync               = "scs-modify-spec-standalone-task-step-sync"
	StepModifySpecStandaloneNewMasterSyncCheck         = "scs-modify-spec-standalone-new-master-sync-check"
	StepModifySpecStandaloneInitGlobalTopology         = "scs-modify-spec-init-global-topology"
	StepModifySpecStandaloneInitGlobalTopologyRo       = "scs-modify-spec-init-global-topology-ro"
	StepModifySpecStandaloneCheckGlobalSynced          = "scs-modify-spec-check-global-synced"
	StepModifySpecStandaloneGlobalShiftBLB 			   = "scs-modify-spec-global-shift-blb"
	StepModifySpecStandaloneDeployFilebeat             = "scs-modify-spec-apply-deploy-filebeat"
	StepModifySpecStandaloneSGlobalAllNodeAddedCheck   = "scs-modify-spec-standalone-all-gnode-added-check"
	StepModifySpecStandaloneTaskStepHandover           = "scs-modify-spec-standalone-task-step-handover"
	StepModifySpecStandaloneHandover                   = "scs-modify-spec-standalone-handover"
	StepModifySpecStandaloneGlobalHandover             = "scs-modify-spec-standalone-global-handover"
	StepModifySpecStandaloneGlobalHandoverCheck        = "scs-modify-spec-standalone-global-handover-check"
	StepModifySpecStandaloneGlobalSwitchBlb            = "scs-modify-spec-standalone-global-switch-blb"
	StepModifySpecStandaloneSetRoRs                    = "scs-modify-spec-standalone-set-ro-rs"
	StepModifySpecStandaloneSetRoTopology              = "scs-modify-spec-standalone-set-ro-topology"
	StepModifySpecStandaloneSetEntranceRs              = "scs-modify-spec-standalone-set-entrance-rs"
	StepModifySpecStandalonePushMonitorHTGRPSlaveFlag  = "scs-modify-spec-standalone-push-htgrp-slave-flag"
	StepModifySpecStandalonePushFlag                   = "scs-modify-spec-standalone-push-flag"
	StepModifySpecStandaloneResizeExecute              = "scs-modify-spec-standalone-resize-exec"
	StepModifySpecStandaloneNewSlaveSyncCheck          = "scs-modify-spec-standalone-new-slave-sync-check"
	StepModifySpecStandaloneTaskStepReleaseNodes       = "scs-modify-spec-standalone-task-step-release-nodes"
	StepModifySpecStandaloneDelGNode                   = "scs-modify-spec-standalone-del-global-node"
	StepModifySpecStandaloneDeleteOldNodes             = "scs-modify-spec-standalone-del-old-nodes"
	StepModifySpecStandaloneCommitSpec                 = "scs-modify-spec-standalone-commit-spec"
	StepModifySpecStandaloneUpdateOpTypeCb             = "scs-modify-spec-standalone-update-op-type-cb"
	StepModifySpecStandaloneUpdateAppTopologyInXmaster = "scs-modify-spec-standalone-update-app-topo-in-xmaster"
	StepModifySpecStandaloneSuccessCallbackGlobal      = "scs-modify-spec-standalone-succ-cb-global"
	StepModifySpecStandaloneSuccessResetGlobalSg       = "scs-modify-spec-standalone-succ-reset-global-sg"
	StepModifySpecStandaloneInitOpMonitor              = "scs-modify-spec-standalone-init-op-monitor"
	StepModifySpecStandaloneCloseTimeWindowTask        = "scs-modify-spec-standalone-close-tw-task"
	StepModifySpecStandaloneSuccessCallback            = "scs-modify-spec-standalone-succ-cb"
	StepModifySpecStandaloneAddSuccessEndEvent         = "scs-modify-spec-standalone-add-success-end-event"
	StepModifySpecStandaloneRollbackReleaseResources   = "scs-modify-spec-standalone-rollback-release-resource"
	StepModifySpecStandaloneTaskStepError              = "scs-modify-spec-standalone-task-step-error"
	StepModifySpecStandaloneRollbackPushFlag           = "scs-modify-spec-standalone-rollback-push-flag"
	StepModfiySpecStandaloneRollbackMeta               = "scs-modify-spec-standalone-rollback-meta"
	StepModifySpecStandaloneRollbackSpec               = "scs-modify-spec-standalone-rollback-spec"
	StepModifySpecStandaloneUpdateOpTypeRb             = "scs-modify-spec-standalone-update-op-type-rb"
	StepModifySpecStandaloneRollbackCallback           = "scs-modify-spec-standalone-rollback-cb"
	StepModifySpecStandaloneAddFailedEndEvent          = "scs-modify-spec-standalone-add-failed-end-event"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyModifySpecStandaloneInitTimeWindowTask,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: timewindow.ProcessSetTaskID,

		SuccessNextStep: StepModifyModifySpecStandaloneGlobalInit,
		ErrorNextStep:   StepModifyModifySpecStandaloneInitTimeWindowTask,
	})
	// Step1 热活注册任务
	// 主角色设置任务后继续执行
	// 从角色检查任务已经创建，未创建则一致等待
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyModifySpecStandaloneGlobalInit,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: gmaster.ProcessInitGlobalModifySpecStatus,

		SuccessNextStep: StepModifySpecStandaloneAddStartEvent,
		ErrorNextStep:   StepModifyModifySpecStandaloneGlobalInit,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneAddStartEvent,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     event.ProcessAddModifyingStartEvent,
		SuccessNextStep: StepModifySpecStandaloneBuildMeta,
		ErrorNextStep:   StepModifySpecStandaloneAddStartEvent,
	})

	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneBuildMeta,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForModifySpec,
		SuccessNextStep: StepModifySpecStandaloneTaskStepTryResizeNodes,
		ErrorNextStep:   StepModifySpecStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepTryResizeNodes,
		Workflow: WorkflowModifySpecStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepTryResizeNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecStandaloneFillSpec,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepTryResizeNodes,
	})

	// Step-2 将规格信息填入Cluster表或Interface表；
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneFillSpec,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepModifySpecStandaloneUpdateOpType,
		ErrorNextStep:   StepModifySpecStandaloneFillSpec,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneUpdateOpType,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     buildmeta.ProcessUpdateCsmasterOpTypeMasterChange,
		SuccessNextStep: StepModifySpecStandaloneResize,
		ErrorNextStep:   StepModifySpecStandaloneUpdateOpType,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneResize,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     resize.ProcessResizeVm,
		SuccessNextStep: StepModifySpecStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifySpecStandaloneResize},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 检查子网ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneCheckSubnetsEnoughIps,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepModifySpecStandaloneTaskStepCreateNodes,
		ErrorNextStep:   StepModifySpecStandaloneCheckSubnetsEnoughIps,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepCreateNodes,
		Workflow: WorkflowModifySpecStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecStandaloneApplyResources,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepCreateNodes,
	})

	// Step-5 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneApplyResources,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepModifySpecStandaloneApplyResourcesCallback,
		ErrorNextStep:   StepModifySpecStandaloneApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-6 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneApplyResourcesCallback,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepModifySpecStandaloneTaskStepDeployNodes,
		ErrorNextStep:   StepModifySpecStandaloneApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepDeployNodes,
		Workflow: WorkflowModifySpecStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecStandaloneInitMachineEnv,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneInitMachineEnv,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifySpecStandaloneApplyGAddNodes,
		ErrorNextStep:   StepModifySpecStandaloneInitMachineEnv,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneApplyGAddNodes,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     metaserver.ProcessAddGlobalNodesForModifySpec,
		SuccessNextStep: StepModifySpecStandaloneGlobalAfterApplyResource,
		ErrorNextStep:   StepModifySpecStandaloneApplyGAddNodes,
	})

	// 热活同步点1 等待所有成员都ModifyStageApplyResource 0%->50%
	// 申请资源&&添加global node元数据
	// 等待global node元数据都加入的原因是后面UpdateSecurityGroups会有竞争
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneGlobalAfterApplyResource,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: gmaster.ProcessCheckModifyStatusAfterApplyResource,

		SuccessNextStep: StepModifySpecStandaloneUpdateSecurityGroups,
		ErrorNextStep:   StepModifySpecStandaloneGlobalAfterApplyResource,
	}, workflow.WithStepTimeout(time.Hour*24*7))

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneUpdateSecurityGroups,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandaloneForModifySpec,
		SuccessNextStep: StepModifySpecStandaloneDeployRedis,
		ErrorNextStep:   StepModifySpecStandaloneUpdateSecurityGroups,
	})

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneDeployRedis,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepModifySpecStandaloneTaskStepConfigNodes,
		ErrorNextStep:   StepModifySpecStandaloneDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepConfigNodes,
		Workflow: WorkflowModifySpecStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecStandaloneSetAcl,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepConfigNodes})

	// Step-7 设置初始auth,acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSetAcl,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     acl.ProcessInitAclStandaloneForNewNode,
		SuccessNextStep: StepModifySpecStandaloneUpdateConfig,
		ErrorNextStep:   StepModifySpecStandaloneSetAcl,
	})

	// Step-8 更新配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneUpdateConfig,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     config.ProcessUpdateConfigStandalone,
		SuccessNextStep: StepModifySpecStandaloneInitTopology,
		ErrorNextStep:   StepModifySpecStandaloneUpdateConfig,
	}, workflow.WithMaxReentry(2, StepModifySpecStandaloneInitTopology))

	// Step-7 设置拓扑结构
	// 对于主从版，仅设置主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneInitTopology,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     topology.ProcessInitStandaloneTopo,
		SuccessNextStep: StepModifySpecStandaloneTaskStepSync,
		ErrorNextStep:   StepModifySpecStandaloneInitTopology,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepSync,
		Workflow: WorkflowModifySpecStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepSync, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecStandaloneNewMasterSyncCheck,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepSync,
	})

	// Step-8 同步数据
	// 将旧规格的数据同步到新规格，TopoCheckerProcessor::exchange_master_check
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneNewMasterSyncCheck,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     syncredis.ProcessSyncFromOldSpecStandalone,
		SuccessNextStep: StepModifySpecStandaloneInitGlobalTopology,
		ErrorNextStep:   StepModifySpecStandaloneNewMasterSyncCheck},

		workflow.WithStepTimeout(15*time.Minute))

	// 2025热活优化1-1 ：把新从、新只读、新主，拼装好（不管只读，只读给下一步操作）
	// 		主地域:负责调用global-api->api，让所有地域新节点都挂好
	// 		从地域:无操作
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneInitGlobalTopology,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     topology.ProcessInitStandaloneGlobalTopo,
		SuccessNextStep: StepModifySpecStandaloneInitGlobalTopologyRo,
		ErrorNextStep:   StepModifySpecStandaloneInitGlobalTopology})

	// 2025热活优化1-2 热活组把ro节点挂好
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneInitGlobalTopologyRo,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepModifySpecStandaloneCheckGlobalSynced,
		ErrorNextStep:   StepModifySpecStandaloneInitGlobalTopologyRo})

	// 2025热活优化2 ：使用info检查所有从节点（包括只读）角色都是slave且up
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneCheckGlobalSynced,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     syncredis.CheckAllNewGroupNodeSynced,
		SuccessNextStep: StepModifySpecStandaloneGlobalShiftBLB,
		ErrorNextStep:   StepModifySpecStandaloneCheckGlobalSynced})

	// 2025热活优化3 ：改lb
	// 		主地域:挂只读组lb、统一入口lb
	//		从地域:挂lb、只读组lb、统一入口lb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneGlobalShiftBLB,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     blb.ShiftLbForStandaloneGroupModifySpec,
		SuccessNextStep: StepModifySpecStandaloneDeployFilebeat,
		ErrorNextStep:   StepModifySpecStandaloneGlobalShiftBLB},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-9 部署filebeat
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneDeployFilebeat,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: deploy.ProcessDeployFilebeat,

		SuccessNextStep: StepModifySpecStandaloneSGlobalAllNodeAddedCheck,
		ErrorNextStep:   StepModifySpecStandaloneDeployFilebeat},

		workflow.WithStepSplitHandler(util.GetToCreateRedisIds),
		workflow.WithStepTimeout(time.Minute*15))

	// 热活同步点2 等待所有成员都ModifyStageApplyResource 50%->100% ，然后把stage改为ModifyStageMigration
	// 等待主角色完成数据同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneSGlobalAllNodeAddedCheck,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: gmaster.ProcessCheckModifyStatusBeforeMigration,

		SuccessNextStep: StepModifySpecStandaloneTaskStepHandover,
		ErrorNextStep:   StepModifySpecStandaloneSGlobalAllNodeAddedCheck,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepHandover,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepHandoverForSync, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifySpecStandaloneHandover,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepHandover,
	})

	// Step-9 切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneHandover,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     handover.ProcessHandoverToNewSpecStandalone,
		SuccessNextStep: StepModifySpecStandaloneGlobalHandover,
		ErrorNextStep:   StepModifySpecStandaloneHandover,
	})

	// 热活切换
	// 主角色local failover
	// 从角色直接返回
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneGlobalHandover,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     handover.ProcessHandoverStandaloneForModifySpec,
		SuccessNextStep: StepModifySpecStandaloneGlobalHandoverCheck,
		ErrorNextStep:   StepModifySpecStandaloneGlobalHandover,
	})

	// 热活同步点3 等待所有成员都ModifyStageMigration 0%->100% ，然后把stage改为ModifyStageReleaseResource
	// 从角色在这里等待，主角色完成上一步切换后在这里把所有角色的进度统一修改后继续
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneGlobalHandoverCheck,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: gmaster.ProcessModifySpecGlobalHandoverCheck,

		SuccessNextStep: StepModifySpecStandaloneGlobalSwitchBlb,
		ErrorNextStep:   StepModifySpecStandaloneGlobalHandoverCheck,
	})
	// 热活组更新blb RS
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneGlobalSwitchBlb,
		Workflow: WorkflowModifySpecStandalone,

		StepProcess: blb.ProcessUpdateStandaloneRsForGlobalModifySpec,

		SuccessNextStep: StepModifySpecStandaloneSetRoRs,
		ErrorNextStep:   StepModifySpecStandaloneGlobalSwitchBlb,
	})

	// Step-10 设置只读实例组rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSetRoRs,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     blb.ProcessSetReadonlyGroupRs,
		SuccessNextStep: StepModifySpecStandaloneSetEntranceRs,
		ErrorNextStep:   StepModifySpecStandaloneSetRoRs,
	})

	// Step-10 设置entrance rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSetEntranceRs,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepModifySpecStandaloneSetRoTopology,
		ErrorNextStep:   StepModifySpecStandaloneSetEntranceRs,
	})

	// 第二次更新热活组中的只读实例的主从关系
	// 注意: 存在场景：从节点都跪了，只下发了部分只读实例的自愈，
	// 导致只读实例slaveof 失败，影响后续流程自愈
	// 增加重试后，故障的只读实例会再次自愈，直到能成功slaveof
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSetRoTopology,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepModifySpecStandalonePushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifySpecStandaloneSetRoTopology},

		workflow.WithMaxReentry(1, StepModifySpecStandalonePushFlag))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandalonePushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepModifySpecStandalonePushFlag,
		ErrorNextStep:   StepModifySpecStandalonePushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandalonePushFlag,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifySpecStandaloneResizeExecute,
		ErrorNextStep:   StepModifySpecStandalonePushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneResizeExecute,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     resize.ProcessResizeMaxmemory,
		SuccessNextStep: StepModifySpecStandaloneNewSlaveSyncCheck,
		ErrorNextStep:   StepModifySpecStandaloneResizeExecute,
	})

	// 检查新从库与新主库是否同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneNewSlaveSyncCheck,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepModifySpecStandaloneTaskStepReleaseNodes,
		ErrorNextStep:   StepModifySpecStandaloneNewSlaveSyncCheck,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecStandaloneTaskStepReleaseNodes,
		Workflow: WorkflowModifySpecStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecStandaloneDelGNode,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepReleaseNodes,
	})

	// 标准版热活删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneDelGNode,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     metaserver.ProcessDelNodesForStandalone,
		SuccessNextStep: StepModifySpecStandaloneDeleteOldNodes,
		ErrorNextStep:   StepModifySpecStandaloneDelGNode,
	})

	// Step-10 删除老节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneDeleteOldNodes,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifySpecStandaloneCommitSpec,
		ErrorNextStep:   StepModifySpecStandaloneDeleteOldNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneCommitSpec,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     specification.ProcessCommitSpec,
		SuccessNextStep: StepModifySpecStandaloneUpdateOpTypeCb,
		ErrorNextStep:   StepModifySpecStandaloneCommitSpec,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneUpdateOpTypeCb,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     buildmeta.ProcessUpdateCsmasterOpTypeNormal,
		SuccessNextStep: StepModifySpecStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifySpecStandaloneUpdateOpTypeCb,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifySpecStandaloneSuccessCallbackGlobal,
		ErrorNextStep:   StepModifySpecStandaloneUpdateAppTopologyInXmaster,
	})

	// 热活同步点4 等待所有成员都ModifyStageReleaseResource 0%->100% ，然后把stage改为ModifyStageComplete
	// 从角色判断同步后就退出，主角色会代劳把以所有从角色身份请求global修改stage，
	// 目的是为了把global的member状态改为normal，解锁后续任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSuccessCallbackGlobal,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     gmaster.ProcessCheckModifySpecGlobalCB,
		SuccessNextStep: StepModifySpecStandaloneSuccessResetGlobalSg,
		ErrorNextStep:   StepModifySpecStandaloneSuccessCallbackGlobal,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSuccessResetGlobalSg,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupResetStandaloneGlobalSg,
		SuccessNextStep: StepModifySpecStandaloneInitOpMonitor,
		ErrorNextStep:   StepModifySpecStandaloneSuccessResetGlobalSg,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneInitOpMonitor,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifySpecStandaloneCloseTimeWindowTask,
		ErrorNextStep:   StepModifySpecStandaloneInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneCloseTimeWindowTask,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepModifySpecStandaloneSuccessCallback,
		ErrorNextStep:   StepModifySpecStandaloneCloseTimeWindowTask,
	})

	// Step-11 变更成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneSuccessCallback,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: StepModifySpecStandaloneAddSuccessEndEvent,
		ErrorNextStep:   StepModifySpecStandaloneSuccessCallback,
	})

	// 变配成功 事件监控处理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneAddSuccessEndEvent,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     event.ProcessAddModifyingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifySpecStandaloneAddSuccessEndEvent,
	})

	// Step-Error-01 变更失败时，调用CsMaster的API，修改cluster状态为配置变更失败
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneRollbackReleaseResources,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifySpecStandaloneTaskStepError,
		ErrorNextStep:   StepModifySpecStandaloneAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneTaskStepError,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifySpecStandaloneRollbackPushFlag,
		ErrorNextStep:   StepModifySpecStandaloneTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneRollbackPushFlag,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModfiySpecStandaloneRollbackMeta,
		ErrorNextStep:   StepModifySpecStandaloneRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModfiySpecStandaloneRollbackMeta,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifySpecStandaloneRollbackSpec,
		ErrorNextStep:   StepModifySpecStandaloneAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneRollbackSpec,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     specification.ProcessRollbackSpec,
		SuccessNextStep: StepModifySpecStandaloneUpdateOpTypeRb,
		ErrorNextStep:   StepModifySpecStandaloneAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneUpdateOpTypeRb,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     buildmeta.ProcessUpdateCsmasterOpTypeNormal,
		SuccessNextStep: StepModifySpecStandaloneRollbackCallback,
		ErrorNextStep:   StepModifySpecStandaloneAddFailedEndEvent,
	})

	// Step-Error-02 比那更失败时，如果已经创建了资源，需要删除资源
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneRollbackCallback,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     callback.ProcessModifyNodesErrorCb,
		SuccessNextStep: StepModifySpecStandaloneAddFailedEndEvent,
		ErrorNextStep:   StepModifySpecStandaloneAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecStandaloneAddFailedEndEvent,
		Workflow:        WorkflowModifySpecStandalone,
		StepProcess:     event.ProcessAddModifyingFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifySpecStandaloneAddFailedEndEvent,
	})
}
