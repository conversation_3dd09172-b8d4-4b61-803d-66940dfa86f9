/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* async_update_inner_security.go */
/*
modification history
--------------------
2022/08/18 , by <PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
)

const (
	WorkflowAsyncUpdateInnerSecurity     = "async_update_inner_security"
	StepAsyncUpdateInnerSecurityUpdateSg = "async_update_inner_security_update"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepAsyncUpdateInnerSecurityUpdateSg,
		Workflow:        WorkflowAsyncUpdateInnerSecurity,
		StepProcess:     securitygroup.ProcessUpdateInnerSg,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepAsyncUpdateInnerSecurityUpdateSg,
	}, workflow.WithStepTimeout(10*time.Minute))
}
