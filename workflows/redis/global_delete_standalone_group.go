/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/13 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_delete_standalone_group.go
 * <AUTHOR>
 * @date 2022/07/13 16:31:58
 * @brief local delete standalone group
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalDeleteStandaloneGroup delete standalone group workflow
const (
	WorkFlowLocalDeleteStandaloneGroup                               = "scs-local-delete-standalone-group"
	WorkFlowLocalDeleteStandaloneGroupStepBuildMeta                  = "scs-local-delete-standalone-group-buildmeta"
	WorkFlowLocalDeleteStandaloneGroupStepSelectMaster               = "scs-local-delete-standalone-group-select-master"
	WorkFlowLocalDeleteStandaloneGroupStepUpdateTopology             = "scs-local-delete-standalone-group-update-topology"
	WorkFlowLocalDeleteStandaloneGroupStepUpdateSg                   = "scs-local-delete-standalone-group-update-sg"
	WorkFlowLocalDeleteStandaloneGroupStepPushMonitorHTGRPSlaveFlag  = "scs-local-delete-standalone-group-push-htgrp-slave-flag"
	WorkFlowLocalDeleteStandaloneGroupStepCbNodeRole                 = "scs-local-delete-standalone-group-cb-node-role"
	WorkFlowLocalDeleteStandaloneGroupStepBindRs                     = "scs-local-delete-standalone-group-bind-rs"
	WorkFlowLocalDeleteStandaloneGroupStepBindEntranceRs             = "scs-local-delete-standalone-group-bind-entrance-rs"
	WorkFlowLocalDeleteStandaloneGroupStepUpdateAppTopologyInXmaster = "scs-local-delete-standalone-group-update-app-topo-in-xmaster"
	WorkFlowLocalDeleteStandaloneGroupStepSuccessCb                  = "scs-local-delete-standalone-group-success-cb"
)

func init() {
	// 设置状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepBuildMeta,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForDeleteQuitGlobalGroup,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepSelectMaster,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepBuildMeta,
	})

	// 选新主
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepSelectMaster,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     metaserver.ProcessSelectNewMasterNode,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepUpdateTopology,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepSelectMaster,
	})

	// 重建本地topo
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepUpdateTopology,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     metaserver.ProcessResetLocalTopology,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepUpdateSg,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepUpdateTopology,
	})

	// SecurityGroup，保证本地域redis在白名单ip中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepUpdateSg,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandaloneLocal,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepUpdateSg,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAllToNo,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepCbNodeRole,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepCbNodeRole,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     callback.ProcessCbNodeRolesQuit,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepBindRs,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepCbNodeRole,
	})

	// 更新blb的绑定rs 需要保证app.GroupId已被清理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepBindRs,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     blb.ProcessUpdateStandaloneRs,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepBindEntranceRs,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepBindRs,
	})

	// 更新entrance blb的绑定rs 需要保证app.GroupId已被清理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepBindEntranceRs,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepBindEntranceRs,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalDeleteStandaloneGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepUpdateAppTopologyInXmaster},
		workflow.WithMaxReentry(5, WorkFlowLocalDeleteStandaloneGroupStepSuccessCb))

	// 回调success
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteStandaloneGroupStepSuccessCb,
		Workflow:        WorkFlowLocalDeleteStandaloneGroup,
		StepProcess:     callback.ProcessGlobalGeneralQuitSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalDeleteStandaloneGroupStepSuccessCb,
	})
}
