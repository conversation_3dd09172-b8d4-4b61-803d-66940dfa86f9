/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/21 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_join_sync_restart_group.go
 * <AUTHOR>
 * @date 2022/11/21 16:30:29
 * @brief 待加入的集群进行重启操作
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/precheck"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalJoinSyncRestartGroup will join one sync group for restart
const (
	WorkFlowLocalJoinSyncRestartGroup                           = "scs-local-join-sync-restart-group"
	WorkFlowLocalJoinSyncRestartGroupPreCheck                   = "scs-local-join-sync-group-pre-check"
	WorkFlowLocalJoinSyncRestartGroupBlockMonitorInXmaster      = "scs-local-join-sync-group-block-monitor-in-xmaster"
	WorkFlowLocalJoinSyncRestartGroupOpenOpHeader               = "scs-local-join-sync-group-open-op-header"
	WorkFlowLocalJoinSyncRestartGroupOpenSyncConfig             = "scs-local-join-sync-group-open-sync-config"
	WorkFlowLocalJoinSyncRestartGroupModifyWhiteList            = "scs-local-join-sync-group-modify-whitelist"
	WorkFlowLocalJoinSyncRestartGroupRebootBuildMeta            = "scs-local-join-sync-group-reboot-build-meta"
	WorkFlowLocalJoinSyncRestartGroupRebootUpgradeSlaves        = "scs-local-join-sync-group-reboot-update-slaves"
	WorkFlowLocalJoinSyncRestartGroupRebootCheckSync            = "scs-local-join-sync-group-reboot-check-sync"
	WorkFlowLocalJoinSyncRestartGroupRebootHandOver             = "scs-local-join-sync-group-reboot-handover"
	WorkFlowLocalJoinSyncRestartGroupRebootUpgradeMaster        = "scs-local-join-sync-group-reboot-update-master"
	WorkFlowLocalJoinSyncRestartGroupCheckOPID                  = "scs-local-join-sync-group-check-opid"
	WorkFlowLocalJoinSyncRestartGroupUpdateAppTopologyInXmaster = "scs-local-join-sync-group-update-app-topo-in-xmaster"
	WorkFlowLocalJoinSyncRestartGroupUnblockMonitorInXmaster    = "scs-local-join-sync-group-unblock-monitor-in-xmaster"
	WorkFlowLocalJoinSyncRestartGroupStepSuccessCb              = "scs-local-join-sync-group-success-cb"
)

func init() {
	// 0 前置检查，明显无需重试的情况快速失败并保存sync-group-id
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupPreCheck,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     precheck.ProcessPrecheckForCreateSyncGroup,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupBlockMonitorInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupOpenOpHeader,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupBlockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupOpenOpHeader,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     syncgroup.ProcessOpenOpHeader,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupOpenSyncConfig,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupOpenOpHeader,
	})

	// 设置support_multi_active参数为yes
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupOpenSyncConfig,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     syncgroup.ProcessOpenSyncConfig,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupModifyWhiteList,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupOpenSyncConfig,
	})

	// 修改集群白名单为0.0.0.0/0
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupModifyWhiteList,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     syncgroup.ProcessModifyWhiteList,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupRebootBuildMeta,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupModifyWhiteList,
	})

	// 重启集群（使用升级替代）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupRebootBuildMeta,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForSyncGroupUpgrade,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupRebootUpgradeSlaves,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupRebootBuildMeta,
	})

	// Step-1 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupRebootUpgradeSlaves,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupRebootCheckSync,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupRebootUpgradeSlaves},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-2 等待数据同步后、执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupRebootCheckSync,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupRebootHandOver,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupRebootCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupRebootHandOver,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     handover.ProcessHandoverClusterForRestarting,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupRebootUpgradeMaster,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupRebootHandOver,
	})

	// Step-4 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupRebootUpgradeMaster,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupCheckOPID,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupRebootUpgradeMaster},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupCheckOPID,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     syncgroup.ProcessSyncGroupCheckOPID,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupCheckOPID,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalJoinSyncRestartGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupUnblockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncRestartGroupStepSuccessCb,
		Workflow:        WorkFlowLocalJoinSyncRestartGroup,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalJoinSyncRestartGroupStepSuccessCb,
	})
}
