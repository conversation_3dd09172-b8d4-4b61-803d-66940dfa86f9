/* Copyright 2024 Baidu Inc. All Rights Reserved. */

/*
DESCRIPTION
变更入口WorkFlow

Parameters

	{

	}
*/
package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/vpc"
)

const (
	WorkflowModifyAppEntranceStandalone                         = "scs-modify-app-entrance-standalone"
	StepModifyAppEntranceStandaloneInitTimeWindowTask           = "scs-modify-app-entrance-standalone-init-time-window-task"
	StepModifyAppEntranceStandaloneAddStartEvent                = "scs-modify-app-entrance-standalone-add-start-event"
	StepModifyAppEntranceStandaloneTaskStepCreateIP             = "scs-modify-app-entrance-standalone-task-step-create-ip"
	StepModifyAppEntranceStandaloneBuildMeta                    = "scs-modify-app-entrance-standalone-build-meta"
	StepModifyAppEntranceStandaloneInitNewBLB                   = "scs-modify-app-entrance-standalone-init-new-blb"
	StepModifyAppEntranceStandaloneCreateNewEndpoint            = "scs-modify-app-entrance-standalone-create-new-endpoint"
	StepModifyAppEntranceStandaloneTaskStepConfigIP             = "scs-modify-app-entrance-standalone-task-step-config-ip"
	StepModifyAppEntranceStandaloneNewBLBSetRs                  = "scs-modify-app-entrance-standalone-new-blb-set-rs"
	StepModifyAppEntranceStandaloneCheckNewBLBAvailable         = "scs-modify-app-entrance-standalone-check-new-blb-available"
	StepModifyAppEntranceStandaloneBindSecurityGroups           = "scs-modify-app-entrance-standalone-bind-security-groups"
	StepModifyAppEntranceStandaloneTaskStepExchangeIP           = "scs-modify-app-entrance-standalone-task-step-exchange-ip"
	StepModifyAppEntranceStandalonePublishServiceRebind         = "scs-modify-app-entrance-standalone-publish-service-rebind"
	StepModifyAppEntranceStandaloneAddNewToDomain               = "scs-modify-app-entrance-standalone-add-new-to-domain"
	StepModifyAppEntranceStandaloneCheckNewInDomain             = "scs-modify-app-entrance-standalone-check-new-in-domain"
	StepModifyAppEntranceStandaloneRemoveOldFromDomain          = "scs-modify-app-entrance-standalone-remove-old-from-domain"
	StepModifyAppEntranceStandaloneCheckOldNotInDomain          = "scs-modify-app-entrance-standalone-check-old-not-in-domain"
	StepModifyAppEntranceStandaloneKillClientOnMaster           = "scs-modify-app-entrance-standalone-kill-client-on-master"
	StepModifyAppEntranceStandaloneOldBLBUnSetRs                = "scs-modify-app-entrance-standalone-old-blb-unset-rs"
	StepModifyAppEntranceStandaloneSwitchBLBTypeInDB            = "scs-modify-app-entrance-standalone-switch-blb-type-in-db"
	StepModifyAppEntranceStandaloneRefreshAgentRecovers         = "scs-modify-app-entrance-standalone-refresh-agent-recovers"
	StepModifyAppEntranceStandaloneTaskStepSuccess              = "scs-modify-app-entrance-standalone-task-step-success"
	StepModifyAppEntranceStandaloneSuccessCb                    = "scs-modify-app-entrance-standalone-success-cb"
	StepModifyAppEntranceStandaloneAddSuccessEndEvent           = "scs-modify-app-entrance-standalone-add-success-end-event"
	StepModifyAppEntranceStandaloneRollbackOldBLBSetRs          = "scs-modify-app-entrance-standalone-rollback-old-blb-set-rs"
	StepModifyAppEntranceStandaloneRollbackPublishServiceRebind = "scs-modify-app-entrance-standalone-rollback-publish-service-rebind"
	StepModifyAppEntranceStandaloneRollbackAddOldToDomain       = "scs-modify-app-entrance-standalone-rollback-add-old-to-domain"
	StepModifyAppEntranceStandaloneRollbackRemoveNewFromDomain  = "scs-modify-app-entrance-standalone-rollback-remove-new-from-domain"
	StepModifyAppEntranceStandaloneRollbackDeleteNewEndpoint    = "scs-modify-app-entrance-standalone-rollback-delete-new-endpoint"
	StepModifyAppEntranceStandaloneRollbackDeleteNewBLB         = "scs-modify-app-entrance-standalone-rollback-delete-new-blb"
	StepModifyAppEntranceStandaloneTaskStepError                = "scs-modify-app-entrance-standalone-rollback-task-step-error"
	StepModifyAppEntranceStandaloneErrorCb                      = "scs-modify-app-entrance-standalone-error-cb"
	StepModifyAppEntranceStandaloneAddFailEndEvent              = "scs-modify-app-entrance-standalone-add-fail-end-event"
)

func init() {

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneInitTimeWindowTask,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepModifyAppEntranceStandaloneBuildMeta,
		ErrorNextStep:   StepModifyAppEntranceStandaloneInitTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneBuildMeta,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForModifyAppDefaultEntrance,
		SuccessNextStep: StepModifyAppEntranceStandaloneAddStartEvent,
		ErrorNextStep:   StepModifyAppEntranceStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneAddStartEvent,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     event.ProcessAddModifyingEntranceStartEvent,
		SuccessNextStep: StepModifyAppEntranceStandaloneTaskStepCreateIP,
		ErrorNextStep:   StepModifyAppEntranceStandaloneAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceStandaloneTaskStepCreateIP,
		Workflow: WorkflowModifyAppEntranceStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, timewindow.StepCreateIP, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAppEntranceStandaloneInitNewBLB,
		ErrorNextStep:   StepModifyAppEntranceStandaloneTaskStepCreateIP,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneInitNewBLB,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     blb.ProcessInitToExchangeBLB,
		SuccessNextStep: StepModifyAppEntranceStandaloneCreateNewEndpoint,
		ErrorNextStep:   StepModifyAppEntranceStandaloneInitNewBLB,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneCreateNewEndpoint,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     endpoint.ProcessCreateToExchangeRwEndpoint,
		SuccessNextStep: StepModifyAppEntranceStandaloneTaskStepConfigIP,
		ErrorNextStep:   StepModifyAppEntranceStandaloneCreateNewEndpoint,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceStandaloneTaskStepConfigIP,
		Workflow: WorkflowModifyAppEntranceStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, timewindow.StepConfigIP, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAppEntranceStandaloneNewBLBSetRs,
		ErrorNextStep:   StepModifyAppEntranceStandaloneTaskStepConfigIP,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneNewBLBSetRs,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     blb.ProcessBindRSToExchangeApp,
		SuccessNextStep: StepModifyAppEntranceStandaloneCheckNewBLBAvailable,
		ErrorNextStep:   StepModifyAppEntranceStandaloneNewBLBSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneCheckNewBLBAvailable,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     blb.ProcessCheckToExchangeBlbAvailable,
		SuccessNextStep: StepModifyAppEntranceStandaloneBindSecurityGroups,
		ErrorNextStep:   StepModifyAppEntranceStandaloneCheckNewBLBAvailable,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneBindSecurityGroups,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     vpc.ProcessCopySgsToNewEntrance,
		SuccessNextStep: StepModifyAppEntranceStandaloneTaskStepExchangeIP,
		ErrorNextStep:   StepModifyAppEntranceStandaloneBindSecurityGroups,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceStandaloneTaskStepExchangeIP,
		Workflow: WorkflowModifyAppEntranceStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, timewindow.StepExchangeIP, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAppEntranceStandalonePublishServiceRebind,
		ErrorNextStep:   StepModifyAppEntranceStandaloneTaskStepExchangeIP,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandalonePublishServiceRebind,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     endpoint.ProcessRebindToExchangeBlb,
		SuccessNextStep: StepModifyAppEntranceStandaloneAddNewToDomain,
		ErrorNextStep:   StepModifyAppEntranceStandalonePublishServiceRebind,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneAddNewToDomain,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     dns.ProcessAddToExchangeBlbToDomain,
		SuccessNextStep: StepModifyAppEntranceStandaloneCheckNewInDomain,
		ErrorNextStep:   StepModifyAppEntranceStandaloneAddNewToDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneCheckNewInDomain,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     dns.ProcessCheckToExchangeBlbInDomain,
		SuccessNextStep: StepModifyAppEntranceStandaloneRemoveOldFromDomain,
		ErrorNextStep:   StepModifyAppEntranceStandaloneCheckNewInDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRemoveOldFromDomain,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     dns.ProcessDeleteOldBlbFromAppDomain,
		SuccessNextStep: StepModifyAppEntranceStandaloneCheckOldNotInDomain,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRemoveOldFromDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneCheckOldNotInDomain,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     dns.ProcessCheckOnlyToExchangeBlbInDomain,
		SuccessNextStep: StepModifyAppEntranceStandaloneKillClientOnMaster,
		ErrorNextStep:   StepModifyAppEntranceStandaloneCheckOldNotInDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneKillClientOnMaster,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     handover.ProcessMasterNodeKillOldClient,
		SuccessNextStep: StepModifyAppEntranceStandaloneOldBLBUnSetRs,
		ErrorNextStep:   StepModifyAppEntranceStandaloneKillClientOnMaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneOldBLBUnSetRs,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     blb.ProcessUnBindRSFromOld,
		SuccessNextStep: StepModifyAppEntranceStandaloneSwitchBLBTypeInDB,
		ErrorNextStep:   StepModifyAppEntranceStandaloneOldBLBUnSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneSwitchBLBTypeInDB,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     callback.ProcessSwitchBlbTypeInDB,
		SuccessNextStep: StepModifyAppEntranceStandaloneRefreshAgentRecovers,
		ErrorNextStep:   StepModifyAppEntranceStandaloneSwitchBLBTypeInDB,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRefreshAgentRecovers,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     config.ProcessRefreshAgentRecovers,
		SuccessNextStep: StepModifyAppEntranceStandaloneTaskStepSuccess,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRefreshAgentRecovers,
	}, workflow.WithMaxReentry(3, StepModifyAppEntranceStandaloneTaskStepSuccess))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceStandaloneTaskStepSuccess,
		Workflow: WorkflowModifyAppEntranceStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepModifyAppEntranceStandaloneSuccessCb,
		ErrorNextStep:   StepModifyAppEntranceStandaloneTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneSuccessCb,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     callback.ProcessModifyEntranceSuccessCb,
		SuccessNextStep: StepModifyAppEntranceStandaloneAddSuccessEndEvent,
		ErrorNextStep:   StepModifyAppEntranceStandaloneSuccessCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneAddSuccessEndEvent,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     event.ProcessAddModifyingEntranceSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyAppEntranceStandaloneAddSuccessEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRollbackOldBLBSetRs,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     blb.ProcessRollbackUnBindRSFromOld,
		SuccessNextStep: StepModifyAppEntranceStandaloneRollbackPublishServiceRebind,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRollbackOldBLBSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRollbackPublishServiceRebind,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     endpoint.ProcessRebindToOriginBlb,
		SuccessNextStep: StepModifyAppEntranceStandaloneRollbackAddOldToDomain,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRollbackPublishServiceRebind,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRollbackAddOldToDomain,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     dns.ProcessCreateAppDomain,
		SuccessNextStep: StepModifyAppEntranceStandaloneRollbackRemoveNewFromDomain,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRollbackAddOldToDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRollbackRemoveNewFromDomain,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     dns.ProcessDeleteToExchangeBlbFromAppDomain,
		SuccessNextStep: StepModifyAppEntranceStandaloneRollbackDeleteNewBLB,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRollbackRemoveNewFromDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRollbackDeleteNewEndpoint,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     endpoint.ProcessDeleteToExchangeRwEndpoint,
		SuccessNextStep: StepModifyAppEntranceStandaloneRollbackDeleteNewBLB,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRollbackDeleteNewEndpoint,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneRollbackDeleteNewBLB,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     blb.ProcessDelToExchangeBLB,
		SuccessNextStep: StepModifyAppEntranceStandaloneTaskStepError,
		ErrorNextStep:   StepModifyAppEntranceStandaloneRollbackDeleteNewBLB,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceStandaloneTaskStepError,
		Workflow: WorkflowModifyAppEntranceStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifyAppEntranceStandaloneErrorCb,
		ErrorNextStep:   StepModifyAppEntranceStandaloneTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneErrorCb,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     callback.ProcessModifyEntranceErrorCb,
		SuccessNextStep: StepModifyAppEntranceStandaloneAddFailEndEvent,
		ErrorNextStep:   StepModifyAppEntranceStandaloneErrorCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceStandaloneAddFailEndEvent,
		Workflow:        WorkflowModifyAppEntranceStandalone,
		StepProcess:     event.ProcessAddModifyingEntranceFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyAppEntranceStandaloneAddFailEndEvent,
	})
}
