/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建SCS标准版实例WORKFLOW

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"Name": "redis-kx42u7p4",
	"UserID": "680cdfe705434d53b13a156284ed973b",
	"Port": 6379,
	"VpcID": "5c03c3e6-c38e-44e8-a50c-0648cff93faf"
	"Replicas": [{
		"Zone": "zoneA",
		"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
		"Role": "master",
		"Count": 1
	}, {
		"Zone": "zoneB",
		"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
		"Role": "slave",
		"Count": 1
	}],
	"DefaultPassword": "xhdogY7*er",
	"Engine": "redis",
	"EngineVersion": "6.2"
	"NodeType": "cache.n1.small",
	"ShardCount": 1,
	"DeployIDList": ["467eee77"],
	"Pool": "bbc-cu876y",
	"ClusterType": "master-slave"
	// Todo 增加克隆集群相关参数
}
*/

package workflows

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/availability"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/namespace_task"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowCreateCluster                                          = "scs-create-cluster-app"
	StepCreateClusterBuildMeta                                     = "scs-create-cluster-app-build-meta"
	StepCreateClusterTaskStepCreateNodes                           = "scs-create-cluster-task-step-create-nodes"
	StepCreateClusterFillSpec                                      = "scs-create-cluster-fill-spec"
	StepCreateClusterCheckSubnetsIpv6                              = "scs-create-cluster-check-subnets-ipv6"
	StepCreateClusterCheckSubnetsEnoughIps                         = "scs-create-cluster-check-subnets-enough-ips"
	StepCreateClusterStartInitBlbAndEndpoint                       = "scs-create-cluster-start-init-blb-and-endpoint"
	StepCreateClusterCreateSecurityGroups                          = "scs-create-cluster-create-security-groups"
	StepCreateClusterApplyResources                                = "scs-create-cluster-apply-resources"
	StepCreateClusterApplyResourcesCallback                        = "scs-create-cluster-apply-resources-cb"
	StepCreateClusterTaskStepDeployNodes                           = "scs-create-cluster-task-step-deploy-nodes"
	StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource = "scs-create-cluster-apply-resources-start-sub-tasks-after-apply-resource"
	StepCreateClusterInitMachineEnv                                = "scs-create-cluster-init-machine-env"
	StepCreateClusterInitMetaserver                                = "scs-create-cluster-init-metaserver"
	StepCreateClusterDeployRedis                                   = "scs-create-cluster-apply-deploy-redis"
	StepCreateClusterSetLocalSlaveOf                               = "scs-create-cluster-set-local-slave-of"
	StepCreateClusterTaskStepConfigNodes                           = "scs-create-cluster-task-step-config-nodes"
	StepCreateClusterSetGlobalSlaveOf                              = "scs-create-cluster-set-remote-slave-of"
	StepCreateClusterDeployFileBeat                                = "scs-create-cluster-apply-deploy-filebeat"
	StepCreateClusterCreateSetDefaultAcl                           = "scs-create-cluster-create-set-default-acl"
	StepCreateClusterInitAppInXmaster                              = "scs-create-cluster-init-app-in-xmaster"
	StepCreateClusterChooseHAServer                                = "scs-create-cluster-choose-ha-server"
	StepCreateClusterInitConfigForCreate                           = "scs-create-cluster-init-config-for-create"
	StepCreateClusterUpdateConfigForNewAgent                       = "scs-create-cluster-update-config-for-new-agent"
	StepCreateClusterUpdatePushFlagAllTrue                         = "scs-create-cluster-update-push-flag-all-true"
	StepCreateClusterCreateDefaultNamespace                        = "scs-create-cluster-create-default-namespace"
	StepCreateClusterTaskStepSuccess                               = "scs-create-cluster-task-step-success"
	StepCreateClusterCreateCallback                                = "scs-create-cluster-create-cb"
	StepCreateClusterRollbackCallback                              = "scs-create-cluster-rollback-callback"
	StepCreateClusterTaskStepError                                 = "scs-create-cluster-task-step-error"
	StepCreateClusterRollbackDeleteAppFromXmaster                  = "scs-create-cluster-rollback-delete-app-from-xmaster"
	StepCreateClusterRollbackDeleteEndpoint                        = "scs-create-cluster-rollback-delete-endpoint"
	StepCreateClusterRollbackDeleteBLB                             = "scs-create-cluster-rollback-delete-blb"
	StepCreateClusterRollbackDeleteOpMonitor                       = "scs-create-cluster-rollback-delete-op-monitor"
	StepCreateClusterRollbackRemoveFromBcmGroup                    = "scs-create-cluster-rollback-remove-from-bcm-group"
	StepCreateClusterRollbackDeleteBcmResource                     = "scs-create-cluster-rollback-delete-bcm-resource"
	StepCreateClusterRollbackReleaseResources                      = "scs-create-cluster-rollback-release-resource"
	WorkflowCreateClusterInitBlbAndEndpoint                        = "scs-create-cluster-init-blb-and-endpoint"
	StepCreateClusterInitBlbAndEndpointInitBLB                     = "scs-create-cluster-init-blb-and-endpoint-init-blb"
	StepCreateClusterInitBlbAndEndpointCreateEndpoint              = "scs-create-cluster-init-blb-and-endpoint-create-endpoint"
	StepCreateClusterInitBlbAndEndpointCreateAppDomain             = "scs-create-cluster-init-blb-and-endpoint-create-app-domain"

	WorkflowCreateClusterUpdateConfig         = "scs-create-cluster-update-config"
	StepCreateClusterUpdateConfigApplyConfTpl = "scs-create-cluster-update-config-apply-conf-tpl"
	StepCreateClusterUpdateConfigUpdateSg     = "scs-create-cluster-update-config-update-sg"

	WorkflowCreateClusterInitBns                   = "scs-create-cluster-init-bns"
	StepCreateClusterInitBnsInitBnsService         = "scs-create-cluster-init-bns-init-bns-service"
	StepCreateClusterInitBnsInitBnsServiceCallback = "scs-create-cluster-init-bns-init-bns-service-cb"
	StepCreateClusterInitBnsSetBnsServiceInstance  = "scs-create-cluster-init-bns-set-bns-service-instance"

	WorkflowCreateClusterInitBlbRs        = "scs-create-cluster-init-blb-rs"
	StepCreateClusterInitBlbRsSetRs       = "scs-create-cluster-init-blb-rs-set-rs"
	StepCreateClusterInitBlbRsSetMcpackRs = "scs-create-cluster-init-blb-rs-set-mcpack-rs"

	WorkflowCreateClusterInitOpMonitor              = "scs-create-cluster-init-op-monitor"
	StepCreateClusterInitOpMonitorCreateBnsService  = "scs-create-cluster-init-op-monitor-create-bns-service"
	StepCreateClusterInitOpMonitorCreateBnsInstance = "scs-create-cluster-init-op-monitor-create-bns-instance"

	WorkflowCreateClusterInitBcmResource           = "scs-create-cluster-init-bcm-resource"
	StepCreateClusterInitBcmResourceCreateResource = "scs-create-cluster-init-bcm-resource-create-resource"
	StepCreateClusterInitBcmResourceAddToBcmGroup  = "scs-create-cluster-init-bcm-resource-add-to-bcm-group"
)

func init() {
	// Step-01 更新x1数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterBuildMeta,
		Workflow: WorkflowCreateCluster,

		StepProcess: buildmeta.ProcessBuildMetaForCreatingCluster,

		SuccessNextStep: StepCreateClusterTaskStepCreateNodes,
		ErrorNextStep:   StepCreateClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterTaskStepCreateNodes,
		Workflow: WorkflowCreateCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.CreateTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepCreateClusterFillSpec,
		ErrorNextStep:   StepCreateClusterTaskStepCreateNodes,
	})

	// Step-02 计算规格并填入
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterFillSpec,
		Workflow: WorkflowCreateCluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepCreateClusterCheckSubnetsIpv6,
		ErrorNextStep:   StepCreateClusterFillSpec,
	})

	// Step-03 检查子网是否为ipv6
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCheckSubnetsIpv6,
		Workflow: WorkflowCreateCluster,

		StepProcess: checksubnets.CheckIPV6,

		SuccessNextStep: StepCreateClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepCreateClusterCheckSubnetsIpv6,
	})

	// Step-04 检查子网中ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCheckSubnetsEnoughIps,
		Workflow: WorkflowCreateCluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepCreateClusterStartInitBlbAndEndpoint,
		ErrorNextStep:   StepCreateClusterCheckSubnetsEnoughIps,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterStartInitBlbAndEndpoint,
		Workflow: WorkflowCreateCluster,

		StepProcess: ProcessCreateClusterStartWorkflowInitBlbAndEndpoint,

		SuccessNextStep: StepCreateClusterCreateSecurityGroups,
		ErrorNextStep:   StepCreateClusterStartInitBlbAndEndpoint,
	})

	// Step-05 创建安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCreateSecurityGroups,
		Workflow: WorkflowCreateCluster,

		StepProcess: securitygroup.ProcessInitSecurityGroupCluster,

		SuccessNextStep: StepCreateClusterApplyResources,
		ErrorNextStep:   StepCreateClusterCreateSecurityGroups,
	})

	// Step-06 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterApplyResources,
		Workflow: WorkflowCreateCluster,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepCreateClusterApplyResourcesCallback,
		ErrorNextStep:   StepCreateClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
	)

	// Step-07 申请资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterApplyResourcesCallback,
		Workflow: WorkflowCreateCluster,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepCreateClusterTaskStepDeployNodes,
		ErrorNextStep:   StepCreateClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterTaskStepDeployNodes,
		Workflow: WorkflowCreateCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.CreateTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource,
		ErrorNextStep:   StepCreateClusterTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource,
		Workflow: WorkflowCreateCluster,

		StepProcess: ProcessCreateClusterStartWorkflowsAfterApplyResources,

		SuccessNextStep: StepCreateClusterInitMachineEnv,
		ErrorNextStep:   StepCreateClusterApplyResourcesStartSubTasksAfterApplyResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitMachineEnv,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepCreateClusterInitMetaserver,
		ErrorNextStep:   StepCreateClusterInitMachineEnv,
	})

	// Step-11 初始化Metaserver
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterInitMetaserver,
		Workflow: WorkflowCreateCluster,

		StepProcess: metaserver.ProcessInitClusterLocal,

		SuccessNextStep: StepCreateClusterDeployRedis,
		ErrorNextStep:   StepCreateClusterInitMetaserver,
	})

	// Step-09 部署所有节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterDeployRedis,
		Workflow: WorkflowCreateCluster,

		StepProcess: deploy.ProcessDeployAllForNewCreate,

		SuccessNextStep: StepCreateClusterTaskStepConfigNodes,
		ErrorNextStep:   StepCreateClusterDeployRedis},

		workflow.WithStepTimeout(15*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterTaskStepConfigNodes,
		Workflow: WorkflowCreateCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.CreateTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepCreateClusterSetLocalSlaveOf,
		ErrorNextStep:   StepCreateClusterTaskStepConfigNodes},

		workflow.WithStepTimeout(15*time.Minute),
	)

	// Step-10 设置Local Redis的slaveof
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterSetLocalSlaveOf,
		Workflow: WorkflowCreateCluster,

		StepProcess: handover.ProcessLocalSlaveOf,

		SuccessNextStep: StepCreateClusterSetGlobalSlaveOf,
		ErrorNextStep:   StepCreateClusterSetLocalSlaveOf},
	)

	// Step-11 设置Global Redis的slaveof
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterSetGlobalSlaveOf,
		Workflow: WorkflowCreateCluster,

		StepProcess: gmaster.ProcessGlobalSlaveOf,

		SuccessNextStep: StepCreateClusterDeployFileBeat,
		ErrorNextStep:   StepCreateClusterSetGlobalSlaveOf},
	)

	// Step-11 部署filebeat
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterDeployFileBeat,
		Workflow: WorkflowCreateCluster,

		StepProcess: deploy.ProcessDeployFilebeat,

		SuccessNextStep: StepCreateClusterCreateSetDefaultAcl,
		ErrorNextStep:   StepCreateClusterDeployFileBeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds),
	)

	// Step-15 初始化acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCreateSetDefaultAcl,
		Workflow: WorkflowCreateCluster,

		StepProcess: acl.ProcessInitAclForCluster,

		SuccessNextStep: StepCreateClusterInitAppInXmaster,
		ErrorNextStep:   StepCreateClusterCreateSetDefaultAcl,
	})

	// Step-18 集群注册到xmaster：拓扑信息、监控策略、自愈开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterInitAppInXmaster,
		Workflow: WorkflowCreateCluster,

		StepProcess: xmaster.ProcessInitAppInXmaster,

		SuccessNextStep: StepCreateClusterChooseHAServer,
		ErrorNextStep:   StepCreateClusterInitAppInXmaster,
	})

	// 选择高可用组件：根据cache_cluster.use_xmaster设置对应高可用组件的开关
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterChooseHAServer,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     availability.InitHAServer,
		SuccessNextStep: StepCreateClusterCreateDefaultNamespace,
		ErrorNextStep:   StepCreateClusterChooseHAServer,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterCreateDefaultNamespace,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     namespace_task.CreateDefaultNamespace,
		SuccessNextStep: StepCreateClusterInitConfigForCreate,
		ErrorNextStep:   StepCreateClusterCreateDefaultNamespace,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitConfigForCreate,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     config.ProcessInitConfigForCreate,
		SuccessNextStep: StepCreateClusterUpdateConfigForNewAgent,
		ErrorNextStep:   StepCreateClusterInitConfigForCreate,
	}, workflow.WithMaxReentry(3, StepCreateClusterUpdateConfigForNewAgent))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterUpdateConfigForNewAgent,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     config.ProcessUpdateConfigNewForCreate,
		SuccessNextStep: StepCreateClusterUpdatePushFlagAllTrue,
		ErrorNextStep:   StepCreateClusterUpdateConfigForNewAgent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterUpdatePushFlagAllTrue,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagAllTrue,
		SuccessNextStep: StepCreateClusterTaskStepSuccess,
		ErrorNextStep:   StepCreateClusterUpdatePushFlagAllTrue,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterTaskStepSuccess,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepCreateClusterCreateCallback,
		ErrorNextStep:   StepCreateClusterTaskStepSuccess,
	})

	// Step-19 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterCreateCallback,
		Workflow: WorkflowCreateCluster,

		StepProcess: callback.ProcessCreateSuccessCb,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterCreateCallback,
	})

	// Step-Error-01 创建失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackCallback,
		Workflow: WorkflowCreateCluster,

		StepProcess: callback.ProcessCreateErrorCb,

		SuccessNextStep: StepCreateClusterTaskStepError,
		ErrorNextStep:   StepCreateClusterRollbackCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterTaskStepError,
		Workflow: WorkflowCreateCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),

		SuccessNextStep: StepCreateClusterRollbackDeleteAppFromXmaster,
		ErrorNextStep:   StepCreateClusterTaskStepError,
	})

	// Step-Error-02 创建失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteAppFromXmaster,
		Workflow: WorkflowCreateCluster,

		StepProcess: xmaster.ProcessDeleteAppFromXmaster,

		SuccessNextStep: StepCreateClusterRollbackDeleteEndpoint,
		ErrorNextStep:   StepCreateClusterRollbackDeleteAppFromXmaster,
	})

	// Step-Error 回滚 Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteEndpoint,
		Workflow: WorkflowCreateCluster,

		StepProcess: endpoint.ProcessDeleteRwEndpoint,

		SuccessNextStep: StepCreateClusterRollbackDeleteBLB,
		ErrorNextStep:   StepCreateClusterRollbackDeleteEndpoint,
	})

	// Step-Error-03 回滚资源申请
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteBLB,
		Workflow: WorkflowCreateCluster,

		StepProcess: blb.ProcessDelBLB,

		SuccessNextStep: StepCreateClusterRollbackDeleteOpMonitor,
		ErrorNextStep:   StepCreateClusterRollbackDeleteBLB,
	})

	// Step-Error-04 回滚创建的bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteOpMonitor,
		Workflow: WorkflowCreateCluster,

		StepProcess: opmonitor.ProcessDeleteOpmonitorBnsService,

		SuccessNextStep: StepCreateClusterRollbackRemoveFromBcmGroup,
		ErrorNextStep:   StepCreateClusterRollbackDeleteOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterRollbackRemoveFromBcmGroup,
		Workflow:        WorkflowCreateCluster,
		StepProcess:     bcm.ProcessRollbackClusterAddToGroup,
		SuccessNextStep: StepCreateClusterRollbackDeleteBcmResource,
		ErrorNextStep:   StepCreateClusterRollbackRemoveFromBcmGroup,
	})

	// Step-Error-05 回滚BCM Resource
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackDeleteBcmResource,
		Workflow: WorkflowCreateCluster,

		StepProcess: bcm.ProcessRollbackBcmResourceWithinApp,

		SuccessNextStep: StepCreateClusterRollbackReleaseResources,
		ErrorNextStep:   StepCreateClusterRollbackDeleteBcmResource,
	})

	// Step-Error-06 回滚BLB资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateClusterRollbackReleaseResources,
		Workflow: WorkflowCreateCluster,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepCreateClusterRollbackReleaseResources,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbAndEndpointInitBLB,
		Workflow:        WorkflowCreateClusterInitBlbAndEndpoint,
		StepProcess:     blb.ProcessInitAppBLB,
		SuccessNextStep: StepCreateClusterInitBlbAndEndpointCreateEndpoint,
		ErrorNextStep:   StepCreateClusterInitBlbAndEndpointInitBLB,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbAndEndpointCreateEndpoint,
		Workflow:        WorkflowCreateClusterInitBlbAndEndpoint,
		StepProcess:     endpoint.ProcessCreateRwEndpoint,
		SuccessNextStep: StepCreateClusterInitBlbAndEndpointCreateAppDomain,
		ErrorNextStep:   StepCreateClusterInitBlbAndEndpointCreateEndpoint,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbAndEndpointCreateAppDomain,
		Workflow:        WorkflowCreateClusterInitBlbAndEndpoint,
		StepProcess:     dns.ProcessCreateAppDomain,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitBlbAndEndpointCreateAppDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterUpdateConfigApplyConfTpl,
		Workflow:        WorkflowCreateClusterUpdateConfig,
		StepProcess:     config.ProcessApplyConfTplForCreate,
		SuccessNextStep: StepCreateClusterUpdateConfigUpdateSg,
		ErrorNextStep:   StepCreateClusterUpdateConfigApplyConfTpl,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterUpdateConfigUpdateSg,
		Workflow:        WorkflowCreateClusterUpdateConfig,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterUpdateConfigUpdateSg,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBnsInitBnsService,
		Workflow:        WorkflowCreateClusterInitBns,
		StepProcess:     bns.ProcessCreateBnsService,
		SuccessNextStep: StepCreateClusterInitBnsInitBnsServiceCallback,
		ErrorNextStep:   StepCreateClusterInitBnsInitBnsService,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBnsInitBnsServiceCallback,
		Workflow:        WorkflowCreateClusterInitBns,
		StepProcess:     bns.ProcessCreateBnsServiceCallback,
		SuccessNextStep: StepCreateClusterInitBnsSetBnsServiceInstance,
		ErrorNextStep:   StepCreateClusterInitBnsInitBnsServiceCallback,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBnsSetBnsServiceInstance,
		Workflow:        WorkflowCreateClusterInitBns,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitBnsSetBnsServiceInstance,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbRsSetRs,
		Workflow:        WorkflowCreateClusterInitBlbRs,
		StepProcess:     blb.ProcessSetProxyRs,
		SuccessNextStep: StepCreateClusterInitBlbRsSetMcpackRs,
		ErrorNextStep:   StepCreateClusterInitBlbRsSetRs,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBlbRsSetMcpackRs,
		Workflow:        WorkflowCreateClusterInitBlbRs,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitBlbRsSetMcpackRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitOpMonitorCreateBnsService,
		Workflow:        WorkflowCreateClusterInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorBnsService,
		SuccessNextStep: StepCreateClusterInitOpMonitorCreateBnsInstance,
		ErrorNextStep:   StepCreateClusterInitOpMonitorCreateBnsService,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitOpMonitorCreateBnsInstance,
		Workflow:        WorkflowCreateClusterInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitOpMonitorCreateBnsInstance,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBcmResourceCreateResource,
		Workflow:        WorkflowCreateClusterInitBcmResource,
		StepProcess:     bcm.ProcessCreateBcmResourceWithinApp,
		SuccessNextStep: StepCreateClusterInitBcmResourceAddToBcmGroup,
		ErrorNextStep:   StepCreateClusterInitBcmResourceCreateResource,
	})

	// 添加到bcm group
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateClusterInitBcmResourceAddToBcmGroup,
		Workflow:        WorkflowCreateClusterInitBcmResource,
		StepProcess:     bcm.ProcessClusterAddToGroup,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateClusterInitBcmResourceAddToBcmGroup,
	})
}

func ProcessCreateClusterStartWorkflowInitBlbAndEndpoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateClusterInitBlbAndEndpoint,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_and_endpoint",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}})
}

func ProcessCreateClusterStartWorkflowsAfterApplyResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateClusterUpdateConfig,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_update_config",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}, {
		WorkFlow:   WorkflowCreateClusterInitBlbRs,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_rs",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}}
	if !privatecloud.IsPrivateENV() {
		params = append(params, &iface.CreateTaskParams{
			WorkFlow:   WorkflowCreateClusterInitBns,
			Schedule:   time.Now(),
			Mutex:      teu.Entity + "_init_bns",
			Entity:     teu.Entity,
			Parameters: teu.Parameters,
		})
		params = append(params, &iface.CreateTaskParams{
			WorkFlow:   WorkflowCreateClusterInitOpMonitor,
			Schedule:   time.Now(),
			Mutex:      teu.Entity + "_init_op_monitor",
			Entity:     teu.Entity,
			Parameters: teu.Parameters,
		})
		params = append(params, &iface.CreateTaskParams{
			WorkFlow:   WorkflowCreateClusterInitBcmResource,
			Schedule:   time.Now(),
			Mutex:      teu.Entity + "_init_bcm_resource",
			Entity:     teu.Entity,
			Parameters: teu.Parameters,
		})
	}
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, params)
}
