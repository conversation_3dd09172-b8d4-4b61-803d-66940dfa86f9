/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* modify_interfaces_cluster_app.go - workflow for modify interface count */

/*
Modification History
--------------------
2023/6/16, by wang<PERSON><PERSON>, create
*/

/*
DESCRIPTION

Parameters
{
	"app_id": "scs-bj-nxewpztnsreg",
	"target_interface_count": 2
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/proxy"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyInterfacesCluster                                  = "scs-modify-interfaces-cluster"
	StepModifyInterfacesClusterBuildMeta                             = "scs-modify-interfaces-cluster-app-build-meta"
	StepModifyInterfacesClusterFillSpec                              = "scs-modify-interfaces-cluster-fill-spec"
	StepModifyInterfacesClusterCheckSubnetsEnoughIps                 = "scs-modify-interfaces-cluster-check-subnets-enough-ips"
	StepModifyInterfacesClusterApplyResources                        = "scs-modify-interfaces-cluster-apply-resources"
	StepModifyInterfacesClusterApplyResourcesCallback                = "scs-modify-interfaces-cluster-apply-resources-cb"
	StepModifyInterfacesClusterInitMachineEnv                        = "scs-modify-interfaces-cluster-init-machine-env"
	StepModifyInterfacesClusterAddGlobalNode                         = "scs-modify-interfaces-cluster-add-global-nodes"
	StepModifyInterfacesClusterUpdateSecurityGroups                  = "scs-modify-interfaces-cluster-update-security-groups"
	StepModifyInterfacesClusterMetaAddNodes                          = "scs-modify-interfaces-cluster-meta-add-nodes"
	StepModifyInterfacesClusterDeployProxy                           = "scs-modify-interfaces-cluster-deploy-proxy"
	StepModifyInterfacesClusterUpdateTlsConfIfNeeded                 = "scs-modify-interfaces-cluster-update-tls-conf-if-needed"
	StepModifyInterfacesClusterSetProxyZeroWeightWhenDelete          = "scs-modify-interfaces-cluster-set-proxy-zero-weight-when-delete"
	StepModifyInterfacesClusterSetProxyZeroWeightWhenDeleteForMcpack = "scs-modify-interfaces-cluster-set-proxy-zero-weight-when-delete-for-mcpack"
	StepModifyInterfacesClusterCheckProxyQPS                         = "scs-modify-interfaces-cluster-check-proxy-qps"
	StepModifyInterfacesClusterMetaDelNodes                          = "scs-modify-interfaces-cluster-meta-del-nodes"
	StepModifyInterfacesClusterSetRs                                 = "scs-modify-interfaces-cluster-set-rs"
	StepModifyInterfacesClusterSetMcpackRs                           = "scs-modify-interfaces-cluster-set-mcpack-rs"
	StepModifyInterfacesClusterSetBnsInstances                       = "scs-modify-interfaces-cluster-set-bns-instances"
	StepModifyInterfacesClusterPushFlag                              = "scs-modify-interfaces-cluster-push-flag"
	StepModifyInterfacesClusterInitBcmResource                       = "scs-modify-interfaces-cluster-init-bcm-resource"
	StepModifyInterfacesClusterAddOrRemoveFromBcmGroup               = "scs-modify-interfaces-cluster-add-or-remove-from-bcm-group"
	StepModifyInterfacesClusterDeleteToDeleteProxies                 = "scs-modify-interfaces-cluster-delete-to-delete-proxies"
	StepModifyInterfacesClusterUpdateAppTopologyInXmaster            = "scs-modify-interfaces-cluster-update-app-topo-in-xmaster"
	StepModifyInterfacesClusterInitOpMonitor                         = "scs-modify-interfaces-cluster-init-op-monitor"
	StepModifyInterfacesClusterSuccessCallback                       = "scs-modify-interfaces-cluster-success-cb"
	StepModifyInterfacesClusterRollbackReleaseResources              = "scs-modify-interfaces-cluster-rollback-release-resource"
	StepModifyInterfacesClusterRollbackBcmGroup                      = "scs-modify-interfaces-cluster-rollback-bcm-group"
	StepModifyInterfacesClusterRollbackBcmResource                   = "scs-modify-interfaces-cluster-rollback-bcm-resource"
	StepModifyInterfacesClusterRollbackPushFlag                      = "scs-modify-interfaces-cluster-rollback-push-flag"
	StepModifyInterfacesClusterRollbackMeta                          = "scs-modify-interfaces-cluster-rollback-meta"
	StepModifyInterfacesClusterRollbackCallback                      = "scs-modify-interfaces-cluster-rollback-cb"
)

func init() {
	// Step-01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterBuildMeta,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: buildmeta.ProcessBuildMetaModifyInterface,

		SuccessNextStep: StepModifyInterfacesClusterFillSpec,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackReleaseResources,
	})

	// Step-02 添加规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterFillSpec,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepModifyInterfacesClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackReleaseResources,
	})

	// Step-03 检查子网数量是否足够
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterCheckSubnetsEnoughIps,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepModifyInterfacesClusterApplyResources,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackReleaseResources,
	})

	// Step-04 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterApplyResources,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepModifyInterfacesClusterApplyResourcesCallback,
		ErrorNextStep:   StepModifyInterfacesClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, StepModifyInterfacesClusterRollbackReleaseResources))

	// Step-05 资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterApplyResourcesCallback,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepModifyInterfacesClusterInitMachineEnv,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackReleaseResources,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyInterfacesClusterInitMachineEnv,
		Workflow:        WorkflowModifyInterfacesCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifyInterfacesClusterAddGlobalNode,
		ErrorNextStep:   StepModifyInterfacesClusterInitMachineEnv,
	})

	// Step-06 注册 metaserver(global)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyInterfacesClusterAddGlobalNode,
		Workflow:        WorkflowModifyInterfacesCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepModifyInterfacesClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyInterfacesClusterAddGlobalNode,
	})

	// Step-07 更新安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterUpdateSecurityGroups,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: securitygroup.ProcessRebuildSecurityGroupCluster,

		SuccessNextStep: StepModifyInterfacesClusterMetaAddNodes,
		ErrorNextStep:   StepModifyInterfacesClusterUpdateSecurityGroups,
	})

	// Step-08 Metaserver增加节点, 如果是热活实例组成员，则在global master中注册节点
	// 注册节点除了在Global Master中更新节点信息，还需要在Global MetaServer中更新节点信息，以及更新节点的安全组信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterMetaAddNodes,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: metaserver.ProcessAddNodes,

		SuccessNextStep: StepModifyInterfacesClusterDeployProxy,
		ErrorNextStep:   StepModifyInterfacesClusterMetaAddNodes,
	})

	// Step-09 部署Proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterDeployProxy,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: deploy.ProcessDeployAll,

		SuccessNextStep: StepModifyInterfacesClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepModifyInterfacesClusterDeployProxy},

		workflow.WithStepTimeout(15*time.Minute),
	)

	// Step-10 TLS
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterUpdateTlsConfIfNeeded,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: util.UpdateTLSConfIfNeededForNew,

		SuccessNextStep: StepModifyInterfacesClusterSetProxyZeroWeightWhenDelete,
		ErrorNextStep:   StepModifyInterfacesClusterUpdateTlsConfIfNeeded,
	})
	// Step-11 调整 BLB Rs 权重(将要下线的 Rs 权重调整为 0)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterSetProxyZeroWeightWhenDelete,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: blb.ProcessSetProxyRsZeroWeightWhenDelete,

		SuccessNextStep: StepModifyInterfacesClusterSetProxyZeroWeightWhenDeleteForMcpack,
		ErrorNextStep:   StepModifyInterfacesClusterSetProxyZeroWeightWhenDelete,
	})

	// 调整 mcpack BLB Rs 权重(将要下线的 Rs 权重调整为 0)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterSetProxyZeroWeightWhenDeleteForMcpack,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: blb.ProcessSetProxyRsZeroWeightWhenDeleteForMcpack,

		SuccessNextStep: StepModifyInterfacesClusterCheckProxyQPS,
		ErrorNextStep:   StepModifyInterfacesClusterSetProxyZeroWeightWhenDeleteForMcpack,
	})

	// Step-12 检查 Proxy 流量
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterCheckProxyQPS,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: proxy.ProcessCheckProxyQPS,

		SuccessNextStep: StepModifyInterfacesClusterMetaDelNodes,
		ErrorNextStep:   StepModifyInterfacesClusterCheckProxyQPS},

		workflow.WithMaxReentry(20, StepModifyInterfacesClusterMetaDelNodes))

	// Step-13 Metaserver删除节点, 如果是热活实例组成员，则在global master中解注册节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterMetaDelNodes,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: metaserver.ProcessDelNodes,

		SuccessNextStep: StepModifyInterfacesClusterSetRs,
		ErrorNextStep:   StepModifyInterfacesClusterMetaDelNodes,
	})

	// Step-14 更新BLB的Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterSetRs,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: blb.ProcessSetProxyRsForModify,

		SuccessNextStep: StepModifyInterfacesClusterSetMcpackRs,
		ErrorNextStep:   StepModifyInterfacesClusterSetRs,
	})

	// Step-15 更新BLB的Rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterSetMcpackRs,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: blb.ProcessSetProxyRsForMcpack,

		SuccessNextStep: StepModifyInterfacesClusterSetBnsInstances,
		ErrorNextStep:   StepModifyInterfacesClusterSetMcpackRs,
	})

	// Step-16 更新Bns Service Instance绑定
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterSetBnsInstances,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: bns.ProcessSetBnsInstances,

		SuccessNextStep: StepModifyInterfacesClusterPushFlag,
		ErrorNextStep:   StepModifyInterfacesClusterSetBnsInstances,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterPushFlag,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: pushflag.ProcessUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifyInterfacesClusterInitBcmResource,
		ErrorNextStep:   StepModifyInterfacesClusterPushFlag,
	})

	// Step-17 创建或删除监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterInitBcmResource,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: bcm.ProcessBcmResourceWithoutApp,

		SuccessNextStep: StepModifyInterfacesClusterAddOrRemoveFromBcmGroup,
		ErrorNextStep:   StepModifyInterfacesClusterInitBcmResource,
	})

	// Step-18 删除需要删除的Proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterAddOrRemoveFromBcmGroup,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: bcm.ProcessProxyAddOrRemoveFromBcmGroup,

		SuccessNextStep: StepModifyInterfacesClusterDeleteToDeleteProxies,
		ErrorNextStep:   StepModifyInterfacesClusterAddOrRemoveFromBcmGroup,
	})

	// Step-17 删除需要删除的Proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterDeleteToDeleteProxies,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: delresource.ProcessDeleteToDeleteNodes,

		SuccessNextStep: StepModifyInterfacesClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyInterfacesClusterDeleteToDeleteProxies,
	})

	// Step-19 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterUpdateAppTopologyInXmaster,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: xmaster.ProcessUpdateAppTopologyInXmaster,

		SuccessNextStep: StepModifyInterfacesClusterInitOpMonitor,
		ErrorNextStep:   StepModifyInterfacesClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyInterfacesClusterInitOpMonitor,
		Workflow:        WorkflowModifyInterfacesCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyInterfacesClusterSuccessCallback,
		ErrorNextStep:   StepModifyInterfacesClusterInitOpMonitor,
	})

	// Step-21 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterSuccessCallback,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: callback.ProcessModifyProxySuccess,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyInterfacesClusterSuccessCallback,
	})

	// Step-Error-01 回滚所有申请的资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterRollbackReleaseResources,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: StepModifyInterfacesClusterRollbackBcmGroup,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyInterfacesClusterRollbackBcmGroup,
		Workflow:        WorkflowModifyInterfacesCluster,
		StepProcess:     bcm.ProcessRollbackProxyAddOrRemoveFromBcmGroup,
		SuccessNextStep: StepModifyInterfacesClusterRollbackBcmResource,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackBcmGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterRollbackBcmResource,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: bcm.ProcessRollbackBcmResourceWithoutApp,

		SuccessNextStep: StepModifyInterfacesClusterRollbackPushFlag,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyInterfacesClusterRollbackPushFlag,
		Workflow:        WorkflowModifyInterfacesCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyInterfacesClusterRollbackMeta,
		ErrorNextStep:   StepModifyInterfacesClusterRollbackPushFlag,
	})

	// Step-Error-03 回滚元数据更改
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterRollbackMeta,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: delresource.ProcessRollbackMeta,

		SuccessNextStep: StepModifyInterfacesClusterRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-Error-04 删除资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyInterfacesClusterRollbackCallback,
		Workflow: WorkflowModifyInterfacesCluster,

		StepProcess: callback.ProcessModifyNodesErrorCb,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
