/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_readonly_instance_app.go
 * <AUTHOR>
 * @date 2022/05/16 18:06:09
 * @brief delete readonly instances

 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowDeleteReadonlyGroup                       = "scs-delete-readonly-group-app"
	StepDeleteReadonlyGroupBuildMeta                  = "scs-delete-readonly-group-buildmeta"
	StepDeleteReadonlyGroupTaskStepReleaseNodes       = "scs-delete-readonly-group-task-step-release-nodes"
	StepDeleteReadonlyGroupDeleteEndpoint             = "scs-delete-readonly-group-delete-endpoint"
	StepDeleteReadonlyGroupUpdateBlb                  = "scs-delete-readonly-group-update-blb"
	StepDeleteReadonlyGroupUpdateEntranceBlb          = "scs-delete-readonly-group-update-entrance-blb"
	StepDeleteReadonlyGroupDeleteDomain               = "scs-delete-readonly-group-delete-domain"
	StepDeleteReadonlyGroupRemoveFromBcmGroup         = "scs-delete-readonly-group-remove-from-bcm-group"
	StepDeleteReadonlyGroupDeleteBcmResource          = "scs-delete-readonly-group-delete-bcm-resource"
	StepDeleteReadonlyGroupDeleteResource             = "scs-delete-readonly-group-delete-resource"
	StepDeleteReadonlyGroupDeleteInstance             = "scs-delete-readonly-group-delete-instance"
	StepDeleteReadonlyGroupUpdateAppTopologyInXmaster = "scs-delete-readonly-group-update-app-topo-in-xmaster"
	StepDeleteReadonlyGroupTaskStepSuccess            = "scs-delete-readonly-group-task-step-success"
	StepDeleteReadonlyGroupDeleteCallback             = "scs-delete-readonly-group-delete-callback"
)

func init() {
	// 注册删除SCS标准版实例只读实例组WORKFLOW
	// Step-0 build meta
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupBuildMeta,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForDeletingRoGroup,
		SuccessNextStep: StepDeleteReadonlyGroupTaskStepReleaseNodes,
		ErrorNextStep:   StepDeleteReadonlyGroupBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepDeleteReadonlyGroupTaskStepReleaseNodes,
		Workflow: WorkflowDeleteReadonlyGroup,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.DeleteReadonlyTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepDeleteReadonlyGroupDeleteEndpoint,
		ErrorNextStep:   StepDeleteReadonlyGroupTaskStepReleaseNodes,
	})

	// Step-1 delete endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupDeleteEndpoint,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     endpoint.ProcessDeleteRoEndpoint,
		SuccessNextStep: StepDeleteReadonlyGroupUpdateBlb,
		ErrorNextStep:   StepDeleteReadonlyGroupDeleteEndpoint},

		workflow.WithMaxReentry(2, StepDeleteReadonlyGroupUpdateBlb))

	// Step-2 update blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupUpdateBlb,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     blb.ProcessDeletingRoDelBLB,
		SuccessNextStep: StepDeleteReadonlyGroupUpdateEntranceBlb,
		ErrorNextStep:   StepDeleteReadonlyGroupUpdateBlb},

		workflow.WithMaxReentry(2, StepDeleteReadonlyGroupUpdateEntranceBlb))

	// Step-3 update entrance blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupUpdateEntranceBlb,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepDeleteReadonlyGroupDeleteDomain,
		ErrorNextStep:   StepDeleteReadonlyGroupUpdateEntranceBlb,
	})

	// 删除只读组域名
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupDeleteDomain,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     dns.ProcessDeleteRoGroupDomain,
		SuccessNextStep: StepDeleteReadonlyGroupRemoveFromBcmGroup,
		ErrorNextStep:   StepDeleteReadonlyGroupDeleteDomain},

		workflow.WithMaxReentry(2, StepDeleteReadonlyGroupRemoveFromBcmGroup))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupRemoveFromBcmGroup,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     bcm.ProcessRoRemoveFromGroup,
		SuccessNextStep: StepDeleteReadonlyGroupDeleteBcmResource,
		ErrorNextStep:   StepDeleteReadonlyGroupRemoveFromBcmGroup})

	// 删除只读实例BCM资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupDeleteBcmResource,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepDeleteReadonlyGroupDeleteResource,
		ErrorNextStep:   StepDeleteReadonlyGroupDeleteBcmResource})

	// 删除只读实例资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupDeleteResource,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     delresource.ProcessDelBccResourcesForRo,
		SuccessNextStep: StepDeleteReadonlyGroupDeleteInstance,
		ErrorNextStep:   StepDeleteReadonlyGroupDeleteResource},

		workflow.WithMaxReentry(2, StepDeleteReadonlyGroupDeleteInstance))

	// 清理只读实例在数据库中的数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupDeleteInstance,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     delresource.ProcessDeleteToDeleteRoNodes,
		SuccessNextStep: StepDeleteReadonlyGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepDeleteReadonlyGroupDeleteInstance},

		workflow.WithMaxReentry(2, StepDeleteReadonlyGroupUpdateAppTopologyInXmaster))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepDeleteReadonlyGroupTaskStepSuccess,
		ErrorNextStep:   StepDeleteReadonlyGroupUpdateAppTopologyInXmaster},

		workflow.WithMaxReentry(5, StepDeleteReadonlyGroupDeleteCallback))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupTaskStepSuccess,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepDeleteReadonlyGroupDeleteCallback,
		ErrorNextStep:   StepDeleteReadonlyGroupTaskStepSuccess})

	// 调用Csmaster接口，完成Csmaster相关元数据处理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyGroupDeleteCallback,
		Workflow:        WorkflowDeleteReadonlyGroup,
		StepProcess:     callback.ProcessDeleteRoGroupCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteReadonlyGroupDeleteCallback,
	})
}
