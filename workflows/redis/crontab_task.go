package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/cron"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
)

const (
	WorkflowCleanDeletedInstanceBns     = "scs-clean-deleted-instance-bns"
	StepCleanDeletedInstanceBnsCleanBns = "scs-clean-deleted-instance-bns-clean-bns"

	WorkflowCleanDeletedClusterBns     = "scs-clean-deleted-cluster-bns"
	StepCleanDeletedClusterBnsCleanBns = "scs-clean-deleted-cluster-bns-clean-bns"

	WorkflowCollectDataUsageForConsole    = "scs-collect-data-usage-for-console"
	StepCollectDataUsageForConsoleCollect = "scs-collect-data-usage-for-console-collect"

	WorkflowTimeWindowTaskSchedule        = "scs-time-window-task-schedule"
	StepTimeWindowTaskScheduleWaitingTask = "scs-time-window-task-schedule-waiting-task"

	WorkflowCleanToDeleteBlb = "scs-task-clean-todelete-blb"
	StepCleanToDeleteBlb     = "scs-step-clean-todelete-blb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCleanDeletedInstanceBnsCleanBns,
		Workflow:        WorkflowCleanDeletedInstanceBns,
		StepProcess:     opmonitor.ProcessCleanToDeleteInstanceOpmonitorInstanceBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCleanDeletedInstanceBnsCleanBns},
		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, workflow.FinalStepError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCleanDeletedClusterBnsCleanBns,
		Workflow:        WorkflowCleanDeletedClusterBns,
		StepProcess:     opmonitor.ProcessCleanDeletedOpmonitorBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCleanDeletedClusterBnsCleanBns},
		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, workflow.FinalStepError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCollectDataUsageForConsoleCollect,
		Workflow:        WorkflowCollectDataUsageForConsole,
		StepProcess:     opmonitor.ProcessCollectDataUsageForConsole,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError},
		workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepTimeWindowTaskScheduleWaitingTask,
		Workflow:        WorkflowTimeWindowTaskSchedule,
		StepProcess:     timewindow.ProcessScheduleWaitingTask,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCleanToDeleteBlb,
		Workflow:        WorkflowCleanToDeleteBlb,
		StepProcess:     cron.ProcessDelToDeleteBLB,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError},
	)
}
