/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), self-healing
*/

/*
DESCRIPTION
集群版实例，自愈子任务，用户提前摘除 proxy、node节点流量

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"SelfHealingParams": [
		{
			"ShardID": 133,
			"NodeShortIDList": [12345, 12346]
		}
	]
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
)

const (
	WorkflowSelfHealStandaloneTrafficRemoval = "scs-self-heal-standalone-traffic-removal"

	StepSelfHealStandaloneTrafficRemovalDelRs          = "scs-self-heal-standalone-traffic-removal-del-rs"
	StepSelfHealStandaloneTrafficRemovalDelEntranceRs  = "scs-self-heal-standalone-traffic-removal-del-entrance-rs"
	StepSelfHealStandaloneTrafficRemovalSendClientKill = "scs-self-heal-standalone-traffic-removal-send-client-kill"
)

func init() {

	// Step-1 从blb从删除rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneTrafficRemovalDelRs,
		Workflow:        WorkflowSelfHealStandaloneTrafficRemoval,
		StepProcess:     blb.ProcessDeleteReadonlyGroupRsForTrafficRemoval,
		SuccessNextStep: StepSelfHealStandaloneTrafficRemovalDelEntranceRs,
		ErrorNextStep:   StepSelfHealStandaloneTrafficRemovalDelRs,
	})

	// Step-2 从entrance中删除rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneTrafficRemovalDelEntranceRs,
		Workflow:        WorkflowSelfHealStandaloneTrafficRemoval,
		StepProcess:     blb.ProcessDeleteEntranceRs,
		SuccessNextStep: StepSelfHealStandaloneTrafficRemovalSendClientKill,
		ErrorNextStep:   StepSelfHealStandaloneTrafficRemovalDelEntranceRs,
	})

	// Step-3 发送client kill
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealStandaloneTrafficRemovalSendClientKill,
		Workflow:        WorkflowSelfHealStandaloneTrafficRemoval,
		StepProcess:     handover.ProcessRoNodeClientKillForTrafficRemoval,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSelfHealStandaloneTrafficRemovalSendClientKill,
	})
}
