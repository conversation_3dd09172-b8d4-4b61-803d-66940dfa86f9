/* Copyright 2021 Baidu Inc. All Rights Reserved. */

/*
DESCRIPTION
变更可用区WorkFlow

Parameters

	{
		"AppID": "scs-bj-nxewpztnsreg",
		"Replicas": [{
			"Zone": "zoneA",
			"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
			"Role": "master",
			"Count": 1
		}, {
			"Zone": "zoneB",
			"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
			"Role": "slave",
			"Count": 1
		}],
		"DestReplicas": [{
			"Zone": "zoneA",
			"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
			"Role": "master",
			"Count": 1
		}, {
			"Zone": "zoneB",
			"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
			"Role": "slave",
			"Count": 1
		}]
	}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyAZStandalone = "scs-modify-az-standalone-app"

	StepModifyAZStandaloneInitTimeWindowTask                 = "scs-modify-az-standalone-init-time-window-task"
	StepModifyAZStandaloneAddStartEvent                      = "scs-modify-az-standalone-add-start-event"
	StepModifyAZStandaloneBuildMeta                          = "scs-modify-az-standalone-app-build-meta"
	StepModifyAZStandaloneTaskStepCreateNodes                = "scs-modify-az-standalone-task-step-create-nodes"
	StepModifyAZStandaloneFillSpec                           = "scs-modify-az-standalone-fill-spec"
	StepModifyAZStandaloneCheckSubnetsEnoughIps              = "scs-modify-az-standalone-check-subnets-enough-ips"
	StepModifyAZStandaloneApplyResources                     = "scs-modify-az-standalone-apply-resources"
	StepModifyAZStandaloneApplyResourcesCallback             = "scs-modify-az-standalone-apply-resources-cb"
	StepModifyAZStandaloneTaskStepDeployNodes                = "scs-modify-az-standalone-task-step-deploy-nodes"
	StepModifyAZStandaloneInitMachineEnv                     = "scs-modify-az-standalone-init-machine-env"
	StepModifyAZStandaloneUpdateSecurityGroups               = "scs-modify-az-standalone-update-security-groups"
	StepModifyAZStandaloneDeployRedis                        = "scs-modify-az-standalone-apply-deploy-redis"
	StepModifyAZStandaloneSetAcl                             = "scs-modify-az-standalone-set-acl"
	StepModifyAZStandaloneUpdateConfig                       = "scs-modify-az-standalone-update-config"
	StepModifyAZStandaloneTaskStepSync                       = "scs-modify-az-standalone-task-step-sync"
	StepModifyAZStandaloneInitTopology                       = "scs-modify-az-standalone-init-topology"
	StepModifyAZStandaloneCheckAllSync                       = "scs-modify-az-standalone-check-all-sync"
	StepModifyAZStandaloneDeployFilebeat                     = "scs-modify-az-standalone-apply-deploy-filebeat"
	StepModifyAZStandaloneTaskStepHandover                   = "scs-modify-az-standalone-task-step-handover"
	StepModifyAZStandaloneSetSwitchTags                      = "scs-modify-az-standalone-set-switch-tags"
	StepModifyAZStandaloneHandoverToNewAZ                    = "scs-modify-az-standalone-handover-to-new-az"
	StepModifyAZStandaloneCorrectMasterSlaveInfo             = "scs-modify-az-standalone-correct-master-slave-info"
	StepModifyAZStandaloneSetRs                              = "scs-modify-az-standalone-set-rs"
	StepModifyAZStandaloneInitBcmResource                    = "scs-modify-az-standalone-init-bcm-resource"
	StepModifyAZStandaloneDeleteOldNodes                     = "scs-modify-az-standalone-del-old-nodes"
	StepModifyAZStandaloneInitOpMonitor                      = "scs-modify-az-standalone-init-op-monitor"
	StepModifyAZStandalonePushMonitorHTGRPSlaveFlag          = "scs-modify-az-standalone-push-htgrp-slave-flag"
	StepModifyAZStandalonePushFlag                           = "scs-modify-az-standalone-push-flag"
	StepModifyAZStandaloneUpdateAppTopologyInXmaster         = "scs-modify-az-standalone-update-app-topo-in-xmaster"
	StepModifyAZStandaloneCloseTimeWindowTask                = "scs-modify-az-standalone-close-tw-task"
	StepModifyAZStandaloneReSetSwitchTags                    = "scs-modify-az-standalone-reset-switch-tags"
	StepModifyAZStandaloneTaskStepSuccess                    = "scs-modify-az-standalone-step-success"
	StepModifyAZStandaloneSuccessCallback                    = "scs-modify-az-standalone-success-cb"
	StepModifyAZStandaloneAddSuccessEndEvent                 = "scs-modify-az-standalone-add-success-end-event"
	StepModifyAZStandaloneRollbackReleaseResource            = "scs-modify-az-standalone-rollback-release-resource"
	StepModifyAZStandaloneTaskStepError                      = "scs-modify-az-standalone-app-task-step-error"
	StepModifyAZStandaloneRollbackBcmResource                = "scs-modify-az-standalone-rollback-bcm-resource"
	StepModifyAZStandaloneRollbackPushFlag                   = "scs-modify-az-standalone-rollback-push-flag"
	StepModifyAZStandaloneRollbackMeta                       = "scs-modify-az-standalone-rollback-meta"
	StepModifyAZStandaloneRollbackUpdateAppTopologyInXmaster = "scs-modify-az-standalone-rollback-update-app-topo-in-xmaster"
	StepModifyAZStandaloneRollbackCallback                   = "scs-modify-az-standalone-rollback-callback"
	StepModifyAZStandaloneAddFailEndEvent                    = "scs-modify-az-standalone-add-fail-end-event"
)

func init() {

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneInitTimeWindowTask,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepModifyAZStandaloneAddStartEvent,
		ErrorNextStep:   StepModifyAZStandaloneInitTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneAddStartEvent,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     event.ProcessAddModifyingAZStartEvent,
		SuccessNextStep: StepModifyAZStandaloneBuildMeta,
		ErrorNextStep:   StepModifyAZStandaloneAddStartEvent,
	})

	// 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneBuildMeta,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForModifyAZ,
		SuccessNextStep: StepModifyAZStandaloneTaskStepCreateNodes,
		ErrorNextStep:   StepModifyAZStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZStandaloneTaskStepCreateNodes,
		Workflow: WorkflowModifyAZStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZStandaloneFillSpec,
		ErrorNextStep:   StepModifyAZStandaloneTaskStepCreateNodes,
	})

	// 将规格信息填入Cluster表或Interface表；
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneFillSpec,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepModifyAZStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyAZStandaloneFillSpec,
	})

	// 检查子网ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneCheckSubnetsEnoughIps,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepModifyAZStandaloneApplyResources,
		ErrorNextStep:   StepModifyAZStandaloneCheckSubnetsEnoughIps,
	})

	// 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneApplyResources,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepModifyAZStandaloneApplyResourcesCallback,
		ErrorNextStep:   StepModifyAZStandaloneApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneApplyResourcesCallback,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     callback.ProcessApplyResourceCallbackForModifyAZ,
		SuccessNextStep: StepModifyAZStandaloneTaskStepDeployNodes,
		ErrorNextStep:   StepModifyAZStandaloneApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZStandaloneTaskStepDeployNodes,
		Workflow: WorkflowModifyAZStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZStandaloneInitMachineEnv,
		ErrorNextStep:   StepModifyAZStandaloneTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneInitMachineEnv,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifyAZStandaloneUpdateSecurityGroups,
		ErrorNextStep:   StepModifyAZStandaloneInitMachineEnv,
	})

	// 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneUpdateSecurityGroups,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandalone,
		SuccessNextStep: StepModifyAZStandaloneDeployRedis,
		ErrorNextStep:   StepModifyAZStandaloneUpdateSecurityGroups,
	})

	// 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneDeployRedis,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepModifyAZStandaloneSetAcl,
		ErrorNextStep:   StepModifyAZStandaloneDeployRedis},
		workflow.WithStepTimeout(15*time.Minute))

	// 设置初始auth,acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneSetAcl,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     acl.ProcessInitAclStandaloneForNewNode,
		SuccessNextStep: StepModifyAZStandaloneUpdateConfig,
		ErrorNextStep:   StepModifyAZStandaloneSetAcl,
	})

	// 更新配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneUpdateConfig,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     config.ProcessUpdateConfigStandalone,
		SuccessNextStep: StepModifyAZStandaloneTaskStepSync,
		ErrorNextStep:   StepModifyAZStandaloneUpdateConfig,
	}, workflow.WithMaxReentry(2, StepModifyAZStandaloneTaskStepSync))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZStandaloneTaskStepSync,
		Workflow: WorkflowModifyAZStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepSync, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZStandaloneInitTopology,
		ErrorNextStep:   StepModifyAZStandaloneTaskStepSync,
	})

	// 设置拓扑结构
	// 对于主从版，仅设置主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneInitTopology,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     handover.ProcessModifyAZLocalSlaveOf,
		SuccessNextStep: StepModifyAZStandaloneCheckAllSync,
		ErrorNextStep:   StepModifyAZStandaloneInitTopology,
	})

	// 检查同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneCheckAllSync,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepModifyAZStandaloneDeployFilebeat,
		ErrorNextStep:   StepModifyAZStandaloneCheckAllSync,
	}, workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneDeployFilebeat,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepModifyAZStandaloneTaskStepHandover,
		ErrorNextStep:   StepModifyAZStandaloneDeployFilebeat},
		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZStandaloneTaskStepHandover,
		Workflow: WorkflowModifyAZStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepHandover, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZStandaloneSetSwitchTags,
		ErrorNextStep:   StepModifyAZStandaloneTaskStepHandover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneSetSwitchTags,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     handover.ProcessHandoverSetTags,
		SuccessNextStep: StepModifyAZStandaloneHandoverToNewAZ,
		ErrorNextStep:   StepModifyAZStandaloneSetSwitchTags})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneHandoverToNewAZ,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     handover.ProcessHandoverForModifyAZ,
		SuccessNextStep: StepModifyAZStandaloneCorrectMasterSlaveInfo,
		ErrorNextStep:   StepModifyAZStandaloneHandoverToNewAZ})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneCorrectMasterSlaveInfo,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     callback.ModifyInstanceMasterSlaveInfo,
		SuccessNextStep: StepModifyAZStandaloneSetRs,
		ErrorNextStep:   StepModifyAZStandaloneCorrectMasterSlaveInfo})

	// 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneSetRs,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     blb.ProcessSetStandaloneRs,
		SuccessNextStep: StepModifyAZStandaloneInitBcmResource,
		ErrorNextStep:   StepModifyAZStandaloneSetRs,
	})

	// 根据变更新建或删除监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneInitBcmResource,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepModifyAZStandalonePushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifyAZStandaloneInitBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandalonePushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepModifyAZStandalonePushFlag,
		ErrorNextStep:   StepModifyAZStandalonePushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandalonePushFlag,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyAZStandaloneDeleteOldNodes,
		ErrorNextStep:   StepModifyAZStandalonePushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneDeleteOldNodes,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyAZStandaloneInitOpMonitor,
		ErrorNextStep:   StepModifyAZStandaloneDeleteOldNodes,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneInitOpMonitor,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyAZStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyAZStandaloneInitOpMonitor,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyAZStandaloneCloseTimeWindowTask,
		ErrorNextStep:   StepModifyAZStandaloneUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneCloseTimeWindowTask,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepModifyAZStandaloneTaskStepSuccess,
		ErrorNextStep:   StepModifyAZStandaloneCloseTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZStandaloneTaskStepSuccess,
		Workflow: WorkflowModifyAZStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepModifyAZStandaloneReSetSwitchTags,
		ErrorNextStep:   StepModifyAZStandaloneTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneReSetSwitchTags,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     handover.ProcessHandoverReSetTags,
		SuccessNextStep: StepModifyAZStandaloneSuccessCallback,
		ErrorNextStep:   StepModifyAZStandaloneReSetSwitchTags,
	})

	// Step-11 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneSuccessCallback,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     callback.ProcessModifyAZSuccessCb,
		SuccessNextStep: StepModifyAZStandaloneAddSuccessEndEvent,
		ErrorNextStep:   StepModifyAZStandaloneSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneAddSuccessEndEvent,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     event.ProcessAddModifyingAZSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyAZStandaloneAddSuccessEndEvent,
	})

	// Step-Error-01 删除已创建的节点
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneRollbackReleaseResource,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifyAZStandaloneTaskStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneTaskStepError,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor(timewindow.ModifyAZTask, "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifyAZStandaloneRollbackBcmResource,
		ErrorNextStep:   StepModifyAZStandaloneTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneRollbackBcmResource,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     bcm.ProcessRollbackBcmResourceWithoutApp,
		SuccessNextStep: StepModifyAZStandaloneRollbackPushFlag,
		ErrorNextStep:   StepModifyAZStandaloneRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneRollbackPushFlag,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyAZStandaloneRollbackMeta,
		ErrorNextStep:   StepModifyAZStandaloneRollbackPushFlag,
	})

	// Step-Error-01 删除已创建的节点
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneRollbackMeta,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifyAZStandaloneRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyAZStandaloneRollbackCallback,
		ErrorNextStep:   StepModifyAZStandaloneRollbackUpdateAppTopologyInXmaster,
	})

	// Step-Error-02 比那更失败时，如果已经创建了资源，需要删除资源
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneRollbackCallback,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     callback.ProcessModifyAZErrorCb,
		SuccessNextStep: StepModifyAZStandaloneAddFailEndEvent,
		ErrorNextStep:   StepModifyAZStandaloneRollbackCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZStandaloneAddFailEndEvent,
		Workflow:        WorkflowModifyAZStandalone,
		StepProcess:     event.ProcessAddModifyingAZFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyAZStandaloneAddFailEndEvent,
	})

}
