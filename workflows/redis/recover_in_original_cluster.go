package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/failover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/recover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowScsRecoverInOriginalCluster             = "scs-recover-in-original-cluster"
	StepRecoverInOriginalClusterTaskStepRecoverData = "scs-recover-in-original-cluster-task-step-recover-data"
	StepRecoverInOriginalClusterDisableFailover     = "scs-recover-in-original-cluster-disable-failover"
	StepRecoverInOriginalClusterDispatchTask        = "scs-recover-in-original-cluster-dispatch-task"
	StepRecoverInOriginalClusterCheckAllSync        = "scs-recover-in-original-cluster-check-all-sync"
	StepRecoverInOriginalClusterEnableFailover      = "scs-recover-in-original-cluster-enable-failover"
	StepRecoverInOriginalClusterTaskStepSuccess     = "scs-recover-in-original-cluster-task-step-success"
	StepRecoverInOriginalClusterCallBack            = "scs-recover-in-original-cluster-cb" // 将cache_cluster status 从恢复中改成运行中
)

func init() {
	// Step-2 dispatch task
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverInOriginalClusterTaskStepRecoverData,
		Workflow: WorkflowScsRecoverInOriginalCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepRecoverData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverInOriginalClusterDisableFailover,
		ErrorNextStep:   StepRecoverInOriginalClusterTaskStepRecoverData,
	})

	// Step 关闭自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDisableFailover,
		Workflow:        WorkflowScsRecoverInOriginalCluster,
		StepProcess:     failover.ProcessDisableFailover,
		SuccessNextStep: StepRecoverInOriginalClusterDispatchTask,
		ErrorNextStep:   StepRecoverInOriginalClusterDisableFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterDispatchTask,
		Workflow:        WorkflowScsRecoverInOriginalCluster,
		StepProcess:     recover.ProcessDbrsRecoverRedisInOriginalClusterDispatchTask,
		SuccessNextStep: StepRecoverInOriginalClusterCheckAllSync,
		ErrorNextStep:   StepRecoverInOriginalClusterDispatchTask},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCheckAllSync,
		Workflow:        WorkflowScsRecoverInOriginalCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRecoverInOriginalClusterEnableFailover,
		ErrorNextStep:   StepRecoverInOriginalClusterCheckAllSync},

		workflow.WithStepTimeout(15*time.Minute))

	// Step 开启自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterEnableFailover,
		Workflow:        WorkflowScsRecoverInOriginalCluster,
		StepProcess:     failover.ProcessEnableFailover,
		SuccessNextStep: StepRecoverInOriginalClusterTaskStepSuccess,
		ErrorNextStep:   StepRecoverInOriginalClusterEnableFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterTaskStepSuccess,
		Workflow:        WorkflowScsRecoverInOriginalCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRecoverInOriginalClusterCallBack,
		ErrorNextStep:   StepRecoverInOriginalClusterTaskStepSuccess,
	})

	// Step-3 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInOriginalClusterCallBack,
		Workflow:        WorkflowScsRecoverInOriginalCluster,
		StepProcess:     callback.ProcessRecoverInOriginalClusterCallBack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverInOriginalClusterCallBack,
	})
}
