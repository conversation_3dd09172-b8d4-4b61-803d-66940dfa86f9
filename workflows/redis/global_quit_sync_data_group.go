/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/21 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_quit_sync_data_group.go
 * <AUTHOR>
 * @date 2022/11/21 20:04:35
 * @brief
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
)

// WorkFlowLocalJoinSyncDataGroup will join sync group to sync data
const (
	WorkFlowLocalQuitSyncDataGroup                = "scs-local-quit-sync-data-group"
	WorkFlowLocalQuitSyncDataGroupQuitSyncChannel = "scs-local-quit-sync-group-quit-sync-channel"
	WorkFlowLocalQuitSyncDataGroupCheckSyncMember = "scs-local-quit-sync-group-check-sync-member"
	WorkFlowLocalQuitSyncDataGroupStepSuccessCb   = "scs-local-quit-sync-group-success-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncDataGroupQuitSyncChannel,
		Workflow:        WorkFlowLocalQuitSyncDataGroup,
		StepProcess:     syncgroup.ProcessQuitSyncChannel,
		SuccessNextStep: WorkFlowLocalQuitSyncDataGroupCheckSyncMember,
		ErrorNextStep:   WorkFlowLocalQuitSyncDataGroupQuitSyncChannel},
		// 增加重试 构建场景：A B 异地多活新增C
		// B向C添加同步通道并没有通过cs-master，导致删除失败
		// 重试保证任务成功
		workflow.WithMaxReentry(2, WorkFlowLocalQuitSyncDataGroupCheckSyncMember))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncDataGroupCheckSyncMember,
		Workflow:        WorkFlowLocalQuitSyncDataGroup,
		StepProcess:     syncgroup.ProcessCheckAndDeleteSyncMember,
		SuccessNextStep: WorkFlowLocalQuitSyncDataGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalQuitSyncDataGroupCheckSyncMember,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitSyncDataGroupStepSuccessCb,
		Workflow:        WorkFlowLocalQuitSyncDataGroup,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalQuitSyncDataGroupStepSuccessCb,
	})
}
