/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_readonly_instance_app.go
 * <AUTHOR>
 * @date 2022/05/16 18:06:09
 * @brief delete readonly instances

 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowDeleteAllInstance                  = "scs-delete-all-instance-app"
	StepDeleteAllInstanceDeleteAppFromXmaster  = "scs-delete-all-instance-delete-app-from-xmaster"
	StepQuitSyncGroup                          = "scs-delete-all-instance-quit-sync-group"
	StepDeleteAllInstanceRemoveFromBcmGroup    = "scs-delete-all-instance-remove-from-bcm-group"
	StepDeleteAllInstanceDeleteBcmResource     = "scs-delete-all-instance-delete-bcm-resource"
	StepDeleteAllInstanceDeleteOpMonitor       = "scs-delete-all-instance-delete-op-monitor"
	StepDeleteAllInstanceDeleteResource        = "scs-delete-all-instance-delete-resource"
	StepDeleteAllInstanceDeleteDns             = "scs-delete-all-instance-delete-dns"
	StepDeleteAllInstanceDeleteEndpoint        = "scs-delete-all-instance-delete-endpoint"
	StepDeleteAllInstanceDeleteBlb             = "scs-delete-all-instance-delete-Blb"
	StepDeleteAllInstanceDeleteEntranceBlb     = "scs-delete-all-instance-delete-entrance-Blb"
	StepDeleteAllInstanceDeleteToDeleteBlb     = "scs-delete-all-instance-delete-to-delete-Blb"
	StepDeleteAllInstanceDeleteBackupPolicy    = "scs-delete-all-instance-delete-backup-policy"
	StepDeleteAllInstanceDeleteAOFBackupPolicy = "scs-delete-all-instance-delete-aof-backup-policy"
	StepDeleteAllInstanceDeleteSecuritygroup   = "scs-delete-all-instance-delete-securitygroup"
)

func init() {
	// 注册删除SCS实例
	// 从xmaster删除集群
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteAppFromXmaster,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     xmaster.ProcessDeleteAppFromXmaster,
		SuccessNextStep: StepQuitSyncGroup,
		ErrorNextStep:   StepDeleteAllInstanceDeleteAppFromXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepQuitSyncGroup,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     syncgroup.ProcessQuitSyncGroup,
		SuccessNextStep: StepDeleteAllInstanceDeleteResource,
		ErrorNextStep:   StepQuitSyncGroup},
		workflow.WithStepTimeout(30*time.Second))

	// Step-1 删除instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteResource,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     delresource.ProcessDelBccResources,
		SuccessNextStep: StepDeleteAllInstanceRemoveFromBcmGroup,
		ErrorNextStep:   StepDeleteAllInstanceDeleteResource},

		workflow.WithMaxReentry(2, StepDeleteAllInstanceRemoveFromBcmGroup))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceRemoveFromBcmGroup,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     bcm.ProcessDeleteAllRemoveFromGroup,
		SuccessNextStep: StepDeleteAllInstanceDeleteBcmResource,
		ErrorNextStep:   StepDeleteAllInstanceRemoveFromBcmGroup,
	})

	// Step-2 删除instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteBcmResource,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     bcm.ProcessDeleteBcmResourceWithinApp,
		SuccessNextStep: StepDeleteAllInstanceDeleteOpMonitor,
		ErrorNextStep:   StepDeleteAllInstanceDeleteBcmResource})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteOpMonitor,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     opmonitor.ProcessDeleteOpmonitorBnsService,
		SuccessNextStep: StepDeleteAllInstanceDeleteDns,
		ErrorNextStep:   StepDeleteAllInstanceDeleteOpMonitor})

	// 删除 Dns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteDns,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     dns.ProcessDeleteAppDomain,
		SuccessNextStep: StepDeleteAllInstanceDeleteEndpoint,
		ErrorNextStep:   StepDeleteAllInstanceDeleteDns},

		workflow.WithMaxReentry(2, StepDeleteAllInstanceDeleteEndpoint))
	// 删除 Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteEndpoint,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     endpoint.ProcessDeleteRwEndpoint,
		SuccessNextStep: StepDeleteAllInstanceDeleteEntranceBlb,
		ErrorNextStep:   StepDeleteAllInstanceDeleteEndpoint},

		workflow.WithMaxReentry(2, StepDeleteAllInstanceDeleteEntranceBlb))

	// 删除 entrance Blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteEntranceBlb,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     blb.ProcessDeleteEntranceBlB,
		SuccessNextStep: StepDeleteAllInstanceDeleteToDeleteBlb,
		ErrorNextStep:   StepDeleteAllInstanceDeleteEntranceBlb},

		workflow.WithMaxReentry(2, StepDeleteAllInstanceDeleteToDeleteBlb))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteToDeleteBlb,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     blb.ProcessDelToDeleteBLB,
		SuccessNextStep: StepDeleteAllInstanceDeleteBlb,
		ErrorNextStep:   StepDeleteAllInstanceDeleteToDeleteBlb})

	// 删除 Blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteBlb,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     blb.ProcessDelBLB,
		SuccessNextStep: StepDeleteAllInstanceDeleteBackupPolicy,
		ErrorNextStep:   StepDeleteAllInstanceDeleteBlb},

		workflow.WithMaxReentry(2, workflow.FinalStepSuccess))

	// check 并删除备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteBackupPolicy,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     backup.ProcessDeleteBackupPolicy,
		SuccessNextStep: StepDeleteAllInstanceDeleteAOFBackupPolicy,
		ErrorNextStep:   StepDeleteAllInstanceDeleteBackupPolicy},

		workflow.WithMaxReentry(2, StepDeleteAllInstanceDeleteAOFBackupPolicy))

	// check 并删除 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteAOFBackupPolicy,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOF,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteAllInstanceDeleteAOFBackupPolicy},

		workflow.WithMaxReentry(2, workflow.FinalStepSuccess))

	// 删除安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllInstanceDeleteSecuritygroup,
		Workflow:        WorkflowDeleteAllInstance,
		StepProcess:     securitygroup.ProcessDelSecurityGroup,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteAllInstanceDeleteSecuritygroup},

		workflow.WithMaxReentry(2, workflow.FinalStepSuccess))
}
