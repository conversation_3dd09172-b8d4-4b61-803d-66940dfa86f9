package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"time"
)

const (
	WorkflowModifyBnsService                 = "scs-modify-bns-service"
	StepModifyBnsServiceCreateNewBns         = "step-modify-bns-service-create-new-bns"
	StepModifyBnsServiceSetBnsInstance       = "step-modify-bns-service-set-bns-instance"
	StepModifyBnsServiceUpdateLocalBnsGroup  = "step-modify-bns-service-update-local-bns-group"
	StepModifyBnsServiceUpdateGlobalBnsGroup = "step-modify-bns-service-update-global-bns-group"
	StepModifyBnsServiceDeleteOldBnsService  = "step-modify-bns-service-delete-old-bns-service"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyBnsServiceCreateNewBns,
		Workflow:        WorkflowModifyBnsService,
		StepProcess:     bns.ProcessCreateNewBnsService,
		SuccessNextStep: StepModifyBnsServiceSetBnsInstance,
		ErrorNextStep:   StepModifyBnsServiceCreateNewBns,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyBnsServiceSetBnsInstance,
		Workflow:        WorkflowModifyBnsService,
		StepProcess:     bns.ProcessSetBnsInstanceForNewBnsService,
		SuccessNextStep: StepModifyBnsServiceUpdateLocalBnsGroup,
		ErrorNextStep:   StepModifyBnsServiceSetBnsInstance,
	}, workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyBnsServiceUpdateLocalBnsGroup,
		Workflow:        WorkflowModifyBnsService,
		StepProcess:     bns.ProcessUpdateLocalBnsGroup,
		SuccessNextStep: StepModifyBnsServiceUpdateGlobalBnsGroup,
		ErrorNextStep:   StepModifyBnsServiceUpdateLocalBnsGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyBnsServiceUpdateGlobalBnsGroup,
		Workflow:        WorkflowModifyBnsService,
		StepProcess:     bns.ProcessUpdateGlobalBnsGroup,
		SuccessNextStep: StepModifyBnsServiceDeleteOldBnsService,
		ErrorNextStep:   StepModifyBnsServiceUpdateGlobalBnsGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyBnsServiceDeleteOldBnsService,
		Workflow:        WorkflowModifyBnsService,
		StepProcess:     bns.ProcessDeleteOldBnsService,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyBnsServiceDeleteOldBnsService,
	})
}
