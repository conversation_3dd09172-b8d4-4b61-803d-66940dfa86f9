/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建SCS标准版实例WORKFLOW

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"Name": "redis-kx42u7p4",
	"UserID": "680cdfe705434d53b13a156284ed973b",
	"Port": 6379,
	"VpcID": "5c03c3e6-c38e-44e8-a50c-0648cff93faf"
	"Replicas": [{
		"Zone": "zoneA",
		"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
		"Role": "master",
		"Count": 1
	}, {
		"Zone": "zoneB",
		"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
		"Role": "slave",
		"Count": 1
	}],
	"DefaultPassword": "xhdogY7*er",
	"Engine": "redis",
	"EngineVersion": "6.2"
	"NodeType": "cache.n1.small",
	"ShardCount": 1,
	"DeployIDList": ["467eee77"],
	"Pool": "bbc-cu876y",
	"ClusterType": "master-slave"
	// Todo 增加克隆集群相关参数
}
*/

package workflows

import (
	"context"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/availability"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowCreateStandalone                         = "scs-create-standalone-app"
	StepCreateStandaloneBuildMeta                    = "scs-create-standalone-app-build-meta"
	StepCreateStandaloneTaskStepCreateNodes          = "scs-create-standalone-task-step-create-nodes"
	StepCreateStandaloneFillSpec                     = "scs-create-standalone-fill-spec"
	StepCreateStandaloneCheckSubnetsIpv6             = "scs-create-standalone-check-subnets-ipv6"
	StepCreateStandaloneCheckSubnetsEnoughIps        = "scs-create-standalone-check-subnets-enough-ips"
	StepCreateStandaloneStartInitBlbAndEndpoint      = "scs-create-standalone-start-init-blb-and-endpoint"
	StepCreateStandaloneCreateSecurityGroups         = "scs-create-standalone-create-security-groups"
	StepCreateStandaloneApplyResources               = "scs-create-standalone-apply-resources"
	StepCreateStandaloneApplyResourcesCallback       = "scs-create-standalone-apply-resources-cb"
	StepCreateStandaloneTaskStepDeployNodes          = "scs-create-standalone-task-step-deploy-nodes"
	StepCreateStandaloneStartSubTasksAfterApply      = "scs-create-standalone-start-sub-tasks-after-apply"
	StepCreateStandaloneInitMachineEnv               = "scs-create-standalone-init-machine-env"
	StepCreateStandaloneApplyConfTpl                 = "scs-create-standalone-apply-conf-tpl"
	StepCreateStandaloneDeployRedis                  = "scs-create-standalone-apply-deploy-redis"
	StepCreateStandaloneTaskStepConfigNodes          = "scs-create-standalone-task-step-config-nodes"
	StepCreateStandaloneInitTopology                 = "scs-create-standalone-init-topology"
	StepCreateStandaloneDeployFilebeat               = "scs-create-standalone-apply-deploy-filebeat"
	StepCreateStandaloneSetRs                        = "scs-create-standalone-set-rs"
	StepCreateStandaloneSetAcl                       = "scs-create-standalone-set-acl"
	StepCreateStandaloneInitAppInXmaster             = "scs-create-standalone-init-app-in-xmaster"
	StepCreateStandaloneChooseHAServer               = "scs-create-standalone-choose-ha-server"
	StepCreateStandaloneUpdateConfigForNewAgent      = "scs-create-standalone-update-config-for-new-agent"
	StepCreateStandalonePushFlagAllTrue              = "scs-create-standalone-push-flag-all-true"
	StepCreateStandaloneTaskStepSuccess              = "scs-create-standalone-task-step-success"
	StepCreateStandaloneCreateCallback               = "scs-create-standalone-create-cb"
	StepCreateStandaloneConfigSetSentinelConf        = "scs-create-standalone-config-get-sentinel-conf"
	StepCreateStandaloneRollbackCallback             = "scs-create-standalone-rollback-callback"
	StepCreateStandaloneTaskStepError                = "scs-create-standalone-task-step-error"
	StepCreateStandaloneRollbackDeleteEndpoint       = "scs-create-standalone-rollback-delete-endpoint"
	StepCreateStandaloneRollbackDeleteAppFromXmaster = "scs-create-standalone-rollback-delete-app-from-xmaster"
	StepCreateStandaloneRollbackDeleteBLB            = "scs-create-standalone-rollback-delete-blb"
	StepCreateStandaloneRollbackDeleteOpMonitor      = "scs-create-standalone-rollback-delete-op-monitor"
	StepCreateStandaloneRollbackRemoveFromBcmGroup   = "scs-create-standalone-rollback-remove-from-bcm-group"
	StepCreateStandaloneRollbackDeleteBcmResource    = "scs-create-standalone-rollback-delete-bcm-resource"
	StepCreateStandaloneRollbackReleaseResources     = "scs-create-standalone-rollback-release-resource"

	WorkFlowCreateStandaloneInitBlbAndEndpoint         = "scs-create-standalone-init-blb-and-endpoint"
	SteoCreateStandaloneInitBlbAndEndpointInitBlb      = "scs-create-standalone-init-blb-and-endpoint-init-blb"
	SteoCreateStandaloneInitBlbAndEndpointInitEndpoint = "scs-create-standalone-init-blb-and-endpoint-init-endpoint"
	SteoCreateStandaloneInitBlbAndEndpointCreateDomain = "scs-create-standalone-init-blb-and-endpoint-create-domain"

	WorkflowCreateStandaloneInitOpMonitor              = "scs-create-standalone-init-op-monitor"
	StepCreateStandaloneInitOpMonitorCreateBnsService  = "scs-create-standalone-init-op-monitor-create-bns-service"
	StepCreateStandaloneInitOpMonitorCreateBnsInstance = "scs-create-standalone-init-op-monitor-create-bns-instance"

	WorkflowCreateStandaloneInitBcmResource           = "scs-create-standalone-init-bcm-resource"
	StepCreateStandaloneInitBcmResourceCreateResource = "scs-create-standalone-init-bcm-resource-create-resource"
	StepCreateStandaloneInitBcmResourceAddToBcmGroup  = "scs-create-standalone-init-bcm-resource-add-to-bcm-group"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// Step-1 构建元数据，包括创建Clusters、Cluster对应的Nodes并分配角色
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneBuildMeta,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForCreatingStandalone,
		SuccessNextStep: StepCreateStandaloneTaskStepCreateNodes,
		ErrorNextStep:   StepCreateStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneTaskStepCreateNodes,
		Workflow: WorkflowCreateStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.CreateTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateStandaloneFillSpec,
		ErrorNextStep:   StepCreateStandaloneTaskStepCreateNodes,
	})

	// Step-2 将规格信息填入Cluster表或Interface表；
	// 同分片的node应当规格相同，Port相同，使用同样的部署集
	// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneFillSpec,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepCreateStandaloneCheckSubnetsIpv6,
		ErrorNextStep:   StepCreateStandaloneFillSpec,
	})

	// Step-3 检查子网，是否使用ipv6双栈网络
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneCheckSubnetsIpv6,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     checksubnets.CheckIPV6,
		SuccessNextStep: StepCreateStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepCreateStandaloneCheckSubnetsIpv6,
	})

	// Step-4 检查子网ip是否充足
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneCheckSubnetsEnoughIps,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepCreateStandaloneStartInitBlbAndEndpoint,
		ErrorNextStep:   StepCreateStandaloneCheckSubnetsEnoughIps,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneStartInitBlbAndEndpoint,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     ProcessCreateStandaloneStartWorkflowInitBlbAndEndpoint,
		SuccessNextStep: StepCreateStandaloneCreateSecurityGroups,
		ErrorNextStep:   StepCreateStandaloneStartInitBlbAndEndpoint,
	})

	// Step-5 创建App对应的安全组
	// 同时调用CsMaster的API，将相关的信息存入CsMaster的数据库中
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneCreateSecurityGroups,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     securitygroup.ProcessInitSecurityGroupStandalone,
		SuccessNextStep: StepCreateStandaloneApplyResources,
		ErrorNextStep:   StepCreateStandaloneCreateSecurityGroups,
	})

	// Step-6 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	applyResourceTimeout := 15 * time.Minute
	if strings.HasPrefix(env.IDC(), "licloud") {
		applyResourceTimeout = 120 * time.Minute
	}
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneApplyResources,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepCreateStandaloneApplyResourcesCallback,
		ErrorNextStep:   StepCreateStandaloneApplyResources},

		workflow.WithStepTimeout(applyResourceTimeout))

	// Step-6 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneApplyResourcesCallback,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepCreateStandaloneTaskStepDeployNodes,
		ErrorNextStep:   StepCreateStandaloneApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneTaskStepDeployNodes,
		Workflow: WorkflowCreateStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.CreateTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateStandaloneStartSubTasksAfterApply,
		ErrorNextStep:   StepCreateStandaloneTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneStartSubTasksAfterApply,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     ProcessCreateStandaloneStartWorkflowsAfterApplyResources,
		SuccessNextStep: StepCreateStandaloneInitMachineEnv,
		ErrorNextStep:   StepCreateStandaloneStartSubTasksAfterApply,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitMachineEnv,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepCreateStandaloneApplyConfTpl,
		ErrorNextStep:   StepCreateStandaloneInitMachineEnv,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneApplyConfTpl,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     config.ProcessApplyConfTplForCreate,
		SuccessNextStep: StepCreateStandaloneDeployRedis,
		ErrorNextStep:   StepCreateStandaloneApplyConfTpl,
	})

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneDeployRedis,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     deploy.ProcessDeployAllForNewCreate,
		SuccessNextStep: StepCreateStandaloneTaskStepConfigNodes,
		ErrorNextStep:   StepCreateStandaloneDeployRedis},

		workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepCreateStandaloneTaskStepConfigNodes,
		Workflow: WorkflowCreateStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.CreateTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepCreateStandaloneInitTopology,
		ErrorNextStep:   StepCreateStandaloneTaskStepConfigNodes})

	// Step-7 设置拓扑结构
	// 对于主从版，仅设置主从关系
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitTopology,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     topology.ProcessInitStandaloneTopo,
		SuccessNextStep: StepCreateStandaloneDeployFilebeat,
		ErrorNextStep:   StepCreateStandaloneInitTopology,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneDeployFilebeat,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepCreateStandaloneSetRs,
		ErrorNextStep:   StepCreateStandaloneDeployFilebeat},
		workflow.WithStepTimeout(5*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// Step-9 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneSetRs,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     blb.ProcessSetStandaloneRs,
		SuccessNextStep: StepCreateStandaloneSetAcl,
		ErrorNextStep:   StepCreateStandaloneSetRs,
	})

	// Step-10 设置初始auth,acl
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneSetAcl,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     acl.ProcessInitAclStandalone,
		SuccessNextStep: StepCreateStandaloneInitAppInXmaster,
		ErrorNextStep:   StepCreateStandaloneSetAcl,
	})

	// todo 增加克隆恢复相关step

	// Step-11 集群注册到xmaster
	// 调用Xmaster的API，初始化拓扑信息、监控策略、自愈开关
	// zhangzhigang06
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitAppInXmaster,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     xmaster.ProcessInitAppInXmaster,
		SuccessNextStep: StepCreateStandaloneChooseHAServer,
		ErrorNextStep:   StepCreateStandaloneInitAppInXmaster,
	})

	// 选择高可用组件
	// 根据cache_cluster.use_xmaster设置对应高可用组件的开关
	// zhangzhigang06
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneChooseHAServer,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     availability.InitHAServer,
		SuccessNextStep: StepCreateStandaloneUpdateConfigForNewAgent,
		ErrorNextStep:   StepCreateStandaloneChooseHAServer,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneUpdateConfigForNewAgent,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     config.ProcessUpdateConfigNewForCreate,
		SuccessNextStep: StepCreateStandalonePushFlagAllTrue,
		ErrorNextStep:   StepCreateStandaloneUpdateConfigForNewAgent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandalonePushFlagAllTrue,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     pushflag.ProcessUpdatePushFlagAllTrue,
		SuccessNextStep: StepCreateStandaloneTaskStepSuccess,
		ErrorNextStep:   StepCreateStandalonePushFlagAllTrue,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneTaskStepSuccess,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepCreateStandaloneCreateCallback,
		ErrorNextStep:   StepCreateStandaloneTaskStepSuccess,
	})

	// Step-12 创建成功回调
	// 调用CsMaster的API，修改cluster状态为运行中
	// 清理node、proxy的status，置为运行中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneCreateCallback,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     callback.ProcessCreateSuccessCb,
		SuccessNextStep: StepCreateStandaloneConfigSetSentinelConf,
		ErrorNextStep:   StepCreateStandaloneCreateCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneConfigSetSentinelConf,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     config.ProcessHotSetSentinelConf,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneConfigSetSentinelConf,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为创建失败
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackCallback,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     callback.ProcessCreateErrorCb,
		SuccessNextStep: StepCreateStandaloneTaskStepError,
		ErrorNextStep:   StepCreateStandaloneRollbackCallback},

		workflow.WithMaxReentry(2, workflow.FinalStepError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneTaskStepError,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),
		SuccessNextStep: StepCreateStandaloneRollbackDeleteEndpoint,
		ErrorNextStep:   StepCreateStandaloneTaskStepError})

	// Step-Error 创建失败时，如果创建了 endpoint, 则需要删除 endpoint
	// wangbin34
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackDeleteEndpoint,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     endpoint.ProcessDeleteRwEndpoint,
		SuccessNextStep: StepCreateStandaloneRollbackDeleteAppFromXmaster,
		ErrorNextStep:   StepCreateStandaloneRollbackCallback},

		workflow.WithMaxReentry(2, workflow.FinalStepError))

	// Step-Error-02 创建失败时，调用XMaster的API，删除已经注册的application信息
	// zhangzhigang06
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackDeleteAppFromXmaster,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     xmaster.ProcessDeleteAppFromXmaster,
		SuccessNextStep: StepCreateStandaloneRollbackDeleteBLB,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteAppFromXmaster},

		workflow.WithMaxReentry(2, workflow.FinalStepError))

	// Step-Error-03 创建失败时，如果已经创建了blb，需要删除blb
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackDeleteBLB,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     blb.ProcessDelBLB,
		SuccessNextStep: StepCreateStandaloneRollbackDeleteOpMonitor,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteBLB},

		workflow.WithMaxReentry(2, workflow.FinalStepError))

	// Step-Error-04 创建失败时，如果已经创建了bns，需要删除bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackDeleteOpMonitor,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     opmonitor.ProcessDeleteOpmonitorBnsService,
		SuccessNextStep: StepCreateStandaloneRollbackRemoveFromBcmGroup,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteOpMonitor})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackRemoveFromBcmGroup,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     bcm.ProcessRollbackStandaloneAddToGroup,
		SuccessNextStep: StepCreateStandaloneRollbackDeleteBcmResource,
		ErrorNextStep:   StepCreateStandaloneRollbackRemoveFromBcmGroup})

	// Step-Error-05 创建失败时，如果已经创建了BCM Resource,则需要删除
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackDeleteBcmResource,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     bcm.ProcessRollbackBcmResourceWithinApp,
		SuccessNextStep: StepCreateStandaloneRollbackReleaseResources,
		ErrorNextStep:   StepCreateStandaloneRollbackDeleteBcmResource})

	// Step-Error-06 创建失败时，如果已经创建了资源，需要删除资源
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneRollbackReleaseResources,
		Workflow:        WorkflowCreateStandalone,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepCreateStandaloneRollbackReleaseResources},

		workflow.WithMaxReentry(2, workflow.FinalStepError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            SteoCreateStandaloneInitBlbAndEndpointInitBlb,
		Workflow:        WorkFlowCreateStandaloneInitBlbAndEndpoint,
		StepProcess:     blb.ProcessInitAppBLB,
		SuccessNextStep: SteoCreateStandaloneInitBlbAndEndpointInitEndpoint,
		ErrorNextStep:   SteoCreateStandaloneInitBlbAndEndpointInitBlb,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            SteoCreateStandaloneInitBlbAndEndpointInitEndpoint,
		Workflow:        WorkFlowCreateStandaloneInitBlbAndEndpoint,
		StepProcess:     endpoint.ProcessCreateRwEndpoint,
		SuccessNextStep: SteoCreateStandaloneInitBlbAndEndpointCreateDomain,
		ErrorNextStep:   SteoCreateStandaloneInitBlbAndEndpointInitEndpoint,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            SteoCreateStandaloneInitBlbAndEndpointCreateDomain,
		Workflow:        WorkFlowCreateStandaloneInitBlbAndEndpoint,
		StepProcess:     dns.ProcessCreateAppDomain,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   SteoCreateStandaloneInitBlbAndEndpointCreateDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitOpMonitorCreateBnsService,
		Workflow:        WorkflowCreateStandaloneInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorBnsService,
		SuccessNextStep: StepCreateStandaloneInitOpMonitorCreateBnsInstance,
		ErrorNextStep:   StepCreateStandaloneInitOpMonitorCreateBnsService,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitOpMonitorCreateBnsInstance,
		Workflow:        WorkflowCreateStandaloneInitOpMonitor,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneInitOpMonitorCreateBnsInstance,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitBcmResourceCreateResource,
		Workflow:        WorkflowCreateStandaloneInitBcmResource,
		StepProcess:     bcm.ProcessCreateBcmResourceWithinApp,
		SuccessNextStep: StepCreateStandaloneInitBcmResourceAddToBcmGroup,
		ErrorNextStep:   StepCreateStandaloneInitBcmResourceCreateResource,
	})

	// 根据用户选择添加到bcm group
	// zhangxuepeng
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCreateStandaloneInitBcmResourceAddToBcmGroup,
		Workflow:        WorkflowCreateStandaloneInitBcmResource,
		StepProcess:     bcm.ProcessStandaloneAddToGroup,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCreateStandaloneInitBcmResourceAddToBcmGroup,
	})
}

func ProcessCreateStandaloneStartWorkflowInitBlbAndEndpoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkFlowCreateStandaloneInitBlbAndEndpoint,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_blb_and_endpoint",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}})
}

func ProcessCreateStandaloneStartWorkflowsAfterApplyResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() {
		return nil
	}
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   WorkflowCreateStandaloneInitOpMonitor,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_op_monitor",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}, {
		WorkFlow:   WorkflowCreateStandaloneInitBcmResource,
		Schedule:   time.Now(),
		Mutex:      teu.Entity + "_init_bcm_resource",
		Entity:     teu.Entity,
		Parameters: teu.Parameters,
	}})
}
