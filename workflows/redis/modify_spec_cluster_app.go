/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建SCS标准版实例WORKFLOW

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"DestSpec": "cache.n1.small"
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/resize"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifySpecCluster = "scs-modify-spec-cluster-app"

	StepModifyModifySpecClusterInitTimeWindowTask           = "scs-modify-spec-cluster-init-time-window-task"
	StepModifyModifySpecClusterGlobalInit                   = "scs-modify-spec-cluster-global-init"
	StepModifySpecClusterAddStartEvent                      = "scs-modify-spec-cluster-add-start-event"
	StepModifySpecClusterBuildMeta                          = "scs-modify-spec-cluster-app-build-meta"
	StepModifySpecClusterStepTryResizeNodes                 = "scs-modify-spec-cluster-app-task-step-try-resize-nodes"
	StepModifySpecClusterFillSpec                           = "scs-modify-spec-cluster-fill-spec"
	StepModifySpecClusterUpdateOpType                       = "scs-modify-spec-cluster-update-op-type"
	StepModifySpecClusterResize                             = "scs-modify-spec-cluster-resize"
	StepModifySpecClusterTaskStepCreateNodes                = "scs-modify-spec-cluster-app-task-step-create-nodes"
	StepModifySpecClusterCheckSubnetsEnoughIps              = "scs-modify-spec-cluster-check-subnets-enough-ips"
	StepModifySpecClusterApplyResources                     = "scs-modify-spec-cluster-apply-resources"
	StepModifySpecClusterGlobalAddNodes                     = "scs-modify-spec-cluster-global-add-nodes"
	StepModifySpecClusterGlobalAfterApplyResource           = "scs-modify-spec-global-after-apply-resource"
	StepModifySpecClusterApplyResourcesCallback             = "scs-modify-spec-cluster-apply-resources-cb"
	StepModifySpecClusterTaskStepDeployNodes                = "scs-modify-spec-cluster-app-task-step-deploy-nodes"
	StepModifySpecClusterInitMachineEnv                     = "scs-modify-spec-cluster-init-machine-env"
	StepModifySpecClusterUpdateSecurityGroups               = "scs-modify-spec-cluster-update-security-groups"
	StepModifySpecClusterDeployRedis                        = "scs-modify-spec-cluster-apply-deploy-redis"
	StepModifySpecClusterTaskStepSync                       = "scs-modify-spec-cluster-app-task-step-sync"
	StepModifySpecClusterNewMasterSyncCheck                 = "scs-modify-spec-cluster-new-master-sync-check"
	StepModifySpecClusterGlobalAllNodeAddedCheck            = "scs-modify-spec-cluster-all-gnode-added-check"
	StepModifySpecClusterSyncGroupCheck                     = "scs-modify-spec-cluster-sync-group-check"
	StepModifySpecClusterDeployFilebeat                     = "scs-modify-spec-cluster-apply-deploy-filebeat"
	StepModifySpecClusterTaskStepHandover                   = "scs-modify-spec-cluster-app-task-step-handover"
	StepModifySpecClusterHandover                           = "scs-modify-spec-cluster-handover"
	StepModifySpecClusterGlobalHandoverCheck                = "scs-modify-spec-cluster-global-handover-check"
	StepModifySpecClusterPushMonitorHTGRPSlaveFlag          = "scs-modify-spec-cluster-push-htgrp-slave-flag"
	StepModifySpecClusterPushFlag                           = "scs-modify-spec-cluster-push-flag"
	StepModifySpecClusterResizeExecute                      = "scs-modify-spec-cluster-resize-exec"
	StepModifySpecClusterNewSlaveSyncCheck                  = "scs-modify-spec-cluster-new-slave-sync-check"
	StepModifySpecClusterTaskStepReleaseNodes               = "scs-modify-spec-cluster-app-task-step-release-nodes"
	StepModifySpecClusterMetaDelNodes                       = "scs-modify-spec-cluster-meta-del-nodes"
	StepModifySpecClusterDeleteOldNodes                     = "scs-modify-spec-cluster-del-old-nodes"
	StepModifySpecClusterCommitSpec                         = "scs-modify-spec-cluster-commit-spec"
	StepModifySpecClusterUpdateOpTypeCb                     = "scs-modify-spec-cluster-update-op-type-cb"
	StepModifySpecClusterUpdateAppTopologyInXmaster         = "scs-modify-spec-cluster-update-app-topo-in-xmaster"
	StepModifySpecClusterSuccessCallbackGlobal              = "scs-modify-spec-cluster-succ-cb-global"
	StepModifySpecClusterSuccessResetGlobalSg               = "scs-modify-spec-cluster-succ-reset-global-sg"
	StepModifySpecClusterInitOpMonitor                      = "scs-modify-spec-cluster-init-op-monitor"
	StepModifySpecClusterCloseTimeWindowTask                = "scs-modify-spec-cluster-close-tw-task"
	StepModifySpecClusterSuccessCallback                    = "scs-modify-spec-cluster-succ-cb"
	StepModifySpecClusterAddSuccessEndEvent                 = "scs-modify-spec-cluster-add-success-end-event"
	StepModifySpecClusterRollbackReleaseResources           = "scs-modify-spec-cluster-rollback-release-resource"
	StepModifySpecClusterTaskStepError                      = "scs-modify-spec-cluster-app-task-step-error"
	StepModifySpecClusterRollbackPushFlag                   = "scs-modify-spec-cluster-rollback-push-flag"
	StepModifySpecClusterRollbackMeta                       = "scs-modify-spec-cluster-rollback-meta"
	StepModifySpecClusterRollbackSpec                       = "scs-modify-spec-cluster-rollback-spec"
	StepModifySpecClusterUpdateOpTypeRb                     = "scs-modify-spec-cluster-update-op-type-rb"
	StepModifySpecClusterRollbackUpdateAppTopologyInXmaster = "scs-modify-spec-cluster-rollback-update-app-topo-in-xmaster"
	StepModifySpecClusterRollbackCallback                   = "scs-modify-spec-cluster-rollback-cb"
	StepModifySpecClusterAddFailedEndEvent                  = "scs-modify-spec-cluster-add-failed-end-event"
)

func init() {
	// Step1 热活注册任务
	// 主角色设置任务后继续执行
	// 从角色检查任务已经创建，未创建则一致等待
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyModifySpecClusterInitTimeWindowTask,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: timewindow.ProcessSetTaskID,

		SuccessNextStep: StepModifyModifySpecClusterGlobalInit,
		ErrorNextStep:   StepModifyModifySpecClusterInitTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyModifySpecClusterGlobalInit,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: gmaster.ProcessInitGlobalModifySpecStatus,

		SuccessNextStep: StepModifySpecClusterAddStartEvent,
		ErrorNextStep:   StepModifyModifySpecClusterGlobalInit,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterAddStartEvent,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     event.ProcessAddModifyingStartEvent,
		SuccessNextStep: StepModifySpecClusterBuildMeta,
		ErrorNextStep:   StepModifySpecClusterAddStartEvent,
	})

	// Step-1 修改x1元数据库
	// 将所有分片的cluster.DestSpec 修改为 param.NodeType
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterBuildMeta,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForModifySpec,
		SuccessNextStep: StepModifySpecClusterStepTryResizeNodes,
		ErrorNextStep:   StepModifySpecClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterStepTryResizeNodes,
		Workflow: WorkflowModifySpecCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepTryResizeNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecClusterFillSpec,
		ErrorNextStep:   StepModifySpecClusterStepTryResizeNodes,
	})

	// Step-2 填写详细配置
	// 将规格信息填入Cluster表或Interface表；
	// 若cluster.DestSpec != cluster.Spec则从x1-base的spec-conf中获取NodeType的具体规格
	// 包括cpu、mem、root/data disk、instanceType并填入x1元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterFillSpec,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepModifySpecClusterUpdateOpType,
		ErrorNextStep:   StepModifySpecClusterFillSpec,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterUpdateOpType,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     buildmeta.ProcessUpdateCsmasterOpTypeMasterChange,
		SuccessNextStep: StepModifySpecClusterResize,
		ErrorNextStep:   StepModifySpecClusterUpdateOpType,
	})

	// Step-3 热迁移
	// 对支持热迁移的实例（通过component/bcc-resource配置）xagent发送resizeVM命令，
	// 对不支持的实例，把节点状态改为ToDelete
	// 在x1元数据中插入需要新建节点的元数据，状态为ToCreate
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterResize,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     resize.ProcessResizeVm,
		SuccessNextStep: StepModifySpecClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifySpecClusterResize},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 检查子网ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterCheckSubnetsEnoughIps,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepModifySpecClusterTaskStepCreateNodes,
		ErrorNextStep:   StepModifySpecClusterCheckSubnetsEnoughIps,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterTaskStepCreateNodes,
		Workflow: WorkflowModifySpecCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecClusterApplyResources,
		ErrorNextStep:   StepModifySpecClusterTaskStepCreateNodes,
	})

	// Step-5 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterApplyResources,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepModifySpecClusterGlobalAddNodes,
		ErrorNextStep:   StepModifySpecClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterGlobalAddNodes,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodesForModifySpec,
		SuccessNextStep: StepModifySpecClusterGlobalAfterApplyResource,
		ErrorNextStep:   StepModifySpecClusterGlobalAddNodes,
	})
	// 热活同步点1
	// 申请资源&&添加global node元数据
	// 等待global node元数据都加入的原因是后面UpdateSecurityGroups会有竞争
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterGlobalAfterApplyResource,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: gmaster.ProcessCheckModifyStatusAfterApplyResource,

		SuccessNextStep: StepModifySpecClusterApplyResourcesCallback,
		ErrorNextStep:   StepModifySpecClusterGlobalAfterApplyResource,
	}, workflow.WithStepTimeout(time.Hour*24*7))

	// Step-7 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterApplyResourcesCallback,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepModifySpecClusterTaskStepDeployNodes,
		ErrorNextStep:   StepModifySpecClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterTaskStepDeployNodes,
		Workflow: WorkflowModifySpecCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecClusterInitMachineEnv,
		ErrorNextStep:   StepModifySpecClusterTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterInitMachineEnv,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifySpecClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifySpecClusterInitMachineEnv,
	})

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterUpdateSecurityGroups,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupClusterForModifySpec,
		SuccessNextStep: StepModifySpecClusterDeployRedis,
		ErrorNextStep:   StepModifySpecClusterUpdateSecurityGroups,
	})

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterDeployRedis,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepModifySpecClusterTaskStepSync,
		ErrorNextStep:   StepModifySpecClusterDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterTaskStepSync,
		Workflow: WorkflowModifySpecCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepSync, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecClusterNewMasterSyncCheck,
		ErrorNextStep:   StepModifySpecClusterTaskStepSync})

	// Step-7 同步数据
	// 将旧规格的数据同步到新规格，TopoCheckerProcessor::exchange_master_check ，同步完成后加入metaserver
	// 所有角色都是新老spec节点共存，相当于副本数变多：
	// 	   热活从角色直接返回
	//     主角色从老主同步数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterNewMasterSyncCheck,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     syncredis.ProcessSyncFromOldSpecCluster,
		SuccessNextStep: StepModifySpecClusterGlobalAllNodeAddedCheck,
		ErrorNextStep:   StepModifySpecClusterNewMasterSyncCheck},

		workflow.WithStepTimeout(15*time.Minute))

	// 热活同步点2 等待所有成员都ModifyStageApplyResource 50%->100% ，然后把stage改为ModifyStageMigration
	// 等待主角色完成数据同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterGlobalAllNodeAddedCheck,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: gmaster.ProcessCheckModifyStatusBeforeMigration,

		SuccessNextStep: StepModifySpecClusterSyncGroupCheck,
		ErrorNextStep:   StepModifySpecClusterGlobalAllNodeAddedCheck,
	})

	// Step-8 异地多活集群特定步骤
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterSyncGroupCheck,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     syncgroup.ProcessCheckModifySpec,
		SuccessNextStep: StepModifySpecClusterDeployFilebeat,
		ErrorNextStep:   StepModifySpecClusterSyncGroupCheck},

		workflow.WithStepTimeout(3*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterDeployFilebeat,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepModifySpecClusterTaskStepHandover,
		ErrorNextStep:   StepModifySpecClusterDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterTaskStepHandover,
		Workflow: WorkflowModifySpecCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepHandoverForSync, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecClusterHandover,
		ErrorNextStep:   StepModifySpecClusterTaskStepHandover})

	// Step-9 切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterHandover,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     handover.ProcessHandoverClusterToCreateMaster,
		SuccessNextStep: StepModifySpecClusterGlobalHandoverCheck,
		ErrorNextStep:   StepModifySpecClusterHandover},

		workflow.WithStepTimeout(60*time.Minute))

	// 热活同步点3 等待所有成员都ModifyStageMigration 0%->100% ，然后把stage改为ModifyStageReleaseResource
	// 从角色在这里等待，主角色完成上一步切换后在这里把所有角色的进度统一修改后继续
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterGlobalHandoverCheck,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: gmaster.ProcessModifySpecGlobalHandoverCheck,

		SuccessNextStep: StepModifySpecClusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifySpecClusterGlobalHandoverCheck,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterPushMonitorHTGRPSlaveFlag,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,

		SuccessNextStep: StepModifySpecClusterPushFlag,
		ErrorNextStep:   StepModifySpecClusterPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterPushFlag,
		Workflow: WorkflowModifySpecCluster,

		StepProcess: pushflag.ProcessUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifySpecClusterResizeExecute,
		ErrorNextStep:   StepModifySpecClusterPushFlag,
	})

	// Step-10 修改maxmemroy等
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterResizeExecute,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     resize.ProcessResizeMaxmemory,
		SuccessNextStep: StepModifySpecClusterNewSlaveSyncCheck,
		ErrorNextStep:   StepModifySpecClusterResizeExecute,
	}, workflow.WithStepTimeout(30*time.Minute))

	// 检查新从库与新主库是否同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterNewSlaveSyncCheck,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepModifySpecClusterTaskStepReleaseNodes,
		ErrorNextStep:   StepModifySpecClusterNewSlaveSyncCheck,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifySpecClusterTaskStepReleaseNodes,
		Workflow: WorkflowModifySpecCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifySpecTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifySpecClusterMetaDelNodes,
		ErrorNextStep:   StepModifySpecClusterTaskStepReleaseNodes,
	})

	// Step-11 删除旧节点Metaserver中信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterMetaDelNodes,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepModifySpecClusterDeleteOldNodes,
		ErrorNextStep:   StepModifySpecClusterMetaDelNodes,
	})

	// Step-11 删除旧节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterDeleteOldNodes,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifySpecClusterCommitSpec,
		ErrorNextStep:   StepModifySpecClusterDeleteOldNodes,
	})

	// Step-12 确认规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterCommitSpec,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     specification.ProcessCommitSpec,
		SuccessNextStep: StepModifySpecClusterUpdateOpTypeCb,
		ErrorNextStep:   StepModifySpecClusterCommitSpec,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterUpdateOpTypeCb,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     buildmeta.ProcessUpdateCsmasterOpTypeNormal,
		SuccessNextStep: StepModifySpecClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifySpecClusterUpdateOpTypeCb,
	})

	// Step-13 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifySpecClusterSuccessCallbackGlobal,
		ErrorNextStep:   StepModifySpecClusterUpdateAppTopologyInXmaster,
	})

	// 热活同步点4 等待所有成员都ModifyStageReleaseResource 0%->100% ，然后把stage改为ModifyStageComplete
	// 从角色判断同步后就退出，主角色会代劳把以所有从角色身份请求global修改stage，
	// 目的是为了把global的member状态改为normal，解锁后续任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterSuccessCallbackGlobal,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     gmaster.ProcessCheckModifySpecGlobalCB,
		SuccessNextStep: StepModifySpecClusterSuccessResetGlobalSg,
		ErrorNextStep:   StepModifySpecClusterSuccessCallbackGlobal,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterSuccessResetGlobalSg,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupResetClusterGlobalSg,
		SuccessNextStep: StepModifySpecClusterInitOpMonitor,
		ErrorNextStep:   StepModifySpecClusterSuccessResetGlobalSg,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterInitOpMonitor,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifySpecClusterCloseTimeWindowTask,
		ErrorNextStep:   StepModifySpecClusterInitOpMonitor,
	})

	// 修改定时任务状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterCloseTimeWindowTask,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepModifySpecClusterSuccessCallback,
		ErrorNextStep:   StepModifySpecClusterCloseTimeWindowTask,
	})

	// Step-14 变更成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterSuccessCallback,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: StepModifySpecClusterAddSuccessEndEvent,
		ErrorNextStep:   StepModifySpecClusterSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterAddSuccessEndEvent,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     event.ProcessAddModifyingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifySpecClusterAddSuccessEndEvent,
	})

	// Step-Error-01 变更失败时，调用CsMaster的API，修改cluster状态为配置变更失败
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackReleaseResources,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifySpecClusterTaskStepError,
		ErrorNextStep:   StepModifySpecClusterAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterTaskStepError,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifySpecClusterRollbackPushFlag,
		ErrorNextStep:   StepModifySpecClusterTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackPushFlag,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifySpecClusterRollbackMeta,
		ErrorNextStep:   StepModifySpecClusterRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackMeta,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifySpecClusterRollbackSpec,
		ErrorNextStep:   StepModifySpecClusterAddFailedEndEvent,
	})

	// Step-12 回滚规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackSpec,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     specification.ProcessRollbackSpec,
		SuccessNextStep: StepModifySpecClusterUpdateOpTypeRb,
		ErrorNextStep:   StepModifySpecClusterRollbackSpec,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterUpdateOpTypeRb,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     buildmeta.ProcessUpdateCsmasterOpTypeNormal,
		SuccessNextStep: StepModifySpecClusterRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifySpecClusterUpdateOpTypeRb,
	})

	// Step-12 回滚规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifySpecClusterRollbackCallback,
		ErrorNextStep:   StepModifySpecClusterRollbackUpdateAppTopologyInXmaster,
	})

	// Step-Error-02 比那更失败时，如果已经创建了资源，需要删除资源
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterRollbackCallback,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     callback.ProcessModifyNodesErrorCb,
		SuccessNextStep: StepModifySpecClusterAddFailedEndEvent,
		ErrorNextStep:   StepModifySpecClusterAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifySpecClusterAddFailedEndEvent,
		Workflow:        WorkflowModifySpecCluster,
		StepProcess:     event.ProcessAddModifyingFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifySpecClusterAddFailedEndEvent,
	})
}
