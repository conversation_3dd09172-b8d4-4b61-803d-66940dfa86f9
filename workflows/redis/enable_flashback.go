package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
)

const (
	WorkflowScsEnableFlashback                       = "scs-enable-flashback"
	StepEnableFlashbackOpenOpHeader                  = "scs-enable-flashback-open-op-header"
	StepEnableFlashbackReopenAppendonly              = "scs-enable-flashback-reopen-appendonly"
	StepEnableFlashbackCreateAOFPolicy               = "scs-enable-flashback-create-aof-policy"
	StepEnableFlashbackCheckRecoverableDatetimeRange = "scs-enable-flashback-check-recoverable-datetime-range"
	StepEnableFlashbackCallback                      = "scs-enable-flashback-callback"
)

func init() {
	// 开启 op-header && 开启 AOF 切割
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableFlashbackOpenOpHeader,
		Workflow:        WorkflowScsEnableFlashback,
		StepProcess:     config.ProcessOpenOpHeader,
		SuccessNextStep: StepEnableFlashbackReopenAppendonly,
		ErrorNextStep:   StepEnableFlashbackOpenOpHeader,
	})

	// 重新打开 appendonly
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableFlashbackReopenAppendonly,
		Workflow:        WorkflowScsEnableFlashback,
		StepProcess:     config.ProcessReopenAppendonly,
		SuccessNextStep: StepEnableFlashbackCreateAOFPolicy,
		ErrorNextStep:   StepEnableFlashbackReopenAppendonly,
	})

	// 注册 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableFlashbackCreateAOFPolicy,
		Workflow:        WorkflowScsEnableFlashback,
		StepProcess:     backup.ProcessCreateBackupPolicyForAOF,
		SuccessNextStep: StepEnableFlashbackCheckRecoverableDatetimeRange,
		ErrorNextStep:   StepEnableFlashbackCreateAOFPolicy,
	})

	// 检查是否可以获取到 AOF 备份时间范围
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableFlashbackCheckRecoverableDatetimeRange,
		Workflow:        WorkflowScsEnableFlashback,
		StepProcess:     backup.PorcessCheckRecoverableDatetimeRange,
		SuccessNextStep: StepEnableFlashbackCallback,
		ErrorNextStep:   StepEnableFlashbackCheckRecoverableDatetimeRange,
	})

	// callback，打开闪回开关，修改集群状态为运行中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableFlashbackCallback,
		Workflow:        WorkflowScsEnableFlashback,
		StepProcess:     callback.ProcessEnableFlashback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepEnableFlashbackCallback,
	})
}
