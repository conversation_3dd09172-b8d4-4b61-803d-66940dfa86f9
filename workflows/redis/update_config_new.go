package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
)

const (
	UpdateConfigNewWorkflow = "scs-update-config-new"

	StepUpdateConfigNewExecute = "scs-update-config-new-execute"

	UpdateAgentRecoverConfs = "scs-update-agent-recover-confs"

	StepUpdateAgentRecoverConfsExecute = "scs-update-agent-recover-confs-execute"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpdateConfigNewExecute,
		Workflow:        UpdateConfigNewWorkflow,
		StepProcess:     config.ProcessUpdateConfigNew,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpdateConfigNewExecute,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpdateAgentRecoverConfsExecute,
		Workflow:        UpdateAgentRecoverConfs,
		StepProcess:     config.ProcessRefreshAgentRecovers,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpdateAgentRecoverConfsExecute,
	})
}
