/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* flush_cluster_app.go - workflow for modify proxy count */

/*
Modification History
--------------------
2022/5/16, by <PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
进行清除数据操作（RedisState::flushing_cluster）

Parameters
{
	"app_id": "scs-bj-nxewpztnsreg",
	"target_op_proxy_infos": {
		"zone": "zoneA",
		"subnet_id": "ea8af8c9-6647-4576-89a6-312e29c44eb6",
		"count": 2
	}
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyProxiesCluster                       = "scs-modify-proxies-cluster"
	StepModifyProxiesClusterBuildMeta                  = "scs-modify-proxies-cluster-app-build-meta"
	StepModifyProxiesClusterFillSpec                   = "scs-modify-proxies-cluster-fill-spec"
	StepModifyProxiesClusterCheckSubnetsEnoughIps      = "scs-modify-proxies-cluster-check-subnets-enough-ips"
	StepModifyProxiesClusterApplyResources             = "scs-modify-proxies-cluster-apply-resources"
	StepModifyProxiesClusterApplyResourcesCallback     = "scs-modify-proxies-cluster-apply-resources-cb"
	StepModifyProxiesClusterInitMachineEnv             = "scs-modify-proxies-cluster-init-machine-env"
	StepModifyProxiesClusterAddGlobalNode              = "scs-modify-proxies-cluster-add-global-nodes"
	StepModifyProxiesClusterUpdateSecurityGroups       = "scs-modify-proxies-cluster-update-security-groups"
	StepModifyProxiesClusterMetaAddNodes               = "scs-modify-proxies-cluster-meta-add-nodes"
	StepModifyProxiesClusterDeployProxy                = "scs-modify-proxies-cluster-deploy-proxy"
	StepModifyProxiesClusterUpdateTlsConfIfNeeded      = "scs-modify-proxies-cluster-update-tls-conf-if-needed"
	StepModifyProxiesClusterMetaDelNodes               = "scs-modify-proxies-cluster-meta-del-nodes"
	StepModifyProxiesClusterSetRs                      = "scs-modify-proxies-cluster-set-rs"
	StepModifyProxiesClusterSetMcpackRs                = "scs-modify-proxies-cluster-set-mcpack-rs"
	StepModifyProxiesClusterSetBnsInstances            = "scs-modify-proxies-cluster-set-bns-instances"
	StepModifyProxiesClusterInitBcmResource            = "scs-modify-proxies-cluster-init-bcm-resource"
	StepModifyProxiesClusterAddOrRemoveFromBcmGroup    = "scs-modify-proxies-cluster-add-or-remove-from-bcm-group"
	StepModifyProxiesClusterDeleteToDeleteProxies      = "scs-modify-proxies-cluster-delete-to-delete-proxies"
	StepModifyProxiesClusterUpdateAppTopologyInXmaster = "scs-modify-proxies-cluster-update-app-topo-in-xmaster"
	StepModifyProxiesClusterInitOpMonitor              = "scs-modify-proxies-cluster-init-op-monitor"
	StepModifyProxiesClusterPushFlag                   = "scs-modify-proxies-cluster-push-flag"
	StepModifyProxiesClusterSuccessCallback            = "scs-modify-proxies-cluster-success-cb"
	StepModifyProxiesClusterRollbackReleaseResources   = "scs-modify-proxies-cluster-rollback-release-resource"
	StepModifyProxiesClusterRollbackBcmGroup           = "scs-modify-proxies-cluster-rollback-bcm-group"
	StepModifyProxiesClusterRollbackPushFlag           = "scs-modify-proxies-cluster-rollback-push-flag"
	StepModifyProxiesClusterRollbackBcmResource        = "scs-modify-proxies-cluster-rollback-bcm-resource"
	StepModifyProxiesClusterRollbackMeta               = "scs-modify-proxies-cluster-rollback-meta"
	StepModifyProxiesClusterRollbackCallback           = "scs-modify-proxies-cluster-rollback-cb"
)

func init() {
	// Step-01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterBuildMeta,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: buildmeta.ProcessBuildMetaModifyProxy,

		SuccessNextStep: StepModifyProxiesClusterFillSpec,
		ErrorNextStep:   StepModifyProxiesClusterRollbackReleaseResources,
	})

	// Step-02 添加规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterFillSpec,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepModifyProxiesClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyProxiesClusterRollbackReleaseResources,
	})

	// Step-03 检查子网数量是否足够
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterCheckSubnetsEnoughIps,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepModifyProxiesClusterApplyResources,
		ErrorNextStep:   StepModifyProxiesClusterRollbackReleaseResources,
	})

	// Step-04 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyProxiesClusterApplyResources,
		Workflow:        WorkflowModifyProxiesCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepModifyProxiesClusterApplyResourcesCallback,
		ErrorNextStep:   StepModifyProxiesClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, StepModifyProxiesClusterRollbackReleaseResources))

	// Step-05 资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterApplyResourcesCallback,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepModifyProxiesClusterInitMachineEnv,
		ErrorNextStep:   StepModifyProxiesClusterRollbackReleaseResources,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyProxiesClusterInitMachineEnv,
		Workflow:        WorkflowModifyProxiesCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifyProxiesClusterAddGlobalNode,
		ErrorNextStep:   StepModifyProxiesClusterInitMachineEnv,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyProxiesClusterAddGlobalNode,
		Workflow:        WorkflowModifyProxiesCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepModifyProxiesClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyProxiesClusterAddGlobalNode,
	})

	// Step-06 更新安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterUpdateSecurityGroups,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: securitygroup.ProcessRebuildSecurityGroupCluster,

		SuccessNextStep: StepModifyProxiesClusterMetaAddNodes,
		ErrorNextStep:   StepModifyProxiesClusterUpdateSecurityGroups,
	})

	// Step-07 Metaserver增加节点, 如果是热活实例组成员，则在global master中注册节点
	// 注册节点除了在Global Master中更新节点信息，还需要在Global MetaServer中更新节点信息，以及更新节点的安全组信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterMetaAddNodes,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: metaserver.ProcessAddNodes,

		SuccessNextStep: StepModifyProxiesClusterDeployProxy,
		ErrorNextStep:   StepModifyProxiesClusterMetaAddNodes,
	})

	// Step-08 部署Proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterDeployProxy,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: deploy.ProcessDeployAll,

		SuccessNextStep: StepModifyProxiesClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepModifyProxiesClusterDeployProxy},

		workflow.WithStepTimeout(15*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterUpdateTlsConfIfNeeded,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: util.UpdateTLSConfIfNeededForNew,

		SuccessNextStep: StepModifyProxiesClusterMetaDelNodes,
		ErrorNextStep:   StepModifyProxiesClusterUpdateTlsConfIfNeeded,
	})

	// Step-09 Metaserver删除节点, 如果是热活实例组成员，则在global master中解注册节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterMetaDelNodes,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: metaserver.ProcessDelNodes,

		SuccessNextStep: StepModifyProxiesClusterSetRs,
		ErrorNextStep:   StepModifyProxiesClusterMetaDelNodes,
	})

	// Step-10 更新BLB的Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterSetRs,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: blb.ProcessSetProxyRsForModify,

		SuccessNextStep: StepModifyProxiesClusterSetMcpackRs,
		ErrorNextStep:   StepModifyProxiesClusterSetRs,
	})

	// Step-10 更新BLB的Rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterSetMcpackRs,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: blb.ProcessSetProxyRsForMcpack,

		SuccessNextStep: StepModifyProxiesClusterSetBnsInstances,
		ErrorNextStep:   StepModifyProxiesClusterSetMcpackRs,
	})

	// Step-11 更新Bns Service Instance绑定
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterSetBnsInstances,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: bns.ProcessSetBnsInstances,

		SuccessNextStep: StepModifyProxiesClusterInitBcmResource,
		ErrorNextStep:   StepModifyProxiesClusterSetBnsInstances,
	})

	// Step-12 根据变更新建或删除监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterInitBcmResource,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: bcm.ProcessBcmResourceWithoutApp,

		SuccessNextStep: StepModifyProxiesClusterAddOrRemoveFromBcmGroup,
		ErrorNextStep:   StepModifyProxiesClusterInitBcmResource,
	})

	// 从bcm group中移除proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterAddOrRemoveFromBcmGroup,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: bcm.ProcessProxyAddOrRemoveFromBcmGroup,

		SuccessNextStep: StepModifyProxiesClusterPushFlag,
		ErrorNextStep:   StepModifyProxiesClusterAddOrRemoveFromBcmGroup,
	})

	// 删除需要删除的Proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterPushFlag,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: pushflag.ProcessUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifyProxiesClusterDeleteToDeleteProxies,
		ErrorNextStep:   StepModifyProxiesClusterPushFlag,
	})

	// Step-12 删除需要删除的Proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterDeleteToDeleteProxies,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: delresource.ProcessDeleteToDeleteNodes,

		SuccessNextStep: StepModifyProxiesClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyProxiesClusterDeleteToDeleteProxies,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterUpdateAppTopologyInXmaster,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: xmaster.ProcessUpdateAppTopologyInXmaster,

		SuccessNextStep: StepModifyProxiesClusterInitOpMonitor,
		ErrorNextStep:   StepModifyProxiesClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyProxiesClusterInitOpMonitor,
		Workflow:        WorkflowModifyProxiesCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyProxiesClusterSuccessCallback,
		ErrorNextStep:   StepModifyProxiesClusterInitOpMonitor,
	})

	// Step-12 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterSuccessCallback,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: callback.ProcessModifyProxySuccess,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyProxiesClusterSuccessCallback,
	})

	// Step-Error-01 回滚所有申请的资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterRollbackReleaseResources,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: StepModifyProxiesClusterRollbackBcmGroup,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyProxiesClusterRollbackBcmGroup,
		Workflow:        WorkflowModifyProxiesCluster,
		StepProcess:     bcm.ProcessRollbackProxyAddOrRemoveFromBcmGroup,
		SuccessNextStep: StepModifyProxiesClusterRollbackBcmResource,
		ErrorNextStep:   StepModifyProxiesClusterRollbackBcmGroup,
	})

	// Step-Error-02 回滚监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterRollbackBcmResource,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: bcm.ProcessRollbackBcmResourceWithoutApp,

		SuccessNextStep: StepModifyProxiesClusterRollbackPushFlag,
		ErrorNextStep:   StepModifyProxiesClusterRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyProxiesClusterRollbackPushFlag,
		Workflow:        WorkflowModifyProxiesCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyProxiesClusterRollbackMeta,
		ErrorNextStep:   StepModifyProxiesClusterRollbackPushFlag,
	})

	// Step-Error-03 回滚元数据更改
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterRollbackMeta,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: delresource.ProcessRollbackMeta,

		SuccessNextStep: StepModifyProxiesClusterRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-Error-04 删除资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyProxiesClusterRollbackCallback,
		Workflow: WorkflowModifyProxiesCluster,

		StepProcess: callback.ProcessModifyNodesErrorCb,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
