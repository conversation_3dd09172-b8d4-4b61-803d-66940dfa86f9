/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/13 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_create_standalone_group.go
 * <AUTHOR>
 * @date 2022/07/13 16:26:36
 * @brief task create local standalone group
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
)

// WorkFlowLocalCreateStandaloneGroup will create standalone group
const (
	WorkFlowLocalCreateStandaloneGroup                     = "scs-local-create-standalone-group"
	WorkFlowLocalCreateStandaloneGroupStepDisableFlashback = "scs-local-create-standalone-group-disable-flashback"
	WorkFlowLocalCreateStandaloneGroupStepBuildMeta        = "scs-local-create-standalone-group-build-meta"
	WorkFlowLocalCreateStandaloneGroupStepRegisterNodes    = "scs-local-create-standalone-group-register-nodes"
	WorkFlowLocalCreateStandaloneGroupStepSuccessCb        = "scs-local-create-standalone-group-success-cb"
)

func init() {
	// 关闭闪回功能
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateStandaloneGroupStepDisableFlashback,
		Workflow:        WorkFlowLocalCreateStandaloneGroup,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOFAndCloseOpheader,
		SuccessNextStep: WorkFlowLocalCreateStandaloneGroupStepBuildMeta,
		ErrorNextStep:   WorkFlowLocalCreateStandaloneGroupStepDisableFlashback,
	})
	// ① 查询global master，获取shard信息，将GlobalShardID写入数据库
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateStandaloneGroupStepBuildMeta,
		Workflow:        WorkFlowLocalCreateStandaloneGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForCreateStandaloneGroup,
		SuccessNextStep: WorkFlowLocalCreateStandaloneGroupStepRegisterNodes,
		ErrorNextStep:   WorkFlowLocalCreateStandaloneGroupStepBuildMeta,
	})
	// ② 调用global-master-api，将redis信息注册至global master
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateStandaloneGroupStepRegisterNodes,
		Workflow:        WorkFlowLocalCreateStandaloneGroup,
		StepProcess:     gmaster.ProcessRegisterStandaloneNodesToGlobalMasterAsLeader,
		SuccessNextStep: WorkFlowLocalCreateStandaloneGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalCreateStandaloneGroupStepRegisterNodes,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateStandaloneGroupStepSuccessCb,
		Workflow:        WorkFlowLocalCreateStandaloneGroup,
		StepProcess:     callback.ProcessGlobalGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalCreateStandaloneGroupStepSuccessCb,
	})
}
