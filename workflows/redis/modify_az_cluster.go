/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */

/*
DESCRIPTION
SCS变更可用区WorkFlow
Parameters

	{
		"AppID": "scs-bj-nxewpztnsreg",
		"Replicas": [{
			"Zone": "zoneA",
			"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
			"Role": "master",
			"Count": 1
		}, {
			"Zone": "zoneB",
			"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
			"Role": "slave",
			"Count": 1
		}],
		"DestReplicas": [{
			"Zone": "zoneA",
			"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
			"Role": "master",
			"Count": 1
		}, {
			"Zone": "zoneB",
			"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
			"Role": "slave",
			"Count": 1
		}]
	}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyAZCluster = "scs-modify-az-cluster-app"

	StepModifyModifyAZClusterInitTimeWindowTask           = "scs-modify-az-cluster-init-time-window-task"
	StepModifyAZClusterAddStartEvent                      = "scs-modify-az-cluster-add-start-event"
	StepModifyAZClusterBuildMeta                          = "scs-modify-az-cluster-app-build-meta"
	StepModifyAZClusterCheckSubnetsEnoughIps              = "scs-modify-az-cluster-check-subnets-enough-ips"
	StepModifyAZClusterTaskStepCreateNodes                = "scs-modify-az-cluster-app-task-step-create-nodes"
	StepModifyAZClusterApplyResources                     = "scs-modify-az-cluster-apply-resources"
	StepModifyAZClusterApplyResourcesCallback             = "scs-modify-az-cluster-apply-resources-cb"
	StepModifyAZClusterTaskStepDeployNodes                = "scs-modify-az-cluster-app-task-step-deploy-nodes"
	StepModifyAZClusterInitMachineEnv                     = "scs-modify-az-cluster-init-machine-env"
	StepModifyAZClusterUpdateSecurityGroups               = "scs-modify-az-cluster-update-security-groups"
	StepModifyAZClusterMetaAddNodes                       = "scs-modify-az-cluster-meta-add-nodes"
	StepModifyAZClusterDeployRedis                        = "scs-modify-az-cluster-apply-deploy-redis"
	StepModifyAZClusterDeployFilebeat                     = "scs-modify-az-cluster-apply-deploy-filebeat"
	StepModifyAZClusterTaskStepSync                       = "scs-modify-az-cluster-app-task-step-sync"
	StepModifyAZClusterUpdateTlsConfIfNeeded              = "scs-modify-az-cluster-update-tls-conf-if-needed"
	StepModifyAZClusterSetLocalSlaveOf                    = "scs-modify-az-cluster-set-local-slave-of"
	StepModifyAZClusterCheckAllSync                       = "scs-modify-az-cluster-check-all-sync"
	StepModifyAZClusterTaskStepSetSwitchTags              = "scs-modify-az-cluster-app-task-step-set-switch-tags"
	StepModifyAZClusterPushFlag                           = "scs-modify-az-cluster-push-flag"
	StepModifyAZClusterTaskStepHandover                   = "scs-modify-az-cluster-app-task-step-handover"
	StepModifyAZClusterHandoverToNewAZ                    = "scs-modify-az-cluster-handover-to-new-az"
	StepModifyAZClusterCorrectMasterSlaveInfo             = "scs-modify-az-cluster-correct-master-slave-info"
	StepModifyAZClusterSetRs                              = "scs-modify-az-cluster-set-rs"
	StepModifyAZClusterSetMcpackRs                        = "scs-modify-az-cluster-set-mcpack-rs"
	StepModifyAZClusterSetBnsInstances                    = "scs-modify-az-cluster-set-bns-instances"
	StepModifyAZClusterInitBcmResource                    = "scs-modify-az-cluster-init-bcm-resource"
	StepModifyAZClusterMetaDelNodes                       = "scs-modify-az-cluster-meta-del-nodes"
	StepModifyAZClusterDeleteOldNodes                     = "scs-modify-az-cluster-del-old-nodes"
	StepModifyAZClusterUpdateAppTopologyInXmaster         = "scs-modify-az-cluster-update-app-topo-in-xmaster"
	StepModifyAZClusterInitOpMonitor                      = "scs-modify-az-cluster-init-op-monitor"
	StepModifyAZClusterCloseTimeWindowTask                = "scs-modify-az-cluster-close-tw-task"
	StepModifyAZClusterTaskStepSuccess                    = "scs-modify-az-cluster-app-task-step-success"
	StepModifyAZClusterTaskStepReSetSwitchTags            = "scs-modify-az-cluster-app-task-step-reset-switch-tags"
	StepModifyAZClusterSuccessCallback                    = "scs-modify-az-cluster-succ-cb"
	StepModifyAZClusterAddSuccessEndEvent                 = "scs-modify-az-cluster-add-success-end-event"
	StepModifyAZClusterRollbackReleaseResources           = "scs-modify-az-cluster-rollback-release-resource"
	StepModifyAZClusterTaskStepError                      = "scs-modify-az-cluster-app-task-step-error"
	StepModifyAZClusterRollbackPushFlag                   = "scs-modify-az-cluster-rollback-push-flag"
	StepModifyAZClusterRollbackMeta                       = "scs-modify-az-cluster-rollback-meta"
	StepModifyAZClusterRollbackUpdateAppTopologyInXmaster = "scs-modify-az-cluster-rollback-update-app-topo-in-xmaster"
	StepModifyAZClusterRollbackCallback                   = "scs-modify-az-cluster-rollback-cb"
	StepModifyAZClusterAddFailedEndEvent                  = "scs-modify-az-cluster-add-failed-end-event"
)

func init() {

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyModifyAZClusterInitTimeWindowTask,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepModifyAZClusterAddStartEvent,
		ErrorNextStep:   StepModifyModifyAZClusterInitTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterAddStartEvent,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     event.ProcessAddModifyingAZStartEvent,
		SuccessNextStep: StepModifyAZClusterBuildMeta,
		ErrorNextStep:   StepModifyAZClusterAddStartEvent,
	})

	// 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterBuildMeta,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForModifyAZ,
		SuccessNextStep: StepModifyAZClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyAZClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterCheckSubnetsEnoughIps,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepModifyAZClusterTaskStepCreateNodes,
		ErrorNextStep:   StepModifyAZClusterCheckSubnetsEnoughIps,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZClusterTaskStepCreateNodes,
		Workflow: WorkflowModifyAZCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZClusterApplyResources,
		ErrorNextStep:   StepModifyAZClusterTaskStepCreateNodes,
	})

	// Step-5 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterApplyResources,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepModifyAZClusterApplyResourcesCallback,
		ErrorNextStep:   StepModifyAZClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-7 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterApplyResourcesCallback,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     callback.ProcessApplyResourceCallbackForModifyAZ,
		SuccessNextStep: StepModifyAZClusterTaskStepDeployNodes,
		ErrorNextStep:   StepModifyAZClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZClusterTaskStepDeployNodes,
		Workflow: WorkflowModifyAZCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZClusterInitMachineEnv,
		ErrorNextStep:   StepModifyAZClusterTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterInitMachineEnv,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifyAZClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyAZClusterInitMachineEnv,
	})

	// Step-6 更新安全组规则
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterUpdateSecurityGroups,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: StepModifyAZClusterMetaAddNodes,
		ErrorNextStep:   StepModifyAZClusterUpdateSecurityGroups,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterMetaAddNodes,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     metaserver.ProcessAddNodes,
		SuccessNextStep: StepModifyAZClusterDeployRedis,
		ErrorNextStep:   StepModifyAZClusterMetaAddNodes,
	})

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterDeployRedis,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepModifyAZClusterTaskStepSync,
		ErrorNextStep:   StepModifyAZClusterDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZClusterTaskStepSync,
		Workflow: WorkflowModifyAZCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepSync, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZClusterDeployFilebeat,
		ErrorNextStep:   StepModifyAZClusterTaskStepSync})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterDeployFilebeat,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepModifyAZClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepModifyAZClusterDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterUpdateTlsConfIfNeeded,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     util.UpdateTLSConfIfNeededForNew,
		SuccessNextStep: StepModifyAZClusterSetLocalSlaveOf,
		ErrorNextStep:   StepModifyAZClusterUpdateTlsConfIfNeeded,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterSetLocalSlaveOf,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     handover.ProcessModifyAZLocalSlaveOf,
		SuccessNextStep: StepModifyAZClusterCheckAllSync,
		ErrorNextStep:   StepModifyAZClusterSetLocalSlaveOf,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterCheckAllSync,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepModifyAZClusterTaskStepSetSwitchTags,
		ErrorNextStep:   StepModifyAZClusterCheckAllSync,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterTaskStepSetSwitchTags,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     handover.ProcessHandoverSetTags,
		SuccessNextStep: StepModifyAZClusterPushFlag,
		ErrorNextStep:   StepModifyAZClusterTaskStepSetSwitchTags,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterPushFlag,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyAZClusterTaskStepHandover,
		ErrorNextStep:   StepModifyAZClusterPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAZClusterTaskStepHandover,
		Workflow: WorkflowModifyAZCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyAZTask, timewindow.StepHandover, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAZClusterHandoverToNewAZ,
		ErrorNextStep:   StepModifyAZClusterTaskStepHandover})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterHandoverToNewAZ,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     handover.ProcessClusterHandoverForModifyAZ,
		SuccessNextStep: StepModifyAZClusterCorrectMasterSlaveInfo,
		ErrorNextStep:   StepModifyAZClusterHandoverToNewAZ,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterCorrectMasterSlaveInfo,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     callback.ModifyInstanceMasterSlaveInfo,
		SuccessNextStep: StepModifyAZClusterSetRs,
		ErrorNextStep:   StepModifyAZClusterCorrectMasterSlaveInfo,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterSetRs,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepModifyAZClusterSetMcpackRs,
		ErrorNextStep:   StepModifyAZClusterSetRs,
	})

	// Step-10 绑定BLB的rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterSetMcpackRs,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepModifyAZClusterSetBnsInstances,
		ErrorNextStep:   StepModifyAZClusterSetMcpackRs,
	})

	// Step-11 绑定bns instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterSetBnsInstances,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: StepModifyAZClusterInitBcmResource,
		ErrorNextStep:   StepModifyAZClusterSetBnsInstances,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterInitBcmResource,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepModifyAZClusterMetaDelNodes,
		ErrorNextStep:   StepModifyAZClusterInitBcmResource,
	})

	// Step-11 删除旧节点Metaserver中信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterMetaDelNodes,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepModifyAZClusterDeleteOldNodes,
		ErrorNextStep:   StepModifyAZClusterMetaDelNodes,
	})

	// Step-11 删除旧节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterDeleteOldNodes,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyAZClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyAZClusterDeleteOldNodes,
	})

	// Step-13 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyAZClusterInitOpMonitor,
		ErrorNextStep:   StepModifyAZClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterInitOpMonitor,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyAZClusterCloseTimeWindowTask,
		ErrorNextStep:   StepModifyAZClusterInitOpMonitor,
	})

	// 修改定时任务状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterCloseTimeWindowTask,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepModifyAZClusterTaskStepSuccess,
		ErrorNextStep:   StepModifyAZClusterCloseTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterTaskStepSuccess,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepModifyAZClusterTaskStepReSetSwitchTags,
		ErrorNextStep:   StepModifyAZClusterTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterTaskStepReSetSwitchTags,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     handover.ProcessHandoverReSetTags,
		SuccessNextStep: StepModifyAZClusterSuccessCallback,
		ErrorNextStep:   StepModifyAZClusterTaskStepReSetSwitchTags,
	})

	// Step-14 变更成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterSuccessCallback,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     callback.ProcessModifyAZSuccessCb,
		SuccessNextStep: StepModifyAZClusterAddSuccessEndEvent,
		ErrorNextStep:   StepModifyAZClusterSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterAddSuccessEndEvent,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     event.ProcessAddModifyingAZSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyAZClusterAddSuccessEndEvent,
	})

	// Step-Error-01 变更失败时，调用CsMaster的API，修改cluster状态为配置变更失败
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterRollbackReleaseResources,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifyAZClusterTaskStepError,
		ErrorNextStep:   StepModifyAZClusterAddFailedEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterTaskStepError,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifyAZClusterRollbackPushFlag,
		ErrorNextStep:   StepModifyAZClusterTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterRollbackPushFlag,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyAZClusterRollbackMeta,
		ErrorNextStep:   StepModifyAZClusterRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterRollbackMeta,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifyAZClusterRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyAZClusterAddFailedEndEvent,
	})

	// Step-12 回滚规格
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyAZClusterRollbackCallback,
		ErrorNextStep:   StepModifyAZClusterRollbackUpdateAppTopologyInXmaster,
	})

	// Step-Error-02 比那更失败时，如果已经创建了资源，需要删除资源
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterRollbackCallback,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     callback.ProcessModifyAZErrorCb,
		SuccessNextStep: StepModifyAZClusterAddFailedEndEvent,
		ErrorNextStep:   StepModifyAZClusterRollbackCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAZClusterAddFailedEndEvent,
		Workflow:        WorkflowModifyAZCluster,
		StepProcess:     event.ProcessAddModifyingAZFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyAZClusterAddFailedEndEvent,
	})
}
