/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/13 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_quit_standalone_group.go
 * <AUTHOR>
 * @date 2022/07/13 16:51:15
 * @brief local cluster 退出标准版热活实例组
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalQuitStandaloneGroup definition
const (
	WorkFlowLocalQuitStandaloneGroup                               = "scs-global-quit-standalone-group"
	WorkFlowLocalQuitStandaloneGroupStepBlockMonitorInXmaster      = "scs-global-quit-standalone-group-block-monitor-in-xmaster"
	WorkFlowLocalQuitStandaloneGroupStepBuildMeta                  = "scs-global-quit-standalone-group-buildmeta"
	WorkFlowLocalQuitStandaloneGroupStepLogout                     = "scs-global-quit-standalone-group-logout"
	WorkFlowLocalQuitStandaloneGroupStepSelectMaster               = "scs-global-quit-standalone-group-select-master"
	WorkFlowLocalQuitStandaloneGroupStepUpdateTopology             = "scs-global-quit-standalone-group-update-topology"
	WorkFlowLocalQuitStandaloneGroupStepUpdateSg                   = "scs-global-quit-standalone-group-update-sg"
	WorkFlowLocalQuitStandaloneGroupStepBindRs                     = "scs-global-quit-standalone-group-bind-rs"
	WorkFlowLocalQuitStandaloneGroupStepPushMonitorHTGRPSlaveFlag  = "scs-global-quit-standalone-group-push-htgrp-slave-flag"
	WorkFlowLocalQuitStandaloneGroupCbNodeRole                     = "scs-global-quit-standalone-group-cb-node-role"
	WorkFlowLocalQuitStandaloneGroupStepUpdateAppTopologyInXmaster = "scs-global-quit-standalone-group-update-app-topo-in-xmaster"
	WorkFlowLocalQuitStandaloneGroupStepUnblockMonitorInXmaster    = "scs-global-quit-standalone-group-unblock-monitor-in-xmaster"
	WorkFlowLocalQuitStandaloneGroupStepSuccessCb                  = "scs-global-quit-standalone-group-success-cb"
	WorkFlowLocalQuitStandaloneGroupStepBindEntranceRs             = "scs-global-quit-standalone-group-bind-entrance-rs"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepBuildMeta,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepBlockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepBuildMeta,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForFollowerQuitGlobalGroup,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepLogout,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepBuildMeta,
	})

	// 调用global-master-api，将redis、proxy从global master解注册
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepLogout,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     metaserver.ProcessDelGlobalNodes,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepSelectMaster,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepLogout,
	})

	// 选新主
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepSelectMaster,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     metaserver.ProcessSelectNewMasterNode,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepUpdateTopology,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepSelectMaster,
	})

	// 重建本地topo
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepUpdateTopology,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     metaserver.ProcessResetLocalTopology,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepUpdateSg,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepUpdateTopology,
	})

	// 更新SecurityGroup，保证本地域redis在白名单ip中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepUpdateSg,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandaloneLocal,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepUpdateSg,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAllToNo,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupCbNodeRole,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupCbNodeRole,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     callback.ProcessCbNodeRolesQuit,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupCbNodeRole,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepUnblockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepSuccessCb,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     callback.ProcessGlobalGeneralQuitSuccessCb,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepBindRs,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepSuccessCb,
	})
	// 更新blb的绑定rs 需要保证app.GroupId已被清理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepBindRs,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     blb.ProcessUpdateStandaloneRs,
		SuccessNextStep: WorkFlowLocalQuitStandaloneGroupStepBindEntranceRs,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepBindRs,
	})

	// 更新entrance blb的绑定rs 需要保证app.GroupId已被清理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitStandaloneGroupStepBindEntranceRs,
		Workflow:        WorkFlowLocalQuitStandaloneGroup,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalQuitStandaloneGroupStepBindEntranceRs,
	})
}
