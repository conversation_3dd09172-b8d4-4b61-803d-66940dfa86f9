/**
* @Copyright 2023 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2023/8/28 11:35
**/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
)

const (
	WorkflowClusterFailoverLogicInspection            = "scs-cluster-failover-logic-inspection"
	StepClusterFailoverLogicInspectionPreCheck        = "scs-cluster-failover-logic-inspection-pre-check"
	StepClusterFailoverLogicInspectionSuccessCallback = "scs-cluster-failover-logic-inspection-succ-cb"
)

const (
	WorkflowStandaloneFailoverLogicInspection            = "scs-standalone-failover-logic-inspection"
	StepStandaloneFailoverLogicInspectionPreCheck        = "scs-standalone-failover-logic-inspection-pre-check"
	StepStandaloneFailoverLogicInspectionSuccessCallback = "scs-standalone-failover-logic-inspection-succ-cb"
)

const (
	WorkflowClusterSelfHealingLogicInspection            = "scs-cluster-self-healing-logic-inspection"
	StepClusterSelfHealingLogicInspectionPreCheck        = "scs-cluster-self-healing-logic-inspection-pre-check"
	StepClusterSelfHealingLogicInspectionSuccessCallback = "scs-cluster-self-healing-logic-inspection-succ-cb"
)

const (
	WorkflowStandaloneSelfHealingLogicInspection            = "scs-standalone-self-healing-logic-inspection"
	StepStandaloneSelfHealingLogicInspectionPreCheck        = "scs-standalone-self-healing-logic-inspection-pre-check"
	StepStandaloneSelfHealingLogicInspectionSuccessCallback = "scs-standalone-self-healing-logic-inspection-succ-cb"
)

func init() {

	// 逻辑故障场景中的切换任务流，网络、机器正常，服务存活，但无法提供正常工作，如pega持续不可写
	// 前置检查，检查通过则触发例行切换工作流
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepClusterFailoverLogicInspectionPreCheck,
		Workflow:        WorkflowClusterFailoverLogicInspection,
		StepProcess:     handover.ProcessPreCheckForLogicInspectionFailover,
		SuccessNextStep: StepClusterFailoverLogicInspectionSuccessCallback,
		ErrorNextStep:   StepClusterFailoverLogicInspectionPreCheck,
	})

	// 成功回调，检查子任务是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepClusterFailoverLogicInspectionSuccessCallback,
		Workflow:        WorkflowClusterFailoverLogicInspection,
		StepProcess:     handover.ProcessLogicInspectionFailoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepClusterFailoverLogicInspectionSuccessCallback,
	})

	// 逻辑故障场景中的切换任务流，网络、机器正常，服务存活，但无法提供正常工作，如pega持续不可写
	// 前置检查，检查通过则触发例行切换工作流
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverLogicInspectionPreCheck,
		Workflow:        WorkflowStandaloneFailoverLogicInspection,
		StepProcess:     handover.ProcessPreCheckForLogicInspectionFailover,
		SuccessNextStep: StepStandaloneFailoverLogicInspectionSuccessCallback,
		ErrorNextStep:   StepStandaloneFailoverLogicInspectionPreCheck,
	})

	// 成功回调，检查子任务是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneFailoverLogicInspectionSuccessCallback,
		Workflow:        WorkflowStandaloneFailoverLogicInspection,
		StepProcess:     handover.ProcessLogicInspectionFailoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepStandaloneFailoverLogicInspectionSuccessCallback,
	})

	// 逻辑故障场景中的 替换任务流，网络、机器正常，服务存活，但无法提供正常工作，如pega持续不可写
	// 前置检查，检查通过则触发 节点迁移工作流
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepClusterSelfHealingLogicInspectionPreCheck,
		Workflow:        WorkflowClusterSelfHealingLogicInspection,
		StepProcess:     handover.ProcessPreCheckForSelfHealingLogicInspection,
		SuccessNextStep: StepClusterSelfHealingLogicInspectionSuccessCallback,
		ErrorNextStep:   StepClusterSelfHealingLogicInspectionPreCheck,
	})

	// 成功回调，检查子任务是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepClusterSelfHealingLogicInspectionSuccessCallback,
		Workflow:        WorkflowClusterSelfHealingLogicInspection,
		StepProcess:     handover.ProcessLogicInspectionFailoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepClusterSelfHealingLogicInspectionSuccessCallback,
	})

	// 逻辑故障场景中的 替换任务流，网络、机器正常，服务存活，但无法提供正常工作，如pega持续不可写
	// 前置检查，检查通过则触发 节点迁移工作流
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneSelfHealingLogicInspectionPreCheck,
		Workflow:        WorkflowStandaloneSelfHealingLogicInspection,
		StepProcess:     handover.ProcessPreCheckForSelfHealingLogicInspection,
		SuccessNextStep: StepStandaloneSelfHealingLogicInspectionSuccessCallback,
		ErrorNextStep:   StepStandaloneSelfHealingLogicInspectionPreCheck,
	})

	// 成功回调，检查子任务是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepStandaloneSelfHealingLogicInspectionSuccessCallback,
		Workflow:        WorkflowStandaloneSelfHealingLogicInspection,
		StepProcess:     handover.ProcessLogicInspectionFailoverSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepStandaloneSelfHealingLogicInspectionSuccessCallback,
	})

}
