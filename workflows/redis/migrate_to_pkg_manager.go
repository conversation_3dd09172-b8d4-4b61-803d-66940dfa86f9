/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* migrate_to_pkg_manager.go */
/*
modification history
--------------------
2023/04/18 , by <PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
包管理存量迁移工具
设计文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/Q1YcygdZG_Zpx-
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/migratetopkg"
)

const (
	WorkflowMigrateToPkg        = "scs-workflow-migratetopkg"
	StepMigrateToPkgBeforeCheck = "scs-workflow-migratetopkg-step-beforecheck"
	StepMigrateToPkgExecute     = "scs-workflow-migratetopkg-step-execute"
	StepMigrateToPkgCallback    = "scs-workflow-migratetopkg-step-callback"
)

func init() {
	// Step-1 前置检查与元数据准备
	// * 获取坑内 bin md5，检查发版记录内是否存在这个版本
	// * 检查集群是否已经迁移过
	// * 检查集群状态是否存活
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToPkgBeforeCheck,
		Workflow:        WorkflowMigrateToPkg,
		StepProcess:     migratetopkg.ProcessMigrateToPkgBeforeCheck,
		SuccessNextStep: StepMigrateToPkgExecute,
		ErrorNextStep:   StepMigrateToPkgBeforeCheck,
	})
	// Step-2 开始操作坑内资源
	// * 检查集群python环境是否符合要求
	//   - 只缺失包的话则将包进行安装
	// 逐个节点（从、主、proxy）升级各个包
	// 		* 升级xagent脚本到最新版
	// 		* 除了bin以外的包用包管理升级流程，升级到托管的最新版
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToPkgExecute,
		Workflow:        WorkflowMigrateToPkg,
		StepProcess:     migratetopkg.ProcessMigrateToPkgExcute,
		SuccessNextStep: StepMigrateToPkgCallback,
		ErrorNextStep:   StepMigrateToPkgExecute,
	})
	// Step-4
	// 维护元数据 & cb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToPkgCallback,
		Workflow:        WorkflowMigrateToPkg,
		StepProcess:     migratetopkg.ProcessMigrateToPkgSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateToPkgCallback,
	})
}
