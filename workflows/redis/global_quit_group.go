/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_quit_group.go */
/*
modification history
--------------------
2022/05/24 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"time"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkFlowLocalQuitGroup                                = "scs-global-quit-group"
	WorkFlowLocalQuitGroup_StepBlockMonitorInXmaster      = "scs-global-quit-group-block-monitor-in-xmaster"
	WorkFlowLocalQuitGroup_StepBuildMeta                  = "scs-global-quit-group-buildmeta"
	WorkFlowLocalQuitGroup_StepLogout                     = "scs-global-quit-group-logout"
	WorkFlowLocalQuitGroup_StepUpdateSlot                 = "scs-global-quit-group-update-slot"
	WorkFlowLocalQuitGroup_StepRegisterMetaserver         = "scs-global-quit-group-register-metaserver"
	WorkFlowLocalQuitGroup_CbMetaserver                   = "scs-global-quit-group-cb-metaserver"
	WorkFlowLocalQuitGroup_StepPushMonitorHTGRPSlaveFlag  = "scs-global-quit-group-push-htgrp-slave-flag"
	WorkFlowLocalQuitGroup_CbNodeRole                     = "scs-global-quit-group-cb-node-role"
	WorkFlowLocalQuitGroup_StepUpdateTopology             = "scs-global-quit-group-update-topology"
	WorkFlowLocalQuitGroup_StepUpdateSg                   = "scs-global-quit-group-update-sg"
	WorkFlowLocalQuitGroup_StepRestartProxy               = "scs-global-quit-group-restart-proxy"
	WorkFlowLocalQuitGroup_UpdateTlsConfIfNeeded          = "scs-global-quit-group-update-tls-conf-if-needed"
	WorkFlowLocalQuitGroup_StepCleanMetaserver            = "scs-global-quit-group-clean-metaserver"
	WorkFlowLocalQuitGroup_StepUpdateAppTopologyInXmaster = "scs-global-quit-group-update-app-topo-in-xmaster"
	WorkFlowLocalQuitGroup_StepUnblockMonitorInXmaster    = "scs-global-quit-group-unblock-monitor-in-xmaster"
	WorkFlowLocalQuitGroup_StepSuccessCb                  = "scs-global-quit-group-success-cb"
)

func init() {
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepBuildMeta,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepBlockMonitorInXmaster,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepBuildMeta,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForFollowerQuitGlobalGroup,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepLogout,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepBuildMeta,
	})
	// ① 调用global-master-api，将redis、proxy从global master解注册
	// 抄作业改成global解注册
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepLogout,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     metaserver.ProcessDelGlobalNodes,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepUpdateSlot,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepLogout,
	})
	// ② 初始化地域metaserver，更新slot分布与global-metaserver中一致
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepUpdateSlot,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     metaserver.ProcessRebuildLocalMetaserver,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepRegisterMetaserver,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepUpdateSlot,
	},
		workflow.WithStepTimeout(30*time.Minute),
	)
	// ③ 将节点信息注册至地域metaserver(如果地域没有主，则任选一个为主)
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepRegisterMetaserver,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     metaserver.ProcessForceAddNodesLocal,
		SuccessNextStep: WorkFlowLocalQuitGroup_CbMetaserver,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepRegisterMetaserver,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_CbMetaserver,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     callback.ProcessCbLocalMetaserverEntrance,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalQuitGroup_CbMetaserver,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAllToNo,
		SuccessNextStep: WorkFlowLocalQuitGroup_CbNodeRole,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepPushMonitorHTGRPSlaveFlag,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_CbNodeRole,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     callback.ProcessCbNodeRolesQuit,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepUpdateTopology,
		ErrorNextStep:   WorkFlowLocalQuitGroup_CbNodeRole,
	})
	// ④ 更新redis拓扑与地域metaserver一致，更新数据库元数据
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepUpdateTopology,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     metaserver.ProcessResetLocalTopology,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepUpdateSg,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepUpdateTopology,
	})
	// ⑤ 更新InnerSecurityGroup，保证本地域redis、proxy在白名单ip中
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepUpdateSg,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupClusterLocal,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepRestartProxy,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepUpdateSg,
	})
	// ⑥ 分批次重启Proxy
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepRestartProxy,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     restart.ProcessUpgradeProxies,
		SuccessNextStep: WorkFlowLocalQuitGroup_UpdateTlsConfIfNeeded,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepRestartProxy,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_UpdateTlsConfIfNeeded,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     util.UpdateTLSConfIfNeeded,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalQuitGroup_UpdateTlsConfIfNeeded,
	})

	// ⑦ 删除原Metaserver中信息
	// workflow.AddStep(&workflow.AddStepParam{
	//	Name:            WorkFlowLocalQuitGroup_StepCleanMetaserver,
	//	Workflow:        WorkFlowLocalQuitGroup,
	//	StepProcess:     metaserver.ProcessDeleteClusterGlobal,
	//	SuccessNextStep: WorkFlowLocalQuitGroup_StepSuccessCb,
	//	ErrorNextStep:   WorkFlowLocalQuitGroup_StepCleanMetaserver,
	// })

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepUpdateAppTopologyInXmaster,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalQuitGroup_StepSuccessCb,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepUnblockMonitorInXmaster,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalQuitGroup_StepSuccessCb,
		Workflow:        WorkFlowLocalQuitGroup,
		StepProcess:     callback.ProcessGlobalGeneralQuitSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalQuitGroup_StepSuccessCb,
	})
}
