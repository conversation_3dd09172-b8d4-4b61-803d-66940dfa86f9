/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
执行节点升级
这里不参考原有代码，原有代码逻辑过于奇葩；
需要进行单独的详细设计，TODO 详细设计文档
// yuning

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"UpgradeParam":  ..
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/readonlygroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowUpgradeStandalone                       = "scs-upgrade-standalone-app"
	StepUpgradeStandaloneInitTimeWindowTask         = "scs-upgrade-standalone-init-tw-task"
	StepUpgradeStandaloneBuildMeta                  = "scs-upgrade-standalone-app-build-meta"
	StepUpgradeStandaloneTaskStepUpgradeFollowers   = "scs-upgrade-standalone-task-step-upgrade-followers"
	StepUpgradeStandaloneSlavesUpgrade              = "scs-upgrade-standalone-slaves-upgrade"
	StepUpgradeStandaloneCheckSync                  = "scs-upgrade-standalone-check-sync"
	StepUpgradeStandaloneTaskStepHandover           = "scs-upgrade-standalone-task-step-handover"
	StepUpgradeStandaloneHandover                   = "scs-upgrade-standalone-handover"
	StepUpgradeStandaloneRoInstSlaveOf              = "scs-upgrade-standalone-ro-slave-of"
	StepUpgradeStandaloneRoInstCheckSync            = "scs-upgrade-standalone-ro-check-sync"
	StepUpgradeStandaloneRoInstUpgrade              = "scs-upgrade-standalone-ro-instance-upgrade"
	StepUpgradeStandaloneTaskStepUpgradeMasters     = "scs-upgrade-standalone-task-step-upgrade-masters"
	StepUpgradeStandaloneMasterUpgrade              = "scs-upgrade-standalone-master-upgrade"
	StepUpgradeStandaloneSecondCheckSync            = "scs-upgrade-standalone-second-check-sync"
	StepUpgradeStandaloneUpdateAzDeployInfo         = "scs-upgrade-standalone-update-azdeployinfo "
	StepUpgradeStandaloneUpdateAppTopologyInXmaster = "scs-upgrade-standalone-update-app-topo-in-xmaster"
	StepUpgradeStandaloneSetACLForVersionUpgrade    = "scs-upgrade-standalone-set-acl-for-version-upgrade"
	StepUpgradeStandaloneTaskStepSuccess            = "scs-upgrade-standalone-task-step-success"
	StepUpgradeStandaloneCallback                   = "scs-upgrade-standalone-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneInitTimeWindowTask,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepUpgradeStandaloneBuildMeta,
		ErrorNextStep:   StepUpgradeStandaloneInitTimeWindowTask,
	})
	// Step-0 构建元数据，包括创建Clusters、Cluster对应的Nodes并分配角色
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneBuildMeta,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForUpgrade,
		SuccessNextStep: StepUpgradeStandaloneTaskStepUpgradeFollowers,
		ErrorNextStep:   StepUpgradeStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeStandaloneTaskStepUpgradeFollowers,
		Workflow: WorkflowUpgradeStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepUpgradeFollowers, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeStandaloneSlavesUpgrade,
		ErrorNextStep:   StepUpgradeStandaloneTaskStepUpgradeFollowers,
	})

	// Step-1 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneSlavesUpgrade,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: StepUpgradeStandaloneCheckSync,
		ErrorNextStep:   StepUpgradeStandaloneSlavesUpgrade},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-2 等待数据同步后、执行主从切换
	// Step-2-1 检查能否切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneCheckSync,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepUpgradeStandaloneTaskStepHandover,
		ErrorNextStep:   StepUpgradeStandaloneCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeStandaloneTaskStepHandover,
		Workflow: WorkflowUpgradeStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepHandover, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeStandaloneHandover,
		ErrorNextStep:   StepUpgradeStandaloneTaskStepHandover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneHandover,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     restart.ProcessHandoverStandalone,
		SuccessNextStep: StepUpgradeStandaloneRoInstSlaveOf,
		ErrorNextStep:   StepUpgradeStandaloneHandover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneRoInstSlaveOf,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     readonlygroup.ProcessRoInstSlaveOf,
		SuccessNextStep: StepUpgradeStandaloneRoInstCheckSync,
		ErrorNextStep:   StepUpgradeStandaloneRoInstSlaveOf,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneRoInstCheckSync,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepUpgradeStandaloneRoInstUpgrade,
		ErrorNextStep:   StepUpgradeStandaloneRoInstCheckSync,
	})

	// 执行所有只读节点的升级
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneRoInstUpgrade,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     upgrade.ProcessUpgradeReadonlyInstances,
		SuccessNextStep: StepUpgradeStandaloneTaskStepUpgradeMasters,
		ErrorNextStep:   StepUpgradeStandaloneRoInstUpgrade},

		workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeStandaloneTaskStepUpgradeMasters,
		Workflow: WorkflowUpgradeStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepUpgradeMasters, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeStandaloneMasterUpgrade,
		ErrorNextStep:   StepUpgradeStandaloneTaskStepUpgradeMasters,
	})

	// Step-3 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneMasterUpgrade,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: StepUpgradeStandaloneSecondCheckSync,
		ErrorNextStep:   StepUpgradeStandaloneMasterUpgrade},

		workflow.WithStepTimeout(5*time.Minute))

	// 再次检查同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneSecondCheckSync,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepUpgradeStandaloneUpdateAzDeployInfo,
		ErrorNextStep:   StepUpgradeStandaloneSecondCheckSync,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneUpdateAzDeployInfo,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     handover.ProcessHandoverUpdateAzDeployInfo,
		SuccessNextStep: StepUpgradeStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepUpgradeStandaloneUpdateAzDeployInfo},
		workflow.WithMaxReentry(3, StepUpgradeStandaloneUpdateAppTopologyInXmaster))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepUpgradeStandaloneSetACLForVersionUpgrade,
		ErrorNextStep:   StepUpgradeStandaloneUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneSetACLForVersionUpgrade,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     acl.ProcessInitAclStandaloneForVersionUpgrade,
		SuccessNextStep: StepUpgradeStandaloneTaskStepSuccess,
		ErrorNextStep:   StepUpgradeStandaloneSetACLForVersionUpgrade,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneTaskStepSuccess,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepUpgradeStandaloneCallback,
		ErrorNextStep:   StepUpgradeStandaloneTaskStepSuccess,
	})

	// Step-4 更新成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeStandaloneCallback,
		Workflow:        WorkflowUpgradeStandalone,
		StepProcess:     callback.ProcessUpgradeOrRestartSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpgradeStandaloneCallback,
	})
}
