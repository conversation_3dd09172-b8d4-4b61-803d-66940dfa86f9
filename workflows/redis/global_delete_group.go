/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_delete_group.go */
/*
modification history
--------------------
2022/05/24 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"time"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkFlowLocalDeleteGroup                                = "scs-local-delete-group"
	WorkFlowLocalDeleteGroup_StepBuildMeta                  = "scs-local-delete-group-buildmeta"
	WorkFlowLocalDeleteGroup_StepInitMetaserver             = "scs-local-delete-group-init-metaserver"
	WorkFlowLocalDeleteGroup_StepRegisterMetaserver         = "scs-local-delete-group-register-metaserver"
	WorkFlowLocalDeleteGroup_CbMetaserver                   = "scs-local-delete-group-cb-metaserver"
	WorkFlowLocalDeleteGroup_StepUpdateTopology             = "scs-local-delete-group-update-topology"
	WorkFlowLocalDeleteGroup_StepUpdateSg                   = "scs-local-delete-group-update-sg"
	WorkFlowLocalDeleteGroup_StepPushMonitorHTGRPSlaveFlag  = "scs-local-delete-group-push-htgrp-slave-flag"
	WorkFlowLocalDeleteGroup_StepCbNodeRole                 = "scs-local-delete-group-cb-node-role"
	WorkFlowLocalDeleteGroup_StepRestartProxy               = "scs-local-delete-group-restart-proxy"
	WorkFlowLocalDeleteGroup_UpdateTlsConfIfNeeded          = "scs-local-delete-group-update-tls-conf-if-needed"
	WorkFlowLocalDeleteGroup_StepCleanMetaserver            = "scs-local-delete-group-clean-metaserver"
	WorkFlowLocalDeleteGroup_StepUpdateAppTopologyInXmaster = "scs-local-delete-group-update-app-topo-in-xmaster"
	WorkFlowLocalDeleteGroup_StepSuccessCb                  = "scs-local-delete-group-success-cb"
)

func init() {
	// ① 设置状态
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepBuildMeta,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForDeleteQuitGlobalGroup,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepInitMetaserver,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepBuildMeta,
	})

	// ② 初始化地域metaserver，更新slot分布与global-metaserver中一致
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepInitMetaserver,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     metaserver.ProcessRebuildLocalMetaserver,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepRegisterMetaserver,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepInitMetaserver,
	},
		workflow.WithStepTimeout(30*time.Minute),
	)
	// ③ 将节点信息注册至地域metaserver(如果地域没有主，则任选一个为主)
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepRegisterMetaserver,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     metaserver.ProcessForceAddNodesLocal,
		SuccessNextStep: WorkFlowLocalDeleteGroup_CbMetaserver,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepRegisterMetaserver,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_CbMetaserver,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     callback.ProcessCbLocalMetaserverEntrance,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepUpdateTopology,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_CbMetaserver,
	})
	// ④ 更新redis拓扑
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepUpdateTopology,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     metaserver.ProcessResetLocalTopology,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepUpdateSg,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepUpdateTopology,
	})
	// ⑤ 更新InnerSecurityGroup，保证本地域redis、proxy在白名单ip中
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepUpdateSg,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupClusterLocal,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepUpdateSg,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAllToNo,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepCbNodeRole,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepPushMonitorHTGRPSlaveFlag,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepCbNodeRole,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     callback.ProcessCbNodeRolesQuit,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepRestartProxy,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepCbNodeRole,
	})
	// ⑥ 分批次重启Proxy
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepRestartProxy,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     restart.ProcessUpgradeProxies,
		SuccessNextStep: WorkFlowLocalDeleteGroup_UpdateTlsConfIfNeeded,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepRestartProxy,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_UpdateTlsConfIfNeeded,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     util.UpdateTLSConfIfNeeded,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_UpdateTlsConfIfNeeded,
	})

	// ⑦ 删除原Metaserver中信息
	// workflow.AddStep(&workflow.AddStepParam{
	//	Name:            WorkFlowLocalDeleteGroup_StepCleanMetaserver,
	//	Workflow:        WorkFlowLocalDeleteGroup,
	//	StepProcess:     metaserver.ProcessDeleteClusterGlobal,
	//	SuccessNextStep: WorkFlowLocalDeleteGroup_StepSuccessCb,
	//	ErrorNextStep:   WorkFlowLocalDeleteGroup_StepCleanMetaserver,
	// })

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalDeleteGroup_StepSuccessCb,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepUpdateAppTopologyInXmaster},
		workflow.WithMaxReentry(5, WorkFlowLocalDeleteGroup_StepSuccessCb))

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteGroup_StepSuccessCb,
		Workflow:        WorkFlowLocalDeleteGroup,
		StepProcess:     callback.ProcessGlobalGeneralQuitSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalDeleteGroup_StepSuccessCb,
	})
}
