/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建SCS标准版实例WORKFLOW

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"Replicas": [{
		"Zone": "zoneA",
		"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
		"Role": "master",
		"Count": 1
	}, {
		"Zone": "zoneB",
		"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
		"Role": "slave",
		"Count": 1
	}],
	"DestReplicas": [{
		"Zone": "zoneA",
		"SubnetIDs": ["467eee77-254f-4222-8714-194322bdbd4f"],
		"Role": "master",
		"Count": 1
	}, {
		"Zone": "zoneB",
		"SubnetIDs": ["1230ab0f-0bbc-4afd-9155-5bab99350d46"],
		"Role": "slave",
		"Count": 1
	}]
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyReplicaStandalone                       = "scs-modify-replicas-standalone-app"
	StepModifyReplicaStandaloneBuildMeta                  = "scs-modify-replica-app-build-meta"
	StepModifyReplicaStandaloneTaskStepCreateNodes        = "scs-modify-replica-app-task-step-create-nodes"
	StepModifyReplicaStandaloneHandover                   = "scs-modify-replica-handover"
	StepModifyReplicaStandaloneFillSpec                   = "scs-modify-replica-fill-spec"
	StepModifyReplicaStandaloneCheckSubnetsEnoughIps      = "scs-modify-replica-check-subnets-enough-ips"
	StepModifyReplicaStandaloneApplyResources             = "scs-modify-replica-apply-resources"
	StepModifyReplicaStandaloneApplyResourcesCallback     = "scs-modify-replica-apply-resources-cb"
	StepModifyReplicaStandaloneTaskStepDeployNodes        = "scs-modify-replica-app-task-step-deploy-nodes"
	StepModifyReplicaStandaloneInitMachineEnv             = "scs-modify-replica-init-machine-env"
	StepModifyReplicaStandaloneAddGlobalNode              = "scs-modify-replica-standalone-add-global-node"
	StepModifyReplicaStandaloneUpdateSecurityGroups       = "scs-modify-replica-update-security-groups"
	StepModifyReplicaStandaloneDeployRedis                = "scs-modify-replica-apply-deploy-redis"
	StepModifyReplicaStandaloneTaskStepConfigNodes        = "scs-modify-replica-app-task-step-config-nodes"
	StepModifyReplicaStandaloneSetAcl                     = "scs-modify-replica-set-acl"
	StepModifyReplicaStandaloneSlaveOfGlobal              = "scs-modify-replica-standalone-slaveof-global"
	StepModifyReplicaStandaloneUpdateConfig               = "scs-modify-replica-update-config"
	StepModifyReplicaStandaloneInitTopology               = "scs-modify-replica-init-topology"
	StepModifyReplicaStandaloneTaskStepSyncNodes          = "scs-modify-replica-app-task-step-sync-nodes"
	StepModifyReplicaStandaloneCheckAllSync               = "scs-modify-replica-check-all-sync"
	StepModifyReplicaStandaloneDeployFilebeat             = "scs-modify-replica-apply-deploy-filebeat"
	StepModifyReplicaStandaloneSetRs                      = "scs-modify-replica-set-rs"
	StepModifyReplicaStandaloneSetRsForHotGroup           = "scs-modify-replica-set-rs-hot-group"
	StepModifyReplicaStandaloneSetEntranceRs              = "scs-modify-replica-set-entrance-rs"
	StepModifyReplicaStandaloneSetRoTopo                  = "scs-modify-replica-set-ro-topo"
	StepModifyReplicaStandaloneInitBcmResource            = "scs-modify-replicas-standalone-init-bcm-resource"
	StepModifyReplicaStandaloneTaskStepReleaseNodes       = "scs-modify-replica-app-task-step-release-nodes"
	StepModifyReplicaStandaloneDeleteGlobalOldNodes       = "scs-modify-replicas-standalone-del-global-old-nodes"
	StepModifyReplicaStandaloneDeleteOldNodes             = "scs-modify-replicas-standalone-del-old-nodes"
	StepModifyReplicaStandaloneUpdateAppTopologyInXmaster = "scs-modify-replicas-standalone-update-app-topo-in-xmaster"
	StepModifyReplicaStandaloneInitOpMonitor              = "scs-modify-replicas-standalone-init-op-monitor"
	StepModifyReplicasStandalonePushMonitorHTGRPSlaveFlag = "scs-modify-replicas-standalone-push-htgrp-slave-flag"
	StepModifyReplicasStandalonePushFlag                  = "scs-modify-replicas-standalone-push-flag"
	StepModifyReplicaStandaloneTaskStepSuccess            = "scs-modify-replica-app-task-step-success"
	StepModifyReplicaStandaloneCreateCallback             = "scs-modify-replica-create-cb"
	StepModifyReplicaStandaloneRollbackReleaseResource    = "scs-modify-replicas-standalone-rollback-release-resource"
	StepModifyReplicaStandaloneTaskStepError              = "scs-modify-replica-app-task-step-error"
	StepModifyReplicaStandaloneRollbackBcmResource        = "scs-modify-replicas-standalone-rollback-bcm-resource"
	StepModifyReplicaStandaloneRollbackPushFlag           = "scs-modify-replicas-standalone-rollback-push-flag"
	StepModifyReplicaStandaloneRollbackMeta               = "scs-modify-replicas-standalone-rollback-meta"
	StepModifyReplicaStandaloneRollbackCallback           = "scs-modify-replicas-standalone-rollback-callback"
)

const (
	WorkflowModifyReplicaStandaloneHandover                       = "scs-modify-replica-handover-standalone"
	StepModifyReplicaStandaloneHandoverAddStartEvent              = "scs-modify-replica-handover-add-start-event"
	StepModifyReplicaStandaloneHandoverHandover                   = "scs-modify-replica-handover-handover"
	StepModifyReplicaStandaloneHandoverSuccessCallback            = "scs-modify-replica-handover-succ-cb"
	StepModifyReplicaStandaloneHandoverUpdateAppTopologyInXmaster = "scs-modify-replica-handover-update-app-topo-in-xmaster"
	StepModifyReplicaStandaloneHandoverAddSuccessEndEvent         = "scs-modify-replica-handover-add-success-end-event"
)

func init() {
	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneBuildMeta,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: buildmeta.ProcessBuildMetaForModifyReplicas,

		SuccessNextStep: StepModifyReplicaStandaloneTaskStepCreateNodes,
		ErrorNextStep:   StepModifyReplicaStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepCreateNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaStandaloneHandover,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepCreateNodes,
	})

	// Step-4 检查是否有主节点为ToDelete状态，如果有，进行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneHandover,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: handover.ProcessHandoverForDeleteReplicas,

		SuccessNextStep: StepModifyReplicaStandaloneFillSpec,
		ErrorNextStep:   StepModifyReplicaStandaloneHandover,
	})

	// Step-2 将规格信息填入Cluster表或Interface表；
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneFillSpec,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepModifyReplicaStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyReplicaStandaloneFillSpec,
	})

	// Step-3 检查子网ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneCheckSubnetsEnoughIps,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepModifyReplicaStandaloneApplyResources,
		ErrorNextStep:   StepModifyReplicaStandaloneRollbackReleaseResource,
	})

	// Step-5 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneApplyResources,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepModifyReplicaStandaloneApplyResourcesCallback,
		ErrorNextStep:   StepModifyReplicaStandaloneApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-6 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneApplyResourcesCallback,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepModifyReplicaStandaloneTaskStepDeployNodes,
		ErrorNextStep:   StepModifyReplicaStandaloneApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepDeployNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaStandaloneInitMachineEnv,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneInitMachineEnv,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: initmachineenv.ProcessInitMachineEnv,

		SuccessNextStep: StepModifyReplicaStandaloneAddGlobalNode,
		ErrorNextStep:   StepModifyReplicaStandaloneInitMachineEnv,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneAddGlobalNode,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: metaserver.ProcessAddGlobalNodes,

		SuccessNextStep: StepModifyReplicaStandaloneUpdateSecurityGroups,
		ErrorNextStep:   StepModifyReplicaStandaloneAddGlobalNode,
	})

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneUpdateSecurityGroups,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: securitygroup.ProcessRebuildSecurityGroupStandalone,

		SuccessNextStep: StepModifyReplicaStandaloneDeployRedis,
		ErrorNextStep:   StepModifyReplicaStandaloneUpdateSecurityGroups,
	})

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneDeployRedis,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: deploy.ProcessDeployAll,

		SuccessNextStep: StepModifyReplicaStandaloneTaskStepConfigNodes,
		ErrorNextStep:   StepModifyReplicaStandaloneDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepConfigNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaStandaloneSlaveOfGlobal,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepConfigNodes})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneSlaveOfGlobal,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: gmaster.ProcessGlobalSlaveOf,

		SuccessNextStep: StepModifyReplicaStandaloneSetAcl,
		ErrorNextStep:   StepModifyReplicaStandaloneSlaveOfGlobal},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-7 设置初始auth,acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneSetAcl,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: acl.ProcessInitAclStandaloneForNewNode,

		SuccessNextStep: StepModifyReplicaStandaloneUpdateConfig,
		ErrorNextStep:   StepModifyReplicaStandaloneSetAcl,
	})

	// Step-8 更新配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneUpdateConfig,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: config.ProcessUpdateConfigStandalone,

		SuccessNextStep: StepModifyReplicaStandaloneInitTopology,
		ErrorNextStep:   StepModifyReplicaStandaloneUpdateConfig,
	}, workflow.WithMaxReentry(2, StepModifyReplicaStandaloneInitTopology))

	// Step-8 设置拓扑结构
	// 对于主从版，仅设置主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneInitTopology,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: topology.ProcessInitStandaloneTopo,

		SuccessNextStep: StepModifyReplicaStandaloneTaskStepSyncNodes,
		ErrorNextStep:   StepModifyReplicaStandaloneInitTopology,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepSyncNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepSync, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaStandaloneCheckAllSync,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepSyncNodes,
	})

	// 检查同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneCheckAllSync,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: syncredis.PocessCheckRedisAllSync,

		SuccessNextStep: StepModifyReplicaStandaloneDeployFilebeat,
		ErrorNextStep:   StepModifyReplicaStandaloneCheckAllSync,
	}, workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneDeployFilebeat,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: deploy.ProcessDeployFilebeat,

		SuccessNextStep: StepModifyReplicaStandaloneSetRs,
		ErrorNextStep:   StepModifyReplicaStandaloneDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// Step-9 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneSetRs,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: blb.ProcessSetStandaloneRs,

		SuccessNextStep: StepModifyReplicaStandaloneSetRsForHotGroup,
		ErrorNextStep:   StepModifyReplicaStandaloneSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneSetRsForHotGroup,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: blb.ProcessUpdateStandaloneRsForGlobalModifySpec,

		SuccessNextStep: StepModifyReplicaStandaloneSetEntranceRs,
		ErrorNextStep:   StepModifyReplicaStandaloneSetRsForHotGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneSetEntranceRs,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: blb.ProcessSetEntranceRs,

		SuccessNextStep: StepModifyReplicaStandaloneSetRoTopo,
		ErrorNextStep:   StepModifyReplicaStandaloneSetEntranceRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneSetRoTopo,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: topology.ProcessTopoForRoInGroup,

		SuccessNextStep: StepModifyReplicaStandaloneInitBcmResource,
		ErrorNextStep:   StepModifyReplicaStandaloneSetRoTopo,
	})

	// 根据变更新建或删除监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneInitBcmResource,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: bcm.ProcessBcmResourceWithoutApp,

		SuccessNextStep: StepModifyReplicasStandalonePushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifyReplicaStandaloneInitBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicasStandalonePushMonitorHTGRPSlaveFlag,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,

		SuccessNextStep: StepModifyReplicasStandalonePushFlag,
		ErrorNextStep:   StepModifyReplicasStandalonePushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicasStandalonePushFlag,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: pushflag.ProcessUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifyReplicaStandaloneTaskStepReleaseNodes,
		ErrorNextStep:   StepModifyReplicasStandalonePushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepReleaseNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaStandaloneDeleteGlobalOldNodes,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepReleaseNodes,
	})

	// Step-10 删除待删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneDeleteGlobalOldNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: metaserver.ProcessDelNodesForStandalone,

		SuccessNextStep: StepModifyReplicaStandaloneDeleteOldNodes,
		ErrorNextStep:   StepModifyReplicaStandaloneDeleteGlobalOldNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneDeleteOldNodes,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: delresource.ProcessDeleteToDeleteNodes,

		SuccessNextStep: StepModifyReplicaStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyReplicaStandaloneDeleteOldNodes,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneUpdateAppTopologyInXmaster,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: xmaster.ProcessUpdateAppTopologyInXmaster,

		SuccessNextStep: StepModifyReplicaStandaloneInitOpMonitor,
		ErrorNextStep:   StepModifyReplicaStandaloneUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaStandaloneInitOpMonitor,
		Workflow:        WorkflowModifyReplicaStandalone,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyReplicaStandaloneTaskStepSuccess,
		ErrorNextStep:   StepModifyReplicaStandaloneInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepSuccess,
		Workflow: WorkflowModifyReplicaStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			"", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepModifyReplicaStandaloneCreateCallback,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepSuccess,
	})

	// Step-11 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneCreateCallback,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: callback.ProcessModifyNodesSuccCb,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyReplicaStandaloneCreateCallback,
	})

	// Step-Error-01 删除已创建的节点
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneRollbackReleaseResource,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: StepModifyReplicaStandaloneTaskStepError,
		ErrorNextStep:   StepModifyReplicaStandaloneRollbackReleaseResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneTaskStepError,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			"", "", timewindow.TaskStatusError),

		SuccessNextStep: StepModifyReplicaStandaloneRollbackBcmResource,
		ErrorNextStep:   StepModifyReplicaStandaloneTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneRollbackBcmResource,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: bcm.ProcessRollbackBcmResourceWithoutApp,

		SuccessNextStep: StepModifyReplicaStandaloneRollbackPushFlag,
		ErrorNextStep:   StepModifyReplicaStandaloneRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneRollbackPushFlag,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifyReplicaStandaloneRollbackMeta,
		ErrorNextStep:   StepModifyReplicaStandaloneRollbackPushFlag,
	})

	// Step-Error-01 删除已创建的节点
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneRollbackMeta,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: delresource.ProcessRollbackMeta,

		SuccessNextStep: StepModifyReplicaStandaloneRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-Error-02 比那更失败时，如果已经创建了资源，需要删除资源
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneRollbackCallback,
		Workflow: WorkflowModifyReplicaStandalone,

		StepProcess: callback.ProcessModifyNodesErrorCb,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneHandoverAddStartEvent,
		Workflow: WorkflowModifyReplicaStandaloneHandover,

		StepProcess: event.ProcessAddSwitchingStartEvent,

		SuccessNextStep: StepModifyReplicaStandaloneHandoverHandover,
		ErrorNextStep:   StepModifyReplicaStandaloneHandoverAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneHandoverHandover,
		Workflow: WorkflowModifyReplicaStandaloneHandover,

		StepProcess: handover.ProcessManualHandoverNewZone,

		SuccessNextStep: StepModifyReplicaStandaloneHandoverSuccessCallback,
		ErrorNextStep:   StepModifyReplicaStandaloneHandoverHandover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneHandoverSuccessCallback,
		Workflow: WorkflowModifyReplicaStandaloneHandover,

		StepProcess: callback.ProcessModifyNodesSuccCb,

		SuccessNextStep: StepModifyReplicaStandaloneHandoverUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyReplicaStandaloneHandoverSuccessCallback,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneHandoverUpdateAppTopologyInXmaster,
		Workflow: WorkflowModifyReplicaStandaloneHandover,

		StepProcess: xmaster.ProcessUpdateAppTopologyInXmaster,

		SuccessNextStep: StepModifyReplicaStandaloneHandoverAddSuccessEndEvent,
		ErrorNextStep:   StepModifyReplicaStandaloneHandoverUpdateAppTopologyInXmaster,
	})

	// 主从切换成功事件监控处理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaStandaloneHandoverAddSuccessEndEvent,
		Workflow: WorkflowModifyReplicaStandaloneHandover,

		StepProcess: event.ProcessAddSwitchingSuccessEndEvent,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyReplicaStandaloneHandoverAddSuccessEndEvent,
	})
}
