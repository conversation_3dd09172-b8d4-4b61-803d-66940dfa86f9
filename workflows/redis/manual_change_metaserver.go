// Copyright(C) 2024 Baidu Inc. All Rights Reserved.
// Author: <PERSON><PERSON> (ca<PERSON>uni<PERSON>@baidu.com)
// Date: 2024/07/18
// Description: 用于手动迁移集群使用的metaserver，修改数据库元数据并设置proxy配置已经pega配置

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/changemeta"
)

const (
	WorkflowManualChangeMetaserver             = "scs-workflow-manual-change-metaserver"
	StepManualChangeMetaserverModifyDbAndProxy = "scs-workflow-manual-change-metaserver-modify-db-and-proxy"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualChangeMetaserverModifyDbAndProxy,
		Workflow:        WorkflowManualChangeMetaserver,
		StepProcess:     changemeta.ProcessChangeMetaserver,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
