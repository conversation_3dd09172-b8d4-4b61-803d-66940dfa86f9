/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* modify_shards_cluster_app.go */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2022/07/07, by <PERSON>(<PERSON><PERSON><PERSON><PERSON>@baidu.com), modify
*/

/*
DESCRIPTION
集群版扩缩容（分片/shard）Workflow

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"DestSpec": "cache.n1.small"
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyAddShardsCluster                       = "scs-modify-add-shards-cluster-app"
	StepModifyAddShardsInitTaskWindowTask                = "scs-modify-add-shards-init-tw-task"
	StepModifyAddShardsGlobalPegaConfigSet               = "modify-add-shards-global-pega-config-set"
	StepModifyAddShardsGlobalInit                        = "modify-add-shards-global-init"
	StepModifyAddShardsClusterAddStartEvent              = "scs-modify-add-shards-cluster-add-start-event"
	StepModifyAddShardsClusterBuildMeta                  = "scs-modify-add-shards-cluster-app-build-meta"
	StepModifyAddShardsTaskStepCreateNodes               = "scs-modify-add-shards-task-step-create-nodes"
	StepModifyAddShardsClusterFillSpec                   = "scs-modify-add-shards-cluster-fill-spec"
	StepModifyAddShardsClusterCheckSubnetsEnoughIps      = "scs-modify-add-shards-cluster-check-subnets-enough-ips"
	StepModifyAddShardsClusterApplyResources             = "scs-modify-add-shards-cluster-apply-resources"
	StepModifyAddShardsGlobalAfterApplyResource          = "modify-add-shards-global-after-apply-resource"
	StepModifyAddShardsClusterApplyResourcesCallback     = "scs-modify-add-shards-cluster-apply-resources-cb"
	StepModifyAddShardsTaskStepDeployNodes               = "scs-modify-add-shards-task-step-deploy-nodes"
	StepModifyAddShardsClusterInitMachineEnv             = "scs-modify-add-shards-cluster-init-machine-env"
	StepModifyAddShardsClusterAddGlobalShard             = "scs-modify-add-shards-cluster-add-global-shard"
	StepModifyAddShardsClusterAddGlobalNode              = "scs-modify-add-shards-cluster-add-global-node"
	StepModifyAddShardsClusterUpdateSecurityGroups       = "scs-modify-add-shards-cluster-update-security-groups"
	StepModifyAddShardsClusterAddShards                  = "scs-modify-add-shards-cluster-add-shards"
	StepModifyAddShardsClusterAddNodes                   = "scs-modify-add-shards-cluster-add-nodes"
	StepModifyAddShardsClusterDeployRedis                = "scs-modify-add-shards-cluster-deploy-redis"
	StepModifyAddShardsTaskStepConfigNodes               = "scs-modify-add-shards-task-step-config-nodes"
	StepModifyAddShardsClusterSetLocalSlaveof            = "scs-modify-add-shards-cluster-set-local-slaveof"
	StepModifyAddShardsClusterSetGlobalSlaveof           = "scs-modify-add-shards-cluster-set-global-slaveof"
	StepModifyAddShardsClusterUpdateTlsConfIfNeeded      = "scs-modify-add-shards-cluster-update-tls-conf-if-needed"
	StepModifyAddShardsClusterDeployFilebeat             = "scs-modify-add-shards-cluster-deploy-filebeat"
	StepModifyAddShardsClusterSetRs                      = "scs-modify-add-shards-cluster-set-rs"
	StepModifyAddShardsClusterSetMcpackRs                = "scs-modify-add-shards-cluster-set-mcpack-rs"
	StepModifyAddShardsClusterSetBnsInstance             = "scs-modify-add-shards-cluster-set-bns-instance"
	StepModifyAddShardsGolbalBeforeMigration             = "scs-modify-add-shards-global-before-migration"
	StepModifyAddShardsClusterStartMigrateTask           = "scs-modify-add-shards-cluster-migrate"
	StepModifyAddShardsGlobalMigrateSync                 = "scs-modfiy-add-shards-global-migrate-sync"
	StepModifyAddShardsClusterUpdateAppTopologyInXmaster = "scs-modify-add-shards-cluster-update-app-topo-in-xmaster"
	StepModifyAddShardsInitBcmResource                   = "scs-modify-add-shards-init-bcm-resource"
	StepModifyAddShardsInitSyncBcmResource               = "scs-modify-add-shards-init-sync-group-bcm-resource"
	StepModifyAddShardsInitOpMonitor                     = "scs-modify-add-shards-init-op-monitor"
	StepModifyAddShardsClusterAddToBcmGroup              = "scs-modify-add-shards-cluster-add-to-bcm-group"
	StepModifyAddShardsClusterPushMonitorHTGRPSlaveFlag  = "scs-modify-add-shards-cluster-push-htgrp-slave-flag"
	StepModifyAddShardsClusterPushFlag                   = "scs-modify-add-shards-cluster-push-flag"
	StepModifyAddShardsClusterSetHtGrpSlaveTaskSuccess   = "scs-modify-add-shards-set-htgrp-slave-task-success"
	StepModifyAddShardsClusterDeleteAOFBackupPolicy      = "scs-modify-add-shards-cluster-delete-aof-backup-policy"
	StepModifyAddShardsClusterSuccessCallback            = "scs-modify-add-shards-cluster-succ-cb"
	StepModifyAddShardsGolbalSetRollback                 = "scs-modify-add-shards-golbal-set-rollback"
	StepModifyAddShardsTaskStepError                     = "scs-modify-add-shards-task-step-error"
	StepModifyAddShardsClusterRollbackReleaseResources   = "scs-modify-add-shards-cluster-rollback-release-resource"
	StepModifyAddShardsClusterRollbackRemoveFromBcmGroup = "scs-modify-add-shards-cluster-rollback-remove-from-bcm-group"
	StepModifyAddShardsClusterRollbackBcmResource        = "scs-modify-add-shards-cluster-rollback-bcm-resource"
	StepModifyAddShardsClusterRollbackSyncBcmResource    = "scs-modify-add-shards-cluster-rollback-sync-group-bcm-resource"
	StepModifyAddShardsClusterRollbackMeta               = "scs-modify-add-shards-cluster-rollback-meta"
	StepModifyAddShardsClusterRollbackCallback           = "scs-modify-add-shards-cluster-rollback-cb"
	StepModifyAddShardsGlobalCompleteRollback            = "scs-modify-add-shards-global-complete-rollback"
	StepModifyAddShardsClusterAddSuccessEndEvent         = "scs-modify-add-shards-cluster-add-success-end-event"
	StepModifyAddShardsClusterAddFailedEndEvent          = "scs-modify-add-shards-cluster-add-failed-end-event"
)

const (
	WorkflowMigrateCluster              = "scs-migrate-cluster-app"
	StepMigrateClusterSetAutoBalance    = "scs-migrate-cluster-app-set-auto-balance"
	StepMigrateClusterTaskStepRebalance = "scs-migrate-cluster-task-step-rebalance"
	StepMigrateClusterCheckAutoBalance  = "scs-migrate-cluster-app-check-auto-balance"
	StepMigrateGlobalComplete           = "scs-migrate-global-complete"
	StepMigrateStatusCallback           = "scs-migrate-cluster-app-status-cb"
	StepMigrateCloseTimeWindowTask      = "scs-migrate-close-tw-task-cb"
)

const (
	WorkflowShrinkCluster              = "scs-shrink-cluster-app"
	StepShrinkClusterShrink            = "scs-shrink-cluster-app-shrink"
	StepShrinkClusterTaskStepRebalance = "scs-shrink-cluster-task-step-rebalance"
	StepShrinkClusterCheckShrink       = "scs-shrink-cluster-app-check-shrink"
	StepShrinkGlobalComplete           = "scs-shrink-global-complete"
)

/*
pega跟redis在扩缩容行为表现不一致, 此处为pega单独设置扩缩容work_flow
1、扩容方式：

	redis扩缩容使用cts组件进行数据迁移
	pega扩缩容使用直接迁移slot的方式

2、扩容效率：

	redis扩缩容效率较高,因此无需很高的超时配置
	pega扩缩容效率较低,因此需要较高的超时配置

3、任务异常干预方式

	redis扩缩容依赖cts组件,其任务异常种类很多,而且干预方式各不相同,不可直接进行重试
	pega扩缩容采用迁移slot的方式,迁移异常时可进行重试

4、扩缩容状态检查命令不一致，需要兼容

	https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/CyUESrdXXe/-Aa5jKzomURVpv
*/
const (
	WorkflowMigratePegaCluster              = "scs-migrate-pega-cluster-app"
	StepMigratePegaClusterSetAutoBalance    = "scs-migrate-pega-cluster-app-set-auto-balance"
	StepMigratePegaClusterTaskStepRebalance = "scs-migrate-pega-cluster-task-step-rebalance"
	StepMigratePegaClusterCheckAutoBalance  = "scs-migrate-pega-cluster-app-check-auto-balance"
	StepMigratePegaGlobalComplete           = "scs-migrate-pega-global-complete"
	StepMigratePegaStatusCallback           = "scs-migrate-pega-cluster-app-status-cb"
	StepMigratePegaCloseTimeWindowTask      = "scs-migrate-pega-cluster-close-tw-task-cb"
	StepMigratePegaProcessReacheMaxRetries  = "scs-migrate-pega-cluster-reache-max-retries"
)

const (
	WorkflowShrinkPegaCluster              = "scs-shrink-pega-cluster-app"
	StepShrinkPegaClusterShrink            = "scs-shrink-pega-cluster-app-shrink"
	StepShrinkPegaClusterTaskStepRebalance = "scs-shrink-pega-cluster-task-step-rebalance"
	StepShrinkPegaClusterCheckShrink       = "scs-shrink-pega-cluster-app-check-shrink"
	StepShrinkPegaGlobalComplete           = "scs-shrink-pega-global-complete"
	StepShrinkPegaProcessReacheMaxRetries  = "scs-shrink-pega-cluster-reache-max-retries"
)

const (
	WorkflowModifyDelShardsCluster                       = "scs-modify-del-shards-cluster-app"
	StepModifyDelShardsInitTaskWindowTask                = "scs-modify-del-shards-init-tw-task"
	StepModifyDelShardsGlobalPegaConfSet                 = "modify-del-shards-global-pega-config-set"
	StepModifyDelShardsGlobalInit                        = "modify-del-shards-global-init"
	StepModifyDelShardsClusterAddStartEvent              = "scs-modify-del-shards-cluster-add-start-event"
	StepModifyDelShardsClusterBuildMeta                  = "scs-modify-del-shards-cluster-build-meta"
	StepModifyDelShardsClusterStartShrinkTask            = "scs-modify-del-shards-cluster-start-shrink-task"
	StepModifyDelShardsGlobalCheckShrink                 = "scs-modify-del-shards-global-check-shrink"
	StepModifyDelShardsClusterMarkShrinkedShards         = "scs-modify-del-shards-cluster-mark-shrinked-shards"
	StepModifyDelShardsClusterTaskStepDeleteNodes        = "scs-modify-del-shards-task-step-delete-nodes"
	StepModifyDelShardsClusterSetRs                      = "scs-modify-del-shards-cluster-set-rs"
	StepModifyDelShardsClusterSetMcpackRs                = "scs-modify-del-shards-cluster-set-mcpack-rs"
	StepModifyDelShardsClusterSetBnsInstance             = "scs-modify-del-shards-cluster-set-bns-instance"
	StepModifyDelShardsClusterPushFlags                  = "scs-modify-del-shards-cluster-push-flags"
	StepModifyDelShardsClusterDelNodes                   = "scs-modify-del-shards-cluster-del-nodes"
	StepModifyDelShardsClusterDelShards                  = "scs-modify-del-shards-cluster-del-shards"
	StepModifyDelShardsClusterRemoveFromGroup            = "scs-modify-del-shards-cluster-remove-from-group"
	StepModifyDelShardsClusterInitBcmResource            = "scs-modify-del-shards-cluster-init-bcm-resource"
	StepModifyDelShardsClusterInitSyncGroupBcmResource   = "scs-modify-del-shards-cluster-init-sync-group-bcm-resource"
	StepModifyDelShardsClusterDeleteOldNodes             = "scs-modify-del-shards-cluster-del-old-nodes"
	StepModifyDelShardsClusterUpdateAppTopologyInXmaster = "scs-modify-del-shards-cluster-update-app-topo-in-xmaster"
	StepModifyDelShardsClusterSuccessCallback            = "scs-modify-del-shards-cluster-succ-cb"
	StepModifyDelShardsGlobalComplete                    = "scs-modify-del-shards-global-complete"
	StepModifyDelShardsClusterDeleteAOFBackupPolicy      = "scs-modify-del-shards-cluster-delete-aof-backup-policy"
	StepModifyDelShardsCloseTimeWindowTask               = "scs-modify-del-shards-close-tw-task-cb"
	StepModifyDelShardsClusterAddSuccessEndEvent         = "scs-modify-del-shards-cluster-add-success-end-event"
)

func init() {
	/**************************************集群版增加分片*******************************************/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsInitTaskWindowTask,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: timewindow.ProcessSetTaskID,

		SuccessNextStep: StepModifyAddShardsGlobalPegaConfigSet,
		ErrorNextStep:   StepModifyAddShardsInitTaskWindowTask,
	})

	// Step-1 热活实例组--初始化全局修改状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsGlobalPegaConfigSet,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: gmaster.ProcessResetPegaHotConfigs,

		SuccessNextStep: StepModifyAddShardsGlobalInit,
		ErrorNextStep:   StepModifyAddShardsGlobalPegaConfigSet,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsGlobalInit,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: gmaster.ProcessInitGlobalAddShardsStatus,

		SuccessNextStep: StepModifyAddShardsClusterAddStartEvent,
		ErrorNextStep:   StepModifyAddShardsGlobalInit,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterAddStartEvent,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: event.ProcessAddModifyingStartEvent,

		SuccessNextStep: StepModifyAddShardsClusterBuildMeta,
		ErrorNextStep:   StepModifyAddShardsClusterAddStartEvent,
	})

	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterBuildMeta,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: buildmeta.ProcessModifyShards,

		SuccessNextStep: StepModifyAddShardsTaskStepCreateNodes,
		ErrorNextStep:   StepModifyAddShardsClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsTaskStepCreateNodes,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddShardTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyAddShardsClusterFillSpec,
		ErrorNextStep:   StepModifyAddShardsTaskStepCreateNodes,
	})

	// Step-2 填入规格信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterFillSpec,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepModifyAddShardsClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyAddShardsClusterFillSpec,
	})

	// Step-3 检查子网IP是否足够
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterCheckSubnetsEnoughIps,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepModifyAddShardsClusterApplyResources,
		ErrorNextStep:   StepModifyAddShardsClusterCheckSubnetsEnoughIps,
	})

	// Step-4 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterApplyResources,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepModifyAddShardsGlobalAfterApplyResource,
		ErrorNextStep:   StepModifyAddShardsClusterApplyResources,
	})

	// Step-4 热活实例组--检查资源全部申请完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsGlobalAfterApplyResource,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: gmaster.ProcessCheckModifyStatusAfterApplyResource,

		SuccessNextStep: StepModifyAddShardsClusterApplyResourcesCallback,
		ErrorNextStep:   StepModifyAddShardsGlobalAfterApplyResource,
	}, workflow.WithStepTimeout(time.Hour*24*7))

	// Step-5 申请资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterApplyResourcesCallback,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepModifyAddShardsTaskStepDeployNodes,
		ErrorNextStep:   StepModifyAddShardsClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsTaskStepDeployNodes,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddShardTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyAddShardsClusterInitMachineEnv,
		ErrorNextStep:   StepModifyAddShardsTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterInitMachineEnv,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifyAddShardsClusterAddGlobalShard,
		ErrorNextStep:   StepModifyAddShardsClusterInitMachineEnv,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterAddGlobalShard,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     metaserver.ProcessAddGlobalShards,
		SuccessNextStep: StepModifyAddShardsClusterAddGlobalNode,
		ErrorNextStep:   StepModifyAddShardsClusterAddGlobalShard,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterAddGlobalNode,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepModifyAddShardsClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyAddShardsClusterAddGlobalNode,
	})

	// Step-6 更新安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterUpdateSecurityGroups,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: securitygroup.ProcessRebuildSecurityGroupCluster,

		SuccessNextStep: StepModifyAddShardsClusterAddShards,
		ErrorNextStep:   StepModifyAddShardsClusterUpdateSecurityGroups,
	})

	// Step-8 更新Metasever -- 增加shards
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterAddShards,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: metaserver.ProcessAddShards,

		SuccessNextStep: StepModifyAddShardsClusterAddNodes,
		ErrorNextStep:   StepModifyAddShardsClusterAddShards,
	})

	// Step-9 更新Metasever -- 增加节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterAddNodes,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: metaserver.ProcessAddNodes,

		SuccessNextStep: StepModifyAddShardsClusterDeployRedis,
		ErrorNextStep:   StepModifyAddShardsClusterAddNodes,
	})

	// Step-7 部署Redis
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterDeployRedis,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: deploy.ProcessDeployAll,

		SuccessNextStep: StepModifyAddShardsTaskStepConfigNodes,
		ErrorNextStep:   StepModifyAddShardsClusterDeployRedis},

		workflow.WithStepTimeout(time.Minute*15))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsTaskStepConfigNodes,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(timewindow.AddShardTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyAddShardsClusterSetLocalSlaveof,
		ErrorNextStep:   StepModifyAddShardsTaskStepConfigNodes})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterSetLocalSlaveof,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: handover.ProcessLocalSlaveOf,

		SuccessNextStep: StepModifyAddShardsClusterSetGlobalSlaveof,
		ErrorNextStep:   StepModifyAddShardsClusterSetLocalSlaveof})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterSetGlobalSlaveof,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: gmaster.ProcessGlobalSlaveOf,

		SuccessNextStep: StepModifyAddShardsClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepModifyAddShardsClusterSetGlobalSlaveof})

	// 包管理todo 检查能否加在这里
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterUpdateTlsConfIfNeeded,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: util.UpdateTLSConfIfNeededForNew,

		SuccessNextStep: StepModifyAddShardsClusterDeployFilebeat,
		ErrorNextStep:   StepModifyAddShardsClusterUpdateTlsConfIfNeeded,
	})

	// Step-7 部署filebeat
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterDeployFilebeat,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: deploy.ProcessDeployFilebeat,

		SuccessNextStep: StepModifyAddShardsClusterSetRs,
		ErrorNextStep:   StepModifyAddShardsClusterDeployFilebeat},

		workflow.WithStepSplitHandler(util.GetToCreateRedisIds),
		workflow.WithStepTimeout(time.Minute*15))

	// Step-10 绑定新的proxy rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterSetRs,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: blb.ProcessSetProxyRsForModify,

		SuccessNextStep: StepModifyAddShardsClusterSetMcpackRs,
		ErrorNextStep:   StepModifyAddShardsClusterSetRs,
	})

	// Step-10 绑定新的proxy rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterSetMcpackRs,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: blb.ProcessSetProxyRsForMcpack,

		SuccessNextStep: StepModifyAddShardsClusterSetBnsInstance,
		ErrorNextStep:   StepModifyAddShardsClusterSetMcpackRs,
	})

	// Step-10 绑定新的proxy bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterSetBnsInstance,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: bns.ProcessSetBnsInstances,

		SuccessNextStep: StepModifyAddShardsGolbalBeforeMigration,
		ErrorNextStep:   StepModifyAddShardsClusterSetBnsInstance,
	})

	// Step-10 热活实例组--启动migrate任务检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsGolbalBeforeMigration,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: gmaster.ProcessCheckModifyStatusBeforeMigration,

		SuccessNextStep: StepModifyAddShardsClusterStartMigrateTask,
		ErrorNextStep:   StepModifyAddShardsGolbalBeforeMigration,
	})

	// Step-10 发起迁移数据任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterStartMigrateTask,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: metaserver.ProcessStartMigrateTask,

		SuccessNextStep: StepModifyAddShardsGlobalMigrateSync,
		ErrorNextStep:   StepModifyAddShardsClusterStartMigrateTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsGlobalMigrateSync,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     metaserver.ProcessGlobalMigrateSync,
		SuccessNextStep: StepModifyAddShardsClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyAddShardsGlobalMigrateSync,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterUpdateAppTopologyInXmaster,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: xmaster.ProcessUpdateAppTopologyInXmaster,

		SuccessNextStep: StepModifyAddShardsInitBcmResource,
		ErrorNextStep:   StepModifyAddShardsClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsInitBcmResource,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepModifyAddShardsInitSyncBcmResource,
		ErrorNextStep:   StepModifyAddShardsInitBcmResource,
	})

	// 创建多活组监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsInitSyncBcmResource,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     bcm.ProcessSyncGroupBcmResource,
		SuccessNextStep: StepModifyAddShardsClusterAddToBcmGroup,
		ErrorNextStep:   StepModifyAddShardsInitSyncBcmResource,
	})

	// 添加到bcm实例组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterAddToBcmGroup,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     bcm.ProcessShardsAddToGroup,
		SuccessNextStep: StepModifyAddShardsInitOpMonitor,
		ErrorNextStep:   StepModifyAddShardsClusterAddToBcmGroup,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsInitOpMonitor,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyAddShardsClusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifyAddShardsInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepModifyAddShardsClusterPushFlag,
		ErrorNextStep:   StepModifyAddShardsClusterPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterPushFlag,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: pushflag.ProcessUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifyAddShardsClusterSetHtGrpSlaveTaskSuccess,
		ErrorNextStep:   StepModifyAddShardsClusterPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterSetHtGrpSlaveTaskSuccess,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     timewindow.ProcessSetHtGrpSlaveTaskSuccess,
		SuccessNextStep: StepModifyAddShardsClusterDeleteAOFBackupPolicy,
		ErrorNextStep:   StepModifyAddShardsClusterSetHtGrpSlaveTaskSuccess,
	})

	// check 并删除 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterDeleteAOFBackupPolicy,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOF,
		SuccessNextStep: StepModifyAddShardsClusterSuccessCallback,
		ErrorNextStep:   StepModifyAddShardsClusterDeleteAOFBackupPolicy,
	})

	// Step-10 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAddShardsClusterSuccessCallback,
		Workflow: WorkflowModifyAddShardsCluster,

		StepProcess: callback.ProcessModifyNodesSuccCb,

		SuccessNextStep: StepModifyAddShardsClusterAddSuccessEndEvent,
		ErrorNextStep:   StepModifyAddShardsClusterSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterAddSuccessEndEvent,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     event.ProcessAddModifyingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyAddShardsClusterAddSuccessEndEvent,
	})

	// Step-Error-01 热活实例组--标记rollback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsGolbalSetRollback,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     gmaster.ProcessSetRollbackStage,
		SuccessNextStep: StepModifyAddShardsTaskStepError,
		ErrorNextStep:   StepModifyAddShardsGolbalSetRollback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsTaskStepError,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifyAddShardsClusterRollbackReleaseResources,
		ErrorNextStep:   StepModifyAddShardsTaskStepError,
	})

	// Step-Error-01 回滚资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterRollbackReleaseResources,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifyAddShardsClusterRollbackRemoveFromBcmGroup,
		ErrorNextStep:   StepModifyAddShardsClusterAddFailedEndEvent,
	})

	// 从实例组中删除
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterRollbackRemoveFromBcmGroup,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     bcm.ProcessRollbackShardsAddToGroup,
		SuccessNextStep: StepModifyAddShardsClusterRollbackBcmResource,
		ErrorNextStep:   StepModifyAddShardsClusterRollbackRemoveFromBcmGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterRollbackBcmResource,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     bcm.ProcessRollbackBcmResourceWithoutApp,
		SuccessNextStep: StepModifyAddShardsClusterRollbackSyncBcmResource,
		ErrorNextStep:   StepModifyAddShardsClusterRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterRollbackSyncBcmResource,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     bcm.ProcessRollbackSyncGroupBcmResource,
		SuccessNextStep: StepModifyAddShardsClusterRollbackMeta,
		ErrorNextStep:   StepModifyAddShardsClusterRollbackSyncBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterRollbackMeta,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifyAddShardsClusterRollbackCallback,
		ErrorNextStep:   StepModifyAddShardsClusterAddFailedEndEvent,
	})

	// Step-Error-02 回滚Callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterRollbackCallback,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     callback.ProcessModifyNodesErrorCb,
		SuccessNextStep: StepModifyAddShardsGlobalCompleteRollback,
		ErrorNextStep:   StepModifyAddShardsClusterRollbackCallback,
	})

	// Step-Error-03 热活实例组--完成global rollbacl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsGlobalCompleteRollback,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     gmaster.ProcessCompleteRollback,
		SuccessNextStep: StepModifyAddShardsClusterAddFailedEndEvent,
		ErrorNextStep:   StepModifyAddShardsGlobalCompleteRollback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAddShardsClusterAddFailedEndEvent,
		Workflow:        WorkflowModifyAddShardsCluster,
		StepProcess:     event.ProcessAddModifyingFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyAddShardsClusterAddFailedEndEvent,
	})

	/**************************************进行扩容任务*********************************************/
	// Step-01 设置auto balance标志位
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigrateClusterSetAutoBalance,
		Workflow: WorkflowMigrateCluster,

		StepProcess: metaserver.ProcessStartAutoBalance,

		SuccessNextStep: StepMigrateClusterTaskStepRebalance,
		ErrorNextStep:   StepMigrateClusterSetAutoBalance,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigrateClusterTaskStepRebalance,
		Workflow: WorkflowMigrateCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddShardTask, timewindow.StepRebalance, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepMigrateClusterCheckAutoBalance,
		ErrorNextStep:   StepMigrateClusterTaskStepRebalance,
	})

	// Step-02 检查auto balance是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigrateClusterCheckAutoBalance,
		Workflow: WorkflowMigrateCluster,

		StepProcess: metaserver.ProcessCheckAutoBalance,

		SuccessNextStep: StepMigrateGlobalComplete,
		ErrorNextStep:   StepMigrateClusterCheckAutoBalance},

		// 跟metaserver配置的超时保持一致  4h
		workflow.WithStepTimeout(4*60*time.Minute))

	// Step-03 热活实例组--完成热活实例组迁移状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigrateGlobalComplete,
		Workflow: WorkflowMigrateCluster,

		StepProcess: gmaster.ProcessCompleteAddShardsMigration,

		SuccessNextStep: StepMigrateStatusCallback,
		ErrorNextStep:   StepMigrateGlobalComplete})

	// Step-04 回调csmaster，重置migrate status
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigrateStatusCallback,
		Workflow: WorkflowMigrateCluster,

		StepProcess: metaserver.ProcessMigrateStatusCallback,

		SuccessNextStep: StepMigrateCloseTimeWindowTask,
		ErrorNextStep:   StepMigrateStatusCallback})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateCloseTimeWindowTask,
		Workflow:        WorkflowMigrateCluster,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateCloseTimeWindowTask})

	/**************************************进行缩容任务*********************************************/
	// Step-01 发送shrink指令
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkClusterShrink,
		Workflow: WorkflowShrinkCluster,

		StepProcess: metaserver.ProcessShrink,

		SuccessNextStep: StepShrinkClusterTaskStepRebalance,
		ErrorNextStep:   StepShrinkClusterShrink,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkClusterTaskStepRebalance,
		Workflow: WorkflowShrinkCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.DeleteShardTask, timewindow.StepRebalance, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepShrinkClusterCheckShrink,
		ErrorNextStep:   StepShrinkClusterTaskStepRebalance,
	})

	// Step-02 检查shrink是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkClusterCheckShrink,
		Workflow: WorkflowShrinkCluster,

		StepProcess: metaserver.ProcessShrinkCheck,

		SuccessNextStep: StepShrinkGlobalComplete,
		ErrorNextStep:   StepShrinkClusterCheckShrink},

		// 跟metaserver配置的超时保持一致  4h
		workflow.WithStepTimeout(4*60*time.Minute))

	// Step-02 检查shrink是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkGlobalComplete,
		Workflow: WorkflowShrinkCluster,

		StepProcess: gmaster.ProcessSetDelShardMigrationComplete,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepShrinkGlobalComplete})

	/**************************************集群版减少分片*******************************************/

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsInitTaskWindowTask,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepModifyDelShardsGlobalPegaConfSet,
		ErrorNextStep:   StepModifyDelShardsInitTaskWindowTask,
	})
	// Step-01 热活实例组--初始化global索容状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsGlobalPegaConfSet,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     gmaster.ProcessResetPegaHotConfigs,
		SuccessNextStep: StepModifyDelShardsGlobalInit,
		ErrorNextStep:   StepModifyDelShardsGlobalPegaConfSet,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsGlobalInit,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     gmaster.ProcessInitDelShardsModifyStatus,
		SuccessNextStep: StepModifyDelShardsClusterAddStartEvent,
		ErrorNextStep:   StepModifyDelShardsGlobalInit,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterAddStartEvent,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     event.ProcessAddModifyingStartEvent,
		SuccessNextStep: StepModifyDelShardsClusterBuildMeta,
		ErrorNextStep:   StepModifyDelShardsClusterAddStartEvent,
	})

	// Step-01 修改x1元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterBuildMeta,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     buildmeta.ProcessModifyShards,
		SuccessNextStep: StepModifyDelShardsClusterStartShrinkTask,
		ErrorNextStep:   StepModifyDelShardsClusterBuildMeta,
	})

	// Step-01 发送迁移任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterStartShrinkTask,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     metaserver.ProcessStartShrinkTask,
		SuccessNextStep: StepModifyDelShardsGlobalCheckShrink,
		ErrorNextStep:   StepModifyDelShardsClusterStartShrinkTask,
	})

	// Step-01 热活实例组，等待所有迁移任务完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsGlobalCheckShrink,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     gmaster.ProcessCheckDelShardMigrationComplete,
		SuccessNextStep: StepModifyDelShardsClusterMarkShrinkedShards,
		ErrorNextStep:   StepModifyDelShardsGlobalCheckShrink,
	})

	// Step-02 将shrinked shards标记为 toDelete
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterMarkShrinkedShards,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     metaserver.ProcessMarkShrinkedShards,
		SuccessNextStep: StepModifyDelShardsClusterTaskStepDeleteNodes,
		ErrorNextStep:   StepModifyDelShardsClusterMarkShrinkedShards,
		// pega缩容耗时较长,并且加了重试,故将此流程检查超时调长
	}, workflow.WithStepTimeout(24*60*time.Minute), workflow.WithCancellable(true))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyDelShardsClusterTaskStepDeleteNodes,
		Workflow: WorkflowModifyDelShardsCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.DeleteShardTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyDelShardsClusterDelNodes,
		ErrorNextStep:   StepModifyDelShardsClusterTaskStepDeleteNodes,
		// pega缩容耗时较长,并且加了重试,故将此流程检查超时调长
	}, workflow.WithStepTimeout(24*60*time.Minute))

	// Step-03 删除metaserver中节点信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterDelNodes,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepModifyDelShardsClusterDelShards,
		ErrorNextStep:   StepModifyDelShardsClusterDelNodes,
	})

	// Step-04 删除metaserver中分片信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterDelShards,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     metaserver.ProcessDelShards,
		SuccessNextStep: StepModifyDelShardsClusterSetRs,
		ErrorNextStep:   StepModifyDelShardsClusterDelShards,
	})

	// 解绑待删除的proxy rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterSetRs,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     blb.ProcessSetProxyRs,
		SuccessNextStep: StepModifyDelShardsClusterSetMcpackRs,
		ErrorNextStep:   StepModifyDelShardsClusterSetRs,
	})

	// Step-10 解绑待删除的proxy rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterSetMcpackRs,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepModifyDelShardsClusterSetBnsInstance,
		ErrorNextStep:   StepModifyDelShardsClusterSetMcpackRs,
	})

	// Step 删除旧的proxy bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterSetBnsInstance,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: StepModifyDelShardsClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyDelShardsClusterSetBnsInstance,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmasterForShardScaleIn,
		SuccessNextStep: StepModifyDelShardsClusterRemoveFromGroup,
		ErrorNextStep:   StepModifyDelShardsClusterUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterRemoveFromGroup,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     bcm.ProcessShardsRemoveFromGroup,
		SuccessNextStep: StepModifyDelShardsClusterInitBcmResource,
		ErrorNextStep:   StepModifyDelShardsClusterRemoveFromGroup,
	})

	// 删除监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterInitBcmResource,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepModifyDelShardsClusterInitSyncGroupBcmResource,
		ErrorNextStep:   StepModifyDelShardsClusterInitBcmResource,
	})

	// 删除多活组监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterInitSyncGroupBcmResource,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     bcm.ProcessSyncGroupBcmResource,
		SuccessNextStep: StepModifyDelShardsClusterPushFlags,
		ErrorNextStep:   StepModifyDelShardsClusterInitSyncGroupBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterPushFlags,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyDelShardsClusterDeleteOldNodes,
		ErrorNextStep:   StepModifyDelShardsClusterPushFlags,
	})

	// Step-05 释放资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterDeleteOldNodes,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyDelShardsClusterSuccessCallback,
		ErrorNextStep:   StepModifyDelShardsClusterDeleteOldNodes,
	})

	// Step-06 成功Callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterSuccessCallback,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: StepModifyDelShardsGlobalComplete,
		ErrorNextStep:   StepModifyDelShardsClusterSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsGlobalComplete,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     gmaster.ProcessCheckDelShardReleaseResourceComplete,
		SuccessNextStep: StepModifyDelShardsClusterDeleteAOFBackupPolicy,
		ErrorNextStep:   StepModifyDelShardsGlobalComplete,
	})

	// check 并删除 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterDeleteAOFBackupPolicy,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOF,
		SuccessNextStep: StepModifyDelShardsCloseTimeWindowTask,
		ErrorNextStep:   StepModifyDelShardsClusterDeleteAOFBackupPolicy,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsCloseTimeWindowTask,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepModifyDelShardsClusterAddSuccessEndEvent,
		ErrorNextStep:   StepModifyDelShardsCloseTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyDelShardsClusterAddSuccessEndEvent,
		Workflow:        WorkflowModifyDelShardsCluster,
		StepProcess:     event.ProcessAddModifyingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyDelShardsClusterAddSuccessEndEvent,
	})

	/**************************************Pega进行扩容任务*********************************************/
	// Step-01 设置auto balance标志位
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigratePegaClusterSetAutoBalance,
		Workflow: WorkflowMigratePegaCluster,

		StepProcess: metaserver.ProcessStartAutoBalance,

		SuccessNextStep: StepMigratePegaClusterTaskStepRebalance,
		ErrorNextStep:   StepMigratePegaClusterSetAutoBalance,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigratePegaClusterTaskStepRebalance,
		Workflow: WorkflowMigratePegaCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.AddShardTask, timewindow.StepRebalance, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepMigratePegaClusterCheckAutoBalance,
		ErrorNextStep:   StepMigratePegaClusterTaskStepRebalance,
	})

	// Step-02 检查auto balance是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigratePegaClusterCheckAutoBalance,
		Workflow: WorkflowMigratePegaCluster,

		StepProcess: metaserver.ProcessPegaCheckAutoBalance,

		SuccessNextStep: StepMigratePegaGlobalComplete,
		ErrorNextStep:   StepMigratePegaClusterCheckAutoBalance},

		// pega 扩容超时设置为12h
		workflow.WithStepTimeout(12*60*time.Minute))

	// Step-03 热活实例组--完成热活实例组迁移状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigratePegaGlobalComplete,
		Workflow: WorkflowMigratePegaCluster,

		StepProcess: gmaster.ProcessCompleteAddShardsMigration,

		SuccessNextStep: StepMigratePegaStatusCallback,
		ErrorNextStep:   StepMigratePegaGlobalComplete})

	// Step-04 回调csmaster，重置migrate status
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigratePegaStatusCallback,
		Workflow: WorkflowMigratePegaCluster,

		StepProcess: metaserver.ProcessMigrateStatusCallback,

		SuccessNextStep: StepMigratePegaCloseTimeWindowTask,
		ErrorNextStep:   StepMigratePegaStatusCallback})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigratePegaCloseTimeWindowTask,
		Workflow:        WorkflowMigratePegaCluster,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigratePegaCloseTimeWindowTask})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepMigratePegaProcessReacheMaxRetries,
		Workflow: WorkflowMigratePegaCluster,

		StepProcess: metaserver.ProcessReacheMaxRetries,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError})

	/**************************************Pega进行缩容任务*********************************************/
	// Step-01 发送shrink指令
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkPegaClusterShrink,
		Workflow: WorkflowShrinkPegaCluster,

		StepProcess: metaserver.ProcessShrink,

		SuccessNextStep: StepShrinkPegaClusterTaskStepRebalance,
		ErrorNextStep:   StepShrinkPegaClusterShrink,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkPegaClusterTaskStepRebalance,
		Workflow: WorkflowShrinkPegaCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.DeleteShardTask, timewindow.StepRebalance, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepShrinkPegaClusterCheckShrink,
		ErrorNextStep:   StepShrinkPegaClusterTaskStepRebalance,
	})

	// Step-02 检查shrink是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkPegaClusterCheckShrink,
		Workflow: WorkflowShrinkPegaCluster,

		StepProcess: metaserver.ProcessPegaShrinkCheck,

		SuccessNextStep: StepShrinkPegaGlobalComplete,
		ErrorNextStep:   StepShrinkPegaClusterCheckShrink},

		// pega 扩容超时设置为12h
		workflow.WithStepTimeout(12*60*time.Minute))

	// Step-02 检查shrink是否完成
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkPegaGlobalComplete,
		Workflow: WorkflowShrinkPegaCluster,

		StepProcess: gmaster.ProcessSetDelShardMigrationComplete,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepShrinkPegaGlobalComplete})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepShrinkPegaProcessReacheMaxRetries,
		Workflow: WorkflowShrinkPegaCluster,

		StepProcess: metaserver.ProcessReacheMaxRetries,

		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError})
}
