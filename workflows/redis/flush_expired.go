package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/flush"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"time"
)

const (
	WorkflowFlushExpired            = "scs-flush-expire-app"
	StepFlushExpiredSetTaskID       = "scs-flush-expire-set-task-id"
	StepFlushExpiredTaskStepExecute = "scs-flush-expire-task-step-execute"
	StepFlushExpiredExecute         = "scs-flush-expire-execute"
	StepFlushExpiredTaskStepSuccess = "scs-flush-expire-task-step-success"
	StepFlushExpiredFinish          = "scs-flush-expire-finish"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushExpiredSetTaskID,
		Workflow:        WorkflowFlushExpired,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepFlushExpiredTaskStepExecute,
		ErrorNextStep:   StepFlushExpiredSetTaskID,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepFlushExpiredTaskStepExecute,
		Workflow: WorkflowFlushExpired,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.FlushExpired, timewindow.StepFlushExpired, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepFlushExpiredExecute,
		ErrorNextStep:   StepFlushExpiredTaskStepExecute,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushExpiredExecute,
		Workflow:        WorkflowFlushExpired,
		StepProcess:     flush.ProcessFlushExpired,
		SuccessNextStep: StepFlushExpiredTaskStepSuccess,
		ErrorNextStep:   StepFlushExpiredExecute,
	}, workflow.WithStepTimeout(24*time.Hour))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushExpiredTaskStepSuccess,
		Workflow:        WorkflowFlushExpired,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepFlushExpiredFinish,
		ErrorNextStep:   StepFlushExpiredTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushExpiredFinish,
		Workflow:        WorkflowFlushExpired,
		StepProcess:     flush.ProcessFinishFlushExpired,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepFlushExpiredFinish,
	})
}
