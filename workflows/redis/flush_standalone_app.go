/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* flush_standalone_app.go - workflow for flush standalone app */

/*
Modification History
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
进行清除数据操作（RedisState::flushing_cluster）

Parameters
{
	"app_id": "scs-bj-nxewpztnsreg",
	"db_index": 0
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/flush"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowFlushStandalone                                 = "scs-flush-standalone-app"
	StepFlushStandaloneBlockPingAvaliableMonitorInXmaster   = "scs-flush-standalone-app-block-ping-avaliable-monitor-in-xmaster"
	StepFlushStandaloneFlush                                = "scs-flush-standalone-app-flush"
	StepFlushStandaloneWaitSlaveFlush                       = "scs-flush-standalone-app-wait-slave-flush"
	StepFlushStandaloneCheckSync                            = "scs-flush-standalone-app-check-sync"
	StepFlushStandaloneUnblockPingAvaliableMonitorInXmaster = "scs-flush-standalone-app-unblock-ping-avaliable-monitor-in-xmaster"
	StepFlushStandaloneFlushCallback                        = "scs-flush-standalone-app-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushStandaloneBlockPingAvaliableMonitorInXmaster,
		Workflow:        WorkflowFlushStandalone,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterPingAvailable,
		SuccessNextStep: StepFlushStandaloneFlush,
		ErrorNextStep:   StepFlushStandaloneBlockPingAvaliableMonitorInXmaster})

	// Step-1 清除数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushStandaloneFlush,
		Workflow:        WorkflowFlushStandalone,
		StepProcess:     flush.ProcessFlushStandalone,
		SuccessNextStep: StepFlushStandaloneWaitSlaveFlush,
		ErrorNextStep:   StepFlushStandaloneFlush},
		workflow.WithStepTimeout(30*time.Minute),
		workflow.WithMaxReentry(1, StepFlushStandaloneWaitSlaveFlush), //清空数据，最多执行3次（设置为1，实际执行3次）
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushStandaloneWaitSlaveFlush,
		Workflow:        WorkflowFlushStandalone,
		StepProcess:     flush.ProcessWaitSlaveFlushComplete,
		SuccessNextStep: StepFlushStandaloneCheckSync,
		ErrorNextStep:   StepFlushStandaloneCheckSync},
		workflow.WithStepTimeout(30*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushStandaloneCheckSync,
		Workflow:        WorkflowFlushStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepFlushStandaloneUnblockPingAvaliableMonitorInXmaster,
		ErrorNextStep:   StepFlushStandaloneCheckSync},
		workflow.WithStepTimeout(15*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushStandaloneUnblockPingAvaliableMonitorInXmaster,
		Workflow:        WorkflowFlushStandalone,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: StepFlushStandaloneFlushCallback,
		ErrorNextStep:   StepFlushStandaloneUnblockPingAvaliableMonitorInXmaster})

	// Step-2 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepFlushStandaloneFlushCallback,
		Workflow: WorkflowFlushStandalone,

		StepProcess: callback.ProcessGeneralSuccessCb,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepFlushStandaloneFlushCallback})
}
