package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/failover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/recover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

/*
克隆恢复参数:

	原集群app_id:
	当前集群app_id:
	备份集id:
*/

/*
1、build_meta

	集群创建完成后增加一步，判断当前集群状态，如果backup_status=BACKUP_CLUSTER_WAIT_RECOVER，
	则该集群为数据恢复集群，则创建 recover_in_new_cluster 的任务。
	任务参数为:
		原集群app_id:
		当前集群app_id:
		备份集id:
		数据恢复类型: rdb恢复（对比时间点恢复）


	if ((table->backup_status() == (int)BACKUP_CLUSTER_WAIT_RECOVER )
	&& (table->status() == CACHE_CLUSTER_RUNNING ||
	table->status() == CACHE_CLUSTER_MODIFIED_FAILED ||
	table->status() == CACHE_CLUSTER_FLUSH_FAILED ||
	table->status() == CACHE_CLUSTER_RECOVER_FAILED ||
	table->status() == CACHE_CLUSTER_PAUSED ||
	table->status() == CACHE_CLUSTER_EXCHANGE_FAILED)) {

2、clone_cluster_backup_record
	2.1、克隆原集群slot分布至新集群
		clone_update_slot(desc_cluster_id, src_cluster_id,src_inst_ids_and_shard_ids, desc_inst_ids_and_shard_ids);
	2.2、克隆原集群备份信息至新集群
		BackupRecordModel* model = new BackupRecordModel(

3、跟原地恢复保持一致

4、成功回调:
	status : 5
	backup_status : 0

*/

const (
	WorkflowScsRecoverInNewClusterByFlashback             = "scs-recover-in-new-cluster-by-flashback"
	StepRecoverInNewClusterByFlashbackTaskStepPrepareData = "scs-recover-in-new-cluster-by-flashback-task-step-prepare-data"
	StepRecoverInNewClusterByFlashbackBuildMeta           = "scs-recover-in-new-cluster-by-flashback-build-meta"
	StepRecoverInNewClusterByFlashbackCloneTopo           = "scs-recover-in-new-cluster-by-flashback-clone-topo"
	StepRecoverInNewClusterByFlashbackTaskStepRecoverData = "scs-recover-in-new-cluster-by-flashback-task-step-recover-data"
	StepRecoverInNewClusterByFlashbackDisableFailover     = "scs-recover-in-new-cluster-by-flashback-disable-failover"
	StepRecoverInNewClusterByFlashbackPrework             = "scs-recover-in-new-cluster-by-flashback-prework"
	StepRecoverInNewClusterByFlashbackDownloadData        = "scs-recover-in-new-cluster-by-flashback-download-data"
	StepRecoverInNewClusterByFlashbackLoadData            = "scs-recover-in-new-cluster-by-flashback-load-data"
	StepRecoverInNewClusterByFlashbackCheckAllSync        = "scs-recover-in-new-cluster-by-flashback-check-all-sync"
	StepRecoverInNewClusterByFlashbackEnableFailover      = "scs-recover-in-new-cluster-by-flashback-enable-failover"
	StepRecoverInNewClusterByFlashbackTaskStepSuccess     = "scs-recover-in-new-cluster-by-flashback-task-step-success"
	StepRecoverInNewClusterByFlashbackCallBack            = "scs-recover-in-new-cluster-by-flashback-cb" // 将cache_cluster status 从恢复中改成运行中
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverInNewClusterByFlashbackTaskStepPrepareData,
		Workflow: WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepPrepareData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverInNewClusterByFlashbackBuildMeta,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackTaskStepPrepareData,
	})

	// 增加build_meta,更新cache_cluster status = 13[暂定],此值需要重新定义，并且联动list接口进行状态展示
	// Step-1 集群状态维护
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackBuildMeta,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterBuildMeta,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackCloneTopo,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackBuildMeta,
	})

	// Step-2 克隆集群拓扑
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackCloneTopo,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterCloneTopo,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackTaskStepRecoverData,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackCloneTopo,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverInNewClusterByFlashbackTaskStepRecoverData,
		Workflow: WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepRecoverData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverInNewClusterByFlashbackDisableFailover,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackTaskStepRecoverData,
	})

	// Step 关闭自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackDisableFailover,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     failover.ProcessDisableFailover,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackPrework,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackDisableFailover,
	})

	// Step dispatch task(停止主库进程, 清理目录)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackPrework,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     recover.ProcessFlashbackRecoverRedisInNewClusterPrework,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackDownloadData,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackPrework},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// Step 下载备份
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackDownloadData,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     recover.ProcessFlashbackRecoverRedisInNewClusterDownloadData,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackLoadData,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackDownloadData,
	})

	// Step-5 dispatch task(拉起进程)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackLoadData,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     recover.ProcessFlashbackRecoverRedisInNewClusterLoadData,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackCheckAllSync,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackLoadData},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// Step 检查主从同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackCheckAllSync,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackEnableFailover,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackCheckAllSync},
		workflow.WithStepTimeout(15*time.Minute))

	// Step 开启自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackEnableFailover,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     failover.ProcessEnableFailover,
		SuccessNextStep: StepRecoverInNewClusterByFlashbackTaskStepSuccess,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackEnableFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackTaskStepSuccess,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRecoverInNewClusterByFlashbackCallBack,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackTaskStepSuccess,
	})

	// Step-6 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterByFlashbackCallBack,
		Workflow:        WorkflowScsRecoverInNewClusterByFlashback,
		StepProcess:     callback.ProcessRecoverInNewClusterForFlashbackCallBack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverInNewClusterByFlashbackCallBack,
	})
}
