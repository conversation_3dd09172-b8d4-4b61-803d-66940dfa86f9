/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_create_sync_group.go
 * <AUTHOR>
 * @date 2022/09/07 10:54:58
 * @brief local create sync group
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/precheck"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalCreateStandaloneGroup will create standalone group
const (
	WorkFlowLocalCreateSyncGroup                           = "scs-local-create-sync-group"
	WorkFlowLocalCreateSyncGroupPreCheck                   = "scs-local-create-sync-group-pre-check"
	WorkFlowLocalCreateSyncGroupBlockMonitorInXmaster      = "scs-local-create-sync-group-block-monitor-in-xmaster"
	WorkFlowLocalCreateSyncGroupOpenOpHeader               = "scs-local-create-sync-group-open-op-header"
	WorkFlowLocalCreateSyncGroupOpenSyncConfig             = "scs-local-create-sync-group-open-sync-config"
	WorkFlowLocalCreateSyncGroupAddSyncChannel             = "scs-local-create-sync-group-add-sync-channel"
	WorkFlowLocalCreateSyncGroupModifyWhiteList            = "scs-local-create-sync-group-modify-whitelist"
	WorkFlowLocalCreateSyncGroupRebootBuildMeta            = "scs-local-create-sync-group-reboot-build-meta"
	WorkFlowLocalCreateSyncGroupRebootUpgradeSlaves        = "scs-local-create-sync-group-reboot-update-slaves"
	WorkFlowLocalCreateSyncGroupRebootCheckSync            = "scs-local-create-sync-group-reboot-check-sync"
	WorkFlowLocalCreateSyncGroupRebootHandOver             = "scs-local-create-sync-group-reboot-handover"
	WorkFlowLocalCreateSyncGroupRebootUpgradeMaster        = "scs-local-create-sync-group-reboot-update-master"
	WorkFlowLocalCreateSyncGroupUpdateAppTopologyInXmaster = "scs-local-create-sync-group-update-app-topo-in-xmaster"
	WorkFlowLocalCreateSyncGroupUnblockMonitorInXmaster    = "scs-local-create-sync-group-unblock-monitor-in-xmaster"
	WorkFlowLocalCreateSyncGroupStepSuccessCb              = "scs-local-create-sync-group-success-cb"
)

func init() {
	// 0 前置检查，明显无需重试的情况快速失败并保存sync-group-id
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupPreCheck,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     precheck.ProcessPrecheckForCreateSyncGroup,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupBlockMonitorInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupOpenOpHeader,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupBlockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupOpenOpHeader,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     syncgroup.ProcessOpenOpHeader,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupOpenSyncConfig,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupOpenOpHeader,
	})

	// 设置support_multi_active参数为yes
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupOpenSyncConfig,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     syncgroup.ProcessOpenSyncConfig,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupAddSyncChannel,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupOpenSyncConfig,
	})

	// 增加同步通道
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupAddSyncChannel,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     syncgroup.ProcessAddSyncChannel,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupModifyWhiteList,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupAddSyncChannel,
	})

	// 修改集群白名单为0.0.0.0/0
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupModifyWhiteList,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     syncgroup.ProcessModifyWhiteList,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupRebootBuildMeta,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupModifyWhiteList,
	})

	// 重启集群（使用升级替代）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupRebootBuildMeta,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForSyncGroupUpgrade,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupRebootUpgradeSlaves,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupRebootBuildMeta,
	})

	// Step-1 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupRebootUpgradeSlaves,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupRebootCheckSync,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupRebootUpgradeSlaves},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-2 等待数据同步后、执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupRebootCheckSync,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupRebootHandOver,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupRebootCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupRebootHandOver,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     handover.ProcessHandoverClusterForRestarting,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupRebootUpgradeMaster,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupRebootHandOver,
	})

	// Step-4 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupRebootUpgradeMaster,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupRebootUpgradeMaster},

		workflow.WithStepTimeout(15*time.Minute))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalCreateSyncGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupUnblockMonitorInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalCreateSyncGroupStepSuccessCb,
		Workflow:        WorkFlowLocalCreateSyncGroup,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalCreateSyncGroupStepSuccessCb,
	})
}
