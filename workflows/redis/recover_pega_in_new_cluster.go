package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/failover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/recover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

/*
克隆恢复参数:

	原集群app_id:
	当前集群app_id:
	备份集id:
*/

/*
1、build_meta

	集群创建完成后增加一步，判断当前集群状态，如果backup_status=BACKUP_CLUSTER_WAIT_RECOVER，
	则该集群为数据恢复集群，则创建 recover_in_new_cluster 的任务。
	任务参数为:
		原集群app_id:
		当前集群app_id:
		备份集id:
		数据恢复类型: rdb恢复（对比时间点恢复）


	if ((table->backup_status() == (int)BACKUP_CLUSTER_WAIT_RECOVER )
	&& (table->status() == CACHE_CLUSTER_RUNNING ||
	table->status() == CACHE_CLUSTER_MODIFIED_FAILED ||
	table->status() == CACHE_CLUSTER_FLUSH_FAILED ||
	table->status() == CACHE_CLUSTER_RECOVER_FAILED ||
	table->status() == CACHE_CLUSTER_PAUSED ||
	table->status() == CACHE_CLUSTER_EXCHANGE_FAILED)) {

2、clone_cluster_backup_record
	2.1、克隆原集群slot分布至新集群
		clone_update_slot(desc_cluster_id, src_cluster_id,src_inst_ids_and_shard_ids, desc_inst_ids_and_shard_ids);
	2.2、克隆原集群备份信息至新集群
		BackupRecordModel* model = new BackupRecordModel(

3、跟原地恢复保持一致

4、成功回调:
	status : 5
	backup_status : 0

*/

const (
	WorkflowScsRecoverPegaInNewCluster             = "scs-recover-pega-in-new-cluster"
	StepRecoverPegaInNewClusterTaskStepPrepareData = "scs-recover-pega-in-new-cluster-task-step-prepare-data"
	StepRecoverPegaInNewClusterBuildMeta           = "scs-recover-pega-in-new-cluster-build-meta"
	StepRecoverPegaInNewClusterCloneTopo           = "scs-recover-pega-in-new-cluster-clone-topo"
	StepRecoverPegaInNewClusterUpdateRdbUrl        = "scs-recover-pega-in-new-cluster-update-rdb-url"
	StepRecoverPegaInNewClusterCloneBackupRecord   = "scs-recover-pega-in-new-cluster-clone-backup-record"
	StepRecoverPegaInNewClusterTaskStepRecoverData = "scs-recover-pega-in-new-cluster-task-step-recover-data"
	StepRecoverPegaInNewClusterCreateNewCDS        = "scs-recover-pega-in-new-cluster-create-new-cds"
	StepRecoverPegaInNewClusterDisableFailover     = "scs-recover-pega-in-new-cluster-disable-failover"
	StepRecoverPegaInNewClusterUseNewCDS           = "scs-recover-pega-in-new-cluster-use-new-cds"
	StepRecoverPegaInNewClusterEnableFailover      = "scs-recover-pega-in-new-cluster-enable-failover"
	StepRecoverPegaInNewClusterDeleteOldCDS        = "scs-recover-pega-in-new-cluster-delete-old-cds"
	StepRecoverPegaInNewClusterCheckAllSync        = "scs-recover-pega-in-new-cluster-check-all-sync"
	StepRecoverPegaInNewClusterTaskStepSuccess     = "scs-recover-pega-in-new-cluster-task-step-success"
	StepRecoverPegaInNewClusterCallBack            = "scs-recover-pega-in-new-cluster-cb" // 将cache_cluster status 从恢复中改成运行中
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverPegaInNewClusterTaskStepPrepareData,
		Workflow: WorkflowScsRecoverPegaInNewCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepPrepareData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverPegaInNewClusterBuildMeta,
		ErrorNextStep:   StepRecoverPegaInNewClusterTaskStepPrepareData,
	})

	// 增加build_meta,更新cache_cluster status = 13[暂定],此值需要重新定义，并且联动list接口进行状态展示
	// Step-1 集群状态维护
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterBuildMeta,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterBuildMeta,
		SuccessNextStep: StepRecoverPegaInNewClusterCloneTopo,
		ErrorNextStep:   StepRecoverPegaInNewClusterBuildMeta,
	})

	// Step-2 克隆集群拓扑
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterCloneTopo,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterCloneTopo,
		SuccessNextStep: StepRecoverPegaInNewClusterCloneBackupRecord,
		ErrorNextStep:   StepRecoverPegaInNewClusterCloneTopo,
	})

	// Step-4 构建新集群的备份记录
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterCloneBackupRecord,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterCloneBackupRecord,
		SuccessNextStep: StepRecoverPegaInNewClusterTaskStepRecoverData,
		ErrorNextStep:   StepRecoverPegaInNewClusterCloneBackupRecord,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverPegaInNewClusterTaskStepRecoverData,
		Workflow: WorkflowScsRecoverPegaInNewCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepRecoverData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverPegaInNewClusterCreateNewCDS,
		ErrorNextStep:   StepRecoverPegaInNewClusterTaskStepRecoverData,
	})

	// Step create new cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterCreateNewCDS,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     recover.ProcessSnapshotRecoverPegaInNewAppCreateNewCDS,
		SuccessNextStep: StepRecoverPegaInNewClusterDisableFailover,
		ErrorNextStep:   StepRecoverPegaInNewClusterCreateNewCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// Step 关闭自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterDisableFailover,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     failover.ProcessDisableFailover,
		SuccessNextStep: StepRecoverPegaInNewClusterUseNewCDS,
		ErrorNextStep:   StepRecoverPegaInNewClusterDisableFailover,
	})

	// Step use new cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterUseNewCDS,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     recover.ProcessSnapshotRecoverPegaInNewAppUseNewCDS,
		SuccessNextStep: StepRecoverPegaInNewClusterEnableFailover,
		ErrorNextStep:   StepRecoverPegaInNewClusterUseNewCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// Step 开启自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterEnableFailover,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     failover.ProcessEnableFailover,
		SuccessNextStep: StepRecoverPegaInNewClusterDeleteOldCDS,
		ErrorNextStep:   StepRecoverPegaInNewClusterEnableFailover,
	})

	// Step delete old cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterDeleteOldCDS,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     recover.ProcessSnapshotRecoverPegaInNewAppDeleteOldCDS,
		SuccessNextStep: StepRecoverPegaInNewClusterCheckAllSync,
		ErrorNextStep:   StepRecoverPegaInNewClusterDeleteOldCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterCheckAllSync,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRecoverPegaInNewClusterTaskStepSuccess,
		ErrorNextStep:   StepRecoverPegaInNewClusterCheckAllSync},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterTaskStepSuccess,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRecoverPegaInNewClusterCallBack,
		ErrorNextStep:   StepRecoverPegaInNewClusterTaskStepSuccess,
	})

	// Step-6 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInNewClusterCallBack,
		Workflow:        WorkflowScsRecoverPegaInNewCluster,
		StepProcess:     callback.ProcessRecoverInNewClusterCallBack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverPegaInNewClusterCallBack,
	})
}
