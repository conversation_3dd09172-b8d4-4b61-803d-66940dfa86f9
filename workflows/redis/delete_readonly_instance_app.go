/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_readonly_instance_app.go
 * <AUTHOR>
 * @date 2022/05/16 18:06:09
 * @brief delete readonly instances

 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowDeleteReadonlyInstance                       = "scs-delete-readonly-instance-app"
	StepDeleteReadonlyInstanceBuildMeta                  = "scs-delete-readonly-instance-buildmeta"
	StepDeleteReadonlyInstanceTaskStepReleaseNodes       = "scs-delete-readonly-instance-task-step-release-nodes"
	StepDeleteReadonlyInstanceUnbindRs                   = "scs-delete-readonly-instance-unbind-rs"
	StepDeleteReadonlyInstanceUnbindEntranceRs           = "scs-delete-readonly-instance-unbind-entrance-rs"
	StepDeleteReadonlyInstanceRemoveFromBcmGroup         = "scs-delete-readonly-instance-remove-from-bcm-group"
	StepDeleteReadonlyInstanceDeleteBcmResource          = "scs-delete-readonly-instance-delete-bcm-resource"
	StepDeleteReadonlyInstanceDeleteResource             = "scs-delete-readonly-instance-delete-resource"
	StepDeleteReadonlyInstanceDeleteInstance             = "scs-delete-readonly-instance-delete-instance"
	StepDeleteReadonlyInstanceUpdateAppTopologyInXmaster = "scs-delete-readonly-instance-update-app-topo-in-xmaster"
	StepDeleteReadonlyInstanceTaskStepSuccess            = "scs-delete-readonly-instance-task-step-success"
	StepDeleteReadonlyInstanceCallback                   = "scs-delete-readonly-instance-callback"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceBuildMeta,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     buildmeta.ProcessBuildMetaForDeletingRoInstance,
		SuccessNextStep: StepDeleteReadonlyInstanceTaskStepReleaseNodes,
		ErrorNextStep:   StepDeleteReadonlyInstanceBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepDeleteReadonlyInstanceTaskStepReleaseNodes,
		Workflow: WorkflowDeleteReadonlyInstance,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.DeleteReadonlyTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepDeleteReadonlyInstanceUnbindRs,
		ErrorNextStep:   StepDeleteReadonlyInstanceTaskStepReleaseNodes,
	})

	// 解绑只读组blb rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceUnbindRs,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     blb.ProcessReadonlyInstanceUnbindRs,
		SuccessNextStep: StepDeleteReadonlyInstanceUnbindEntranceRs,
		ErrorNextStep:   StepDeleteReadonlyInstanceUnbindRs},

		workflow.WithMaxReentry(2, StepDeleteReadonlyInstanceUnbindEntranceRs))

	// 解绑blb entrance rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceUnbindEntranceRs,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepDeleteReadonlyInstanceRemoveFromBcmGroup,
		ErrorNextStep:   StepDeleteReadonlyInstanceUnbindEntranceRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceRemoveFromBcmGroup,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     bcm.ProcessRoRemoveFromGroup,
		SuccessNextStep: StepDeleteReadonlyInstanceDeleteBcmResource,
		ErrorNextStep:   StepDeleteReadonlyInstanceRemoveFromBcmGroup,
	})

	// 删除BCM资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceDeleteBcmResource,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepDeleteReadonlyInstanceDeleteResource,
		ErrorNextStep:   StepDeleteReadonlyInstanceDeleteBcmResource})

	// 删除资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceDeleteResource,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     delresource.ProcessDelBccResourcesForRo,
		SuccessNextStep: StepDeleteReadonlyInstanceDeleteInstance,
		ErrorNextStep:   StepDeleteReadonlyInstanceDeleteResource},

		workflow.WithMaxReentry(2, StepDeleteReadonlyInstanceDeleteInstance))

	// 删除实例
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceDeleteInstance,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     delresource.ProcessDeleteToDeleteRoNodes,
		SuccessNextStep: StepDeleteReadonlyInstanceUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepDeleteReadonlyInstanceDeleteInstance},

		workflow.WithMaxReentry(2, StepDeleteReadonlyInstanceUpdateAppTopologyInXmaster))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceUpdateAppTopologyInXmaster,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepDeleteReadonlyInstanceTaskStepSuccess,
		ErrorNextStep:   StepDeleteReadonlyInstanceUpdateAppTopologyInXmaster},

		workflow.WithMaxReentry(5, StepDeleteReadonlyInstanceCallback))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceTaskStepSuccess,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepDeleteReadonlyInstanceCallback,
		ErrorNextStep:   StepDeleteReadonlyInstanceTaskStepSuccess,
	})

	// 调用Csmaster接口，完成Csmaster相关元数据删除
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteReadonlyInstanceCallback,
		Workflow:        WorkflowDeleteReadonlyInstance,
		StepProcess:     callback.ProcessDeleteRoInstanceCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteReadonlyInstanceCallback,
	})
}
