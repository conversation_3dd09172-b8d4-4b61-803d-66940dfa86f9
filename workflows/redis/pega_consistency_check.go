package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/consistencycheck"
)

const (
	WorkflowPegaConsistencyCheck     = "scs-pega-consistency-check"
	StepPegaConsistencyCheckSnapShot = "scs-pega-consistency-check-snapshot"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPegaConsistencyCheckSnapShot,
		Workflow:        WorkflowPegaConsistencyCheck,
		StepProcess:     consistencycheck.ProcessPegaConsistencySnapShot,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPegaConsistencyCheckSnapShot,
	})
}
