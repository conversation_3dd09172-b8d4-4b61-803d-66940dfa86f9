/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/21 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_join_sync_data_group.go
 * <AUTHOR>
 * @date 2022/11/21 16:31:28
 * @brief 选中的集群进行相关数据添加
 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
)

// WorkFlowLocalJoinSyncDataGroup will join sync group to sync data
const (
	WorkFlowLocalJoinSyncDataGroup                = "scs-local-join-sync-data-group"
	WorkFlowLocalJoinSyncDataGroupBGSAVE          = "scs-local-join-sync-group-bgsave"
	WorkFlowLocalJoinSyncDataGroupAddSyncTask     = "scs-local-join-sync-group-add-sync-task"
	WorkFlowLocalJoinSyncDataGroupAddSyncChannel  = "scs-local-join-sync-group-add-sync-channel"
	WorkFlowLocalJoinSyncDataGroupWaitRDBSyncDone = "scs-local-join-sync-data-group-wait-rdb-sync-done"
	WorkFlowLocalJoinSyncDataGroupStepSuccessCb   = "scs-local-join-sync-group-success-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncDataGroupBGSAVE,
		Workflow:        WorkFlowLocalJoinSyncDataGroup,
		StepProcess:     syncgroup.CallSeedClusterToBGSAVE,
		SuccessNextStep: WorkFlowLocalJoinSyncDataGroupAddSyncTask,
		ErrorNextStep:   WorkFlowLocalJoinSyncDataGroupBGSAVE,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncDataGroupAddSyncTask,
		Workflow:        WorkFlowLocalJoinSyncDataGroup,
		StepProcess:     syncgroup.ProcessAddSyncTask,
		SuccessNextStep: WorkFlowLocalJoinSyncDataGroupAddSyncChannel,
		ErrorNextStep:   WorkFlowLocalJoinSyncDataGroupAddSyncTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncDataGroupAddSyncChannel,
		Workflow:        WorkFlowLocalJoinSyncDataGroup,
		StepProcess:     syncgroup.ProcessJoinSyncChannel,
		SuccessNextStep: WorkFlowLocalJoinSyncDataGroupWaitRDBSyncDone,
		ErrorNextStep:   WorkFlowLocalJoinSyncDataGroupAddSyncChannel,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncDataGroupWaitRDBSyncDone,
		Workflow:        WorkFlowLocalJoinSyncDataGroup,
		StepProcess:     syncgroup.ProcessCheckRdbSyncTask,
		SuccessNextStep: WorkFlowLocalJoinSyncDataGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalJoinSyncDataGroupWaitRDBSyncDone,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinSyncDataGroupStepSuccessCb,
		Workflow:        WorkFlowLocalJoinSyncDataGroup,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalJoinSyncDataGroupStepSuccessCb,
	})
}
