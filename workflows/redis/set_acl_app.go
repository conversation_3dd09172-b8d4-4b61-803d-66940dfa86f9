/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
执行修改密码、修改acl（NewMasterServiceImpl::acl_user_actions）
// shangshuai

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"AclParams"  ...
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
)

const (
	WorkflowScsSetAcl      = "scs-set-acl"
	StepScsSetAclBuildMeta = "scs-set-acl-build-meta"
	StepScsSetAclDoSetAcl  = "scs-set-acl-do-set-acl"
)

func init() {
	// Step-1 执行acl的修改
	// 开发量：
	// (1) Csmaster中创建相关任务
	// (2) 根据请求参数更新acl数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepScsSetAclBuildMeta,
		Workflow:        WorkflowScsSetAcl,
		StepProcess:     buildmeta.ProcessBuildMetaForSetAcl,
		SuccessNextStep: StepScsSetAclDoSetAcl,
		ErrorNextStep:   StepScsSetAclBuildMeta,
	})

	// Step-2 执行acl的修改
	// 开发量：
	// （1）Xagent修改密码、acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepScsSetAclDoSetAcl,
		Workflow:        WorkflowScsSetAcl,
		StepProcess:     acl.ProcessUpdateAcl,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepScsSetAclDoSetAcl,
	})
}
