/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* flush_cluster_app.go - workflow for flush bdrp app */

/*
Modification History
--------------------
2022/5/19, by <PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
进行清除数据操作（RedisState::flushing_cluster）

Parameters
{
	"app_id": "scs-bj-nxewpztnsreg",
	"db_index": 0
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/flush"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowFlushCluster                                 = "scs-flush-cluster-app"
	StepFlushClusterBlockPingAvaliableMonitorInXmaster   = "scs-flush-block-ping-avalible-monitor-in-xmaster"
	StepFlushClusterFlush                                = "scs-flush-cluster-flush"
	StepFlushClusterWaitSlaveFlush                       = "scs-flush-cluster-wait-slave-flush"
	StepFlushClusterCheckSync                            = "scs-flush-cluster-check-sync"
	StepFlushClusterUnblockPingAvaliableMonitorInXmaster = "scs-flush-unblock-ping-avalible-monitor-in-xmaster"
	StepFlushClusterFlushCallback                        = "scs-flush-cluster-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushClusterBlockPingAvaliableMonitorInXmaster,
		Workflow:        WorkflowFlushCluster,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterPingAvailable,
		SuccessNextStep: StepFlushClusterFlush,
		ErrorNextStep:   StepFlushClusterBlockPingAvaliableMonitorInXmaster})

	// Step-01 清除所有Redis的数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushClusterFlush,
		Workflow:        WorkflowFlushCluster,
		StepProcess:     flush.ProcessFlushCluster,
		SuccessNextStep: StepFlushClusterWaitSlaveFlush,
		ErrorNextStep:   StepFlushClusterFlush},
		workflow.WithStepTimeout(30*time.Minute),
		workflow.WithMaxReentry(1, StepFlushClusterWaitSlaveFlush), //清空数据，最多执行3次（设置为1，实际执行3次）
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushClusterWaitSlaveFlush,
		Workflow:        WorkflowFlushCluster,
		StepProcess:     flush.ProcessWaitSlaveFlushComplete,
		SuccessNextStep: StepFlushClusterCheckSync,
		ErrorNextStep:   StepFlushClusterCheckSync},
		workflow.WithStepTimeout(30*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushClusterCheckSync,
		Workflow:        WorkflowFlushCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepFlushClusterUnblockPingAvaliableMonitorInXmaster,
		ErrorNextStep:   StepFlushClusterCheckSync},
		workflow.WithStepTimeout(15*time.Minute),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFlushClusterUnblockPingAvaliableMonitorInXmaster,
		Workflow:        WorkflowFlushCluster,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: StepFlushClusterFlushCallback,
		ErrorNextStep:   StepFlushClusterUnblockPingAvaliableMonitorInXmaster})

	// Step-02 清除Redis数据后的回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepFlushClusterFlushCallback,
		Workflow: WorkflowFlushCluster,

		StepProcess: callback.ProcessGeneralSuccessCb,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepFlushClusterFlushCallback})
}
