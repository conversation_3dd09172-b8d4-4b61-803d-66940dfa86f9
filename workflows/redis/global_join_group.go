/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_join_group.go */
/*
modification history
--------------------
2022/05/24 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/precheck"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkFlowLocalJoinGroup                                = "scs-local-join-group"
	WorkFlowLocalJoinGroup_StepPreCheck                   = "scs-local-join-group-precheck"
	WorkFlowLocalJoinGroup_StepDisableFlashback           = "scs-local-join-group-disable-flashback"
	WorkFlowLocalJoinGroup_StepBlockMonitorInXmaster      = "scs-local-join-group-block-monitor-in-xmaster"
	WorkFlowLocalJoinGroup_StepBuildMeta                  = "scs-local-join-group-build-meta"
	WorkFlowLocalJoinGroup_StepTurnOnPegaUseRsidPsync     = "scs-local-join-group-turn-on-pega-use-rsid-psync"
	WorkFlowLocalJoinGroup_StepRestartAllSlaves           = "scs-local-join-group-restart-all-slaves"
	WorkFlowLocalJoinGroup_StepRegisterNodes              = "scs-local-join-group-register-nodes"
	WorkFlowLocalJoinGroup_StepCbMetaServer               = "scs-local-join-group-cb-meta-server"
	WorkFlowLocalJoinGroup_StepCbNodeRole                 = "scs-local-join-group-cb-node-role"
	WorkFlowLocalJoinGroup_StepUpdateSg                   = "scs-local-join-group-update-sg"
	WorkFlowLocalJoinGroup_StepBindMasterNode             = "scs-local-join-group-bind-master-node"
	WorkFlowLocalJoinGroup_StepPushMonitorHTGRPSlaveFlag  = "scs-local-join-group-push-htgrp-slave-flag"
	WorkFlowLocalJoinGroup_StepRestartProxy               = "scs-local-join-group-restart-proxy"
	WorkFlowLocalJoinGroup_UpdateTlsConfIfNeeded          = "scs-local-join-group-update-tls-conf-if-needed"
	WorkFlowLocalJoinGroup_StepCleanMetaserver            = "scs-local-join-group-clean-metaserver"
	WorkFlowLocalJoinGroup_StepUpdateAppTopologyInXmaster = "scs-local-join-group-update-app-topo-in-xmaster"
	WorkFlowLocalJoinGroup_StepUnblockMonitorInXmaster    = "scs-local-join-group-unblock-monitor-in-xmaster"
	WorkFlowLocalJoinGroup_StepSuccessCb                  = "scs-local-join-group-success-cb"
)

func init() {
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepPreCheck,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     precheck.ProcessPrecheckForJoinGlobalGroup,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepDisableFlashback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepDisableFlashback,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOFAndCloseOpheader,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepBlockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepDisableFlashback,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepBuildMeta,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepBlockMonitorInXmaster,
	})

	// ① 查询global master，获取shard信息，将GlobalShardID写入数据库
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepBuildMeta,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForJoinGlobalGroup,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepTurnOnPegaUseRsidPsync,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepTurnOnPegaUseRsidPsync,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     gmaster.TurnOnPegaUseRsidPsyncAndBuildMetaForRestart,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepRestartAllSlaves,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepTurnOnPegaUseRsidPsync,
	})
	// 重启所有从节点，目的是让需要重启生效的参数生效
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepRestartAllSlaves,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     restart.ProcessRestartForJoinGroup,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepRegisterNodes,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepRestartAllSlaves,
	})
	// ② 调用global-master-api，将redis、proxy信息注册至global master
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepRegisterNodes,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     gmaster.ProcessRegisterNodesToGlobalMasterAsSlave,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepCbMetaServer,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepRegisterNodes,
	})
	// ③ 回调csmaster，更新metaserver信息
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepCbMetaServer,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     callback.ProcessCbGlobalMetaserverEntrance,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepCbNodeRole,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepCbMetaServer,
	})
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepCbNodeRole,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     callback.ProcessCbNodeRolesJoin,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepUpdateSg,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepCbNodeRole,
	})

	// ④ 更新InnerSecurity白名单ip，将热活实例组中所有地域、所有redis、proxy的fixip加入；更新acl信息保证与globalmaster一致
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepUpdateSg,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepBindMasterNode,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepUpdateSg,
	})
	// ⑤ 调用global-master-api获取主redis信息，将slave挂载到对应主，并修改数据库中元数据信息
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepBindMasterNode,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepBindMasterNode,
	})

	// ⑥ 分批次重启Proxy
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAll,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepRestartProxy,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepPushMonitorHTGRPSlaveFlag,
	})

	// ⑥ 分批次重启Proxy
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepRestartProxy,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     restart.ProcessUpgradeProxies,
		SuccessNextStep: WorkFlowLocalJoinGroup_UpdateTlsConfIfNeeded,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepRestartProxy,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_UpdateTlsConfIfNeeded,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     util.UpdateTLSConfIfNeeded,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepCleanMetaserver,
		ErrorNextStep:   WorkFlowLocalJoinGroup_UpdateTlsConfIfNeeded,
	})

	// ⑦ 删除原Metaserver中信息
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepCleanMetaserver,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     metaserver.ProcessDeleteClusterLocal,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepCleanMetaserver,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalJoinGroup_StepSuccessCb,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepUnblockMonitorInXmaster,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalJoinGroup_StepSuccessCb,
		Workflow:        WorkFlowLocalJoinGroup,
		StepProcess:     callback.ProcessGlobalGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalJoinGroup_StepSuccessCb,
	})

}
