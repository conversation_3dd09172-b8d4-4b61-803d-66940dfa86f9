/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
暂停集群（MasterServiceHandler::pause_cluster、RedisV7PausingState::handle）

// yuning

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"BackupParams" : [
		{
			NodeShortId: 1234,
			ObjectKey: "xxxx",
		},
		{
			NodeShortId: 1235,
			ObjectKey: "yyyy",
		}
	]
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowPauseStandalone      = "scs-pause-standalone-app"
	StepPauseStandalonePause     = "scs-pause-standalone-app-pause"
	StepPauseUnbindAllRoRs       = "scs-pause-standalone-app-unbind-all-ro-rs"
	StepPauseUnbindAllEntranceRs = "scs-pause-standalone-app-unbind-all-entrance-rs"
	StepPauseStandaloneCallback  = "scs-pause-standalone-app-cb"
)

func init() {
	// Step-1 摘除所有Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseStandalonePause,
		Workflow:        WorkflowPauseStandalone,
		StepProcess:     blb.ProcessUnbindAllRsStandaloneRs,
		SuccessNextStep: StepPauseUnbindAllRoRs,
		ErrorNextStep:   StepPauseStandalonePause,
	})

	// Step-2 摘除所有只读Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseUnbindAllRoRs,
		Workflow:        WorkflowPauseStandalone,
		StepProcess:     blb.ProcessUnbindAllReadonlyRs,
		SuccessNextStep: StepPauseUnbindAllEntranceRs,
		ErrorNextStep:   StepPauseUnbindAllRoRs,
	})

	// Step-2 摘除所有entranceRs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseUnbindAllEntranceRs,
		Workflow:        WorkflowPauseStandalone,
		StepProcess:     blb.ProcessUnbindAllEntranceRs,
		SuccessNextStep: StepPauseStandaloneCallback,
		ErrorNextStep:   StepPauseUnbindAllEntranceRs,
	})

	// Step-3 成功回调
	// 调用CsMaster的API，修改cluster状态为8
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseStandaloneCallback,
		Workflow:        WorkflowPauseStandalone,
		StepProcess:     callback.ProcessPauseCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPauseStandaloneCallback,
	})
}
