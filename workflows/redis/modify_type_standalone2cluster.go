/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* standalone2cluster */
/*
modification history
--------------------
2024/04/02, by wang<PERSON><PERSON>(wang<PERSON><PERSON>@baidu.com), create
*/

/*
DESCRIPTION
标准版变配到集群版

Parameters
{
	"app_id": "scs-bj-ysohzbmpuwhj",
	"node_type": "cache.n1.micro",
	"shard_count": 2,
    "replicas": [{                      // New node && proxy use replicas
		"zone": "zoneE",
		"subnet_ids": ["09b2c10c-62c9-4401-8a2d-e07833729082"],
		"role": "master",
		"count": 2
	}]
}
*/

package workflows

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dts"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/precheck"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyTypeStandalone2cluster                           = "scs-modify-type-standalone2cluster"
	StepModifyTypeStandalone2clusterPreCheck                       = "scs-modify-type-standalone2cluster-precheck"
	StepModifyTypeStandalone2clusterCurSlaveSyncCheck              = "scs-modify-type-standalone2cluster-cur-slave-sync-check"
	StepModifyTypeStandalone2clusterBuildMeta                      = "scs-modify-type-standalone2cluster-build-meta"
	StepModifyTypeStandalone2clusterFillSpec                       = "scs-modify-type-standalone2cluster-fill-spec"
	StepModifyTypeStandalone2clusterCheckSubnetsEnoughIps          = "scs-modify-type-standalone2cluster-check-subnets-enough-ips"
	StepModifyTypeStandalone2clusterCreateSecurityGroups           = "scs-modify-type-standalone2cluster-create-security-groups"
	StepModifyTypeStandalone2clusterApplyResources                 = "scs-modify-type-standalone2cluster-apply-resources"
	StepModifyTypeStandalone2clusterApplyResourcesCallback         = "scs-modify-type-standalone2cluster-apply-resources-cb"
	StepModifyTypeStandalone2clusterUpdateSecurityGroups           = "scs-modify-type-standalone2cluster-update-security-groups"
	StepModifyTypeStandalone2clusterInitMetaserver                 = "scs-modify-type-standalone2cluster-init-metaserver"
	StepModifyTypeStandalone2clusterDeployRedis                    = "scs-modify-type-standalone2cluster-deploy-redis"
	StepModifyTypeStandalone2clusterSetLocalSlaveof                = "scs-modify-type-standalone2cluster-set-local-slaveof"
	StepModifyTypeStandalone2clusterNewSlaveSyncCheck              = "scs-modify-type-standalone2cluster-new-slave-sync-check"
	StepModifyTypeStandalone2clusterDeployFilebeat                 = "scs-modify-type-standalone2cluster-deploy-filebeat"
	StepModifyTypeStandalone2clusterSetDefaultAcl                  = "scs-modify-type-standalone2cluster-set-default-acl"
	StepModifyTypeStandalone2clusterInitConfigForCreate            = "scs-modify-type-standalone2cluster-init-config-for-create"
	StepModifyTypeStandalone2clusterUpdateConfigForNewAgent        = "scs-modify-type-standalone2cluster-update-config-for-new-agent"
	StepModifyTypeStandalone2clusterDTSCreateTask                  = "scs-modify-type-standalone2cluster-dts-create-task"
	StepModifyTypeStandalone2clusterDTSPrecheckTask                = "scs-modify-type-standalone2cluster-dts-precheck-task"
	StepModifyTypeStandalone2clusterDTSStartTask                   = "scs-modify-type-standalone2cluster-dts-start-task"
	StepModifyTypeStandalone2clusterDTSCheckTaskSync               = "scs-modify-type-standalone2cluster-dts-check-task-sync"
	StepModifyTypeStandalone2clusterCheckRedisKeyspace             = "scs-modify-type-standalone2cluster-check-redis-keyspace"
	StepModifyTypeStandalone2clusterSetRs                          = "scs-modify-type-standalone2cluster-set-rs"
	StepModifyTypeStandalone2clusterDTSShutdownTask                = "scs-modify-type-standalone2cluster-dts-shutdown-task"
	StepModifyTypeStandalone2clusterUpdateAppTopologyInXmaster     = "scs-modify-type-standalone2cluster-update-app-topo-in-xmaster"
	StepModifyTypeStandalone2clusterInitBcmResource                = "scs-modify-type-standalone2cluster-init-bcm-resource"
	StepModifyTypeStandalone2clusterAddToBcmGroup                  = "scs-modify-type-standalone2cluster-add-to-bcm-group"
	StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsService  = "scs-modify-type-standalone2cluster-init-op-monitor-create-bns-service"
	StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsInstance = "scs-modify-type-standalone2cluster-init-op-monitor-create-bns-instance"
	StepModifyTypeStandalone2clusterPushMonitorHTGRPSlaveFlag      = "scs-modify-type-standalone2cluster-push-htgrp-slave-flag"
	StepModifyTypeStandalone2clusterPushFlag                       = "scs-modify-type-standalone2cluster-push-flag"
	StepModifyTypeStandalone2clusterDTSDeleteTask                  = "scs-modify-type-standalone2cluster-dts-delete-task"
	StepModifyTypeStandalone2clusterDeleteOldNodes                 = "scs-modify-type-standalone2cluster-delete-old-nodes"
	StepModifyTypeStandalone2clusterDeleteEntranceEndpoint         = "scs-modify-type-standalone2cluster-delete-entrance-endpoint"
	StepModifyTypeStandalone2clusterDeleteEntranceBLB              = "scs-modify-type-standalone2cluster-delete-entrance-blb"
	StepModifyTypeStandalone2clusterDeleteAOFBackupPolicy          = "scs-modify-type-standalone2cluster-delete-aof-backup-policy"
	StepModifyTypeStandalone2clusterCreateBnsService               = "scs-modify-type-standalone2cluster-create-bns-service"
	StepModifyTypeStandalone2clusterSuccessCallback                = "scs-modify-type-standalone2cluster-succ-cb"

	StepModifyTypeStandalone2clusterRollbackDeleteMetaserverRecord = "scs-modify-type-standalone2cluster-rollback-delete-metaserver-record"
	StepModifyTypeStandalone2clusterRollbackReleaseResources       = "scs-modify-type-standalone2cluster-rollback-release-resource"
	StepModifyTypeStandalone2clusterRollbackRemoveFromBcmGroup     = "scs-modify-type-standalone2cluster-rollback-remove-from-bcm-group"
	StepModifyTypeStandalone2clusterRollbackBcmResource            = "scs-modify-type-standalone2cluster-rollback-bcm-resource"
	StepModifyTypeStandalone2clusterRollbackMeta                   = "scs-modify-type-standalone2cluster-rollback-meta"
	StepModifyTypeStandalone2clusterRollbackCallback               = "scs-modify-type-standalone2cluster-rollback-cb"
	StepModifyTypeStandalone2clusterSuccessEndEvent                = "scs-modify-type-standalone2cluster-success-end-event"
	StepModifyTypeStandalone2clusterFailedEndEvent                 = "scs-modify-type-standalone2cluster-failed-end-event"
)

// init init 函数用于初始化工作流，包括添加每一步的处理方法和下一步的逻辑。
// 每一步都包括了名称、工作流、处理方法、成功后的下一步和错误后的下一步。
func init() {
	/**************************************变配前置检查*******************************************/
	// Step 检查当前实例 db 个数，仅有 db0 或空 db 才能进行同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterPreCheck,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     precheck.ProcessPrecheckForModifyType,
		SuccessNextStep: StepModifyTypeStandalone2clusterCurSlaveSyncCheck,
		ErrorNextStep:   StepModifyTypeStandalone2clusterPreCheck,
	})
	// Step 检查当前集群主从同步状态(DTS 同步使用的旧从当做源数据同步)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterCurSlaveSyncCheck,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepModifyTypeStandalone2clusterBuildMeta,
		ErrorNextStep:   StepModifyTypeStandalone2clusterNewSlaveSyncCheck,
	})
	/**************************************新增集群版分片*******************************************/
	// Step 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterBuildMeta,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: buildmeta.ProcessModifyType,

		SuccessNextStep: StepModifyTypeStandalone2clusterFillSpec,
		ErrorNextStep:   StepModifyTypeStandalone2clusterBuildMeta,
	})

	// Step 填入规格信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterFillSpec,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepModifyTypeStandalone2clusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyTypeStandalone2clusterFillSpec,
	})

	// Step-3 检查子网IP是否足够
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterCheckSubnetsEnoughIps,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepModifyTypeStandalone2clusterCreateSecurityGroups,
		ErrorNextStep:   StepModifyTypeStandalone2clusterCheckSubnetsEnoughIps,
	})

	// Step 创建安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterCreateSecurityGroups,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: securitygroup.ProcessInitSecurityGroupCluster,

		SuccessNextStep: StepModifyTypeStandalone2clusterApplyResources,
		ErrorNextStep:   StepModifyTypeStandalone2clusterCreateSecurityGroups,
	})

	// Step 申请资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterApplyResources,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepModifyTypeStandalone2clusterApplyResourcesCallback,
		ErrorNextStep:   StepModifyTypeStandalone2clusterApplyResources,
	})

	// Step 申请资源回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterApplyResourcesCallback,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepModifyTypeStandalone2clusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyTypeStandalone2clusterApplyResourcesCallback,
	})

	// Step 更新安全组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterUpdateSecurityGroups,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: securitygroup.ProcessRebuildSecurityGroupCluster,

		SuccessNextStep: StepModifyTypeStandalone2clusterInitMetaserver,
		ErrorNextStep:   StepModifyTypeStandalone2clusterUpdateSecurityGroups,
	})

	// Step 初始化Metaserver
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterInitMetaserver,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: metaserver.ProcessInitClusterLocal,

		SuccessNextStep: StepModifyTypeStandalone2clusterDeployRedis,
		ErrorNextStep:   StepModifyTypeStandalone2clusterInitMetaserver,
	})

	// Step 部署所有节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterDeployRedis,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: deploy.ProcessDeployAllForNewCreate,

		SuccessNextStep: StepModifyTypeStandalone2clusterSetLocalSlaveof,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDeployRedis},

		workflow.WithStepTimeout(time.Minute*15))

	// Step slaveof
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterSetLocalSlaveof,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: handover.ProcessLocalSlaveOf,

		SuccessNextStep: StepModifyTypeStandalone2clusterNewSlaveSyncCheck,
		ErrorNextStep:   StepModifyTypeStandalone2clusterSetLocalSlaveof})

	// Step 检查新从库与新主库是否同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterNewSlaveSyncCheck,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepModifyTypeStandalone2clusterDeployFilebeat,
		ErrorNextStep:   StepModifyTypeStandalone2clusterNewSlaveSyncCheck,
	})

	// Step 部署filebeat
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterDeployFilebeat,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: deploy.ProcessDeployFilebeat,

		SuccessNextStep: StepModifyTypeStandalone2clusterSetDefaultAcl,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDeployFilebeat},

		workflow.WithStepSplitHandler(util.GetToCreateRedisIds),
		workflow.WithStepTimeout(time.Minute*15))

	// Step 初始化acl (集群版全支持 ACL, 标准版低于 6.0 版本不支持 ACL)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterSetDefaultAcl,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: acl.ProcessInitAclForCluster,

		SuccessNextStep: StepModifyTypeStandalone2clusterInitConfigForCreate,
		ErrorNextStep:   StepModifyTypeStandalone2clusterSetDefaultAcl,
	})

	// Step 设置 hash_tag && support_scan 等配置(会请求 x1-api /v1/scs/modifyConfigInfo 接口)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterInitConfigForCreate,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     config.ProcessInitConfigForCreate,
		SuccessNextStep: StepModifyTypeStandalone2clusterUpdateConfigForNewAgent,
		ErrorNextStep:   StepModifyTypeStandalone2clusterInitConfigForCreate,
	})

	// Step update config
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterUpdateConfigForNewAgent,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     config.ProcessUpdateConfigNewForCreate,
		SuccessNextStep: StepModifyTypeStandalone2clusterDTSCreateTask,
		ErrorNextStep:   StepModifyTypeStandalone2clusterUpdateConfigForNewAgent,
	})

	// Step 启动 DTS 任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterDTSCreateTask,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: dts.ProcessCreateTask,

		SuccessNextStep: StepModifyTypeStandalone2clusterDTSPrecheckTask,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDTSCreateTask,
	})

	// Step DTS 前置检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterDTSPrecheckTask,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: dts.ProcessPrecheckTask,

		SuccessNextStep: StepModifyTypeStandalone2clusterDTSStartTask,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDTSPrecheckTask,
	})

	// Step DTS 启动
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDTSStartTask,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     dts.ProcessStartTask,
		SuccessNextStep: StepModifyTypeStandalone2clusterDTSCheckTaskSync,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDTSStartTask,
	})

	// Step DTS 检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDTSCheckTaskSync,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     dts.ProcessCheckTaskSync,
		SuccessNextStep: StepModifyTypeStandalone2clusterCheckRedisKeyspace,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDTSCheckTaskSync,
	})

	// Step Redis keyspace 检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterCheckRedisKeyspace,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     dts.ProcessCheckRedisKeyspace,
		SuccessNextStep: StepModifyTypeStandalone2clusterSetRs,
		ErrorNextStep:   StepModifyTypeStandalone2clusterCheckRedisKeyspace,
	})

	// Step 绑定新的proxy rs(这个时候 proxy 开始接流量，需要进行断开同步)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterSetRs,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: blb.ProcessSetProxyRsForModifyType,

		SuccessNextStep: StepModifyTypeStandalone2clusterDTSShutdownTask,
		ErrorNextStep:   StepModifyTypeStandalone2clusterSetRs,
	})

	// Step DTS shutdown
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDTSShutdownTask,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     dts.ProcessShutdownTask,
		SuccessNextStep: StepModifyTypeStandalone2clusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDTSShutdownTask,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterUpdateAppTopologyInXmaster,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: xmaster.ProcessUpdateAppTopologyInXmaster,

		SuccessNextStep: StepModifyTypeStandalone2clusterInitBcmResource,
		ErrorNextStep:   StepModifyTypeStandalone2clusterUpdateAppTopologyInXmaster,
	})

	// 创建监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterInitBcmResource,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: bcm.ProcessBcmResourceWithoutApp,

		SuccessNextStep: StepModifyTypeStandalone2clusterAddToBcmGroup,
		ErrorNextStep:   StepModifyTypeStandalone2clusterInitBcmResource,
	})

	// 添加到bcm实例组
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterAddToBcmGroup,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: bcm.ProcessShardsAddToGroup,

		SuccessNextStep: StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsService,
		ErrorNextStep:   StepModifyTypeStandalone2clusterAddToBcmGroup,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsService,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorBnsService,
		SuccessNextStep: StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsInstance,
		ErrorNextStep:   StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsService,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsInstance,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: opmonitor.ProcessCreateOpmonitorInstanceBns,

		SuccessNextStep: StepModifyTypeStandalone2clusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifyTypeStandalone2clusterInitOpMonitorCreateBnsInstance,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterPushMonitorHTGRPSlaveFlag,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,

		SuccessNextStep: StepModifyTypeStandalone2clusterPushFlag,
		ErrorNextStep:   StepModifyTypeStandalone2clusterPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterPushFlag,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: pushflag.ProcessUpdatePushFlagForReplaceNodes,

		SuccessNextStep: StepModifyTypeStandalone2clusterDTSDeleteTask,
		ErrorNextStep:   StepModifyTypeStandalone2clusterPushFlag,
	})

	// Step DTS delete
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDTSDeleteTask,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     dts.ProcessDeleteTask,
		SuccessNextStep: StepModifyTypeStandalone2clusterDeleteOldNodes,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDTSDeleteTask,
	})

	// Step Resource delete
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDeleteOldNodes,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyTypeStandalone2clusterDeleteEntranceEndpoint,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDeleteOldNodes,
	})

	// Step Delete Entrance Endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDeleteEntranceEndpoint,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     endpoint.ProcessDeleteEntranceEndpoint,
		SuccessNextStep: StepModifyTypeStandalone2clusterDeleteEntranceBLB,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDeleteEntranceEndpoint},

		workflow.WithMaxReentry(2, StepModifyTypeStandalone2clusterDeleteEntranceBLB))

	// Step Delete Entrance BLB
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDeleteEntranceBLB,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     blb.ProcessDeleteEntranceBlB,
		SuccessNextStep: StepModifyTypeStandalone2clusterDeleteAOFBackupPolicy,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDeleteEntranceBLB},

		workflow.WithMaxReentry(2, StepModifyTypeStandalone2clusterDeleteAOFBackupPolicy))

	// check 并删除 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterDeleteAOFBackupPolicy,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOF,
		SuccessNextStep: StepModifyTypeStandalone2clusterCreateBnsService,
		ErrorNextStep:   StepModifyTypeStandalone2clusterDeleteAOFBackupPolicy,
	})

	// create bns service
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterCreateBnsService,
		Workflow: WorkflowModifyTypeStandalone2cluster,
		StepProcess: func(ctx context.Context, teu *workflow.TaskExecUnit) error {
			params := []*iface.CreateTaskParams{{
				WorkFlow:   WorkflowCreateClusterInitBns,
				Schedule:   time.Now(),
				Mutex:      teu.Entity + "_init_bns",
				Entity:     teu.Entity,
				Parameters: teu.Parameters,
			}}
			return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, params)
		},
		SuccessNextStep: StepModifyTypeStandalone2clusterSuccessCallback,
		ErrorNextStep:   StepModifyTypeStandalone2clusterCreateBnsService,
	})

	// Step-10 成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterSuccessCallback,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: callback.ProcessModifyTypeSuccCb,

		SuccessNextStep: StepModifyTypeStandalone2clusterSuccessEndEvent,
		ErrorNextStep:   StepModifyTypeStandalone2clusterSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterSuccessEndEvent,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     event.ProcessAddModifyingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyTypeStandalone2clusterSuccessEndEvent,
	})

	// --------------------------------------------------------------------------- rollback
	// Step-Error 删除 metaserver 记录
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterRollbackDeleteMetaserverRecord,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: metaserver.ProcessDeleteClusterLocal,

		SuccessNextStep: StepModifyTypeStandalone2clusterRollbackReleaseResources,
		ErrorNextStep:   StepModifyTypeStandalone2clusterRollbackDeleteMetaserverRecord,
	})

	// Step-Error 回滚资源
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterRollbackReleaseResources,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: delresource.ProcessRollbackBccResources,

		SuccessNextStep: StepModifyTypeStandalone2clusterRollbackRemoveFromBcmGroup,
		ErrorNextStep:   StepModifyTypeStandalone2clusterRollbackReleaseResources,
	})

	// 从实例组中删除
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterRollbackRemoveFromBcmGroup,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: bcm.ProcessRollbackShardsAddToGroup,

		SuccessNextStep: StepModifyTypeStandalone2clusterRollbackBcmResource,
		ErrorNextStep:   StepModifyTypeStandalone2clusterRollbackRemoveFromBcmGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterRollbackBcmResource,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: bcm.ProcessRollbackBcmResourceWithoutApp,

		SuccessNextStep: StepModifyTypeStandalone2clusterRollbackMeta,
		ErrorNextStep:   StepModifyTypeStandalone2clusterRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyTypeStandalone2clusterRollbackMeta,
		Workflow: WorkflowModifyTypeStandalone2cluster,

		StepProcess: delresource.ProcessRollbackMeta,

		SuccessNextStep: StepModifyTypeStandalone2clusterRollbackCallback,
		ErrorNextStep:   StepModifyTypeStandalone2clusterFailedEndEvent,
	})

	// Step-Error-02 回滚Callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterRollbackCallback,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     callback.ProcessModifyTypeErrorCb,
		SuccessNextStep: StepModifyTypeStandalone2clusterFailedEndEvent,
		ErrorNextStep:   StepModifyTypeStandalone2clusterRollbackCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyTypeStandalone2clusterFailedEndEvent,
		Workflow:        WorkflowModifyTypeStandalone2cluster,
		StepProcess:     event.ProcessAddModifyingFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyTypeStandalone2clusterFailedEndEvent,
	})
}
