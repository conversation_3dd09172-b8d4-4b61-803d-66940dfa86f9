/* Copyright 2024 Baidu Inc. All Rights Reserved. */

/*
DESCRIPTION
变更入口WorkFlow

Parameters

	{

	}
*/
package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/vpc"
)

const (
	WorkflowModifyAppEntranceCluster                         = "scs-modify-app-entrance-cluster"
	StepModifyAppEntranceClusterInitTimeWindowTask           = "scs-modify-app-entrance-cluster-init-time-window-task"
	StepModifyAppEntranceClusterAddStartEvent                = "scs-modify-app-entrance-cluster-add-start-event"
	StepModifyAppEntranceClusterBuildMeta                    = "scs-modify-app-entrance-cluster-build-meta"
	StepModifyAppEntranceClusterTaskStepCreateIP             = "scs-modify-app-entrance-cluster-task-step-create-ip"
	StepModifyAppEntranceClusterInitNewBLB                   = "scs-modify-app-entrance-cluster-init-new-blb"
	StepModifyAppEntranceClusterCreateNewEndpoint            = "scs-modify-app-entrance-cluster-create-new-endpoint"
	StepModifyAppEntranceClusterTaskStepConfigIP             = "scs-modify-app-entrance-cluster-task-step-config-ip"
	StepModifyAppEntranceClusterNewBLBSetRs                  = "scs-modify-app-entrance-cluster-new-blb-set-rs"
	StepModifyAppEntranceClusterNewBLBSetMcpackRs            = "scs-modify-app-entrance-cluster-new-blb-set-mcpack-rs"
	StepModifyAppEntranceClusterCheckNewAvailable            = "scs-modify-app-entrance-cluster-check-new-blb-available"
	StepModifyAppEntranceClusterBindSecurityGroups           = "scs-modify-app-entrance-cluster-bind-security-groups"
	StepModifyAppEntranceClusterTaskStepExchangeIP           = "scs-modify-app-entrance-cluster-task-step-exchange-ip"
	StepModifyAppEntranceClusterPublishServiceRebind         = "scs-modify-app-entrance-cluster-publish-service-rebind"
	StepModifyAppEntranceClusterAddNewToDomain               = "scs-modify-app-entrance-cluster-add-new-to-domain"
	StepModifyAppEntranceClusterCheckNewInDomain             = "scs-modify-app-entrance-cluster-check-new-in-domain"
	StepModifyAppEntranceClusterRemoveOldFromDomain          = "scs-modify-app-entrance-cluster-remove-old-from-domain"
	StepModifyAppEntranceClusterCheckOldNotInDomain          = "scs-modify-app-entrance-cluster-check-old-not-in-domain"
	StepModifyAppEntranceClusterModifyConfig                 = "scs-modify-app-entrance-cluster-modify-config"
	StepModifyAppEntranceClusterCheckConfig                  = "scs-modify-app-entrance-cluster-check-config"
	StepModifyAppEntranceClusterKillClientOnProxy            = "scs-modify-app-entrance-cluster-kill-client-on-proxy"
	StepModifyAppEntranceClusterOldBLBUnSetRs                = "scs-modify-app-entrance-cluster-old-blb-unset-rs"
	StepModifyAppEntranceClusterOldBLBUnSetMcpackRs          = "scs-modify-app-entrance-cluster-old-blb-unset-mcpack-rs"
	StepModifyAppEntranceClusterSwitchBLBTypeInDB            = "scs-modify-app-entrance-cluster-switch-blb-type-in-db"
	StepModifyAppEntranceClusterRefreshAgentRecovers         = "scs-modify-app-entrance-cluster-refresh-agent-recovers"
	StepModifyAppEntranceClusterTaskStepSuccess              = "scs-modify-app-entrance-cluster-task-step-success"
	StepModifyAppEntranceClusterSuccessCb                    = "scs-modify-app-entrance-cluster-success-cb"
	StepModifyAppEntranceClusterAddSuccessEndEvent           = "scs-modify-app-entrance-cluster-add-success-end-event"
	StepModifyAppEntranceClusterRollbackOldBLBSetRs          = "scs-modify-app-entrance-cluster-rollback-old-blb-set-rs"
	StepModifyAppEntranceClusterRollbackOldBLBSetMcpackRs    = "scs-modify-app-entrance-cluster-rollback-old-blb-set-mcpack-rs"
	StepModifyAppEntranceClusterRollbackPublishServiceRebind = "scs-modify-app-entrance-cluster-rollback-publish-service-rebind"
	StepModifyAppEntranceClusterRollbackAddOldToDomain       = "scs-modify-app-entrance-cluster-rollback-add-old-to-domain"
	StepModifyAppEntranceClusterRollbackRemoveNewFromDomain  = "scs-modify-app-entrance-cluster-rollback-remove-new-from-domain"
	StepModifyAppEntranceClusterRollbackDeleteNewEndpoint    = "scs-modify-app-entrance-cluster-rollback-delete-new-endpoint"
	StepModifyAppEntranceClusterRollbackDeleteNewBLB         = "scs-modify-app-entrance-cluster-rollback-delete-new-blb"
	StepModifyAppEntranceClusterTaskStepError                = "scs-modify-app-entrance-cluster-rollback-task-step-error"
	StepModifyAppEntranceClusterErrorCb                      = "scs-modify-app-entrance-cluster-error-cb"
	StepModifyAppEntranceClusterAddFailEndEvent              = "scs-modify-app-entrance-cluster-add-fail-end-event"
)

func init() {

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterInitTimeWindowTask,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepModifyAppEntranceClusterAddStartEvent,
		ErrorNextStep:   StepModifyAppEntranceClusterInitTimeWindowTask,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterAddStartEvent,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     event.ProcessAddModifyingEntranceStartEvent,
		SuccessNextStep: StepModifyAppEntranceClusterBuildMeta,
		ErrorNextStep:   StepModifyAppEntranceClusterAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterBuildMeta,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForModifyAppDefaultEntrance,
		SuccessNextStep: StepModifyAppEntranceClusterTaskStepCreateIP,
		ErrorNextStep:   StepModifyAppEntranceClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceClusterTaskStepCreateIP,
		Workflow: WorkflowModifyAppEntranceCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, timewindow.StepCreateIP, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAppEntranceClusterInitNewBLB,
		ErrorNextStep:   StepModifyAppEntranceClusterTaskStepCreateIP,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterInitNewBLB,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessInitToExchangeBLB,
		SuccessNextStep: StepModifyAppEntranceClusterCreateNewEndpoint,
		ErrorNextStep:   StepModifyAppEntranceClusterInitNewBLB,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterCreateNewEndpoint,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     endpoint.ProcessCreateToExchangeRwEndpoint,
		SuccessNextStep: StepModifyAppEntranceClusterTaskStepConfigIP,
		ErrorNextStep:   StepModifyAppEntranceClusterCreateNewEndpoint,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceClusterTaskStepConfigIP,
		Workflow: WorkflowModifyAppEntranceCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, timewindow.StepConfigIP, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAppEntranceClusterNewBLBSetRs,
		ErrorNextStep:   StepModifyAppEntranceClusterTaskStepConfigIP,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterNewBLBSetRs,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessBindProxyToExchangeApp,
		SuccessNextStep: StepModifyAppEntranceClusterNewBLBSetMcpackRs,
		ErrorNextStep:   StepModifyAppEntranceClusterNewBLBSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterNewBLBSetMcpackRs,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessBindProxyMcpackToExchangeApp,
		SuccessNextStep: StepModifyAppEntranceClusterCheckNewAvailable,
		ErrorNextStep:   StepModifyAppEntranceClusterNewBLBSetMcpackRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterCheckNewAvailable,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessCheckToExchangeBlbAvailable,
		SuccessNextStep: StepModifyAppEntranceClusterBindSecurityGroups,
		ErrorNextStep:   StepModifyAppEntranceClusterCheckNewAvailable,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterBindSecurityGroups,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     vpc.ProcessCopySgsToNewEntrance,
		SuccessNextStep: StepModifyAppEntranceClusterTaskStepExchangeIP,
		ErrorNextStep:   StepModifyAppEntranceClusterBindSecurityGroups,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceClusterTaskStepExchangeIP,
		Workflow: WorkflowModifyAppEntranceCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, timewindow.StepExchangeIP, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyAppEntranceClusterPublishServiceRebind,
		ErrorNextStep:   StepModifyAppEntranceClusterTaskStepExchangeIP,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterPublishServiceRebind,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     endpoint.ProcessRebindToExchangeBlb,
		SuccessNextStep: StepModifyAppEntranceClusterAddNewToDomain,
		ErrorNextStep:   StepModifyAppEntranceClusterPublishServiceRebind,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterAddNewToDomain,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     dns.ProcessAddToExchangeBlbToDomain,
		SuccessNextStep: StepModifyAppEntranceClusterCheckNewInDomain,
		ErrorNextStep:   StepModifyAppEntranceClusterAddNewToDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterCheckNewInDomain,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     dns.ProcessCheckToExchangeBlbInDomain,
		SuccessNextStep: StepModifyAppEntranceClusterRemoveOldFromDomain,
		ErrorNextStep:   StepModifyAppEntranceClusterCheckNewInDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRemoveOldFromDomain,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     dns.ProcessDeleteOldBlbFromAppDomain,
		SuccessNextStep: StepModifyAppEntranceClusterCheckOldNotInDomain,
		ErrorNextStep:   StepModifyAppEntranceClusterRemoveOldFromDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterCheckOldNotInDomain,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     dns.ProcessCheckOnlyToExchangeBlbInDomain,
		SuccessNextStep: StepModifyAppEntranceClusterModifyConfig,
		ErrorNextStep:   StepModifyAppEntranceClusterCheckOldNotInDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterModifyConfig,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     config.ModifyClusterEntry,
		SuccessNextStep: StepModifyAppEntranceClusterCheckConfig,
		ErrorNextStep:   StepModifyAppEntranceClusterModifyConfig,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterCheckConfig,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     config.CheckClusterEntry,
		SuccessNextStep: StepModifyAppEntranceClusterKillClientOnProxy,
		ErrorNextStep:   StepModifyAppEntranceClusterCheckConfig,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterKillClientOnProxy,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     handover.ProcessProxyKillOldClient,
		SuccessNextStep: StepModifyAppEntranceClusterOldBLBUnSetRs,
		ErrorNextStep:   StepModifyAppEntranceClusterKillClientOnProxy,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterOldBLBUnSetRs,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessUnBindProxyFromOld,
		SuccessNextStep: StepModifyAppEntranceClusterOldBLBUnSetMcpackRs,
		ErrorNextStep:   StepModifyAppEntranceClusterOldBLBUnSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterOldBLBUnSetMcpackRs,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessUnBindProxyMcpackFromOld,
		SuccessNextStep: StepModifyAppEntranceClusterSwitchBLBTypeInDB,
		ErrorNextStep:   StepModifyAppEntranceClusterOldBLBUnSetMcpackRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterSwitchBLBTypeInDB,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     callback.ProcessSwitchBlbTypeInDB,
		SuccessNextStep: StepModifyAppEntranceClusterRefreshAgentRecovers,
		ErrorNextStep:   StepModifyAppEntranceClusterSwitchBLBTypeInDB,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRefreshAgentRecovers,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     config.ProcessRefreshAgentRecovers,
		SuccessNextStep: StepModifyAppEntranceClusterTaskStepSuccess,
		ErrorNextStep:   StepModifyAppEntranceClusterRefreshAgentRecovers,
	},
		workflow.WithMaxReentry(3, StepModifyAppEntranceClusterTaskStepSuccess))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceClusterTaskStepSuccess,
		Workflow: WorkflowModifyAppEntranceCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepModifyAppEntranceClusterSuccessCb,
		ErrorNextStep:   StepModifyAppEntranceClusterTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterSuccessCb,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     callback.ProcessModifyEntranceSuccessCb,
		SuccessNextStep: StepModifyAppEntranceClusterAddSuccessEndEvent,
		ErrorNextStep:   StepModifyAppEntranceClusterSuccessCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterAddSuccessEndEvent,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     event.ProcessAddModifyingEntranceSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyAppEntranceClusterAddSuccessEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackOldBLBSetRs,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessRollbackUnBindProxyFromOld,
		SuccessNextStep: StepModifyAppEntranceClusterRollbackOldBLBSetMcpackRs,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackOldBLBSetRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackOldBLBSetMcpackRs,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessRollbackUnBindProxyMcpackFromOld,
		SuccessNextStep: StepModifyAppEntranceClusterRollbackPublishServiceRebind,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackOldBLBSetMcpackRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackPublishServiceRebind,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     endpoint.ProcessRebindToOriginBlb,
		SuccessNextStep: StepModifyAppEntranceClusterRollbackAddOldToDomain,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackPublishServiceRebind,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackAddOldToDomain,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     dns.ProcessCreateAppDomain,
		SuccessNextStep: StepModifyAppEntranceClusterRollbackRemoveNewFromDomain,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackAddOldToDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackRemoveNewFromDomain,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     dns.ProcessDeleteToExchangeBlbFromAppDomain,
		SuccessNextStep: StepModifyAppEntranceClusterRollbackDeleteNewEndpoint,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackRemoveNewFromDomain,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackDeleteNewEndpoint,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     endpoint.ProcessDeleteToExchangeRwEndpoint,
		SuccessNextStep: StepModifyAppEntranceClusterRollbackDeleteNewBLB,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackDeleteNewEndpoint,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterRollbackDeleteNewBLB,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     blb.ProcessDelToExchangeBLB,
		SuccessNextStep: StepModifyAppEntranceClusterTaskStepError,
		ErrorNextStep:   StepModifyAppEntranceClusterRollbackDeleteNewBLB,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyAppEntranceClusterTaskStepError,
		Workflow: WorkflowModifyAppEntranceCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ModifyEntranceTask, "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifyAppEntranceClusterErrorCb,
		ErrorNextStep:   StepModifyAppEntranceClusterTaskStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterErrorCb,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     callback.ProcessModifyEntranceErrorCb,
		SuccessNextStep: StepModifyAppEntranceClusterAddFailEndEvent,
		ErrorNextStep:   StepModifyAppEntranceClusterErrorCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyAppEntranceClusterAddFailEndEvent,
		Workflow:        WorkflowModifyAppEntranceCluster,
		StepProcess:     event.ProcessAddModifyingEntranceFailedEndEvent,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepModifyAppEntranceClusterAddFailEndEvent,
	})
}
