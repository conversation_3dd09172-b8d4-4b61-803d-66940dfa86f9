/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* restart_cluster_app.go - File Description */
/*
Modification History
--------------------
2022/5/27, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowRestartCluster                       = "scs-restart-cluster-app"
	StepRestartClusterInitTimeWindowTask         = "scs-restart-cluster-init-tw-task"
	StepRestartClusterAddStartEvent              = "scs-restart-cluster-add-start-event"
	StepRestartClusterBuildMeta                  = "scs-restart-cluster-app-build-meta"
	StepRestartClusterTaskStepRestartFollowers   = "scs-restart-cluster-task-step-restart-followers"
	StepRestartClusterSlavesRestart              = "scs-restart-cluster-slaves-restart"
	StepRestartClusterSlavesCheckSync            = "scs-restart-cluster-check-sync"
	StepRestartClusterTaskStepHandover           = "scs-restart-cluster-task-step-handover"
	StepRestartClusterHandover                   = "scs-restart-cluster-handover"
	StepRestartClusterTaskStepRestartMasters     = "scs-restart-cluster-task-step-restart-masters"
	StepRestartClusterMasterRestart              = "scs-restart-cluster-master-restart"
	StepRestartClusterSecondCheckSync            = "scs-restart-cluster-second-check-sync"
	StepRestartClusterGlobalRestart              = "scs-restart-cluster-global-restart"
	StepRestartClusterGlobalSlaveof              = "scs-restart-cluster-global-slaveof"
	StepRestartClusterGlobalCheckSync            = "scs-restart-cluster-global-check-sync"
	StepRestartClusterPushMonitorHTGRPSlaveFlag  = "scs-restart-cluster-push-htgrp-slave-flag"
	StepRestartClusterDeployFilebeat             = "scs-restart-cluster-deploy-filebeat"
	StepRestartClusterCloseTimeWindowTask        = "scs-restart-cluster-close-tw-task"
	StepRestartClusterUpdateAppTopologyInXmaster = "scs-restart-cluster-update-app-topo-in-xmaster"
	StepRestartClusterCallback                   = "scs-restart-cluster-callback"
	StepRestartClusterTaskStepSuccess            = "scs-restart-cluster-task-step-success"
	StepRestartClusterAddSuccessEndEvent         = "scs-restart-cluster-add-success-end-event"
)

func init() {
	// 定义WORKFLOW的执行步骤

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterInitTimeWindowTask,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepRestartClusterAddStartEvent,
		ErrorNextStep:   StepRestartClusterInitTimeWindowTask,
	})

	// Step-0 构建元数据，包括创建Clusters、Cluster对应的Nodes并分配角色
	// cuiyi01

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterAddStartEvent,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     event.ProcessAddRestartingStartEvent,
		SuccessNextStep: StepRestartClusterBuildMeta,
		ErrorNextStep:   StepRestartClusterAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterBuildMeta,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForRestart,
		SuccessNextStep: StepRestartClusterTaskStepRestartFollowers,
		ErrorNextStep:   StepRestartClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartClusterTaskStepRestartFollowers,
		Workflow: WorkflowRestartCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepRestartFollowers, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRestartClusterSlavesRestart,
		ErrorNextStep:   StepRestartClusterTaskStepRestartFollowers,
	})

	// Step-1 执行所有从节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterSlavesRestart,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     restart.ProcessRestartSlavesCluster,
		SuccessNextStep: StepRestartClusterSlavesCheckSync,
		ErrorNextStep:   StepRestartClusterSlavesRestart},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-2-1 检查能否切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterSlavesCheckSync,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartClusterTaskStepHandover,
		ErrorNextStep:   StepRestartClusterSlavesCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartClusterTaskStepHandover,
		Workflow: WorkflowRestartCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepHandover, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRestartClusterHandover,
		ErrorNextStep:   StepRestartClusterTaskStepHandover,
	})

	// Step-2-2 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterHandover,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     handover.ProcessHandoverClusterForRestarting,
		SuccessNextStep: StepRestartClusterTaskStepRestartMasters,
		ErrorNextStep:   StepRestartClusterHandover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartClusterTaskStepRestartMasters,
		Workflow: WorkflowRestartCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepRestartMasters, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRestartClusterMasterRestart,
		ErrorNextStep:   StepRestartClusterTaskStepRestartMasters,
	})

	// Step-3 执行所有从节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterMasterRestart,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     restart.ProcessRestartSlavesCluster,
		SuccessNextStep: StepRestartClusterSecondCheckSync,
		ErrorNextStep:   StepRestartClusterMasterRestart},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-3 检查所有从节点的同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterSecondCheckSync,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartClusterGlobalRestart,
		ErrorNextStep:   StepRestartClusterSecondCheckSync},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-3 执行所有热活从节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterGlobalRestart,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     restart.ProcessRestartSlavesClusterInGroup,
		SuccessNextStep: StepRestartClusterGlobalSlaveof,
		ErrorNextStep:   StepRestartClusterGlobalRestart},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-4 手动挂主，防止热活组找不到主的port
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterGlobalSlaveof,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: StepRestartClusterGlobalCheckSync,
		ErrorNextStep:   StepRestartClusterGlobalSlaveof},

		workflow.WithStepTimeout(5*time.Minute))

	// 检查同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterGlobalCheckSync,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartClusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepRestartClusterGlobalCheckSync,
	})

	// 部署filebeat for redis cluster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAll,
		SuccessNextStep: StepRestartClusterDeployFilebeat,
		ErrorNextStep:   StepRestartClusterPushMonitorHTGRPSlaveFlag,
	})

	// 部署filebeat for redis cluster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterDeployFilebeat,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepRestartClusterCloseTimeWindowTask,
		ErrorNextStep:   StepRestartClusterDeployFilebeat},
		workflow.WithStepTimeout(5*time.Minute),
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterCloseTimeWindowTask,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepRestartClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepRestartClusterCloseTimeWindowTask})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepRestartClusterCallback,
		ErrorNextStep:   StepRestartClusterUpdateAppTopologyInXmaster},
		workflow.WithMaxReentry(5, StepRestartClusterCallback))

	// Step-4 重启成功回调
	// 调用CsMaster的API，修改cluster状态为5
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterCallback,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     callback.ProcessUpgradeOrRestartSuccessCb,
		SuccessNextStep: StepRestartClusterTaskStepSuccess,
		ErrorNextStep:   StepRestartClusterCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterTaskStepSuccess,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRestartClusterAddSuccessEndEvent,
		ErrorNextStep:   StepRestartClusterTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartClusterAddSuccessEndEvent,
		Workflow:        WorkflowRestartCluster,
		StepProcess:     event.ProcessAddRestartingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRestartClusterAddSuccessEndEvent,
	})
}
