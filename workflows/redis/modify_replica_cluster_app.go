/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建SCS标准版实例WORKFLOW

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"DestSpec": "cache.n1.small"
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowModifyReplicaCluster                       = "scs-modify-replicas-cluster-app"
	StepModifyReplicaClusterBuildMeta                  = "scs-modify-replica-cluster-app-build-meta"
	StepModifyReplicaClusterTaskStepCreateNodes        = "scs-modify-replica-cluster-app-task-step-create-nodes"
	StepModifyReplicaClusterHandover                   = "scs-modify-replica-cluster-handover"
	StepModifyReplicaClusterFillSpec                   = "scs-modify-replica-cluster-fill-spec"
	StepModifyReplicaClusterCheckSubnetsEnoughIps      = "scs-modify-replica-cluster-check-subnets-enough-ips"
	StepModifyReplicaClusterApplyResources             = "scs-modify-replica-cluster-apply-resources"
	StepModifyReplicaClusterApplyResourcesCallback     = "scs-modify-replica-cluster-apply-resources-cb"
	StepModifyReplicaClusterTaskStepDeployNodes        = "scs-modify-replica-cluster-app-task-step-deploy-nodes"
	StepModifyReplicaClusterInitMachineEnv             = "scs-modify-replica-cluster-init-machine-env"
	StepModifyReplicaClusterAddGlobalNode              = "scs-modify-replica-cluster-add-global-node"
	StepModifyReplicaClusterUpdateSecurityGroups       = "scs-modify-replica-cluster-update-security-groups"
	StepModifyReplicaClusterMetaAddNodes               = "scs-modify-replica-cluster-meta-add-nodes"
	StepModifyReplicaClusterDeployRedis                = "scs-modify-replica-cluster-apply-deploy-redis"
	StepModifyReplicaClusterTaskStepConfigNodes        = "scs-modify-replica-cluster-app-task-step-config-nodes"
	StepModifyReplicaClusterSetGlobalSlaveOf           = "scs-modify-replica-cluster-set-global-slave-of"
	StepModifyReplicaClusterSetLocalSlaveOf            = "scs-modify-replica-cluster-set-local-slave-of"
	StepModifyReplicaClusterUpdateTlsConfIfNeeded      = "scs-modify-replica-cluster-update-tls-conf-if-needed"
	StepModifyReplicaClusterTaskStepSync               = "scs-modify-replica-cluster-app-task-step-sync"
	StepModifyReplicaClusterCheckAllSync               = "scs-modify-replica-cluster-check-all-sync"
	StepModifyReplicaClusterDeployFilebeat             = "scs-modify-replica-cluster-apply-deploy-filebeat"
	StepModifyReplicaClusterSetRs                      = "scs-modify-replica-cluster-set-rs"
	StepModifyReplicaClusterSetMcpackRs                = "scs-modify-replica-cluster-set-mcpack-rs"
	StepModifyReplicaClusterSetBnsInstance             = "scs-modify-replica-cluster-set-bns-instance"
	StepModifyReplicaClusterInitBcmResource            = "scs-modify-replica-cluster-init-bcm-resource"
	StepModifyReplicaClusterTaskStepReleaseNodes       = "scs-modify-replica-cluster-app-task-step-release-nodes"
	StepModifyReplicaClusterMetaDelNodes               = "scs-modify-replica-cluster-meta-del-nodes"
	StepModifyReplicaClusterDeleteOldNodes             = "scs-modify-replica-cluster-del-old-nodes"
	StepModifyReplicaClusterUpdateAppTopologyInXmaster = "scs-modify-replica-cluster-update-app-topo-in-xmaster"
	StepModifyReplicaClusterInitOpMonitor              = "scs-modify-replica-cluster-init-op-monitor"
	StepModifyReplicasClusterPushMonitorHTGRPSlaveFlag = "scs-modify-replicas-cluster-push-htgrp-slave-flag"
	StepModifyReplicasClusterPushFlag                  = "scs-modify-replicas-cluster-push-flag"
	StepModifyReplicaClusterTaskStepSuccess            = "scs-modify-replica-cluster-app-task-step-success"
	StepModifyReplicaClusterCreateCallback             = "scs-modify-replica-cluster-create-cb"
	StepModifyReplicaClusterRollbackReleaseResource    = "scs-modify-replica-cluster-rollback-release-resource"
	StepModifyReplicaClusterTaskStepError              = "scs-modify-replica-cluster-app-task-step-error"
	StepModifyReplicaClusterRollbackBcmResource        = "scs-modify-replica-cluster-rollback-bcm-resource"
	StepModifyReplicaClusterRollbackPushFlag           = "scs-modify-replica-cluster-rollback-push-flag"
	StepModifyReplicaClusterRollbackMeta               = "scs-modify-replica-cluster-rollback-meta"
	StepModifyReplicaClusterRollbackCallback           = "scs-modify-replica-cluster-rollback-callback"
)

const (
	WorkflowModifyReplicaClusterHandover                       = "scs-modify-replica-cluster-handover"
	StepModifyReplicaClusterHandoverAddStartEvent              = "scs-modify-replica-cluster-handover-add-start-event"
	StepModifyReplicaClusterHandoverHandover                   = "scs-modify-replica-cluster-handover-handover"
	StepModifyReplicaClusterHandoverSuccessCallback            = "scs-modify-replica-cluster-handover-succ-cb"
	StepModifyReplicaClusterHandoverUpdateAppTopologyInXmaster = "scs-modify-replica-cluster-handover-update-app-topo-in-xmaster"
	StepModifyReplicaClusterHandoverAddSuccessEndEvent         = "scs-modify-replica-cluster-handover-add-success-end-event"
)

func init() {
	// Step-1 修改x1元数据库
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterBuildMeta,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: buildmeta.ProcessBuildMetaForModifyReplicas,

		SuccessNextStep: StepModifyReplicaClusterTaskStepCreateNodes,
		ErrorNextStep:   StepModifyReplicaClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepCreateNodes,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepCreateNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaClusterHandover,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepCreateNodes,
	})

	// Step-2 检查是否有主节点为ToDelete状态，如果有，进行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterHandover,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: handover.ProcessHandoverClusterToDeleteMasters,

		SuccessNextStep: StepModifyReplicaClusterFillSpec,
		ErrorNextStep:   StepModifyReplicaClusterHandover,
	})

	// Step-3 将规格信息填入Cluster表或Interface表；
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterFillSpec,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: specification.ProcessFillSpec,

		SuccessNextStep: StepModifyReplicaClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepModifyReplicaClusterFillSpec,
	})

	// Step-4 检查子网ip是否充足
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterCheckSubnetsEnoughIps,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: checksubnets.CheckEnoughIPs,

		SuccessNextStep: StepModifyReplicaClusterApplyResources,
		ErrorNextStep:   StepModifyReplicaClusterCheckSubnetsEnoughIps,
	})

	// Step-5 创建资源，并将资源的信息存入Node或Proxy表中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterApplyResources,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: applyresource.ProcessApplyBccResources,

		SuccessNextStep: StepModifyReplicaClusterApplyResourcesCallback,
		ErrorNextStep:   StepModifyReplicaClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-6 将创建好的资源同步至Csmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterApplyResourcesCallback,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: callback.ProcessApplyResourceCallback,

		SuccessNextStep: StepModifyReplicaClusterTaskStepDeployNodes,
		ErrorNextStep:   StepModifyReplicaClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepDeployNodes,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepDeployNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaClusterInitMachineEnv,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepDeployNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterInitMachineEnv,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepModifyReplicaClusterAddGlobalNode,
		ErrorNextStep:   StepModifyReplicaClusterInitMachineEnv,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterAddGlobalNode,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepModifyReplicaClusterUpdateSecurityGroups,
		ErrorNextStep:   StepModifyReplicaClusterAddGlobalNode,
	})

	// Step-6 更新安全组规则
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterUpdateSecurityGroups,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: securitygroup.ProcessRebuildSecurityGroupCluster,

		SuccessNextStep: StepModifyReplicaClusterMetaAddNodes,
		ErrorNextStep:   StepModifyReplicaClusterUpdateSecurityGroups,
	})

	// Step-7 将新的节点加入Metaserver， 如果app是热活实例组成员，则调用Global Master更新相关元信息
	// 注册节点除了在Global Master中更新节点信息，还需要在Global MetaServer中更新节点信息，以及更新节点的安全组信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterMetaAddNodes,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: metaserver.ProcessAddNodes,

		SuccessNextStep: StepModifyReplicaClusterDeployRedis,
		ErrorNextStep:   StepModifyReplicaClusterMetaAddNodes,
	})

	// Step-8 部署Redis
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterDeployRedis,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: deploy.ProcessDeployAll,

		SuccessNextStep: StepModifyReplicaClusterTaskStepConfigNodes,
		ErrorNextStep:   StepModifyReplicaClusterDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepConfigNodes,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepConfigNodes, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaClusterSetGlobalSlaveOf,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepConfigNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterSetGlobalSlaveOf,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: gmaster.ProcessGlobalSlaveOf,

		SuccessNextStep: StepModifyReplicaClusterSetLocalSlaveOf,
		ErrorNextStep:   StepModifyReplicaClusterSetGlobalSlaveOf})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterSetLocalSlaveOf,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: handover.ProcessLocalSlaveOf,

		SuccessNextStep: StepModifyReplicaClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepModifyReplicaClusterSetLocalSlaveOf})

	// 包管理todo 检查能否加在这里
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterUpdateTlsConfIfNeeded,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: util.UpdateTLSConfIfNeededForNew,

		SuccessNextStep: StepModifyReplicaClusterTaskStepSync,
		ErrorNextStep:   StepModifyReplicaClusterUpdateTlsConfIfNeeded,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepSync,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepSync, timewindow.TaskStatusExecuting),

		SuccessNextStep: StepModifyReplicaClusterCheckAllSync,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepSync,
	})

	// 检查同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterCheckAllSync,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: syncredis.PocessCheckRedisAllSync,

		SuccessNextStep: StepModifyReplicaClusterDeployFilebeat,
		ErrorNextStep:   StepModifyReplicaClusterCheckAllSync},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-8 部署filebeat
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterDeployFilebeat,
		Workflow: WorkflowModifyReplicaCluster,

		StepProcess: deploy.ProcessDeployFilebeat,

		SuccessNextStep: StepModifyReplicaClusterSetRs,
		ErrorNextStep:   StepModifyReplicaClusterDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// Step-10 绑定BLB的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterSetRs,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepModifyReplicaClusterSetMcpackRs,
		ErrorNextStep:   StepModifyReplicaClusterSetRs,
	})

	// Step-10 绑定BLB的rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterSetMcpackRs,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepModifyReplicaClusterSetBnsInstance,
		ErrorNextStep:   StepModifyReplicaClusterSetMcpackRs,
	})

	// Step-11 绑定bns instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterSetBnsInstance,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: StepModifyReplicaClusterInitBcmResource,
		ErrorNextStep:   StepModifyReplicaClusterSetBnsInstance,
	})

	// Step-12 根据变更新建或删除监控对象
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterInitBcmResource,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     bcm.ProcessBcmResourceWithoutApp,
		SuccessNextStep: StepModifyReplicaClusterTaskStepReleaseNodes,
		ErrorNextStep:   StepModifyReplicaClusterInitBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepReleaseNodes,
		Workflow: WorkflowModifyReplicaCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.ChangeReplicasTask, timewindow.StepReleaseNodes, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepModifyReplicaClusterMetaDelNodes,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepReleaseNodes,
	})

	// Step-13 删除metaserver中待删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterMetaDelNodes,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepModifyReplicasClusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepModifyReplicaClusterMetaDelNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicasClusterPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepModifyReplicasClusterPushFlag,
		ErrorNextStep:   StepModifyReplicasClusterPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicasClusterPushFlag,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyReplicaClusterDeleteOldNodes,
		ErrorNextStep:   StepModifyReplicasClusterPushFlag,
	})

	// Step-14 删除待删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterDeleteOldNodes,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepModifyReplicaClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyReplicaClusterDeleteOldNodes,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyReplicaClusterInitOpMonitor,
		ErrorNextStep:   StepModifyReplicaClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterInitOpMonitor,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepModifyReplicaClusterTaskStepSuccess,
		ErrorNextStep:   StepModifyReplicaClusterInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepSuccess,
		Workflow: WorkflowModifyReplicaCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			"", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepModifyReplicaClusterCreateCallback,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepSuccess,
	})

	// Step-14 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterCreateCallback,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyReplicaClusterCreateCallback,
	})

	// Step-Error-01 删除已创建的节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterRollbackReleaseResource,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepModifyReplicaClusterTaskStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepModifyReplicaClusterTaskStepError,
		Workflow: WorkflowModifyReplicaCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			"", "", timewindow.TaskStatusError),
		SuccessNextStep: StepModifyReplicaClusterRollbackBcmResource,
		ErrorNextStep:   StepModifyReplicaClusterTaskStepError,
	})

	// Step-Error-02 删除已创建的节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterRollbackBcmResource,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     bcm.ProcessRollbackBcmResourceWithoutApp,
		SuccessNextStep: StepModifyReplicaClusterRollbackPushFlag,
		ErrorNextStep:   StepModifyReplicaClusterRollbackBcmResource,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterRollbackPushFlag,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepModifyReplicaClusterRollbackMeta,
		ErrorNextStep:   StepModifyReplicaClusterRollbackPushFlag,
	})

	// Step-Error-03 删除已创建的节点元信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterRollbackMeta,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepModifyReplicaClusterRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-Error-04 失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterRollbackCallback,
		Workflow:        WorkflowModifyReplicaCluster,
		StepProcess:     callback.ProcessModifyNodesErrorCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-01 可用区主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterHandoverAddStartEvent,
		Workflow:        WorkflowModifyReplicaClusterHandover,
		StepProcess:     event.ProcessAddSwitchingStartEvent,
		SuccessNextStep: StepModifyReplicaClusterHandoverHandover,
		ErrorNextStep:   StepModifyReplicaClusterHandoverAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterHandoverHandover,
		Workflow:        WorkflowModifyReplicaClusterHandover,
		StepProcess:     handover.ProcessHandoverClusterNewZone,
		SuccessNextStep: StepModifyReplicaClusterHandoverSuccessCallback,
		ErrorNextStep:   StepModifyReplicaClusterHandoverHandover,
	})

	// Step-02 可用区间主从切换成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterHandoverSuccessCallback,
		Workflow:        WorkflowModifyReplicaClusterHandover,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: StepModifyReplicaClusterHandoverUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepModifyReplicaClusterHandoverSuccessCallback,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterHandoverUpdateAppTopologyInXmaster,
		Workflow:        WorkflowModifyReplicaClusterHandover,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepModifyReplicaClusterHandoverAddSuccessEndEvent,
		ErrorNextStep:   StepModifyReplicaClusterHandoverUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepModifyReplicaClusterHandoverAddSuccessEndEvent,
		Workflow:        WorkflowModifyReplicaClusterHandover,
		StepProcess:     event.ProcessAddSwitchingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepModifyReplicaClusterHandoverAddSuccessEndEvent,
	})
}
