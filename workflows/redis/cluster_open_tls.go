package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowScsClusterOpenTls     = "scs-cluster-open-tls"
	StepOpenTlsClusterBuildMeta   = "scs-open-tls-cluster-app-build-meta"
	StepOpenTlsClusterCreateTls   = "scs-open-tls-cluster-app-create-tls"
	StepOpenTlsClusterDeliverConf = "scs-open-tls-cluster-app-deliver-conf"
	StepOpenTlsClusterCallback    = "scs-open-tls-cluster-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// 1、校验集群架构、版本
	// 2、cache_cluster   +  application   更新tls_port信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTlsClusterBuildMeta,
		Workflow:        WorkflowScsClusterOpenTls,
		StepProcess:     buildmeta.ProcessBuildMetaOpenTls,
		SuccessNextStep: StepOpenTlsClusterCreateTls,
		ErrorNextStep:   StepOpenTlsClusterBuildMeta,
	})

	// 创建ca证书
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTlsClusterCreateTls,
		Workflow:        WorkflowScsClusterOpenTls,
		StepProcess:     util.CreateTLS,
		SuccessNextStep: StepOpenTlsClusterDeliverConf,
		ErrorNextStep:   StepOpenTlsClusterCreateTls,
	})

	// 下发tls配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTlsClusterDeliverConf,
		Workflow:        WorkflowScsClusterOpenTls,
		StepProcess:     util.DeliverTLSConf,
		SuccessNextStep: StepOpenTlsClusterCallback,
		ErrorNextStep:   StepOpenTlsClusterDeliverConf},

		workflow.WithStepTimeout(15*time.Minute))

	/*
		成功回调:
			变更集群状态
			变更证书表状态为:inuse
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTlsClusterCallback,
		Workflow:        WorkflowScsClusterOpenTls,
		StepProcess:     callback.ProcessOpenTlsSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepOpenTlsClusterCallback,
	})
}
