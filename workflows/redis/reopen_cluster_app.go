/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* reopen_cluster_app.go - File Description */
/*
Modification History
--------------------
2022/5/23, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowReopenCluster              = "scs-reopen-cluster-app"
	StepReopenSyncGroupModifyWhitelist = "scs-reopen-sync-group-app-modify-whitelist"
	StepReopenClusterRebindRs          = "scs-reopen-cluster-app-rebind-rs"
	StepReopenClusterRebindMcpackRs    = "scs-reopen-cluster-app-rebind-mcpack-rs"
	StepReopenClusterSetBnsInstance    = "scs-reopen-cluster-app-set-bns-instance"
	StepReopenClusterCallback          = "scs-reopen-cluster-app-cb"
)

func init() {
	// Step-0 给sync-agent的集群增加全放开的白名单
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenSyncGroupModifyWhitelist,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     syncgroup.ProcessModifyWhiteList,
		SuccessNextStep: StepReopenClusterRebindRs,
		ErrorNextStep:   StepReopenSyncGroupModifyWhitelist,
	})

	// Step-1 重新挂载所有rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenClusterRebindRs,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     blb.ProcessRebingProxyRs,
		SuccessNextStep: StepReopenClusterRebindMcpackRs,
		ErrorNextStep:   StepReopenClusterRebindRs,
	})

	// Step-1 重新挂载所有rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenClusterRebindMcpackRs,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     blb.ProcessRebingProxyMcpackRs,
		SuccessNextStep: StepReopenClusterSetBnsInstance,
		ErrorNextStep:   StepReopenClusterRebindMcpackRs,
	})

	// Step-2 重新设置bns instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenClusterSetBnsInstance,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: StepReopenClusterCallback,
		ErrorNextStep:   StepReopenClusterSetBnsInstance,
	})

	// Step-2 成功回调
	// 调用CsMaster的API，修改cluster状态为5
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenClusterCallback,
		Workflow:        WorkflowReopenCluster,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepReopenClusterCallback,
	})
}
