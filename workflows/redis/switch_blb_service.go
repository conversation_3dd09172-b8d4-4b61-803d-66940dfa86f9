/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* switch_blb_service.go */
/*
modification history
--------------------
2022/08/30 , by wang<PERSON><PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
)

const (
	WorkflowSwitchBlbService = "switch_blb_service"
	StepSwitchBlbServiceExec = "switch_blb_service_exec"
	StepSwitchAppDomainExec  = "switch_app_domain_exec"
)

func init() {
	// Step-1 执行服务发布点的互换以及更新两个集群的服务网卡和服务发布点信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchBlbServiceExec,
		Workflow:        WorkflowSwitchBlbService,
		StepProcess:     endpoint.ProcessSwitchService,
		SuccessNextStep: StepSwitchAppDomainExec,
		ErrorNextStep:   StepSwitchBlbServiceExec,
	})

	// Step-2 更新 domain 信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchAppDomainExec,
		Workflow:        WorkflowSwitchBlbService,
		StepProcess:     dns.ProcessSwitchAppDomain,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSwitchAppDomainExec,
	})
}
