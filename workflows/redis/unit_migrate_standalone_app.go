/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/11/09, by wang<PERSON><PERSON>(<EMAIL>), node_migrate for container
*/

/*
DESCRIPTION
进行标准版集群实例迁移

Parameters
{
   "app_id" : "scs-bj-geahxzrqaqge",
   // 复用自愈参数
   "unhealth_shards" : [
      {
         "node_ids" : [
            "2.scs-bj-geahxzrqaqge-bj_pjaxamengvtb_0-AZONE-gzhxy.scs"
         ],
         "node_short_ids" : [
            4299
         ],
         "shard_short_id" : 4297
      }
   ],
   // 指定 label 迁移
   "resource_labels": [
        "uuid=79b24f0d-50d9-4fab-8e03-202bd9688c5c"
   ]
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/topology"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowUnitMigrateStandalone                               = "scs-unit-migrate-standalone-app"
	StepUnitMigrateStandaloneBuildMeta                          = "scs-unit-migrate-standalone-app-build-meta"
	StepUnitMigrateStandaloneSetRoTopology                      = "scs-unit-migrate-standalone-app-set-ro-topology"
	StepUnitMigrateStandaloneFillSpec                           = "scs-unit-migrate-standalone-fill-spec"
	StepUnitMigrateStandaloneCheckSubnetsEnoughIps              = "scs-unit-migrate-standalone-check-subnets-enough-ips"
	StepUnitMigrateStandaloneApplyResources                     = "scs-unit-migrate-standalone-apply-resources"
	StepUnitMigrateStandaloneApplyResourcesCallback             = "scs-unit-migrate-standalone-apply-resources-cb"
	StepUnitMigrateStandaloneInitMachineEnv                     = "scs-unit-migrate-standalone-init-machine-env"
	StepUnitMigrateStandaloneUpdateGlobalTopo                   = "scs-unit-migrate-standalone-update-global-topo"
	StepUnitMigrateStandaloneUpdateSecurityGroups               = "scs-unit-migrate-standalone-update-security-groups"
	StepUnitMigrateStandaloneDeployRedis                        = "scs-unit-migrate-standalone-apply-deploy-redis"
	StepUnitMigrateStandaloneSetAcl                             = "scs-unit-migrate-standalone-set-acl"
	StepUnitMigrateStandaloneSlaveOfGlobal                      = "scs-unit-migrate-standalone-slaveof-global"
	StepUnitMigrateStandaloneSlaveOfLocal                       = "scs-unit-migrate-standalone-slaveof-local"
	StepUnitMigrateStandaloneDeployFilebeat                     = "scs-unit-migrate-standalone-apply-deploy-filebeat"
	StepUnitMigrateStandaloneSecondSetRoTopology                = "scs-unit-migrate-standalone-app-second-set-ro-topology"
	StepUnitMigrateStandaloneCheckShardSync                     = "scs-unit-migrate-standalone-check-shard-sync"
	StepUnitMigrateStandaloneSetRs                              = "scs-unit-migrate-standalone-set-rs"
	StepUnitMigrateStandaloneSetRoRs                            = "scs-unit-migrate-standalone-set-ro-rs"
	StepUnitMigrateStandaloneSetEntranceRs                      = "scs-unit-migrate-standalone-set-entrance-rs"
	StepUnitMigrateStandalonePushMonitorHTGRPSlaveFlag          = "scs-unit-migrate-standalone-push-htgrp-slave-flag"
	StepUnitMigrateStandalonePushFlag                           = "scs-unit-migrate-standalone-push-flag"
	StepUnitMigrateStandaloneGlobalDelNodes                     = "scs-unit-migrate-standalone-global-del-nodes"
	StepUnitMigrateStandaloneDeleteOldNodes                     = "scs-unit-migrate-standalone-del-old-nodes"
	StepUnitMigrateStandaloneResetFailoverFlag                  = "scs-unit-migrate-standalone-reset-failover-flag"
	StepUnitMigrateStandaloneUpdateAppTopologyInXmaster         = "scs-unit-migrate-standalone-update-app-topo-in-xmaster"
	StepUnitMigrateStandaloneInitOpMonitor                      = "scs-unit-migrate-standalone-init-op-monitor"
	StepUnitMigrateStandaloneSuccessCallback                    = "scs-unit-migrate-standalone-succ-cb"
	StepUnitMigrateStandaloneRollbackReleaseResources           = "scs-unit-migrate-standalone-rollback-release-resource"
	StepUnitMigrateStandaloneRollbackPushFlag                   = "scs-unit-migrate-standalone-rollback-push-flag"
	StepUnitMigrateStandaloneRollbackMeta                       = "scs-unit-migrate-standalone-rollback-meta"
	StepUnitMigrateStandaloneRollbackCallback                   = "scs-unit-migrate-standalone-rollback-cb"
	StepUnitMigrateStandaloneRollbackUpdateAppTopologyInXmaster = "scs-unit-migrate-standalone-rollback-update-app-topo-in-xmaster"
)

func init() {
	// Step-1 构建元数据，将需要删除的节点改为ToDelete、新加节点ToCreate
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneBuildMeta,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForUnitMigrate,
		SuccessNextStep: StepUnitMigrateStandaloneSetRoTopology,
		ErrorNextStep:   StepUnitMigrateStandaloneBuildMeta,
	})

	// 更新热活组中的只读实例的主从关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSetRoTopology,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepUnitMigrateStandaloneFillSpec,
		ErrorNextStep:   StepUnitMigrateStandaloneSetRoTopology},

		workflow.WithMaxReentry(1, StepUnitMigrateStandaloneFillSpec))

	// Step-2 将规格信息填入Cluster表或Interface表；
	// 同分片的node应当规格相同，Port相同，使用同样的部署集
	// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneFillSpec,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepUnitMigrateStandaloneCheckSubnetsEnoughIps,
		ErrorNextStep:   StepUnitMigrateStandaloneFillSpec,
	})
	// Step-4 检查子网ip是否充足
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneCheckSubnetsEnoughIps,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepUnitMigrateStandaloneApplyResources,
		ErrorNextStep:   StepUnitMigrateStandaloneCheckSubnetsEnoughIps,
	})

	// Step-6 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneApplyResources,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepUnitMigrateStandaloneApplyResourcesCallback,
		ErrorNextStep:   StepUnitMigrateStandaloneApplyResources},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithMaxReentry(2, StepUnitMigrateStandaloneRollbackReleaseResources))

	// Step-6 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneApplyResourcesCallback,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepUnitMigrateStandaloneInitMachineEnv,
		ErrorNextStep:   StepUnitMigrateStandaloneApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneInitMachineEnv,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepUnitMigrateStandaloneUpdateGlobalTopo,
		ErrorNextStep:   StepUnitMigrateStandaloneInitMachineEnv,
	})

	// 更新标准版热活topo
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneUpdateGlobalTopo,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepUnitMigrateStandaloneUpdateSecurityGroups,
		ErrorNextStep:   StepUnitMigrateStandaloneUpdateGlobalTopo,
	})

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneUpdateSecurityGroups,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandalone,
		SuccessNextStep: StepUnitMigrateStandaloneDeployRedis,
		ErrorNextStep:   StepUnitMigrateStandaloneUpdateSecurityGroups,
	})

	// // 更新只读实例的安全组规则
	// _ = workflow.AddStep(&workflow.AddStepParam{
	// 	Name:            StepUnitMigrateStandaloneUpdateRoSecurityGroups,
	// 	Workflow:        WorkflowUnitMigrateStandalone,
	// 	StepProcess:     securitygroup.ProcessRebuildRoSecurityGroupStandalone,
	// 	SuccessNextStep: StepUnitMigrateStandaloneDeployRedis,
	// 	ErrorNextStep:   StepUnitMigrateStandaloneUpdateRoSecurityGroups,
	// })

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneDeployRedis,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepUnitMigrateStandaloneSetAcl,
		ErrorNextStep:   StepUnitMigrateStandaloneDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-7 设置初始auth,acl
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSetAcl,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     acl.ProcessInitAclStandaloneForNewNode,
		SuccessNextStep: StepUnitMigrateStandaloneSlaveOfGlobal,
		ErrorNextStep:   StepUnitMigrateStandaloneSetAcl,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSlaveOfGlobal,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: StepUnitMigrateStandaloneSlaveOfLocal,
		ErrorNextStep:   StepUnitMigrateStandaloneSlaveOfGlobal,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSlaveOfLocal,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     handover.ProcessLocalSlaveOf,
		SuccessNextStep: StepUnitMigrateStandaloneDeployFilebeat,
		ErrorNextStep:   StepUnitMigrateStandaloneSlaveOfLocal,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneDeployFilebeat,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepUnitMigrateStandaloneSecondSetRoTopology,
		ErrorNextStep:   StepUnitMigrateStandaloneDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// 第二次更新热活组中的只读实例的主从关系
	// 注意: 存在场景：从节点都跪了，只下发了部分只读实例的自愈，
	// 导致只读实例slaveof 失败，影响后续流程自愈
	// 增加重试后，故障的只读实例会再次自愈，直到能成功slaveof
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSecondSetRoTopology,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     topology.ProcessTopoForRoInGroup,
		SuccessNextStep: StepUnitMigrateStandaloneCheckShardSync,
		ErrorNextStep:   StepUnitMigrateStandaloneSecondSetRoTopology},

		workflow.WithMaxReentry(1, StepUnitMigrateStandaloneCheckShardSync))

	// 检查对应分片的主从同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneCheckShardSync,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     syncredis.ProcessCheckShardHasSyncForStandaloneFailover,
		SuccessNextStep: StepUnitMigrateStandaloneSetRs,
		ErrorNextStep:   StepUnitMigrateStandaloneCheckShardSync,
	})

	// Step 绑定BLB的rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSetRs,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     blb.ProcessUpdateStandaloneRs,
		SuccessNextStep: StepUnitMigrateStandaloneSetRoRs,
		ErrorNextStep:   StepUnitMigrateStandaloneSetRs,
	})

	// Step 绑定只读组rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSetRoRs,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     blb.ProcessSetReadonlyGroupRs,
		SuccessNextStep: StepUnitMigrateStandaloneSetEntranceRs,
		ErrorNextStep:   StepUnitMigrateStandaloneSetRoRs,
	})

	// 绑定统一读入口 rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSetEntranceRs,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepUnitMigrateStandalonePushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepUnitMigrateStandaloneSetEntranceRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandalonePushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepUnitMigrateStandalonePushFlag,
		ErrorNextStep:   StepUnitMigrateStandalonePushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandalonePushFlag,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepUnitMigrateStandaloneGlobalDelNodes,
		ErrorNextStep:   StepUnitMigrateStandalonePushFlag,
	})

	// 标准版热活删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneGlobalDelNodes,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     metaserver.ProcessDelNodesForStandalone,
		SuccessNextStep: StepUnitMigrateStandaloneDeleteOldNodes,
		ErrorNextStep:   StepUnitMigrateStandaloneGlobalDelNodes,
	})

	// Step-10 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneDeleteOldNodes,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepUnitMigrateStandaloneResetFailoverFlag,
		ErrorNextStep:   StepUnitMigrateStandaloneDeleteOldNodes,
	})

	// 重置failover flag
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneResetFailoverFlag,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     handover.ProcessResetFailoveredShards,
		SuccessNextStep: StepUnitMigrateStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepUnitMigrateStandaloneResetFailoverFlag,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepUnitMigrateStandaloneInitOpMonitor,
		ErrorNextStep:   StepUnitMigrateStandaloneUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneInitOpMonitor,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepUnitMigrateStandaloneSuccessCallback,
		ErrorNextStep:   StepUnitMigrateStandaloneInitOpMonitor,
	})

	// Step-11 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneSuccessCallback,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUnitMigrateStandaloneSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneRollbackReleaseResources,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepUnitMigrateStandaloneRollbackPushFlag,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneRollbackPushFlag,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepUnitMigrateStandaloneRollbackMeta,
		ErrorNextStep:   StepUnitMigrateStandaloneRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneRollbackMeta,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepUnitMigrateStandaloneRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneRollbackCallback,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: StepUnitMigrateStandaloneRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnitMigrateStandaloneRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowUnitMigrateStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
