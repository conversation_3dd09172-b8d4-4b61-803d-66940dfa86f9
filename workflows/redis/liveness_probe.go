/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* liveness_probe.go */
/*
modification history
--------------------
2023/01/01 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
模拟二次探测的测试任务
*/

package workflows

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowScsLivenessProbe    = "scs-liveness-probe"
	StepWorkflowScsLivenessProb = "scs-liveness-probe-step"
)

func init() {
	// 标准版手动主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepWorkflowScsLivenessProb,
		Workflow:        WorkflowScsLivenessProbe,
		StepProcess:     livenessProbe,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepWorkflowScsLivenessProb,
	})
}

func livenessProbe(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var target *x1model.Node = nil
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				target = node
			}
		}
	}

	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			_, err := util.LivenessProbe(ctx, &xagent.Addr{
				Host: node.FloatingIP,
				Port: int32(node.XagentPort),
			}, target, password)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
