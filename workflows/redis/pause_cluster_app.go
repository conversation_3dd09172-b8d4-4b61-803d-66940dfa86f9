/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* pause_cluster_app.go - File Description */
/*
Modification History
--------------------
2022/5/23, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
)

const (
	WorkflowPauseClusterApp              = "scs-pause-cluster-app"
	StepPauseSyncGroupModifyWhitelist    = "scs-pause-sync-group-app-modify-whitelist"
	StepPauseClusterUnbindAllRs          = "scs-pause-cluster-app-unbind-all-rs"
	StepPauseClusterUnbindMcpackRs       = "scs-pause-cluster-app-unbind-mcpack-rs"
	StepPauseClusterClearAllBnsInstances = "scs-pause-cluster-app-clear-all-bns-instances"
	StepPauseClusterCallbacks            = "scs-pause-cluster-app-callbacks"
)

func init() {

	// Step-0 给sync-agent的集群增加全放开的白名单
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseSyncGroupModifyWhitelist,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     syncgroup.ProcessUpdateSyncGroupWhiteList, // 给proxy增加所有实例的白名单，清空其他的白名单
		SuccessNextStep: StepPauseClusterUnbindAllRs,
		ErrorNextStep:   StepPauseSyncGroupModifyWhitelist,
	})

	// Step-1 摘除所有Rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseClusterUnbindAllRs,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     blb.ProcessUnbindAllProxys,
		SuccessNextStep: StepPauseClusterUnbindMcpackRs,
		ErrorNextStep:   StepPauseClusterUnbindAllRs,
	})

	// Step-1 摘除所有Rs(for mcpack)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseClusterUnbindMcpackRs,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     blb.ProcessUnbindAllProxysMcpackRs,
		SuccessNextStep: StepPauseClusterClearAllBnsInstances,
		ErrorNextStep:   StepPauseClusterUnbindMcpackRs,
	})

	// Step-2 清除所有BnsInstances
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseClusterClearAllBnsInstances,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     bns.ProcessClearBnsInstances,
		SuccessNextStep: StepPauseClusterCallbacks,
		ErrorNextStep:   StepPauseClusterClearAllBnsInstances,
	})

	// Step-2 成功回调
	// 调用CsMaster的API，修改cluster状态为8
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPauseClusterCallbacks,
		Workflow:        WorkflowPauseClusterApp,
		StepProcess:     callback.ProcessPauseCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPauseClusterCallbacks,
	})
}
