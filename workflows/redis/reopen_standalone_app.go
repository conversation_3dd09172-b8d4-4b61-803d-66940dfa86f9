/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
重新开启集群集群（NewMasterServiceImpl::reboot_cache_cluster， RedisV7RebootingState::handle）
// yuning

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowReopenStandalone             = "scs-reopen-standalone-app"
	StepReopenStandaloneReopen           = "scs-reopen-standalone-app-reopen"
	StepReopenStandaloneRebindAllRs      = "scs-reopen-standalone-rebind-all-rs"
	StepReopenStandaloneRebindEntranceRs = "scs-reopen-standalone-rebind-entrance-rs"
	StepReopenStandaloneCallback         = "scs-reopen-standalone-app-cb"
)

func init() {
	// Step-1 重新挂载所有rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenStandaloneReopen,
		Workflow:        WorkflowReopenStandalone,
		StepProcess:     blb.ProcessRebingStandaloneRs,
		SuccessNextStep: StepReopenStandaloneRebindAllRs,
		ErrorNextStep:   StepReopenStandaloneReopen,
	})

	// Step-2 重新挂载所有只读rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenStandaloneRebindAllRs,
		Workflow:        WorkflowReopenStandalone,
		StepProcess:     blb.ProcessRebingReadonlyRs,
		SuccessNextStep: StepReopenStandaloneRebindEntranceRs,
		ErrorNextStep:   StepReopenStandaloneRebindAllRs,
	})

	// Step-2 重新挂载所有entrance rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenStandaloneRebindEntranceRs,
		Workflow:        WorkflowReopenStandalone,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepReopenStandaloneCallback,
		ErrorNextStep:   StepReopenStandaloneRebindEntranceRs,
	})

	// Step-3 成功回调
	// 调用CsMaster的API，修改cluster状态为5
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepReopenStandaloneCallback,
		Workflow:        WorkflowReopenStandalone,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepReopenStandaloneCallback,
	})
}
