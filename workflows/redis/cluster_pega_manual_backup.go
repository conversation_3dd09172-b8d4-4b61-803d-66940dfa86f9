package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowScsClusterPegaManualBackup           = "scs-cluster-pega-manual-backup"
	StepPegaManualBackupCheckAndUpdateBackupTask = "scs-pega-manual-backup-check-and-update-backup-task"
	StepPegaManualBackupCb                       = "scs-pega-manual-backup-cb" // 将cache_cluster backupstatus改成0
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPegaManualBackupCheckAndUpdateBackupTask,
		Workflow:        WorkflowScsClusterPegaManualBackup,
		StepProcess:     backup.ProcessPegaManualBackupTask,
		SuccessNextStep: StepPegaManualBackupCb,
		ErrorNextStep:   StepPegaManualBackupCheckAndUpdateBackupTask},
		// 备份任务最大执行时间 10h
		workflow.WithStepTimeout(10*60*time.Minute))

	/*
		成功回调:
			将cache_cluster backupstatus改成0
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPegaManualBackupCb,
		Workflow:        WorkflowScsClusterPegaManualBackup,
		StepProcess:     callback.ProcessManualBackupCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPegaManualBackupCb,
	})
}
