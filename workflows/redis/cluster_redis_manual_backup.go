package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowScsClusterRedisManualBackup           = "scs-cluster-redis-manual-backup"
	StepRedisManualBackupCheckAndUpdateBackupTask = "scs-redis-manual-backup-check-and-update-backup-task"
	StepRedisManualBackupCb                       = "scs-redis-manual-backup-cb" // 将cache_cluster backupstatus改成0
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRedisManualBackupCheckAndUpdateBackupTask,
		Workflow:        WorkflowScsClusterRedisManualBackup,
		StepProcess:     backup.ProcessRedisManualBackupTask,
		SuccessNextStep: StepRedisManualBackupCb,
		ErrorNextStep:   StepRedisManualBackupCheckAndUpdateBackupTask},
		// 备份任务最大执行时间 10h
		workflow.WithStepTimeout(10*60*time.Minute))

	/*
		成功回调:
			将cache_cluster backupstatus改成0
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRedisManualBackupCb,
		Workflow:        WorkflowScsClusterRedisManualBackup,
		StepProcess:     callback.ProcessManualBackupCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRedisManualBackupCb,
	})
}
