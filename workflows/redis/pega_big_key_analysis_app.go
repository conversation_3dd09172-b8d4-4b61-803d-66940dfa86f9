/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file pega_big_key_analysis_app.go
 * <AUTHOR>
 * @date 2023/03/22 14:57:19
 * @brief
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/analysis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowPegaBigKeyAnalysis         = "scs-pega-bigkey-analysis-app"
	StepPegaBigKeyAnalysisBuildMeta    = "scs-pega-big-key-analysis-build-meta"
	StepPegaBigKeyAnalysisDispatchTask = "scs-pega-big-key-analysis-dispatch-task"
	StepPegaBigKeyAnalysisCallBack     = "scs-pega-big-key-analysis-cb"
)

func init() {
	// Step-1 build meta
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPegaBigKeyAnalysisBuildMeta,
		Workflow:        WorkflowPegaBigKeyAnalysis,
		StepProcess:     buildmeta.ProcessBuildMetaForPegaAnalysis,
		SuccessNextStep: StepPegaBigKeyAnalysisDispatchTask,
		ErrorNextStep:   StepPegaBigKeyAnalysisBuildMeta,
	})

	// Step-2 dispatch task
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPegaBigKeyAnalysisDispatchTask,
		Workflow:        WorkflowPegaBigKeyAnalysis,
		StepProcess:     analysis.ProcessDispatchAnalysisTask,
		SuccessNextStep: StepPegaBigKeyAnalysisCallBack,
		ErrorNextStep:   StepPegaBigKeyAnalysisDispatchTask},

		workflow.WithStepTimeout(120*time.Minute),
		workflow.WithStepSplitHandler(util.GetToAnalysisRedisIds))

	// Step-3 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPegaBigKeyAnalysisCallBack,
		Workflow:        WorkflowPegaBigKeyAnalysis,
		StepProcess:     callback.ProcessAnalysisBigKeyCallBack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPegaBigKeyAnalysisCallBack,
	})
}
