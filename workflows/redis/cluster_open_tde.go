package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowScsClusterOpenTde      = "scs-cluster-open-tde"
	StepOpenTdeClusterBuildMeta    = "scs-open-tde-cluster-app-build-meta"
	StepOpenTdeClusterCreateTdeKey = "scs-open-tde-cluster-app-create-tde-key"
	StepOpenTdeClusterDeliverConf  = "scs-open-tde-cluster-app-deliver-conf"
	StepOpenTdeClusterPostCheck    = "scs-open-tde-cluster-app-post-check"
	StepOpenTdeClusterCallback     = "scs-open-tde-cluster-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTdeClusterBuildMeta,
		Workflow:        WorkflowScsClusterOpenTde,
		StepProcess:     buildmeta.ProcessBuildMetaOpenTde,
		SuccessNextStep: StepOpenTdeClusterCreateTdeKey,
		ErrorNextStep:   StepOpenTdeClusterBuildMeta,
	})

	// 创建tde-key
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTdeClusterCreateTdeKey,
		Workflow:        WorkflowScsClusterOpenTde,
		StepProcess:     util.CreateTDE,
		SuccessNextStep: StepOpenTdeClusterDeliverConf,
		ErrorNextStep:   StepOpenTdeClusterCreateTdeKey,
	})

	// 下发配置
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTdeClusterDeliverConf,
		Workflow:        WorkflowScsClusterOpenTde,
		StepProcess:     util.DeliverTdeConf,
		SuccessNextStep: StepOpenTdeClusterPostCheck,
		ErrorNextStep:   StepOpenTdeClusterDeliverConf},

		workflow.WithStepTimeout(15*time.Minute))

	// 后置检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTdeClusterPostCheck,
		Workflow:        WorkflowScsClusterOpenTde,
		StepProcess:     util.OpenTdePostCheck,
		SuccessNextStep: StepOpenTdeClusterCallback,
		ErrorNextStep:   StepOpenTdeClusterPostCheck},

		workflow.WithStepTimeout(60*time.Minute))

	/*
		成功回调:
			变更集群状态
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepOpenTdeClusterCallback,
		Workflow:        WorkflowScsClusterOpenTde,
		StepProcess:     callback.ProcessOpenTdeSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepOpenTdeClusterCallback,
	})
}
