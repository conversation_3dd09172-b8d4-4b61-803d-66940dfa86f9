/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* shard_failover.go */
/*
modification history
--------------------
2022/07/07 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
进行标准版集群故障节点自愈-故障切换
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowShardFailover                       = "scs-shard-failover"
	StepShardFailoverFailover                   = "scs-shard-failover-build-meta"
	StepShardFailoverFixTopologyInMetaserver    = "scs-shard-failover-fix-topology-in-metaserver"
	StepShardFailoverOldMasterClientKill        = "scs-shard-failover-old-master-client-kill"
	StepShardFailoverResetFlag                  = "scs-shard-failover-reset-flag"
	StepShardFailoverUpdateAppTopologyInXmaster = "scs-shard-failover-update-app-topo-in-xmaster"
	StepShardFailoverTryFixReplication          = "scs-shard-failover-try-fix-replication"
)

func init() {
	// Step-01 进行故障切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepShardFailoverFailover,
		Workflow:        WorkflowShardFailover,
		StepProcess:     handover.ProcessHandoverClusterShardFromCsmaster,
		SuccessNextStep: StepShardFailoverFixTopologyInMetaserver,
		ErrorNextStep:   StepShardFailoverFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepShardFailoverFixTopologyInMetaserver,
		Workflow:        WorkflowShardFailover,
		StepProcess:     metaserver.ProcessTryCorrectShardMasterInMetaserver,
		SuccessNextStep: StepShardFailoverOldMasterClientKill,
		ErrorNextStep:   StepShardFailoverFixTopologyInMetaserver,
	})

	// Step-02 关闭旧主节点的客户端连接
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepShardFailoverOldMasterClientKill,
		Workflow:        WorkflowShardFailover,
		StepProcess:     handover.ProcessOldMasterClientKill,
		SuccessNextStep: StepShardFailoverResetFlag,
		ErrorNextStep:   StepShardFailoverOldMasterClientKill,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepShardFailoverResetFlag,
		Workflow:        WorkflowShardFailover,
		StepProcess:     handover.ProcessResetCsmasterSwitchFlag,
		SuccessNextStep: StepShardFailoverUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepShardFailoverResetFlag,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepShardFailoverUpdateAppTopologyInXmaster,
		Workflow:        WorkflowShardFailover,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepShardFailoverTryFixReplication,
		ErrorNextStep:   StepShardFailoverUpdateAppTopologyInXmaster,
	})

	// 尝试检查并修复复制关系
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepShardFailoverTryFixReplication,
		Workflow:        WorkflowShardFailover,
		StepProcess:     handover.ProcessTryFixReplication,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepShardFailoverTryFixReplication,
	},
		// 修复复制关系，单次超时时间10s
		workflow.WithStepTimeout(10*time.Second),
		// 修复复制关系，最多重试5次，共执行6次; 实测发现，设置为5，一共执行了7次，故改成4
		workflow.WithMaxReentry(4, workflow.FinalStepSuccess),
	)
}
