/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
执行删除标准版实例（RedisState::delete_cluster(））
// shangshuai

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deleteall"
)

const (
	WorkflowDeleteStandalone          = "scs-delete-standalone-app"
	StepDeleteStandaloneDeleteExecute = "scs-delete-standalone-delete-execute"
)

// import (
// 	"time"

// 	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
// 	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
// 	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
// 	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
// 	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
// 	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
// 	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
// )

// func init() {
// 	workflow.RegisterWorkerFlow(&workflow.WorkFlowOpt{
// 		Name:      "scs-delete-standalone-app",
// 		Dimension: workflow.DimApplication,
// 	})

// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-build-meta",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-build-meta", buildmeta.ProcessBuildMetaForDelete)

// 	// Step-1 删除资源
// 	// 1. 删除虚机资源
// 	// 2. 解绑eip、vpc安全组等
// 	// 3. 删除blb、dns等
// 	// 4. 删除安全组
// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-unbind-security-groups",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-unbind-security-groups", securitygroup.ProcessUnbindSecurityGroup)

// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-delete-bcc-resources",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-delete-bcc-resources", delresource.ProcessDelBccResources)

// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-delete-domain",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-delete-domain", dns.ProcessDeleteAppDomain)

// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-unbind-eip",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-unbind-eip", blb.ProcessUnbindEIP)

// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-delete-blb",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-delete-blb", blb.ProcessDelBLB)

// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-delete-security-groups",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-delete-security-groups", securitygroup.ProcessDelSecurityGroup)

// 	// Step-2 调用Csmaster接口，完成Csmaster相关元数据删除
// 	workflow.AddWorkerFlowStep(&workflow.StepOpt{
// 		Name:       "scs-delete-standalone-app-delete-callback",
// 		WorkFlow:   "scs-delete-standalone-app",
// 		Dimension:  workflow.DimApplication,
// 		CheckPoint: false,
// 		Timeout:    3 * time.Minute,
// 	})
// 	processor.RegisterProcessor("scs-delete-standalone-app-delete-callback", callback.ProcessDeleteStandaloneCb)

// }

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepDeleteStandaloneDeleteExecute,
		Workflow: WorkflowDeleteStandalone,

		StepProcess: deleteall.ProcessDeleteAll,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteStandaloneDeleteExecute,
	})
}
