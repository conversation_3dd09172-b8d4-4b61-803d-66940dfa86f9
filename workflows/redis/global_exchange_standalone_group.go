/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_exchange_standalone_group.go
 * <AUTHOR>
 * @date 2022/07/22 17:33:09
 * @brief 标准版热活实例组切主
 *
 **/
package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalExchangeStandaloneGroup delete standalone group workflow
const (
	WorkFlowLocalExchangeStandaloneGroup                           = "scs-local-exchange-standalone-group"
	WorkFlowLocalExchangeStandaloneGroupStepBindRs                 = "scs-local-exchange-standalone-group-bind-rs"
	WorkFlowLocalExchangeStandaloneGroupStepUpdateSg               = "scs-local-exchange-standalone-update-sg"
	WorkFlowLocalExchangeStandaloneGroupBindEntranceRs             = "scs-local-exchange-standalone-group-bind-entrance-rs"
	WorkFlowLocalExchangeStandaloneGroupUpdateAzdeployInfo         = "scs-local-exchange-standalone-group-update-azdeployinfo"
	WorkFlowLocalExchangeStandaloneGroupPushMonitorHTGRPSlaveFlag  = "scs-local-exchange-standalone-group-push-htgrp-slave-flag"
	WorkFlowLocalExchangeStandaloneGroupUpdateAppTopologyInXmaster = "scs-local-exchange-standalone-group-update-app-topo-in-xmaster"
	WorkFlowLocalExchangeStandaloneGroupStepSuccessCb              = "scs-local-exchange-standalone-group-success-cb"
)

func init() {
	// 更新blb的绑定rs 需要保证app.GroupId未被清理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupStepBindRs,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     blb.ProcessUpdateStandaloneRs,
		SuccessNextStep: WorkFlowLocalExchangeStandaloneGroupStepUpdateSg,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupStepBindRs,
	})

	// 更新security group白名单ip，将热活实例组中所有地域、所有redis、proxy的fixip加入；更新acl信息保证与globalmaster一致
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupStepUpdateSg,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupStandalone,
		SuccessNextStep: WorkFlowLocalExchangeStandaloneGroupBindEntranceRs,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupStepUpdateSg,
	})

	// 更新entrance rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupBindEntranceRs,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: WorkFlowLocalExchangeStandaloneGroupUpdateAzdeployInfo,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupBindEntranceRs,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupUpdateAzdeployInfo,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     handover.ProcessHandoverUpdateAzDeployInfo,
		SuccessNextStep: WorkFlowLocalExchangeStandaloneGroupPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupUpdateAzdeployInfo,
	}, workflow.WithMaxReentry(3, WorkFlowLocalExchangeStandaloneGroupPushMonitorHTGRPSlaveFlag))

	// 热活实例推送flag
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAll,
		SuccessNextStep: WorkFlowLocalExchangeStandaloneGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupPushMonitorHTGRPSlaveFlag,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalExchangeStandaloneGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupUpdateAppTopologyInXmaster,
	})

	// 回调success
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalExchangeStandaloneGroupStepSuccessCb,
		Workflow:        WorkFlowLocalExchangeStandaloneGroup,
		StepProcess:     callback.ProcessGeneralSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalExchangeStandaloneGroupStepSuccessCb,
	})
}
