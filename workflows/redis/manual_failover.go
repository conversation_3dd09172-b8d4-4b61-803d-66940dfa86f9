package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowManualFailoverStandalone                       = "scs-workflow-manual-failover-standalone"
	StepManualFailoverStandaloneSyncCheck                  = "scs-workflow-manual-failover-standalone-sync-check"
	StepManualFailoverStandaloneFailover                   = "scs-workflow-manual-failover-standalone-failover"
	StepManualFailoverStandaloneUpdateAzDeployInfo         = "scs-workflow-manual-failover-standalone-update-azdeployinfo"
	StepManualFailoverStandaloneUpdateAppTopologyInXmaster = "scs-workflow-manual-failover-standalone-update-app-topo-in-xmaster"
)

const (
	WorkflowManualFailoverCluster                       = "scs-workflow-manual-failover-cluster"
	StepManualFailoverClusterSyncCheck                  = "scs-workflow-manual-failover-cluster-sync-check"
	StepManualFailoverClusterFailover                   = "scs-workflow-manual-failover-cluster-failover"
	StepManualFailoverClusterUpdateAppTopologyInXmaster = "scs-workflow-manual-failover-cluster-update-app-topo-in-xmaster"
)

const (
	WorkflowSwitchMasterSlaveStandalone                       = "scs-workflow-switch-master-slave-standalone"
	StepSwitchMasterSlaveStandaloneSyncCheck                  = "scs-workflow-switch-master-slave-standalone-sync-check"
	StepSwitchMasterSlaveStandaloneAddStartEvent              = "scs-workflow-switch-master-slave-standalone-add-start-event"
	StepSwitchMasterSlaveStandaloneHandover                   = "scs-workflow-switch-master-slave-standalone-handover"
	StepSwitchMasterSlaveStandaloneUpdateAzDeployInfo         = "scs-workflow-switch-master-slave-standalone-update-azdeployinfo"
	StepSwitchMasterSlaveStandaloneUpdateAppTopologyInXmaster = "scs-workflow-switch-master-slave-standalone-update-app-topo-in-xmaster"
	StepSwitchMasterSlaveStandaloneSuccessCallback            = "scs-workflow-switch-master-slave-standalone-succ-cb"
	StepSwitchMasterSlaveStandaloneAddSuccessEndEvent         = "scs-workflow-switch-master-slave-standalone-add-success-end-event"
)

const (
	WorkflowSwitchMasterSlaveCluster                       = "scs-workflow-switch-master-slave-cluster"
	StepSwitchMasterSlaveClusterSyncCheck                  = "scs-workflow-switch-master-slave-cluster-sync-check"
	StepSwitchMasterSlaveClusterAddStartEvent              = "scs-workflow-switch-master-slave-cluster-add-start-event"
	StepSwitchMasterSlaveClusterHandover                   = "scs-workflow-switch-master-slave-cluster-handover"
	StepSwitchMasterSlaveClusterUpdateAppTopologyInXmaster = "scs-workflow-switch-master-slave-cluster-update-app-topo-in-xmaster"
	StepSwitchMasterSlaveClusterSuccessCallback            = "scs-workflow-switch-master-slave-cluster-succ-cb"
	StepSwitchMasterSlaveClusterAddSuccessEndEvent         = "scs-workflow-switch-master-slave-cluster-add-success-end-event"
)

func init() {
	// 标准版手动主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverStandaloneSyncCheck,
		Workflow:        WorkflowManualFailoverStandalone,
		StepProcess:     syncredis.ProcessCheckShardSyncForManualFailover,
		SuccessNextStep: StepManualFailoverStandaloneFailover,
		ErrorNextStep:   StepManualFailoverStandaloneSyncCheck,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverStandaloneFailover,
		Workflow:        WorkflowManualFailoverStandalone,
		StepProcess:     handover.ProcessStandaloneManualFailover,
		SuccessNextStep: StepManualFailoverStandaloneUpdateAzDeployInfo,
		ErrorNextStep:   StepManualFailoverStandaloneFailover,
	}, workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverStandaloneUpdateAzDeployInfo,
		Workflow:        WorkflowManualFailoverStandalone,
		StepProcess:     handover.ProcessHandoverUpdateAzDeployInfo,
		SuccessNextStep: StepManualFailoverStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepManualFailoverStandaloneUpdateAzDeployInfo,
	}, workflow.WithMaxReentry(3, StepManualFailoverStandaloneUpdateAppTopologyInXmaster))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowManualFailoverStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepManualFailoverStandaloneUpdateAppTopologyInXmaster,
	})

	// 集群版手动主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverClusterSyncCheck,
		Workflow:        WorkflowManualFailoverCluster,
		StepProcess:     syncredis.ProcessCheckShardSyncForManualFailover,
		SuccessNextStep: StepManualFailoverClusterFailover,
		ErrorNextStep:   StepManualFailoverClusterSyncCheck,
	}, workflow.WithStepTimeout(5*time.Minute))
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverClusterFailover,
		Workflow:        WorkflowManualFailoverCluster,
		StepProcess:     handover.ProcessClusterManualFailover,
		SuccessNextStep: StepManualFailoverClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepManualFailoverClusterFailover,
	}, workflow.WithStepTimeout(5*time.Minute))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepManualFailoverClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowManualFailoverCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepManualFailoverClusterUpdateAppTopologyInXmaster,
	})

	// 标准版(用户)手动主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneSyncCheck,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     syncredis.ProcessCheckShardSyncForSwitchMasterSlave,
		SuccessNextStep: StepSwitchMasterSlaveStandaloneAddStartEvent,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneSyncCheck,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneAddStartEvent,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     event.ProcessAddSwitchingStartEvent,
		SuccessNextStep: StepSwitchMasterSlaveStandaloneHandover,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneHandover,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     handover.ProcessStandaloneManualSwitchMasterSlave,
		SuccessNextStep: StepSwitchMasterSlaveStandaloneUpdateAzDeployInfo,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneHandover,
	}, workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneUpdateAzDeployInfo,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     handover.ProcessHandoverUpdateAzDeployInfo,
		SuccessNextStep: StepSwitchMasterSlaveStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneUpdateAzDeployInfo,
	}, workflow.WithMaxReentry(3, StepSwitchMasterSlaveStandaloneUpdateAppTopologyInXmaster))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepSwitchMasterSlaveStandaloneSuccessCallback,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneSuccessCallback,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: StepSwitchMasterSlaveStandaloneAddSuccessEndEvent,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveStandaloneAddSuccessEndEvent,
		Workflow:        WorkflowSwitchMasterSlaveStandalone,
		StepProcess:     event.ProcessAddSwitchingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSwitchMasterSlaveStandaloneAddSuccessEndEvent,
	})

	// 集群版(用户)手动主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveClusterSyncCheck,
		Workflow:        WorkflowSwitchMasterSlaveCluster,
		StepProcess:     syncredis.ProcessCheckShardSyncForSwitchMasterSlave,
		SuccessNextStep: StepSwitchMasterSlaveClusterAddStartEvent,
		ErrorNextStep:   StepSwitchMasterSlaveClusterSyncCheck,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveClusterAddStartEvent,
		Workflow:        WorkflowSwitchMasterSlaveCluster,
		StepProcess:     event.ProcessAddSwitchingStartEvent,
		SuccessNextStep: StepSwitchMasterSlaveClusterHandover,
		ErrorNextStep:   StepSwitchMasterSlaveClusterAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveClusterHandover,
		Workflow:        WorkflowSwitchMasterSlaveCluster,
		StepProcess:     handover.ProcessClusterManualSwitchMasterSlave,
		SuccessNextStep: StepSwitchMasterSlaveClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepSwitchMasterSlaveClusterHandover,
	}, workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSwitchMasterSlaveCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepSwitchMasterSlaveClusterSuccessCallback,
		ErrorNextStep:   StepSwitchMasterSlaveClusterUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveClusterSuccessCallback,
		Workflow:        WorkflowSwitchMasterSlaveCluster,
		StepProcess:     callback.ProcessModifyNodesSuccCb,
		SuccessNextStep: StepSwitchMasterSlaveClusterAddSuccessEndEvent,
		ErrorNextStep:   StepSwitchMasterSlaveClusterSuccessCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchMasterSlaveClusterAddSuccessEndEvent,
		Workflow:        WorkflowSwitchMasterSlaveCluster,
		StepProcess:     event.ProcessAddSwitchingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSwitchMasterSlaveClusterAddSuccessEndEvent,
	})

}
