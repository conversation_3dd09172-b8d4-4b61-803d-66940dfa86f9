/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_readonly_instance_app.go
 * <AUTHOR>
 * @date 2022/05/16 18:06:09
 * @brief delete readonly instances

 *
 **/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bcm"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowDeleteAllReadonlyGroup                       = "scs-delete-all-ro-group-app"
	StepDeleteAllReadonlyGroupBuildMeta                  = "scs-delete-all-ro-group-app-build-meta"
	StepDeleteAllReadonlyGroupDeleteEndpoint             = "scs-delete-all-ro-group-app-delete-endpoint"
	StepDeleteAllReadonlyGroupUpdateBlb                  = "scs-delete-all-ro-group-app-update-blb"
	StepDeleteAllReadonlyGroupUpdateEntrance             = "scs-delete-all-ro-group-app-update-entrance"
	StepDeleteAllReadonlyGroupDeleteDomain               = "scs-delete-all-ro-group-app-delete-domain"
	StepDeleteAllReadonlyGroupRemoveFromBcmGroup         = "scs-delete-all-ro-group-app-remove-from-bcm-group"
	StepDeleteAllReadonlyGroupDeleteResource             = "scs-delete-all-ro-group-app-delete-resource"
	StepDeleteAllReadonlyGroupDeleteAllRoNodes           = "scs-delete-all-ro-group-app-delete-all-ro-nodes"
	StepDeleteAllReadonlyGroupUpdateAppTopologyInXmaster = "scs-delete-all-ro-group-app-update-app-topo-in-xmaster"
	StepDeleteAllReadonlyGroupDeleteCallback             = "scs-delete-all-ro-group-app-delete-callback"
)

func init() {
	// 注册删除SCS标准版实例只读实例组WORKFLOW
	// Step-1 build meta
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupBuildMeta,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForDeletingAllRoGroup,
		SuccessNextStep: StepDeleteAllReadonlyGroupDeleteEndpoint,
		ErrorNextStep:   StepDeleteAllReadonlyGroupBuildMeta,
	})

	// Step delete endpoint
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupDeleteEndpoint,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     endpoint.ProcessDeleteRoEndpointAll,
		SuccessNextStep: StepDeleteAllReadonlyGroupUpdateBlb,
		ErrorNextStep:   StepDeleteAllReadonlyGroupDeleteEndpoint},

		workflow.WithMaxReentry(2, StepDeleteAllReadonlyGroupUpdateBlb))

	// Step-2 update blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupUpdateBlb,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     blb.ProcessDeletingAllRoDelBLB,
		SuccessNextStep: StepDeleteAllReadonlyGroupUpdateEntrance,
		ErrorNextStep:   StepDeleteAllReadonlyGroupUpdateBlb},

		workflow.WithMaxReentry(2, StepDeleteAllReadonlyGroupUpdateEntrance))

	// Step-2 update entrance blb
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupUpdateEntrance,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     blb.ProcessSetEntranceRs,
		SuccessNextStep: StepDeleteAllReadonlyGroupDeleteDomain,
		ErrorNextStep:   StepDeleteAllReadonlyGroupUpdateEntrance},

		workflow.WithMaxReentry(2, StepDeleteAllReadonlyGroupDeleteDomain))

	// 删除只读组域名
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupDeleteDomain,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     dns.ProcessDeleteAllRoGroupDomain,
		SuccessNextStep: StepDeleteAllReadonlyGroupRemoveFromBcmGroup,
		ErrorNextStep:   StepDeleteAllReadonlyGroupDeleteDomain},

		workflow.WithMaxReentry(2, StepDeleteAllReadonlyGroupRemoveFromBcmGroup))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupRemoveFromBcmGroup,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     bcm.ProcessRoRemoveFromGroup,
		SuccessNextStep: StepDeleteAllReadonlyGroupDeleteResource,
		ErrorNextStep:   StepDeleteAllReadonlyGroupRemoveFromBcmGroup})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupDeleteResource,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     delresource.ProcessDelBccResourcesForRo,
		SuccessNextStep: StepDeleteAllReadonlyGroupDeleteAllRoNodes,
		ErrorNextStep:   StepDeleteAllReadonlyGroupDeleteResource},

		workflow.WithMaxReentry(2, StepDeleteAllReadonlyGroupDeleteAllRoNodes))

	// 清理只读实例在数据库中的数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupDeleteAllRoNodes,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     delresource.ProcessDeleteAllRoNodes,
		SuccessNextStep: StepDeleteAllReadonlyGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepDeleteAllReadonlyGroupDeleteAllRoNodes},

		workflow.WithMaxReentry(2, StepDeleteAllReadonlyGroupUpdateAppTopologyInXmaster))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepDeleteAllReadonlyGroupDeleteCallback,
		ErrorNextStep:   StepDeleteAllReadonlyGroupUpdateAppTopologyInXmaster},

		workflow.WithMaxReentry(5, StepDeleteAllReadonlyGroupDeleteCallback))

	// 调用Csmaster接口，完成Csmaster相关元数据处理
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDeleteAllReadonlyGroupDeleteCallback,
		Workflow:        WorkflowDeleteAllReadonlyGroup,
		StepProcess:     callback.ProcessDeleteAllRoGroupCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteAllReadonlyGroupDeleteCallback,
	})
}
