package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/migratetox1"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
)

const (
	WorkflowMigrateToX1           = "scs-workflow-migratetox1"
	StepMigrateToX1BeforeCheck    = "scs-step-migratetox1-beforecheck"
	StepMigrateToX1Execute        = "scs-workflow-migratetox1-step-execute"
	StepMigrateToX1FillSpec       = "scs-workflow-migratetox1-step-fillspec"
	StepMigrateToX1FillSpecCommit = "scs-workflow-migratetox1-step-fillspec-commit"
	StepMigrateToX1Callback       = "scs-workflow-migratetox1-step-callback"
	StepMigrateToX1ErrorCb        = "scs-workflow-migratetox1-step-error-cb"
)

const (
	WorkflowMigrateToX1UpdateSg = "scs-workflow-migratetox1-update-sg"
	StepMigrateToX1UpdateSg     = "scs-step-migratetox1-update-sg"
)

func init() {
	// Step-1 执行csmaster到x1框架的迁移前置检查
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1BeforeCheck,
		Workflow:        WorkflowMigrateToX1,
		StepProcess:     migratetox1.ProcessMigrateToX1BeforeCheck,
		SuccessNextStep: StepMigrateToX1Execute,
		ErrorNextStep:   StepMigrateToX1BeforeCheck,
	})

	// Step-1 执行csmaster到x1框架的迁移
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1Execute,
		Workflow:        WorkflowMigrateToX1,
		StepProcess:     migratetox1.ProcessMigrateToX1,
		SuccessNextStep: StepMigrateToX1FillSpec,
		ErrorNextStep:   StepMigrateToX1Execute,
	})

	// Step-2 填充规格信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1FillSpec,
		Workflow:        WorkflowMigrateToX1,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepMigrateToX1FillSpecCommit,
		ErrorNextStep:   StepMigrateToX1FillSpec,
	})

	// Step-3 填充规格信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1FillSpecCommit,
		Workflow:        WorkflowMigrateToX1,
		StepProcess:     specification.ProcessCommitSpec,
		SuccessNextStep: StepMigrateToX1Callback,
		ErrorNextStep:   StepMigrateToX1FillSpecCommit,
	})

	// Step-4 回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1Callback,
		Workflow:        WorkflowMigrateToX1,
		StepProcess:     callback.ProcessMigrateToX1Cb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateToX1Callback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1ErrorCb,
		Workflow:        WorkflowMigrateToX1,
		StepProcess:     callback.ProcessMigrateToX1ErrorCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepMigrateToX1ErrorCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToX1UpdateSg,
		Workflow:        WorkflowMigrateToX1UpdateSg,
		StepProcess:     migratetox1.ProcessAddXAgentSg,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateToX1UpdateSg,
	})
}
