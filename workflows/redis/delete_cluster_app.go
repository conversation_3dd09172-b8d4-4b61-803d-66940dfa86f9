/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/15 11:36
**/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deleteall"
)

// imports

// const
const (
	WorkflowDeleteCluster          = "scs-delete-cluster-app"
	StepDeleteClusterDeleteExecute = "scs-delete-cluster-delete-execute"
)

// typedefs

// vars

// functions

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepDeleteClusterDeleteExecute,
		Workflow: WorkflowDeleteCluster,

		StepProcess: deleteall.ProcessDeleteAll,

		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDeleteClusterDeleteExecute,
	})
}
