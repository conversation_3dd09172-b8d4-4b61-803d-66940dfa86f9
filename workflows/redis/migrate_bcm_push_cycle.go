package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/migratebcmcycle"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
)

const (
	WorkflowMigrateBcmPushCycle              = "scs-workflow-migrate-bcm-push-cycle"
	StepMigrateBcmPushCyclePrecheck          = "step-migrate-bcm-push-cycle-precheck"
	StepMigrateBcmPushCycleUpgradeAgent      = "step-migrate-bcm-push-cycle-upgrade-agent"
	StepMigrateBcmPushCycleCheckMonitorError = "step-migrate-bcm-push-cycle-check-monitor-error"
	StepMigrateBcmPushCycleChangeConf        = "step-migrate-bcm-push-cycle-change-conf"
	StepMigrateBcmPushCycleFastCheckResult   = "step-migrate-bcm-push-cycle-fast-check-result"
	StepMigrateBcmPushCycleSlowCheckResult   = "step-migrate-bcm-push-cycle-slow-check-result"
	StepMigrateBcmPushCycleUpdateApp         = "step-migrate-bcm-push-cycle-update-app"

	WorkflowRollbackMigrateBcmPushCycle            = "scs-workflow-rollback-migrate-bcm-push-cycle"
	StepRollbackMigrateBcmPushCyclePrecheck        = "step-rollback-migrate-bcm-push-cycle-precheck"
	StepRollbackMigrateBcmPushCycleChangeConf      = "step-rollback-migrate-bcm-push-cycle-change-conf"
	StepRollbackMigrateBcmPushCycleFastCheckResult = "step-rollback-migrate-bcm-push-cycle-fast-check-result"
	StepRollbackMigrateBcmPushCycleSlowCheckResult = "step-rollback-migrate-bcm-push-cycle-slow-check-result"
	StepRollbackMigrateBcmPushCycleUpdateApp       = "step-rollback-migrate-bcm-push-cycle-update-app"
)

func init() {

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCyclePrecheck,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessMigrateBcmCyclePreCheck,
		SuccessNextStep: StepMigrateBcmPushCycleUpgradeAgent,
		ErrorNextStep:   StepMigrateBcmPushCyclePrecheck,
	},
		workflow.WithMaxReentry(3, workflow.FinalStepError),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCycleUpgradeAgent,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     upgrade.ProcessUpgradePackages,
		SuccessNextStep: StepMigrateBcmPushCycleCheckMonitorError,
		ErrorNextStep:   workflow.FinalStepError,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCycleCheckMonitorError,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     opmonitor.ProcessCheckMonitorError,
		SuccessNextStep: StepMigrateBcmPushCycleChangeConf,
		ErrorNextStep:   StepMigrateBcmPushCycleCheckMonitorError,
	},
		workflow.WithMaxReentry(3, workflow.FinalStepError),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCycleChangeConf,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessMigrateBcmCycleChangeConf,
		SuccessNextStep: StepMigrateBcmPushCycleFastCheckResult,
		ErrorNextStep:   StepMigrateBcmPushCycleChangeConf,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCycleFastCheckResult,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessMigrateBcmCycleFastCheck,
		SuccessNextStep: StepMigrateBcmPushCycleSlowCheckResult,
		ErrorNextStep:   StepMigrateBcmPushCycleFastCheckResult,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCycleSlowCheckResult,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessMigrateBcmCycleSlowCheck,
		SuccessNextStep: StepMigrateBcmPushCycleUpdateApp,
		ErrorNextStep:   StepMigrateBcmPushCycleSlowCheckResult,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateBcmPushCycleUpdateApp,
		Workflow:        WorkflowMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessMigrateBcmCycleUpdateApp,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateBcmPushCycleUpdateApp,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRollbackMigrateBcmPushCyclePrecheck,
		Workflow:        WorkflowRollbackMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessRollbackMigrateBcmCyclePreCheck,
		SuccessNextStep: StepRollbackMigrateBcmPushCycleChangeConf,
		ErrorNextStep:   StepRollbackMigrateBcmPushCyclePrecheck,
	},
		workflow.WithMaxReentry(3, workflow.FinalStepError),
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRollbackMigrateBcmPushCycleChangeConf,
		Workflow:        WorkflowRollbackMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessRollbackMigrateBcmCycleChangeConf,
		SuccessNextStep: StepRollbackMigrateBcmPushCycleFastCheckResult,
		ErrorNextStep:   StepRollbackMigrateBcmPushCycleChangeConf,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRollbackMigrateBcmPushCycleFastCheckResult,
		Workflow:        WorkflowRollbackMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessRollbackMigrateBcmCycleFastCheck,
		SuccessNextStep: StepRollbackMigrateBcmPushCycleSlowCheckResult,
		ErrorNextStep:   StepRollbackMigrateBcmPushCycleFastCheckResult,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRollbackMigrateBcmPushCycleSlowCheckResult,
		Workflow:        WorkflowRollbackMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessRollbackMigrateBcmCycleSlowCheck,
		SuccessNextStep: StepRollbackMigrateBcmPushCycleUpdateApp,
		ErrorNextStep:   StepRollbackMigrateBcmPushCycleSlowCheckResult,
	},
	)

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRollbackMigrateBcmPushCycleUpdateApp,
		Workflow:        WorkflowRollbackMigrateBcmPushCycle,
		StepProcess:     migratebcmcycle.ProcessRollbackMigrateBcmCycleUpdateApp,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRollbackMigrateBcmPushCycleUpdateApp,
	},
	)
}
