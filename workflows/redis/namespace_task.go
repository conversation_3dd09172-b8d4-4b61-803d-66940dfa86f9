package workflows

/*
DESCRIPTION
注入任务 删除命名空间操作

Parameters

	{
	   "task_id" : "abe8203b-765c-46d5-b4a3-64e3f2ae7c8a",
		"target_namespace" : "ns1",
		"origin_namespace" : "ns2",
	}
*/

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/namespace_task"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowIngestNamespaceTask                      = "scs-namespace-task-ingest-namespace"
	StepIngestNamespaceTaskPrepare                   = "scs-ingest-namespace-task-prepare"
	StepIngestNamespaceTaskDownloadPrecheckMeta      = "scs-ingest-namespace-task-download-precheck-meta"
	StepIngestNamespaceTaskDownloadPrecheckFileNum   = "scs-ingest-namespace-task-download-precheck-filenum"
	StepIngestNamespaceTaskGetDownloadNodeMeta       = "scs-ingest-namespace-task-get-download_meta"
	StepIngestNamespaceTaskDownloadPrecheckDisk      = "scs-ingest-namespace-task-download-precheck-disk"
	StepIngestNamespaceTaskCreateEmptyDir            = "scs-ingest-namespace-task-create-empty-dir"
	StepIngestNamespaceTaskDownloadOnAllNode         = "scs-ingest-namespace-task-download-on-all-node"
	StepIngestNamespaceTaskCheckDownloadedSstFileNum = "scs-ingest-namespace-task-check-downloaded-sst-filenum"
	StepIngestNamespaceTaskIngestPrecheck            = "scs-ingest-namespace-task-ingest-precheck"
	StepIngestNamespaceTaskIngestOnAllShard          = "scs-ingest-namespace-task-ingest-on-all-shard"
	StepIngestNamespaceTaskSumKeysNum                = "scs-ingest-namespace-task-sum-keys-num"
	StepIngestNamespaceTaskRemoveEmptyDir            = "scs-ingest-namespace-task-remove-empty-dir"
	StepIngestNamespaceTaskSuccessCb                 = "scs-ingest-namespace-task-success-cb"
	StepIngestNamespaceTaskRollbackIngestOnAllShard  = "scs-ingest-namespace-task-rollback-ingest-on-all-shard"
	StepIngestNamespaceTaskRollbackDownloadOnSlave   = "scs-ingest-namespace-task-rollback-download-on-slave"
	StepIngestNamespaceTaskRollbackDownloadCheckSync = "scs-ingest-namespace-task-rollback-download-check-sync"
	StepIngestNamespaceTaskRollbackDownloadOnMaster  = "scs-ingest-namespace-task-rollback-download-on-master"
	StepIngestNamespaceTaskFailCb                    = "scs-ingest-namespace-task-fail-cb"
)

func init() {

	// Step-1 修改任务状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskPrepare,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.IngestNamespacePrepare,
		SuccessNextStep: StepIngestNamespaceTaskDownloadPrecheckMeta,
		ErrorNextStep:   StepIngestNamespaceTaskPrepare,
	})

	// 预检查元信息
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskDownloadPrecheckMeta,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.DownloadPrecheckMeta,
		SuccessNextStep: StepIngestNamespaceTaskDownloadPrecheckFileNum,
		ErrorNextStep:   StepIngestNamespaceTaskDownloadPrecheckMeta,
	})

	//  预检查文件数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskDownloadPrecheckFileNum,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.DownloadPrecheckFileNum,
		SuccessNextStep: StepIngestNamespaceTaskGetDownloadNodeMeta,
		ErrorNextStep:   StepIngestNamespaceTaskDownloadPrecheckFileNum,
	},
		workflow.WithMaxReentry(5, StepIngestNamespaceTaskFailCb))

	//  获取下载元数据
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskGetDownloadNodeMeta,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.GetDownloadNodeMeta,
		SuccessNextStep: StepIngestNamespaceTaskDownloadPrecheckDisk,
		ErrorNextStep:   StepIngestNamespaceTaskGetDownloadNodeMeta,
	})

	//  检查硬盘空间
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskDownloadPrecheckDisk,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.DownloadPrecheckDisk,
		SuccessNextStep: StepIngestNamespaceTaskCreateEmptyDir,
		ErrorNextStep:   StepIngestNamespaceTaskDownloadPrecheckDisk,
	},
		workflow.WithMaxReentry(10, StepIngestNamespaceTaskFailCb))

	//  创建空闲目录
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskCreateEmptyDir,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.CreateEmptyDirOnNode,
		SuccessNextStep: StepIngestNamespaceTaskDownloadOnAllNode,
		ErrorNextStep:   StepIngestNamespaceTaskCreateEmptyDir,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	//  下载
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskDownloadOnAllNode,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.DownloadOnAllNode,
		SuccessNextStep: StepIngestNamespaceTaskCheckDownloadedSstFileNum,
		ErrorNextStep:   StepIngestNamespaceTaskDownloadOnAllNode,
	},
		workflow.WithStepTimeout(180*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskCheckDownloadedSstFileNum,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.CheckDownloadedSstFileNum,
		SuccessNextStep: StepIngestNamespaceTaskIngestPrecheck,
		ErrorNextStep:   StepIngestNamespaceTaskCheckDownloadedSstFileNum,
	})

	// 注入预检测
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskIngestPrecheck,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.IngestNamespacePrecheckNode,
		SuccessNextStep: StepIngestNamespaceTaskIngestOnAllShard,
		ErrorNextStep:   StepIngestNamespaceTaskIngestPrecheck,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	//  注入命名空间
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskIngestOnAllShard,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.IngestNamespaceOnAllShard,
		SuccessNextStep: StepIngestNamespaceTaskSumKeysNum,
		ErrorNextStep:   StepIngestNamespaceTaskIngestOnAllShard,
	},
		workflow.WithStepSplitHandler(util.GetAllClusterIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskSumKeysNum,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.SumKeysNum,
		SuccessNextStep: StepIngestNamespaceTaskRemoveEmptyDir,
		ErrorNextStep:   StepIngestNamespaceTaskSumKeysNum,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskRemoveEmptyDir,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.RemoveEmptyDirOnNode,
		SuccessNextStep: StepIngestNamespaceTaskSuccessCb,
		ErrorNextStep:   StepIngestNamespaceTaskRemoveEmptyDir,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskSuccessCb,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.IngestNamespaceSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepIngestNamespaceTaskSuccessCb,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskRollbackIngestOnAllShard,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.RollbackIngestNamespaceOnAllShard,
		SuccessNextStep: StepIngestNamespaceTaskRollbackDownloadOnSlave,
		ErrorNextStep:   StepIngestNamespaceTaskRollbackIngestOnAllShard,
	},
		workflow.WithStepSplitHandler(util.GetAllClusterIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskRollbackDownloadOnSlave,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.RollbackDownloadOnSlave,
		SuccessNextStep: StepIngestNamespaceTaskRollbackDownloadCheckSync,
		ErrorNextStep:   StepIngestNamespaceTaskRollbackDownloadOnSlave,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts),
		workflow.WithStepTimeout(60*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskRollbackDownloadCheckSync,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.RollbackDownloadCheckSync,
		SuccessNextStep: StepIngestNamespaceTaskRollbackDownloadOnMaster,
		ErrorNextStep:   StepIngestNamespaceTaskRollbackDownloadCheckSync,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskRollbackDownloadOnMaster,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.RollbackDownloadOnMaster,
		SuccessNextStep: StepIngestNamespaceTaskFailCb,
		ErrorNextStep:   StepIngestNamespaceTaskRollbackDownloadOnMaster,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts),
		workflow.WithStepTimeout(60*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepIngestNamespaceTaskFailCb,
		Workflow:        WorkflowIngestNamespaceTask,
		StepProcess:     namespace_task.IngestNamespaceFailCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepIngestNamespaceTaskFailCb,
	})

}

const (
	WorkflowSwitchNamespaceTask                                = "scs-namespace-task-switch-namespace"
	StepSwitchNamespaceTaskPrepare                             = "scs-switch-namespace-task-prepare"
	StepSwitchNamespaceTaskPrecheckMeta                        = "scs-switch-namespace-task-precheck-meta"
	StepSwitchNamespaceTaskPrecheckNode                        = "scs-switch-namespace-task-precheck-node"
	StepSwitchNamespaceTaskSwitchNamespaceOnAllCluster         = "scs-switch-namespace-task-switch-namespace-on-all-cluster"
	StepSwitchNamespaceTaskRollbackSwitchNamespaceOnAllCluster = "scs-switch-namespace-task-rollback-switch-namespace-on-all-cluster"
	StepSwitchNamespaceTaskSuccessCb                           = "scs-switch-namespace-task-success-cb"
	StepSwitchNamespaceTaskFailCb                              = "scs-switch-namespace-task-fail-cb"
)

func init() {

	// Step-1 修改任务状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskPrepare,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.SwitchNamespacePrepare,
		SuccessNextStep: StepSwitchNamespaceTaskPrecheckMeta,
		ErrorNextStep:   StepSwitchNamespaceTaskPrepare,
	})

	// Step-2 修改命名空间状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskPrecheckMeta,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.SwitchNamespacePrecheckMeta,
		SuccessNextStep: StepSwitchNamespaceTaskPrecheckNode,
		ErrorNextStep:   StepSwitchNamespaceTaskPrecheckMeta,
	})

	// Step-3 检查同步状态、命名空间状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskPrecheckNode,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.SwitchNamespacePrecheckNode,
		SuccessNextStep: StepSwitchNamespaceTaskSwitchNamespaceOnAllCluster,
		ErrorNextStep:   StepSwitchNamespaceTaskPrecheckNode,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	// Step-4 切换命名空间
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskSwitchNamespaceOnAllCluster,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.SwitchNamespaceOnAllCluster,
		SuccessNextStep: StepSwitchNamespaceTaskSuccessCb,
		ErrorNextStep:   StepSwitchNamespaceTaskSwitchNamespaceOnAllCluster,
	},
		workflow.WithStepSplitHandler(util.GetAllClusterIds),
		workflow.WithMaxReentry(10, StepSwitchNamespaceTaskRollbackSwitchNamespaceOnAllCluster))

	// Step-5 切换命名空间成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskSuccessCb,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.SwitchNamespaceSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSwitchNamespaceTaskSuccessCb,
	})

	// 回滚命名空间
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskRollbackSwitchNamespaceOnAllCluster,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.RollbackSwitchNamespaceOnAllCluster,
		SuccessNextStep: StepSwitchNamespaceTaskFailCb,
		ErrorNextStep:   StepSwitchNamespaceTaskRollbackSwitchNamespaceOnAllCluster,
	},
		workflow.WithStepSplitHandler(util.GetAllClusterIds))

	// 回滚命名空间失败回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSwitchNamespaceTaskFailCb,
		Workflow:        WorkflowSwitchNamespaceTask,
		StepProcess:     namespace_task.SwitchNamespaceFailCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepSwitchNamespaceTaskFailCb,
	})

}

const (
	WorkflowDeleteNamespaceTask                            = "scs-namespace-task-delete-namespace"
	WorkflowDeleteNamespaceTaskPrePare                     = "scs-delete-namespace-task-prepare"
	WorkflowDeleteNamespaceTaskPrecheckMeta                = "scs-delete-namespace-task-precheck-meta"
	WorkflowDeleteNamespaceTaskPrecheckNode                = "scs-delete-namespace-task-precheck-node"
	WorkflowDeleteNamespaceTaskDeleteNamespaceOnAllCluster = "scs-delete-namespace-task-delete-namespace-on-all-cluster"
	WorkflowDeleteNamespaceTaskSuccessCb                   = "scs-delete-namespace-task-success-cb"
	WorkflowDeleteNamespaceTaskFailCb                      = "scs-delete-namespace-task-fail-cb"
)

func init() {

	// Step-1 修改任务状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkflowDeleteNamespaceTaskPrePare,
		Workflow:        WorkflowDeleteNamespaceTask,
		StepProcess:     namespace_task.DeleteNamespacePrepare,
		SuccessNextStep: WorkflowDeleteNamespaceTaskPrecheckMeta,
		ErrorNextStep:   WorkflowDeleteNamespaceTaskPrePare,
	})

	//Step-2 检测元数据是否准确
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkflowDeleteNamespaceTaskPrecheckMeta,
		Workflow:        WorkflowDeleteNamespaceTask,
		StepProcess:     namespace_task.DeleteNamespacePrecheckMeta,
		SuccessNextStep: WorkflowDeleteNamespaceTaskPrecheckNode,
		ErrorNextStep:   WorkflowDeleteNamespaceTaskPrecheckMeta,
	})

	// Step-3 检测节点复制状态以及命名空间是否存在
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkflowDeleteNamespaceTaskPrecheckNode,
		Workflow:        WorkflowDeleteNamespaceTask,
		StepProcess:     namespace_task.DeleteNamespacePrecheckNode,
		SuccessNextStep: WorkflowDeleteNamespaceTaskDeleteNamespaceOnAllCluster,
		ErrorNextStep:   WorkflowDeleteNamespaceTaskPrecheckNode,
	},
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	// Step-4 删除命名空间
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkflowDeleteNamespaceTaskDeleteNamespaceOnAllCluster,
		Workflow:        WorkflowDeleteNamespaceTask,
		StepProcess:     namespace_task.DeleteNamespaceOnAllCluster,
		SuccessNextStep: WorkflowDeleteNamespaceTaskSuccessCb,
		ErrorNextStep:   WorkflowDeleteNamespaceTaskDeleteNamespaceOnAllCluster,
	},
		workflow.WithStepTimeout(5*time.Minute),
		workflow.WithStepSplitHandler(util.GetAllClusterIds))

	// Step-5 修改任务状态、命名空间、集群状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkflowDeleteNamespaceTaskSuccessCb,
		Workflow:        WorkflowDeleteNamespaceTask,
		StepProcess:     namespace_task.DeleteNamespaceSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkflowDeleteNamespaceTaskSuccessCb,
	})

	// Step-6 修改任务状态、集群状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkflowDeleteNamespaceTaskFailCb,
		Workflow:        WorkflowDeleteNamespaceTask,
		StepProcess:     namespace_task.DeleteNamespaceFailCb,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   WorkflowDeleteNamespaceTaskFailCb,
	})

}
