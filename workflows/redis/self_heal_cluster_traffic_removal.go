/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), self-healing
*/

/*
DESCRIPTION
集群版实例，自愈子任务，用户提前摘除 proxy、node节点流量

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"SelfHealingParams": [
		{
			"ShardID": 133,
			"NodeShortIDList": [12345, 12346]
		}
	]
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
)

const (
	WorkflowSelfHealClusterTrafficRemoval = "scs-self-heal-cluster-traffic-removal"

	StepSelfHealClusterTrafficRemovalDelRs       = "scs-self-heal-cluster-traffic-removal-del-rs"
	StepSelfHealClusterTrafficRemovalDelMcpackRs = "scs-self-heal-cluster-traffic-removal-del-mcpack-rs"
	// StepSelfHealClusterTrafficRemovalDelBnsInstance = "scs-self-heal-cluster-traffic-removal-del-bns-instance"
	StepSelfHealClusterTrafficRemovalDelMetaNodes   = "scs-self-heal-cluster-traffic-removal-del-meta-nodes"
	StepSelfHealClusterTrafficRemovalSendClientKill = "scs-self-heal-cluster-traffic-removal-send-client-kill"
)

func init() {

	// Step-1 从blb从删除rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterTrafficRemovalDelRs,
		Workflow:        WorkflowSelfHealClusterTrafficRemoval,
		StepProcess:     blb.ProcessDeleteProxyRsForTrafficRemoval,
		SuccessNextStep: StepSelfHealClusterTrafficRemovalDelMcpackRs,
		ErrorNextStep:   StepSelfHealClusterTrafficRemovalDelRs,
	})

	// Step-2 从blb从删除mcpack rs
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterTrafficRemovalDelMcpackRs,
		Workflow:        WorkflowSelfHealClusterTrafficRemoval,
		StepProcess:     blb.ProcessDeleteProxyRsMcpackForTrafficRemoval,
		SuccessNextStep: StepSelfHealClusterTrafficRemovalDelMetaNodes,
		ErrorNextStep:   StepSelfHealClusterTrafficRemovalDelMcpackRs,
	})

	// // Step-3 从bns中删除instance; 经沟通，bns暂不摘流;
	// _ = workflow.AddStep(&workflow.AddStepParam{
	// 	Name:            StepSelfHealClusterTrafficRemovalDelBnsInstance,
	// 	Workflow:        WorkflowSelfHealClusterTrafficRemoval,
	// 	StepProcess:     bns.ProcessDeleteBnsInstancesForTrafficRemoval,
	// 	SuccessNextStep: StepSelfHealClusterTrafficRemovalDelMetaNodes,
	// 	ErrorNextStep:   StepSelfHealClusterTrafficRemovalDelBnsInstance,
	// })

	// Step-4 从metaserver删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterTrafficRemovalDelMetaNodes,
		Workflow:        WorkflowSelfHealClusterTrafficRemoval,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepSelfHealClusterTrafficRemovalSendClientKill,
		ErrorNextStep:   StepSelfHealClusterTrafficRemovalDelMetaNodes,
	})

	// Step-5 发送client kill
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterTrafficRemovalSendClientKill,
		Workflow:        WorkflowSelfHealClusterTrafficRemoval,
		StepProcess:     handover.ProcessProxyClientKillForTrafficRemoval,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSelfHealClusterTrafficRemovalSendClientKill,
	})
}
