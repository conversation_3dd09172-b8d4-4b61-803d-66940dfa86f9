/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), self-healing
*/

/*
DESCRIPTION
进行标准版集群故障节点自愈

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"SelfHealingParams": [
		{
			"ShardID": 133,
			"NodeShortIDList": [12345, 12346]
		}
	]
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/appStateManage"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/applyresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/availability"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/bns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	checksubnets "icode.baidu.com/baidu/scs/x1-task/processors/redis/check_subnets"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/delresource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/initmachineenv"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/pushflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
	securitygroup "icode.baidu.com/baidu/scs/x1-task/processors/redis/security_group"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/specification"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowSelfHealCluster                     = "scs-failover-cluster-app"
	StepSelfHealClusterUpdateAppStatus          = "scs-self-heal-cluster-update-app-status"
	StepSelfHealClusterLockGroup                = "scs-self-heal-cluster-lock-group"
	StepSelfHealClusterAddStartEvent            = "scs-self-heal-cluster-add-start-event"
	StepSelfHealClusterBuildMeta                = "scs-self-heal-cluster-build-meta"
	StepSelfHealClusterFillSpec                 = "scs-self-heal-cluster-fill-spec"
	StepSelfHealClusterTryTrafficRemoval        = "scs-self-heal-cluster-try-traffic-removal"
	StepSelfHealClusterTrafficRemovalCallback   = "scs-self-heal-cluster-traffic-removal-cb"
	StepSelfHealClusterCheckSubnetsEnoughIps    = "scs-self-heal-cluster-check-subnets-enough-ips"
	StepSelfHealClusterApplyResources           = "scs-self-heal-cluster-apply-resources"
	StepSelfHealClusterApplyResourcesCallback   = "scs-self-heal-cluster-apply-resources-cb"
	StepSelfHealClusterInitMachineEnv           = "scs-self-heal-cluster-init-machine-env"
	StepSelfHealClusterUpdateSecurityGroups     = "scs-self-heal-cluster-update-security-groups"
	StepSelfHealClusterUpdateGlobalTopoForProxy = "scs-self-heal-cluster-update-global-topo-for-proxy"
	StepSelfHealClusterMetaAddProxys            = "scs-self-heal-cluster-meta-add-proxys"

	StepSelfHealClusterDeployRedis           = "scs-self-heal-cluster-apply-deploy-redis"
	StepSelfHealClusterSlaveOfGlobal         = "scs-self-heal-cluster-slaveof-global"
	StepSelfHealClusterSlaveOfLocal          = "scs-self-heal-cluster-slaveof-local"
	StepSelfHealClusterUpdateTlsConfIfNeeded = "scs-self-heal-cluster-update-tls-conf-if-needed"
	StepSelfHealClusterDeployFilebeat        = "scs-self-heal-cluster-apply-deploy-filebeat"

	StepSelfHealClusterCheckShardSync   = "scs-self-heal-cluster-check-shard-sync"
	StepSelfHealClusterUpdateGlobalTopo = "scs-self-heal-cluster-update-global-topo"
	StepSelfHealClusterMetaAddNodes     = "scs-self-heal-cluster-meta-add-nodes"

	StepSelfHealClusterSetRs                              = "scs-self-heal-cluster-set-rs"
	StepSelfHealClusterSetMcpackRs                        = "scs-self-heal-cluster-set-mcpack-rs"
	StepSelfHealClusterSetBnsInstance                     = "scs-self-heal-cluster-set-bns-instance"
	StepSelfHealClusterMetaMasterChange                   = "scs-self-heal-cluster-meta-master-change"
	StepSelfHealClusterMetaDelNodes                       = "scs-self-heal-cluster-meta-del-nodes"
	StepSelfHealClusterPushMonitorHTGRPSlaveFlag          = "scs-self-heal-cluster-push-htgrp-slave-flag"
	StepSelfHealClusterPushFlag                           = "scs-self-heal-cluster-push-flag"
	StepSelfHealClusterDeleteOldNodes                     = "scs-self-heal-cluster-del-old-nodes"
	StepSelfHealClusterResetFailoverFlag                  = "scs-self-heal-cluster-reset-failover-flag"
	StepSelfHealClusterUpdateAppTopologyInXmaster         = "scs-self-heal-cluster-update-app-topo-in-xmaster"
	StepSelfHealClusterInitOpMonitor                      = "scs-self-heal-cluster-init-op-monitor"
	StepSelfHealClusterReleaseGroup                       = "scs-self-heal-cluster-release-group"
	StepSelfHealClusterAddSuccessEndEvent                 = "scs-self-heal-cluster-add-success-end-event"
	StepSelfHealClusterSuccessCallback                    = "scs-self-heal-cluster-succ-cb"
	StepSelfHealClusterRollbackReleaseResources           = "scs-self-heal-cluster-rollback-release-resource"
	StepSelfHealClusterRollbackPushFlag                   = "scs-self-heal-cluster-rollback-push-flag"
	StepSelfHealClusterRollbackBuildMeta                  = "scs-self-heal-cluster-rollback-build-meta"
	StepSelfHealClusterRollbackCallback                   = "scs-self-heal-cluster-rollback-cb"
	StepSelfHealClusterRollbackUpdateAppTopologyInXmaster = "scs-self-heal-cluster-rollback-update-app-topo-in-xmaster"
	StepSelfHealClusterAddFailEndEvent                    = "scs-self-heal-cluster-add-fail-end-event"
)

func init() {
	// Step-1 检查任务来源，若任务来自xmaster，则更新cache_cluster.status为17
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateAppStatus,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     appStateManage.UpdateAppStatusForSelfHealing,
		SuccessNextStep: StepSelfHealClusterLockGroup,
		ErrorNextStep:   StepSelfHealClusterUpdateAppStatus,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterLockGroup,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     gmaster.TryToGetGlobalSelfHealLock,
		SuccessNextStep: StepSelfHealClusterAddStartEvent,
		ErrorNextStep:   StepSelfHealClusterLockGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterAddStartEvent,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     event.ProcessSelfHealingStartEvent,
		SuccessNextStep: StepSelfHealClusterBuildMeta,
		ErrorNextStep:   StepSelfHealClusterAddStartEvent,
	})

	// Step-1 构建元数据，将需要删除的节点改为ToDelete、新加节点ToCreate
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterBuildMeta,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForClusterSelfHeal,
		SuccessNextStep: StepSelfHealClusterFillSpec,
		ErrorNextStep:   StepSelfHealClusterBuildMeta,
	})

	// Step-2 将规格信息填入Cluster表或Interface表；
	// 同分片的node应当规格相同，Port相同，使用同样的部署集
	// 一个Interface代表一个Proxy的部署组，规格相同，Port相同，使用同样的部署集
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterFillSpec,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     specification.ProcessFillSpec,
		SuccessNextStep: StepSelfHealClusterTryTrafficRemoval,
		ErrorNextStep:   StepSelfHealClusterFillSpec,
	})

	// 尝试创建提前摘流子任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterTryTrafficRemoval,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     availability.ProcessClusterCreateSubTaskForTrafficRemoval,
		SuccessNextStep: StepSelfHealClusterTrafficRemovalCallback,
		ErrorNextStep:   StepSelfHealClusterTryTrafficRemoval},
		workflow.WithMaxReentry(1, StepSelfHealClusterTrafficRemovalCallback),
	)

	// 检查提前摘流子任务结果
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterTrafficRemovalCallback,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     availability.ProcessTrafficRemovalCallback,
		SuccessNextStep: StepSelfHealClusterCheckSubnetsEnoughIps,
		ErrorNextStep:   StepSelfHealClusterTrafficRemovalCallback,
	})

	// Step-4 检查子网ip是否充足
	// caoyuning
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterCheckSubnetsEnoughIps,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     checksubnets.CheckEnoughIPs,
		SuccessNextStep: StepSelfHealClusterApplyResources,
		ErrorNextStep:   StepSelfHealClusterCheckSubnetsEnoughIps,
	})

	// Step-6 创建资源，并将资源的信息存入Node或Proxy表中
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterApplyResources,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     applyresource.ProcessApplyBccResources,
		SuccessNextStep: StepSelfHealClusterApplyResourcesCallback,
		ErrorNextStep:   StepSelfHealClusterApplyResources},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-6 将创建好的资源同步至Csmaster
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterApplyResourcesCallback,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     callback.ProcessApplyResourceCallback,
		SuccessNextStep: StepSelfHealClusterInitMachineEnv,
		ErrorNextStep:   StepSelfHealClusterApplyResourcesCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterInitMachineEnv,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     initmachineenv.ProcessInitMachineEnv,
		SuccessNextStep: StepSelfHealClusterUpdateSecurityGroups,
		ErrorNextStep:   StepSelfHealClusterInitMachineEnv,
	})

	// Step-6 更新安全组规则
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateSecurityGroups,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     securitygroup.ProcessRebuildSecurityGroupCluster,
		SuccessNextStep: StepSelfHealClusterUpdateGlobalTopoForProxy,
		ErrorNextStep:   StepSelfHealClusterUpdateSecurityGroups,
	})

	// 注册 proxy
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateGlobalTopoForProxy,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     metaserver.ProcessAddGlobalProxys,
		SuccessNextStep: StepSelfHealClusterMetaAddProxys,
		ErrorNextStep:   StepSelfHealClusterUpdateGlobalTopoForProxy,
	})

	// 注册 proxy(local meta)
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMetaAddProxys,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     metaserver.ProcessAddProxys,
		SuccessNextStep: StepSelfHealClusterDeployRedis,
		ErrorNextStep:   StepSelfHealClusterMetaAddProxys,
	})

	// Step-6 部署相关包
	// 部署Redis
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDeployRedis,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     deploy.ProcessDeployAll,
		SuccessNextStep: StepSelfHealClusterSlaveOfGlobal,
		ErrorNextStep:   StepSelfHealClusterDeployRedis},

		workflow.WithStepTimeout(15*time.Minute))

	workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSlaveOfGlobal,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: StepSelfHealClusterSlaveOfLocal,
		ErrorNextStep:   StepSelfHealClusterSlaveOfGlobal,
	})

	workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSlaveOfLocal,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     handover.ProcessLocalSlaveOf,
		SuccessNextStep: StepSelfHealClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepSelfHealClusterSlaveOfLocal,
	})

	// 包管理todo 检查能否加在这里
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateTlsConfIfNeeded,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     util.UpdateTLSConfIfNeededForNew,
		SuccessNextStep: StepSelfHealClusterDeployFilebeat,
		ErrorNextStep:   StepSelfHealClusterUpdateTlsConfIfNeeded,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDeployFilebeat,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepSelfHealClusterCheckShardSync,
		ErrorNextStep:   StepSelfHealClusterDeployFilebeat},

		workflow.WithStepTimeout(15*time.Minute),
		workflow.WithStepSplitHandler(util.GetToCreateRedisIds))

	// 检查对应分片的主从同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterCheckShardSync,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     syncredis.ProcessCheckShardHasSyncForClusterFailover,
		SuccessNextStep: StepSelfHealClusterUpdateGlobalTopo,
		ErrorNextStep:   StepSelfHealClusterCheckShardSync,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateGlobalTopo,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     metaserver.ProcessAddGlobalNodes,
		SuccessNextStep: StepSelfHealClusterMetaAddNodes,
		ErrorNextStep:   StepSelfHealClusterUpdateGlobalTopo,
	})
	//  metaserver添加节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMetaAddNodes,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     metaserver.ProcessAddNodes,
		SuccessNextStep: StepSelfHealClusterSetRs,
		ErrorNextStep:   StepSelfHealClusterMetaAddNodes,
	})
	// Step-9 绑定BLB的rs
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSetRs,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     blb.ProcessSetProxyRsForModify,
		SuccessNextStep: StepSelfHealClusterSetMcpackRs,
		ErrorNextStep:   StepSelfHealClusterSetRs,
	})

	// Step-9 绑定BLB的rs(for mcpack)
	// shangshuai
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSetMcpackRs,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepSelfHealClusterSetBnsInstance,
		ErrorNextStep:   StepSelfHealClusterSetMcpackRs,
	})

	// Step-9 绑定bns instance
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSetBnsInstance,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     bns.ProcessSetBnsInstances,
		SuccessNextStep: StepSelfHealClusterMetaMasterChange,
		ErrorNextStep:   StepSelfHealClusterSetBnsInstance,
	})

	// 单副本集群metaserver切主
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMetaMasterChange,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     metaserver.ProcessMetaMasterChange,
		SuccessNextStep: StepSelfHealClusterMetaDelNodes,
		ErrorNextStep:   StepSelfHealClusterMetaMasterChange,
	})

	// Step-10 metaserver删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterMetaDelNodes,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     metaserver.ProcessDelNodes,
		SuccessNextStep: StepSelfHealClusterPushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepSelfHealClusterMetaDelNodes,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterPushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealClusterPushFlag,
		ErrorNextStep:   StepSelfHealClusterPushMonitorHTGRPSlaveFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterPushFlag,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     pushflag.ProcessUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealClusterDeleteOldNodes,
		ErrorNextStep:   StepSelfHealClusterPushFlag,
	})

	// Step-11 删除节点
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterDeleteOldNodes,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     delresource.ProcessDeleteToDeleteNodes,
		SuccessNextStep: StepSelfHealClusterResetFailoverFlag,
		ErrorNextStep:   StepSelfHealClusterDeleteOldNodes,
	})

	// Step-12 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterResetFailoverFlag,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     handover.ProcessResetFailoveredShards,
		SuccessNextStep: StepSelfHealClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepSelfHealClusterResetFailoverFlag,
	})

	// Step-13 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepSelfHealClusterInitOpMonitor,
		ErrorNextStep:   StepSelfHealClusterUpdateAppTopologyInXmaster,
	})

	// 创建监控用bns
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterInitOpMonitor,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     opmonitor.ProcessCreateOpmonitorInstanceBns,
		SuccessNextStep: StepSelfHealClusterReleaseGroup,
		ErrorNextStep:   StepSelfHealClusterInitOpMonitor,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterReleaseGroup,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     gmaster.TryToReleaseGlobalSelfHealLock,
		SuccessNextStep: StepSelfHealClusterAddSuccessEndEvent,
		ErrorNextStep:   StepSelfHealClusterReleaseGroup,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterAddSuccessEndEvent,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     event.ProcessSelfHealingSuccessEndEvent,
		SuccessNextStep: StepSelfHealClusterSuccessCallback,
		ErrorNextStep:   StepSelfHealClusterAddSuccessEndEvent,
	})

	// Step-13 创建成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterSuccessCallback,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepSelfHealClusterSuccessCallback,
	})

	// Step-Error-01 创建失败时，调用CsMaster的API，修改cluster状态为运行中
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackReleaseResources,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     delresource.ProcessRollbackBccResources,
		SuccessNextStep: StepSelfHealClusterRollbackPushFlag,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackPushFlag,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     pushflag.ProcessRollbackUpdatePushFlagForReplaceNodes,
		SuccessNextStep: StepSelfHealClusterAddFailEndEvent,
		ErrorNextStep:   StepSelfHealClusterRollbackPushFlag,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterAddFailEndEvent,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     event.ProcessSelfHealingFailEndEvent,
		SuccessNextStep: StepSelfHealClusterRollbackBuildMeta,
		ErrorNextStep:   StepSelfHealClusterAddFailEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackBuildMeta,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     delresource.ProcessRollbackMeta,
		SuccessNextStep: StepSelfHealClusterRollbackCallback,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// 回滚节点状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackCallback,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     callback.ProcessSelfHealingCb,
		SuccessNextStep: StepSelfHealClusterRollbackUpdateAppTopologyInXmaster,
		ErrorNextStep:   workflow.FinalStepError,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepSelfHealClusterRollbackUpdateAppTopologyInXmaster,
		Workflow:        WorkflowSelfHealCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   workflow.FinalStepError,
	})
}
