/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
执行标准版配置修改
// shangshuai

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
	"ConfigureParams" : [
		{
			Key: "appendonly",
			Type: "redis_config" // redis_config/proxy_config/redis_allowed_cmds
			Value: "no"
		},
		{
			Key: "allowed-cmds",
			Type: "redis_allowed_cmds",
			Value: "keys|flushall"
		}
	]
}
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
)

const (
	WorkflowConfigStandalone      = "scs-config-standalone-app"
	StepConfigStandaloneBuildMeta = "scs-config-standalone-app-build-meta"
	StepConfigStandaloneConfigSet = "scs-config-standalone-app-configset"
)

func init() {
	// Step-1 执行配置变更
	// 1. 通过XAgent向节点发送配置变更
	// 开发量：
	// (1) XAgent相关代码
	// (2) Csmaster在收到前端配置变更指令后，创建相关任务（MasterServiceHandler::modify_config_info中添加）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepConfigStandaloneBuildMeta,
		Workflow:        WorkflowConfigStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForUpdateConfig,
		SuccessNextStep: StepConfigStandaloneConfigSet,
		ErrorNextStep:   StepConfigStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepConfigStandaloneConfigSet,
		Workflow:        WorkflowConfigStandalone,
		StepProcess:     config.ProcessUpdateConfigStandalone,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepConfigStandaloneConfigSet,
	})
}
