package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"time"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/faultInjection"
)

const (
	WorkflowFaultInjection        = "scs-fault-injection"
	StepFaultInjectionStartEvent  = "scs-fault-injection-start-event"
	StepFaultInjectionPreCheck    = "scs-fault-injection-pre-check"
	StepFaultInjectionStartExec   = "scs-fault-injection-start-exec"
	StepFaultInjectionCheckTasks  = "scs-fault-injection-check-tasks"
	StepFaultInjectionCallback    = "scs-fault-injection-callback"
	StepFaultInjectionSelfHealing = "scs-fault-injection-self-healing"

	WorkflowFaultInjectionExec      = "scs-fault-injection-exec"
	StepFaultInjectionExecute       = "scs-fault-injection-exec-execute"
	StepFaultInjectionEndEvent      = "scs-fault-injection-exec-end-event"
	StepFaultInjectionEndEventError = "scs-fault-injection-exec-end-event-error"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionStartEvent,
		Workflow:        WorkflowFaultInjection,
		StepProcess:     faultInjection.ProcessFaultInjectionStartEvent,
		SuccessNextStep: StepFaultInjectionPreCheck,
		ErrorNextStep:   StepFaultInjectionStartEvent,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionPreCheck,
		Workflow:        WorkflowFaultInjection,
		StepProcess:     faultInjection.ProcessFaultInjectionPreCheck,
		SuccessNextStep: StepFaultInjectionStartExec,
		ErrorNextStep:   StepFaultInjectionPreCheck,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionStartExec,
		Workflow:        WorkflowFaultInjection,
		StepProcess:     faultInjection.ProcessFaultInjectionStartExecution,
		SuccessNextStep: StepFaultInjectionCheckTasks,
		ErrorNextStep:   StepFaultInjectionStartExec,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionCheckTasks,
		Workflow:        WorkflowFaultInjection,
		StepProcess:     faultInjection.ProcessFaultInjectionCheckTasks,
		SuccessNextStep: StepFaultInjectionCallback,
		ErrorNextStep:   StepFaultInjectionCheckTasks,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionCallback,
		Workflow:        WorkflowFaultInjection,
		StepProcess:     faultInjection.ProcessFaultInjectionCallback,
		SuccessNextStep: StepFaultInjectionSelfHealing,
		ErrorNextStep:   StepFaultInjectionCallback,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionSelfHealing,
		Workflow:        WorkflowFaultInjection,
		StepProcess:     faultInjection.ProcessFaultInjectionWaitSelfHealing,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepFaultInjectionSelfHealing,
	}, workflow.WithStepTimeout(15*time.Minute))

	// 子流程
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionExecute,
		Workflow:        WorkflowFaultInjectionExec,
		StepProcess:     faultInjection.ProcessFaultInjectionExecute,
		SuccessNextStep: StepFaultInjectionEndEvent,
		ErrorNextStep:   StepFaultInjectionExecute,
	}, workflow.WithMaxReentry(3, StepFaultInjectionEndEventError))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionEndEvent,
		Workflow:        WorkflowFaultInjectionExec,
		StepProcess:     faultInjection.ProcessFaultInjectionEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepFaultInjectionEndEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepFaultInjectionEndEventError,
		Workflow:        WorkflowFaultInjectionExec,
		StepProcess:     faultInjection.ProcessFaultInjectionEndEventError,
		SuccessNextStep: workflow.FinalStepError,
		ErrorNextStep:   StepFaultInjectionEndEventError,
	})
}
