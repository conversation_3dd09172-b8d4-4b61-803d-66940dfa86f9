/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/23 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_delete_sync_group.go
 * <AUTHOR>
 * @date 2022/09/23 15:11:02
 * @brief delete sync group workflow
 *
 **/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

// WorkFlowLocalDeleteStandaloneGroup delete standalone group workflow
const (
	WorkFlowLocalDeleteSyncGroup                           = "scs-local-delete-sync-group"
	WorkFlowLocalDeleteSyncGroupBlockMonitorInXmaster      = "scs-local-delete-sync-group-block-monitor-in-xmaster"
	WorkFlowLocalDeleteSyncGroupCloseSyncConfig            = "scs-local-delete-sync-group-close-sync-config"
	WorkFlowLocalDeleteSyncGroupDeleteAOFBackupPolicy      = "scs-local-delete-sync-group-delete-aof-backup-policy"
	WorkFlowLocalDeleteSyncGroupCloseOpHeader              = "scs-local-delete-sync-group-close-op-header"
	WorkFlowLocalDeleteSyncGroupDeleteSyncPoint            = "scs-local-delete-sync-group-delete-sync-point"
	WorkFlowLocalDeleteSyncGroupDelSyncChannel             = "scs-local-delete-sync-group-del-sync-channel"
	WorkFlowLocalDeleteSyncGroupStepKillSyncAgent          = "scs-local-delete-sync-group-kill-sync-agent"
	WorkFlowLocalDeleteSyncGroupRebootBuildMeta            = "scs-local-delete-sync-group-reboot-build-meta"
	WorkFlowLocalDeleteSyncGroupRebootUpgradeSlaves        = "scs-local-delete-sync-group-reboot-update-slaves"
	WorkFlowLocalDeleteSyncGroupRebootCheckSync            = "scs-local-delete-sync-group-reboot-check-sync"
	WorkFlowLocalDeleteSyncGroupRebootHandOver             = "scs-local-delete-sync-group-reboot-handover"
	WorkFlowLocalDeleteSyncGroupRebootUpgradeMaster        = "scs-local-delete-sync-group-reboot-update-master"
	WorkFlowLocalDeleteSyncGroupUpdateAppTopologyInXmaster = "scs-local-delete-sync-group-update-app-topo-in-xmaster"
	WorkFlowLocalDeleteSyncGroupUnblockMonitorInXmaster    = "scs-local-delete-sync-group-unblock-monitor-in-xmaster"
	WorkFlowLocalDeleteSyncGroupStepSuccessCb              = "scs-local-delete-sync-group-success-cb"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupBlockMonitorInXmaster,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     xmaster.ProcessBlockAppMonitorInXmasterTopologySlave,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupCloseSyncConfig,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupBlockMonitorInXmaster,
	})

	// 设置support_multi_active参数为no
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupCloseSyncConfig,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     syncgroup.ProcessCloseSyncConfig,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupDeleteAOFBackupPolicy,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupCloseSyncConfig,
	})

	// check 并删除 AOF 备份策略
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupDeleteAOFBackupPolicy,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     backup.ProcessDeleteBackupPolicyForAOF,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupCloseOpHeader,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupDeleteAOFBackupPolicy,
	})

	// 设置use-op-header参数为no
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupCloseOpHeader,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     syncgroup.ProcessCloseOpHeader,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupStepKillSyncAgent,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupCloseOpHeader,
	})

	// kill sync-agent
	// 顺序需要保持：kill-sync-agent -> delete sync-point -> delete sync channel
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupStepKillSyncAgent,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     syncgroup.ProcessKillSyncAgent,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupDeleteSyncPoint,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupStepKillSyncAgent},

		workflow.WithMaxReentry(2, WorkFlowLocalDeleteSyncGroupDeleteSyncPoint))

	// delete sync point
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupDeleteSyncPoint,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     syncgroup.ProcessDelSyncPoint,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupDelSyncChannel,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupDeleteSyncPoint,
	})

	//	删除同步通道
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupDelSyncChannel,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     syncgroup.ProcessDelSyncChannel,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupRebootBuildMeta,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupDelSyncChannel},

		workflow.WithMaxReentry(2, WorkFlowLocalDeleteSyncGroupRebootBuildMeta))

	// 重启集群（使用升级替代）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupRebootBuildMeta,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     buildmeta.ProcessBuildMetaForSyncGroupUpgrade,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupRebootUpgradeSlaves,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupRebootBuildMeta,
	})

	// Step-1 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupRebootUpgradeSlaves,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupRebootCheckSync,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupRebootUpgradeSlaves},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-2 等待数据同步后、执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupRebootCheckSync,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupRebootHandOver,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupRebootCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-3 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupRebootHandOver,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     handover.ProcessHandoverClusterForRestarting,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupRebootUpgradeMaster,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupRebootHandOver,
	})

	// Step-4 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupRebootUpgradeMaster,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupRebootUpgradeMaster},

		workflow.WithStepTimeout(15*time.Minute))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupUnblockMonitorInXmaster,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupUpdateAppTopologyInXmaster},
		workflow.WithMaxReentry(5, WorkFlowLocalDeleteSyncGroupUnblockMonitorInXmaster))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupUnblockMonitorInXmaster,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     xmaster.ProcessUnblockAppMonitorInXmasterAll,
		SuccessNextStep: WorkFlowLocalDeleteSyncGroupStepSuccessCb,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupUnblockMonitorInXmaster},
		workflow.WithMaxReentry(5, WorkFlowLocalDeleteSyncGroupStepSuccessCb))

	// 返回成功
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowLocalDeleteSyncGroupStepSuccessCb,
		Workflow:        WorkFlowLocalDeleteSyncGroup,
		StepProcess:     callback.ProcessGlobalSyncQuitSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowLocalDeleteSyncGroupStepSuccessCb,
	})
}
