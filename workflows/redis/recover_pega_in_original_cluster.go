package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/failover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/recover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowScsRecoverPegaInOriginalCluster             = "scs-recover-pega-in-original-cluster"
	StepRecoverPegaInOriginalClusterTaskStepRecoverData = "scs-recover-pega-in-original-cluster-task-step-recover-data"
	StepRecoverPegaInOriginalClusterCreateNewCDS        = "scs-recover-pega-in-original-cluster-create-new-cds"
	StepRecoverPegaInOriginalClusterDisableFailover     = "scs-recover-pega-in-original-cluster-disable-failover"
	StepRecoverPegaInOriginalClusterUseNewCDS           = "scs-recover-pega-in-original-cluster-use-new-cds"
	StepRecoverPegaInOriginalClusterEnableFailover      = "scs-recover-pega-in-original-cluster-enable-failover"
	StepRecoverPegaInOriginalClusterDeleteOldCDS        = "scs-recover-pega-in-original-cluster-delete-old-cds"
	StepRecoverPegaInOriginalClusterTaskStepSuccess     = "scs-recover-pega-in-original-cluster-task-step-success"
	StepRecoverPegaInOriginalClusterCallBack            = "scs-recover-pega-in-original-cluster-cb" // 将cache_cluster status 从恢复中改成运行中
)

func init() {
	// Step-2 dispatch task
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverPegaInOriginalClusterTaskStepRecoverData,
		Workflow: WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepRecoverData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverPegaInOriginalClusterCreateNewCDS,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterTaskStepRecoverData,
	})

	// create new cds
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterCreateNewCDS,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     recover.ProcessSnapshotRecoverRedisInOriginalAppCreateNewCDS,
		SuccessNextStep: StepRecoverPegaInOriginalClusterDisableFailover,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterCreateNewCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// Step 关闭自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterDisableFailover,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     failover.ProcessDisableFailover,
		SuccessNextStep: StepRecoverPegaInOriginalClusterUseNewCDS,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterDisableFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterUseNewCDS,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     recover.ProcessSnapshotRecoverRedisInOriginalAppUseNewCDS,
		SuccessNextStep: StepRecoverPegaInOriginalClusterEnableFailover,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterUseNewCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// Step 开启自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterEnableFailover,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     failover.ProcessEnableFailover,
		SuccessNextStep: StepRecoverPegaInOriginalClusterDeleteOldCDS,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterEnableFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterDeleteOldCDS,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     recover.ProcessSnapshotRecoverRedisInOriginalAppDeleteOldCDS,
		SuccessNextStep: StepRecoverPegaInOriginalClusterTaskStepSuccess,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterDeleteOldCDS},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterTaskStepSuccess,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRecoverPegaInOriginalClusterCallBack,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterTaskStepSuccess,
	})

	// Step-3 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverPegaInOriginalClusterCallBack,
		Workflow:        WorkflowScsRecoverPegaInOriginalCluster,
		StepProcess:     callback.ProcessRecoverInOriginalClusterCallBack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverPegaInOriginalClusterCallBack,
	})
}
