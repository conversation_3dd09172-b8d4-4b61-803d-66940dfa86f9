/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* upgrade_cluster_app.go - File Description */
/*
Modification History
--------------------
2022/5/28, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowUpgradeCluster                       = "scs-upgrade-cluster-app"
	StepUpgradeClusterInitTimeWindowTask         = "scs-upgrade-cluster-init-tw-task"
	StepUpgradeClusterBuildMeta                  = "scs-upgrade-cluster-app-build-meta"
	StepUpgradeClusterTaskStepUpgradeFollowers   = "scs-upgrade-cluster-task-step-upgrade-followers"
	StepUpgradeClusterSlavesUpgrade              = "scs-upgrade-cluster-slaves-upgrade"
	StepUpgradeClusterCheckSync                  = "scs-upgrade-cluster-check-sync"
	StepUpgradeClusterTaskStepHandover           = "scs-upgrade-cluster-task-step-handover"
	StepUpgradeClusterHandover                   = "scs-upgrade-cluster-handover"
	StepUpgradeClusterTaskStepUpgradeMaster      = "scs-upgrade-cluster-task-step-upgrade-master"
	StepUpgradeClusterMasterUpgrade              = "scs-upgrade-cluster-master-upgrade"
	StepUpgradeClusterSecondCheckSync            = "scs-upgrade-cluster-second-check-sync"
	StepUpgradeClusterTaskStepUpgradeProxies     = "scs-upgrade-cluster-task-step-upgrade-proxies"
	StepUpgradeClusterProxiesRestart             = "scs-upgrade-cluster-proxies-restart"
	StepUpgradeClusterUpdateTlsConfIfNeeded      = "scs-upgrade-cluster-update-tls-conf-if-needed"
	StepUpgradeClusterUpdateAppTopologyInXmaster = "scs-upgrade-cluster-update-app-topo-in-xmaster"
	StepUpgradeClusterTaskStepSuccess            = "scs-upgrade-cluster-task-step-success"
	StepUpgradeClusterCallback                   = "scs-upgrade-cluster-cb"
)

func init() {
	// 定义WORKFLOW的执行步骤
	// 关联时间窗口任务
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterInitTimeWindowTask,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepUpgradeClusterBuildMeta,
		ErrorNextStep:   StepUpgradeClusterInitTimeWindowTask,
	})
	// Step-0 构建元数据，包括创建Clusters、Cluster对应的Nodes并分配角色
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterBuildMeta,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     buildmeta.ProcessBuildMetaForUpgrade,
		SuccessNextStep: StepUpgradeClusterTaskStepUpgradeFollowers,
		ErrorNextStep:   StepUpgradeClusterBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeClusterTaskStepUpgradeFollowers,
		Workflow: WorkflowUpgradeCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepUpgradeFollowers, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeClusterSlavesUpgrade,
		ErrorNextStep:   StepUpgradeClusterTaskStepUpgradeFollowers,
	})

	// Step-1 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterSlavesUpgrade,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: StepUpgradeClusterCheckSync,
		ErrorNextStep:   StepUpgradeClusterSlavesUpgrade},

		workflow.WithStepTimeout(15*time.Minute))

	// Step-2 等待数据同步后、执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterCheckSync,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepUpgradeClusterTaskStepHandover,
		ErrorNextStep:   StepUpgradeClusterCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeClusterTaskStepHandover,
		Workflow: WorkflowUpgradeCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepHandover, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeClusterHandover,
		ErrorNextStep:   StepUpgradeClusterTaskStepHandover,
	})

	// Step-3 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterHandover,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     handover.ProcessHandoverClusterForRestarting,
		SuccessNextStep: StepUpgradeClusterTaskStepUpgradeMaster,
		ErrorNextStep:   StepUpgradeClusterHandover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeClusterTaskStepUpgradeMaster,
		Workflow: WorkflowUpgradeCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepUpgradeMasters, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeClusterMasterUpgrade,
		ErrorNextStep:   StepUpgradeClusterTaskStepUpgradeMaster,
	})

	// Step-4 执行所有从节点的升级&重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterMasterUpgrade,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     upgrade.ProcessUpgradeSlaves,
		SuccessNextStep: StepUpgradeClusterSecondCheckSync,
		ErrorNextStep:   StepUpgradeClusterMasterUpgrade},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterSecondCheckSync,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepUpgradeClusterTaskStepUpgradeProxies,
		ErrorNextStep:   StepUpgradeClusterSecondCheckSync,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepUpgradeClusterTaskStepUpgradeProxies,
		Workflow: WorkflowUpgradeCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.UpgradeTask, timewindow.StepUpgradeProxies, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepUpgradeClusterProxiesRestart,
		ErrorNextStep:   StepUpgradeClusterTaskStepUpgradeProxies,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterProxiesRestart,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     upgrade.ProcessUpgradeProxies,
		SuccessNextStep: StepUpgradeClusterUpdateTlsConfIfNeeded,
		ErrorNextStep:   StepUpgradeClusterProxiesRestart},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterUpdateTlsConfIfNeeded,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     util.UpdateTLSConfIfNeeded,
		SuccessNextStep: StepUpgradeClusterUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepUpgradeClusterUpdateTlsConfIfNeeded},

		workflow.WithStepTimeout(15*time.Minute))

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterUpdateAppTopologyInXmaster,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepUpgradeClusterTaskStepSuccess,
		ErrorNextStep:   StepUpgradeClusterUpdateAppTopologyInXmaster,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterTaskStepSuccess,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepUpgradeClusterCallback,
		ErrorNextStep:   StepUpgradeClusterTaskStepSuccess,
	})

	// Step-4 更新成功回调
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeClusterCallback,
		Workflow:        WorkflowUpgradeCluster,
		StepProcess:     callback.ProcessUpgradeOrRestartSuccessCb,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUpgradeClusterCallback,
	})
}
