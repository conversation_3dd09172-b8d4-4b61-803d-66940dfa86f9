package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/blb"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/crossAzNearest"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/dns"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/endpoint"
)

const (
	EnableCrossAzNearestWorkflow               = "cross_az_nearest_enable"
	StepEnableCrossAzNearestBuildMeta          = "step_enable_cross_az_nearest_build_meta"
	StepEnableCrossAzNearestInitBLB            = "step_enable_cross_az_nearest_init_blb"
	StepEnableCrossAzNearestInitEndpoint       = "step_enable_cross_az_nearest_init_endpoint"
	StepEnableCrossAzNearestAddInterface       = "step_enable_cross_az_nearest_add_interface"
	StepEnableCrossAzNearestWaitAddInterface   = "step_enable_cross_az_nearest_wait_add_interface"
	StepEnableCrossAzNearestAddOpProxy         = "step_enable_cross_az_nearest_add_op_proxy"
	StepEnableCrossAzNearestWaitAddOpProxy     = "step_enable_cross_az_nearest_wait_add_op_proxy"
	StepEnableCrossAzNearestUpdateBlbRs        = "step_enable_cross_az_nearest_update_blb_rs"
	StepEnableCrossAzNearestUpdateBlbRsMcpack  = "step_enable_cross_az_nearest_update_blb_rs_mcpack"
	StepEnableCrossAzNearestUpdateMetaserver   = "step_enable_cross_az_nearest_update_metaserver"
	StepEnableCrossAzNearestUpdateConfig       = "step_enable_cross_az_nearest_update_config"
	StepEnableCrossAzNearestUpdateClusterEntry = "step_enable_cross_az_nearest_update_cluster_entry"
	StepEnableCrossAzNearestUpdateDomain       = "step_enable_cross_az_nearest_update_domain"
	StepEnableCrossAzNearestCallback           = "step_enable_cross_az_nearest_callback"

	DisableCrossAzNearestWorkflow               = "cross_az_nearest_disable"
	StepDisableCrossAzNearestBuildMeta          = "step_disable_cross_az_nearest_build_meta"
	StepDisableCrossAzNearestUpdateDomain       = "step_disable_cross_az_nearest_update_domain"
	StepDisableCrossAzNearestUpdateMetaserver   = "step_disable_cross_az_nearest_update_metaserver"
	StepDisableCrossAzNearestUpdateConfig       = "step_disable_cross_az_nearest_update_config"
	StepDisableCrossAzNearestUpdateClusterEntry = "step_disable_cross_az_nearest_update_cluster_entry"
	StepDisableCrossAzNearestUpdateBlbRs        = "step_disable_cross_az_nearest_update_blb_rs"
	StepDisableCrossAzNearestUpdateBlbRsMcpack  = "step_disable_cross_az_nearest_update_blb_rs_mcpack"
	StepDisableCrossAzNearestUnbindRs           = "step_disable_cross_az_nearest_unbind_rs"
	StepDisableCrossAzNearestUnbindMcpack       = "step_disable_cross_az_nearest_unbind_mcpack"
	StepDisableCrossAzNearestFakeDeleteBlbs     = "step_disable_cross_az_nearest_fake_delete_blbs"
	StepDisableCrossAzNearestCallback           = "step_disable_cross_az_nearest_callback"

	PurgeToDeleteBLBWorkflow    = "purge_to_delete_blb_workflow"
	StepPurgeToDeleteBLBExecute = "step_purge_to_delete_blb_execute"

	RebindToDeleteBLBWorkflow    = "rebind_to_delete_blb_workflow"
	StepRebindToDeleteBLBExecute = "step_rebind_to_delete_blb_execute"

	UnbindToDeleteBLBWorkflow    = "unbind_to_delete_blb_workflow"
	StepUnbindToDeleteBLBExecute = "step_unbind_to_delete_blb_execute"
)

func init() {
	// 开启跨AZ就近访问流程
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestBuildMeta,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessBuildMetaForEnableCrossAzNearest,
		SuccessNextStep: StepEnableCrossAzNearestInitBLB,
		ErrorNextStep:   StepEnableCrossAzNearestBuildMeta,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestInitBLB,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcessInitAppBLBNoUpdateCacheCluster,
		SuccessNextStep: StepEnableCrossAzNearestInitEndpoint,
		ErrorNextStep:   StepEnableCrossAzNearestInitBLB,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestInitEndpoint,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     endpoint.ProcessCreateCrossAzNearestRwEndpoint,
		SuccessNextStep: StepEnableCrossAzNearestAddInterface,
		ErrorNextStep:   StepEnableCrossAzNearestInitEndpoint,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestAddInterface,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessRecoverInterfacesForEnableCrossAzNearest,
		SuccessNextStep: StepEnableCrossAzNearestWaitAddInterface,
		ErrorNextStep:   StepEnableCrossAzNearestAddInterface,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestWaitAddInterface,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessWaitSubtasksFinish,
		SuccessNextStep: StepEnableCrossAzNearestAddOpProxy,
		ErrorNextStep:   StepEnableCrossAzNearestWaitAddInterface,
	}, workflow.WithStepTimeout(15*time.Minute))
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestAddOpProxy,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessAddOpProxy,
		SuccessNextStep: StepEnableCrossAzNearestWaitAddOpProxy,
		ErrorNextStep:   StepEnableCrossAzNearestAddOpProxy,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestWaitAddOpProxy,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessWaitSubtasksFinish,
		SuccessNextStep: StepEnableCrossAzNearestUpdateBlbRs,
		ErrorNextStep:   StepEnableCrossAzNearestWaitAddOpProxy,
	}, workflow.WithStepTimeout(15*time.Minute))
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestUpdateBlbRs,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcessSetProxyRs,
		SuccessNextStep: StepEnableCrossAzNearestUpdateBlbRsMcpack,
		ErrorNextStep:   StepEnableCrossAzNearestUpdateBlbRs,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestUpdateBlbRsMcpack,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepEnableCrossAzNearestUpdateMetaserver,
		ErrorNextStep:   StepEnableCrossAzNearestUpdateBlbRsMcpack,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestUpdateMetaserver,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessUpdateMetaForEnableCrossAzNearest,
		SuccessNextStep: StepEnableCrossAzNearestUpdateConfig,
		ErrorNextStep:   StepEnableCrossAzNearestUpdateMetaserver,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestUpdateConfig,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     config.ProcessUpdateConfigNew,
		SuccessNextStep: StepEnableCrossAzNearestUpdateClusterEntry,
		ErrorNextStep:   StepEnableCrossAzNearestUpdateConfig,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestUpdateClusterEntry,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessUpdateProxyClusterEntry,
		SuccessNextStep: StepEnableCrossAzNearestUpdateDomain,
		ErrorNextStep:   StepEnableCrossAzNearestUpdateClusterEntry,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestUpdateDomain,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     dns.ProcessAppDomainAddRecordsWithAz,
		SuccessNextStep: StepEnableCrossAzNearestCallback,
		ErrorNextStep:   StepEnableCrossAzNearestUpdateDomain,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepEnableCrossAzNearestCallback,
		Workflow:        EnableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessEnableCrossAzNearestCallback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepEnableCrossAzNearestCallback,
	})

	// 关闭跨AZ就近访问流程
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestBuildMeta,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessBuildMetaForDisableCrossAzNearest,
		SuccessNextStep: StepDisableCrossAzNearestUpdateDomain,
		ErrorNextStep:   StepDisableCrossAzNearestBuildMeta,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUpdateDomain,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     dns.ProcessAppDomainDeleteRecordsWithAz,
		SuccessNextStep: StepDisableCrossAzNearestUpdateMetaserver,
		ErrorNextStep:   StepDisableCrossAzNearestUpdateDomain,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUpdateMetaserver,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessUpdateMetaForDisableCrossAzNearest,
		SuccessNextStep: StepDisableCrossAzNearestUpdateConfig,
		ErrorNextStep:   StepDisableCrossAzNearestUpdateMetaserver,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUpdateConfig,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     config.ProcessUpdateConfigNew,
		SuccessNextStep: StepDisableCrossAzNearestUpdateClusterEntry,
		ErrorNextStep:   StepDisableCrossAzNearestUpdateConfig,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUpdateClusterEntry,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessUpdateProxyClusterEntry,
		SuccessNextStep: StepDisableCrossAzNearestUpdateBlbRs,
		ErrorNextStep:   StepDisableCrossAzNearestUpdateClusterEntry,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUpdateBlbRs,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcessSetProxyRs,
		SuccessNextStep: StepDisableCrossAzNearestUpdateBlbRsMcpack,
		ErrorNextStep:   StepDisableCrossAzNearestUpdateBlbRs,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUpdateBlbRsMcpack,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcessSetProxyRsForMcpack,
		SuccessNextStep: StepDisableCrossAzNearestUnbindRs,
		ErrorNextStep:   StepDisableCrossAzNearestUpdateBlbRsMcpack,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUnbindRs,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcesUnbindAllProxysForDisableCrossAzNearest,
		SuccessNextStep: StepDisableCrossAzNearestUnbindMcpack,
		ErrorNextStep:   StepDisableCrossAzNearestUnbindRs,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestUnbindMcpack,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcesUnbindAllProxysMcpackForDisableCrossAzNearest,
		SuccessNextStep: StepDisableCrossAzNearestFakeDeleteBlbs,
		ErrorNextStep:   StepDisableCrossAzNearestUnbindMcpack,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestFakeDeleteBlbs,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     blb.ProcessFakeDeleteBlbsForDisableCrossAz,
		SuccessNextStep: StepDisableCrossAzNearestCallback,
		ErrorNextStep:   StepDisableCrossAzNearestFakeDeleteBlbs,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepDisableCrossAzNearestCallback,
		Workflow:        DisableCrossAzNearestWorkflow,
		StepProcess:     crossAzNearest.ProcessDisableCrossAzNearestCallback,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepDisableCrossAzNearestCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepPurgeToDeleteBLBExecute,
		Workflow:        PurgeToDeleteBLBWorkflow,
		StepProcess:     blb.ProcessPurgeToDeleteBLBs,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepPurgeToDeleteBLBExecute,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRebindToDeleteBLBExecute,
		Workflow:        RebindToDeleteBLBWorkflow,
		StepProcess:     blb.ProcessRebingProxyRsForToDeleteBlbs,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRebindToDeleteBLBExecute,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUnbindToDeleteBLBExecute,
		Workflow:        UnbindToDeleteBLBWorkflow,
		StepProcess:     blb.ProcessUnbindProxyRsForToDeleteBlbs,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepUnbindToDeleteBLBExecute,
	})
}
