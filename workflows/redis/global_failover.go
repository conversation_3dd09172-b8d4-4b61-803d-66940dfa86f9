/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_failover.go */
/*
modification history
--------------------
2022/07/13 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package workflows

import (
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkFlowGlobalFailover                                = "scs-global-failover"
	WorkFlowGlobalFailover_StepBuildMeta                  = "scs-global-failover-build-meta"
	WorkFlowGlobalFailover_StepUpdateAppTopologyInXmaster = "scs-global-failover-update-app-topo-in-xmaster"
)

func init() {
	// 更新节点的role
	// 回调csmaster修改csmaster的instance role还有master slave
	workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowGlobalFailover_StepBuildMeta,
		Workflow:        WorkFlowGlobalFailover,
		StepProcess:     buildmeta.ProcessBuildMetaForGlobalFailover,
		SuccessNextStep: WorkFlowGlobalFailover_StepUpdateAppTopologyInXmaster,
		ErrorNextStep:   WorkFlowGlobalFailover_StepBuildMeta,
	})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            WorkFlowGlobalFailover_StepUpdateAppTopologyInXmaster,
		Workflow:        WorkFlowGlobalFailover,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   WorkFlowGlobalFailover_StepUpdateAppTopologyInXmaster,
	})
}
