/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
// yuning
执行节点重启
这里不参考原有代码，原有代码逻辑过于奇葩；
需要进行单独的详细设计，TODO 详细设计文档

Parameters
{
	"AppID": "scs-bj-nxewpztnsreg",
}
*/

package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/gmaster"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/monitorflag"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/readonlygroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/restart"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/xmaster"
)

const (
	WorkflowRestartStandalone                       = "scs-restart-standalone-app"
	StepRestartStandaloneInitTimeWindowTask         = "scs-restart-standalone-init-tw-task"
	StepRestartStandaloneAddStartEvent              = "scs-restart-standalone-add-start-event"
	StepRestartStandaloneBuildMeta                  = "scs-restart-standalone-app-build-meta"
	StepRestartStandaloneTaskStepRestartFollowers   = "scs-restart-standalone-task-step-restart-followers"
	StepRestartStandaloneSlavesRestart              = "scs-restart-standalone-slaves-restart"
	StepRestartStandaloneSlavesCheckSync            = "scs-restart-standalone-check-sync"
	StepRestartStandaloneTaskStepHandover           = "scs-restart-standalone-task-step-handover"
	StepRestartStandaloneHandover                   = "scs-restart-standalone-handover"
	StepRestartStandaloneRoInstSlaveOf              = "scs-restart-standalone-ro-slave-of"
	StepRestartStandaloneRoInstCheckSync            = "scs-restart-standalone-ro-check-sync"
	StepRestartStandaloneRoInstRestart              = "scs-restart-standalone-ro-instances-restart"
	StepRestartStandaloneTaskStepRestartMasters     = "scs-restart-standalone-task-step-restart-masters"
	StepRestartStandaloneMasterRestart              = "scs-restart-standalone-master-restart"
	StepRestartStandaloneSecondCheckSync            = "scs-restart-standalone-second-check-sync"
	StepRestartStandaloneGlobalRestart              = "scs-restart-standalone-global-restart"
	StepRestartStandaloneGlobalRestartRoInst        = "scs-restart-standalone-global-restart-ro-inst"
	StepRestartStandaloneGlobalSlaveof              = "scs-restart-standalone-global-slaveof"
	StepRestartStandaloneGlobalCheckSync            = "scs-restart-standalone-global-check-sync"
	StepRestartStandalonePushMonitorHTGRPSlaveFlag  = "scs-restart-standalone-push-htgrp-slave-flag"
	StepRestartStandaloneDeployFilebeat             = "scs-restart-standalone-deploy-filebeat"
	StepRestartStandaloneUpdateAzDeployInfo         = "scs-restart-standalone-update-azdeployinfo"
	StepRestartStandaloneCloseTimeWindowTask        = "scs-restart-standalone-close-tw-task"
	StepRestartStandaloneUpdateAppTopologyInXmaster = "scs-restart-standalone-update-app-topo-in-xmaster"
	StepRestartStandaloneCallback                   = "scs-restart-standalone-callback"
	StepRestartStandaloneTaskStepSuccess            = "scs-restart-standalone-task-step-success"
	StepRestartStandaloneAddSuccessEndEvent         = "scs-restart-standalone-add-success-end-event"
)

func init() {
	// 定义WORKFLOW的执行步骤

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneInitTimeWindowTask,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     timewindow.ProcessSetTaskID,
		SuccessNextStep: StepRestartStandaloneAddStartEvent,
		ErrorNextStep:   StepRestartStandaloneInitTimeWindowTask,
	})

	// Step-0 构建元数据，包括创建Clusters、Cluster对应的Nodes并分配角色
	// cuiyi01

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneAddStartEvent,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     event.ProcessAddRestartingStartEvent,
		SuccessNextStep: StepRestartStandaloneBuildMeta,
		ErrorNextStep:   StepRestartStandaloneAddStartEvent,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneBuildMeta,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     buildmeta.ProcessBuildMetaForRestart,
		SuccessNextStep: StepRestartStandaloneTaskStepRestartFollowers,
		ErrorNextStep:   StepRestartStandaloneBuildMeta,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartStandaloneTaskStepRestartFollowers,
		Workflow: WorkflowRestartStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepRestartFollowers, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRestartStandaloneSlavesRestart,
		ErrorNextStep:   StepRestartStandaloneTaskStepRestartFollowers,
	})

	// Step-1 执行所有从节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneSlavesRestart,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     restart.ProcessRestartSlavesStandalone,
		SuccessNextStep: StepRestartStandaloneSlavesCheckSync,
		ErrorNextStep:   StepRestartStandaloneSlavesRestart},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-2-1 检查能否切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneSlavesCheckSync,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartStandaloneTaskStepHandover,
		ErrorNextStep:   StepRestartStandaloneSlavesCheckSync},

		workflow.WithStepTimeout(15*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartStandaloneTaskStepHandover,
		Workflow: WorkflowRestartStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepHandover, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRestartStandaloneHandover,
		ErrorNextStep:   StepRestartStandaloneTaskStepHandover,
	})

	// Step-2-2 执行主从切换
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneHandover,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     restart.ProcessHandoverStandalone,
		SuccessNextStep: StepRestartStandaloneRoInstSlaveOf,
		ErrorNextStep:   StepRestartStandaloneHandover,
	})

	// 只读节点重新挂主（需要注意热活相关内容处理）
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneRoInstSlaveOf,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     readonlygroup.ProcessRoInstSlaveOf,
		SuccessNextStep: StepRestartStandaloneRoInstCheckSync,
		ErrorNextStep:   StepRestartStandaloneRoInstSlaveOf,
	})

	// 检查同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneRoInstCheckSync,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartStandaloneRoInstRestart,
		ErrorNextStep:   StepRestartStandaloneRoInstCheckSync,
	})

	// 执行所有只读节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneRoInstRestart,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     restart.ProcessRestartReadonlyInstances,
		SuccessNextStep: StepRestartStandaloneTaskStepRestartMasters,
		ErrorNextStep:   StepRestartStandaloneRoInstRestart},

		workflow.WithStepTimeout(5*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartStandaloneTaskStepRestartMasters,
		Workflow: WorkflowRestartStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepRestartMasters, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRestartStandaloneMasterRestart,
		ErrorNextStep:   StepRestartStandaloneTaskStepRestartMasters,
	})

	// Step-3 执行所有旧主节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneMasterRestart,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     restart.ProcessRestartSlavesStandalone,
		SuccessNextStep: StepRestartStandaloneSecondCheckSync,
		ErrorNextStep:   StepRestartStandaloneMasterRestart},

		workflow.WithStepTimeout(5*time.Minute))

	// 再次检查所有从节点的同步状态
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneSecondCheckSync,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartStandaloneGlobalRestart,
		ErrorNextStep:   StepRestartStandaloneSecondCheckSync,
	})

	// Step-3 执行所有热活中的从节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneGlobalRestart,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     restart.ProcessRestartSlavesStandaloneInGroup,
		SuccessNextStep: StepRestartStandaloneGlobalRestartRoInst,
		ErrorNextStep:   StepRestartStandaloneGlobalRestart},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-3 执行所有热活中的只读节点的重启
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneGlobalRestartRoInst,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     restart.ProcessRestartRoInstStandaloneInGroup,
		SuccessNextStep: StepRestartStandaloneGlobalSlaveof,
		ErrorNextStep:   StepRestartStandaloneGlobalRestartRoInst},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-4 手动挂主，防止热活组找不到主的port
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneGlobalSlaveof,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     gmaster.ProcessGlobalSlaveOf,
		SuccessNextStep: StepRestartStandaloneGlobalCheckSync,
		ErrorNextStep:   StepRestartStandaloneGlobalSlaveof},

		workflow.WithStepTimeout(5*time.Minute))

	// Step-4 检查热活从节点的同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneGlobalCheckSync,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRestartStandalonePushMonitorHTGRPSlaveFlag,
		ErrorNextStep:   StepRestartStandaloneGlobalCheckSync},

		workflow.WithStepTimeout(5*time.Minute))

	// 给监控下发热活从地域flag
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandalonePushMonitorHTGRPSlaveFlag,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     monitorflag.ProcessUpdateHTGRPSlaveFlagAll,
		SuccessNextStep: StepRestartStandaloneDeployFilebeat,
		ErrorNextStep:   StepRestartStandalonePushMonitorHTGRPSlaveFlag,
	})

	// Step-4 部署filebeat for 存量集群重启操作
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneDeployFilebeat,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     deploy.ProcessDeployFilebeat,
		SuccessNextStep: StepRestartStandaloneUpdateAzDeployInfo,
		ErrorNextStep:   StepRestartStandaloneDeployFilebeat},
		workflow.WithStepTimeout(5*time.Minute),
		workflow.WithStepSplitHandler(util.GetInUseRedisInsts))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneUpdateAzDeployInfo,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     handover.ProcessHandoverUpdateAzDeployInfo,
		SuccessNextStep: StepRestartStandaloneCloseTimeWindowTask,
		ErrorNextStep:   StepRestartStandaloneUpdateAzDeployInfo},
		workflow.WithMaxReentry(3, StepRestartStandaloneCloseTimeWindowTask))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneCloseTimeWindowTask,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     timewindow.ProcessSetTWTaskStatusSuccess,
		SuccessNextStep: StepRestartStandaloneUpdateAppTopologyInXmaster,
		ErrorNextStep:   StepRestartStandaloneCloseTimeWindowTask})

	// 更新拓扑信息到xmaster
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneUpdateAppTopologyInXmaster,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     xmaster.ProcessUpdateAppTopologyInXmaster,
		SuccessNextStep: StepRestartStandaloneCallback,
		ErrorNextStep:   StepRestartStandaloneUpdateAppTopologyInXmaster},
		workflow.WithMaxReentry(5, StepRestartStandaloneCallback))

	// Step-4 重启成功回调
	// 调用CsMaster的API，修改cluster状态为5
	// cuiyi01
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneCallback,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     callback.ProcessUpgradeOrRestartSuccessCb,
		SuccessNextStep: StepRestartStandaloneTaskStepSuccess,
		ErrorNextStep:   StepRestartStandaloneCallback,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRestartStandaloneTaskStepSuccess,
		Workflow: WorkflowRestartStandalone,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RelaunchTask, timewindow.StepRestartMasters, timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRestartStandaloneAddSuccessEndEvent,
		ErrorNextStep:   StepRestartStandaloneTaskStepSuccess,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRestartStandaloneAddSuccessEndEvent,
		Workflow:        WorkflowRestartStandalone,
		StepProcess:     event.ProcessAddRestartingSuccessEndEvent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRestartStandaloneAddSuccessEndEvent,
	})
}
