package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/migrate"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
)

const (
	WorkflowUpgrageAgents   = "scs-workflow-upgrade-agents"
	StepUpgradeAgentExecute = "step-upgrade-agent-execute"

	WorflowMigrateToNewAgentCheckConfs     = "scs-workflow-migrate-to-new-agent-check-confs"
	StepMigrateToNewAgentCheckConfsExecute = "step-migrate-to-new-agent-check-confs-execute"

	WorkflowMigrateToNewAgentPreCheck    = "scs-workflow-migrate-to-new-agent-pre-check"
	StepMigrateToNewAgentPreCheckExecute = "step-migrate-to-new-agent-pre-check-execute"

	WorkflowMigrateToNewAgent         = "scs-workflow-migrate-to-new-agent"
	StepMigrateToNewAgentPrecheck     = "step-migrate-to-new-agent-precheck"
	StepMigrateToNewAgentExecute      = "step-migrate-to-new-agent-execute"
	StepMigrateToNewAgentCheckBcmPush = "step-migrate-to-new-agent-check-bcm-push"

	WorkflowMigrateToNewAgentRollback    = "scs-workflow-migrate-to-new-agent-rollback"
	StepMigrateToNewAgentRollbackExecute = "step-migrate-to-new-agent-rollback-execute"
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepUpgradeAgentExecute,
		Workflow:        WorkflowUpgrageAgents,
		StepProcess:     upgrade.ProcessUpgradePackages,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError,
	},
		workflow.WithStepTimeout(10*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToNewAgentCheckConfsExecute,
		Workflow:        WorflowMigrateToNewAgentCheckConfs,
		StepProcess:     migrate.ProcessCheckAppsConf,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToNewAgentPreCheckExecute,
		Workflow:        WorkflowMigrateToNewAgentPreCheck,
		StepProcess:     migrate.ProcessPreCheckForMigration,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   workflow.FinalStepError,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToNewAgentPrecheck,
		Workflow:        WorkflowMigrateToNewAgent,
		StepProcess:     migrate.ProcessPreCheckForMigration,
		SuccessNextStep: StepMigrateToNewAgentExecute,
		ErrorNextStep:   workflow.FinalStepError,
	})
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToNewAgentExecute,
		Workflow:        WorkflowMigrateToNewAgent,
		StepProcess:     migrate.ProcessMigrateToNewAgent,
		SuccessNextStep: StepMigrateToNewAgentCheckBcmPush,
		ErrorNextStep:   StepMigrateToNewAgentExecute,
	}, workflow.WithStepTimeout(30*time.Minute))
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToNewAgentCheckBcmPush,
		Workflow:        WorkflowMigrateToNewAgent,
		StepProcess:     migrate.ProcessCheckPushBcm,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateToNewAgentCheckBcmPush,
	}, workflow.WithStepTimeout(10*time.Minute))

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepMigrateToNewAgentRollbackExecute,
		Workflow:        WorkflowMigrateToNewAgentRollback,
		StepProcess:     migrate.ProcessRollbackMigrateToNewAgent,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepMigrateToNewAgentRollbackExecute,
	}, workflow.WithStepTimeout(10*time.Minute))
}
