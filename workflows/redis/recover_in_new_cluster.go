package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/failover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/recover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncredis"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

/*
克隆恢复参数:

	原集群app_id:
	当前集群app_id:
	备份集id:
*/

/*
1、build_meta

	集群创建完成后增加一步，判断当前集群状态，如果backup_status=BACKUP_CLUSTER_WAIT_RECOVER，
	则该集群为数据恢复集群，则创建 recover_in_new_cluster 的任务。
	任务参数为:
		原集群app_id:
		当前集群app_id:
		备份集id:
		数据恢复类型: rdb恢复（对比时间点恢复）


	if ((table->backup_status() == (int)BACKUP_CLUSTER_WAIT_RECOVER )
	&& (table->status() == CACHE_CLUSTER_RUNNING ||
	table->status() == CACHE_CLUSTER_MODIFIED_FAILED ||
	table->status() == CACHE_CLUSTER_FLUSH_FAILED ||
	table->status() == CACHE_CLUSTER_RECOVER_FAILED ||
	table->status() == CACHE_CLUSTER_PAUSED ||
	table->status() == CACHE_CLUSTER_EXCHANGE_FAILED)) {

2、clone_cluster_backup_record
	2.1、克隆原集群slot分布至新集群
		clone_update_slot(desc_cluster_id, src_cluster_id,src_inst_ids_and_shard_ids, desc_inst_ids_and_shard_ids);
	2.2、克隆原集群备份信息至新集群
		BackupRecordModel* model = new BackupRecordModel(

3、跟原地恢复保持一致

4、成功回调:
	status : 5
	backup_status : 0

*/

const (
	WorkflowScsRecoverInNewCluster             = "scs-recover-in-new-cluster"
	StepRecoverInNewClusterTaskStepPrepareData = "scs-recover-in-new-cluster-task-step-prepare-data"
	StepRecoverInNewClusterBuildMeta           = "scs-recover-in-new-cluster-build-meta"
	StepRecoverInNewClusterCloneTopo           = "scs-recover-in-new-cluster-clone-topo"
	StepRecoverInNewClusterCloneBackupRecord   = "scs-recover-in-new-cluster-clone-backup-record"
	StepRecoverInNewClusterTaskStepRecoverData = "scs-recover-in-new-cluster-task-step-recover-data"
	StepRecoverInNewClusterDisableFailover     = "scs-recover-in-new-cluster-disable-failover"
	StepRecoverInNewClusterDispatchTask        = "scs-recover-in-new-cluster-dispatch-task"
	StepRecoverInNewClusterCheckAllSync        = "scs-recover-in-new-cluster-check-all-sync"
	StepRecoverInNewClusterEnableFailover      = "scs-recover-in-new-cluster-enable-failover"
	StepRecoverInNewClusterTaskStepSuccess     = "scs-recover-in-new-cluster-task-step-success"
	StepRecoverInNewClusterCallBack            = "scs-recover-in-new-cluster-cb" // 将cache_cluster status 从恢复中改成运行中
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverInNewClusterTaskStepPrepareData,
		Workflow: WorkflowScsRecoverInNewCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepPrepareData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverInNewClusterBuildMeta,
		ErrorNextStep:   StepRecoverInNewClusterTaskStepPrepareData,
	})

	// 增加build_meta,更新cache_cluster status = 13[暂定],此值需要重新定义，并且联动list接口进行状态展示
	// Step-1 集群状态维护
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterBuildMeta,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterBuildMeta,
		SuccessNextStep: StepRecoverInNewClusterCloneTopo,
		ErrorNextStep:   StepRecoverInNewClusterBuildMeta,
	})

	// Step-2 克隆集群拓扑
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterCloneTopo,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterCloneTopo,
		SuccessNextStep: StepRecoverInNewClusterCloneBackupRecord,
		ErrorNextStep:   StepRecoverInNewClusterCloneTopo,
	})

	// Step-4 构建新集群的备份记录
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterCloneBackupRecord,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     buildmeta.ProcessRecoverInNewClusterCloneBackupRecord,
		SuccessNextStep: StepRecoverInNewClusterTaskStepRecoverData,
		ErrorNextStep:   StepRecoverInNewClusterCloneBackupRecord,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:     StepRecoverInNewClusterTaskStepRecoverData,
		Workflow: WorkflowScsRecoverInNewCluster,
		StepProcess: timewindow.GetUpdateTaskDetailProcessor(
			timewindow.RecoverTask, timewindow.StepRecoverData, timewindow.TaskStatusExecuting),
		SuccessNextStep: StepRecoverInNewClusterDisableFailover,
		ErrorNextStep:   StepRecoverInNewClusterTaskStepRecoverData,
	})

	// Step 关闭自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDisableFailover,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     failover.ProcessDisableFailover,
		SuccessNextStep: StepRecoverInNewClusterDispatchTask,
		ErrorNextStep:   StepRecoverInNewClusterDisableFailover,
	})

	// Step-5 dispatch task
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterDispatchTask,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     recover.ProcessDbrsRecoverRedisInNewClusterDispatchTask,
		SuccessNextStep: StepRecoverInNewClusterCheckAllSync,
		ErrorNextStep:   StepRecoverInNewClusterDispatchTask},

		workflow.WithStepTimeout(60*time.Minute),
		workflow.WithStepSplitHandler(util.GetToRecoverRedisShardIds))

	// 检查数据同步
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterCheckAllSync,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     syncredis.PocessCheckRedisAllSync,
		SuccessNextStep: StepRecoverInNewClusterEnableFailover,
		ErrorNextStep:   StepRecoverInNewClusterCheckAllSync},
		workflow.WithStepTimeout(15*time.Minute))

	// Step 开启自愈
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterEnableFailover,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     failover.ProcessEnableFailover,
		SuccessNextStep: StepRecoverInNewClusterTaskStepSuccess,
		ErrorNextStep:   StepRecoverInNewClusterEnableFailover,
	})

	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterTaskStepSuccess,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     timewindow.GetUpdateTaskDetailProcessor("", "", timewindow.TaskStatusSuccess),
		SuccessNextStep: StepRecoverInNewClusterCallBack,
		ErrorNextStep:   StepRecoverInNewClusterTaskStepSuccess,
	})

	// Step-6 callback
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepRecoverInNewClusterCallBack,
		Workflow:        WorkflowScsRecoverInNewCluster,
		StepProcess:     callback.ProcessRecoverInNewClusterCallBack,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepRecoverInNewClusterCallBack,
	})
}
