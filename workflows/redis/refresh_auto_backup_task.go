package workflows

import (
	"time"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
)

const (
	WorkflowScsRefreshAutoBackupTask    = "scs-refresh-auto-backup-task"
	StepCheckAndUpdateBackupTask        = "scs-check-and-update-backup-task"
	StepBackupCb                        = "scs-backup-cb"                            // 将cache_cluster backupstatus改成0
	StepCheckAndClearExpireBackupRecord = "scs-check-and-clear-expire-backup-record" // 清理过期的备份任务
)

func init() {
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCheckAndUpdateBackupTask,
		Workflow:        WorkflowScsRefreshAutoBackupTask,
		StepProcess:     backup.ProcessRefreshBackupTask,
		SuccessNextStep: StepBackupCb,
		ErrorNextStep:   StepCheckAndUpdateBackupTask},
		// 备份任务最大执行时间 1h
		workflow.WithStepTimeout(1*60*time.Minute))

	/*
		成功回调:
			将cache_cluster backupstatus改成0
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepBackupCb,
		Workflow:        WorkflowScsRefreshAutoBackupTask,
		StepProcess:     callback.ProcessRefreshBackupCb,
		SuccessNextStep: StepCheckAndClearExpireBackupRecord,
		ErrorNextStep:   StepBackupCb,
	})

	/*
		检查&清理过期的备份任务
	*/
	_ = workflow.AddStep(&workflow.AddStepParam{
		Name:            StepCheckAndClearExpireBackupRecord,
		Workflow:        WorkflowScsRefreshAutoBackupTask,
		StepProcess:     backup.ProcessCheckAndClearExpireBackupRecord,
		SuccessNextStep: workflow.FinalStepSuccess,
		ErrorNextStep:   StepCheckAndClearExpireBackupRecord,
	})
}
