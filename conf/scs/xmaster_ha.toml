DefaultMonitorRules = '[{"cond":"1==1","expr":"((time()-$lastActiveTime)>10) || ($instance_alive==0)","filter":"repeat_time()>=2","level":99,"mutexList":[],"name":"instance_alive","tag":{},"timeout":300},{"cond":"(exist(\"instance_ping_available\")==1)","expr":"($instance_ping_available==0)","filter":"repeat_time()>=24","level":99,"mutexList":[],"name":"instance_ping_available","tag":{},"timeout":300},{"cond":"(($role==\"slave\") && ((exist(\"info_role\")==1) && ($info_role!=\"\")) && (exist(\"master_ip\")==1))","expr":"(($info_role!=\"slave\") || ($master_ip!=$info_master_host) || ($master_port!=$info_master_port))","filter":"repeat_time()>=3","level":99,"mutexList":[],"name":"instance_topology_slave","tag":{},"timeout":300}]'
PegadbMonitorRules = '[{"cond":"1==1","expr":"((time()-$lastActiveTime)>10) || ($instance_alive==0)","filter":"repeat_time()>=2","level":99,"mutexList":[],"name":"instance_alive","tag":{},"timeout":300},{"cond":"(exist(\"instance_logic_available\")==1)","expr":"((time()-$lastLogicAvailTime)>30)&&((time()-$lastReportTime)<30)","filter":"","level":89,"mutexList":[],"name":"instance_logic_available","tag":{},"timeout":300},{"cond":"(exist(\"instance_ping_available\")==1)","expr":"($instance_ping_available==0)","filter":"repeat_time()>=24","level":99,"mutexList":[],"name":"instance_ping_available","tag":{},"timeout":300},{"cond":"(($role==\"slave\") && ((exist(\"info_role\")==1) && ($info_role!=\"\")) && (exist(\"master_ip\")==1))","expr":"(($info_role!=\"slave\") || ($master_ip!=$info_master_host) || ($master_port!=$info_master_port))","filter":"repeat_time()>=3","level":99,"mutexList":[],"name":"instance_topology_slave","tag":{},"timeout":300}]'
IdcName = "test"
XmasterEndpoint = "************:8900"

[IDC_SPEC.bjtest]
DefaultMonitorRules = '[{"cond":"1==1","expr":"((time()-$lastActiveTime)>10) || ($instance_alive==0)","filter":"repeat_time()>=2","level":99,"mutexList":[],"name":"instance_alive","tag":{},"timeout":300},{"cond":"(exist(\"instance_ping_available\")==1)","expr":"($instance_ping_available==0)","filter":"repeat_time()>=24","level":99,"mutexList":[],"name":"instance_ping_available","tag":{},"timeout":300},{"cond":"(($role==\"slave\") && ((exist(\"info_role\")==1) && ($info_role!=\"\")) && (exist(\"master_ip\")==1))","expr":"(($info_role!=\"slave\") || ($master_ip!=$info_master_host) || ($master_port!=$info_master_port))","filter":"repeat_time()>=3","level":99,"mutexList":[],"name":"instance_topology_slave","tag":{},"timeout":300}]'
PegadbMonitorRules = '[{"cond":"1==1","expr":"((time()-$lastActiveTime)>10) || ($instance_alive==0)","filter":"repeat_time()>=2","level":99,"mutexList":[],"name":"instance_alive","tag":{},"timeout":300},{"cond":"(exist(\"instance_logic_available\")==1)","expr":"((time()-$lastLogicAvailTime)>30)&&((time()-$lastReportTime)<30)","filter":"","level":89,"mutexList":[],"name":"instance_logic_available","tag":{},"timeout":300},{"cond":"(exist(\"instance_ping_available\")==1)","expr":"($instance_ping_available==0)","filter":"repeat_time()>=24","level":99,"mutexList":[],"name":"instance_ping_available","tag":{},"timeout":300},{"cond":"(($role==\"slave\") && ((exist(\"info_role\")==1) && ($info_role!=\"\")) && (exist(\"master_ip\")==1))","expr":"(($info_role!=\"slave\") || ($master_ip!=$info_master_host) || ($master_port!=$info_master_port))","filter":"repeat_time()>=3","level":99,"mutexList":[],"name":"instance_topology_slave","tag":{},"timeout":300}]'
IdcName = "bjtest"
XmasterEndpoint = "*************:7401"

[IDC_SPEC.dbstack]
IdcName = "dbstack"
XmasterEndpoint = "*************:8900"

[IDC_SPEC.onlineedge]
IdcName = "onlineedge"
XmasterEndpoint = "************:35016"

[IDC_SPEC.edgetest]
IdcName = "edgetest"
XmasterEndpoint = "************:35052"
