[Image]
IsolatedBcc = "1422ed20-82dd-4f4c-8996-9c0abc8c5b40"
UnIsolatedBcc = "be6b1606-9e7e-41e3-a700-553a9f1fb64e"

[PkgConf]
AgentBins = ["slot-redis", "redis", "PegaDB2", "proxy-slot", "nutcraker",
    "json-module", "cascad-module", "bloomfilter-module", "sync-agent", "sst_dump", "proxy", "sync-agent2"]
NeedExcutePkgs = ["agent", "csagent", "monitor-agent", "cron", "smartdba"]
CorePkgs = ["slot-redis", "redis", "PegaDB2", "proxy-slot", "nutcraker", "sync-agent", "proxy", "sync-agent2"]
