##
# 以下是各种package配置
#
[Packages]

# test
[Packages.test]
    ## name
    Name = "xcache"
    ## repo
    Repo = "default_http"
    ## default version
    DefaultVersion = "0.0.1"
    ## static env vars
    [Packages.test.StaticEnv]
        test_var = "test"

# xcache
[Packages.xcache]
    ## name
    Name = "scs-package"
    ## repo
    Repo = "package_bos"
    ## default version
    DefaultVersion = "20231011110632"
    ## static env vars
    [Packages.xcache.StaticEnv]
        #### agent
        [Packages.xcache.StaticEnv.agent]
            container_type = "kvm"
            csmaster_ip = "csmaster.xxx.com"
            csmaster_port = 80
            bcm_host = "bcm.xxx.com"
            bcm_port = 80
            accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
            secretkey = "8d310407bcd44b26919fe067ce73526a"
            iam_host = "iam.baidu.com"
            iam_port = 9090
            username = "username"
            password = "password"
            ###### backup bos info
            bos_endpoint = "http://bj-bos-sandbox.baidu-int.com"
            bos_bucket_name = "scs-backup-rdb-bucket"
            bos_ak = "74a500c63f6b4d0e9889813c74f03644"
            bos_sk = "dd00078db7844fd8bec69ea71787d906"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.test]
                csmaster_ip = "*************"
                csmaster_port = 8997
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.testnew]
                csmaster_ip = "*************"
                csmaster_port = 8763
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.clustertest]
                csmaster_ip = "*************"
                csmaster_port = 9657
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.preonline]
                csmaster_ip = "*************"
                csmaster_port = 8456
                bos_bucket_name = "scs-hkg-backup-rdb-bucket"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.minicloud]
                csmaster_ip = "*************"
                csmaster_port = 8456
                bcm_host = "**************"
                bcm_port = "8869"
                accesskey = "4386541c7b154d12a322912235a5a5b9"
                secretkey = "76214293c52641e59027b185dd7c8eb7"
                iam_host = "**************"
                iam_port = "35357"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.clustermini]
                csmaster_ip = "*************"
                csmaster_port = 9657
                bcm_host = "**************"
                bcm_port = "8869"
                accesskey = "4386541c7b154d12a322912235a5a5b9"
                secretkey = "76214293c52641e59027b185dd7c8eb7"
                iam_host = "**************"
                iam_port = "35357"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinebj]
                csmaster_ip = "scs.bj.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.bj.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.baidubce.com"
                iam_port = "80"
                bos_endpoint = "bj.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-bj"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinebd]
                csmaster_ip = "scs.bd.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.bd.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.bdbl.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "bd.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinesin]
                csmaster_ip = "scs.sin.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.sin.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.bdbl.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "sin.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-sin"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinebdfsg]
                csmaster_ip = "scs.hb-fsg.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.hb-fsg.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.fsg-bdbl.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "hb-fsg.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-fsg"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinebjfsg]
                csmaster_ip = "scs.bjfsg.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.bjfsg.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.bjfsg.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "bjfsg.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-bjfsg"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinefsh]
                csmaster_ip = "scs.fsh.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.fsh.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.fsh.bce-internal.baidu.com"
                iam_port = "80"
                bos_endpoint = "fsh.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-fsh"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinegz]
                csmaster_ip = "scs.gz.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.gz.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.gz.bce-internal.baidu.com"
                iam_port = "80"
                bos_endpoint = "gz.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-gz"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinehkg]
                csmaster_ip = "scs.hkg.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.hkg.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.hkg.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "hkg.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-hkg"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinesu]
                csmaster_ip = "scs.su.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.su.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.su.bce-internal.baidu.com"
                iam_port = "80"
                bos_endpoint = "su.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-su"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinesuvip]
                csmaster_ip = "scsvip01.su.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.su.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.su.bce-internal.baidu.com"
                iam_port = "80"
                bos_endpoint = "su.bcebos.com"
                bos_bucket_name = "su-vip01-backup"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinewh]
                csmaster_ip = "scs.fwh.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.fwh.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.fwh.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "fwh.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-whgg"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlineyq]
                csmaster_ip = "scs.yq.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.yq.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.yq.bce-internal.sdns.baidu.com"
                iam_port = "80"
                bos_endpoint = "yq.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-yq"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinecd]
                csmaster_ip = "scs.cd.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.cd.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.cd.bce-internal.sdns.baidu.com"
                iam_port = "80"
                bos_endpoint = "cd.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-cd"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlinenj]
                csmaster_ip = "scs.nj.baidubce.com"
                csmaster_port = 80
                bcm_host = "bcm.nj.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.nj.bce.baidu-int.com"
                iam_port = "80"
                bos_endpoint = "nj.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-nj"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.bjtest]
                csmaster_ip = "*************"
                csmaster_port = 9000
                bcm_host = "bcm.bj.baidubce.com"
                bcm_port = "80"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.baidubce.com"
                iam_port = "80"
                bos_endpoint = "bj.bcebos.com"
                bos_bucket_name = "scs-backup-rdb-bucket-bj"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
            [Packages.xcache.StaticEnv.agent.IDC_SPEC.onlineedge]
                csmaster_ip = "************"
                csmaster_port = 35014
                bcm_host = "************"
                bcm_port = "35007"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.su.bce-internal.baidu.com"
                iam_port = "80"
                bos_endpoint = "************:35004"
                bos_bucket_name = "scs-backup-rdb-bucket-edge"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"
        [Packages.xcache.StaticEnv.agent.IDC_SPEC.edgetest]
                # TODO 换成真正的edgetest地址
                csmaster_ip = "************"
                csmaster_port = 35014
                bcm_host = "************"
                bcm_port = "35007"
                accesskey = "11c27ada0ced4745845c1198b1a5f2a7"
                secretkey = "8d310407bcd44b26919fe067ce73526a"
                iam_host = "iam.su.bce-internal.baidu.com"
                iam_port = "80"
                bos_endpoint = "************:35004"
                bos_bucket_name = "scs-backup-rdb-bucket-edgetest"
                bos_ak = "ALTAKuFVfTw7QfT1PAGup31Uxq"
                bos_sk = "2a8269b4c0d04b729e949bb814dbe089"

        #### csagent
        [Packages.xcache.StaticEnv.csagent]
            csmonitor_ip = "*************"
            csmonitor_port = 8002
            connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.minicloud]
                csmonitor_ip = "*************"
                csmonitor_port = 8767
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinebj]
                csmonitor_ip = "scsmonitor.bj.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinebd]
                csmonitor_ip = "scsmonitor.bd.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinesin]
                csmonitor_ip = "scsmonitor.sin.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinebdfsg]
                csmonitor_ip = "scsmonitor.hb-fsg.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinebjfsg]
                csmonitor_ip = "scsmonitor.bjfsg.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinefsh]
                csmonitor_ip = "scsmonitor.fsh.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinegz]
                csmonitor_ip = "scsmonitor.gz.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinehkg]
                csmonitor_ip = "scsmonitor.hkg.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinesu]
                csmonitor_ip = "scsmonitor.su.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinesuvip]
                csmonitor_ip = "scsmonitorvip01.su.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinewh]
                csmonitor_ip = "scsmonitor.fwh.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlineyq]
                csmonitor_ip = "scsmonitor.yq.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinecd]
                csmonitor_ip = "scsmonitor.cd.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.onlinenj]
                csmonitor_ip = "scsmonitor.nj.baidubce.com"
                csmonitor_port = 8080
                connection_timeout_ms = 10000
            [Packages.xcache.StaticEnv.csagent.IDC_SPEC.bjtest]
                csmonitor_ip = "*************"
                csmonitor_port = 8080
                connection_timeout_ms = 10000

        #### cron
        [Packages.xcache.StaticEnv.cron]
            bos_host = "bj-bos-sandbox.baidu-int.com"
            bucket_name = "scs-log-download"
            ak_encrypt = "43btIj0f4maOyZ4cgQJMR7Ic8+NTnSgutjR2DQjvftqyYy31PeVB46Nq9sDErZMi"
            sk_encrypt = "Mf6c9f7SaV8/YjqAP0Uca3F8o7DivaJA/TNgsR2r7SSRmjQ9mI5+AbHBNN1xnozR"
            log_transfer_addr = "http://gzhxy-y32-sandbox018.gzhxy.baidu.com"
            log_transfer_port = 8279
            log_transfer_url = "/transfer/slowlog"
            hotkey_transfer_url = "/transfer/hotkey"
            token = "paastest123"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinebj]
                bos_host = "bj.bcebos.com"
                bucket_name = "scs-log-download-bj"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.bj.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-bj"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinebd]
                bos_host = "bd.bcebos.com"
                bucket_name = "scs-log-download-bdbl"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.bdbl.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-bdbl"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinesin]
                bos_host = "sin.bcebos.com"
                bucket_name = "scs-log-download-sin"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://*************"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinebdfsg]
                bos_host = "hb-fsg.bcebos.com"
                bucket_name = "scs-log-download-bdfsg"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://*************"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinebjfsg]
                bos_host = "bjfsg.bcebos.com"
                bucket_name = "scs-log-download-bjfsg"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://*************"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinefsh]
                bos_host = "fsh.bcebos.com"
                bucket_name = "scs-log-download-fsh"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.fsh.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinegz]
                bos_host = "gz.bcebos.com"
                bucket_name = "scs-log-download-gz"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
             sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.gz.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-gz"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinehkg]
                bos_host = "hkg.bcebos.com"
                bucket_name = "scs-log-download-hkg"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.hkg.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-hkg"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinesu]
                bos_host = "su.bcebos.com"
                bucket_name = "scs-log-download-su"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.su.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-su"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinesuvip]
                bos_host = "su.bcebos.com"
                bucket_name = "scs-log-download-su"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.suvip.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-su"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinewh]
                bos_host = "fwh.bcebos.com"
                bucket_name = "scs-log-download-whgg"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.whgg.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-whgg"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlineyq]
                bos_host = "yq.bcebos.com"
                bucket_name = "scs-log-download-yq"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.yq.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-yq"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinecd]
                bos_host = "cd.bcebos.com"
                bucket_name = "scs-log-download-cd"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.cd.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-cd"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.onlinenj]
                bos_host = "nj.bcebos.com"
                bucket_name = "scs-log-download-nj"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://scslogtrans.nj.baidubce.com"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-nj"
                custom_lifecycle_prefix = "one-year"
            [Packages.xcache.StaticEnv.cron.IDC_SPEC.bjtest]
                bos_host = "bj.bcebos.com"
                bucket_name = "scs-log-download-bj"
                ak_encrypt = "c0JLVuPhYeHniDJo6MHgQCCZQG4uxWq399UifAh+qRw="
                sk_encrypt = "VGXm/I9kHVlP0C5MOo9InVoApM2/uPoa97qen3ozc0e3+LqiN1UUjF5RAQQtEaEO"
                log_transfer_addr = "http://*************"
                log_transfer_port = 8279
                log_transfer_url = "/transfer/slowlog"
                hotkey_transfer_url = "/transfer/hotkey"
                token = "mi6cfyTpGNk2R6"
                custom_lifecycle_bucket_name = "scs-custom-lifecycle-log-download-bj"
                custom_lifecycle_prefix = "one-year"

