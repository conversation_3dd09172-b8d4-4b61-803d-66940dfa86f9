BosAK = "91884b9e79a442f29f8b41106d060e72"
BosSK = "c9379def1b3f4a0ea9dbaca23c74c55b"



[IDC_SPEC.onlinebd]
BosEndpoint = "bd.bcebos.com"
BosBucket = "scs-package-manager-onlinebd"
[IDC_SPEC.onlinebj]
BosEndpoint = "bj.bcebos.com"
BosBucket = "scs-package-manager-onlinebj"
[IDC_SPEC.onlinesin]
BosEndpoint = "sin.bcebos.com"
BosBucket = "scs-package-manager-onlinesin"
[IDC_SPEC.onlinebdfsg]
BosEndpoint = "hb-fsg.bcebos.com"
BosBucket = "scs-package-manager-onlinebdfsg"
[IDC_SPEC.onlinebjfsg]
BosEndpoint = "bjfsg.bcebos.com"
BosBucket = "scs-package-manager-onlinebjfsg"
[IDC_SPEC.onlinefsh]
BosEndpoint = "fsh.bcebos.com"
BosBucket = "scs-package-manager-onlinefsh"
[IDC_SPEC.onlinegz]
BosEndpoint = "gz.bcebos.com"
BosBucket = "scs-package-manager-onlinegz"
[IDC_SPEC.onlinehkg]
BosEndpoint = "hkg.bcebos.com"
BosBucket = "scs-package-manager-onlinehkg"
[IDC_SPEC.onlinesu]
BosEndpoint = "su.bcebos.com"
BosBucket = "scs-package-manager-onlinesu"
[IDC_SPEC.onlinesuvip]
BosEndpoint = "su.bcebos.com"
BosBucket = "scs-package-manager-onlinesuvip"
[IDC_SPEC.onlinewh]
BosEndpoint = "fwh.bcebos.com"
BosBucket = "scs-package-manager-onlinewh"
[IDC_SPEC.onlineyq]
BosEndpoint = "yq.bcebos.com"
BosBucket = "scs-package-manager-onlineyq"
[IDC_SPEC.onlinecd]
BosEndpoint = "cd.bcebos.com"
BosBucket = "scs-package-manager-onlinecd"
[IDC_SPEC.onlinenj]
BosEndpoint = "nj.bcebos.com"
BosBucket = "scs-package-manager-onlinenj"
[IDC_SPEC.bjtest]
BosEndpoint = "bj.bcebos.com"
BosBucket = "scs-package-manager-bjtest"
# 理想aws私有化，都用香港的
[IDC_SPEC.licloudontest]
BosEndpoint = "hkg.bcebos.com"
BosBucket = "scs-package-manager-onlinehkg"
[IDC_SPEC.licloudprod]
BosEndpoint = "hkg.bcebos.com"
BosBucket = "scs-package-manager-onlinehkg"
# dbstack使用http的文件服务器, 并不使用bos
[IDC_SPEC.dbstack]
BosEndpoint = "http://*************:8060/scs/package"
BosBucket = "fake-bucket"
[IDC_SPEC.edgetest]
BosEndpoint = "************:35004"
BosBucket = "scs-package-manager-onlinesu"
[IDC_SPEC.onlineedge]
BosEndpoint = "************:35004"
BosBucket = "scs-package-manager-onlinesu"