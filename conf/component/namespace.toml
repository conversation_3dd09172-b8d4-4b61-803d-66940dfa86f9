[SstDownloadConf]
DownloadParentDir = "/mnt/download_sst"
DiskUsagePercentThreshold = 90
# 40 * 1024 * 1024 * 1024
DiskFreeBytesThreshold = 21474836480
TrafficLimitMB = 100
# 请求xagent单次下载最大slot数量
MaxSlotNum = 16
# 请求xagent单次下载最大文件size
MaxSstFileSize = 1048576000
[SstCleanupConf]
Step = 10
[OperationConf]
ReplicationOffset = 100000
ConnectRetry = 2
ConnectTimeout = 500
ReadTimeout = 5000
WriteTimeout = 5000
[BosConf.IDC_SPEC.onlinebd]
BosEndpoint = "bd.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinebj]
BosEndpoint = "bj.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinesin]
BosEndpoint = "sin.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinebdfsg]
BosEndpoint = "hb-fsg.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinebjfsg]
BosEndpoint = "bjfsg.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinefsh]
BosEndpoint = "fsh.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinegz]
BosEndpoint = "gz.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinehkg]
BosEndpoint = "hkg.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinesu]
BosEndpoint = "su.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinesuvip]
BosEndpoint = "su.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinewh]
BosEndpoint = "fwh.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlineyq]
BosEndpoint = "yq.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinecd]
BosEndpoint = "cd.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.onlinenj]
BosEndpoint = "nj.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.bjtest]
BosEndpoint = "bj.bcebos.com"
BosListMaxKeys = 1000
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[BosConf.IDC_SPEC.test]
BosEndpoint = "bj-bos-sandbox.baidu-int.com"
BosListMaxKeys = 2
CheckCRC32 = false
CheckMD5 = true
PresignedUrlExpiration = 10800
[S3Conf.IDC_SPEC.test]
S3Endpoint = "10.151.238.13:8000"
S3ListMaxKeys = 1
CheckCRC32 = true
PresignedUrlExpiration = 10800
Accesskey="6odOToa0L2QS7DCy"
Secretkey="4Wh3Ik4s3kZaYJIrOvKN6VRWixwpJOro"
UseSSL=false
[S3Conf.IDC_SPEC.dbstack]
S3Endpoint = "10.151.238.13:8000"
S3ListMaxKeys = 1
CheckCRC32 = true
PresignedUrlExpiration = 10800
Accesskey="6odOToa0L2QS7DCy"
Secretkey="4Wh3Ik4s3kZaYJIrOvKN6VRWixwpJOro"
UseSSL=false
[[MirrorBosConf]]
BosRegion = "bj"
ScsRegion = "bj"
BosEndpoint = "bj.bcebos.com"
[[MirrorBosConf]]
BosRegion = "bd"
ScsRegion = "bd"
BosEndpoint = "bd.bcebos.com"
[[MirrorBosConf]]
BosRegion = "su"
ScsRegion = "su"
BosEndpoint = "su.bcebos.com"
[[MirrorBosConf]]
BosRegion = "gz"
ScsRegion = "gz"
BosEndpoint = "gz.bcebos.com"
[[MirrorBosConf]]
BosRegion = "cd"
ScsRegion = "cd"
BosEndpoint = "cd.bcebos.com"
[[MirrorBosConf]]
BosRegion = "hkg"
ScsRegion = "hkg"
BosEndpoint = "hkg.bcebos.com"
[[MirrorBosConf]]
BosRegion = "fwh"
ScsRegion = "wh"
BosEndpoint = "fwh.bcebos.com"
[[MirrorBosConf]]
BosRegion = "fsh"
ScsRegion = "fsh"
BosEndpoint = "fsh.bcebos.com"
[[MirrorBosConf]]
BosRegion = "yq"
ScsRegion = "yq"
BosEndpoint = "yq.bcebos.com"
[[MirrorBosConf]]
BosRegion = "nj"
ScsRegion = "nj"
BosEndpoint = "nj.bcebos.com"