GServiceName = "scs"
DefaultSgIpList = ["*************","10.0.0.0/8","127.0.0.1"]
DefaultVmPort = 8181
[IDC_SPEC.minicloud]
    DefaultSgIpList = ["*************","10.0.0.0/8","127.0.0.1"]

[IDC_SPEC.onlinebj]
    DefaultSgIpList = ["**********/21", "***********", "************", "***********", "************", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "***********", "***********", "***********", "***********"]

[IDC_SPEC.onlinebd]
    DefaultSgIpList = ["**********", "**********", "**********", "**********", "***********", "***********", "***********", "***********"]

[IDC_SPEC.onlinesin]
    DefaultSgIpList = ["**********/21", "**********", "**********"]

[IDC_SPEC.preonline]
    DefaultSgIpList = ["*************", "*************", "*************", "10.0.0.0/8","127.0.0.1"]

[IDC_SPEC.onlinebdfsg]
    DefaultSgIpList = ["**********", "**********", "**********", "**********", "***********", "***********", "***********", "***********", "************", "************", "*************"]

[IDC_SPEC.onlinebjfsg]
    DefaultSgIpList = ["**********/21", "**********", "**********", "**********", "**********", "**********", "**********", "************", "************", "*************"]

[IDC_SPEC.onlinefsh]
    DefaultSgIpList = ["**********", "**********", "***********", "***********", "***********", "***********", "**********6", "**********7", "************", "************", "*************"]

[IDC_SPEC.onlinegz]
    DefaultSgIpList = ["**********/21", "**********9", "**********0", "**********1", "**********2", "**********3", "**********9", "**********0", "**********1", "**********2", "************", "************", "*************"]

[IDC_SPEC.onlinehkg]
    DefaultSgIpList = ["**********/21", "***********", "**********", "**********", "**********", "**********8", "**********9", "************", "************", "*************"]

[IDC_SPEC.onlinesu]
    DefaultSgIpList = ["**********/21", "**********", "**********", "**********0", "**********1", "***********", "***********", "***********", "***********", "**********6", "**********5", "**********6", "************", "************", "*************"]

[IDC_SPEC.onlinesuvip]
    DefaultSgIpList = ["**********/21", "************", "************", "*************"]

[IDC_SPEC.onlinewh]
    DefaultSgIpList = ["**********", "**********", "***********", "***********", "***********", "***********", "**********6", "**********7", "************", "************", "*************"]

[IDC_SPEC.bjtest]
    DefaultSgIpList = ["**********/21", "**********93"]

[IDC_SPEC.onlineyq]
    DefaultSgIpList = ["**********/21", "10.215.215.16", "10.215.215.79", "10.215.215.147", "10.215.215.206"]

[IDC_SPEC.onlinecd]
    DefaultSgIpList = ["**********/21", "10.55.0.35", "10.55.0.120"]

[IDC_SPEC.onlinenj]
    DefaultSgIpList = ["**********/21", "10.68.46.95", "10.68.56.26", "10.68.13.24", "10.95.10.159"]

[IDC_SPEC.onlineedge]
DefaultSgIpList = ["**********/21", "100.64.224.0/19", "100.79.10.253", "100.79.10.252", "**********", "**********", "**********0", "**********1", "***********", "***********", "***********", "***********", "**********6", "**********5", "**********6", "************", "************", "*************"]

[IDC_SPEC.edgetest]
DefaultSgIpList = ["**********/21", "100.64.224.0/19", "100.79.10.253", "100.79.10.252", "**********", "**********", "**********0", "**********1", "***********", "***********", "***********", "***********", "**********6", "**********5", "**********6", "************", "************", "*************"]

