# DeploySetTagPrefix is the prefix for the deployment set tag
DeploySetTagPrefix = "a="

# SpecResourceConfig defines the configuration for resource specifications
# Container IOps Read Limit = ${DataDiskCapacityInGB} / ${DeviceCapacityInGB} * ${DeviceReadIOPS} * ${DeviceReadIOPSLimitRate}/1000
# Container IOps Write Limit = ${DataDiskCapacityInGB} / ${DeviceCapacityInGB} * ${DeviceWriteIOPS} * ${DeviceWriteIOPSLimitRate}/1000
# Container BPS Read Limit = ${DataDiskCapacityInGB} / ${DeviceCapacityInGB} * ${DeviceReadIOBPS} * ${DeviceReadIOBPSLimitRate}/1000
# Container BPS Write Limit = ${DataDiskCapacityInGB} / ${DeviceCapacityInGB} * ${DeviceWriteIOBPS} * ${DeviceWriteIOBPSLimitRate}/1000
[[SpecResourceConfig]]
AZone = "*"
Spec = "*"
ResourceTag = "disk_type=cds_share"
DeviceCapacityInGB = 500
DeviceWriteIOPS = 0
DeviceReadIOPS = 0
DeviceWriteIOBPS = 0
DeviceReadIOBPS = 0
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.g4s1.micro"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.g4s1.small"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.l5ds1.micro"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.l5ds1.small"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.l5ds1.medium"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.l5ds1.large"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000

[[SpecResourceConfig]]
AZone = "*"
Spec = "pega.l5ds1.xlarge"
ResourceTag = "disk_type=ssd"
DeviceCapacityInGB = 3500
DeviceWriteIOPS = 1500000
DeviceReadIOPS = 1500000
DeviceWriteIOBPS = 7000000000
DeviceReadIOBPS = 7000000000
DeviceWriteIOPSLimitRate = 1000
DeviceReadIOPSLimitRate = 1000
DeviceWriteIOBPSLimitRate = 1000
DeviceReadIOBPSLimitRate = 1000