# Service的名字，必选，需自定义修改
Name = "csmaster-model"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，ms
# ConnTimeOut = 200
# 写数据超时，ms
# WriteTimeOut = 200
# 读数据超时，ms
# ReadTimeOut = 1000

[Strategy]
# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "*************"
        Port = 3306
    [[Resource.Manual.onlinebj]]
        Host = "************"
        Port = 5366
    [[Resource.Manual.onlinebd]]
        Host = "************"
        Port = 5365
    [[Resource.Manual.onlinesin]]
        Host = "*********"
        Port = 5018
    [[Resource.Manual.onlinebdfsg]]
        Host = "***********"
        Port = 5010
    [[Resource.Manual.onlinebjfsg]]
        Host = "***********"
        Port = 5025
    [[Resource.Manual.onlinefsh]]
        Host = "*************"
        Port = 6077
    [[Resource.Manual.onlinegz]]
        Host = "************"
        Port = 5360
    [[Resource.Manual.onlinehkg]]
        Host = "*************"
        Port = 5208
    [[Resource.Manual.onlinesu]]
        Host = "************"
        Port = 5363
    [[Resource.Manual.onlinesuvip]]
        Host = "*************"
        Port = 6614
    [[Resource.Manual.onlinewh]]
        Host = "************"
        Port = 5364
    [[Resource.Manual.onlineyq]]
        Host = "smartbns.bcescsyq-bmi0000.xdb.all.serv"
        Port = 5101
    [[Resource.Manual.onlinecd]]
        Host = "*************"
        Port = 10768
    [[Resource.Manual.onlinenj]]
        Host = "smartbns.bcescsnj-bmi0000.xdb.all.serv"
        Port = 5209
    [[Resource.Manual.preonline]]
        Host = "*************"
        Port = 3306
    [[Resource.Manual.bjtest]]
        Host = "bcescsbjtest-bmi0000.xdb.all.serv"
        Port = 5046
    [[Resource.Manual.licloudontest]]
        Host = "127.0.0.1"
        Port = 3306
    [[Resource.Manual.licloudprod]]
        Host = "***********"
        Port = 3306
    [[Resource.Manual.dbstack]]
        Host = "127.0.0.1"
        Port = 6203
    [[Resource.Manual.edgetest]]
        Host = "bcescsbjtest-bmi0000.xdb.all.serv"
        Port = 5046
    [[Resource.Manual.onlineedge]]
        Host = "************"
        Port = 5363
# mysql 特有的配置，可选，需自定义修改
[MySQL]
Username    = "root"
Password    = "paastest123"
DBName      = "bce_scs_refactor_mini"
DBDriver    = "mysql"
# MaxOpenPerIP 每个 ip 最多连接数，若过小，会出现查询排队等待的情况
# 若为0-则不限制
MaxOpenPerIP= 50
# MaxIdlePerIP 每个 ip 最多连接空闲数，最大可以设置成 和 MaxOpenPerIP 一样
# 若为0-使用 sql 标准库的默认值2
MaxIdlePerIP= 50
# ConnMaxLifeTime 连接复用最长时间，单位 ms
# 若为0-则不限制，连接不会关闭
# 可以根据线上实际情况，设置一个比较大的合适的时间，以减少创建新连接带来的不稳定性
ConnMaxLifeTime = 5000
# 是否sql注释传递logid
LogIDTransport = true
# 若是 DDBS 或者 数据库不支持prepare，DSNParams 需配置上 interpolateParams=true
DSNParams ="charset=utf8&timeout=90s&collation=utf8mb4_unicode_ci&parseTime=true"

[MySQL.IDC_SPEC.clustertest]
DBName   = "csmaster_cluster_test"
[MySQL.IDC_SPEC.onlinebj]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "3gj2OxM1OrfoR1bm"
[MySQL.IDC_SPEC.onlinebd]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "IlUcU4hjCtbyOqOV4"
[MySQL.IDC_SPEC.onlinesin]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "7rI_xxY6tkQJUGR5h"
[MySQL.IDC_SPEC.onlinebdfsg]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "W776WlGhH8UOI1ZZA"
[MySQL.IDC_SPEC.onlinebjfsg]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "2cB_uvl9awDkLm8VM"
[MySQL.IDC_SPEC.onlinefsh]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "^t$SHzjzpsas8Hse"
[MySQL.IDC_SPEC.onlinegz]
DBName   = "bce_scs"
Username = "bce_scs_gzns_w"
Password = "PVrvns97CC7iQtKy"
[MySQL.IDC_SPEC.onlinehkg]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "n8qWJ1N2kr6Z8pxa"
[MySQL.IDC_SPEC.onlinesu]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "f_UJYZzr5du4iOsj"
[MySQL.IDC_SPEC.onlinesuvip]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "F9Jc*wkiNkCQd"
[MySQL.IDC_SPEC.onlinewh]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "9SOpn3OBCngO"
[MySQL.IDC_SPEC.onlineyq]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "x_n+6ga@lcxDoh"
[MySQL.IDC_SPEC.onlinecd]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "_W=wTt_jT:,ahs"
[MySQL.IDC_SPEC.onlinenj]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "shg6hd!bQoM"
[MySQL.IDC_SPEC.preonline]
DBName   = "bce_scs_sandbox_preonline"
[MySQL.IDC_SPEC.bjtest]
DBName   = "bce_scs"
Username = "bce_scs_w"
Password = "vgr,axvdgqd_!Ik"
[MySQL.IDC_SPEC.licloudontest]
DBName   = "bce_scs"
Username = "root"
Password = "paastest@123"
[MySQL.IDC_SPEC.licloudprod]
DBName   = "bce_scs"
Username = "root"
Password = "ZSt5o85R9kW+qOJ58jNF"
[MySQL.IDC_SPEC.dbstack]
DBName   = "bce_scs"
Username = "paas_x1_w"
Password = "paas_x1_w_pwd"
DSNParams ="charset=utf8&timeout=90s&collation=utf8mb4_unicode_ci&parseTime=true&interpolateParams=true"
[MySQL.IDC_SPEC.edgetest]
DBName   = "bce_scs_edge"
Username = "bce_scs_w"
Password = "vgr,axvdgqd_!Ik"
[MySQL.IDC_SPEC.onlineedge]
DBName   = "bce_scs_edge"
Username = "bce_scs_w"
Password = "f_UJYZzr5du4iOsj"