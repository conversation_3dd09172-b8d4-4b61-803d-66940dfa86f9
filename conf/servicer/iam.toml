# Service的名字，必选，需自定义修改
Name = "iam"

# 连接超时，单位 ms,默认5000
ConnTimeOut = 5000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 10000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 10000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 2

# 资源定位
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "iam.bj.internal-qasandbox.baidu-int.com"
        Port = 80
    [[Resource.Manual.minicloud]]
        Host = "**************"
        Port = 35357
    [[Resource.Manual.onlinebj]]
        Host = "iam.bj.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinebd]]
        Host = "iam.bdbl.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinesin]]
        Host = "iam.sin.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinebdfsg]]
        Host = "iam.fsg-bdbl.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinebjfsg]]
        Host = "iam.bjfsg.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinefsh]]
        Host = "iam.fsh.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlineglobalmaster]]
        Host = "iam.su.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinegz]]
        Host = "iam.gz.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinehkg]]
        Host = "iam.hkg.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinesu]]
        Host = "iam.su.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlinesuvip]]
        Host = "iam.su.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlineszfsg]]
        Host = "iam.szfsg.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlinewh]]
        Host = "iam.fwh.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlineyq]]
        Host = "iam.yq.bce-internal.sdns.baidu.com"
        Port = 80
    [[Resource.Manual.onlinecd]]
        Host = "iam.cd.bce-internal.sdns.baidu.com"
        Port = 80
    [[Resource.Manual.onlinenj]]
        Host = "iam.nj.bce.baidu-int.com"
        Port = 80
    [[Resource.Manual.preonline]]
        Host = "iam.hkg.internal-qasandbox.baidu-int.com"
        Port = 80
    [[Resource.Manual.bjtest]]
        Host = "iam.bj.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.edgetest]]
        Host = "iam.su.bce-internal.baidu.com"
        Port = 80
    [[Resource.Manual.onlineedge]]
        Host = "iam.su.bce-internal.baidu.com"
        Port = 80
# 其他专有配置，如mysql、redis等都有专有配置
[IAM]
    Protocol = "http"
    UserName = "scs"
    Password = "6rNIRB1msOdpvly3C2XrnD3xDeLZ1Svw"
    Domain = "default"
    Product = "scs"

    [IAM.IDC_SPEC.test]
        Password = "scs"
