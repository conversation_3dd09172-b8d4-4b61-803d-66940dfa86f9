# Service的名字，必选，需自定义修改
Name = "elb-v2"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 1000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 5000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 120000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 3

[Strategy]
## 资源使用策略，非必选，默认使用 RoundRobin
## RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "blb.bj.qasandbox.baidu-int.com"
        Port = 80
    [[Resource.Manual.minicloud]]
        Host = "**************"
        Port = 8087
    [[Resource.Manual.onlinebj]]
        Host = "blb.bj.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinebd]]
        Host = "blb.bd.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinesin]]
        Host = "blb.sin.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinebdfsg]]
        Host = "blb.hb-fsg.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinebjfsg]]
        Host = "blb.bjfsg.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinefsh]]
        Host = "blb.fsh.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinegz]]
        Host = "blb.gz.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinehkg]]
        Host = "blb.hkg.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinesu]]
        Host = "blb.su.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinesuvip]]
        Host = "blb.su.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinewh]]
        Host = "blb.fwh.baidubce.com"
        Port = 80
    [[Resource.Manual.onlineyq]]
        Host = "blb.yq.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinecd]]
        Host = "blb.cd.baidubce.com"
        Port = 80
    [[Resource.Manual.onlinenj]]
        Host = "blb.nj.baidubce.com"
        Port = 80
    [[Resource.Manual.preonline]]
        Host = "blb.internal-qapreonline.baidu-int.com"
        Port = 80
    [[Resource.Manual.bjtest]]
        Host = "blb.bj.baidubce.com"
        Port = 80
    [[Resource.Manual.edgetest]]
        Host = "127.0.0.1"
        Port = 8659
    [[Resource.Manual.onlineedge]]
        Host = "************"
        Port = 8659

# 其他专有配置，如mysql、redis等都有专有配置
[BLB]
    ResourceID = "ea2c4a2286ca4540afcb7f7d4ba2d199"
    EncryptKey = "wKbDYCYTKOjtDXEc"
    Product = "scs"
    [BLB.IDC_SPEC.onlinebj]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinebd]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinesin]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinebdfsg]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinebjfsg]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinefsh]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinegz]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinehkg]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinesu]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinesuvip]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinewh]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlineyq]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinecd]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlinenj]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.bjtest]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.edgetest]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
    [BLB.IDC_SPEC.onlineedge]
        ResourceID = "a1fc078b28b04a29a9f315c23117b5ed"
