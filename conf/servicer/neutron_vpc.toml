# Service的名字，必选，需自定义修改
Name = "neutron_vpc"

# 各种自定义的参数，会以 Option 的方式放到 Servicer 中，全部非必选
# 若使用bns，超时参数不需要配置，若配置会覆盖 BNS 配置的值
# 连接超时，单位 ms,默认5000
ConnTimeOut = 5000
# 写数据超时，单位 ms,默认5000
WriteTimeOut = 15000
# 读数据超时，单位 ms,默认5000
ReadTimeOut = 15000

# 可选，请求失败后的重试次数：总请求次数 = Retry + 1,默认0
Retry = 2

[Strategy]
    ## 资源使用策略，非必选，默认使用 RoundRobin
    ## RoundRobin-依次轮询 Random-随机 LocalityAware-la加权轮询，需要策略配置，
    Name="RoundRobin"

# 资源定位配置必须有且只有一项
# 资源定位：使用 BNS
# [Resource.BNS]
# BNSName = "group.test-service.all"
# PortKey = "pbrpc"
# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
    [[Resource.Manual.default]]
        Host = "**************"
        Port = 9696
    [[Resource.Manual.minicloud]]
        Host = "**************"
        Port = 9696
    [[Resource.Manual.onlinebj]]
        Host = "bccproxy.bj.bce-internal.baidu.com"
        Port = 19696
    [[Resource.Manual.onlinebd]]
        Host = "neutron.bdbl.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.onlinesin]]
        Host = "bccproxy.sin.bce.baidu-int.com"
        Port = 19696
    [[Resource.Manual.onlinebdfsg]]
        Host = "bccproxy.fsg-bdbl.bce.baidu-int.com"
        Port = 19696
    [[Resource.Manual.onlinebjfsg]]
        Host = "bccproxy.bjfsg.bce.baidu-int.com"
        Port = 19696
    [[Resource.Manual.onlinefsh]]
        Host = "neutron.fsh.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.onlinegz]]
        Host = "neutron-a.gz.bce-internal.baidu.com"
        Port = 9696
    [[Resource.Manual.onlinehkg]]
        Host = "neutron.hkg.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.onlinesu]]
        Host = "neutron.su.bce-internal.baidu.com"
        Port = 9696
    [[Resource.Manual.onlinesuvip]]
        Host = "neutron.su.bce-internal.baidu.com"
        Port = 9696
    [[Resource.Manual.onlinewh]]
        Host = "neutron.fwh.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.onlineyq]]
        Host = "neutron.yq.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.onlinecd]]
        Host = "neutron.cd.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.onlinenj]]
        Host = "neutron.nj.bce.baidu-int.com"
        Port = 9696
    [[Resource.Manual.preonline]]
        Host = "**************"
        Port = 9696
    [[Resource.Manual.bjtest]]
        Host = "bccproxy.bj.bce-internal.baidu.com"
        Port = 19696
    [[Resource.Manual.edgetest]]
        Host = "bec-neutron-proxy-server.baidu-int.com"
        Port = 80
    [[Resource.Manual.onlineedge]]
        Host = "bec-neutron-proxy-server.baidu-int.com"
        Port = 80

# 其他专有配置，如mysql、redis等都有专有配置
[Endpoint]
    Product = "scs"
