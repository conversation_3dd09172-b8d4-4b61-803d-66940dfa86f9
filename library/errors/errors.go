package errors

import "icode.baidu.com/baidu/scs/x1-base/common/cerrs"

var (
	CreateBlbFail                  = cerrs.NewConst(cerrs.CODE_USER_BASE+1, "create blb fail", true, nil)
	DeleteBlbFail                  = cerrs.NewConst(cerrs.CODE_USER_BASE+2, "delete blb fail", true, nil)
	CreateListenerFail             = cerrs.NewConst(cerrs.CODE_USER_BASE+3, "create listener fail", true, nil)
	BindRsFail                     = cerrs.NewConst(cerrs.CODE_USER_BASE+4, "bind rs fail", true, nil)
	UnBindRsFail                   = cerrs.NewConst(cerrs.CODE_USER_BASE+5, "unbind rs fail", true, nil)
	UpdateCsmasterClusterModelFail = cerrs.NewConst(cerrs.CODE_USER_BASE+6, "update csmaster cluster model fail", true, nil)
	UpdateCsmasterAclStatusFail    = cerrs.NewConst(cerrs.CODE_USER_BASE+7, "update csmaster acl status fail", true, nil)
	UpdateCsmasterBackupStatusFail = cerrs.NewConst(cerrs.CODE_USER_BASE+8, "update csmaster backup status fail", true, nil)
	QueryCsmasterClusterFail       = cerrs.NewConst(cerrs.CODE_USER_BASE+9, "query csmaster cluster fail", true, nil)
	UnbindEipFail                  = cerrs.NewConst(cerrs.CODE_USER_BASE+10, "unbind eip fail", true, nil)
	BackupInProgress               = cerrs.NewConst(cerrs.CODE_USER_BASE+11, "backup in progress", false, nil)
	XmasterQueryFail               = cerrs.NewConst(cerrs.CODE_USER_BASE+12, "xmaster query fail", false, nil)
	XmasterOpFail                  = cerrs.NewConst(cerrs.CODE_USER_BASE+13, "xmaster op fail", true, nil)
)
