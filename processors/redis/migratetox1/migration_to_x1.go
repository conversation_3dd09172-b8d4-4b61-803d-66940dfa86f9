package migratetox1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func createApp(ctx context.Context, params *iface.Parameters, clusterModel *csmaster.CsmasterCluster) (*x1model.Application, error) {
	var clusterType string
	switch clusterModel.ClusterType {
	case "master_slave":
		clusterType = x1model.AppTypeStandalone
	case "cluster":
		clusterType = x1model.AppTypeCluster
	default:
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("unsupported cluster type %s", clusterModel.ClusterType))
		return nil, fmt.Errorf("unsupported cluster type %s", clusterModel.ClusterType)
	}
	createTime, err := time.ParseInLocation("2006-01-02 15:04:05", clusterModel.CreateTime, time.UTC)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
		return nil, err
	}
	app := &x1model.Application{
		AppId:                   clusterModel.ClusterShowId,
		AppName:                 clusterModel.ClusterName,
		Product:                 x1model.ProductSCS,
		Type:                    clusterType,
		Port:                    params.ServicePortList[0],
		McpackPort:              params.ServicePortList[1],
		InnerPort:               params.ServicePortList[2],
		Azone:                   "",
		Rzone:                   "",
		VpcId:                   clusterModel.VpcId,
		UserId:                  params.UserID,
		Status:                  x1model.AppStatusInUse,
		Replicas:                base_utils.Format(params.Replicas),
		CreateTime:              createTime,
		UpdateTime:              time.Now(),
		SecurityGroupId:         clusterModel.SecurityGroupId,
		InternalSecurityGroupId: clusterModel.ShardSecurityGroupId,
		AppShortID:              params.ClusterShortID,
		UserShortID:             params.UserShortID,
		ImageID:                 bccresource.GetBccImageId(),
		DeploySetIds:            clusterModel.DeployIdList,
		Domain:                  clusterModel.Domain,
		LocalMetaserver:         params.MetaserverID,
		Region:                  params.Region,
		IpType:                  x1model.Ipv4,
	}
	return app, nil
}

type CsmasterShardInstancesList []*CsmasterShardInstances

type CsmasterShardInstances struct {
	ShardId   int64
	Instances []*csmaster.CsmasterInstance
}

func (c CsmasterShardInstancesList) Len() int {
	return len(c)
}

func (c CsmasterShardInstancesList) Less(i, j int) bool {
	return c[i].ShardId < c[j].ShardId
}

func (c CsmasterShardInstancesList) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}

func createClusters(ctx context.Context, params *iface.Parameters, app *x1model.Application, insts []*csmaster.CsmasterInstance, zoneMapper zone.ZoneMapperFunc) error {
	cmSIList := getCmSIList(insts)
	maxNodeId := 0
	clusterIdx := 0
	for _, cmSI := range cmSIList {
		if cmSI.Instances[0].CacheInstanceType == 0 {
			continue
		}
		clusterPort := params.ServicePortList[0]
		if params.ClusterType == "cluster" {
			clusterPort = params.ServicePortList[2]
		}
		cluster := &x1model.Cluster{
			ClusterId:      app.AppId + "-" + fmt.Sprintf("%d", clusterIdx),
			AppId:          app.AppId,
			Engine:         params.Engine,
			EngineVersion:  params.EngineVersion,
			Status:         x1model.ClusterStatusInUse,
			Port:           clusterPort,
			StoreType:      x1model.StoreTypeDRAM,
			Spec:           "",
			DestSpec:       params.NodeType,
			CreateTime:     time.Now(),
			UpdateTime:     time.Now(),
			ClusterShortID: int(cmSI.ShardId),
		}
		app.Clusters = append(app.Clusters, cluster)
		if err := createNodesForCluster(ctx, params, app, cluster, cmSI.Instances, &maxNodeId, zoneMapper); err != nil {
			return err
		}
		clusterIdx++
	}
	return nil
}

func getCmSIList(insts []*csmaster.CsmasterInstance) CsmasterShardInstancesList {
	cmSIMap := make(map[int64][]*csmaster.CsmasterInstance)
	for _, inst := range insts {
		if _, ok := cmSIMap[inst.ShardId]; !ok {
			cmSIMap[inst.ShardId] = make([]*csmaster.CsmasterInstance, 0)
		}
		cmSIMap[inst.ShardId] = append(cmSIMap[inst.ShardId], inst)
	}
	cmSIList := make(CsmasterShardInstancesList, 0)
	for shardId, insts := range cmSIMap {
		cmSIList = append(cmSIList, &CsmasterShardInstances{ShardId: shardId, Instances: insts})
	}
	sort.Sort(cmSIList)
	return cmSIList
}

func createNodesForCluster(ctx context.Context, params *iface.Parameters, app *x1model.Application, cluster *x1model.Cluster, insts []*csmaster.CsmasterInstance, maxNodeId *int, zoneMapper zone.ZoneMapperFunc) error {
	for _, inst := range insts {
		role := "master"
		if inst.CacheInstanceType == 2 {
			role = "slave"
		}
		if len(inst.Ipv6) != 0 {
			app.IpType = x1model.Ipv6
		}
		azone, ok := zoneMapper(inst.AvailabilityZone, true)
		if !ok {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("unsupported availability zone %s", inst.AvailabilityZone))
			return fmt.Errorf("unsupported availability zone %s", inst.AvailabilityZone)
		}
		cluster.Nodes = append(cluster.Nodes, &x1model.Node{
			AppId:         cluster.AppId,
			ClusterId:     cluster.ClusterId,
			NodeId:        cluster.ClusterId + "." + fmt.Sprintf("%d", *maxNodeId),
			Engine:        cluster.Engine,
			EngineVersion: cluster.EngineVersion,
			Port:          int(inst.Port),
			Basedir:       util.DefaultBaseDir,
			Ip:            inst.FixIp,
			XagentPort:    x1model.DefaultXagentPort,
			Region:        params.Region,
			LogicZone:     inst.AvailabilityZone,
			Azone:         azone,
			VpcId:         params.VPCID,
			SubnetId:      inst.SubnetId,
			Role:          role,
			Status:        x1model.NodeOrProxyStatusInUse,
			ResourceId:    inst.Uuid,
			FloatingIP:    inst.FloatingIp,
			IPv6:          inst.Ipv6,
			RootPassword:  inst.Password,
			NodeShortID:   int(inst.Id),
			HostName:      inst.HostName,
			NodeFixID:     inst.NodeShowId,
		})
		cluster.MaxNodeIndex++
		*maxNodeId++
	}
	return nil
}

func createInterfaces(ctx context.Context, params *iface.Parameters, app *x1model.Application, insts []*csmaster.CsmasterInstance, zoneMapper zone.ZoneMapperFunc) error {
	cmSIList := getCmSIList(insts)
	maxProxyId := 0
	for _, cmSI := range cmSIList {
		if cmSI.Instances[0].CacheInstanceType != 0 {
			continue
		}
		proxyGetter := getProxyGetter(cmSI.Instances)
		totalProxyCount := len(cmSI.Instances)
		proxyCountPerAz := 1
		countPerItf := len(params.Replicas)
		if len(params.Replicas) == 1 {
			countPerItf = 2
			proxyCountPerAz = 2
		}
		itfCount := totalProxyCount / countPerItf
		for i := 0; i < itfCount; i++ {
			itf := &x1model.Interface{
				InterfaceId:   app.AppId + "-itf-" + fmt.Sprintf("%d", i),
				Engine:        x1model.EngineBDRPProxy,
				EngineVersion: params.EngineVersion,
				Port:          int(cmSI.Instances[0].Port),
				McpackPort:    int(cmSI.Instances[0].Port) + 1,
				AppId:         app.AppId,
				StoreType:     x1model.StoreTypeDRAM,
				Spec:          "",
				DestSpec:      "proxy.n1.small",
				CreateTime:    time.Now(),
				UpdateTime:    time.Now(),
			}
			app.Interfaces = append(app.Interfaces, itf)
			if err := createProxyForItf(ctx, params, app, itf, proxyGetter, &maxProxyId, proxyCountPerAz, zoneMapper); err != nil {
				return err
			}
		}
	}
	return nil
}

func getProxyGetter(insts []*csmaster.CsmasterInstance) func(zone string) (*csmaster.CsmasterInstance, error) {
	type zoneIter struct {
		idx   int
		zone  string
		insts []*csmaster.CsmasterInstance
	}
	zoneMap := make(map[string]*zoneIter)
	for _, inst := range insts {
		if inst.CacheInstanceType != 0 {
			continue
		}
		if _, ok := zoneMap[inst.AvailabilityZone]; !ok {
			zoneMap[inst.AvailabilityZone] = &zoneIter{
				idx:   0,
				zone:  inst.AvailabilityZone,
				insts: make([]*csmaster.CsmasterInstance, 0),
			}
		}
		zoneMap[inst.AvailabilityZone].insts = append(zoneMap[inst.AvailabilityZone].insts, inst)
	}
	return func(zone string) (*csmaster.CsmasterInstance, error) {
		if _, ok := zoneMap[zone]; !ok {
			return nil, fmt.Errorf("no instance in zone %s", zone)
		}
		itf := zoneMap[zone]
		if itf.idx >= len(itf.insts) {
			return nil, fmt.Errorf("no instance in zone %s", zone)
		}
		inst := itf.insts[itf.idx]
		itf.idx++
		return inst, nil
	}
}

func createProxyForItf(ctx context.Context, params *iface.Parameters, app *x1model.Application, itf *x1model.Interface, proxyGetter func(zone string) (*csmaster.CsmasterInstance, error), maxProxyId *int, proxyCountPerAz int, zoneMapper zone.ZoneMapperFunc) error {
	for _, replica := range params.Replicas {
		for i := 0; i < proxyCountPerAz; i++ {
			inst, err := proxyGetter(replica.Zone)
			if err != nil {
				return err
			}
			azone, ok := zoneMapper(inst.AvailabilityZone, true)
			if !ok {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("unsupported availability zone %s", inst.AvailabilityZone))
				return fmt.Errorf("unsupported availability zone %s", inst.AvailabilityZone)
			}
			itf.Proxys = append(itf.Proxys, &x1model.Proxy{
				ProxyId:         itf.InterfaceId + "." + fmt.Sprintf("%d", *maxProxyId),
				Ip:              inst.FixIp,
				Port:            int(inst.Port),
				McpackPort:      int(inst.Port) + 1,
				StatPort:        22222,
				Region:          app.Region,
				LogicZone:       inst.AvailabilityZone,
				Azone:           azone,
				VpcId:           app.VpcId,
				XagentPort:      x1model.DefaultXagentPort,
				AppId:           app.AppId,
				Basedir:         util.DefaultBaseDir,
				Status:          x1model.NodeOrProxyStatusInUse,
				InterfaceId:     itf.InterfaceId,
				ResourceOrderId: "",
				SubnetId:        inst.SubnetId,
				ResourceId:      inst.Uuid,
				FloatingIP:      inst.FloatingIp,
				IPv6:            inst.Ipv6,
				RootPassword:    inst.Password,
				Engine:          itf.Engine,
				EngineVersion:   itf.EngineVersion,
				ProxyShortID:    int(inst.Id),
				HostName:        inst.HostName,
				NodeFixID:       inst.NodeShowId,
			})
			*maxProxyId++
		}
	}
	return nil
}

func createBlbs(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster) error {
	createTime, err := time.ParseInLocation("2006-01-02 15:04:05", clusterModel.CreateTime, time.UTC)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
		return err
	}
	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:             app.AppId,
		Name:              app.AppId,
		Type:              x1model.BLBTypeNormal,
		VpcId:             app.VpcId,
		SubnetId:          clusterModel.SubnetId,
		IpType:            x1model.Ipv4,
		BgwGroupId:        "",
		BgwGroupExclusive: 0,
		BgwGroupMode:      "",
		MasterAZ:          "",
		SlaveAZ:           "",
		BlbId:             clusterModel.ElbId,
		Vip:               "",
		Ovip:              clusterModel.ElbPnetip,
		Ipv6:              "",
		Status:            x1model.BLBStatusAvailable,
		CreateAt:          createTime,
		UpdateAt:          time.Now(),
	})
	if app.IpType == x1model.Ipv6 {
		app.BLBs = append(app.BLBs, &x1model.BLB{
			AppId:             app.AppId,
			Name:              "",
			Type:              x1model.BLBTypeNormal,
			VpcId:             app.VpcId,
			SubnetId:          clusterModel.SubnetId,
			IpType:            x1model.Ipv6,
			BgwGroupId:        "",
			BgwGroupExclusive: 0,
			BgwGroupMode:      "",
			MasterAZ:          "",
			SlaveAZ:           "",
			BlbId:             clusterModel.ElbIpv6Id,
			Vip:               "",
			Ovip:              clusterModel.ElbIpv6,
			Ipv6:              "",
			Status:            x1model.BLBStatusAvailable,
			CreateAt:          createTime,
			UpdateAt:          time.Now(),
		})
	}
	return nil
}

func createRedisAcl(ctx context.Context, app *x1model.Application) error {
	if app.Type != x1model.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "app type is not standalone, skip create redis acl")
		return nil
	}
	acls, err := resource.CsmasterOpAgent.GetRedisAclsByAppShortID(ctx, app.AppShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis acls by app short id failed", logit.Error("error", err))
		return err
	}
	var redisAcls []*x1model.RedisAcl
	for _, acl := range acls {
		var password string
		if len(acl.Password) != 0 {
			password, err = crypto_utils.EncryptKey(acl.Password)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "encrypt redis acl password failed", logit.Error("error", err))
				return err
			}
		}
		redisAcls = append(redisAcls, &x1model.RedisAcl{
			AppID:          app.AppId,
			AccountName:    acl.UserName,
			CreateAt:       acl.CreateTime,
			UpdateAt:       time.Now(),
			Version:        0,
			Engine:         x1model.EngineRedis,
			Password:       password,
			AllowedCmds:    acl.AllowedCommands,
			AllowedSubCmds: acl.AllowedSubCommands,
			KeyPatterns:    acl.KeyPatterns,
			Properties:     "",
			Status:         x1model.AppStatusInUse,
		})
	}
	if len(redisAcls) > 0 {
		if err := x1model.RedisAclSave(ctx, redisAcls); err != nil {
			resource.LoggerTask.Warning(ctx, "save redis acls failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func updateSubnet(ctx context.Context, app *x1model.Application) error {
	var relicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &relicas); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal replicas failed", logit.Error("error", err))
		return err
	}
	relicasMap := make(map[string]*iface.Replica)
	for _, replica := range relicas {
		relicasMap[replica.Zone] = replica
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if len(node.SubnetId) != 0 {
				continue
			}
			replica, ok := relicasMap[node.LogicZone]
			if ok && len(replica.SubnetIDs) > 0 {
				node.SubnetId = replica.SubnetIDs[0]
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if len(proxy.SubnetId) != 0 {
				continue
			}
			replica, ok := relicasMap[proxy.LogicZone]
			if ok && len(replica.SubnetIDs) > 0 {
				proxy.SubnetId = replica.SubnetIDs[0]
			}
		}
	}
	return nil
}

func createConfs(ctx context.Context, app *x1model.Application) error {
	if app.Type != x1model.AppTypeStandalone {
		resource.LoggerTask.Notice(ctx, "app type is not standalone, skip create confs")
		return nil
	}
	confs, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, app.AppShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get confs by app short id failed", logit.Error("error", err))
		return err
	}
	var confsModel []*x1model.Config
	for _, conf := range confs {
		confsModel = append(confsModel, &x1model.Config{
			AppId: app.AppId,
			Name:  conf.ConfName,
			Type:  x1model.ConfigTypeRedis,
			Value: conf.Value,
		})
	}
	if len(confsModel) > 0 {
		if err := x1model.ConfigSave(ctx, confsModel); err != nil {
			resource.LoggerTask.Warning(ctx, "save confs failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func createDefaultAcl(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	if len(param.DefaultPassword) != 0 {
		defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
		if err != nil && !x1model.IsNotFound(err) {
			resource.LoggerTask.Warning(ctx, "get acl fail", logit.String("app id:", app.AppId),
				logit.Error("error", err))
			return err
		}
		if defaultAcl != nil {
			resource.LoggerTask.Notice(ctx, "default acl already exists, skip create default acl")
			return nil
		}
		encryptPw, err := crypto_utils.EncryptKey(param.DefaultPassword)
		if err != nil {
			resource.LoggerTask.Error(ctx, "encrpt password error", logit.Error("error", err))
			return err
		}
		redisAcls := []*x1model.RedisAcl{{
			AppID:       app.AppId,
			AccountName: x1model.DefaultACLUser,
			CreateAt:    time.Now(),
			UpdateAt:    time.Now(),
			Engine:      param.Engine,
			Password:    encryptPw,
			AllowedCmds: "@all",
			KeyPatterns: "*",
			Status:      x1model.AppStatusInUse,
		}}
		if err := x1model.RedisAclSave(ctx, redisAcls); err != nil {
			resource.LoggerTask.Error(ctx, "save redis acls error", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func createDefaultConfigSet(ctx context.Context, app *x1model.Application) error {
	configList, err := x1model.ConfigAllByCond(ctx, "app_id = ? AND type = ?", app.AppId, x1model.ConfigTypeRedis)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get config list fail", logit.Error("err", err))
		return err
	}
	for _, config := range configList {
		if config.Name == "disable_commands" {
			resource.LoggerTask.Notice(ctx, "disable commands config already exists, skip create default config set")
			return nil
		}
	}
	configs := []*x1model.Config{{
		AppId: app.AppId,
		Name:  "disable_commands",
		Type:  x1model.ConfigTypeRedis,
		Value: "flushall,flushdb",
	}}
	if err := x1model.ConfigSave(ctx, configs); err != nil {
		resource.LoggerTask.Error(ctx, "save configset error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessMigrateToX1(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processMigrateToX1(ctx, teu); err != nil {
		if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
			return err2
		}
		return err
	}
	return nil
}

func processMigrateToX1(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters failed", logit.Error("error", err))
		return err
	}
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, params.UserID, params.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.Status != 24 {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("expect cluster status is 24, but got %d", clusterModel.Status))
		return fmt.Errorf("expect cluster status is 24, but got %d", clusterModel.Status)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, params.AppID)
	if err == nil && app.Status == x1model.AppStatusInUse {
		resource.LoggerTask.Warning(ctx, "app is in use")
		return errors.New("app is in use")
	}
	app, err = createApp(ctx, params, clusterModel)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create app failed", logit.Error("error", err))
		return err
	}
	instances, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get instance models failed", logit.Error("error", err))
		return err
	}
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, params.UserID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}
	if err := createClusters(ctx, params, app, instances, zoneMapper); err != nil {
		resource.LoggerTask.Warning(ctx, "create clusters failed", logit.Error("error", err))
		return err
	}
	if params.ClusterType == "cluster" {
		if err := createInterfaces(ctx, params, app, instances, zoneMapper); err != nil {
			resource.LoggerTask.Warning(ctx, "create interfaces failed", logit.Error("error", err))
			return err
		}
	}
	if err := createBlbs(ctx, app, clusterModel); err != nil {
		resource.LoggerTask.Warning(ctx, "create blbs failed", logit.Error("error", err))
		return err
	}
	if params.ClusterType == "standalone" {
		if err := createRedisAcl(ctx, app); err != nil {
			resource.LoggerTask.Warning(ctx, "create redis acl failed", logit.Error("error", err))
			return err
		}
		if err := createDefaultAcl(ctx, app, params); err != nil {
			resource.LoggerTask.Warning(ctx, "create default acl failed", logit.Error("error", err))
			return err
		}
		if err := createConfs(ctx, app); err != nil {
			resource.LoggerTask.Warning(ctx, "create confs failed", logit.Error("error", err))
			return err
		}
		if err := createDefaultConfigSet(ctx, app); err != nil {
			resource.LoggerTask.Warning(ctx, "create default config set failed", logit.Error("error", err))
			return err
		}
	}
	if err := updateSubnet(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "update subnet failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "save applications failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessAddXAgentSg(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters failed", logit.Error("error", err))
		return err
	}
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	req := &security_group.ReopenSecurityGroupRulesParams{
		SecurityGroupID: clusterModel.SecurityGroupId,
		ServicePorts:    []int32{x1model.DefaultXagentPort},
		WhitelistIPs:    security_group.Instance().GetDefaultIps(ctx),
		UserId:          params.UserID,
		IsEnableIpv6:    false,
	}
	if err := security_group.Instance().ReopenSecurityGroupRules(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "reopen security group rules failed", logit.Error("error", err))
		return err
	}
	if len(clusterModel.ShardSecurityGroupId) != 0 {
		req := &security_group.ReopenSecurityGroupRulesParams{
			SecurityGroupID: clusterModel.ShardSecurityGroupId,
			ServicePorts:    []int32{x1model.DefaultXagentPort},
			WhitelistIPs:    security_group.Instance().GetDefaultIps(ctx),
			UserId:          params.UserID,
			IsEnableIpv6:    false,
		}
		if err := security_group.Instance().ReopenSecurityGroupRules(ctx, req); err != nil {
			resource.LoggerTask.Warning(ctx, "reopen inner security group rules failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}
