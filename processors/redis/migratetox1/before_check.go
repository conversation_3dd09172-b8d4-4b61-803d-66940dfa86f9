package migratetox1

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func checkXAgentAlive(ctx context.Context, instance *csmaster.CsmasterInstance) error {
	cli := deploy.NewDefaultClient()
	resp, err := cli.CheckAlive(ctx, &deploy.CheckAliveRequest{
		XagentAddr: &deploy.XagentAddr{
			Host: instance.FloatingIp,
			Port: x1model.DefaultXagentPort,
		},
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check xagent alive failed", logit.Error("error", err), logit.String("resp", base_utils.Format(resp)))
		return err
	}
	return nil
}

func ProcessMigrateToX1BeforeCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processMigrateToX1BeforeCheck(ctx, teu); err != nil {
		if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
			return err2
		}
		return err
	}
	return nil
}

func processMigrateToX1BeforeCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters failed", logit.Error("error", err))
		return err
	}
	clusterDetail, err := csmaster.CsmasterOp().CsmasterGetClusterDetail(ctx, &csmaster.ListCacheClusterInstancesParam{
		UserID: params.UserID,
		AppID:  params.AppID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster detail failed", logit.Error("error", err))
		return err
	}
	if clusterDetail.HitX1 == 1 {
		resource.LoggerTask.Warning(ctx, "cluster has been migrated to x1", logit.Error("error", err))
		return err
	}
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, params.UserID, params.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if len(clusterModel.MasterDomain) != 0 {
		resource.LoggerTask.Warning(ctx, "cluster is old multi-region cluster")
		return err
	}
	g := gtask.Group{}
	instances, err := csmaster.CsmasterOp().GetInstanceModels(ctx, params.UserID, params.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get instance models failed", logit.Error("error", err))
		return err
	}
	for _, instance := range instances {
		instance := instance
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return checkXAgentAlive(ctx, instance)
			})
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check xagent alive failed", logit.Error("error", err))
		return err
	}
	return nil
}
