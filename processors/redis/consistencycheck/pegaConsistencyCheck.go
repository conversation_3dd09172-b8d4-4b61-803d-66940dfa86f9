package consistencycheck

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessPegaConsistencySnapShot 创建pega一致性检查快照
func ProcessPegaConsistencySnapShot(ctx context.Context, teu *workflow.TaskExecUnit) error {
	cluster, err := x1model.ClusterGetByClusterId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("get cluster %s failed", teu.Entity), logit.Error("error", err))
		return err
	}
	cModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("get cluster model %s failed", teu.Entity), logit.Error("error", err))
		return err
	}
	if cModel.Status != csmaster_model_interface.CACHE_CLUSTER_RUNNING && cModel.Status != csmaster_model_interface.CACHE_CLUSTER_MODIFIED_FAILED {
		if err := sendNextTask(ctx, cluster); err != nil {
			return err
		}
		return nil
	}
	var masterNode *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		if node.Role == x1model.RoleTypeMaster {
			masterNode = node
			break
		}
	}
	if masterNode == nil {
		resource.LoggerTask.Warning(ctx,
			fmt.Sprintf("get master node for shard %s failed", teu.Entity), logit.Error("error", err))
		return fmt.Errorf("get master node for shard %s failed", teu.Entity)
	}

	for _, node := range cluster.Nodes {
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		status, err := checkNodeRocksdbStatus(ctx, node, "")
		if err != nil {
			return err
		}
		if status == "skip" {
			return nil
		}
		if status == "next" {
			if err := sendNextTask(ctx, cluster); err != nil {
				return err
			}
			return nil
		}
	}

	if err := util.PegaConsistencyBgsave(ctx, masterNode.FloatingIP, masterNode.Port, ""); err != nil {
		resource.LoggerTask.Warning(ctx,
			fmt.Sprintf("create pega consistency bgsave for shard %s failed", teu.Entity), logit.Error("error", err))
		return err
	}
	return nil
}

func checkNodeRocksdbStatus(ctx context.Context, node *x1model.Node, password string) (status string, err error) {
	rocksInfo, err := util.GetPegaRocksDBInfo(ctx, node.FloatingIP, node.Port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx,
			fmt.Sprintf("get rocksdb info for shard %s failed", node.ClusterId), logit.Error("error", err))
		return "", err
	}
	resource.LoggerTask.Trace(ctx, "pega rocksdb info",
		logit.String("node_id", node.NodeId),
		logit.String("rocksdb_info", base_utils.Format(rocksInfo)))
	if rocksInfo.Backups < 0 || rocksInfo.IsCaculatingChecksum < 0 {
		resource.LoggerTask.Warning(ctx,
			fmt.Sprintf("get rocksdb info for shard %s failed", node.ClusterId), logit.Error("error", err))
		return "", fmt.Errorf(fmt.Sprintf("get rocksdb info for shard %s failed", node.ClusterId))
	}
	if rocksInfo.IsCaculatingChecksum > 0 {
		resource.LoggerTask.Notice(ctx, "rocksdb checksum is already running")
		return "skip", nil
	}
	if rocksInfo.Backups > 0 {
		resource.LoggerTask.Notice(ctx, "backup exists, pega consistency check next time")
		return "next", nil
	}
	return "", nil
}

func sendNextTask(ctx context.Context, cluster *x1model.Cluster) error {
	scheduleTime := time.Now().Add(1 * time.Hour)
	if !isInOperatingTime(scheduleTime) {
		resource.LoggerTask.Warning(ctx, "pega consistency check is not in operating time")
		return nil
	}
	resource.LoggerTask.Notice(ctx, "backup exists, pega consistency check after 1 hour")
	if err := resource.TaskOperator.CreateTask(ctx, &iface.Task{
		TaskID:     uuid.NewString(),
		WorkFlow:   "scs-pega-consistency-check",
		Entity:     cluster.ClusterId,
		EntityDim:  "app",
		Status:     iface.TaskStatusWaiting,
		CreatedAt:  time.Now(),
		Schedule:   time.Now().Add(1 * time.Hour),
		Deadline:   time.Now().Add(12 * time.Hour),
		Mutex:      "pega_consistency" + cluster.ClusterId,
		Parameters: "",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "create pega consistency check task failed", logit.Error("error", err))
		return err
	}
	return nil
}

func isInOperatingTime(t time.Time) bool {
	// 只在1点到5点之间执行
	if t.Hour() < 1 || t.Hour() > 5 {
		return false
	}
	return true
}
