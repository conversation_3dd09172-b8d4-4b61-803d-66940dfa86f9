/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type ReplicaChanges struct {
	ToAdd    []*iface.Replica
	ToDelete []*iface.Replica
}

func getReplicaChanges(ctx context.Context, app *x1model.Application, param *iface.Parameters) (*ReplicaChanges, error) {
	ret := &ReplicaChanges{}
	var replicas []*iface.Replica
	destReplicas := param.Replicas
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		return nil, err
	}
	raw, err := json.Marshal(destReplicas)
	if err != nil {
		return nil, err
	}
	app.DestReplicas = string(raw)

	for _, destReplica := range destReplicas {
		found := false
		for _, replica := range replicas {
			if replica.Zone == destReplica.Zone {
				if destReplica.Count > replica.Count {
					ret.ToAdd = append(ret.ToAdd, &iface.Replica{
						Zone:      destReplica.Zone,
						SubnetIDs: destReplica.SubnetIDs,
						Role:      destReplica.Role,
						Count:     destReplica.Count - replica.Count,
					})
				} else if destReplica.Count < replica.Count {
					ret.ToDelete = append(ret.ToDelete, &iface.Replica{
						Zone:      destReplica.Zone,
						SubnetIDs: destReplica.SubnetIDs,
						Role:      destReplica.Role,
						Count:     replica.Count - destReplica.Count,
					})
				}
				found = true
				break
			}
		}
		if !found {
			ret.ToAdd = append(ret.ToAdd, &iface.Replica{
				Zone:      destReplica.Zone,
				SubnetIDs: destReplica.SubnetIDs,
				Role:      destReplica.Role,
				Count:     destReplica.Count,
			})
		}
	}
	for _, replica := range replicas {
		found := false
		for _, destReplica := range destReplicas {
			if destReplica.Zone == replica.Zone {
				found = true
				break
			}
		}
		if !found {
			ret.ToDelete = append(ret.ToDelete, &iface.Replica{
				Zone:      replica.Zone,
				SubnetIDs: replica.SubnetIDs,
				Role:      replica.Role,
				Count:     replica.Count,
			})
		}
	}
	return ret, nil
}

func modifyProxiesReplicasCore(ctx context.Context, app *x1model.Application, destReplicas map[string]*iface.Replica, zoneMapper zone.ZoneMapperFunc, totalCount int, itfs []*x1model.Interface) error {
	maxProxyIdx, err := util.GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get max proxy index failed", logit.Error("error", err))
		return err
	}
	oriItfCount := len(itfs)

	destProxyCountPerItfPerAz := 1
	destItfCount := int(math.Ceil(float64(totalCount) / float64(len(destReplicas))))
	if len(destReplicas) == 1 {
		destProxyCountPerItfPerAz = 2
		destItfCount = int(math.Ceil(float64(totalCount) / 2.0))
	}

	adjustIdxMax := 0
	if destItfCount > oriItfCount {
		maxItfIdx, err := util.GetMaxInterfaceIdx(ctx, itfs)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get max interface index failed", logit.Error("error", err))
			return err
		}
		for i := maxItfIdx + 1; i <= maxItfIdx+(destItfCount-oriItfCount); i++ {
			app.Interfaces = append(app.Interfaces, &x1model.Interface{
				InterfaceId:   app.AppId + "-itf-" + strconv.Itoa(i),
				AppId:         app.AppId,
				Engine:        x1model.EngineBDRPProxy,
				EngineVersion: itfs[0].EngineVersion,
				Port:          app.Port,
				Status:        x1model.ClusterStatusInUse,
				CreateTime:    time.Now(),
				UpdateTime:    time.Now(),
				DestSpec:      itfs[0].DestSpec,
				StoreType:     x1model.StoreTypeDRAM,
			})
			itfs = append(itfs, app.Interfaces[len(app.Interfaces)-1])
		}
		adjustIdxMax = len(itfs)
	} else if destItfCount < oriItfCount {
		for idx := len(itfs) - 1; idx >= len(itfs)-(destItfCount-oriItfCount); idx-- {
			for _, proxy := range itfs[idx].Proxys {
				proxy.Status = x1model.NodeOrProxyStatusToDelete
			}
		}
		adjustIdxMax = len(itfs) - (destItfCount - oriItfCount)
	}

	for i := 0; i < adjustIdxMax; i++ {
		itf := itfs[i]
		for _, proxy := range itf.Proxys {
			if _, ok := destReplicas[proxy.LogicZone]; !ok {
				proxy.Status = x1model.NodeOrProxyStatusToDelete
			}
		}
		for lzone, replica := range destReplicas {
			curCount := 0
			for _, proxy := range itf.Proxys {
				if proxy.LogicZone == lzone && proxy.Status != x1model.NodeOrProxyStatusToDelete {
					curCount++
				}
			}
			if curCount > destProxyCountPerItfPerAz {
				for _, proxy := range itf.Proxys {
					if proxy.LogicZone == lzone && proxy.Status != x1model.NodeOrProxyStatusToDelete {
						proxy.Status = x1model.NodeOrProxyStatusToDelete
						curCount--
						if curCount <= destProxyCountPerItfPerAz {
							break
						}
					}
				}
			} else if curCount < destProxyCountPerItfPerAz {
				for ; curCount < destProxyCountPerItfPerAz; curCount++ {
					azone, found := zoneMapper(lzone, true)
					if !found {
						resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", lzone))
						return cerrs.Errorf("not found azone for lzone %s", lzone)
					}
					itf.Proxys = append(itf.Proxys, &x1model.Proxy{
						ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyIdx+1),
						InterfaceId:   itf.InterfaceId,
						AppId:         app.AppId,
						Engine:        itf.Engine,
						EngineVersion: itf.EngineVersion,
						Port:          itf.Port,
						Region:        itfs[0].Proxys[0].Region,
						LogicZone:     lzone,
						Azone:         azone,
						VpcId:         itfs[0].Proxys[0].VpcId,
						SubnetId:      replica.SubnetIDs[0],
						XagentPort:    x1model.DefaultXagentPort,
						Status:        x1model.NodeOrProxyStatusToCreate,
						Basedir:       util.DefaultBaseDir,
						McpackPort:    itf.Port + 1,
						StatPort:      22222,
					})
					maxProxyIdx++
				}
			}
		}
	}
	return nil
}

func modifyProxysReplicas(ctx context.Context, app *x1model.Application, param *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	destReplicas := map[string]*iface.Replica{}
	for idx := range param.Replicas {
		destReplicas[param.Replicas[idx].Zone] = &param.Replicas[idx]
	}
	normalItfs := []*x1model.Interface{}
	normalCount := 0
	for _, itf := range app.Interfaces {
		if strings.Contains(itf.InterfaceId, "-itfop-") {
			continue
		}
		normalItfs = append(normalItfs, itf)
		normalCount += len(itf.Proxys)
	}
	if normalCount > 0 {
		if err := modifyProxiesReplicasCore(ctx, app, destReplicas, zoneMapper, normalCount, normalItfs); err != nil {
			return err
		}
	}
	return nil
}

func modifyNodesReplicas(ctx context.Context, app *x1model.Application, param *iface.Parameters, replica_changes *ReplicaChanges, zoneMapper zone.ZoneMapperFunc) error {
	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get max node index failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		del_marker := make(map[string]int)
		for _, del_replica := range replica_changes.ToDelete {
			del_marker[del_replica.Zone] = del_replica.Count
		}
		resource.LoggerTask.Trace(ctx, "replica del marks", logit.String("del_marker", base_utils.Format(del_marker)))

		toDeleteNodeCount := 0
		// 先在从里面找
		for _, node := range cluster.Nodes {
			if count, has := del_marker[node.LogicZone]; node.Role == x1model.RoleTypeSlave && has && count > 0 {
				resource.LoggerTask.Trace(ctx, fmt.Sprintf("mark node %s to delete", node.NodeId))
				node.Status = x1model.NodeOrProxyStatusToDelete
				del_marker[node.LogicZone] = count - 1
				toDeleteNodeCount++
			}
		}

		// 再在主里面找
		for _, node := range cluster.Nodes {
			if count, has := del_marker[node.LogicZone]; node.Role == x1model.RoleTypeMaster && has && count > 0 {
				resource.LoggerTask.Trace(ctx, fmt.Sprintf("mark node %s to delete", node.NodeId))
				node.Status = x1model.NodeOrProxyStatusToDelete
				del_marker[node.LogicZone] = count - 1
				toDeleteNodeCount++
			}
		}

		if toDeleteNodeCount > len(cluster.Nodes)-1 {
			resource.LoggerTask.Warning(ctx, "not enough nodes to delete")
			return cerrs.ErrInvalidParams.Errorf("not enough nodes to delete")
		}

		for _, replica := range replica_changes.ToAdd {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return fmt.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for j := 0; j < replica.Count; j++ {
				maxNodeIdx++
				cluster.Nodes = append(cluster.Nodes, &x1model.Node{
					NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
					ClusterId:     cluster.ClusterId,
					AppId:         app.AppId,
					Engine:        cluster.Engine,
					EngineVersion: cluster.EngineVersion,
					Port:          cluster.Port,
					Region:        cluster.Nodes[0].Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					Role:          x1model.RoleTypeSlave,
					VpcId:         cluster.Nodes[0].VpcId,
					SubnetId:      replica.SubnetIDs[0],
					Pool:          cluster.Nodes[0].Pool,
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
				})
			}
		}
	}
	return nil
}

func modifyReplicasCheck(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	totalCount := 0
	curCount := len(app.Clusters[0].Nodes)
	for _, replica := range param.Replicas {
		totalCount += replica.Count
	}
	if totalCount == curCount {
		return errors.New("replica has no changes")
	}
	return nil
}

func ProcessBuildMetaForModifyReplicas(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := modifyReplicasCheck(ctx, app, param); err != nil {
		return err
	}
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	replicaChanges, err := getReplicaChanges(ctx, app, param)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get replicas changes failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "replicas change info",
		logit.String("old_replicas", app.Replicas),
		logit.String("new_replicas", app.DestReplicas),
		logit.String("replica_changes", base_utils.Format(replicaChanges)))

	// TODO 需要每个shard单独加锁处理，以防修改时同时有切换任务导致标记混乱；不过几率不高
	// 由于切换并行处理上线, 不会有并行的切换任务, 上述问题不会出现
	if err := modifyNodesReplicas(ctx, app, param, replicaChanges, zoneMapper); err != nil {
		return err
	}

	param.DestReplicas = param.Replicas
	if err := modifyProxysAZ(ctx, app, param, zoneMapper); err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
