/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* build_meta_global_join_group.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package buildmeta

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/callback"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessBuildMetaForJoinStandaloneGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetGlobalParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "groupid is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}
	if len(app.AppGroupID) != 0 {
		resource.LoggerTask.Warning(ctx, "app is already in group")
		return errors.Errorf("app is already in group")
	}
	app.AppGroupID = param.GroupId
	app.Status = x1model.AppStatusFollowerJoin
	app.AppGroupSeqID = int(param.Id)

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node.Role = x1model.RoleTypeSlave
		}
	}

	err = callback.ModifyCsmasterMigrateStatus(ctx, app, callback.MigrateStatusToCreate)
	if err != nil {
		return err
	}

	shards, err := gmaster.GlobalMasterOp().GetShards(ctx, &gmaster.GetShardsParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shards info fail", logit.Error("err", err))
		return err
	}
	if len(shards) != len(app.Clusters) {
		resource.LoggerTask.Warning(ctx, "global shard num not eq to app cluster")
		return errors.Errorf("global shard num not eq to app cluster")
	}

	for i := 0; i < len(shards); i++ {
		app.Clusters[i].GlobalID = shards[i].ShardId
		app.Clusters[i].GlobalSeqID = int(shards[i].Id)
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}
	return nil
}
