package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessBuildMetaForUpdateConfig(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	oriConfList, err := x1model.ConfigAllByCond(ctx, "app_id = ?", teu.Entity)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get config list error", logit.Error("error", err))
		return err
	}

	oriConfMap := map[string]*x1model.Config{}
	for _, conf := range oriConfList {
		oriConfMap[conf.Name] = conf
	}

	newConfList := make([]*x1model.Config, 0)
	for _, conf := range params.ConfigList {
		if oriConf, ok := oriConfMap[conf.Name]; ok {
			if conf.Value != oriConf.Value {
				oriConf.Value = conf.Value
				newConfList = append(newConfList, oriConf)
			}
		} else {
			newConfList = append(newConfList, &x1model.Config{
				AppId: teu.Entity,
				Name:  conf.Name,
				Type:  conf.Type,
				Value: conf.Value,
			})
		}
	}

	if len(newConfList) == 0 {
		resource.LoggerTask.Notice(ctx, "no new config to update")
		return nil
	}

	if err := x1model.ConfigSave(ctx, newConfList); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to save config before taking effect",
			logit.String("appId", teu.Entity),
			logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}
