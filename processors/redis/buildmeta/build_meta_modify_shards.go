/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/04/26, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// addShards 在元数据中增加新分片
func addShards(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	maxClusterIdx, err := util.GetMaxClusterIdx(ctx, app.Clusters)
	if err != nil {
		return err
	}
	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get max node index failed", logit.Error("error", err))
		return err
	}
	curCount := len(app.Clusters)
	firstCluster := app.Clusters[0]
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("to add %d shards", param.ShardCount-curCount))
	for i := 0; i < param.ShardCount-curCount; i++ {
		maxClusterIdx++
		cluster := &x1model.Cluster{
			ClusterId:       app.AppId + "-" + strconv.Itoa(maxClusterIdx),
			AppId:           app.AppId,
			Engine:          firstCluster.Engine,
			EngineVersion:   firstCluster.EngineVersion,
			Port:            firstCluster.Port,
			Status:          x1model.ClusterStatusInUse,
			CreateTime:      time.Now(),
			UpdateTime:      time.Now(),
			StoreType: util.GetStoreType(ctx, &util.GetStoreTypeParmas{
				NodeType: firstCluster.DestSpec,
			}),
			Spec:            firstCluster.Spec,
			DestSpec:        firstCluster.DestSpec,
			Cpu:             firstCluster.Cpu,
			ActualCpu:       firstCluster.ActualCpu,
			MemSize:         firstCluster.MemSize,
			ActualMemSize:   firstCluster.ActualMemSize,
			DiskSize:        firstCluster.DiskSize,
			ActualDiskSize:  firstCluster.ActualDiskSize,
			SysDiskSize:     firstCluster.SysDiskSize,
			AvailableVolume: firstCluster.AvailableVolume,
		}
		app.Clusters = append(app.Clusters, cluster)
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("add cluster %s", cluster.ClusterId))
		for _, node := range app.Clusters[0].Nodes {
			maxNodeIdx++
			cluster.Nodes = append(cluster.Nodes, &x1model.Node{
				NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
				ClusterId:     cluster.ClusterId,
				AppId:         app.AppId,
				Engine:        cluster.Engine,
				EngineVersion: cluster.EngineVersion,
				Port:          cluster.Port,
				Region:        firstCluster.Nodes[0].Region,
				LogicZone:     node.LogicZone,
				Azone:         node.Azone,
				Role:          node.Role,
				VpcId:         firstCluster.Nodes[0].VpcId,
				SubnetId:      node.SubnetId,
				Pool:          param.Pool,
				XagentPort:    x1model.DefaultXagentPort,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       util.DefaultBaseDir,
			})
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("add node %s", cluster.Nodes[len(cluster.Nodes)-1].NodeId))
		}
	}
	return nil
}

// modifyProxies 根据变化后的shard数调整元数据中的interface与proxy数据
func modifyProxies(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	var curItfCount, targetItfCount, zoneCount int
	var itfs []*x1model.Interface
	proxyCntPerAz := 1

	for _, itf := range app.Interfaces {
		if !strings.Contains(itf.InterfaceId, "-itfop-") {
			curItfCount++
			itfs = append(itfs, itf)
		}
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("current interfaces %s", base_utils.SelectColumn(itfs, "InterfaceId")))

	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		return err
	}
	if len(replicas) > 1 {
		zoneCount = len(replicas)
	} else {
		zoneCount = 2
		proxyCntPerAz = 2
	}
	totalProxyCount, err := GetTotalProxyCount(ctx, &GetTotalProxyCountParams{
		App:        app,
		Engine:     app.Clusters[0].Engine,
		ShardCount: param.ShardCount,
		NodeType:   app.Clusters[0].DestSpec,
		ForceSpecs: nil,
		UserID:     app.UserId,
		VpcID:      app.VpcId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get total proxy count failed", logit.Error("error", err))
		return err
	}
	targetItfCount = totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		targetItfCount++
	}
	resource.LoggerTask.Trace(ctx, "change proxys", logit.String("curItfCount", strconv.Itoa(curItfCount)), logit.String("targetItfCount", strconv.Itoa(targetItfCount)),
		logit.String("zoneCount", strconv.Itoa(zoneCount)), logit.String("proxyCntPerAz", strconv.Itoa(proxyCntPerAz)))

	if targetItfCount > curItfCount {
		maxItfIdx, err := util.GetMaxInterfaceIdx(ctx, itfs)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get max interface index failed", logit.Error("error", err))
			return err
		}
		maxProxyIdx, err := util.GetMaxProxyIndex(ctx, app)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get max proxy index failed", logit.Error("error", err))
			return err
		}
		zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
			return err
		}
		firstItf := itfs[0]
		for i := 0; i < targetItfCount-curItfCount; i++ {
			maxItfIdx++
			itf := &x1model.Interface{
				InterfaceId:    app.AppId + "-itf-" + strconv.Itoa(maxItfIdx),
				AppId:          app.AppId,
				Engine:         x1model.EngineBDRPProxy,
				EngineVersion:  itfs[0].EngineVersion,
				Port:           app.Port,
				Status:         x1model.ClusterStatusInUse,
				CreateTime:     time.Now(),
				UpdateTime:     time.Now(),
				StoreType:      x1model.StoreTypeDRAM,
				Spec:           firstItf.Spec,
				DestSpec:       firstItf.DestSpec,
				Cpu:            firstItf.Cpu,
				ActualCpu:      firstItf.ActualCpu,
				MemSize:        firstItf.MemSize,
				ActualMemSize:  firstItf.ActualMemSize,
				DiskSize:       firstItf.DiskSize,
				ActualDiskSize: firstItf.ActualDiskSize,
				SysDiskSize:    firstItf.SysDiskSize,
			}
			app.Interfaces = append(app.Interfaces, itf)
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("add interface %s", itf.InterfaceId))
			for _, replica := range replicas {
				for j := 0; j < proxyCntPerAz; j++ {
					maxProxyIdx++
					azone, found := zoneMapper(replica.Zone, true)
					if !found {
						resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
						return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
					}
					itf.Proxys = append(itf.Proxys, &x1model.Proxy{
						ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyIdx),
						InterfaceId:   itf.InterfaceId,
						AppId:         app.AppId,
						Engine:        itf.Engine,
						EngineVersion: itf.EngineVersion,
						Port:          itf.Port,
						Region:        itfs[0].Proxys[0].Region,
						LogicZone:     replica.Zone,
						Azone:         azone,
						VpcId:         itfs[0].Proxys[0].VpcId,
						SubnetId:      replica.SubnetIDs[0],
						XagentPort:    x1model.DefaultXagentPort,
						Status:        x1model.NodeOrProxyStatusToCreate,
						Basedir:       util.DefaultBaseDir,
						McpackPort:    itf.Port + 1,
						StatPort:      22222,
					})
					resource.LoggerTask.Trace(ctx, fmt.Sprintf("add proxy %s", itf.Proxys[len(itf.Proxys)-1].ProxyId))
				}
			}
		}
	} else if targetItfCount < curItfCount {
		for i := 0; i < curItfCount-targetItfCount; i++ {
			itf := itfs[len(itfs)-1-i]
			for _, proxy := range itf.Proxys {
				proxy.Status = x1model.NodeOrProxyStatusToDelete
			}
		}
	}
	return nil
}

func ProcessModifyShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if param.ShardCount > len(app.Clusters) {
		if err := addShards(ctx, app, param); err != nil {
			resource.LoggerTask.Error(ctx, "add shards error", logit.Error("error", err))
			return err
		}
	}
	if err := modifyProxies(ctx, app, param); err != nil {
		resource.LoggerTask.Error(ctx, "modify proxies error", logit.Error("error", err))
		return err
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
