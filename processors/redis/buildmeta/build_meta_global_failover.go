/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* build_meta_global_failover.go */
/*
modification history
--------------------
2022/07/14 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package buildmeta

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessBuildMetaForGlobalFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 更新节点的role
	param, err := iface.GetGlobalParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}
	if err = CallCsmasterUpdateNodesForGlobal(ctx, app, param); err != nil {
		resource.LoggerTask.Warning(ctx, "CallCsmasterUpdateNodesForGlobal fail", logit.Error("err", err))
		return err
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}

	return nil
}

// CallCsmasterUpdateNodesForGlobal 把global failover的新信息同步到csmaster
func CallCsmasterUpdateNodesForGlobal(ctx context.Context, app *x1model.Application, param *iface.GlobalParam) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}

	for _, cluster := range app.Clusters {
		if cluster.GlobalID != param.GFShardId {
			continue
		}
		for _, node := range cluster.Nodes {
			item := csdk.CsmasterInstance{
				Uuid: node.ResourceId,
			}

			if node.NodeId == param.GFNewMasterRedisId {
				node.Role = x1model.RoleTypeMaster
				item.CacheInstanceType = 3
				item.SlaverRedis = param.GFNewSlaveIps
			} else {
				node.Role = x1model.RoleTypeSlave
				item.CacheInstanceType = 2
				item.MasterRedis = param.GFNewMasterIp
			}
			creq.Models = append(creq.Models, &item)
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}
