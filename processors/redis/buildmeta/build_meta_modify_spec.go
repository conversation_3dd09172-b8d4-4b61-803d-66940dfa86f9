/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessBuildMetaForModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		cluster.DestSpec = param.NodeType
		// 适配变配到PEGADB本地盘
		cluster.StoreType = util.GetStoreType(ctx, &util.GetStoreTypeParmas{
			NodeType: cluster.DestSpec,
		})
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessUpdateCsmasterOpTypeMasterChange(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 修改csmaster optype, 保证下发的cpu参数正确
	if err := util.ModifyCsmasterOpType(ctx, app, util.OpTypeMasterChange); err != nil {
		resource.LoggerTask.Error(ctx, "modify csmaster optype error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessUpdateCsmasterOpTypeNormal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 恢复csmaster optype
	if err := util.ModifyCsmasterOpType(ctx, app, util.OpTypeNormal); err != nil {
		resource.LoggerTask.Error(ctx, "modify csmaster optype error", logit.Error("error", err))
		return err
	}
	return nil
}
