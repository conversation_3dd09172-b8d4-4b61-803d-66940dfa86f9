package buildmeta

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessBuildMetaOpenTls(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusTlsOpening {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			proxy.Status = x1model.NodeOrProxyStatusTlsOpening
		}
	}

	csmasterClusterInfo, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmaster cluster info fail", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "csmasterClusterInfo",
		logit.String("csmasterClusterInfo:", base_utils.Format(csmasterClusterInfo)))

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func ProcessBuildMetaCloseTls(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusTlsClosing {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			proxy.Status = x1model.NodeOrProxyStatusTlsClosing
		}
	}

	csmasterClusterInfo, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmaster cluster info fail", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "csmasterClusterInfo",
		logit.String("csmasterClusterInfo:", base_utils.Format(csmasterClusterInfo)))

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}
