/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessBuildMetaForUpgrade(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 1. 获取 app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 2. 获取参数
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	csmasterClusterInfo, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmaster cluster info fail", logit.Error("error", err))
		return err
	}

	// 3. 检查是否是大版本升级
	if len(param.TargetKernelVersion) != 0 {
		resource.LoggerTask.Notice(ctx, "check app version", logit.String("online verison", csmasterClusterInfo.KernelVersion),
			logit.String("new version", param.TargetKernelVersion))
		if util.GetBigVersion(csmasterClusterInfo.KernelVersion) > util.GetBigVersion(param.TargetKernelVersion) {
			resource.LoggerTask.Error(ctx, "new version is smaller than online", logit.String("online verison", csmasterClusterInfo.KernelVersion),
				logit.String("new version", param.TargetKernelVersion))
			return errors.Errorf("new version is smaller than online")
		}
		// 历史上只有1种场景csmasterClusterInfo.KernelVersion = "",即pegadb,这个时候元数据保持原样
		if csmasterClusterInfo.KernelVersion != "" &&
			util.GetBigVersion(csmasterClusterInfo.KernelVersion) < util.GetBigVersion(param.TargetKernelVersion) {
			if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
				Model: &csmaster.CsmasterCluster{
					KernelVersion: param.TargetKernelVersion,
				},
				UserID: app.UserId,
				AppID:  app.AppId,
			}); err != nil {
				resource.LoggerTask.Error(ctx, "cb csmaster update kernel version err", logit.Error("error", err))
				return err
			}
			csmasterClusterInfo.KernelVersion = param.TargetKernelVersion
		}
	}

	// 3. 设置 node 状态为 toupgrade
	for _, cluster := range app.Clusters {
		// 用于大版本升级，获取配置时使用的 cluster.EngineVersion 判断版本
		cluster.EngineVersion = csmasterClusterInfo.KernelVersion
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToUpgrade {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			node.Status = x1model.NodeOrProxyStatusToUpgrade
		}

		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToUpgrade {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			node.Status = x1model.NodeOrProxyStatusToUpgrade
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToUpgrade {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			proxy.Status = x1model.NodeOrProxyStatusToUpgrade
		}
	}

	version, err := GetDeployConfVersion(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get deploy conf failed", logit.Error("error", err))
		return err
	}
	imageId, _ := util.GetImageIdAndVersion(app.ImageID)
	app.ImageID = imageId + ":" + version

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForSyncGroupUpgrade(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToUpgrade {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			node.Status = x1model.NodeOrProxyStatusToUpgrade
		}

		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToUpgrade {
				resource.LoggerTask.Warning(ctx, "another upgrade is running", logit.String("appId", teu.Entity))
				return errors.Errorf("another upgrade is running")
			}
			node.Status = x1model.NodeOrProxyStatusToUpgrade
		}
	}

	version, err := GetDeployConfVersion(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get deploy conf failed", logit.Error("error", err))
		return err
	}
	imageId, _ := util.GetImageIdAndVersion(app.ImageID)
	app.ImageID = imageId + ":" + version

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}
