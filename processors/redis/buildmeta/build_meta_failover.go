/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessBuildMetaForSelfHealing(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	for _, uhi := range param.UnHealthShards {
		cluster, err := FindClusterByShortID(ctx, app, uhi.ShardShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "cluster not found", logit.Error("error", err))
			return err
		}
		if err := util.TryCorrectMasterRole(ctx, cluster); err != nil {
			resource.LoggerTask.Warning(ctx, "try correct master role error", logit.Error("error", err))
			return err
		}
		for _, nodeShortId := range uhi.NodeShortIDs {
			node, err := FindNodeByShortID(ctx, cluster, nodeShortId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "node not found", logit.Error("error", err))
				continue
			}
			// 非单副本实例，跳过主节点自愈
			replNum := GetCacheClusterReplicationNum(ctx, cluster)
			if node.Role == x1model.RoleTypeMaster && replNum > 1 {
				resource.LoggerTask.Trace(ctx, "master node, skip", logit.String("node_id", node.NodeId), logit.Int("repl_num", replNum))
				continue
			}
			// 进行ping探测和slave复制状态检查
			if !param.IsForceReplace {
				if err := util.PingTest(ctx, node.FloatingIP, node.Port, PintTestTimeout, acl); err == nil {
					if replCheckErr := util.CheckSlaveReplication(ctx, cluster, node, password); replCheckErr == nil {
						resource.LoggerTask.Notice(ctx, "node alive and slave replication check success, skip replace",
							logit.String("node", base_utils.Format(node)))
						continue
					} else {
						resource.LoggerTask.Notice(ctx, "node alive but slave replication check failed, need replace",
							logit.String("node", base_utils.Format(node)),
							logit.Error("replcheck_err", replCheckErr))
					}
				} else {
					resource.LoggerTask.Notice(ctx, "ping node failed, need replace",
						logit.String("node", base_utils.Format(node)),
						logit.Error("ping_err", err))
				}
			}
			node.Status = x1model.NodeOrProxyStatusToFakeDelete
			for _, n := range cluster.RoNodes {
				if n.ResourceId == node.ResourceId {
					n.Status = node.Status
				}
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("node %s mark as tofakedelete", node.NodeId))
		}
	}
	if err := util.AddNewNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}

	if err := util.AddNewRoNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func markToDeleteNodeInCluster(ctx context.Context, clusterId string, nodeIds []string, replication_num int32, param *iface.Parameters) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()

	resource.LoggerTask.Notice(ctx, "mark to delete node in cluster",
		logit.String("cluster", clusterId), logit.String("nodeIds", base_utils.Format(nodeIds)))
	cluster, err := x1model.ClusterGetByClusterId(ctx, clusterId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster error", logit.Error("error", err))
		return err
	}
	if err := util.TryCorrectMasterRole(ctx, cluster); err != nil {
		resource.LoggerTask.Warning(ctx, "try correct master role error", logit.Error("error", err))
		return err
	}
	acl, err := x1model.RedisAclGetInUse(ctx, cluster.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	for _, node := range cluster.Nodes {
		//单副本集群的主node也需要进行自愈
		if node.Role == x1model.RoleTypeMaster && replication_num > 1 {
			resource.LoggerTask.Trace(ctx, "master node, skip", logit.String("node_id", node.NodeId))
			continue
		}
		if in, _ := base_utils.InArray(node.NodeId, nodeIds); !in {
			resource.LoggerTask.Trace(ctx, "node not in list, skip", logit.String("node_id", node.NodeId))
			continue
		}
		if !param.SelfHealFromCsmaster.IsForceReplace {
			if err := util.PingTest(ctx, node.FloatingIP, node.Port, PintTestTimeout, nil); err == nil {
				if replCheckErr := util.CheckSlaveReplication(ctx, cluster, node, password); replCheckErr == nil {
					resource.LoggerTask.Notice(ctx, "node alive and slave replication check success, skip replace",
						logit.String("node", base_utils.Format(node)))
					continue
				} else {
					resource.LoggerTask.Notice(ctx, "node alive but slave replication check failed, need replace",
						logit.String("node", base_utils.Format(node)),
						logit.Error("replcheck_err", replCheckErr))
				}
			} else {
				resource.LoggerTask.Notice(ctx, "ping node failed, need replace",
					logit.String("node", base_utils.Format(node)),
					logit.Error("ping_err", err))
			}
		}
		resource.LoggerTask.Notice(ctx, "mark node to delete", logit.String("node", node.NodeId))
		node.Status = x1model.NodeOrProxyStatusToFakeDelete
	}
	if err := x1model.ClusterSave(ctx, []*x1model.Cluster{cluster}); err != nil {
		resource.LoggerTask.Error(ctx, "save cluster error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForClusterSelfHeal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmaster model error", logit.Error("error", err))
		return err
	}

	clusterNodeIdsMap := make(map[string][]string)
	for _, un := range param.SelfHealFromCsmaster.NodeShortIDs {
		node, cluster, err := FindNodeByShortIDInApp(ctx, app, un)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}
		if _, ok := clusterNodeIdsMap[cluster.ClusterId]; !ok {
			clusterNodeIdsMap[cluster.ClusterId] = make([]string, 0)
		}
		clusterNodeIdsMap[cluster.ClusterId] = append(clusterNodeIdsMap[cluster.ClusterId], node.NodeId)
	}
	resource.LoggerTask.Trace(ctx, "Unhealth cluster NodeIds Map", logit.String("clusterNodeIdsMap", base_utils.Format(clusterNodeIdsMap)))

	g := gtask.Group{}
	for clusterId, nodeIds := range clusterNodeIdsMap {
		clusterId := clusterId
		nodeIds := nodeIds
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return markToDeleteNodeInCluster(ctx, clusterId, nodeIds, csmasterModel.ReplicationNum, param)
			})
		})
	}

	for _, up := range param.SelfHealFromCsmaster.ProxyShortIDs {
		proxy, err := FindProxyByShortIDInApp(ctx, app, up)
		if err != nil {
			resource.LoggerTask.Error(ctx, "proxy not found", logit.Error("error", err))
			return err
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				if !param.SelfHealFromCsmaster.IsForceReplace {
					if err := util.PingTest(ctx, proxy.FloatingIP, proxy.Port, PintTestTimeout, nil); err == nil {
						resource.LoggerTask.Warning(ctx, "proxy alive, skip replace", logit.String("proxy", base_utils.Format(proxy)))
						return nil
					}
				}
				resource.LoggerTask.Notice(ctx, "mark proxy to delete", logit.String("proxy", proxy.ProxyId))
				proxy.Status = x1model.NodeOrProxyStatusToFakeDelete
				if err := x1model.ProxysSave(ctx, []*x1model.Proxy{proxy}); err != nil {
					resource.LoggerTask.Error(ctx, "save proxy error", logit.Error("error", err))
					return err
				}
				return nil
			})
		})
	}
	if _, err = g.Wait(); err != nil {
		resource.LoggerTask.Error(ctx, "mark to delete node error", logit.Error("error", err))
		return err
	}

	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := util.AddNewNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}
	if err := util.AddNewProxyForReplacing(ctx, app, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new proxy for replcaing failed", logit.Error("error", err))
		return err
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

// GetCacheClusterReplicationNum, 获取分片可用副本数，不含只读节点
func GetCacheClusterReplicationNum(ctx context.Context, cluster *x1model.Cluster) int {
	replNum := 0
	for _, node := range cluster.Nodes {
		if node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate {
			replNum++
		}
	}
	return replNum
}
