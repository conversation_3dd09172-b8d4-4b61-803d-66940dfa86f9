/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_pega_analysis.go
 * <AUTHOR>
 * @date 2023/03/22 15:04:11
 * @brief
 *
 **/

package buildmeta

import (
	"context"
	"sort"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	csService "icode.baidu.com/baidu/scs/x1-api/httpserver/services/csmaster"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	baseCsmaster "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessRecoverInNewClusterBuildMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverNewClusterParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	srcAppId := param.SrcClusterShowID
	appBackupId := param.AppBackupID
	destAppId := param.DestClusterShowID
	restoreType := param.RestoreType // 基于RDB恢复:common.RESTORE_BY_RDB   基于时间点恢复:common.RESTORE_BY_TIME
	resource.LoggerTask.Notice(ctx, "get recover new cluster params ", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("appBackupId:", base_utils.Format(appBackupId)),
		logit.String("restoreType:", base_utils.Format(restoreType)))

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 更新cache_cluster表status状态
	if err := csService.UpdateClusterModel(ctx, &baseCsmaster.UpdateClusterModelParams{
		Model: &baseCsmaster.CsmasterCluster{
			Status: 121, //重构后数据恢复的状态码
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		errorMessage := "update cluster status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}

	/*
		修复源集群克隆状态  clone_status -> 0
			NOT_CLONE   = 0
		    IS_CLONEING = 1
	*/
	srcApp, err := x1model.ApplicationGetByAppId(ctx, srcAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := csService.UpdateClusterModel(ctx, &baseCsmaster.UpdateClusterModelParams{
		Model: &baseCsmaster.CsmasterCluster{
			CloneStatus: 0,
		},
		UserID:         srcApp.UserId,
		AppID:          srcApp.AppId,
		RequiredFields: []string{"clone_status"},
	}); err != nil {
		errorMessage := "update cluster clone status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}

	return nil
}
func ProcessRecoverInNewClusterCloneTopo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverNewClusterParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	srcAppId := param.SrcClusterShowID
	appBackupId := param.AppBackupID
	destAppId := param.DestClusterShowID
	restoreType := param.RestoreType // 基于RDB恢复:common.RESTORE_BY_RDB   基于时间点恢复:common.RESTORE_BY_TIME
	resource.LoggerTask.Notice(ctx, "get recover new cluster params ", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("appBackupId:", base_utils.Format(appBackupId)),
		logit.String("restoreType:", base_utils.Format(restoreType)))

	// 获取原集群分片列表
	srcAppShardList, err := GetShardList(ctx, srcAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get shard list by appId failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("srcAppShardList:", base_utils.Format(srcAppShardList)))

	// 获取目标集群分片列表
	destAppShardList, err := GetShardList(ctx, destAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get shard list by appId failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("destAppShardList:", base_utils.Format(destAppShardList)))

	// 原分片和目标分片列表排序
	sort.Ints(srcAppShardList)
	sort.Ints(destAppShardList)

	if len(destAppShardList) > 1 {
		resource.LoggerTask.Notice(ctx, "len(destAppShardList) > 1, need clone cluster topo")
	} else {
		resource.LoggerTask.Notice(ctx, "len(destAppShardList) = 1, skip clone cluster topo")
		return nil
	}

	// 获取原集群topo
	srcClusterTopo, err := metaserver.GetClusterTopo(ctx, srcAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app topo from metaserver failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app topo success", logit.String("srcClusterTopo:", base_utils.Format(srcClusterTopo)))

	// 获取目标集群topo
	destClusterTopo, err := metaserver.GetClusterTopo(ctx, destAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app topo from metaserver failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app topo success", logit.String("destClusterTopo:", base_utils.Format(destClusterTopo)))

	// clone topo
	err = metaserver.CloneClusterTopo(ctx, srcAppId, srcClusterTopo, srcAppShardList, destAppId, destClusterTopo, destAppShardList)
	if err != nil {
		resource.LoggerTask.Error(ctx, "clone app topo failed",
			logit.String("srcAppId:", base_utils.Format(srcAppId)),
			logit.String("srcClusterTopo:", base_utils.Format(srcClusterTopo)),
			logit.String("destAppId:", base_utils.Format(destAppId)),
			logit.String("destClusterTopo:", base_utils.Format(destClusterTopo)),
			logit.Error("err", err))
		return err
	}
	return nil
}

func ProcessRecoverInNewClusterUpdateRdbUrl(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverNewClusterParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	srcAppId := param.SrcClusterShowID
	appBackupId := param.AppBackupID
	destAppId := param.DestClusterShowID
	restoreType := param.RestoreType // 基于RDB恢复:common.RESTORE_BY_RDB   基于时间点恢复:common.RESTORE_BY_TIME
	resource.LoggerTask.Notice(ctx, "get recover new cluster params ", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("appBackupId:", base_utils.Format(appBackupId)),
		logit.String("restoreType:", base_utils.Format(restoreType)))

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.AppId != destAppId {
		resource.LoggerTask.Error(ctx, "app_id is not equal to dest_cluster_show_id", logit.String("app", app.AppId))
		return err
	}

	// 获取源集群的备份信息,同时生成copy生成目标集群的备份信息
	getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
		DataType:             "Redis",
		AppID:                srcAppId,
		AppDataBackupID:      appBackupId,
		DownloadUrlExpireSec: 7200, // 下载链接有效期
	}
	appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
	if err != nil {
		errMsg := "call dbrs api get app backup detail failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	// 获取目标备份集
	validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, srcAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
		return err
	}
	var appBackup *x1model.AppBackup
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == appBackupId {
			resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
			appBackup = backupRecord
		}
	}
	if appBackup == nil {
		errMsg := "get appBackup failed"
		resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	for _, backupRecord := range appBackupDetail.AppDataBackupShows {
		if appBackupDetail.AppDataBackupID == appBackupId {
			// 更新集群备份下载链接
			access := ""
			if len(backupRecord.OuterLinks) > 0 {
				access = backupRecord.OuterLinks[0]
			}
			for _, appBackupItem := range appBackup.AppBackupItems {
				if appBackupItem.BackupID == backupRecord.DataBackupID {
					resource.LoggerTask.Notice(ctx, "update rdb download url",
						logit.String("backupId:", base_utils.Format(appBackupItem.BackupID)),
						logit.String("rdb url", base_utils.Format(access)))
					appBackupItem.Access = access
				}

			}
		}
	}
	saveErr := x1model.SaveBackup(ctx, []*x1model.AppBackup{appBackup})
	if saveErr != nil {
		resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessRecoverInNewClusterCloneBackupRecord(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverNewClusterParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	srcAppId := param.SrcClusterShowID
	appBackupId := param.AppBackupID
	destAppId := param.DestClusterShowID
	restoreType := param.RestoreType // 基于RDB恢复:common.RESTORE_BY_RDB   基于时间点恢复:common.RESTORE_BY_TIME
	resource.LoggerTask.Notice(ctx, "get recover new cluster params ", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("appBackupId:", base_utils.Format(appBackupId)),
		logit.String("restoreType:", base_utils.Format(restoreType)))

	// 获取原集群分片列表
	srcAppShardList, err := GetShardList(ctx, srcAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get shard list by appId failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("srcAppShardList:", base_utils.Format(srcAppShardList)))

	// 获取目标集群分片列表
	destAppShardList, err := GetShardList(ctx, destAppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get shard list by appId failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("destAppShardList:", base_utils.Format(destAppShardList)))

	// 原分片和目标分片列表排序
	sort.Ints(srcAppShardList)
	sort.Ints(destAppShardList)

	if len(srcAppShardList) != len(destAppShardList) {
		resource.LoggerTask.Error(ctx, "len(srcAppShardList) != len(destAppShardList), can not clone recover")
	}

	srcCacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, srcAppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("srcClusterShowId", srcAppId),
			logit.Error("queryError", err))
		return err
	}

	destCacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, destAppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("destClusterShowId", destAppId),
			logit.Error("queryError", err))
		return err
	}

	// 获取原集群备份记录
	var backupModels []*csmaster_model_interface.BackupRecord
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "cluster_id = ? and batch_id = ?",
		srcCacheCluster.Id, appBackupId); err != nil {
		resource.LoggerTask.Warning(ctx, "get src backup record failed",
			logit.String("srcClusterShowId", srcAppId),
			logit.Error("err", err))
		return err
	}

	// 创建目标集群备份记录
	var destBackupModels []*csmaster_model_interface.BackupRecord
	destClusterBatchId := uuid.New().String()
	for i, srcShardId := range srcAppShardList {
		for _, backupRecord := range backupModels {
			if backupRecord.ShardName == cast.ToString(srcShardId) {
				destBackupModels = append(destBackupModels, &csmaster_model_interface.BackupRecord{
					BatchID: destClusterBatchId,
					// 使用 InstanceID 存储下源集群分片 ID
					InstanceID:  srcAppShardList[i],
					ClusterID:   int(destCacheCluster.Id),
					StartTime:   time.Now(), // 当前时间
					Duration:    backupRecord.Duration,
					Status:      backupRecord.Status,
					BackupType:  backupRecord.BackupType,
					Bucket:      backupRecord.Bucket,
					ObjectKey:   backupRecord.ObjectKey,
					ObjectSize:  backupRecord.ObjectSize,
					ShardName:   cast.ToString(destAppShardList[i]),
					Comment:     backupRecord.Comment,
					Expairation: backupRecord.Expairation,
				})
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "insert destBackupModels", logit.String("destBackupModels :", base_utils.Format(destBackupModels)))
	if err := resource.CsmasterModel.CreateMulti(ctx, destBackupModels); err != nil {
		resource.LoggerTask.Error(ctx, "insert destBackupModels", logit.String("destBackupModels :",
			base_utils.Format(destBackupModels)), logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessRecoverInNewClusterCloneBackupRecordBak(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverNewClusterParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	srcAppId := param.SrcClusterShowID
	appBackupId := param.AppBackupID
	destAppId := param.DestClusterShowID
	restoreType := param.RestoreType // 基于RDB恢复:common.RESTORE_BY_RDB   基于时间点恢复:common.RESTORE_BY_TIME
	resource.LoggerTask.Notice(ctx, "get recover new cluster params ", logit.String("srcAppId:", base_utils.Format(srcAppId)),
		logit.String("destAppId:", base_utils.Format(destAppId)),
		logit.String("appBackupId:", base_utils.Format(appBackupId)),
		logit.String("restoreType:", base_utils.Format(restoreType)))

	validBackupRecord, err := x1model.GetValidBackupByAppBackupId(ctx, appBackupId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appBackupId failed", logit.Error("err", err))
		return err
	}

	// 判断原集群备份集是否唯一,理论上只应有一条备份集数据
	if len(validBackupRecord) != 1 {
		resource.LoggerTask.Error(ctx, "the num of validBackupRecord is invalid", logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
		return err
	}

	srcAppBackupRecord := validBackupRecord[0]
	// 根据源集群备份信息,生成一份目标集群的备份集数据,同时存储进数据库
	var destAppBackup *x1model.AppBackup
	destAppBackup.AppBackupID = srcAppBackupRecord.AppBackupID
	destAppBackup.AppID = destAppId // 更新备份集信息为目标集群的集群showid
	destAppBackup.BackupType = srcAppBackupRecord.BackupType
	destAppBackup.Comment = srcAppBackupRecord.Comment
	destAppBackup.Expairation = srcAppBackupRecord.Expairation
	destAppBackup.Status = srcAppBackupRecord.Status
	for _, backupRecord := range srcAppBackupRecord.AppBackupItems {
		destAppBackup.AppBackupItems = append(destAppBackup.AppBackupItems, &x1model.AppBackupItem{
			AppBackupID: backupRecord.AppBackupID,
			BackupID:    backupRecord.BackupID,
			ShardID:     backupRecord.ShardID,
			StartTime:   backupRecord.StartTime,
			Duration:    backupRecord.Duration,
			Status:      backupRecord.Status,
			Access:      backupRecord.Access,
			ObjectSize:  backupRecord.ObjectSize,
		})
	}
	saveErr := x1model.SaveBackup(ctx, []*x1model.AppBackup{destAppBackup})
	if saveErr != nil {
		resource.LoggerTask.Error(ctx, "save dest app backup record failed", logit.Error("err", err))
		return err
	}

	return nil
}

func GetShardList(ctx context.Context, appId string) ([]int, error) {
	appShardList := []int{}
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return nil, err
	}
	if app.AppId != appId {
		resource.LoggerTask.Error(ctx, "app.AppId is not equal to appId", logit.String("app", app.AppId))
		return nil, err
	}
	for _, srcCluster := range app.Clusters {
		appShardList = append(appShardList, srcCluster.ClusterShortID)
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("appId:", base_utils.Format(appId)),
		logit.String("appShardList:", base_utils.Format(appShardList)))

	return appShardList, nil
}
