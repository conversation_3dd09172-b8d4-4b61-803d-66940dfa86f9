package buildmeta

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessBuildMetaOpenTde(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, app.AppId)
	if err != nil {
		errorMessage := "get cluster tde info failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppId)))
		return err
	}
	if len(redisTdeInfos) > 0 {
		// 集群已经开启tde加密
		errorMessage := "cluster is already open tde"
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppId)))
		return errors.New(errorMessage)
	}
	return nil
}
