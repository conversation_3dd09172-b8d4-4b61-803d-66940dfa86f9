/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2023/06/26, by wang<PERSON><PERSON> create
*/

/*
DESCRIPTION
SCS 扩缩 interface
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessBuildMetaModifyInterface
// 生成 Interface 的数据结构
func ProcessBuildMetaModifyInterface(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var curItfCount, targetItfCount int

	// (1) params
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	targetItfCount = param.TargetInterfaceCount
	if targetItfCount < 1 {
		return cerrs.Errorf("param TargetInterfaceCount not invalid")
	}

	// (2) app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// (2) cur itfs
	var itfs []*x1model.Interface
	proxyCntPerAz := 1

	for _, itf := range app.Interfaces {
		if !strings.Contains(itf.InterfaceId, "-itfop-") {
			curItfCount++
			itfs = append(itfs, itf)
		}
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("current interfaces %s", base_utils.SelectColumn(itfs, "InterfaceId")))

	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		return err
	}
	if len(replicas) == 1 {
		proxyCntPerAz = 2
	}

	// (3) build meta
	if targetItfCount > curItfCount {
		maxItfIdx, err := util.GetMaxInterfaceIdx(ctx, itfs)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get max interface index failed", logit.Error("error", err))
			return err
		}
		maxProxyIdx, err := util.GetMaxProxyIndex(ctx, app)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get max proxy index failed", logit.Error("error", err))
			return err
		}
		zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
			return err
		}
		firstItf := itfs[0]
		for i := 0; i < targetItfCount-curItfCount; i++ {
			maxItfIdx++
			itf := &x1model.Interface{
				InterfaceId:    app.AppId + "-itf-" + strconv.Itoa(maxItfIdx),
				AppId:          app.AppId,
				Engine:         x1model.EngineBDRPProxy,
				EngineVersion:  itfs[0].EngineVersion,
				Port:           app.Port,
				Status:         x1model.ClusterStatusInUse,
				CreateTime:     time.Now(),
				UpdateTime:     time.Now(),
				StoreType:      x1model.StoreTypeDRAM,
				Spec:           firstItf.Spec,
				DestSpec:       firstItf.DestSpec,
				Cpu:            firstItf.Cpu,
				ActualCpu:      firstItf.ActualCpu,
				MemSize:        firstItf.MemSize,
				ActualMemSize:  firstItf.ActualMemSize,
				DiskSize:       firstItf.DiskSize,
				ActualDiskSize: firstItf.ActualDiskSize,
				SysDiskSize:    firstItf.SysDiskSize,
			}
			app.Interfaces = append(app.Interfaces, itf)
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("add interface %s", itf.InterfaceId))
			for _, replica := range replicas {
				for j := 0; j < proxyCntPerAz; j++ {
					maxProxyIdx++
					azone, found := zoneMapper(replica.Zone, true)
					if !found {
						resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
						return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
					}
					itf.Proxys = append(itf.Proxys, &x1model.Proxy{
						ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyIdx),
						InterfaceId:   itf.InterfaceId,
						AppId:         app.AppId,
						Engine:        itf.Engine,
						EngineVersion: itf.EngineVersion,
						Port:          itf.Port,
						Region:        itfs[0].Proxys[0].Region,
						LogicZone:     replica.Zone,
						Azone:         azone,
						VpcId:         itfs[0].Proxys[0].VpcId,
						SubnetId:      replica.SubnetIDs[0],
						XagentPort:    x1model.DefaultXagentPort,
						Status:        x1model.NodeOrProxyStatusToCreate,
						Basedir:       util.DefaultBaseDir,
						McpackPort:    itf.Port + 1,
						StatPort:      22222,
					})
					resource.LoggerTask.Trace(ctx, fmt.Sprintf("add proxy %s", itf.Proxys[len(itf.Proxys)-1].ProxyId))
				}
			}
		}
	} else if targetItfCount < curItfCount {
		for i := 0; i < curItfCount-targetItfCount; i++ {
			itf := itfs[len(itfs)-1-i]
			for _, proxy := range itf.Proxys {
				proxy.Status = x1model.NodeOrProxyStatusToDelete
			}
		}
	}

	// save
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
