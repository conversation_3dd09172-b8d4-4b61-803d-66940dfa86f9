/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package buildmeta

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type ReplicaAZChanges struct {
	ToAdd    []*iface.Replica
	ToDelete []*iface.Replica
}

type TargetProxyAZ struct {
	SubnetID  string
	LogicZone string
	AZone     string
	Count     int
}

func markAZUnMatchNodeToDelete(ctx context.Context, cluster *x1model.Cluster, isMaster bool, resourceType string, destReplicas []iface.Replica) []string {
	fixIds := make([]string, 0)
	for _, node := range cluster.Nodes {
		found := false
		if node.Role != x1model.RoleTypeMaster && isMaster {
			continue
		}
		if node.Role == x1model.RoleTypeMaster && !isMaster {
			continue
		}
		for i := range destReplicas {
			// 如果是容器,则只比较可用区
			// 如果是虚拟机,则比较可用区和子网
			if resourceType == "container" {
				if destReplicas[i].Count > 0 && destReplicas[i].Zone == node.LogicZone {
					found = true
					destReplicas[i].Count--
				}
			} else {
				// 理论上只有一个子网
				for _, subnet := range destReplicas[i].SubnetIDs {
					if destReplicas[i].Count > 0 && node.SubnetId == subnet {
						found = true
						destReplicas[i].Count--
					}
				}
			}
			if found {
				break
			}
		}
		if !found {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("not found subnet for node %s", node.NodeId))
			node.Status = x1model.NodeOrProxyStatusToFakeDelete
			fixIds = append(fixIds, node.NodeFixID)
		}
	}
	return fixIds
}

func modifyNodesAZ(ctx context.Context, app *x1model.Application, param *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {

	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get max node index failed", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		destReplicas := make([]iface.Replica, len(param.DestReplicas))
		toReuseFixIDs := make([]string, 0)
		copy(destReplicas, param.DestReplicas)
		// 优先保留主节点
		toReuseFixIDs = append(toReuseFixIDs, markAZUnMatchNodeToDelete(ctx, cluster, true, app.ResourceType, destReplicas)...)
		toReuseFixIDs = append(toReuseFixIDs, markAZUnMatchNodeToDelete(ctx, cluster, false, app.ResourceType, destReplicas)...)

		resource.LoggerTask.Notice(ctx, "to create node for cluster",
			logit.String("cluster id", cluster.ClusterId),
			logit.String("to create replicas", base_utils.Format(destReplicas)))
		for _, replica := range destReplicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return fmt.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for j := 0; j < replica.Count; j++ {
				maxNodeIdx++
				if len(toReuseFixIDs) == 0 {
					resource.LoggerTask.Warning(ctx, "no fix id to reuse",
						logit.String("cluster id", cluster.ClusterId),
						logit.String("to create replicas", base_utils.Format(destReplicas)))
					return errors.New("no fix id to reuse")
				}
				toReuseFixID := ""
				toReuseFixID, toReuseFixIDs = toReuseFixIDs[len(toReuseFixIDs)-1], toReuseFixIDs[:len(toReuseFixIDs)-1]
				cluster.Nodes = append(cluster.Nodes, &x1model.Node{
					NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
					ClusterId:     cluster.ClusterId,
					AppId:         app.AppId,
					Engine:        cluster.Engine,
					EngineVersion: cluster.EngineVersion,
					Port:          cluster.Port,
					Region:        cluster.Nodes[0].Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					Role:          x1model.RoleTypeSlave,
					VpcId:         cluster.Nodes[0].VpcId,
					SubnetId:      replica.SubnetIDs[0],
					Pool:          cluster.Nodes[0].Pool,
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
					NodeFixID:     toReuseFixID,
				})
			}
		}
	}

	return nil
}

func ProcessBuildMetaForModifyAZ(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	if err := modifyNodesAZ(ctx, app, param, zoneMapper); err != nil {
		return err
	}

	if app.Type == x1model.AppTypeCluster {
		if err := modifyProxysAZ(ctx, app, param, zoneMapper); err != nil {
			return err
		}
	}

	rss, err := json.Marshal(param.DestReplicas)
	if err != nil {
		return fmt.Errorf("marshal replicas failed,err:%w", err)
	}

	if app.DestReplicas != string(rss) {
		app.DestReplicas = string(rss)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

// nolint:gocyclo
func modifyProxysAZ(ctx context.Context, app *x1model.Application, param *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	/* 因为当前线上各种变更中并未保证proxy的az与后端node节点的一致性，因此在可用区迁移场景下，重新规划下线上的Proxy的分布
	   Proxy数量:
	     因为当前新增副本数&&Pega变配并不变更Proxy数量,因此迁移可用区先不考虑新增Proxy的问题
	     使用的Proxy数量仍有迁移前保持一致,避免迁移可用区导致新增大量Proxy
	     如果当前集群Proxy数量(不包括op增加的Proxy)已经大于目标数量，则保留多出的Proxy
	*/
	// 计算总共需要的proxy数量
	var curItfCount, targetItfCount, zoneCount, curProxyCount int
	var itfs []*x1model.Interface
	proxyCntPerAz := 1

	for _, itf := range app.Interfaces {
		if !strings.Contains(itf.InterfaceId, "-itfop-") {
			curItfCount++
			curProxyCount += len(itf.Proxys)
			itfs = append(itfs, itf)
		}
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("current interfaces %s", base_utils.SelectColumn(itfs, "InterfaceId")))

	replicas := param.DestReplicas
	if len(replicas) > 1 {
		zoneCount = len(replicas)
	} else {
		zoneCount = 2
		proxyCntPerAz = 2
	}

	targetItfCount = curProxyCount / zoneCount
	if curProxyCount%zoneCount != 0 {
		targetItfCount++
	}
	resource.LoggerTask.Trace(ctx, "change proxys", logit.String("curProxyCount", strconv.Itoa(curProxyCount)),
		logit.String("curItfCount", strconv.Itoa(curItfCount)),
		logit.String("targetItfCount", strconv.Itoa(targetItfCount)), logit.String("zoneCount", strconv.Itoa(zoneCount)),
		logit.String("proxyCntPerAz", strconv.Itoa(proxyCntPerAz)))

	// 重新规划Proxy的策略:
	// 场景：如果是可用区少变多,比如2变3,则可能导致itf数量减少
	// 		如果可用区多变少,比如3变2,则可能导致itf数量增加
	//      如果可用区数量不变,则itf可能不变
	//
	// 1.减少itf数量
	// 2.修改保留下的itf下proxy可用区分布
	// 3.增加itf

	// 计算每个itf下目标可用区分布
	targetInterfaceAZ := make([]TargetProxyAZ, 0)
	for _, replica := range replicas {
		azone, found := zoneMapper(replica.Zone, true)
		if !found {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
			return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
		}
		targetInterfaceAZ = append(targetInterfaceAZ, TargetProxyAZ{
			SubnetID:  replica.SubnetIDs[0],
			LogicZone: replica.Zone,
			AZone:     azone,
			Count:     proxyCntPerAz,
		})
	}

	// 保留node fix id确保监控连续性
	toReuseFixIDs := make([]string, 0)
	toReuseBcmGroupIDs := make([]string, 0)
	maxProxyIdx, err := util.GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get max proxy index failed", logit.Error("error", err))
		return err
	}
	// 删除多余的itf
	if targetItfCount < curItfCount {
		delCount := curItfCount - targetItfCount
		delOffset := len(app.Interfaces) - 1
		for {
			// delOffset < 0理论上不会出现
			if delCount <= 0 || delOffset < 0 {
				break
			}
			if strings.Contains(app.Interfaces[delOffset].InterfaceId, "-itfop-") {
				delOffset--
				continue
			}
			for _, proxy := range app.Interfaces[delOffset].Proxys {
				proxy.Status = x1model.NodeOrProxyStatusToFakeDelete
				toReuseFixIDs = append(toReuseFixIDs, proxy.NodeFixID)
				toReuseBcmGroupIDs = append(toReuseBcmGroupIDs, app.Interfaces[delOffset].BcmInstanceGroup)
			}
			delOffset--
			delCount--
		}
	}

	// 对确定保留的itf修正可用区
	reuseItfCount := 0
	if targetItfCount > curItfCount {
		reuseItfCount = curItfCount
	} else {
		reuseItfCount = targetItfCount
	}
	for i, itf := range app.Interfaces {
		if strings.Contains(itf.InterfaceId, "-itfop-") {
			continue
		}
		if reuseItfCount <= 0 {
			break
		}
		reuseItfCount--
		targetInterfaceAZTmp := make([]TargetProxyAZ, len(targetInterfaceAZ))
		copy(targetInterfaceAZTmp, targetInterfaceAZ)
		for _, proxy := range app.Interfaces[i].Proxys {
			found := false
			for j := range targetInterfaceAZTmp {
				if targetInterfaceAZTmp[j].Count > 0 {
					if (app.ResourceType == "container" && targetInterfaceAZTmp[j].LogicZone == proxy.LogicZone) ||
						(app.ResourceType != "container" && targetInterfaceAZTmp[j].SubnetID == proxy.SubnetId) {
						targetInterfaceAZTmp[j].Count--
						found = true
						break
					}
				}
			}
			if !found {
				proxy.Status = x1model.NodeOrProxyStatusToFakeDelete
				toReuseFixIDs = append(toReuseFixIDs, proxy.NodeFixID)
				if proxy.BcmInstanceGroup != "" {
					toReuseBcmGroupIDs = append(toReuseBcmGroupIDs, proxy.BcmInstanceGroup)
				} else {
					toReuseBcmGroupIDs = append(toReuseBcmGroupIDs, app.Interfaces[i].BcmInstanceGroup)
				}
			}
		}
		for k := 0; k < len(targetInterfaceAZTmp); k++ {
			for count := 0; count < targetInterfaceAZTmp[k].Count; count++ {
				toReuseFixID := ""
				toReuseBcmGroupID := ""
				if len(toReuseFixIDs) > 0 {
					toReuseFixID, toReuseFixIDs = toReuseFixIDs[len(toReuseFixIDs)-1], toReuseFixIDs[:len(toReuseFixIDs)-1]
					toReuseBcmGroupID, toReuseBcmGroupIDs = toReuseBcmGroupIDs[len(toReuseBcmGroupIDs)-1], toReuseBcmGroupIDs[:len(toReuseBcmGroupIDs)-1]
				}
				maxProxyIdx++
				app.Interfaces[i].Proxys = append(app.Interfaces[i].Proxys, &x1model.Proxy{
					ProxyId:          app.Interfaces[i].InterfaceId + "." + strconv.Itoa(maxProxyIdx),
					InterfaceId:      app.Interfaces[i].InterfaceId,
					AppId:            app.AppId,
					Engine:           app.Interfaces[i].Engine,
					EngineVersion:    app.Interfaces[i].EngineVersion,
					Port:             app.Interfaces[i].Port,
					Region:           itfs[0].Proxys[0].Region,
					LogicZone:        targetInterfaceAZTmp[k].LogicZone,
					Azone:            targetInterfaceAZTmp[k].AZone,
					VpcId:            itfs[0].Proxys[0].VpcId,
					SubnetId:         targetInterfaceAZTmp[k].SubnetID,
					XagentPort:       x1model.DefaultXagentPort,
					Status:           x1model.NodeOrProxyStatusToCreate,
					Basedir:          util.DefaultBaseDir,
					McpackPort:       app.Interfaces[i].Port + 1,
					StatPort:         22222,
					NodeFixID:        toReuseFixID,
					BcmInstanceGroup: toReuseBcmGroupID,
				})
			}
		}
	}

	// 新增itf
	if targetItfCount > curItfCount {
		maxItfIdx, err := util.GetMaxInterfaceIdx(ctx, itfs)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get max interface index failed", logit.Error("error", err))
			return err
		}
		firstItf := itfs[0]
		for i := 0; i < targetItfCount-curItfCount; i++ {
			maxItfIdx++
			itf := &x1model.Interface{
				InterfaceId:    app.AppId + "-itf-" + strconv.Itoa(maxItfIdx),
				AppId:          app.AppId,
				Engine:         x1model.EngineBDRPProxy,
				EngineVersion:  itfs[0].EngineVersion,
				Port:           app.Port,
				Status:         x1model.ClusterStatusInUse,
				CreateTime:     time.Now(),
				UpdateTime:     time.Now(),
				StoreType:      x1model.StoreTypeDRAM,
				Spec:           firstItf.Spec,
				DestSpec:       firstItf.DestSpec,
				Cpu:            firstItf.Cpu,
				ActualCpu:      firstItf.ActualCpu,
				MemSize:        firstItf.MemSize,
				ActualMemSize:  firstItf.ActualMemSize,
				DiskSize:       firstItf.DiskSize,
				ActualDiskSize: firstItf.ActualDiskSize,
				SysDiskSize:    firstItf.SysDiskSize,
			}
			app.Interfaces = append(app.Interfaces, itf)
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("add interface %s", itf.InterfaceId))
			for _, replica := range replicas {
				azone, found := zoneMapper(replica.Zone, true)
				if !found {
					resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
					return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
				}
				for j := 0; j < proxyCntPerAz; j++ {
					toReuseFixID := ""
					toReuseBcmGroupID := ""
					if len(toReuseFixIDs) > 0 {
						toReuseFixID, toReuseFixIDs = toReuseFixIDs[len(toReuseFixIDs)-1], toReuseFixIDs[:len(toReuseFixIDs)-1]
						toReuseBcmGroupID, toReuseBcmGroupIDs = toReuseBcmGroupIDs[len(toReuseBcmGroupIDs)-1], toReuseBcmGroupIDs[:len(toReuseBcmGroupIDs)-1]
					}
					maxProxyIdx++
					itf.Proxys = append(itf.Proxys, &x1model.Proxy{
						ProxyId:          itf.InterfaceId + "." + strconv.Itoa(maxProxyIdx),
						InterfaceId:      itf.InterfaceId,
						AppId:            app.AppId,
						Engine:           itf.Engine,
						EngineVersion:    itf.EngineVersion,
						Port:             itf.Port,
						Region:           itfs[0].Proxys[0].Region,
						LogicZone:        replica.Zone,
						Azone:            azone,
						VpcId:            itfs[0].Proxys[0].VpcId,
						SubnetId:         replica.SubnetIDs[0],
						XagentPort:       x1model.DefaultXagentPort,
						Status:           x1model.NodeOrProxyStatusToCreate,
						Basedir:          util.DefaultBaseDir,
						McpackPort:       itf.Port + 1,
						StatPort:         22222,
						NodeFixID:        toReuseFixID,
						BcmInstanceGroup: toReuseBcmGroupID,
					})
					resource.LoggerTask.Trace(ctx, fmt.Sprintf("add proxy %s", itf.Proxys[len(itf.Proxys)-1].ProxyId))
				}
			}
		}
	}
	return nil
}
