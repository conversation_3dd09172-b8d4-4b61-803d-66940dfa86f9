/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/11/09, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
*/

package buildmeta

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessBuildMetaForUnitMigrate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, uhi := range param.UnHealthShards {
		cluster, err := FindClusterByShortID(ctx, app, uhi.ShardShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "cluster not found", logit.Error("error", err))
			return err
		}
		if err := util.TryCorrectMasterRole(ctx, cluster); err != nil {
			resource.LoggerTask.Warning(ctx, "try correct master role error", logit.Error("error", err))
			return err
		}
		for _, nodeShortId := range uhi.NodeShortIDs {
			node, err := FindNodeByShortID(ctx, cluster, nodeShortId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "node not found", logit.Error("error", err))
				continue
			}

			node.Status = x1model.NodeOrProxyStatusToDelete
			for _, n := range cluster.RoNodes {
				if n.ResourceId == node.ResourceId {
					n.Status = node.Status
				}
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("node %s mark as tofakedelete", node.NodeId))
		}
	}
	if err := util.AddNewNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, param.SubnetIDForMigrate); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}

	if err := util.AddNewRoNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, param.SubnetIDForMigrate); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func markToDeleteNodeInClusterForUnitMigrate(ctx context.Context, clusterId string, nodeIds []string, param *iface.Parameters) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()

	resource.LoggerTask.Notice(ctx, "mark to delete node in cluster",
		logit.String("cluster", clusterId), logit.String("nodeIds", base_utils.Format(nodeIds)))
	cluster, err := x1model.ClusterGetByClusterId(ctx, clusterId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster error", logit.Error("error", err))
		return err
	}
	if err := util.TryCorrectMasterRole(ctx, cluster); err != nil {
		resource.LoggerTask.Warning(ctx, "try correct master role error", logit.Error("error", err))
		return err
	}
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			resource.LoggerTask.Trace(ctx, "master node, skip", logit.String("node_id", node.NodeId))
			continue
		}
		if in, _ := base_utils.InArray(node.NodeId, nodeIds); !in {
			resource.LoggerTask.Trace(ctx, "node not in list, skip", logit.String("node_id", node.NodeId))
			continue
		}
		resource.LoggerTask.Notice(ctx, "mark node to delete", logit.String("node", node.NodeId))
		// 设置标记
		node.Status = x1model.NodeOrProxyStatusToDelete
	}
	if err := x1model.ClusterSave(ctx, []*x1model.Cluster{cluster}); err != nil {
		resource.LoggerTask.Error(ctx, "save cluster error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessBuildMetaForClusterUnitMigrate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	clusterNodeIdsMap := make(map[string][]string)
	for _, un := range param.SelfHealFromCsmaster.NodeShortIDs {
		node, cluster, err := FindNodeByShortIDInApp(ctx, app, un)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}
		if _, ok := clusterNodeIdsMap[cluster.ClusterId]; !ok {
			clusterNodeIdsMap[cluster.ClusterId] = make([]string, 0)
		}
		clusterNodeIdsMap[cluster.ClusterId] = append(clusterNodeIdsMap[cluster.ClusterId], node.NodeId)
	}
	resource.LoggerTask.Trace(ctx, "To be unit_migrated cluster NodeIds Map", logit.String("clusterNodeIdsMap", base_utils.Format(clusterNodeIdsMap)))

	g := gtask.Group{}
	for clusterId, nodeIds := range clusterNodeIdsMap {
		clusterId := clusterId
		nodeIds := nodeIds
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return markToDeleteNodeInClusterForUnitMigrate(ctx, clusterId, nodeIds, param)
			})
		})
	}

	for _, up := range param.SelfHealFromCsmaster.ProxyShortIDs {
		proxy, err := FindProxyByShortIDInApp(ctx, app, up)
		if err != nil {
			resource.LoggerTask.Error(ctx, "proxy not found", logit.Error("error", err))
			return err
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				resource.LoggerTask.Notice(ctx, "mark proxy to delete", logit.String("proxy", proxy.ProxyId))
				proxy.Status = x1model.NodeOrProxyStatusToDelete
				if err := x1model.ProxysSave(ctx, []*x1model.Proxy{proxy}); err != nil {
					resource.LoggerTask.Error(ctx, "save proxy error", logit.Error("error", err))
					return err
				}
				return nil
			})
		})
	}
	if _, err = g.Wait(); err != nil {
		resource.LoggerTask.Error(ctx, "mark to delete node error", logit.Error("error", err))
		return err
	}

	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := util.AddNewNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, param.SubnetIDForMigrate); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}
	if err := util.AddNewProxyForReplacing(ctx, app, param.SubnetIDForMigrate); err != nil {
		resource.LoggerTask.Warning(ctx, "add new proxy for replcaing failed", logit.Error("error", err))
		return err
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
