/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/04/26, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	cdsquota "icode.baidu.com/baidu/scs/x1-task/processors/redis/cds_quota"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

func fillAppCluster(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	app.AppName = param.Name
	app.ImageID = bccresource.GetBccImageId()

	if param.Engine == x1model.EnginePegaDB {
		app.ImageID = bccresource.GetBccImageIdForPegaDB()
	}
	app.Product = x1model.ProductSCS
	app.UserId = param.UserID
	rss, err := json.Marshal(param.Replicas)
	if err != nil {
		resource.LoggerTask.Error(ctx, "marshal replicas failed", logit.Error("error", err))
		return err
	}
	app.Replicas = string(rss)
	app.VpcId = param.VPCID
	app.Type = x1model.AppTypeCluster
	app.DeploySetIds = strings.Join(param.DeployIDSet, ",")
	app.Port = param.ServicePortList[0]
	app.McpackPort = param.ServicePortList[1]
	app.InnerPort = param.ServicePortList[2]
	app.Domain = param.Domain
	app.BnsService = param.BnsName
	app.AppShortID = param.ClusterShortID
	localmetaserverID, err := getLocalMetaserver(ctx, app, param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get local metaserver id failed", logit.Error("error", err))
		return err
	}
	app.LocalMetaserver = localmetaserverID
	app.Region = param.Region
	app.ResourceType = param.ResourceType
	app.UseNewPackage = getUseNewPackage(ctx, app)
	app.UseNewAgent = param.UseNewAgent
	version, err := GetDeployConfVersion(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get deploy conf failed", logit.Error("error", err))
		return err
	}
	pocPegaPackage := getPocPegaPackage(ctx, app, param)
	if pocPegaPackage != "" && pocPegaPackage != "default" {
		version = pocPegaPackage
	}
	app.ImageID += ":" + version
	// 包管理用自己的镜像，基于centos8
	// https://console.cloud.baidu-int.com/devops/icode/repos/baidu/scs/scs-image/tree/x1_pkg_manager_centos8
	if app.UseNewPackage == 1 {
		if param.Engine == x1model.EngineRedis {
			app.ImageID = conf.PkgManConf.Image.IsolatedBcc
		} else {
			app.ImageID = conf.PkgManConf.Image.UnIsolatedBcc
		}
	}

	// 用于判断实例是否支持热活从地域监控,从而控制后续监控类的下发
	app.HtGrpMonAvailable = 1
	app.BcmCycle = getBcmPushCycle(ctx, app)

	return nil
}

func fillProxies(ctx context.Context, app *x1model.Application, parameters *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Interfaces) > 0 {
		resource.LoggerTask.Warning(ctx, "interfaces has been created")
		return nil
	}

	// Proxy分布策略如下
	// Proxy总数量与ShardCount等同
	// InterfaceCount * 可用区数量（当可用区数量为1时，视为2） = Proxy总数量,
	// 当可用区数量为1时，一个Interface中有2个Proxy，当多个可用区时，Interface中Proxy数量与可用区数量相同
	// 当出现不能整除时，向上去整
	totalProxyCount, err := GetTotalProxyCount(ctx, &GetTotalProxyCountParams{
		App:        app,
		Engine:     parameters.Engine,
		ShardCount: parameters.ShardCount,
		NodeType:   parameters.NodeType,
		ForceSpecs: parameters.ForceSpecs,
		UserID:     app.UserId,
		VpcID:      app.VpcId,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "get total proxy count failed", logit.Error("error", err))
		return err
	}
	proxyCountPerAz := 1
	zoneCount := len(parameters.Replicas)
	if zoneCount < 2 {
		proxyCountPerAz = 2
		zoneCount = 2
	}
	itfCount := totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		itfCount++
	}

	for i := 0; i < itfCount; i++ {
		app.Interfaces = append(app.Interfaces, &x1model.Interface{
			InterfaceId:   app.AppId + "-itf-" + strconv.Itoa(i),
			AppId:         app.AppId,
			Engine:        x1model.EngineBDRPProxy,
			EngineVersion: getBdrpProxyEngineVersion(ctx, app),
			Port:          app.Port,
			Status:        x1model.ClusterStatusInUse,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			DestSpec:      "proxy.n1.small",
			StoreType:     x1model.StoreTypeDRAM,
		})
	}

	maxProxyId := 0
	for _, itf := range app.Interfaces {
		for _, replica := range parameters.Replicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for i := 0; i < proxyCountPerAz; i++ {
				maxProxyId++
				itf.Proxys = append(itf.Proxys, &x1model.Proxy{
					ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyId),
					InterfaceId:   itf.InterfaceId,
					AppId:         app.AppId,
					Engine:        itf.Engine,
					EngineVersion: itf.EngineVersion,
					Port:          itf.Port,
					Region:        parameters.Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					VpcId:         parameters.VPCID,
					SubnetId:      replica.SubnetIDs[0],
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
					McpackPort:    itf.Port + 1,
					StatPort:      22222,
				})
			}
		}
	}
	return nil
}

func getBdrpProxyEngineVersion(ctx context.Context, app *x1model.Application) string {
	defaultVersion := "3"
	if app.UseNewAgent == "yes" {
		flagVal, err := resource.CsmasterOpAgent.GetFlag(ctx, "use_new_proxy",
			map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
		if err != nil {
			resource.LoggerTask.Trace(ctx, "get use new proxy flag failed", logit.Error("error", err))
			return defaultVersion
		}
		resource.LoggerTask.Trace(ctx, "get use new proxy flag succ", logit.String("flag", flagVal))
		if flagVal == "yes" {
			return "4"
		}
	}
	return defaultVersion
}

func ProcessBuildMetaForCreatingCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// userid后续重构api会拿来鉴权，所以要先存回去
	if app.UserId == "" {
		app.UserId = param.UserID
		if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
			resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
			return err
		}
	}

	if err := fillAppCluster(ctx, app, param); err != nil {
		return err
	}

	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	if err := fillProxies(ctx, app, param, zoneMapper); err != nil {
		return err
	}

	if err := createClusters(ctx, app, param, zoneMapper); err != nil {
		return err
	}

	if err := createBlbModels(ctx, app, param); err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	if err := createDefaultAcl(ctx, app, param); err != nil {
		return err
	}

	if err := createDefaultConfigSet(ctx, app); err != nil {
		return err
	}

	if err := cdsquota.GetPegaCdsConf(ctx, app, param); err != nil {
		return err
	}

	return nil
}

func getUseNewPackage(ctx context.Context, app *x1model.Application) int {
	if app.Type == x1model.EngineMc {
		return 0
	}
	return 1
}

func getLocalMetaserver(ctx context.Context, app *x1model.Application, param *iface.Parameters) (string, error) {
	// 默认情况是用param带进来的，也就是csmaster选的
	metaserverID := param.MetaserverID
	if metaserver.ShouldUseNewMetaserver(ctx, app) {
		if param.Engine == x1model.EnginePegaDB {
			metaserverID = "SlotNewMetaserver_01"
		} else {
			metaserverID = "NewMetaserver_01"
		}
		if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
			Model: &csmaster.CsmasterCluster{
				MetaserverId: metaserverID,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}); err != nil {
			return "", err
		}
		resource.LoggerTask.Trace(ctx, "hit new metaserver flag", logit.String("metaserverid", metaserverID))
	}
	return metaserverID, nil
}

func getBcmPushCycle(ctx context.Context, app *x1model.Application) int {
	var bcmPushCycleStr string
	var err error
	if app.UseNewAgent != "yes" {
		bcmPushCycleStr, err = resource.CsmasterOpAgent.GetFlag(ctx, "bcm_push_cycle",
			map[string]string{"iam_user_id": app.UserId}, "60")
	} else {
		bcmPushCycleStr, err = resource.CsmasterOpAgent.GetFlag(ctx, "bcm_push_cycle_new_agent",
			map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "60")
	}
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get bcm push cycle error", logit.Error("error", err))
		return 60
	}
	bcmPushCycle, err := strconv.Atoi(bcmPushCycleStr)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "bcm push cycle is not int", logit.Error("error", err))
		return 60
	}
	if in, _ := base_utils.InArray(bcmPushCycle, []int{5, 60}); !in {
		resource.LoggerTask.Warning(ctx, "bcm push cycle is invalid")
		return 60
	}
	return bcmPushCycle
}

func getPocPegaPackage(ctx context.Context, app *x1model.Application, param *iface.Parameters) string {
	pocPegaVersion := "default"
	if param.Engine != x1model.EnginePegaDB {
		return pocPegaVersion
	}
	pocFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "poc_flag", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get poc flag error", logit.Error("error", err))
		pocFlag = "no"
	}
	if pocFlag == "yes" {
		pocPegaVersion, err = resource.CsmasterOpAgent.GetFlag(ctx, "poc_pega_version", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "default")
		if err != nil {
			resource.LoggerTask.Notice(ctx, "get poc pega version error", logit.Error("error", err))
			pocPegaVersion = "default"
		}
	}
	return pocPegaVersion
}
