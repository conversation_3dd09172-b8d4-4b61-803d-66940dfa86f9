/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	cdsquota "icode.baidu.com/baidu/scs/x1-task/processors/redis/cds_quota"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

func fillApp(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	app.AppName = param.Name
	app.ImageID = bccresource.GetBccImageId()
	if param.Engine == x1model.EnginePegaDB {
		app.ImageID = bccresource.GetBccImageIdForPegaDB()
	}
	app.Product = x1model.ProductSCS
	app.UserId = param.UserID
	rss, err := json.Marshal(param.Replicas)
	if err != nil {
		resource.LoggerTask.Error(ctx, "marshal replicas failed", logit.Error("error", err))
		return err
	}
	app.Replicas = string(rss)
	app.VpcId = param.VPCID
	app.Type = x1model.AppTypeStandalone
	app.DeploySetIds = strings.Join(param.DeployIDSet, ",")
	app.Port = param.ServicePortList[0]
	app.Domain = param.Domain
	app.ResourceType = param.ResourceType
	app.UseNewPackage = getUseNewPackage(ctx, app)
	app.UseNewAgent = param.UseNewAgent
	version, err := GetDeployConfVersion(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get deploy conf failed", logit.Error("error", err))
		return err
	}
	app.ImageID += ":" + version
	if app.UseNewPackage == 1 {
		if param.Engine == x1model.EngineRedis {
			app.ImageID = conf.PkgManConf.Image.IsolatedBcc
		} else {
			app.ImageID = conf.PkgManConf.Image.UnIsolatedBcc
		}
	}

	// 用于判断实例是否支持热活从地域监控,从而控制后续监控类的下发
	app.HtGrpMonAvailable = 1
	app.BcmCycle = getBcmPushCycle(ctx, app)
	return nil
}

func createClusters(ctx context.Context, app *x1model.Application, param *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Clusters) > 0 {
		resource.LoggerTask.Warning(ctx, "clusters has been created")
		return nil
	}
	port := param.ServicePortList[0]
	if len(app.Interfaces) > 0 {
		port = param.ServicePortList[2]
	}
	for i := 0; i < param.ShardCount; i++ {
		app.Clusters = append(app.Clusters, &x1model.Cluster{
			ClusterId:     app.AppId + "-" + strconv.Itoa(i),
			AppId:         app.AppId,
			Engine:        param.Engine,
			EngineVersion: param.EngineVersion,
			Port:          port,
			Status:        x1model.ClusterStatusInUse,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			DestSpec:      param.NodeType,
			StoreType: util.GetStoreType(ctx, &util.GetStoreTypeParmas{
				NodeType: param.NodeType,
			}),
		})
	}
	maxNodeIdx := 0
	for _, cluster := range app.Clusters {
		for _, replica := range param.Replicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for i := 0; i < replica.Count; i++ {
				role := "slave"
				if replica.Role == "master" && i == 0 {
					role = "master"
				}
				cluster.Nodes = append(cluster.Nodes, &x1model.Node{
					NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
					ClusterId:     cluster.ClusterId,
					AppId:         app.AppId,
					Engine:        cluster.Engine,
					EngineVersion: cluster.EngineVersion,
					Port:          cluster.Port,
					Region:        param.Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					Role:          role,
					VpcId:         param.VPCID,
					SubnetId:      replica.SubnetIDs[0],
					Pool:          param.Pool,
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
				})
				maxNodeIdx++
			}
		}
	}
	return nil
}

func createBlbModels(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	if len(app.BLBs) > 0 {
		return nil
	}
	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:             app.AppId,
		Name:              app.AppId,
		VpcId:             app.VpcId,
		SubnetId:          param.BlbSubnetID,
		Type:              x1model.BLBTypeApp,
		IpType:            x1model.Ipv4,
		BgwGroupId:        param.BgwGroup.BgwGroupId,
		BgwGroupMode:      param.BgwGroup.BgwGroupMode,
		BgwGroupExclusive: param.BgwGroup.BgwGroupExclusive,
		MasterAZ:          param.BgwGroup.MasterAZ,
		SlaveAZ:           param.BgwGroup.SlaveAZ,
	})
	if util.UseNormalBlb() {
		app.BLBs[0].Type = x1model.BLBTypeNormal
	}
	return nil
}

func createDefaultAcl(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	if len(param.DefaultPassword) != 0 {
		encryptPw, err := crypto_utils.EncryptKey(param.DefaultPassword)
		if err != nil {
			resource.LoggerTask.Error(ctx, "encrpt password error", logit.Error("error", err))
			return err
		}
		switch app.Type {
		case x1model.AppTypeStandalone:
			redisAcls := []*x1model.RedisAcl{{
				AppID:       app.AppId,
				AccountName: x1model.DefaultACLUser,
				CreateAt:    time.Now(),
				UpdateAt:    time.Now(),
				Engine:      param.Engine,
				Password:    encryptPw,
				AllowedCmds: "@all",
				KeyPatterns: "*",
				Status:      x1model.ACLStatusToCreate,
			}}
			if err := x1model.RedisAclSave(ctx, redisAcls); err != nil {
				resource.LoggerTask.Error(ctx, "save redis acls error", logit.Error("error", err))
				return err
			}
		case x1model.AppTypeCluster:
			if app.UseNewAgent == "yes" {
				proxyAcls := []*x1model.ProxyAcl{{
					AppID:       app.AppId,
					AccountName: x1model.DefaultACLUser,
					CreateAt:    time.Now(),
					UpdateAt:    time.Now(),
					Engine:      param.Engine,
					Password:    encryptPw,
					AllowedCmds: "@all",
					KeyPatterns: "*",
					Status:      x1model.ACLStatusInUse,
				}}
				if err := x1model.ProxyAclSave(ctx, proxyAcls); err != nil {
					resource.LoggerTask.Error(ctx, "save proxy acls error", logit.Error("error", err))
					return err
				}
			}
		}
	}
	return nil
}

func createDefaultConfigSet(ctx context.Context, app *x1model.Application) error {
	configs := []*x1model.Config{{
		AppId: app.AppId,
		Name:  "disable_commands",
		Type:  x1model.ConfigTypeRedis,
		Value: "flushall,flushdb",
	}}
	if err := x1model.ConfigSave(ctx, configs); err != nil {
		resource.LoggerTask.Error(ctx, "save configset error", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessBuildMetaForCreatingStandalone SCS标准版实例创建过程中，生成对应的X1数据结构
// 1. 生成1个Cluster，
// 2. 生成n个node，数量等同于副本的数量
// 对应的原始代码为 OriginalReqToGeneralReqProcessor::fill_add_shards_changes
// 原始代码覆盖了创建&集群扩容分片的场景，这里只涉及标准版创建的场景（创建单分片）
func ProcessBuildMetaForCreatingStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := fillApp(ctx, app, param); err != nil {
		return err
	}

	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	if err := createClusters(ctx, app, param, zoneMapper); err != nil {
		return err
	}

	if err := createBlbModels(ctx, app, param); err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	if err := createDefaultAcl(ctx, app, param); err != nil {
		return err
	}

	if err := createDefaultConfigSet(ctx, app); err != nil {
		return err
	}

	if err := cdsquota.GetPegaCdsConf(ctx, app, param); err != nil {
		return err
	}

	return nil
}
