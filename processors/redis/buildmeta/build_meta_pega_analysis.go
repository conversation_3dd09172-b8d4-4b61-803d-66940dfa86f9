/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_pega_analysis.go
 * <AUTHOR>
 * @date 2023/03/22 15:04:11
 * @brief
 *
 **/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessBuildMetaForPegaAnalysis(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.AppShortID != param.ClusterShortID {
		resource.LoggerTask.Error(ctx, "app short id not equal cluster short id", logit.String("app", app.AppId))
		return err
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeSlave {
				node.Status = x1model.NodeOrProxyStatusToAnalyze
				// only one slave exec analysis task
				break
			}
		}
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	// 清空之前的分析结果
	err = resource.CsmasterOpAgent.DeleteAnalysisTaskResult(ctx, int64(param.ClusterShortID))
	if err != nil {
		resource.LoggerTask.Error(ctx, "delete old analysis result error", logit.Error("error", err))
		return err
	}

	return nil
}
