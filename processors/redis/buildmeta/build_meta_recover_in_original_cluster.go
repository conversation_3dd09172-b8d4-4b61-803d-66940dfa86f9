/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_pega_analysis.go
 * <AUTHOR>
 * @date 2023/03/22 15:04:11
 * @brief
 *
 **/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessRecoverInOriginalClusterBuildMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverOriginalParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	appId := param.ClusterShowID
	appBackupId := param.AppBackupID

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.AppId != appId {
		resource.LoggerTask.Error(ctx, "app_id is not equal to cluster_show_id", logit.String("app", app.AppId))
		return err
	}

	// 更新数据库里的备份信息,获取最新的备份下载链接
	getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
		DataType:             "Redis",
		AppID:                appId,
		AppDataBackupID:      appBackupId,
		DownloadUrlExpireSec: 7200, // 下载链接有效期
	}
	appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
	if err != nil {
		errMsg := "call dbrs api get app backup detail failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	// 获取目标备份集
	validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
		return err
	}
	var appBackup *x1model.AppBackup
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == appBackupId {
			resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
			appBackup = backupRecord
		}
	}
	if appBackup == nil {
		errMsg := "get appBackup failed"
		resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	for _, backupRecord := range appBackupDetail.AppDataBackupShows {
		if appBackupDetail.AppDataBackupID == appBackupId {
			// 更新集群备份下载链接
			access := ""
			if len(backupRecord.OuterLinks) > 0 {
				access = backupRecord.OuterLinks[0]
			}
			for _, appBackupItem := range appBackup.AppBackupItems {
				if appBackupItem.BackupID == backupRecord.DataBackupID {
					resource.LoggerTask.Notice(ctx, "update rdb download url",
						logit.String("backupId:", base_utils.Format(appBackupItem.BackupID)),
						logit.String("rdb url", base_utils.Format(access)))
					appBackupItem.Access = access
				}

			}
		}
	}
	saveErr := x1model.SaveBackup(ctx, []*x1model.AppBackup{appBackup})
	if saveErr != nil {
		resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
		return err
	}

	return nil
}
