/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"
	"math"
	"strconv"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	PintTestTimeout = 1
)

func FindClusterByShortID(ctx context.Context, app *x1model.Application, shortId int64) (*x1model.Cluster, error) {
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == int(shortId) {
			return cluster, nil
		}
	}
	return nil, fmt.Errorf("cannot find cluster by short id %d", shortId)
}

func FindNodeByShortID(ctx context.Context, cluster *x1model.Cluster, shortId int64) (*x1model.Node, error) {
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.NodeShortID == int(shortId) {
			return node, nil
		}
	}
	return nil, fmt.Errorf("cannot find node by short id %d", shortId)
}

func FindNodeByShortIDInApp(ctx context.Context, app *x1model.Application, shortId int64) (*x1model.Node, *x1model.Cluster, error) {
	for _, cluster := range app.Clusters {
		node, err := FindNodeByShortID(ctx, cluster, shortId)
		if err == nil {
			return node, cluster, nil
		}
	}
	return nil, nil, fmt.Errorf("cannot find node by short id %d", shortId)
}

func FindProxyByShortIDInApp(ctx context.Context, app *x1model.Application, shortId int64) (*x1model.Proxy, error) {
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.ProxyShortID == int(shortId) {
				return proxy, nil
			}
		}
	}
	return nil, fmt.Errorf("cannot find node by short id %d", shortId)
}

func FindRoNodeByShortID(ctx context.Context, cluster *x1model.Cluster, shortId int64) (*x1model.RoNode, error) {
	for _, roNode := range cluster.RoNodes {
		if roNode.NodeShortID == int(shortId) {
			return roNode, nil
		}
	}
	return nil, fmt.Errorf("cannot find ronode by short id %d", shortId)
}

func FindRoNodeByShortIDInApp(ctx context.Context, app *x1model.Application, shortId int64) (*x1model.RoNode, *x1model.Cluster, error) {
	for _, cluster := range app.Clusters {
		roNode, err := FindRoNodeByShortID(ctx, cluster, shortId)
		if err == nil {
			return roNode, cluster, nil
		}
	}
	return nil, nil, fmt.Errorf("cannot find ronode by short id %d", shortId)
}

type GetTotalProxyCountParams struct {
	App        *x1model.Application
	Engine     string
	ShardCount int
	NodeType   string
	ForceSpecs []*iface.ForceSpec
	UserID     string
	VpcID      string
}

// GetTotalProxyCount returns the total proxy count of the application.
// The total proxy count is the count of all shards for redis;
// Or cpu_count_of_the_node / 2 * shard_count for pegadb.
func GetTotalProxyCount(ctx context.Context, params *GetTotalProxyCountParams) (count int, err error) {
	var proxyCoefficient float64
	var curProxyCount int = 0
	pocFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "poc_flag", map[string]string{"iam_user_id": params.UserID, "vpc_id": params.VpcID}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get poc flag error", logit.Error("error", err))
		pocFlag = "no"
	}
	if pocFlag == "yes" {
		pocProxyCoefficient, err := resource.CsmasterOpAgent.GetFlag(ctx, "poc_proxy_coefficient",
			map[string]string{"iam_user_id": params.UserID, "vpc_id": params.VpcID}, "2")
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get flag error", logit.Error("error", err))
			return 0, err
		}
		proxyCoefficient, err = strconv.ParseFloat(pocProxyCoefficient, 64)
		if err != nil || proxyCoefficient < 1.0 || proxyCoefficient > 10.0 {
			resource.LoggerTask.Warning(ctx, "get flag error", logit.Error("error", err), logit.Float64("poc_proxy_coefficient", proxyCoefficient))
			proxyCoefficient = 2.0
		}
		for _, itf := range params.App.Interfaces {
			for _, proxy := range itf.Proxys {
				if proxy.Status == x1model.NodeOrProxyStatusToCreate || proxy.Status == x1model.NodeOrProxyStatusInUse {
					curProxyCount++
				}
			}
		}
	}
	if params.Engine == "redis" {
		if pocFlag == "yes" {
			pocProxyCount := int(math.Ceil(float64(params.ShardCount) * proxyCoefficient))
			if pocProxyCount < curProxyCount {
				pocProxyCount = curProxyCount
			}
			return pocProxyCount, nil
		}
		return params.ShardCount, nil
	}
	param := &specification.GetSpecificationParams{
		UserID: params.App.UserId,
		Name:   params.NodeType,
		Engine: params.Engine,
		StoreType: util.GetStoreType(ctx, &util.GetStoreTypeParmas{
			NodeType: params.NodeType,
		}),
		AppType: params.App.Type,
	}
	spec, err := specification.GetSpecification(ctx, param)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find spec by param %s", base_utils.Format(param)), logit.Error("error", err))
		return 0, err
	}
	cpuCount := spec.CPUCount
	for _, fspec := range params.ForceSpecs {
		if fspec.Engine == "pegadb" && fspec.Cpu > 0 {
			cpuCount = fspec.Cpu
		}
	}

	extra_proxy := 0
	if cpuCount%4 != 0 {
		extra_proxy = 1
	}
	if pocFlag == "yes" {
		pocProxyCount := int(math.Ceil(float64((cpuCount/4+extra_proxy)*params.ShardCount) * proxyCoefficient))
		if pocProxyCount < curProxyCount {
			pocProxyCount = curProxyCount
		}
		return pocProxyCount, nil
	}
	return (cpuCount/4 + extra_proxy) * params.ShardCount, nil
}

func GetDeployConfVersion(ctx context.Context) (string, error) {
	deployClient := deploy.NewDefaultClient()
	ret, err := deployClient.GetServerDeployConf(ctx, &deploy.GetServerDeployConfRequest{
		Conf: &deploy.CommonServerConf{
			PackageTag: "xcache",
			Version:    "",
			WorkDir:    "/root",
			PORT:       6379,
			EnvVars:    nil,
		},
	})
	if err != nil {
		return "", err
	}
	return ret.Package.Version, nil
}
