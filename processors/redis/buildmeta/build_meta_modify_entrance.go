/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package buildmeta

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func createNewBlbModels(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {

	//九章DMS使用服务网卡+服务发布点操作Redis,
	//因此服务发布点不能随意删除
	//服务发布点存在的话复用服务发布点
	var servicePublishEndpoint string
	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			servicePublishEndpoint = blb.ServicePublishEndpoint
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			servicePublishEndpoint = blb.ServicePublishEndpoint
		}
	}

	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:                  app.AppId,
		Name:                   app.AppId,
		VpcId:                  app.VpcId,
		SubnetId:               param.AppDstDefaultEntranceParam.SubnetID,
		Type:                   x1model.BLBTypeAppToExchange,
		IpType:                 x1model.Ipv4,
		MasterAZ:               param.AppDstDefaultEntranceParam.AvailabilityZone,
		ServicePublishEndpoint: servicePublishEndpoint,
	})
	return nil
}

// ProcessBuildMetaForModifyAppDefaultEntrance
func ProcessBuildMetaForModifyAppDefaultEntrance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := createNewBlbModels(ctx, app, param); err != nil {
		resource.LoggerTask.Error(ctx, "create blb models error", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
