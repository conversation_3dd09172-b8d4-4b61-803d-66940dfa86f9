/*
modification history
--------------------
2022/01/18, by s<PERSON><PERSON><PERSON>(<EMAIL>), first version
*/

/*
DESCRIPTION
	更新acl数据库
*/

package buildmeta

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessBuildMetaForSetAcl 更新user acl数据库信息
func ProcessBuildMetaForSetAcl(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	// 解析参数
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail",
			logit.String("processor", teu.Processor),
			logit.String("appId", app.AppId),
			logit.Error("err", err),
		)
		return cerrs.ErrInvalidParams.Wrap(err)
	}
	if app.Type == x1model.AppTypeCluster {
		return processBuildMetaForSetProxyAcl(ctx, app, params)
	}

	aclParams := params.AclParam
	// 获取原始的acl信息
	aclList, err := getAppAclList(ctx, app.AppId)
	if err != nil {
		return err
	}

	// 根据参数更新acl list
	updatedAclList := updateAclListWithParams(app.AppId, x1model.EngineRedis, aclList, aclParams)
	if len(updatedAclList) == 0 {
		resource.LoggerTask.Notice(ctx, "no acl updated",
			logit.String("appId", app.AppId))
		return nil
	}

	// 更新数据库状态
	if err := x1model.RedisAclSave(ctx, updatedAclList); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to save acl before taking effect",
			logit.String("appId", app.AppId),
			logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

// 获取数据库中指定appId的所有非deleted和todelete状态的acl list
func getAppAclList(ctx context.Context, appId string) ([]*x1model.RedisAcl, error) {
	aclList, err := x1model.RedisAclAllByCond(ctx, "app_id = ? AND status not in ?",
		appId, []string{x1model.ACLStatusDeleted, x1model.ACLStatusToDelete})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get acl fail",
			logit.String("appId", appId),
			logit.Error("err", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	return aclList, nil
}

// 获取待更新的acl list (同时也会在某些情况下修改原始的aclList节点的状态 inuse/tocreate->todelete)
func updateAclListWithParams(appId, engine string, aclList []*x1model.RedisAcl, aclParams *iface.AclParam) []*x1model.RedisAcl {
	userACLs := map[string]*x1model.RedisAcl{}
	for _, acl := range aclList {
		userACLs[acl.AccountName] = acl
	}

	var updatedAclList []*x1model.RedisAcl
	if aclParams.Action != iface.ActionDelete {
		// create or update
		for _, acl := range aclParams.AclList {
			var newAcl *x1model.RedisAcl
			if oriAcl, has := userACLs[acl.AccountName]; has {
				passwdChanged := aclPasswdChanged(oriAcl, acl, aclParams.Action)
				authorityChanged := aclAuthorityChanged(oriAcl, acl, aclParams.Action)
				if !passwdChanged && !authorityChanged {
					continue
				}
				// default user 如果密码发生更新，保留老acl并创建新acl；其他情况，直接原地更新
				if oriAcl.AccountName == x1model.DefaultACLUser && passwdChanged {
					oriAcl.Status = x1model.ACLStatusToDelete
					updatedAclList = append(updatedAclList, oriAcl)
					newAcl = createNewAclModel(appId, engine, acl, aclParams.Action, oriAcl)
				} else {
					newAcl = updateAclModel(oriAcl, acl, aclParams.Action)
				}
			} else {
				newAcl = createNewAclModel(appId, engine, acl, aclParams.Action, nil)
			}

			updatedAclList = append(updatedAclList, newAcl)
		}
	} else {
		// delete
		for _, acl := range aclParams.AclList {
			if oriAcl, has := userACLs[acl.AccountName]; has {
				oriAcl.Status = x1model.ACLStatusToDelete
				updatedAclList = append(updatedAclList, oriAcl)
			}
		}
	}

	return updatedAclList
}

// 判断是否是需要修改密码的action
func isAclChangePasswdAction(action string) bool {
	return action == iface.ActionCreate ||
		action == iface.ActionUpdate ||
		action == iface.ActionModifyPasswd
}

// 判断是否是需要修改authority的action
func isAclChangeAuthorityAction(action string) bool {
	return action == iface.ActionCreate ||
		action == iface.ActionUpdate ||
		action == iface.ActionModifyAuthority
}

// 比较数据库里的acl与acl更新参数的passwd
func aclPasswdChanged(aclModel *x1model.RedisAcl, acl *iface.RedisAcl, action string) bool {
	if !isAclChangePasswdAction(action) {
		return false
	}
	// 密码一样也要触发更新
	return true
}

// 比较数据库里的acl与acl更新参数的authority配置
func aclAuthorityChanged(aclModel *x1model.RedisAcl, acl *iface.RedisAcl, action string) bool {
	if !isAclChangeAuthorityAction(action) {
		return false
	}

	return aclModel.AllowedCmds != acl.AllowedCmds ||
		aclModel.AllowedSubCmds != acl.AllowedSubCmds ||
		aclModel.KeyPatterns != acl.KeyPatterns
}

// 创建新的acl model
func createNewAclModel(appId, engine string, acl *iface.RedisAcl, action string,
	oriAclModel *x1model.RedisAcl) *x1model.RedisAcl {
	var version int
	var encryptedPasswd, allowedCmds, allowedSubCmds, keyPatterns string

	if oriAclModel != nil {
		version = oriAclModel.Version + 1
		encryptedPasswd = oriAclModel.Password
		allowedCmds = oriAclModel.AllowedCmds
		allowedSubCmds = oriAclModel.AllowedSubCmds
		keyPatterns = oriAclModel.KeyPatterns
	}

	if isAclChangePasswdAction(action) {
		encryptedPasswd, _ = crypto_utils.EncryptKey(acl.Password)
	}

	if isAclChangeAuthorityAction(action) {
		allowedCmds = acl.AllowedCmds
		allowedSubCmds = acl.AllowedSubCmds
		keyPatterns = acl.KeyPatterns
	}

	return &x1model.RedisAcl{
		AppID:          appId,
		Version:        version,
		Engine:         engine,
		AccountName:    acl.AccountName,
		Password:       encryptedPasswd,
		AllowedCmds:    allowedCmds,
		AllowedSubCmds: allowedSubCmds,
		KeyPatterns:    keyPatterns,
		Properties:     acl.Properties,
		Status:         x1model.ACLStatusToCreate,
		CreateAt:       time.Now(),
		UpdateAt:       time.Now(),
	}
}

// 更新acl model
func updateAclModel(aclModel *x1model.RedisAcl, acl *iface.RedisAcl, action string) *x1model.RedisAcl {
	aclModel.Version++

	if isAclChangePasswdAction(action) {
		encryptedPasswd, _ := crypto_utils.EncryptKey(acl.Password)
		aclModel.Password = encryptedPasswd
	}

	if isAclChangeAuthorityAction(action) {
		aclModel.AllowedCmds = acl.AllowedCmds
		aclModel.AllowedSubCmds = acl.AllowedSubCmds
		aclModel.KeyPatterns = acl.KeyPatterns
	}

	aclModel.Status = x1model.ACLStatusToUpdate
	aclModel.UpdateAt = time.Now()

	return aclModel
}

func processBuildMetaForSetProxyAcl(ctx context.Context, app *x1model.Application, params *iface.Parameters) error {
	aclParams := params.AclParam
	ProxyAcls, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND status not in ?",
		app.AppId, []string{x1model.ACLStatusDeleted, x1model.ACLStatusToDelete})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get acl fail",
			logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	ProxyAclsToRedis := acl.ProxyAclsToRedisAcls(ProxyAcls)
	updatedProxyAcls := acl.RedisAclsToProxyAcls(
		updateAclListWithParams(app.AppId, "proxy", ProxyAclsToRedis, aclParams))
	// 更新acl
	if err := x1model.ProxyAclSave(ctx, updatedProxyAcls); err != nil {
		resource.LoggerTask.Warning(ctx, "save acl fail",
			logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}
