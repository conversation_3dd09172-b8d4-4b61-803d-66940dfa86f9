package buildmeta

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/unittest"
)

func TestGetTotalProxyCount(t *testing.T) {
	unittest.UnitTestInit(3)
	specification.MustLoadConf(context.Background())
	type args struct {
		ctx    context.Context
		params *GetTotalProxyCountParams
	}
	tests := []struct {
		name      string
		args      args
		wantCount int
		wantErr   bool
	}{
		{
			name: "test-redis-engine",
			args: args{
				ctx: context.Background(),
				params: &GetTotalProxyCountParams{
					App:        &x1model.Application{},
					Engine:     "redis",
					ShardCount: 10,
					NodeType:   "cache.n1.small",
					ForceSpecs: nil,
				},
			},
			wantCount: 10,
			wantErr:   false,
		},
		{
			name: "test-pegadb-engine",
			args: args{
				ctx: context.Background(),
				params: &GetTotalProxyCountParams{
					App: &x1model.Application{
						Type: "cluster",
					},
					Engine:     "pegadb",
					ShardCount: 10,
					NodeType:   "pega.g4s1.micro",
					ForceSpecs: nil,
				},
			},
			wantCount: 20,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCount, err := GetTotalProxyCount(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTotalProxyCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotCount != tt.wantCount {
				t.Errorf("GetTotalProxyCount() gotCount = %v, want %v", gotCount, tt.wantCount)
			}
		})
	}
}

func TestGetDeployConfVersion(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "test-get-deploy-version",
			args: args{
				ctx: context.Background(),
			},
			want:    "20220216190700",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := GetDeployConfVersion(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeployConfVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
