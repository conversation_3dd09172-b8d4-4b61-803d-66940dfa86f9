/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_create_readonly_instance.go
 * <AUTHOR>
 * @date 2022/05/16 20:15:39
 * @brief build meta for creating readonly instance
 *
 **/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessBuildMetaForDeletingRoGroup SCS标准版只读组删除过程中的处理
func ProcessBuildMetaForDeletingRoGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.DeleteRoGroupParams.ReadonlyGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 获取只读组所有实例并标记清除
	for _, i := range roGroup.CacheInstances {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				if i.Uuid == node.ResourceId {
					node.RoGroupStatus = x1model.RoInstDeleting
					node.Status = x1model.NodeOrProxyStatusToDelete
				}
			}
		}
	}

	// blb标记需要删除
	for _, b := range app.BLBs {
		if b.BlbId == roGroup.BlbID {
			b.Status = x1model.BLBStatusToDelete
		}
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}
