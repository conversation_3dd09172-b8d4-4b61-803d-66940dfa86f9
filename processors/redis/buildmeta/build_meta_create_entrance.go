/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/29 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_create_entrance.go
 * <AUTHOR>
 * @date 2022/12/29 17:30:29
 * @brief
 *
 **/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessBuildMetaForCreatingEntrance 生成一个统一入口的blb
func ProcessBuildMetaForCreatingEntrance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := createEntranceBlbModel(ctx, app, param); err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}

func createEntranceBlbModel(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:             app.AppId,
		Name:              app.AppId,
		VpcId:             app.VpcId,
		SubnetId:          param.CreateEntranceParams.BlbSubnetId,
		Type:              x1model.BLBTypeAppEntrance,
		IpType:            x1model.Ipv4,
		Status:            x1model.BLBStatusToCreate,
		BgwGroupId:        param.CreateEntranceParams.BgwGroup.BgwGroupId,
		BgwGroupMode:      param.CreateEntranceParams.BgwGroup.BgwGroupMode,
		BgwGroupExclusive: param.CreateEntranceParams.BgwGroup.BgwGroupExclusive,
		MasterAZ:          param.CreateEntranceParams.BgwGroup.MasterAz,
		SlaveAZ:           param.CreateEntranceParams.BgwGroup.SlaveAz,
	})
	return nil
}
