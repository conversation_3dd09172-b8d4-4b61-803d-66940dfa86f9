/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func processSelfHealClusterShard(ctx context.Context, clusterId string, unhealthNodeIds []string, masterAlone bool) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()
	cluster, err := x1model.ClusterGetByClusterId(ctx, clusterId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster from x1 db failed", logit.String("cluster_id", clusterId), logit.Error("error", err))
	}
	for _, node := range cluster.Nodes {
		for _, unhealthNodeId := range unhealthNodeIds {
			if node.NodeId == unhealthNodeId {
				if node.Role == x1model.RoleTypeMaster && !masterAlone {
					return fmt.Errorf("cannot do master self heal in cluster %s", cluster.ClusterId)
				}
				if node.Status == x1model.NodeOrProxyStatusInUse {
					node.Status = x1model.NodeOrProxyStatusToFakeDelete
				}
				break
			}
		}
	}
	if err := x1model.ClusterSave(ctx, []*x1model.Cluster{cluster}); err != nil {
		resource.LoggerTask.Warning(ctx, "save cluster failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessSelfHealClusterFromCsmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load app failed", logit.Error("error", err))
		return err
	}
	replicas := []*iface.Replica{}
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		resource.LoggerTask.Warning(ctx, "parse app.replicas failed", logit.Error("error", err))
		return err
	}
	masterAlone := (len(replicas) == 1 && replicas[0].Count == 1)

	clusterUnhealthNodesMap := map[string][]string{}

	for _, unid := range param.SelfHealFromCsmaster.NodeShortIDs {
		found := false
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				if node.NodeShortID == int(unid) {
					if _, has := clusterUnhealthNodesMap[cluster.ClusterId]; !has {
						clusterUnhealthNodesMap[cluster.ClusterId] = []string{node.NodeId}
					} else {
						clusterUnhealthNodesMap[cluster.ClusterId] = append(clusterUnhealthNodesMap[cluster.ClusterId], node.NodeId)
					}
					found = true
					break
				}
			}
			if found {
				break
			}
		}
	}

	g := gtask.Group{}
	for cid, unids := range clusterUnhealthNodesMap {
		cid := cid
		unids := unids
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processSelfHealClusterShard(ctx, cid, unids, masterAlone)
			})
		})
	}
	_, err = g.Wait()
	return err
}
