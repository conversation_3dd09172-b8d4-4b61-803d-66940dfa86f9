/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessBuildMetaForBackup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 校验集群状态为12，跳过
	clusterStatus, err := csmaster.CsmasterOp().GetClusterStatus(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster status error", logit.Error("error", err))
		return err
	}

	if clusterStatus == csmaster.CsmasterStatusCreatedFailed {
		resource.LoggerTask.Trace(ctx, "cluster has created failed", logit.Error("error", err), logit.String("cluster", app.AppId))
		return nil
	}

	backup, err := x1model.BackupGetByCond(ctx, "app_id = ? AND status = ? AND expire_at > ?",
		app.AppId, x1model.BackupStatusDoing, time.Now())
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get backup task fail", logit.String("appId", app.AppId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if backup != nil {
		if backup.BackupID == param.BackupParam.BackupID {
			resource.LoggerTask.Warning(ctx, "repeated backup request",
				logit.String("appId", app.AppId),
				logit.String("backupId", backup.BackupID))
			return nil
		}
		// 新的备份任务需要开始，将原doing的backup记录更新为error
		backup.Status = x1model.BackupStatusError
		if err := x1model.BackupsSave(ctx, []*x1model.Backup{backup}); err != nil {
			resource.LoggerTask.Error(ctx, "update backup task status fail", logit.Error("error", err))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	backup = &x1model.Backup{
		AppId:    app.AppId,
		BackupID: param.BackupParam.BackupID,
		CreateAt: time.Now(),
		UpdateAt: time.Now(),
		ExpireAt: time.Now().Add(7 * 24 * time.Hour),
		Status:   x1model.BackupStatusDoing,
		Type:     param.BackupParam.BackupType,
		Comment:  param.BackupParam.Comment,
	}
	for _, item := range param.BackupParam.Items {
		cluster, err := FindClusterByShortID(ctx, app, item.ShardShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "cluster not found", logit.Error("error", err))
			return err
		}
		node, err := FindNodeByShortID(ctx, cluster, item.NodeShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}
		access := item.Access
		if len(access) == 0 {
			access = fmt.Sprintf("bos://scs-backup-rdb-bucket/expiration/%d_%d_%s",
				app.AppShortID, node.NodeShortID, backup.BackupID)
		}
		backup.Items = append(backup.Items, &x1model.BackupItem{
			AppId:     app.AppId,
			BackupID:  param.BackupParam.BackupID,
			ClusterId: cluster.ClusterId,
			NodeID:    node.NodeId,
			CreateAt:  time.Now(),
			UpdateAt:  time.Now(),
			Access:    access,
			Status:    x1model.BackupStatusDoing,
		})
	}
	if err := x1model.BackupsSave(ctx, []*x1model.Backup{backup}); err != nil {
		resource.LoggerTask.Error(ctx, "save backup task fail", logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}
