/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_create_readonly_instance.go
 * <AUTHOR>
 * @date 2022/05/16 20:15:39
 * @brief build meta for creating readonly instance
 *
 **/

package buildmeta

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessBuildMetaForDeletingRoInstance SCS标准版只读实例删除过程中的处理
func ProcessBuildMetaForDeletingRoInstance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 获取只读组需要删除的实例并标记清除
	for _, i := range param.DeleteRoInstParams.RoInstances {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				if i.ResourceID == node.ResourceId {
					// 标记删除
					node.RoGroupStatus = x1model.RoInstDeleting
					node.Status = x1model.NodeOrProxyStatusToDelete
				}
			}
		}
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}
