/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/04/26, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessBuildMetaModifyProxy
// 生成OP用Proxy的数据结构
func ProcessBuildMetaModifyProxy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	maxProxyIdx, err := util.GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get max proxy index failed", logit.Error("error", err))
		return err
	}

	var opItfList []*x1model.Interface
	zoneOpProxyMap := map[string][]*x1model.Proxy{}
	for _, itf := range app.Interfaces {
		if !strings.Contains(itf.InterfaceId, "-itfop-") {
			continue
		}
		opItfList = append(opItfList, itf)
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToDelete || proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			if _, has := zoneOpProxyMap[proxy.LogicZone]; !has {
				zoneOpProxyMap[proxy.LogicZone] = []*x1model.Proxy{proxy}
			} else {
				zoneOpProxyMap[proxy.LogicZone] = append(zoneOpProxyMap[proxy.LogicZone], proxy)
			}
		}
	}

	maxItfIdx, err := util.GetMaxInterfaceIdx(ctx, opItfList)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get max interface index failed", logit.Error("error", err))
		return err
	}

	for _, targetOpProxyInfo := range param.TargetOpProxyInfos {
		var proxyList []*x1model.Proxy
		if _, has := zoneOpProxyMap[targetOpProxyInfo.Zone]; has {
			proxyList = zoneOpProxyMap[targetOpProxyInfo.Zone]
		}
		azone, found := zoneMapper(targetOpProxyInfo.Zone, true)
		if !found {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", targetOpProxyInfo.Zone))
			return cerrs.Errorf("not found azone for lzone %s", targetOpProxyInfo.Zone)
		}
		diff := targetOpProxyInfo.Count - len(proxyList)
		firstItf := app.Interfaces[0]
		if diff > 0 {
			for i := 0; i < diff; i++ {
				maxItfIdx++
				maxProxyIdx++
				newItf := &x1model.Interface{
					InterfaceId:    app.AppId + "-itfop-" + strconv.Itoa(maxItfIdx),
					AppId:          app.AppId,
					Engine:         x1model.EngineBDRPProxy,
					EngineVersion:  app.Interfaces[0].EngineVersion,
					Port:           app.Port,
					Status:         x1model.ClusterStatusInUse,
					CreateTime:     time.Now(),
					UpdateTime:     time.Now(),
					StoreType:      x1model.StoreTypeDRAM,
					Spec:           firstItf.Spec,
					DestSpec:       firstItf.DestSpec,
					Cpu:            firstItf.Cpu,
					ActualCpu:      firstItf.ActualCpu,
					MemSize:        firstItf.MemSize,
					ActualMemSize:  firstItf.ActualMemSize,
					DiskSize:       firstItf.DiskSize,
					ActualDiskSize: firstItf.ActualDiskSize,
					SysDiskSize:    firstItf.SysDiskSize,
				}
				newItf.Proxys = append(newItf.Proxys, &x1model.Proxy{
					ProxyId:       newItf.InterfaceId + "." + strconv.Itoa(maxProxyIdx),
					InterfaceId:   newItf.InterfaceId,
					AppId:         app.AppId,
					Engine:        newItf.Engine,
					EngineVersion: newItf.EngineVersion,
					Port:          newItf.Port,
					Region:        app.Interfaces[0].Proxys[0].Region,
					LogicZone:     targetOpProxyInfo.Zone,
					Azone:         azone,
					VpcId:         app.Interfaces[0].Proxys[0].VpcId,
					SubnetId:      targetOpProxyInfo.SubnetID,
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
					McpackPort:    newItf.Port + 1,
					StatPort:      22222,
				})
				app.Interfaces = append(app.Interfaces, newItf)
			}
		} else if diff < 0 {
			for i := 0; i < -diff; i++ {
				proxyList[i].Status = x1model.NodeOrProxyStatusToDelete
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
