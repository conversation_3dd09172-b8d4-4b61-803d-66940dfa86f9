/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/16 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file build_meta_create_readonly_instance.go
 * <AUTHOR>
 * @date 2022/05/16 20:15:39
 * @brief build meta for creating readonly instance
 *
 **/

package buildmeta

import (
	"context"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessBuildMetaForCreatingRoInstance SCS标准版只读组创建过程中，生成对应的X1数据结构
// 2. 生成n个Ronode
func ProcessBuildMetaForCreatingRoInstance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if roGroup.Status == x1model.RoGroupCreating {
		// 创建blb model
		if err := createBlbModel(ctx, app, param, roGroup); err != nil {
			return err
		}
	}

	// 只读组处于updating 状态下的处理
	// 创建n个roNode
	if err := createReadonlyInstanceModel(ctx, app, param); err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}

func createBlbModel(ctx context.Context, app *x1model.Application, param *iface.Parameters, roGroup *csmaster_model_interface.ReadonlyGroup) error {
	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:             app.AppId,
		Name:              app.AppId,
		VpcId:             app.VpcId,
		SubnetId:          roGroup.SubnetID,
		Type:              x1model.BLBTypeAppReadOnly,
		IpType:            x1model.Ipv4,
		Status:            x1model.BLBStatusToCreate,
		BgwGroupId:        roGroup.BgwGroupID,
		BgwGroupMode:      "",
		BgwGroupExclusive: roGroup.BgwGroupExclusive,
		MasterAZ:          "",
		SlaveAZ:           "",
		RoGroupID:         param.CreateRoInstParams.RoGroupID,
	})
	return nil
}

func createReadonlyInstanceModel(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get max node index failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("max node index %d", maxNodeIdx))

	if len(app.Clusters) == 1 && app.Clusters[0].ClusterId == app.AppId+"-0" {
		for _, replica := range param.CreateRoInstParams.RoInstances {
			maxNodeIdx++
			azone, found := zoneMapper(replica.AvailabilityZone, true)
			if !found {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("not found azone for lzone %s", replica.AvailabilityZone))
				return cerrs.Errorf("not found azone for lzone %s", replica.AvailabilityZone)
			}
			role := "slave"
			app.Clusters[0].RoNodes = append(app.Clusters[0].RoNodes, &x1model.RoNode{
				NodeId:        app.Clusters[0].ClusterId + "." + strconv.Itoa(maxNodeIdx),
				ClusterId:     app.Clusters[0].ClusterId,
				AppId:         app.AppId,
				Engine:        app.Clusters[0].Engine,
				EngineVersion: app.Clusters[0].EngineVersion,
				Port:          app.Clusters[0].Port,
				Region:        param.CreateRoInstParams.Region,
				LogicZone:     replica.AvailabilityZone,
				Azone:         azone,
				Role:          role,
				VpcId:         app.VpcId,
				SubnetId:      replica.SubnetID,
				Pool:          param.CreateRoInstParams.Pool,
				XagentPort:    x1model.DefaultXagentPort,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       util.DefaultBaseDir,
				RoGroupID:     param.CreateRoInstParams.RoGroupID,
				RoGroupStatus: x1model.RoInstCreating, // 定义节点在组中的状态
				RoGroupWeight: int(replica.Weight),
			})
		}
	}
	return nil
}
