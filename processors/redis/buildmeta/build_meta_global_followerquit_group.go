/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* build_meta_global_followerquit_group.go */
/*
modification history
--------------------
2022/05/30 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package buildmeta

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessBuildMetaForFollowerQuitGlobalGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Warning(ctx, "app has no group id")
		return errors.Errorf("app has no group id")
	}
	// 更新metaserver信息
	app.Status = x1model.AppStatusFollowerQuit

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}
	return nil
}
