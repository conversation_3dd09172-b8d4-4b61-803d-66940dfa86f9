/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/01, by wang<PERSON><PERSON>(wang<PERSON><EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package buildmeta

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// 将标准版的 cluster & node 标记为 todelete
func markShardTodelete(ctx context.Context, app *x1model.Application) error {
	for _, cluster := range app.Clusters {
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			break
		}

		for _, node := range cluster.Nodes {
			node.ClusterId = fmt.Sprintf("%s-0_todelete", app.AppId)
			node.Status = x1model.NodeOrProxyStatusToFakeDelete
		}

		cluster.ClusterId = fmt.Sprintf("%s-0_todelete", app.AppId)
	}
	return nil
}

// 获取 region
func getRegionFromNode(ctx context.Context, app *x1model.Application) (string, error) {
	region := ""
	for _, node := range app.Clusters[0].Nodes {
		if node.Region != "" {
			region = node.Region
			break
		}
	}
	if region == "" {
		return region, cerrs.Errorf("not found region for node")
	}
	return region, nil
}

// addShards 在元数据中增加新分片
func addShardsForModifyType(ctx context.Context, app *x1model.Application, param *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get max node index failed", logit.Error("error", err))
		return err
	}

	// 标准版的 cluster
	firstCluster := app.Clusters[0]
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("to add %d shards", param.ShardCount))

	// 增加 cluster(从 0 开始计算)
	for i := 0; i < param.ShardCount; i++ {
		app.Clusters = append(app.Clusters, &x1model.Cluster{
			ClusterId:     app.AppId + "-" + strconv.Itoa(i),
			AppId:         app.AppId,
			Engine:        firstCluster.Engine,
			EngineVersion: firstCluster.EngineVersion,
			Port:          8080,
			Status:        x1model.ClusterStatusInUse,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			DestSpec:      param.NodeType,
			StoreType:     x1model.StoreTypeDRAM,
		})
	}

	// 增加 Node
	for _, cluster := range app.Clusters {
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}
		for _, replica := range param.Replicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for i := 0; i < replica.Count; i++ {
				maxNodeIdx++
				role := "slave"
				if replica.Role == "master" && i == 0 {
					role = "master"
				}
				cluster.Nodes = append(cluster.Nodes, &x1model.Node{
					NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
					ClusterId:     cluster.ClusterId,
					AppId:         app.AppId,
					Engine:        cluster.Engine,
					EngineVersion: cluster.EngineVersion,
					Port:          cluster.Port,
					Region:        firstCluster.Nodes[0].Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					Role:          role,
					VpcId:         firstCluster.Nodes[0].VpcId,
					SubnetId:      replica.SubnetIDs[0],
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
				})
				resource.LoggerTask.Trace(ctx, fmt.Sprintf("add node %s", cluster.Nodes[len(cluster.Nodes)-1].NodeId))
			}
		}
	}
	return nil
}

// addProxies 添加代理节点到应用程序中，并根据分布策略计算每个可用区的代理节点数量
// ctx: 上下文信息
// app: 应用程序对象指针，包含应用程序的基本信息和已创建的接口列表
// parameters: 参数结构体指针，包含引擎类型、节点类型、强制规格、用户ID、VPC ID等信息
// zoneMapper: 可用区转换函数，将逻辑可用区转换为物理可用区
// 返回值：error，错误信息或nil
func addProxiesForModifyType(ctx context.Context, app *x1model.Application, parameters *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Interfaces) > 0 {
		resource.LoggerTask.Warning(ctx, "interfaces has been created")
		return nil
	}

	// Proxy分布策略如下
	// Proxy总数量与ShardCount等同
	// InterfaceCount * 可用区数量（当可用区数量为1时，视为2） = Proxy总数量,
	// 当可用区数量为1时，一个Interface中有2个Proxy，当多个可用区时，Interface中Proxy数量与可用区数量相同
	// 当出现不能整除时，向上去整
	totalProxyCount, err := GetTotalProxyCount(ctx, &GetTotalProxyCountParams{
		App:        app,
		Engine:     x1model.EngineRedis,
		ShardCount: parameters.ShardCount,
		NodeType:   parameters.NodeType,
		ForceSpecs: parameters.ForceSpecs,
		UserID:     app.UserId,
		VpcID:      app.VpcId,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "get total proxy count failed", logit.Error("error", err))
		return err
	}

	// Replicas
	proxyCountPerAz := 1
	zoneCount := len(parameters.Replicas)
	if zoneCount < 2 {
		proxyCountPerAz = 2
		zoneCount = 2
	}
	itfCount := totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		itfCount++
	}

	for i := 0; i < itfCount; i++ {
		app.Interfaces = append(app.Interfaces, &x1model.Interface{
			InterfaceId: app.AppId + "-itf-" + strconv.Itoa(i),
			AppId:       app.AppId,
			Engine:      x1model.EngineBDRPProxy,
			// Redis version, 此处没有使用
			EngineVersion: "6.0",
			Port:          app.Port,
			Status:        x1model.ClusterStatusInUse,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			DestSpec:      "proxy.n1.small",
			StoreType:     x1model.StoreTypeDRAM,
		})
	}

	maxProxyId := 0
	for _, itf := range app.Interfaces {
		for _, replica := range parameters.Replicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
				return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for i := 0; i < proxyCountPerAz; i++ {
				maxProxyId++
				itf.Proxys = append(itf.Proxys, &x1model.Proxy{
					ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyId),
					InterfaceId:   itf.InterfaceId,
					AppId:         app.AppId,
					Engine:        itf.Engine,
					EngineVersion: itf.EngineVersion,
					Port:          itf.Port,
					Region:        app.Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					VpcId:         app.VpcId,
					SubnetId:      replica.SubnetIDs[0],
					XagentPort:    x1model.DefaultXagentPort,
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
					McpackPort:    itf.Port + 1,
					StatPort:      22222,
				})
			}
		}
	}
	return nil
}

// getMetaserverID 获取metaserver的ID，返回一个字符串和错误信息
func getMetaserverID(ctx context.Context) (string, error) {
	metaType := "scs"
	metaClusters, err := x1model.MetaClustersGetAllByCond(ctx, "meta_cluster_id NOT LIKE ? AND type = ?", "%_Global_%", metaType)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get meta cluster failed", logit.Error("err", err))
		return "", err
	}
	if len(metaClusters) == 0 {
		return "", nil
	}
	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)
	return metaClusters[r.Intn(len(metaClusters))].MetaClusterID, nil
}

// syncRedisACLToProxy 将redis中的ACL同步到proxy中，如果在proxy中没有则创建，如果已经存在则不做任何操作
// ctx: 上下文信息，context.Context类型
// app: 应用实例指针，*x1model.Application类型
// 返回值：error类型，nil表示同步成功，非nil表示同步失败
func syncRedisACLToProxy(ctx context.Context, app *x1model.Application) error {
	redisACLList, err := x1model.RedisAclAllByCond(ctx, "app_id = ? AND status <> ?", app.AppId, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis acl list fail", logit.String("appId", app.AppId), logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	proxyACLList, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND status <> ?", app.AppId, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy acl list fail", logit.String("appId", app.AppId), logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	curProxyACLs := map[string]*x1model.ProxyAcl{}
	for _, acl := range proxyACLList {
		curProxyACLs[acl.AccountName] = acl
	}

	// create
	var toCreateACLList []*x1model.ProxyAcl
	for _, racl := range redisACLList {
		_, has := curProxyACLs[racl.AccountName]
		if has {
			continue
		}

		toCreateACLList = append(toCreateACLList, &x1model.ProxyAcl{
			AppID:          racl.AppID,
			AccountName:    racl.AccountName,
			CreateAt:       racl.CreateAt,
			UpdateAt:       racl.UpdateAt,
			Version:        racl.Version,
			Engine:         "proxy",
			Password:       racl.Password,
			AllowedCmds:    racl.AllowedCmds,
			AllowedSubCmds: racl.AllowedSubCmds,
			KeyPatterns:    racl.KeyPatterns,
			Properties:     racl.Properties,
			Status:         racl.Status,
		})
	}

	if len(toCreateACLList) == 0 {
		resource.LoggerTask.Notice(ctx, "no redis acl sync to proxy", logit.String("appId", app.AppId))
		return nil
	}

	// 更新acl
	if err := x1model.ProxyAclSave(ctx, toCreateACLList); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy acl fail", logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func deleteRedisACL(ctx context.Context, app *x1model.Application) error {
	redisACLList, err := x1model.RedisAclAllByCond(ctx, "app_id = ? AND status <> ?", app.AppId, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis acl list fail", logit.String("appId", app.AppId), logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// update
	var toUpdateACLList []*x1model.RedisAcl
	for _, acl := range redisACLList {
		acl.Status = x1model.ACLStatusDeleted
		acl.UpdateAt = time.Now()
		toUpdateACLList = append(toUpdateACLList, acl)
	}

	if len(toUpdateACLList) == 0 {
		resource.LoggerTask.Notice(ctx, "no redis acl update", logit.String("appId", app.AppId))
		return nil
	}

	// 更新acl
	if err := x1model.RedisAclSave(ctx, toUpdateACLList); err != nil {
		resource.LoggerTask.Warning(ctx, "save redis acl fail", logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func syncVpcPasswordFreeConfig(ctx context.Context, app *x1model.Application) error {
	// standalone auto-auth-address
	var confRedisRecords []*csmaster_model_interface.ConfRecordList
	if err := resource.CsmasterModel.GetAllByCond(ctx, &confRedisRecords, "cluster_id = ? AND conf_name = ?", app.AppShortID, "auto-auth-address"); err != nil {
		resource.LoggerTask.Warning(ctx, "get auto-auth-address config from csmaster failed", logit.Error("error", err))
		return fmt.Errorf("get auto-auth-address config from csmaster failed: %w", err)
	}
	if len(confRedisRecords) == 0 {
		return nil
	}
	confValue := confRedisRecords[0].Value

	// cluster scsproxy_auto_auth_address
	var confProxyRecords []*csmaster_model_interface.ConfRecordList
	if err := resource.CsmasterModel.GetAllByCond(ctx, &confProxyRecords, "cluster_id = ? AND conf_name = ?", app.AppShortID, "scsproxy_auto_auth_address"); err != nil {
		resource.LoggerTask.Warning(ctx, "get scsproxy_auto_auth_address config from csmaster failed", logit.Error("error", err))
		return fmt.Errorf("get scsproxy_auto_auth_address config from csmaster failed: %w", err)
	}
	if len(confProxyRecords) > 0 {
		if confValue == confProxyRecords[0].Value {
			return nil
		}
		confProxyRecords[0].Value = confValue
	} else {
		// sync config
		confProxyRecords = append(confProxyRecords, &csmaster_model_interface.ConfRecordList{
			ClusterID:  int(app.AppShortID),
			ConfName:   "scsproxy_auto_auth_address",
			ConfModule: 2,
			Value:      confValue,
			Effected:   1,
		})
	}

	if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, confProxyRecords); err != nil {
		resource.LoggerTask.Warning(ctx, "save config to csmaster failed", logit.Error("error", err))
		return fmt.Errorf("save config to csmaster failed: %w", err)
	}
	return nil
}

// ProcessModifyType 处理修改类型的任务，包括更新应用信息、添加代理等操作。
// ctx: 上下文信息，context.Context类型。
// teu: *workflow.TaskExecUnit，包含任务执行单元的相关信息。
// 返回值：error，如果发生错误则返回非nil错误，否则返回nil。
func ProcessModifyType(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 1 Parse param
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 2 Get model
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	// 3 更改 Type 为 cluster
	app.Type = x1model.AppTypeCluster
	// Standalone 使用 RedisAuth，Cluster 使用 ClientAuth, 以下操作用于幂等操作
	clientAuth := csCluster.ClientAuth
	if len(csCluster.RedisAuth) > 0 {
		// 将 RedisAuth 的密码移到 ClientAuth 上
		clientAuth = csCluster.RedisAuth
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Version:     5001, // 修改 proxy hashtag 配置时需要 version 是 5001
			RedisAuth:   "",
			ClientAuth:  clientAuth,
			ClusterType: "cluster", // console 会根据此值判断是否获取只读实例组列表
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"client_auth", "redis_auth"},
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update csmaster cluster version failed", logit.String("appId", app.AppId))
		return err
	}

	// 4 添加 app.InnerPort
	app.InnerPort = 8080

	// 5 需要将旧 cluster & node 进行标记为 todelete
	if len(app.Clusters) == 1 {
		if err := markShardTodelete(ctx, app); err != nil {
			resource.LoggerTask.Error(ctx, "mark shard todelete error", logit.Error("error", err))
			return err
		}
	}

	// 6 填充 Region(创建标准时没有设置 App.Region)
	if app.Region == "" {
		region, err := getRegionFromNode(ctx, app)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get region error", logit.Error("error", err))
			return err
		}
		app.Region = region
	}

	// zoneMapper
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}

	// 7 Add Cluster
	if param.ShardCount > (len(app.Clusters) - 1) {
		if err := addShardsForModifyType(ctx, app, param, zoneMapper); err != nil {
			resource.LoggerTask.Error(ctx, "add shards error", logit.Error("error", err))
			return err
		}
	}

	// Replicas
	destReplicas := param.Replicas
	raw, err := json.Marshal(destReplicas)
	if err != nil {
		return err
	}
	app.DestReplicas = string(raw)

	// 8 Add Proxy
	if err := addProxiesForModifyType(ctx, app, param, zoneMapper); err != nil {
		return err
	}

	// 9 设置 local metaserver
	app.AppShortID = int(csCluster.Id)
	if app.LocalMetaserver == "" {
		app.LocalMetaserver, err = getMetaserverID(ctx)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get metaserver id failed", logit.Error("err", err))
			return err
		}
	}

	// 10 将标准版 Redis acl 同步到集群版 Proxy
	err = syncRedisACLToProxy(ctx, app)
	if err != nil {
		return err
	}

	// 11 将标准版 Redis acl 删除
	err = deleteRedisACL(ctx, app)
	if err != nil {
		return err
	}

	// 12 将标准版免密访问配置同步到集群版
	err = syncVpcPasswordFreeConfig(ctx, app)
	if err != nil {
		return err
	}

	// save
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
