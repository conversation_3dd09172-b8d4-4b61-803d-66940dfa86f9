package handover

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	fixReplInterval = 1 * time.Second // 执行复制关系修复操作的时间间隔, 1s
)

func ProcessTryFixReplication(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID),
			logit.Error("error", err))
		return err
	}
	password, err := util.GetRedisDecryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password error", logit.String("app_id", app.AppId),
			logit.Error("error", err))
		return err
	}

	retryTimer := time.NewTimer(fixReplInterval)
	defer retryTimer.Stop()
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-retryTimer.C:
			err := FixClusterReplication(ctx, app, cluster, password)
			if err == nil {
				resource.LoggerTask.Notice(ctx, "fix replication success", logit.String("cluster_id", cluster.ClusterId))
				return nil
			}
			resource.LoggerTask.Warning(ctx, "fix replication failed", logit.Error("error", err))
			retryTimer.Reset(fixReplInterval)
		}
	}
}

// FixClusterReplication, 修复指定cluster的复制关系，幂等地给所有node和ronode发送slaveof master命令
func FixClusterReplication(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, password string) error {
	if len(app.AppGroupID) != 0 {
		return FixClusterReplicationGlobal(ctx, app, cluster, password)
	}
	return FixClusterReplicationLocal(ctx, app, cluster, password)
}

func FixClusterReplicationGlobal(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, password string) error {
	if app == nil {
		return fmt.Errorf("app is nil")
	}
	if cluster == nil {
		return fmt.Errorf("cluster is nil")
	}
	if len(app.AppGroupID) == 0 {
		return nil
	}
	masterNode := util.GetMasterNodeInUse(ctx, cluster)
	if masterNode == nil {
		// 若cluster中不含master节点，认为元数据异常，返回错误；热活从地域天然没有主节点，所以该函数不适用于热活从地域
		resource.LoggerTask.Warning(ctx, "master node is nil", logit.String("cluster_id", cluster.ClusterId))
		return fmt.Errorf("master node is nil")
	}

	resource.LoggerTask.Notice(ctx, "start to send slaveof master via global api",
		logit.String("app_group_id", app.AppGroupID),
		logit.String("app_id", app.AppId),
		logit.String("user_id", app.UserId),
		logit.String("shard_global_id", cluster.GlobalID),
		logit.String("master_node", masterNode.NodeId))
	resp, err := gmaster.GlobalMasterOp().SlaveOfMaster(ctx, &gmaster.SlaveOfMasterParams{
		AppGroupID:    app.AppGroupID,
		AppID:         app.AppId,
		UserID:        app.UserId,
		ShardGlobalID: cluster.GlobalID,
		NewNodeID:     masterNode.NodeId,
	})
	if resp == nil {
		resource.LoggerTask.Warning(ctx, "response from global api SlaveOfMaster is empty", logit.Error("error", err))
		return fmt.Errorf("%s", "response from global api SlaveOfMaster is empty")
	}
	resource.LoggerTask.Notice(ctx, "finish to send slaveof master via global api",
		logit.String("request_id", resp.RequestID),
		logit.String("code", resp.Code),
		logit.String("message", resp.Message),
		logit.Error("error", err))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to call global api SlaveOfMaster", logit.Error("error", err))
		return err
	}
	if resp.Message != "SUCCESS" {
		resource.LoggerTask.Warning(ctx, "fail to call global api SlaveOfMaster, message not SUCCESS", logit.String("message", resp.Message))
		return fmt.Errorf("%s", resp.Message)
	}
	return nil
}

func FixClusterReplicationLocal(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, password string) error {
	if app == nil {
		return fmt.Errorf("app is nil")
	}
	if cluster == nil {
		return fmt.Errorf("cluster is nil")
	}
	if len(app.AppGroupID) != 0 {
		return nil
	}
	masterNode := util.GetMasterNodeInUse(ctx, cluster)
	if masterNode == nil {
		// 若cluster中不含master节点，认为元数据异常，返回错误；热活从地域天然没有主节点，所以该函数不适用于热活从地域
		resource.LoggerTask.Warning(ctx, "master node is nil", logit.String("cluster_id", cluster.ClusterId))
		return fmt.Errorf("master node is nil")
	}

	g := gtask.Group{
		Concurrent:    25, // 与切换步骤保持一致
		AllowSomeFail: true,
	}
	// 传播上层ctx到gtask中，避免上层ctx被取消后，gtask内部任务一直执行
	// NOTE:
	// task内监听了ctx的Done，若ctx有超时且被取消，则gtask内部任务也会被取消；
	// 注意，该超时仅对未开始执行的task有效，已开始执行的task不受影响，需要在task执行的func内监听ctx的Done
	ctx = g.WithContext(ctx)
	totalCount := 0
	for _, node := range x1model.FetchAllNodesOfCluster(ctx, cluster) {
		if node.NodeId == masterNode.NodeId {
			continue
		}
		node := node
		totalCount++
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				if err := util.SetSlaveOfMasterIdempotent(ctx, node.FloatingIP, node.Port, password, masterNode.Ip, cast.ToString(masterNode.Port)); err != nil {
					resource.LoggerTask.Warning(ctx, "send slaveof master fail",
						logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
						logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)),
						logit.Error("error", err))
					return err
				}
				resource.LoggerTask.Notice(ctx, "send slaveof master suc", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
					logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)))
				return nil
			})
		})
	}
	succCount, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur in fixing replication",
			logit.String("cluster_id", cluster.ClusterId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "fix replication success",
		logit.String("cluster_id", cluster.ClusterId),
		logit.Int("totalCount", totalCount),
		logit.Int("succount", succCount))
	return nil
}
