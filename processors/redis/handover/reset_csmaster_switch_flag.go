package handover

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessResetCsmasterSwitchFlag(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 在上一步获取的数据库数据中，找到与csmaster传入参数一致的cluster（shard）和node（旧主）
	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID), logit.Error("error", err))
		return err
	}
	r, err := resource.RedisClient.Get(ctx, "scs-shardswitch-skip"+teu.TaskID+cluster.ClusterId).Result()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis key error", logit.Error("error", err))
		return nil
	}
	if r == "1" {
		req := &csmaster.ResetShardFailoverFlagParam{
			UserID:       app.UserId,
			AppID:        app.AppId,
			ShardShortID: cluster.ClusterShortID,
			AppShortID:   app.AppShortID,
		}
		if err := csmaster.CsmasterOp().ResetShardFailoverFlag(ctx, req); err != nil {
			resource.LoggerTask.Warning(ctx, "reset failover flag error", logit.Error("error", err))
		}
	}
	return nil
}
