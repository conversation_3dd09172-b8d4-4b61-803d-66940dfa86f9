/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* slave_of.go */
/*
modification history
--------------------
2022/08/22 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package handover

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessLocalSlaveOf 处理本地从节点的操作，包括获取应用信息、获取ACL密码、遍历集群并进行从节点操作
// ctx: 上下文对象，context.Context类型
// teu: 任务执行单元，*workflow.TaskExecUnit类型，不能为nil
// 返回值：error类型，表示操作是否成功，如果成功则返回nil，否则返回相应的错误信息
func ProcessLocalSlaveOf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}

	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		return errors.Errorf("get acl fail,err:%s", err.Error())
	}

	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	g := gtask.Group{
		Concurrent: 25,
	}
	for _, cluster := range app.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}

		cluster := cluster
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processLocalClusterSlaveOf(ctx, cluster.ClusterId, password)
			})
		})
	}
	_, err = g.Wait()
	return err
}

func processLocalClusterSlaveOf(ctx context.Context, clusterId string, adminPass string) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()
	cluster, err := x1model.ClusterGetByClusterId(ctx, clusterId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster fail", logit.Error("dbError", err))
		return err
	}
	var masterNode *x1model.Node = nil
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.Role == x1model.RoleTypeMaster &&
			(node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate) {
			if masterNode != nil {
				return errors.Errorf("cluster:%s has more than 1 master node", cluster.ClusterId)
			}
			masterNode = node
			break
		}
	}
	if masterNode == nil {
		return errors.Errorf("cluster:%s has no master node", cluster.ClusterId)
	}
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}
		if node.NodeId != masterNode.NodeId && node.Role == x1model.RoleTypeSlave {
			err = util.SetSlaveOf(ctx, node.FloatingIP, node.Port, adminPass, masterNode.Ip, cast.ToString(masterNode.Port))
			if err != nil {
				resource.LoggerTask.Warning(ctx, "send slaveof master fail", logit.Error("Error", err))
				continue
			}
		}
	}
	return nil
}

func ProcessModifyAZLocalSlaveOf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}

	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		return errors.Errorf("get acl fail,err:%s", err.Error())
	}

	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	g := gtask.Group{
		Concurrent: 25,
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processModifyAZLocalClusterSlaveOf(ctx, cluster.ClusterId, password)
			})
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "process local cluster slave of fail", logit.Error("error", err))
		return err
	}
	return nil
}

func processModifyAZLocalClusterSlaveOf(ctx context.Context, clusterId string, adminPass string) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()
	cluster, err := x1model.ClusterGetByClusterId(ctx, clusterId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster fail", logit.Error("dbError", err))
		return err
	}
	var masterNode *x1model.Node = nil
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.Role == x1model.RoleTypeMaster &&
			(node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete) {
			if masterNode != nil {
				return errors.Errorf("cluster:%s has more than 1 master node", cluster.ClusterId)
			}
			masterNode = node
			break
		}
	}
	if masterNode == nil {
		return errors.Errorf("cluster:%s has no master node", cluster.ClusterId)
	}
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}
		if node.NodeId != masterNode.NodeId && node.Role == x1model.RoleTypeSlave {
			err = util.SetSlaveOf(ctx, node.FloatingIP, node.Port, adminPass, masterNode.Ip, cast.ToString(masterNode.Port))
			if err != nil {
				resource.LoggerTask.Warning(ctx, "send slaveof master fail", logit.Error("Error", err))
				continue
			}
		}
	}
	return nil
}
