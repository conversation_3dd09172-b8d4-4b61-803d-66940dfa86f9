package handover

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	certification = "fbbef3155aa64306ba4df82721fc039a"
	timeout       = 5
	taskTimeout   = 120
)

func ProcessOldMasterClientKill(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID), logit.Error("error", err))
		return err
	}
	oldMasterNode, err := util.FindNodeByShortID(ctx, cluster, param.ShardSwitchFromCsmsater.MasterShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find node with short id %d", param.ShardSwitchFromCsmsater.MasterShortID), logit.Error("error", err))
		return err
	}

	// deployClient := deploy.NewDefaultClient()
	cmds := []string{"CLIENT", "KILL", "TYPE", "normal"}
	asyncTasks := make(map[string]*xagent.TaskContext, 0)
	// mapTaskIdToNode := make(map[string]*x1model.Node, 0)
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.String("app id:", app.AppId),
			logit.Error("error", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	for _, node := range cluster.Nodes {
		if node.NodeId == oldMasterNode.NodeId {
			continue
		}
		xagentAddr := xagent.Addr{
			Host: node.FloatingIP,
			Port: cast.ToInt32(node.XagentPort),
		}
		redisCmdReq := xagent.AsyncRequest{
			Addr:   &xagentAddr,
			Action: "rediscmd",
			Params: util.RedisCmdParams{
				Host:          oldMasterNode.Ip,
				Port:          int32(oldMasterNode.Port),
				Password:      password,
				Timeout:       timeout,
				Certification: certification,
				Cmds:          cmds,
			},
			TimeoutSec: taskTimeout,
		}
		asyncCtx := xagent.Instance().DoAsync(ctx, &redisCmdReq)
		taskID := cast.ToString(cluster.ClusterId) + ":" + cast.ToString(node.NodeId)
		asyncTasks[taskID] = asyncCtx
	}

	for taskID, task := range asyncTasks {
		taskIds := strings.Split(taskID, ":")
		clusterID := taskIds[0]
		nodeID := taskIds[1]
		_, err = task.Wait()
		if err != nil {
			resource.LoggerTask.Notice(ctx, "kill client on node fail", logit.String("cluster id:", clusterID),
				logit.String("node id:", nodeID), logit.Error("error", err))
		}
	}

	return nil
}

// ProcessMasterNodeKillOldClient kill掉master上的client
func ProcessMasterNodeKillOldClient(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var toExchangeBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			toExchangeBlb = blb
		}
	}

	if toExchangeBlb == nil {
		resource.LoggerTask.Notice(ctx, "blb not found", logit.String("appId", app.AppId))
		return errors.New("blb not found")
	}
	// 如果是容器实例并且有service publish endpoint，则执行换绑blb的时候旧链接已经断开了
	if app.ResourceType == "container" && toExchangeBlb.ServicePublishEndpoint != "" && toExchangeBlb.ResourceUserId != "" {
		resource.LoggerTask.Notice(ctx, "container instance has service publish endpoint, skip kill client", logit.String("blbId", toExchangeBlb.BlbId))
		return nil
	}

	cmds := []string{"CLIENT", "KILL", "TYPE", "normal"}
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.String("app id:", app.AppId),
			logit.Error("error", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	// 失败后退化为blb摘除
	gg := gtask.Group{
		Concurrent:    25,
		AllowSomeFail: true,
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role != x1model.RoleTypeMaster || node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: cast.ToInt32(node.XagentPort),
				},
				Action: "rediscmd",
				Params: util.RedisCmdParams{
					Host:          "127.0.0.1",
					Port:          int32(node.Port),
					Password:      password,
					Timeout:       timeout,
					Certification: certification,
					Cmds:          cmds,
				},
				TimeoutSec: taskTimeout,
			}
			gg.Go(func() error {
				_, err := xagent.Instance().DoAsync(ctx, req).Wait()
				if err != nil {
					resource.LoggerTask.Warning(ctx, "client kill fail",
						logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
						logit.Error("err", err))
				}
				return err
			})
		}
	}

	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "client kill fail",
			logit.Error("err", err))
		return nil
	}

	return nil
}

// ProcessProxyKillOldClient kill掉proxy上的client
func ProcessProxyKillOldClient(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var toExchangeBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			toExchangeBlb = blb
		}
	}

	if toExchangeBlb == nil {
		resource.LoggerTask.Notice(ctx, "blb not found", logit.String("appId", app.AppId))
		return errors.New("blb not found")
	}
	// 如果是容器实例并且有service publish endpoint，则执行换绑blb的时候旧链接已经断开了
	if app.ResourceType == "container" && toExchangeBlb.ServicePublishEndpoint != "" && toExchangeBlb.ResourceUserId != "" {
		resource.LoggerTask.Notice(ctx, "container instance has service publish endpoint, skip kill client", logit.String("blbId", toExchangeBlb.BlbId))
		return nil
	}

	cmds := []string{"CLIENT", "KILL", "TYPE", "normal"}
	var passwd string
	defaultAcl, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusInUse)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	if len(defaultAcl) > 0 && defaultAcl[0].Password != "" {
		passwd, _ = crypto_utils.DecryptKey(defaultAcl[0].Password)
	}

	// 忽略失败,失败后会退化为blb摘除
	gg := gtask.Group{Concurrent: 10,
		AllowSomeFail: true}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: cast.ToInt32(proxy.XagentPort),
				},
				Action: "rediscmd",
				Params: util.RedisCmdParams{
					Host:          "127.0.0.1",
					Port:          int32(proxy.Port),
					Password:      passwd,
					Timeout:       timeout,
					Certification: certification,
					Cmds:          cmds,
				},
				TimeoutSec: taskTimeout,
			}
			gg.Go(func() error {
				_, err := xagent.Instance().DoAsync(ctx, req).Wait()
				if err != nil {
					resource.LoggerTask.Warning(ctx, "client kill fail",
						logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
						logit.Error("err", err))
				}
				return err
			})
		}
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "client kill fail",
			logit.Error("err", err))
		return nil
	}

	return nil
}

func ProcessProxyClientKillForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 从task参数中获取故障的proxy节点，整理到map中
	faultProxyShortIDs := make(map[int]struct{})
	for _, proxyShortID := range param.SelfHealFromCsmaster.ProxyShortIDs {
		proxy, err := buildmeta.FindProxyByShortIDInApp(ctx, app, proxyShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "proxy not found", logit.Error("error", err))
			return err
		}
		faultProxyShortIDs[proxy.ProxyShortID] = struct{}{}
	}
	if len(faultProxyShortIDs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no fault proxy, skip client kill for traffic removal", logit.String("appId", app.AppId))
		return nil
	}

	// 执行client kill
	cmds := []string{"CLIENT", "KILL", "TYPE", "normal"}
	var passwd string
	defaultAcl, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusInUse)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	if len(defaultAcl) > 0 && defaultAcl[0].Password != "" {
		passwd, _ = crypto_utils.DecryptKey(defaultAcl[0].Password)
	}

	// 遍历proxy节点，状态为 toFakeDelete、toDelete 且 在故障节点字典中，则执行client kill
	gg := gtask.Group{
		Concurrent:    10,
		AllowSomeFail: true,
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if _, ok := faultProxyShortIDs[proxy.ProxyShortID]; !ok {
				resource.LoggerTask.Notice(ctx, "proxy not in faultProxyShortIDs",
					logit.Int("proxyShortID", proxy.ProxyShortID),
					logit.String("faultProxyShortIDs", base_utils.Format(faultProxyShortIDs)),
				)
				continue
			}
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToFakeDelete, x1model.NodeOrProxyStatusToDelete:
				req := &xagent.AsyncRequest{
					Addr: &xagent.Addr{
						Host: proxy.FloatingIP,
						Port: cast.ToInt32(proxy.XagentPort),
					},
					Action: "rediscmd",
					Params: util.RedisCmdParams{
						Host:          "127.0.0.1",
						Port:          int32(proxy.Port),
						Password:      passwd,
						Timeout:       timeout,
						Certification: certification,
						Cmds:          cmds,
					},
					TimeoutSec: taskTimeout,
				}
				gg.Go(func() error {
					_, err := xagent.Instance().DoAsync(ctx, req).Wait()
					if err != nil {
						resource.LoggerTask.Warning(ctx, "send client kill fail",
							logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
							logit.Error("err", err))
					}
					resource.LoggerTask.Notice(ctx, "send client kill success",
						logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)))
					return nil
				})
			}
		}
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "client kill fail",
			logit.Error("err", err))
		return nil
	}

	return nil
}

func ProcessRoNodeClientKillForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 从task参数中获取故障的ronode节点，整理到map中
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	faultRoNodeShortIDs := make(map[int]struct{})
	for _, uhi := range param.UnHealthShards {
		for _, nodeShortID := range uhi.NodeShortIDs {
			roNode, _, err := buildmeta.FindRoNodeByShortIDInApp(ctx, app, nodeShortID)
			if err == nil && roNode != nil {
				faultRoNodeShortIDs[roNode.NodeShortID] = struct{}{}
			}
		}
	}
	if len(faultRoNodeShortIDs) <= 0 {
		resource.LoggerTask.Trace(ctx, "no fault ronode, skip send client kill")
		return nil
	}

	// 执行client kill
	cmds := []string{"CLIENT", "KILL", "TYPE", "normal"}
	var passwd string
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	if acl != nil && len(acl.Password) != 0 {
		passwd, _ = crypto_utils.DecryptKey(acl.Password)
	}

	// 遍历ronode节点，状态为 toFakeDelete、toDelete 且 在故障节点字典中，则执行client kill
	gg := gtask.Group{
		Concurrent:    10,
		AllowSomeFail: true,
	}

	for _, cluster := range app.Clusters {
		for _, roNode := range cluster.RoNodes {

			if _, ok := faultRoNodeShortIDs[roNode.NodeShortID]; !ok {
				resource.LoggerTask.Notice(ctx, "node not in faultRoNodeShortIDs",
					logit.Int("roNodeShortID", roNode.NodeShortID),
					logit.String("faultRoNodeShortIDs", base_utils.Format(faultRoNodeShortIDs)),
				)
				continue
			}

			switch roNode.Status {
			case x1model.NodeOrProxyStatusToFakeDelete, x1model.NodeOrProxyStatusToDelete:
				req := &xagent.AsyncRequest{
					Addr: &xagent.Addr{
						Host: roNode.FloatingIP,
						Port: cast.ToInt32(roNode.XagentPort),
					},
					Action: "rediscmd",
					Params: util.RedisCmdParams{
						Host:          "127.0.0.1",
						Port:          int32(roNode.Port),
						Password:      passwd,
						Timeout:       timeout,
						Certification: certification,
						Cmds:          cmds,
					},
					TimeoutSec: taskTimeout,
				}
				gg.Go(func() error {
					_, err := xagent.Instance().DoAsync(ctx, req).Wait()
					if err != nil {
						resource.LoggerTask.Warning(ctx, "send client kill fail",
							logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
							logit.Error("err", err))
					}
					resource.LoggerTask.Notice(ctx, "send client kill success",
						logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)))
					return nil
				})

			}
		}
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "client kill fail",
			logit.Error("err", err))
		return nil
	}

	return nil
}
