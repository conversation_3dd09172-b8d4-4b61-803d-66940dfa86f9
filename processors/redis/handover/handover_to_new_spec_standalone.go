/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package handover

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func handoverAndChangeBlbRs(ctx context.Context, cluster *x1model.Cluster, app *x1model.Application, password string) error {
	var toDeleteMaster *x1model.Node
	var toCreateMaster *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			switch node.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = node
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				toDeleteMaster = node
			}
		}
	}
	if toCreateMaster == nil || toDeleteMaster == nil {
		return nil
	}

	resource.LoggerTask.Notice(ctx, fmt.Sprintf("begin to switch master from old %s to new %s",
		toDeleteMaster.NodeId, toCreateMaster.NodeId))

	var blbIds []string
	appBlbList := make([]*x1model.BLB, 0)
	var userID string
	for _, blbInst := range app.BLBs {
		if len(blbInst.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = blbInst.ResourceUserId
		}
		if blbInst.Type == x1model.BLBTypeNormal {
			blbIds = append(blbIds, blbInst.BlbId)
		}
		if blbInst.Type == x1model.BLBTypeApp {
			appBlbList = append(appBlbList, blbInst)
		}
	}
	if cluster.EngineVersion != "2.8" {
		if err := util.SetClientPause(ctx, toDeleteMaster.FloatingIP, toDeleteMaster.Port, password, 5*time.Second); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("sent client pause to old master %s(%s:%d) failed",
				toDeleteMaster.NodeId, toDeleteMaster.FloatingIP, toDeleteMaster.Port), logit.Error("error", err))
			return err
		}
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("sent client pause to old master %s(%s:%d) succ",
		toDeleteMaster.NodeId, toDeleteMaster.FloatingIP, toDeleteMaster.Port))

	if err := util.SetSlaveOf(ctx, toCreateMaster.FloatingIP, toCreateMaster.Port, password, "no", "one"); err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("sent slaveof no one to new master %s(%s:%d) failed",
			toCreateMaster.NodeId, toCreateMaster.FloatingIP, toCreateMaster.Port), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("sent slaveof no one to new master %s(%s:%d) failed",
		toCreateMaster.NodeId, toCreateMaster.FloatingIP, toCreateMaster.Port))

	var uuid_create string
	var uuid_delete string
	if app.ResourceType == "container" {
		uuid_create = toCreateMaster.ContainerId
		uuid_delete = toDeleteMaster.ContainerId
	} else {
		uuid_create = toCreateMaster.ResourceId
		uuid_delete = toDeleteMaster.ResourceId
	}
	if len(blbIds) != 0 {
		if err := blb.Instance().BindRs(ctx, &blb.BindRsParam{
			UserID: userID,
			BLBIDs: blbIds,
			Rss:    []*blb.Rs{{UUID: uuid_create, Weight: 1, Port: int32(toCreateMaster.Port)}},
		}); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("bind new master %s to blbs failed", toCreateMaster.NodeId),
				logit.String("new_master_uuid", uuid_create),
				logit.String("blbs", base_utils.Format(blbIds)),
				logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("bind new master %s to blbs succ", toCreateMaster.NodeId),
			logit.String("new_master_uuid", uuid_create),
			logit.String("blbs", base_utils.Format(blbIds)))

		if err := blb.Instance().UnbindRs(ctx, &blb.UnbindRsParam{
			UserID: userID,
			BLBIDs: blbIds,
			UUIDs:  []string{uuid_delete},
		}); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("unbind old master %s to blbs failed", toDeleteMaster.NodeId),
				logit.String("old_master_uuid", uuid_delete),
				logit.String("blbs", base_utils.Format(blbIds)),
				logit.Error("error", err))
			return err
		}
	}

	if len(appBlbList) != 0 {
		// 更新appblb 绑定rs
		for _, blb := range appBlbList {
			if err := blbv2.Instance().BindRs(ctx, &blbv2.BindRsParams{
				UserID:  userID,
				BLBID:   blb.BlbId,
				IPGroup: blb.IPGroupID,
				Rss: []*blbv2.Rs{
					{
						UUID:   uuid_create,
						IP:     toCreateMaster.Ip,
						Port:   toCreateMaster.Port,
						Weight: 1,
					},
				},
			}); err != nil {
				resource.LoggerTask.Warning(ctx, "bind app blb backend fail", logit.String("bind:", toCreateMaster.NodeId))
				return err
			}

			resource.LoggerTask.Notice(ctx, fmt.Sprintf("bind new master %s to blbs succ", toCreateMaster.NodeId),
				logit.String("new_master_uuid", uuid_create),
				logit.String("blbs", base_utils.Format(appBlbList)))
			toDeleteRs := fmt.Sprintf("%s:%d", toDeleteMaster.Ip, toDeleteMaster.Port)
			if err := blbv2.Instance().UnbindRs(ctx, &blbv2.UnbindRsParams{
				UserID:     userID,
				BLBID:      blb.BlbId,
				IPGroup:    blb.IPGroupID,
				MemberList: []string{toDeleteRs},
			}); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind app blb backend fail", logit.String("unbind:", toDeleteMaster.NodeId))
				return err
			}
		}
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("bind old master %s to blbs succ", toDeleteMaster.NodeId),
		logit.String("old_master_uuid", uuid_delete),
		logit.String("blbs", base_utils.Format(blbIds)))
	return nil
}

// ProcessHandoverToNewSpecStandalone 用于标准版变配中的切主逻辑
func ProcessHandoverToNewSpecStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) != 0 {
		return nil
	}
	for _, cluster := range app.Clusters {
		var toCreateMaster *x1model.Node
		var masterId string
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate && node.Role == x1model.RoleTypeMaster {
				toCreateMaster = node
			}
			if node.Status != x1model.NodeOrProxyStatusToCreate && node.Role == x1model.RoleTypeMaster {
				masterId = node.NodeId
			}
		}
		if toCreateMaster == nil {
			continue
		}
		if err := processHandoverStandaloneShard(ctx, &handoverClusterShardParams{
			appId:            app.AppId,
			clusterId:        cluster.ClusterId,
			oldMasterId:      masterId,
			isManual:         true,
			candidatesIds:    nil,
			toCreateMasterId: toCreateMaster.NodeId,
			taskId:           teu.TaskID,
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "process handover standalone shard fail", logit.Error("error", err))
			return err
		}
	}
	if err := util.SetSwitchableFlagsAfterModifySpecHandover(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "set switchable flags fail", logit.Error("error", err))
		return err
	}
	return nil
}
