/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package handover

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	modifyingEvent = "modifying_cluster"
	switchingEvent = "switching_cluster"
	replacingEvent = "replacing_cluster"
)

type ReplicationInfo struct {
	AvailabilityZone string `json:"availabilityZone"`
	SubnetId         string `json:"subnetId"`
	ReplicationNum   int64  `json:"replicationNum"`
	IsMaster         int32  `json:"isMaster"`
}

type AzDeployItem struct {
	Name        string `json:"name"`
	SubnetId    string `json:"subnetId"`
	MasterAz    bool   `json:"masterAz"`
	Replication int64  `json:"replication"`
}

func IsReplaceToDeleteNode(node *x1model.Node) bool {
	if node.Status == x1model.NodeOrProxyStatusToDelete || node.Status == x1model.NodeOrProxyStatusToFakeDelete {
		return true
	}
	return false
}

func ProcessHandoverForDeleteReplicas(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	return handoverToDeleteMaster(ctx, app, true)
}

func handoverToDeleteMaster(ctx context.Context, app *x1model.Application, isManualSwitch bool) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+app.AppId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()

	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	for _, cluster := range app.Clusters {
		var oldMaster *x1model.Node
		needHandover := false
		candidates := []*x1model.Node{}
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && IsReplaceToDeleteNode(node) && len(cluster.Nodes) > 1 {
				needHandover = true
				oldMaster = node
			}
			if node.Role == x1model.RoleTypeSlave && node.Status == x1model.NodeOrProxyStatusInUse {
				candidates = append(candidates, node)
			}
		}
		resource.LoggerTask.Trace(ctx, "handover info",
			logit.Bool("need_handover", needHandover),
			logit.String("candidates", base_utils.Format(candidates)),
			logit.String("old_master", base_utils.Format(oldMaster)))

		if needHandover {
			req := util.MasterSwitchReq{
				Acl:                   defaultAcl,
				ClusterInfo:           cluster,
				App:                   app,
				ManualCandidateSlaves: candidates,
				IsManualSwitch:        true,
				UseForbidWrite:        KernalSupportForbidWrite(ctx, cluster, app, oldMaster),
			}
			newMaster, err := util.MasterSwitchShard(ctx, &req)
			if err != nil {
				return err
			}
			resource.LoggerTask.Trace(ctx, "switch master succ", logit.String("new_master", base_utils.Format(newMaster)))
			newMaster.Role = x1model.RoleTypeMaster
			oldMaster.Role = x1model.RoleTypeSlave
			creq := &csmaster.SaveInstancesParams{
				AppID:  app.AppId,
				UserID: app.UserId,
				Models: []*csmaster.CsmasterInstance{{
					Uuid:              newMaster.ResourceId,
					CacheInstanceType: int32(util.GetCacheInstanceType(newMaster.Engine, newMaster.Role)),
					MasterRedis:       util.GetMasterRedis(app.Clusters[0], newMaster),
					SlaverRedis:       util.GetSlaveRedis(app.Clusters[0], newMaster),
				}, {
					Uuid:              oldMaster.ResourceId,
					CacheInstanceType: int32(util.GetCacheInstanceType(oldMaster.Engine, oldMaster.Role)),
					MasterRedis:       util.GetMasterRedis(app.Clusters[0], oldMaster),
					SlaverRedis:       util.GetSlaveRedis(app.Clusters[0], oldMaster),
				}},
				RequiredFields: []string{"master_redis", "slaver_redis"},
			}
			if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
				resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
				return err
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func getReplicasMasterZoneChange(ctx context.Context, app *x1model.Application, param *iface.Parameters) (needHandover bool, newZone string, err error) {
	replicas := []*iface.Replica{}
	destReplicas := param.Replicas
	if err = json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		return
	}
	var raw []byte
	raw, err = json.Marshal(destReplicas)
	if err != nil {
		return
	}
	app.DestReplicas = string(raw)

	var oldZone string
	for _, replica := range replicas {
		if replica.Role == x1model.RoleTypeMaster {
			oldZone = replica.Zone
		}
	}

	for _, replica := range destReplicas {
		if replica.Role == x1model.RoleTypeMaster {
			newZone = replica.Zone
		}
	}

	if len(oldZone) != 0 && len(newZone) != 0 && oldZone != newZone {
		needHandover = true
		return
	}
	needHandover = false
	return
}

func ProcessManualHandoverNewZone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	needHandover, newZone, err := getReplicasMasterZoneChange(ctx, app, param)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "replica format invalid", logit.Error("err", err))
		return err
	}
	if !needHandover {
		return nil
	}
	for _, cluster := range app.Clusters {
		var oldMaster *x1model.Node
		candidates := []*x1model.Node{}
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				oldMaster = node
			}
			if node.LogicZone == newZone {
				candidates = append(candidates, node)
			}
		}
		resource.LoggerTask.Trace(ctx, "manual handover info",
			logit.String("candidates", base_utils.Format(candidates)),
			logit.String("old_master", base_utils.Format(oldMaster)))

		req := util.MasterSwitchReq{
			Acl:                   defaultAcl,
			ClusterInfo:           cluster,
			App:                   app,
			ManualCandidateSlaves: candidates,
			IsManualSwitch:        true,
			UseForbidWrite:        KernalSupportForbidWrite(ctx, cluster, app, oldMaster),
		}
		newMaster, err := util.MasterSwitchShard(ctx, &req)
		if err != nil {
			return err
		}
		resource.LoggerTask.Trace(ctx, "switch master succ",
			logit.String("old_master", base_utils.Format(oldMaster)),
			logit.String("new_master", base_utils.Format(newMaster)))
		if err := util.HandoverUpdateCsmaster(ctx, app.UserId, app.Clusters[0], oldMaster, newMaster); err != nil {
			resource.LoggerTask.Warning(ctx, "update handover info to csmaster failed", logit.Error("err", err))
			return err
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessHandoverStandaloneShardFromCsmaster will deal with handover
// 处理csmaster（replace_switch_redis_master_manager.cc）发送过来的切换任务
func ProcessHandoverStandaloneShardFromCsmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 事件监控"开始事件"处理
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.New("teu is nilptr")
	}

	// 进行故障切换
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 在上一步获取的数据库数据中，找到与csmaster传入参数一致的cluster（shard）和node（旧主）
	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID), logit.Error("error", err))
		return err
	}
	node, err := util.FindNodeByShortID(ctx, cluster, param.ShardSwitchFromCsmsater.MasterShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find node with short id %d", param.ShardSwitchFromCsmsater.MasterShortID), logit.Error("error", err))
		return err
	}
	return processHandoverStandaloneShard(ctx, &handoverClusterShardParams{
		appId:            app.AppId,
		clusterId:        cluster.ClusterId,
		oldMasterId:      node.NodeId,
		isManual:         false,
		candidatesIds:    nil,
		toCreateMasterId: "",
		taskId:           teu.TaskID,
	})
}

// processHandoverStandaloneShard will shard-failover
// 实际执行standalone（single shard）级别的故障自愈
func processHandoverStandaloneShard(ctx context.Context, params *handoverClusterShardParams) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+params.appId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err := x1model.ApplicationGetByAppId(ctx, params.appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}

	cluster, err := x1model.ClusterGetByClusterId(ctx, params.clusterId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster error", logit.Error("error", err))
		return err
	}
	var oldMaster *x1model.Node
	for _, node := range cluster.Nodes {
		if (len(params.oldMasterId) == 0 && node.Role == x1model.RoleTypeMaster) || node.NodeId == params.oldMasterId {
			oldMaster = node
			break
		}
	}
	if oldMaster == nil {
		return errors.New("master node not found")
	}

	if oldMaster.Role != x1model.RoleTypeMaster {
		resource.LoggerTask.Warning(ctx, "old master role not match expect, begin to check csmaster", logit.String("old_master", oldMaster.NodeId))
		// 数据库中的master角色不符合预期，尝试从csmaster获取角色进行更正
		if err := tryCorrectMasterRole(ctx, params, cluster); err != nil {
			return err
		}
		// 认为已经切换过
		if oldMaster.Role != x1model.RoleTypeMaster {
			resource.LoggerTask.Warning(ctx, "old master role not match expect, skip", logit.String("old_master", oldMaster.NodeId))
			if err := markSwitchSkipped(ctx, params); err != nil {
				return err
			}
			return nil
		}
		resource.LoggerTask.Warning(ctx, "old master role match expect, correct by csmaster", logit.String("old_master", oldMaster.NodeId))
	}

	var password string
	if defaultACL != nil && len(defaultACL.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(defaultACL.Password)
	}
	if !params.isManual {
		if secondaryConfirmation(ctx, oldMaster, cluster, nil, password) {
			resource.LoggerTask.Warning(ctx, "secondary confirm node alive")
			if err := markSwitchSkipped(ctx, params); err != nil {
				return err
			}
			return nil
		}
	}

	/*
		processHandoverStandaloneShard 此方法是例行切换和手动切换公共方法
		此处在二次探测之后增加分支，仅例行切换推送事件
	*/

	isFirstTime := false
	taskDetail, err := resource.TaskOperator.GetTaskDetail(ctx, params.taskId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get task detail failed", logit.Error("error", err))
		return err
	}
	if taskDetail == nil {
		err := errors.New("taskDetail is nil")
		resource.LoggerTask.Warning(ctx, "get task detail failed", logit.Error("error", err))
		return err
	}
	stepName := taskDetail.Step
	stepNameSplit := strings.Split(stepName, "__")
	if len(stepNameSplit) != 2 {
		err := errors.New("task step name error")
		resource.LoggerTask.Warning(ctx, "step name error", logit.Error("error", err))
		return err
	}
	if stepNameSplit[1] == "0" {
		isFirstTime = true
	}

	resource.LoggerTask.Notice(ctx, "task detail", logit.String("isFirstTime :", base_utils.Format(isFirstTime)),
		logit.String("taskDetail :", base_utils.Format(taskDetail)))

	if !params.isManual {
		if isFirstTime {
			resource.LoggerTask.Notice(ctx, "add start event", logit.String("params :", base_utils.Format(params)))
			if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, replacingEvent); err != nil {
				resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start failed", logit.Error("error", err))
			}
			resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start success")
		}
	}

	candidates := fillCandidates(cluster, params)

	req := util.MasterSwitchReq{
		Acl:                   defaultACL,
		ClusterInfo:           cluster,
		App:                   app,
		ManualCandidateSlaves: candidates,
		IsManualSwitch:        params.isManual,
		UseForbidWrite:        KernalSupportForbidWrite(ctx, cluster, app, oldMaster),
	}
	newMaster, err := util.MasterSwitchShard(ctx, &req)
	if err != nil {
		// 例行切换失败，记录切换失败事件
		if !params.isManual {
			if isFirstTime {
				resource.LoggerTask.Notice(ctx, "add end event", logit.String("params :", base_utils.Format(params)))
				if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, replacingEvent, event.FailoverFailedEvent); err != nil {
					resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end failed", logit.Error("error", err))
				}
				resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end success")
			}
		}
		resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" switch master failed, retry:"+stepNameSplit[1])
		return err
	}

	// 例行切换成功，记录切换成功事件
	if !params.isManual {
		if !isFirstTime {
			resource.LoggerTask.Notice(ctx, "add start event", logit.String("params :", base_utils.Format(params)))
			if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, replacingEvent); err != nil {
				resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start failed", logit.Error("error", err))
			}
			resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start success")
		}

		resource.LoggerTask.Notice(ctx, "add end event", logit.String("params :", base_utils.Format(params)))
		if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, replacingEvent, event.FailoverSuccessEvent); err != nil {
			resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end failed", logit.Error("error", err))
		}
		resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end success")
	}

	resource.LoggerTask.Notice(ctx, "switch master succ", logit.String("new_master_id", newMaster.NodeId))
	if err := util.HandoverUpdateCsmaster(ctx, app.UserId, cluster, oldMaster, newMaster); err != nil {
		resource.LoggerTask.Warning(ctx, "update csmaster error", logit.Error("error", err))
		return err
	}
	if err := x1model.ClusterSave(ctx, []*x1model.Cluster{cluster}); err != nil {
		resource.LoggerTask.Warning(ctx, "save cluster error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessStandaloneManualFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	return processHandoverStandaloneShard(ctx, &handoverClusterShardParams{
		appId:            param.ManualFailover.AppID,
		clusterId:        param.ManualFailover.ShardID,
		oldMasterId:      "",
		isManual:         true,
		candidatesIds:    param.ManualFailover.Candidates,
		toCreateMasterId: "",
		taskId:           teu.TaskID,
	})
}

func HasMasterInCandidates(ctx context.Context, cluster *x1model.Cluster, candidates []string) (*x1model.Node, bool) {
	for _, candidateNodeID := range candidates {
		for _, node := range cluster.Nodes {
			if node.NodeId == candidateNodeID && node.Role == x1model.RoleTypeMaster {
				return node, true
			}
		}
	}
	return nil, false
}

// ProcessStandaloneManualSwitchMasterSlave 标准版，用户手动切换主从
func ProcessStandaloneManualSwitchMasterSlave(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if param.ManualSwitchMasterSlave == nil || len(param.ManualSwitchMasterSlave) == 0 {
		err := fmt.Errorf("manual failover shard list is nil")
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	var errS error
	for i, _ := range param.ManualSwitchMasterSlave {
		shardToSwitch := param.ManualSwitchMasterSlave[i]
		for _, cluster := range app.Clusters {
			if cluster.ClusterId == shardToSwitch.ShardID {
				// 检查是否需要切换
				masterNode, has := HasMasterInCandidates(ctx, cluster, shardToSwitch.Candidates)
				if has {
					resource.LoggerTask.Error(ctx, "there is master in candidates already and skip switching",
						logit.String("cluster", cluster.ClusterId),
						logit.String("master node", masterNode.NodeId),
						logit.String("candidates", base_utils.Format(shardToSwitch.Candidates)))
					break
				}
				errS = processHandoverStandaloneShard(ctx, &handoverClusterShardParams{
					appId:            shardToSwitch.AppID,
					clusterId:        shardToSwitch.ShardID,
					oldMasterId:      "",
					isManual:         true,
					candidatesIds:    shardToSwitch.Candidates,
					toCreateMasterId: "",
					taskId:           teu.TaskID,
				})
				if errS != nil {
					resource.LoggerTask.Error(ctx, "process handover standalone shard fail",
						logit.String("clusterId", cluster.ClusterId),
						logit.String("candidates", base_utils.Format(shardToSwitch.Candidates)),
						logit.Error("error", errS))
					return errS
				}
				return nil
			}
		}
	}
	return nil
}

// ProcessHandoverStandaloneForModifySpec 标准版热活组变配中的切新主流程
func ProcessHandoverStandaloneForModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		return nil
	}

	// 热活从节点直接返回
	if len(app.AppGroupID) != 0 && !metaserver.IsGlobalLeader(ctx, app) {
		return nil
	}
	for _, cluster := range app.Clusters {
		var toCreateMaster *x1model.Node
		var masterId string
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate && node.Role == x1model.RoleTypeMaster {
				toCreateMaster = node
			}
			if node.Status != x1model.NodeOrProxyStatusToCreate && node.Role == x1model.RoleTypeMaster {
				masterId = node.NodeId
			}
		}
		if toCreateMaster == nil {
			continue
		}

		defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
		if err != nil && !x1model.IsNotFound(err) {
			resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
			return err
		}
		var password string
		if defaultACL != nil && len(defaultACL.Password) != 0 {
			password, _ = crypto_utils.DecryptKey(defaultACL.Password)
		}

		replicationInfo, err := util.GetReplicationInfo(ctx, toCreateMaster.FloatingIP, toCreateMaster.Port, password)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get replication info error", logit.Error("error", err))
			return err
		}
		if replicationInfo.Role == "master" {
			continue
		}

		if err := processHandoverStandaloneShard(ctx, &handoverClusterShardParams{
			appId:            app.AppId,
			clusterId:        cluster.ClusterId,
			oldMasterId:      masterId,
			isManual:         true,
			candidatesIds:    nil,
			toCreateMasterId: toCreateMaster.NodeId,
			taskId:           teu.TaskID,
		}); err != nil {
			return err
		}
	}

	return nil
}

func ProcessHandoverUpdateAzDeployInfo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.New("teu is nilptr")
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	// 获取自愈切主前的 az_deploy_info 和 dest_az_deploy_info 信息
	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmasterModel failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get csmasterModel from csmaster",
		logit.String("csmasterModel :", base_utils.Format(csmasterModel)))

	oldAzDeployInfo := csmasterModel.AzDeployInfo
	oldDestAzDeployInfo := csmasterModel.DestAzDeployInfo
	oldAvailabilityZone := csmasterModel.AvailabilityZone
	resource.LoggerTask.Notice(ctx, "get az_deploy_info from csmaster",
		logit.String("oldAzDeployInfo : ", oldAzDeployInfo),
		logit.String("oldDestAzDeployInfo : ", oldDestAzDeployInfo),
		logit.String("oldAvailabilityZone : ", oldAvailabilityZone))

	// 根据cache_instance里节点类型和可用区信息 构造新的 az_deploy_info 和 dest_az_deploy_info
	newAzDeployInfo := ""
	newDestAzDeployInfo := ""
	newAvailabilityZone := ""

	csmasterInstances, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmasterInstances failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get csmasterInstances from csmaster",
		logit.String("csmasterInstances :", base_utils.Format(csmasterInstances)))

	/*
		构造新的 az_deploy_info 和 dest_az_deploy_info
		根据 az_deploy_info 构造新的 az_deploy_info
		如果 cache_instance 表主节点所在az跟 az_deploy_info 不一致,则根据实际主节点所在az更新az_deploy_info
		如果一致则不作变更
	*/
	var masterAz string
	for _, instanceInfo := range csmasterInstances {
		if instanceInfo.CacheInstanceType == 3 {
			masterAz = instanceInfo.AvailabilityZone
		}
	}

	// 热活产品针对从地域也默认展示一个主库，故此处不修改az_deploy_info
	if masterAz == "" {
		resource.LoggerTask.Notice(ctx, "masterAz is null, multi-region cluster slave region, no need to update data")
		return nil
	}

	resource.LoggerTask.Notice(ctx, "masterAz from csmaster cache_instance table",
		logit.String("masterAz :", base_utils.Format(masterAz)))

	if oldAzDeployInfo == "" {
		resource.LoggerTask.Notice(ctx, "oldAzDeployInfo is null, no need to update data")
		/*
			历史背景说明：
				通过open-api创建的集群,replicationInfo字段不是必填项,导致csmaster创建集群时无法获取集群拓扑信息导致无法填充az_deploy_info字段,所以通过open-api创建的集群az_deploy_info字段为空;
				csmaster依赖console传递的availability_zone进行集群创建,如果是多az的集群,默认第一个az为主az,第二个为从az;
					例如:
						console传递的信息为: "availabilityZone" : "zoneE+zoneD",
						csmaster则认为主az为:zoneE,从az为zoneD
			针对标准版多az集群,集群重启、升级、自愈切换、手动切换均不会变更az_deploy_info和availability_zone字段,导致可能会影响集群后续操作。

			针对通过open-api创建的标准版多az集群,若az_deploy_info为空,则在集群发生重启、升级、自愈切换、手动切换之后变更availability_zone字段。
		*/
		tmpAz := strings.Split(oldAvailabilityZone, "+")
		if len(tmpAz) < 2 {
			resource.LoggerTask.Notice(ctx, "cluster only has one az, no need to update data",
				logit.String("oldAvailabilityZone : ", oldAvailabilityZone))
		} else {
			// 校验 availability_zone 第一个az跟masterAz是否一致,若不一致则修改
			tmpAvailabilityZone := ""
			if tmpAz[0] == masterAz {
				resource.LoggerTask.Notice(ctx,
					"cluster has more than one az, but availability_zone is equal to masterAz, no need to update data",
					logit.String("oldAvailabilityZone : ", oldAvailabilityZone))
			} else {
				// 构造新的 availability_zone
				var tmpAzArr []string
				tmpAzArr = append(tmpAzArr, masterAz)
				for _, az := range tmpAz {
					if az != masterAz {
						tmpAzArr = append(tmpAzArr, az)
					}
				}

				for i, az := range tmpAzArr {
					tmpAvailabilityZone += az
					if i < len(tmpAzArr)-1 {
						tmpAvailabilityZone += "+"
					}
				}
				newAvailabilityZone = tmpAvailabilityZone
				resource.LoggerTask.Notice(ctx,
					"cluster has more than one az, and availability_zone is not equal to masterAz, need to update data",
					logit.String("oldAvailabilityZone : ", oldAvailabilityZone),
					logit.String("newAvailabilityZone : ", newAvailabilityZone))

				// 调用csmaster接口进行变更
				if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
					Model: &csmaster.CsmasterCluster{
						AvailabilityZone: newAvailabilityZone,
					},
					UserID: app.UserId,
					AppID:  app.AppId,
				}); err != nil {
					return err
				}
			}
		}
	} else {
		deserializationAzDeploy, err := deserializationAzDeployList(oldAzDeployInfo)
		if err != nil {
			resource.LoggerTask.Error(ctx, "deserializationAzDeployList failed", logit.Error("error", err))
			return err
		}

		isMatched := false
		for _, azDeploy := range deserializationAzDeploy {
			if azDeploy.MasterAz && azDeploy.Name == masterAz {
				isMatched = true
			}
		}

		if isMatched {
			resource.LoggerTask.Notice(ctx, "newAzDeployInfo is equal to oldAzDeployInfo, no need to update data",
				logit.String("oldAzDeployInfo : ", oldAzDeployInfo))
		} else {
			// 生成新的 az_deploy_info
			var azDeployInfo string
			for i, replica := range deserializationAzDeploy {
				azDeployInfo += replica.Name
				azDeployInfo += ","
				azDeployInfo += replica.SubnetId
				azDeployInfo += ","
				if replica.Name == masterAz {
					azDeployInfo += "true"
				} else {
					azDeployInfo += "false"
				}
				azDeployInfo += ","
				azDeployInfo += cast.ToString(replica.Replication)
				if i < len(deserializationAzDeploy)-1 {
					azDeployInfo += ";"
				}
			}
			newAzDeployInfo = azDeployInfo
			newDestAzDeployInfo = azDeployInfo
			resource.LoggerTask.Notice(ctx, "newAzDeployInfo is not equal to oldAzDeployInfo, need to update data",
				logit.String("newAzDeployInfo : ", newAzDeployInfo),
				logit.String("newDestAzDeployInfo : ", newDestAzDeployInfo),
				logit.String("oldAzDeployInfo : ", oldAzDeployInfo),
				logit.String("oldDestAzDeployInfo : ", oldDestAzDeployInfo))

			if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
				Model: &csmaster.CsmasterCluster{
					AzDeployInfo:     newAzDeployInfo,
					DestAzDeployInfo: newDestAzDeployInfo,
				},
				UserID: app.UserId,
				AppID:  app.AppId,
			}); err != nil {
				return err
			}
		}
	}
	/*
		application.replicas   也需要同步修改
	*/
	if app.Replicas == "" {
		resource.LoggerTask.Notice(ctx, "app.Replicas is null, no need to update data")
	} else {
		oldAppReplicas := []*iface.Replica{}
		if err := json.Unmarshal([]byte(app.Replicas), &oldAppReplicas); err != nil {
			resource.LoggerTask.Warning(ctx, "parse app.replicas failed", logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Notice(ctx, "appReplicas from app.Replicas table",
			logit.String("app.Replicas :", base_utils.Format(app.Replicas)))

		isMatched := false
		for _, appReplica := range oldAppReplicas {
			if appReplica.Role == "master" {
				if appReplica.Zone == masterAz {
					isMatched = true
				}
			}
		}

		newAppReplicas := []*iface.Replica{}
		if isMatched {
			resource.LoggerTask.Notice(ctx, "newReplicas is equal to oldReplicas, no need to update data",
				logit.String("oldReplicas : ", app.Replicas))
		} else {
			for _, appReplica := range oldAppReplicas {
				newAppReplicas = append(newAppReplicas, &iface.Replica{
					Zone:      appReplica.Zone, // 逻辑可用区
					SubnetIDs: appReplica.SubnetIDs,
					Role: func() string {
						if appReplica.Zone == masterAz {
							return "master"
						}
						return "slave"
					}(),
					Count: appReplica.Count,
				})
			}

			rss, err := json.Marshal(newAppReplicas)
			if err != nil {
				resource.LoggerTask.Error(ctx, "marshal replicas failed", logit.Error("error", err))
				return err
			}
			newReplicas := string(rss)
			resource.LoggerTask.Notice(ctx, "newReplicas is not equal to oldReplicas, need to update data",
				logit.String("newReplicas : ", newReplicas),
				logit.String("newDestReplicas : ", newReplicas),
				logit.String("oldReplicas : ", app.Replicas),
				logit.String("oldDestReplicas : ", app.DestReplicas))
			app.Replicas = newReplicas
			app.DestReplicas = newReplicas
			if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
				resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
				return err
			}
		}
	}
	return nil
}

func deserializationAzDeployList(azDeployInfo string) ([]*AzDeployItem, error) {
	var azDeployList []*AzDeployItem
	rawList := strings.Split(azDeployInfo, ";")
	for _, rawData := range rawList {
		rawData := strings.Split(rawData, ",")
		if len(rawData) != 4 {
			return nil, errors.New("azDeployInfo format is incorrect")
		}
		isMaster := false
		if rawData[2] == "true" {
			isMaster = true
		}
		Replication, err := strconv.ParseInt(rawData[3], 10, 64)
		if err != nil {
			return nil, err
		}
		azDeployList = append(azDeployList, &AzDeployItem{
			Name:        rawData[0],
			SubnetId:    rawData[1],
			MasterAz:    isMaster,
			Replication: Replication,
		})
	}
	return azDeployList, nil
}

// ProcessHandoverForModifyAZ 变更可用区切新主流程
func ProcessHandoverForModifyAZ(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		candidatesIds := make([]string, 0)
		var masterId string
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				masterId = node.NodeId
			}
			for _, replica := range param.DestReplicas {
				if replica.Role == "master" {
					if app.ResourceType == "container" {
						if replica.Zone == node.LogicZone && (node.Status == x1model.NodeOrProxyStatusInUse ||
							node.Status == x1model.NodeOrProxyStatusToCreate) {
							candidatesIds = append(candidatesIds, node.NodeId)
						}
					} else {
						for _, subnetID := range replica.SubnetIDs {
							if subnetID == node.SubnetId && (node.Status == x1model.NodeOrProxyStatusInUse ||
								node.Status == x1model.NodeOrProxyStatusToCreate) {
								candidatesIds = append(candidatesIds, node.NodeId)
							}
						}
					}
				}
			}
		}
		if masterId == "" {
			continue
		}

		if in, _ := base_utils.InArray(masterId, candidatesIds); in {
			resource.LoggerTask.Notice(ctx, "no need handover", logit.String("appId", app.AppId),
				logit.String("masterId", masterId), logit.String("clusterId", cluster.ClusterId))
			continue
		}

		// isManual:false 1.二次检测.2.记录事件 3.不禁写
		// isManual:true 1.不进行二次检测.2.不记录事件 3.禁写
		// toCreateMasterId,不为空的话,则设置为后候选主
		if err := processHandoverStandaloneShard(ctx, &handoverClusterShardParams{
			appId:            app.AppId,
			clusterId:        cluster.ClusterId,
			oldMasterId:      masterId,
			isManual:         true,
			candidatesIds:    candidatesIds,
			toCreateMasterId: "",
			taskId:           teu.TaskID,
		}); err != nil {
			resource.LoggerTask.Error(ctx, "process handover error", logit.Error("error", err))
			return err
		}
	}

	return nil
}
