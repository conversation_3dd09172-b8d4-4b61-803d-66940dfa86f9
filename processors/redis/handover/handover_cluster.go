/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package handover

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/event"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type handoverClusterShardParams struct {
	appId            string
	clusterId        string
	oldMasterId      string
	isManual         bool
	candidatesIds    []string
	toCreateMasterId string
	taskId           string
}

func processHandoverClusterShard(ctx context.Context, params *handoverClusterShardParams) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+params.clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get switch shard lock error", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err := x1model.ApplicationGetByAppId(ctx, params.appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	cluster, err := x1model.ClusterGetByClusterId(ctx, params.clusterId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster error", logit.Error("error", err))
		return err
	}
	var oldMaster *x1model.Node
	for _, node := range cluster.Nodes {
		if (len(params.oldMasterId) == 0 && node.Role == x1model.RoleTypeMaster) || node.NodeId == params.oldMasterId {
			oldMaster = node
			break
		}
	}
	if oldMaster == nil {
		return errors.New("master node not found")
	}

	if oldMaster.Role != x1model.RoleTypeMaster {
		resource.LoggerTask.Warning(ctx, "old master role not match expect, begin to check csmaster", logit.String("old_master", oldMaster.NodeId))
		// 数据库中的master角色不符合预期，尝试从csmaster获取角色进行更正
		if err := tryCorrectMasterRole(ctx, params, cluster); err != nil {
			return err
		}
		// 认为已经切换过
		if oldMaster.Role != x1model.RoleTypeMaster {
			resource.LoggerTask.Warning(ctx, "old master role not match expect, skip", logit.String("old_master", oldMaster.NodeId))
			if err := markSwitchSkipped(ctx, params); err != nil {
				return err
			}
			return nil
		}
		resource.LoggerTask.Warning(ctx, "old master role match expect, correct by csmaster", logit.String("old_master", oldMaster.NodeId))
	}

	if !params.isManual {
		// 二次确认检查，避免误切换
		var pList []*x1model.Proxy
		for _, itf := range app.Interfaces {
			for _, p := range itf.Proxys {
				pList = append(pList, p)
			}
		}

		if secondaryConfirmation(ctx, oldMaster, cluster, pList, "") {
			resource.LoggerTask.Warning(ctx, "secondary confirm node alive")
			if err := markSwitchSkipped(ctx, params); err != nil {
				return err
			}
			return nil
		}
	}

	/*
		processHandoverClusterShard 此方法是例行切换和手动切换公共方法
		此处在二次探测之后增加分支，仅例行切换推送事件
		根据step判断当前切换进展，具体而言：如果切换失败，则判断是否是第一次切换，如果是则发送事件，如果不是则屏蔽发送，直至成功后发送切换成功事件
	*/
	isFirstTime := false
	taskDetail, err := resource.TaskOperator.GetTaskDetail(ctx, params.taskId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get task detail failed", logit.Error("error", err))
		return err
	}
	if taskDetail == nil {
		err := errors.New("taskDetail is nil")
		resource.LoggerTask.Warning(ctx, "get task detail failed", logit.Error("error", err))
		return err
	}
	stepName := taskDetail.Step
	stepNameSplit := strings.Split(stepName, "__")
	if len(stepNameSplit) != 2 {
		err := errors.New("task step name error")
		resource.LoggerTask.Warning(ctx, "step name error", logit.Error("error", err))
		return err
	}
	if stepNameSplit[1] == "0" {
		isFirstTime = true
	}

	resource.LoggerTask.Notice(ctx, "task detail", logit.String("isFirstTime :", base_utils.Format(isFirstTime)),
		logit.String("taskDetail :", base_utils.Format(taskDetail)))

	if !params.isManual {
		if isFirstTime {
			resource.LoggerTask.Notice(ctx, "add start event", logit.String("params :", base_utils.Format(params)))
			if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, replacingEvent); err != nil {
				resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start failed", logit.Error("error", err))
			}
			resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start success")
		}
	}

	// 构造候选者list
	candidates := fillCandidates(cluster, params)
	switchReq := util.MasterSwitchReq{
		Acl:                   util.NoPasswordRedisAcl,
		ClusterInfo:           cluster,
		App:                   app,
		ManualCandidateSlaves: candidates,
		IsManualSwitch:        params.isManual,
		UseForbidWrite:        KernalSupportForbidWrite(ctx, cluster, app, oldMaster),
	}
	newMaster, err := util.MasterSwitchShard(ctx, &switchReq)
	if err != nil {
		// 例行切换失败，记录切换失败事件
		if !params.isManual {
			if isFirstTime {
				resource.LoggerTask.Notice(ctx, "add end event", logit.String("params :", base_utils.Format(params)))
				if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, replacingEvent, event.FailoverFailedEvent); err != nil {
					resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end failed", logit.Error("error", err))
				}
				resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end success")
			}
		}
		resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" switch master failed, retry:"+stepNameSplit[1])
		return err
	}

	// 例行切换成功，记录切换成功事件
	if !params.isManual {
		if !isFirstTime {
			resource.LoggerTask.Notice(ctx, "add start event", logit.String("params :", base_utils.Format(params)))
			if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, replacingEvent); err != nil {
				resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start failed", logit.Error("error", err))
			}
			resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start success")
		}

		resource.LoggerTask.Notice(ctx, "add end event", logit.String("params :", base_utils.Format(params)))
		if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, replacingEvent, event.FailoverSuccessEvent); err != nil {
			resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end failed", logit.Error("error", err))
		}
		resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end success")
	}

	resource.LoggerTask.Notice(ctx, "switch master succ", logit.String("new_master_id", newMaster.NodeId))
	if err := util.UpdateMeta(ctx, app.UserId, cluster, oldMaster, newMaster); err != nil {
		resource.LoggerTask.Warning(ctx, "update csmaster error", logit.Error("error", err))
		return err
	}
	if err := x1model.ClusterSave(ctx, []*x1model.Cluster{cluster}); err != nil {
		resource.LoggerTask.Warning(ctx, "save cluster error", logit.Error("error", err))
		return err
	}
	return nil
}

func KernalSupportForbidWrite(ctx context.Context, cluster *x1model.Cluster, app *x1model.Application, oldMaster *x1model.Node) bool {
	resource.LoggerTask.Notice(ctx, "check kernel support forbid write",
		logit.String("app_type", app.Type),
		logit.String("cluster_engine", cluster.Engine),
		logit.String("cluster_version", cluster.EngineVersion))
	if cluster.Engine == "pegadb" ||
		cluster.EngineVersion == "4.0" ||
		cluster.EngineVersion == "5.0" ||
		cluster.EngineVersion == "6.0" ||
		cluster.EngineVersion == "7.0" {
		password := ""
		if app.Type == x1model.AppTypeStandalone {
			defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
			if err != nil && !x1model.IsNotFound(err) {
				resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
				defaultACL = nil
			}
			if defaultACL != nil && len(defaultACL.Password) != 0 {
				password, _ = crypto_utils.DecryptKey(defaultACL.Password)
			}
		}
		return util.CheckSupportForbidWrite(ctx, oldMaster, password)
	}
	return false
}

func fillCandidates(cluster *x1model.Cluster, params *handoverClusterShardParams) []*x1model.Node {
	var candidates []*x1model.Node
	for _, node := range cluster.Nodes {
		// 若参数 toCreateMasterId 不为空，则只有nodeid==toCreateMasterId的才可能作为候选者
		if len(params.toCreateMasterId) != 0 {
			if node.NodeId == params.toCreateMasterId {
				candidates = append(candidates, node)
			}
			continue
		}
		// 非slave不可以候选
		if node.Role != x1model.RoleTypeSlave {
			continue
		}
		if !util.IsNodeSwitchable(node) {
			continue
		}
		// 若candidatesIds不为空，则只有其中的节点可以作为候选者
		if len(params.candidatesIds) > 0 {
			if in, _ := base_utils.InArray(node.NodeId, params.candidatesIds); in {
				candidates = append(candidates, node)
			}
		} else {
			// 能走到这里的node都可以作为候选者
			candidates = append(candidates, node)
		}
	}
	return candidates
}

func secondaryConfirmation(ctx context.Context, oldMaster *x1model.Node, cluster *x1model.Cluster, proxys []*x1model.Proxy, password string) bool {
	if err := util.PingTest(ctx, oldMaster.FloatingIP, oldMaster.Port, 3, nil); err == nil {
		resource.LoggerTask.Notice(ctx, "ping test ok", logit.String("node_id", oldMaster.NodeId))
		return true
	} else {
		resource.LoggerTask.Notice(ctx, "ping test failed", logit.String("node_id", oldMaster.NodeId), logit.Error("error", err))
	}
	// 若不用降级，则为最终判定
	if xagentDetectRet, needDownGrade := pingViaXagent(ctx, oldMaster, cluster, proxys, password); !needDownGrade {
		resource.LoggerTask.Trace(ctx, "ping via xagent suc,no need to downgrade", logit.String("result", base_utils.Format(xagentDetectRet)))
		return xagentDetectRet
	}
	resource.LoggerTask.Trace(ctx, "ping via xagent fail, downgrade")
	return checkSlavesOnline(ctx, oldMaster, cluster, password)
}

func checkSlavesOnline(ctx context.Context, oldMaster *x1model.Node, cluster *x1model.Cluster, password string) bool {
	type detectT struct {
		node        *x1model.Node
		masterAlive bool
	}
	var slaveDetects []*detectT
	g := gtask.Group{}
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		node := node
		if node.Role != x1model.RoleTypeSlave {
			continue
		}
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		slaveDetect := &detectT{
			node:        node,
			masterAlive: false,
		}
		slaveDetects = append(slaveDetects, slaveDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				replicationInfo, err := util.GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
				if err != nil {
					resource.LoggerTask.Notice(ctx, "get replication slave info for multi-point detection failed",
						logit.Error("error", err), logit.String("node_id", node.NodeId))
					return nil
				}
				if replicationInfo.Role == "slave" &&
					replicationInfo.MasterHost == oldMaster.Ip &&
					replicationInfo.MasterPort == oldMaster.Port &&
					replicationInfo.MasterLinkStatus == "up" {
					slaveDetect.masterAlive = true
					resource.LoggerTask.Notice(ctx, "multi-point detection master alive", logit.String("node_id", node.NodeId))
					return nil
				}
				resource.LoggerTask.Trace(ctx, "multi-point detection master not alive", logit.String("node_id", node.NodeId))
				return nil
			})
		})
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "multi-point detection panic", logit.Error("error", err))
		return false
	}
	aliveCount := 0
	for _, slaveDetect := range slaveDetects {
		if slaveDetect.masterAlive {
			aliveCount++
		}
	}
	if aliveCount >= len(slaveDetects)/2+1 {
		return true
	}
	return false
}

func markSwitchSkipped(ctx context.Context, params *handoverClusterShardParams) error {
	if err := resource.RedisClient.SetNX(ctx, "scs-shardswitch-skip"+params.taskId+params.clusterId, "1", 5*time.Minute).Err(); err != nil {
		resource.LoggerTask.Warning(ctx, "set skip switch flag failed", logit.Error("error", err))
		return err
	}
	return nil
}

func tryCorrectMasterRole(ctx context.Context, params *handoverClusterShardParams, cluster *x1model.Cluster) error {
	var insts []*csmaster_model_interface.CacheInstance
	if err := resource.CsmasterModel.GetAllByCond(ctx, &insts, "shard_id = ?", cluster.ClusterShortID); err != nil {
		resource.LoggerTask.Warning(ctx, "get insts error", logit.Error("error", err))
		return err
	}
	for _, inst := range insts {
		for _, node := range cluster.Nodes {
			if int64(node.NodeShortID) == inst.Id {
				node.Role = func() string {
					switch inst.CacheInstanceType {
					case 3:
						return x1model.RoleTypeMaster
					case 2:
						return x1model.RoleTypeSlave
					default:
						return node.Role
					}
				}()
			}
		}
	}
	return nil
}

// ProcessHandoverClusterShardFromCsmaster
// 处理csmaster（replace_switch_redis_master_manager.cc）发送过来的切换任务
func ProcessHandoverClusterShardFromCsmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 事件监控"开始事件"处理
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.New("teu is nilptr")
	}

	// 进行故障切换
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 在上一步获取的数据库数据中，找到与csmaster传入参数一致的cluster（shard）和node（旧主）
	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID),
			logit.Error("error", err))
		return err
	}
	node, err := util.FindNodeByShortID(ctx, cluster, param.ShardSwitchFromCsmsater.MasterShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find node with short id %d", param.ShardSwitchFromCsmsater.MasterShortID),
			logit.Error("error", err))
		return err
	}
	return processHandoverClusterShard(ctx, &handoverClusterShardParams{
		appId:            app.AppId,
		clusterId:        cluster.ClusterId,
		oldMasterId:      node.NodeId,
		isManual:         false,
		candidatesIds:    nil,
		toCreateMasterId: "",
		taskId:           teu.TaskID,
	})
}

func ProcessHandoverClusterToDeleteMasters(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		var toDeleteMaster *x1model.Node
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			if node.Role == x1model.RoleTypeMaster {
				toDeleteMaster = node
			}
		}
		if toDeleteMaster == nil {
			continue
		}
		clusterId := cluster.ClusterId
		masterId := toDeleteMaster.NodeId
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processHandoverClusterShard(ctx, &handoverClusterShardParams{
					appId:            app.AppId,
					clusterId:        clusterId,
					oldMasterId:      masterId,
					isManual:         true,
					candidatesIds:    nil,
					toCreateMasterId: "",
					taskId:           teu.TaskID,
				})
			})
		})
	}
	_, err = g.Wait()
	return err
}

func ProcessHandoverClusterNewZone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	needHandover, newZone, err := getReplicasMasterZoneChange(ctx, app, param)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "replica format invalid", logit.Error("err", err))
		return err
	}
	if !needHandover {
		return nil
	}
	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		candidateIds := []string{}
		var masterNode *x1model.Node
		for _, node := range cluster.Nodes {
			if node.LogicZone == newZone {
				candidateIds = append(candidateIds, node.NodeId)
			}
			if node.Role == x1model.RoleTypeMaster {
				masterNode = node
			}
		}
		if masterNode == nil || masterNode.LogicZone == newZone {
			continue
		}
		clusterId := cluster.ClusterId
		masterId := masterNode.NodeId
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processHandoverClusterShard(ctx, &handoverClusterShardParams{
					appId:            app.AppId,
					clusterId:        clusterId,
					oldMasterId:      masterId,
					isManual:         true,
					candidatesIds:    candidateIds,
					toCreateMasterId: "",
					taskId:           teu.TaskID,
				})
			})
		})
	}
	_, err = g.Wait()
	return err
}

// ProcessHandoverClusterToCreateMaster 集群版变配的切主流程
func ProcessHandoverClusterToCreateMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) != 0 && !metaserver.IsGlobalLeader(ctx, app) {
		return nil
	}
	pocFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "poc_flag", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get flag error", logit.Error("error", err))
		pocFlag = "no"
	}
	if pocFlag == "yes" {
		if err := processHandoverClusterToCreateMasterParallel(ctx, app, teu.TaskID); err != nil {
			return err
		}
	} else {
		if err := processHandoverClusterToCreateMaster(ctx, app, teu.TaskID); err != nil {
			return err
		}
	}
	if err := util.SetSwitchableFlagsAfterModifySpecHandover(ctx, app.AppId); err != nil {
		resource.LoggerTask.Warning(ctx, "reset all nodes switchable error", logit.Error("error", err))
		return err
	}
	return nil
}

func processHandoverClusterToCreateMaster(ctx context.Context, app *x1model.Application, taskID string) error {
	for _, cluster := range app.Clusters {
		if err := doClusterHandoverToCreateMaster(ctx, app, cluster, taskID); err != nil {
			return err
		}
	}
	return nil
}

func processHandoverClusterToCreateMasterParallel(ctx context.Context, app *x1model.Application, taskID string) error {
	g := gtask.Group{Concurrent: 25}
	for _, cluster := range app.Clusters {
		cluster := cluster
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return doClusterHandoverToCreateMaster(ctx, app, cluster, taskID)
			})
		})
	}
	_, err := g.Wait()
	return err
}

func doClusterHandoverToCreateMaster(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, taskID string) error {
	var toCreateMaster *x1model.Node
	var masterId string
	for _, node := range cluster.Nodes {
		if node.Status == x1model.NodeOrProxyStatusToCreate && node.Role == x1model.RoleTypeMaster {
			toCreateMaster = node
		}
		if node.Status != x1model.NodeOrProxyStatusToCreate && node.Role == x1model.RoleTypeMaster {
			masterId = node.NodeId
		}
	}
	if toCreateMaster == nil {
		resource.LoggerTask.Notice(ctx, "no to create master", logit.String("clusterId", cluster.ClusterId))
		return nil
	}

	// 如果存在正在创建的master，必须存在旧主
	if len(masterId) == 0 {
		resource.LoggerTask.Warning(ctx, "old master not found", logit.String("clusterId", cluster.ClusterId))
		return fmt.Errorf("old master not found, clusterId: %s", cluster.ClusterId)
	}

	replicationInfo, err := util.GetReplicationInfo(ctx, toCreateMaster.FloatingIP, toCreateMaster.Port, "")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get replication info error", logit.Error("error", err))
		return err
	}
	if replicationInfo.Role == "master" {
		resource.LoggerTask.Notice(ctx, "to create master is already master", logit.String("clusterId", cluster.ClusterId))
		return nil
	}

	if err := processHandoverClusterShard(ctx, &handoverClusterShardParams{
		appId:            app.AppId,
		clusterId:        cluster.ClusterId,
		oldMasterId:      masterId,
		isManual:         true,
		candidatesIds:    nil,
		toCreateMasterId: toCreateMaster.NodeId,
		taskId:           taskID,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "process handover cluster shard error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessHandoverClusterForRestarting(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 在热活中的集群跳过
	if len(app.AppGroupID) != 0 {
		return nil
	}

	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		clusterId := cluster.ClusterId
		var candidates []string
		var masterId string
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				masterId = node.NodeId
			}
			if node.Role == x1model.RoleTypeSlave {
				candidates = append(candidates, node.NodeId)
			}
		}
		if len(masterId) == 0 {
			continue
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processHandoverClusterShard(ctx, &handoverClusterShardParams{
					appId:            app.AppId,
					clusterId:        clusterId,
					oldMasterId:      masterId,
					isManual:         true,
					candidatesIds:    candidates,
					toCreateMasterId: "",
					taskId:           teu.TaskID,
				})
			})
		})
	}
	_, err = g.Wait()
	return err
}

// ProcessHandoverForCreateGroup 用于热活组Pega创建时修改use-rsid-psync参数生效用
func ProcessHandoverForCreateGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}
	isNeedConfigset, err := util.NeedConfigSetForJoinGroup(ctx, app)
	if err != nil {
		return err
	}
	if !isNeedConfigset {
		return nil
	}
	return ProcessHandoverClusterForRestarting(ctx, teu)
}

func ProcessResetFailoveredShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToCreate ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				req := &csmaster.ResetShardFailoverFlagParam{
					UserID:       app.UserId,
					AppID:        app.AppId,
					ShardShortID: cluster.ClusterShortID,
					AppShortID:   app.AppShortID,
				}
				if err := csmaster.CsmasterOp().ResetShardFailoverFlag(ctx, req); err != nil {
					resource.LoggerTask.Warning(ctx, "reset failover flag error", logit.Error("error", err))
				}
				break
			}
		}
	}
	return nil
}

func ProcessClusterManualFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	return processHandoverClusterShard(ctx, &handoverClusterShardParams{
		appId:            param.ManualFailover.AppID,
		clusterId:        param.ManualFailover.ShardID,
		oldMasterId:      "",
		isManual:         true,
		candidatesIds:    param.ManualFailover.Candidates,
		toCreateMasterId: "",
		taskId:           teu.TaskID,
	})
}

// ProcessClusterManualSwitchMasterSlave 集群版，用户手动切换主从，支持多分片
func ProcessClusterManualSwitchMasterSlave(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if param.ManualSwitchMasterSlave == nil || len(param.ManualSwitchMasterSlave) == 0 {
		err := fmt.Errorf("manual shard failover list is nil")
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if param.ManualSwitchConcurrent <= 0 {
		param.ManualSwitchConcurrent = 1
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	g := gtask.Group{Concurrent: param.ManualSwitchConcurrent}
	for i, _ := range param.ManualSwitchMasterSlave {
		shardToSwitch := param.ManualSwitchMasterSlave[i]
		for j, _ := range app.Clusters {
			cluster := app.Clusters[j]
			if cluster.ClusterId == shardToSwitch.ShardID {
				// 检查是否需要切换
				masterNode, has := HasMasterInCandidates(ctx, cluster, shardToSwitch.Candidates)
				if has {
					resource.LoggerTask.Error(ctx, "there is master in candidates already and skip switching",
						logit.String("cluster", cluster.ClusterId),
						logit.String("master node", masterNode.NodeId),
						logit.String("candidates", base_utils.Format(shardToSwitch.Candidates)))
					break
				}

				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return processHandoverClusterShard(ctx, &handoverClusterShardParams{
							appId:            shardToSwitch.AppID,
							clusterId:        shardToSwitch.ShardID,
							oldMasterId:      "",
							isManual:         true,
							candidatesIds:    shardToSwitch.Candidates,
							toCreateMasterId: "",
							taskId:           teu.TaskID,
						})
					})
				})
			}
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "process handover cluster shard fail", logit.Error("error", err))
		return err
	}
	return nil
}

func pingViaXagent(ctx context.Context, oldMaster *x1model.Node, cluster *x1model.Cluster,
	proxys []*x1model.Proxy, password string) (isAlive bool, needDownGrade bool) {
	type detectT struct {
		agentID       string
		callXagentSuc bool
		masterAlive   bool
	}
	var slaveDetects []*detectT
	g := gtask.Group{}
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		node := node
		if node.Role != x1model.RoleTypeSlave {
			continue
		}
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		slaveDetect := &detectT{
			agentID:       node.NodeId,
			callXagentSuc: false,
			masterAlive:   false,
		}
		slaveDetects = append(slaveDetects, slaveDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				isLive, err := util.LivenessProbe(ctx, &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				}, oldMaster, password)
				if err != nil {
					if err == util.ErrCallXagentFail {
						resource.LoggerTask.Trace(ctx, "call xagent fail", logit.String("id", node.NodeId), logit.Error("err", err))
						return nil
					}
					slaveDetect.callXagentSuc = true
					resource.LoggerTask.Trace(ctx, "detection via xagent not alive", logit.String("id", node.NodeId), logit.Error("err", err))
					return nil
				}
				if isLive {
					slaveDetect.callXagentSuc = true
					slaveDetect.masterAlive = true
					resource.LoggerTask.Trace(ctx, "detection via xagent alive", logit.String("node_id", node.NodeId))
					return nil
				}
				resource.LoggerTask.Trace(ctx, "detection via xagent not alive", logit.String("node_id", node.NodeId))
				return nil
			})
		})
	}
	proxyIdx := 0
	for len(slaveDetects) <= 11 {
		if proxyIdx >= len(proxys) {
			break
		}
		proxy := proxys[proxyIdx]
		slaveDetect := &detectT{
			agentID:       proxy.ProxyId,
			callXagentSuc: false,
			masterAlive:   false,
		}
		slaveDetects = append(slaveDetects, slaveDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				isLive, err := util.LivenessProbe(ctx, &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				}, oldMaster, password)
				if err != nil {
					if err == util.ErrCallXagentFail {
						resource.LoggerTask.Trace(ctx, "call xagent fail", logit.String("id", proxy.ProxyId), logit.Error("err", err))
						return nil
					}
					slaveDetect.callXagentSuc = true
					resource.LoggerTask.Trace(ctx, "detection via xagent not alive", logit.String("id", proxy.ProxyId), logit.Error("err", err))
					return nil
				}
				if isLive {
					slaveDetect.callXagentSuc = true
					slaveDetect.masterAlive = true
					resource.LoggerTask.Trace(ctx, "detection via xagent alive", logit.String("node_id", proxy.ProxyId))
					return nil
				}
				resource.LoggerTask.Trace(ctx, "detection via xagent not alive", logit.String("node_id", proxy.ProxyId))
				return nil
			})
		})
		proxyIdx++
	}

	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "multi-point via xagent detection panic", logit.Error("error", err))
		return false, true
	}
	callXagentSucCnt := 0
	aliveCount := 0
	for _, slaveDetect := range slaveDetects {
		if slaveDetect.masterAlive {
			aliveCount++
		}
		if slaveDetect.callXagentSuc {
			callXagentSucCnt++
		}
	}
	// 调用xagent全都失败，降级到老版本二次探测
	if callXagentSucCnt == 0 {
		return false, true
	}

	if aliveCount >= len(slaveDetects)/2+1 {
		return true, false
	}
	return false, false
}

func ProcessPreCheckForLogicInspectionFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID),
			logit.Error("error", err))
		return err
	}

	switch cluster.Engine {
	case x1model.EnginePegaDB:
		if app.Type != "cluster" {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("only cluster mode is supported for pega"),
				logit.String("cluster", cluster.ClusterId))
			return errors.New("only cluster mode is supported for pega")
		}

		pegaNodesNotInBackgroundErr, err := GetPegaNodesNotInBackgroundErr(ctx, cluster, "")
		if err != nil {
			resource.LoggerTask.Error(ctx, fmt.Sprintf("fail to get pega node not in background error"),
				logit.String("cluster", cluster.ClusterId),
				logit.Error("error", err))
			return err
		}
		if len(pegaNodesNotInBackgroundErr) == 0 {
			resource.LoggerTask.Error(ctx, fmt.Sprintf("all of master and slave nodes are in background error"),
				logit.String("cluster", cluster.ClusterId))
			return cerrs.ErrorTaskManual.Wrap(fmt.Errorf("all of master and slave nodes are "+
				"in background error, cluster id %s", cluster.ClusterId))
		}
		resource.LoggerTask.Trace(ctx, "find nodes not in background error",
			logit.String("cluster", cluster.ClusterId),
			logit.String("nodes", base_utils.Format(pegaNodesNotInBackgroundErr)))

		candidateList := make([]string, 0)
		for _, candidateNode := range pegaNodesNotInBackgroundErr {
			if candidateNode.Role == x1model.RoleTypeSlave {
				candidateList = append(candidateList, candidateNode.NodeId)
			}
		}
		if len(candidateList) == 0 {
			resource.LoggerTask.Error(ctx, fmt.Sprintf("cantidate list is nil"),
				logit.String("cluster", cluster.ClusterId))
			return cerrs.ErrorTaskManual.Wrap(errors.New("cantidate list is nil"))
		}

		manualFailoverParams := &iface.Parameters{
			AppID: app.AppId,
			ManualFailover: &iface.ManualFailover{
				AppID:      app.AppId,
				ShardID:    cluster.ClusterId,
				Candidates: candidateList,
				SyncOffset: 1, // NOTE: 1. 为避免丢失数据，需要做主从同步diff检查，故设置SyncOffset；2. SyncOffset置为0时例行切换中不做检查，故设置为1。
			},
		}
		return createManualFailoverSubTaskForLogicInspection(ctx, teu, app, cluster, manualFailoverParams)
	default:
		resource.LoggerTask.Error(ctx, "logic inspection failover only support pega now")
		return errors.New("logic inspection failover only support pega now")
	}
}

func ProcessPreCheckForSelfHealingLogicInspection(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var (
		app   *x1model.Application
		param *iface.Parameters
		err   error
	)

	param, err = iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	switch app.Type {
	case x1model.AppTypeCluster:
		return preCheckForClusterSelfHealingLogicInspection(ctx, teu, app, param)
	case x1model.AppTypeStandalone:
		return preCheckForStandaloneSelfHealingLogicInspection(ctx, teu, app, param)
	default:
		return fmt.Errorf("unknow app type, app id %s", app.AppId)
	}
}

func ProcessLogicInspectionFailoverSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(1 * time.Second)
			subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
				return err
			}

			switch subTasksStatus {
			case taskIface.SubTasksStatusRunning, taskIface.SubTasksStatusWaiting:
				resource.LoggerTask.Notice(ctx, "sub tasks are running, try check next time")
				continue
			case taskIface.SubTasksStatusSuccess:
				return nil
			case taskIface.SubTasksStatusError:
				resource.LoggerTask.Warning(ctx, "sub tasks failed", logit.Error("error", err))
				return cerrs.ErrorTaskManual.Wrap(errors.New("sub tasks failed"))
			default:
				resource.LoggerTask.Warning(ctx, "unknown sub tasks status", logit.Error("error", err))
				return cerrs.ErrorTaskManual.Wrap(errors.New("unknown sub tasks status"))
			}
		}
	}
}

func preCheckForClusterSelfHealingLogicInspection(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, param *iface.Parameters) error {
	var (
		clusterNodeIdsMap map[string][]string
		err               error
	)
	if param.SelfHealFromCsmaster != nil {
		// 从节点故障，集群版，可能涉及多个cluster和interface
		clusterNodeIdsMap = make(map[string][]string)
		for _, un := range param.SelfHealFromCsmaster.NodeShortIDs {
			node, cluster, err := buildmeta.FindNodeByShortIDInApp(ctx, app, un)
			if err != nil {
				resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
				return err
			}
			if _, ok := clusterNodeIdsMap[cluster.ClusterId]; !ok {
				clusterNodeIdsMap[cluster.ClusterId] = make([]string, 0)
			}
			clusterNodeIdsMap[cluster.ClusterId] = append(clusterNodeIdsMap[cluster.ClusterId], node.NodeId)
		}
		resource.LoggerTask.Trace(ctx, "Unhealthy cluster NodeIds Map", logit.String("clusterNodeIdsMap", base_utils.Format(clusterNodeIdsMap)))
	} else {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("param.self_heal_from_csmaster is nil"),
			logit.String("cluster", app.AppId))
		return errors.New("param.self_heal_from_csmaster is nil")
	}

	g := gtask.Group{}
	for clusterId := range clusterNodeIdsMap {
		cid := clusterId
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				cluster, err := x1model.ClusterGetByClusterId(ctx, cid)
				if err != nil {
					resource.LoggerTask.Error(ctx, "fail to get cluster by cluster id", logit.String("clusterId", cid),
						logit.Error("error", err))
					return err
				}

				pegaNodesNotInBackgroundErr, err := GetPegaNodesNotInBackgroundErr(ctx, cluster, "")
				if err != nil {
					resource.LoggerTask.Error(ctx, fmt.Sprintf("fail to get pega node not in background error"),
						logit.String("cluster", cluster.ClusterId),
						logit.Error("error", err))
					return err
				}
				if len(pegaNodesNotInBackgroundErr) == 0 {
					resource.LoggerTask.Error(ctx, fmt.Sprintf("all of master and slave nodes are in background error"),
						logit.String("cluster", cluster.ClusterId))
					return cerrs.ErrorTaskManual.Wrap(fmt.Errorf("all of master and slave nodes are in background error, "+
						"cluster id %s", cluster.ClusterId))
				}

				findMaster := false
				for _, node := range pegaNodesNotInBackgroundErr {
					if node.Role == x1model.RoleTypeMaster {
						findMaster = true
						break
					}
				}
				if !findMaster {
					resource.LoggerTask.Error(ctx, fmt.Sprintf("master node is in background error, can not replace slave node"),
						logit.String("cluster", cluster.ClusterId))
					return cerrs.ErrorTaskManual.Wrap(fmt.Errorf("master node is in background error, can not replace "+
						"slave node, cluster id %s", cluster.ClusterId))
				}
				return nil
			})
		})
	}
	if _, err = g.Wait(); err != nil {
		resource.LoggerTask.Error(ctx, "fail to check is all nodes in background err", logit.Error("error", err))
		return err
	}

	selfHealingTaskParam := *param
	if app.Type == x1model.AppTypeCluster {
		selfHealingTaskParam.SelfHealFromCsmaster.IsForceReplace = true
	} else if app.Type == x1model.AppTypeStandalone {
		//todo 后续涉及到标准版集群，也需也只强制替换flag
	}
	return createSelfHealingSubTaskForLogicInspection(ctx, teu, app, &selfHealingTaskParam)
}

func preCheckForStandaloneSelfHealingLogicInspection(ctx context.Context, teu *workflow.TaskExecUnit,
	app *x1model.Application, param *iface.Parameters) error {
	resource.LoggerTask.Warning(ctx, fmt.Sprintf("only cluster mode is supported"),
		logit.String("app", app.AppId))
	return errors.New("only cluster mode is supported")
}

// createManualFailoverSubTaskForLogicInspection 逻辑故障场景中，创建例行切换任务以进行切主
func createManualFailoverSubTaskForLogicInspection(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, cluster *x1model.Cluster, subTaskParam *iface.Parameters) error {
	var (
		subWorkflow string
	)
	switch app.Type {
	case x1model.AppTypeCluster:
		subWorkflow = "scs-workflow-manual-failover-cluster"
	case x1model.AppTypeStandalone:
		subWorkflow = "scs-workflow-manual-failover-standalone"
	default:
		resource.LoggerTask.Error(ctx, "unsupported app type", logit.String("app", app.AppId))
		return fmt.Errorf("unsupported app type, app id %s", app.AppId)
	}

	paramsBytes, err := json.Marshal(subTaskParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("fail to marshal params"),
			logit.String("cluster", cluster.ClusterId),
			logit.Error("error", err))
		return err
	}
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*taskIface.CreateTaskParams{{
		WorkFlow:   subWorkflow,
		Schedule:   time.Now(),
		Mutex:      fmt.Sprintf("c_%s%d", app.AppId, cluster.ClusterShortID),
		Entity:     app.AppId,
		Parameters: string(paramsBytes),
	}})
}

// createSelfHealingSubTaskForLogicInspection 逻辑故障场景中，创建自愈任务以进行替换节点
func createSelfHealingSubTaskForLogicInspection(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, subTaskParam *iface.Parameters) error {
	var (
		subWorkflow string
	)
	switch app.Type {
	case x1model.AppTypeCluster:
		subWorkflow = "scs-failover-cluster-app"
	case x1model.AppTypeStandalone:
		subWorkflow = "scs-failover-standalone-app"
	default:
		resource.LoggerTask.Error(ctx, "unsupported app type", logit.String("app", app.AppId))
		return fmt.Errorf("unsupported app type, app id %s", app.AppId)
	}
	paramsBytes, err := json.Marshal(subTaskParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("fail to marshal params"),
			logit.String("app", app.AppId),
			logit.Error("error", err))
		return err
	}
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*taskIface.CreateTaskParams{{
		WorkFlow:   subWorkflow,
		Schedule:   time.Now(),
		Mutex:      fmt.Sprintf("n_%s", app.AppId),
		Entity:     app.AppId,
		Parameters: string(paramsBytes),
	}})
}

func GetPegaNodesNotInBackgroundErr(ctx context.Context, cluster *x1model.Cluster, password string) ([]*x1model.Node, error) {
	type detectT struct {
		node              *x1model.Node
		isInBackgroundErr bool
	}
	var nodeDetects []*detectT
	var pegaNodesNotInBackgroundErr = make([]*x1model.Node, 0)
	g := gtask.Group{}
	for _, node := range cluster.Nodes {
		node := node
		if node.Status != x1model.NodeOrProxyStatusInUse {
			continue
		}
		nodeDetect := &detectT{
			node:              node,
			isInBackgroundErr: false,
		}
		nodeDetects = append(nodeDetects, nodeDetect)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				rocksDBInfo, err := util.GetPegaRocksDBInfo(ctx, node.FloatingIP, node.Port, password)
				if err != nil {
					resource.LoggerTask.Notice(ctx, "get pega rocksdb info failed",
						logit.Error("error", err), logit.String("node_id", node.NodeId))
					return err
				}
				resource.LoggerTask.Trace(ctx, "get pega rocksdb info",
					logit.String("rocksDBInfo", base_utils.Format(rocksDBInfo)))
				nodeDetect.isInBackgroundErr = rocksDBInfo.InBackgroundError
				return nil
			})
		})
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get pega rocksdb info failed", logit.Error("error", err))
		return pegaNodesNotInBackgroundErr, err
	}
	for _, nodeDetect := range nodeDetects {
		if !nodeDetect.isInBackgroundErr {
			pegaNodesNotInBackgroundErr = append(pegaNodesNotInBackgroundErr, nodeDetect.node)
		}
	}
	return pegaNodesNotInBackgroundErr, nil
}

// ProcessClusterHandoverForModifyAZ 集群变更可用区切新主流程
func ProcessClusterHandoverForModifyAZ(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	g := gtask.Group{
		Concurrent: 10,
	}
	for _, cluster := range app.Clusters {
		clusterId := cluster.ClusterId
		candidatesIds := make([]string, 0)
		var masterId string
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				masterId = node.NodeId
			}
			for _, replica := range param.DestReplicas {
				if replica.Role == "master" {
					if app.ResourceType == "container" {
						if replica.Zone == node.LogicZone && (node.Status == x1model.NodeOrProxyStatusInUse ||
							node.Status == x1model.NodeOrProxyStatusToCreate) {
							candidatesIds = append(candidatesIds, node.NodeId)
						}
					} else {
						for _, subnetID := range replica.SubnetIDs {
							if subnetID == node.SubnetId && (node.Status == x1model.NodeOrProxyStatusInUse ||
								node.Status == x1model.NodeOrProxyStatusToCreate) {
								candidatesIds = append(candidatesIds, node.NodeId)
							}
						}
					}
				}
			}
		}
		if len(masterId) == 0 {
			continue
		}
		if in, _ := base_utils.InArray(masterId, candidatesIds); in {
			resource.LoggerTask.Notice(ctx, "no need handover", logit.String("appId", app.AppId),
				logit.String("masterId", masterId), logit.String("clusterId", cluster.ClusterId))
			continue
		}
		resource.LoggerTask.Notice(ctx, "Handover For Modify AZ",
			logit.String("app_id", app.AppId), logit.String("cluster_id", clusterId),
			logit.String("manual_candidate_slaves", base_utils.Format(candidatesIds)),
			logit.Bool("is_manual_switch", true))
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processHandoverClusterShard(ctx, &handoverClusterShardParams{
					appId:            app.AppId,
					clusterId:        clusterId,
					oldMasterId:      masterId,
					isManual:         true,
					candidatesIds:    candidatesIds,
					toCreateMasterId: "",
					taskId:           teu.TaskID,
				})
			})
		})
	}
	_, err = g.Wait()
	return err
}

func ProcessHandoverSetTags(ctx context.Context, teu *workflow.TaskExecUnit) error {

	if err := util.SetSwitchableFlagsAfterModifySpecHandover(ctx, teu.Entity); err != nil {
		resource.LoggerTask.Warning(ctx, "set switchable flags fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessHandoverReSetTags(ctx context.Context, teu *workflow.TaskExecUnit) error {

	if err := util.ResetAllNodesSwichable(ctx, teu.Entity); err != nil {
		resource.LoggerTask.Error(ctx, "reset all nodes switchable failed", logit.Error("error", err))
		return err
	}
	return nil
}
