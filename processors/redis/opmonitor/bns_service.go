package opmonitor

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/services/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	cs_model "icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessCreateOpmonitorBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	op := opmonitor.Instance()

	var engine string
	for _, cluster := range app.Clusters {
		engine = cluster.Engine
	}
	req := &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: csCluster.Id,
		Engine:    engine,
	}

	if err := op.CreateClusterBns(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "create cluster bns fail", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessDeleteOpmonitorBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	op := opmonitor.Instance()

	req := &opmonitor.ClusterBnsParams{
		AppType:   app.Type,
		ClusterID: csCluster.Id,
		Engine:    util.GetEngine(app),
	}

	if err := op.DeleteClusterBns(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "create cluster bns fail", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessCleanDeletedOpmonitorBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.CleanBnsParam == nil || param.CleanBnsParam.DaysAgoEnd < 1 {
		resource.LoggerTask.Warning(ctx, "clean bns param is nil or days ago end lt 1")
		return errors.New("invalid param")
	}

	clusters, err := resource.CsmasterOpAgent.GetClusterModelByCond(ctx,
		"status = ? and use_new_agent=? and version in (?,?) and recycle_time > ? and recycle_time < ?",
		cs_model.CACHE_CLUSTER_DELETED, "yes", csmaster.VersionV5, csmaster.VersionV7,
		base_utils.FormatWithCST(time.Now().AddDate(0, 0, -param.CleanBnsParam.DaysAgoStart)),
		base_utils.FormatWithCST(time.Now().AddDate(0, 0, -param.CleanBnsParam.DaysAgoEnd)))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cache cluster by status fail",
			logit.Int("status", cs_model.CACHE_CLUSTER_DELETED))
		return err
	}

	op := opmonitor.Instance()

	for _, cluster := range clusters {
		app, err := x1model.ApplicationGetByAppId(ctx, cluster.ClusterShowId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err),
				logit.String("appId", cluster.ClusterShowId))
			return err
		}
		if err := op.DeleteClusterBns(ctx, &opmonitor.ClusterBnsParams{
			AppType:   app.Type,
			ClusterID: cluster.Id,
			Engine:    util.GetEngine(app),
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "delete cluster bns fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}
