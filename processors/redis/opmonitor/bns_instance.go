package opmonitor

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	cs_model "icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/vep"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessCreateOpmonitorInstanceBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	op := opmonitor.Instance()

	if app.Type == x1model.AppTypeCluster {
		proxyReq := &opmonitor.ProxyInstanceBnsParams{
			ClusterID:      csCluster.Id,
			ProxyInstances: make([]*opmonitor.ProxyInstanceInfo, 0),
		}
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxys {
				if proxy.Status != x1model.NodeOrProxyStatusToCreate {
					continue
				}
				proxyReq.ProxyInstances = append(proxyReq.ProxyInstances,
					&opmonitor.ProxyInstanceInfo{
						ShortID:    proxy.ProxyShortID,
						FloatingIP: transformFloatingIP(ctx, proxy.FloatingIP, proxy.Port),
					})
			}
		}
		if len(proxyReq.ProxyInstances) > 0 {
			if err := op.CreateProxyInstanceBns(ctx, proxyReq); err != nil {
				resource.LoggerTask.Warning(ctx, "create proxy instance bns fail", logit.Error("error", err))
				return err
			}
		}
	}
	nodeReq := &opmonitor.NodeInstanceBnsParams{
		ClusterID:     csCluster.Id,
		NodeInstances: make([]*opmonitor.NodeInstanceInfo, 0),
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			nodeReq.NodeInstances = append(nodeReq.NodeInstances,
				&opmonitor.NodeInstanceInfo{
					ShortID:    node.NodeShortID,
					FloatingIP: transformFloatingIP(ctx, node.FloatingIP, node.Port),
					Engine:     cluster.Engine,
				})
		}
		for _, node := range cluster.RoNodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			nodeReq.NodeInstances = append(nodeReq.NodeInstances,
				&opmonitor.NodeInstanceInfo{
					ShortID:    node.NodeShortID,
					FloatingIP: transformFloatingIP(ctx, node.FloatingIP, node.Port),
					Engine:     cluster.Engine,
				})
		}
	}

	if len(nodeReq.NodeInstances) > 0 {
		if err := op.CreateNodeInstanceBns(ctx, nodeReq); err != nil {
			resource.LoggerTask.Warning(ctx, "create node instance bns fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessRollbackCreateOpmonitorInstanceBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	op := opmonitor.Instance()

	if app.Type == x1model.AppTypeCluster {
		proxyReq := &opmonitor.ProxyInstanceBnsParams{
			ClusterID:      csCluster.Id,
			ProxyInstances: make([]*opmonitor.ProxyInstanceInfo, 0),
		}
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxys {
				if proxy.Status != x1model.NodeOrProxyStatusToCreate {
					continue
				}
				proxyReq.ProxyInstances = append(proxyReq.ProxyInstances,
					&opmonitor.ProxyInstanceInfo{
						ShortID:    proxy.ProxyShortID,
						FloatingIP: proxy.FloatingIP,
					})
			}
		}
		if len(proxyReq.ProxyInstances) > 0 {
			if err := op.DeleteProxyInstanceBns(ctx, proxyReq); err != nil {
				resource.LoggerTask.Warning(ctx, "create proxy instance bns fail", logit.Error("error", err))
				return err
			}
		}
	}
	nodeReq := &opmonitor.NodeInstanceBnsParams{
		ClusterID:     csCluster.Id,
		NodeInstances: make([]*opmonitor.NodeInstanceInfo, 0),
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			nodeReq.NodeInstances = append(nodeReq.NodeInstances,
				&opmonitor.NodeInstanceInfo{
					ShortID:    node.NodeShortID,
					FloatingIP: node.FloatingIP,
					Engine:     cluster.Engine,
				})
		}
		for _, node := range cluster.RoNodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			nodeReq.NodeInstances = append(nodeReq.NodeInstances,
				&opmonitor.NodeInstanceInfo{
					ShortID:    node.NodeShortID,
					FloatingIP: node.FloatingIP,
					Engine:     cluster.Engine,
				})
		}
	}
	if len(nodeReq.NodeInstances) > 0 {
		if err := op.DeleteNodeInstanceBns(ctx, nodeReq); err != nil {
			resource.LoggerTask.Warning(ctx, "create node instance bns fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessCleanToDeleteInstanceOpmonitorInstanceBns(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	instances, err := resource.CsmasterOpAgent.GetInstanceToDeleteByClearStatus(ctx, cs_model.BNS_UNCLEAR_STATUS)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get instance to delete to clear status failed",
			logit.Error("error", err))
		return err
	}
	op := opmonitor.Instance()

	for _, instance := range instances {
		csCluster, err := resource.CsmasterOpAgent.GetClusterModel(ctx, instance.ClusterID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed",
				logit.Int64("clusterid", instance.ClusterID))
			return err
		}
		// 过度代码
		if csCluster.UseNewAgent != "yes" {
			continue
		}
		if csCluster.Version == cs_model.VersionMemcached {
			continue
		}
		if instance.CacheInstanceType == cs_model.INSTANCE_TYPE_PROXY {
			if err := op.DeleteProxyInstanceBns(ctx, &opmonitor.ProxyInstanceBnsParams{
				ClusterID: csCluster.Id,
				ProxyInstances: []*opmonitor.ProxyInstanceInfo{{
					ShortID:    int(instance.InstanceID.Int32),
					FloatingIP: transformFloatingIP(ctx, instance.FloatingIP, instance.Port),
				}}}); err != nil {
				resource.LoggerTask.Warning(ctx, "delete proxy instance bns fail", logit.Error("error", err))
				// 避免脏数据影响主流程
				continue
			}
		} else if instance.CacheInstanceType == cs_model.INSTANCE_TYPE_MASTER_REDIS ||
			instance.CacheInstanceType == cs_model.INSTANCE_TYPE_SLAVE_REDIS {
			app, err := x1model.ApplicationGetByAppId(ctx, csCluster.ClusterShowId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get app info failed", logit.Error("error", err))
				return err
			}
			if err := op.DeleteNodeInstanceBns(ctx, &opmonitor.NodeInstanceBnsParams{
				ClusterID: csCluster.Id,
				NodeInstances: []*opmonitor.NodeInstanceInfo{{
					ShortID:    int(instance.InstanceID.Int32),
					FloatingIP: transformFloatingIP(ctx, instance.FloatingIP, instance.Port),
					Engine:     util.GetEngine(app),
				}},
			}); err != nil {
				resource.LoggerTask.Warning(ctx, "delete node instance bns fail", logit.Error("error", err))
				// 避免脏数据影响主流程
				continue
			}
		} else {
			continue
		}
		instance.ClearStatus = cs_model.BNS_CLEARER_STATUS
		if err := resource.CsmasterOpAgent.UpdateCacheInstanceToDelete(ctx, []*cs_model.CacheInstanceToDelete{instance}); err != nil {
			resource.LoggerTask.Warning(ctx, "update cache instance to delete failed")
			return err
		}
	}

	return nil
}

func transformFloatingIP(ctx context.Context, floatingIP string, port int) string {
	if strings.HasPrefix(floatingIP, "vep:") {
		ip, _, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(floatingIP, port)
		if err != nil {
			return floatingIP
		}
		return ip
	}
	return floatingIP
}
