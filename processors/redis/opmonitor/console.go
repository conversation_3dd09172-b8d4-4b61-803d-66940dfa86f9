package opmonitor

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/services/csmaster"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

const RedisUsageMetric = "used_memory"
const PegaDBUsageMetric = "used_dbsize"
const KeyPrefix = "scs:online:console:usage:"

func getUsage(ctx context.Context, host string, port int, password string, metric string) (int64, error) {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	raw, err := c.Info(ctx, "memory").Result()
	if err != nil {
		return 0, err
	}
	raw = strings.ReplaceAll(raw, "\r\n", "\n")
	raw = strings.ReplaceAll(raw, "\r", "")
	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}
		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		switch kv[0] {
		case metric:
			usage, err := strconv.ParseInt(kv[1], 10, 64)
			if err != nil {
				return 0, err
			}
			return usage, nil
		}
	}
	return 0, errors.New("metric not found")
}

// ProcessCollectDataUsageForConsole 收集数据使用情况
func ProcessCollectDataUsageForConsole(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.CollectUsageParam == nil || param.CollectUsageParam.SchedulePeriodMinutes == 0 {
		resource.LoggerTask.Warning(ctx, "collect data usage param is nil or schedule period = 0")
		return errors.New("invalid param")
	}

	//支持Redis和PegaDB
	var clusters []*csmaster_model_interface.SimpleCacheCluster
	if err := resource.CsmasterModel.GetAllByCondNoTx(ctx,
		&clusters, "status not in (?,?,?,?) and version in (?,?)", 0, 10, 12, 58,
		csmaster.VersionV5, csmaster.VersionV7); err != nil {
		resource.LoggerTask.Error(ctx, "get cache cluster by condition fail", logit.Error("error", err))
		return err
	}

	g := gtask.Group{
		Concurrent:    10,
		AllowSomeFail: true,
	}

	for _, cluster := range clusters {
		clusterVersion := cluster.Version
		clusterShowID := cluster.ClusterShowId
		clusterStoreType := cluster.StoreType
		clusterGroupID := cluster.GroupId
		period := param.CollectUsageParam.SchedulePeriodMinutes
		g.Go(func() error {
			x1modelResource, err := x1model.GetDbAgent(ctx)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get db agent fail", logit.Error("error", err))
				return err
			}
			var acls []*x1model.RedisAcl
			if err := x1modelResource.GetAllByCondNoTx(ctx, &acls, "app_id = ? AND account_name = ? AND status = ?",
				clusterShowID, x1model.DefaultACLUser, x1model.ACLStatusInUse); err != nil {
				resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err), logit.String("appId", clusterShowID))
				return err
			}
			password := ""
			if len(acls) > 0 && len(acls[0].Password) != 0 && clusterVersion != csmaster.VersionV5 {
				password, _ = crypto_utils.DecryptKey(acls[0].Password)
			}
			metric := RedisUsageMetric
			if clusterStoreType == csmaster.STORE_PEGA {
				metric = PegaDBUsageMetric
			}
			var nodes []*x1model.Node
			if err := x1modelResource.GetAllByCondNoTx(ctx, &nodes, "app_id=? and status=?", clusterShowID, x1model.NodeOrProxyStatusInUse); err != nil {
				resource.LoggerTask.Error(ctx, "get node fail", logit.Error("error", err),
					logit.String("appId", clusterShowID))
				return err
			}

			masters := make([]*x1model.Node, 0)
			slaves := make([]*x1model.Node, 0)
			for _, node := range nodes {
				if node.Role == x1model.RoleTypeMaster {
					masters = append(masters, node)
				}
				if node.Role == x1model.RoleTypeSlave {
					slaves = append(slaves, node)
				}
				continue
			}
			var totalUsageBytes int64
			if len(masters) > 0 {
				for _, node := range masters {
					usage, err := getUsage(ctx, node.FloatingIP, node.Port, password, metric)
					if err != nil {
						resource.LoggerTask.Error(ctx, "get data usage from node fail",
							logit.String("node", base_utils.Format(node)))
						return err
					}
					totalUsageBytes += usage
				}
				if err := resource.RedisClient.Set(ctx, KeyPrefix+clusterShowID, totalUsageBytes, time.Duration(period)*4*time.Minute).Err(); err != nil {
					resource.LoggerTask.Error(ctx, "set data usage key fail", logit.Error("error", err), logit.String("appId", clusterShowID))
					return err
				}
			} else {
				if clusterGroupID == "" {
					resource.LoggerTask.Warning(ctx, "not found master node", logit.String("appId", clusterShowID))
					return nil
				}
				if len(slaves) == 0 {
					resource.LoggerTask.Warning(ctx, "not found slave node", logit.String("appId", clusterShowID))
					return nil
				}
				shardUsageBytes := make(map[string]int64, 0)
				shardSlaveCount := make(map[string]int64, 0)
				for _, node := range slaves {
					usage, err := getUsage(ctx, node.FloatingIP, node.Port, password, metric)
					// PegaDB全量同步阶段, info memory为空
					if err != nil && (err.Error() != "metric not found" || clusterStoreType != csmaster.STORE_PEGA) {
						resource.LoggerTask.Error(ctx, "get data usage from node fail",
							logit.String("node", base_utils.Format(node)))
						return err
					}
					if _, found := shardUsageBytes[node.ClusterId]; !found {
						shardUsageBytes[node.ClusterId] = usage
						shardSlaveCount[node.ClusterId] = 1
					} else {
						shardUsageBytes[node.ClusterId] += usage
						shardSlaveCount[node.ClusterId] += 1
					}
				}
				for shardID, usage := range shardUsageBytes {
					totalUsageBytes += usage / shardSlaveCount[shardID]
				}
				if err := resource.RedisClient.Set(ctx, KeyPrefix+clusterShowID, totalUsageBytes, time.Duration(period)*4*time.Minute).Err(); err != nil {
					resource.LoggerTask.Error(ctx, "set data usage key fail", logit.Error("error", err), logit.String("appId", clusterShowID))
					return err
				}
			}
			return nil
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "collect usage not all success", logit.Error("err", err))
		return err
	}
	return nil
}
