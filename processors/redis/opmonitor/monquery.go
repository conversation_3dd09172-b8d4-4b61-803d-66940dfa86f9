package opmonitor

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/monquery"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const MonitorErrorItem = "monitor_error"
const MonitorErrorThreshold = 15

const (
	ProcCPUUsage      = "proc_cpu_usage"
	UsedMemory        = "used_memory"
	MaxMemory         = "maxmemory"
	PegaUsedDiskSize  = "data_disk_used_mb"
	DiskUsedPercent   = "disk_used_percent"
	ProxyProcCPUUsage = "proxy_proc_cpu_usage"

	SampleFuncMax = "max"
	SampleFuncMin = "min"
	SampleFuncAvg = "avg"
	SampleFuncSum = "sum"
)

var (
	GetPercentileFromMonquery = monquery.GetPercentileFromMonquery
	GetPercentile             = monquery.GetPercentile
)

var DefaultItemList = []string{ProcCPUUsage, UsedMemory, MaxMemory, PegaUsedDiskSize, DiskUsedPercent}

type GetMonqueryParams struct {
	ClusterModel   *csmaster_model_interface.CacheCluster
	InstanceModels []*csmaster_model_interface.CacheInstance
	InstanceFilter func(*csmaster_model_interface.CacheInstance) bool
	Items          []string
	SampleFunc     string
	Start          string
	End            string
	Interval       string
}
type MonqueryLeastInsCntParam struct {
	BaseInsShortIDs []int64
	Item            string
	SampleFunc      string
	Start           string
	End             string
	Interval        string
	Threshold       int
}

func getInstanceNamespaces(ctx context.Context, app *x1model.Application, csModule *csmaster_model_interface.CacheCluster) ([]string, error) {
	namespaces := make([]string, 0)
	op := opmonitor.Instance()
	if app.Type == x1model.AppTypeCluster {
		proxyReq := &opmonitor.ProxyInstanceBnsParams{
			ClusterID:      csModule.Id,
			ProxyInstances: make([]*opmonitor.ProxyInstanceInfo, 0),
		}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				if proxy.Status != x1model.NodeOrProxyStatusInUse {
					continue
				}
				proxyReq.ProxyInstances = append(proxyReq.ProxyInstances,
					&opmonitor.ProxyInstanceInfo{
						ShortID:    proxy.ProxyShortID,
						FloatingIP: transformFloatingIP(ctx, proxy.FloatingIP, proxy.Port),
					})
			}
		}
		if len(proxyReq.ProxyInstances) > 0 {
			proxyNamespaces, err := op.ProxyInstanceBns(proxyReq)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get proxy instance bns fail", logit.Error("error", err))
				return namespaces, err
			}
			namespaces = append(namespaces, proxyNamespaces...)
		}
	}
	nodeReq := &opmonitor.NodeInstanceBnsParams{
		ClusterID:     csModule.Id,
		NodeInstances: make([]*opmonitor.NodeInstanceInfo, 0),
	}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			nodeReq.NodeInstances = append(nodeReq.NodeInstances,
				&opmonitor.NodeInstanceInfo{
					ShortID:    node.NodeShortID,
					FloatingIP: transformFloatingIP(ctx, node.FloatingIP, node.Port),
					Engine:     cluster.Engine,
				})
		}
		if len(nodeReq.NodeInstances) > 0 {
			nodeNamespaces, err := op.NodeInstanceBns(ctx, nodeReq)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get node instance bns fail", logit.Error("error", err))
				return namespaces, err
			}
			namespaces = append(namespaces, nodeNamespaces...)
		}
	}
	return namespaces, nil
}

func checkMonitorError(ctx context.Context, namespaces []string, appID string) error {
	req := &monquery.CommonRequest{
		Namespaces: strings.Join(namespaces, ","),
		Items:      MonitorErrorItem,
		Token:      appID,
	}
	rsp, err := monquery.NewDefaultMonquerySdk().GetLatestItemData(ctx, req)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get monitor error fail", logit.Error("error", err))
		return err
	}
	if !rsp.Success {
		resource.LoggerTask.Warning(ctx, "get monitor error fail", logit.String("message", rsp.Message))
		return cerrs.ErrorTaskManual.Wrap(errors.New(rsp.Message))
	}
	for _, data := range rsp.Data {
		if len(data.Items[MonitorErrorItem]) == 0 {
			resource.LoggerTask.Warning(ctx, "namespace have no datapoint", logit.String("namespace", data.NameSpace))
			return cerrs.ErrorTaskManual.Wrap(errors.New(fmt.Sprintf("namespace:%s have no datapoint", data.NameSpace)))
		}
		if data.Items[MonitorErrorItem][0].Value > MonitorErrorThreshold ||
			data.Items[MonitorErrorItem][0].Timestamp.(string) < base_utils.FormatWithCST(time.Now().Add(-1*time.Minute)) {
			resource.LoggerTask.Warning(ctx, "monitor error threshold exceed",
				logit.String("namespace", data.NameSpace), logit.Float64("value", data.Items[MonitorErrorItem][0].Value))
			return cerrs.ErrorTaskManual.Wrap(errors.New(fmt.Sprintf("namespace:%s monitor error threshold exceed", data.NameSpace)))
		}
	}
	return nil
}

func ProcessCheckMonitorError(ctx context.Context, teu *workflow.TaskExecUnit) error {

	//等待监控数据推动到Noah
	time.Sleep(time.Minute * 1)

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	namespaces, err := getInstanceNamespaces(ctx, app, csCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get instance namespaces fail", logit.Error("error", err))
		return err
	}

	return checkMonitorError(ctx, namespaces, app.AppId)
}

func GetInstancesLatestMonquery(ctx context.Context, params *GetMonqueryParams) (resp *monquery.CommonResponse, err error) {
	req, err := getInstancesMonqueryRequest(params)
	if err != nil {
		return nil, err
	}
	return monquery.NewDefaultMonquerySdk().GetLatestItemData(ctx, req)
}

func GetInstancesHistoryMonquery(ctx context.Context, params *GetMonqueryParams) (resp *monquery.CommonResponse, err error) {
	req, err := getInstancesMonqueryRequest(params)
	if err != nil {
		return nil, err
	}
	return monquery.NewDefaultMonquerySdk().GetHistoryItemData(ctx, req)
}

func getInstancesMonqueryRequest(params *GetMonqueryParams) (*monquery.CommonRequest, error) {
	appIDChunks := strings.Split(params.ClusterModel.ClusterShowId, "-")
	if len(appIDChunks) != 3 {
		return nil, errors.New("invalid appid")
	}
	region := appIDChunks[1]
	var namespaces []string
	for _, instanceModel := range params.InstanceModels {
		if params.InstanceFilter(instanceModel) {
			continue
		}
		engine := "redis"
		if instanceModel.CacheInstanceType == 0 {
			engine = "proxy"
		}
		if strings.Contains(env.IDC(), "test") {
			namespaces = append(namespaces, fmt.Sprintf("%d.cluster-%d-%ssandbox.BCE.all", instanceModel.Id, params.ClusterModel.Id, engine))
		} else {
			if region == "su" {
				namespaces = append(namespaces, fmt.Sprintf("%d.cluster-%d-%s.BCE.all", instanceModel.Id, params.ClusterModel.Id, engine))
			} else {
				namespaces = append(namespaces, fmt.Sprintf("%d.cluster-%d-%s%sonline.BCE.all", instanceModel.Id, params.ClusterModel.Id, engine, region))
			}
		}
	}
	req := &monquery.CommonRequest{
		Namespaces: strings.Join(namespaces, ","),
		Items:      strings.Join(params.Items, ","),
		Token:      params.ClusterModel.ClusterShowId,
		SampleFunc: params.SampleFunc,
		Start:      params.Start,
		End:        params.End,
		Interval:   params.Interval,
	}
	return req, nil
}

func GetInstanceModels(ctx context.Context, cacheClusterID int64, shortIDs []int64) ([]*csmaster_model_interface.CacheInstance, error) {
	if len(shortIDs) == 0 {
		return nil, fmt.Errorf("shortIDs is empty")
	}
	if cacheClusterID == 0 {
		return nil, fmt.Errorf("cacheClusterID is empty")
	}
	var insts []*csmaster_model_interface.CacheInstance
	if err := resource.CsmasterModel.GetAllByCond(ctx, &insts, "cluster_id = ? and id in ?", cacheClusterID, shortIDs); err != nil {
		resource.LoggerTask.Warning(ctx, "get insts error", logit.Error("error", err))
		return nil, err
	}
	return insts, nil
}

func GetLeastInsCntByMonquery(ctx context.Context, app *x1model.Application, param *MonqueryLeastInsCntParam) (int, error) {

	// 查询监控
	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return 0, err
	}

	cacheInstances, err := GetInstanceModels(ctx, csCluster.Id, param.BaseInsShortIDs)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cache instance models error", logit.Error("error", err))
		return 0, err
	}

	getMonParams := &GetMonqueryParams{
		ClusterModel:   csCluster,
		InstanceModels: cacheInstances,
		InstanceFilter: func(instance *csmaster_model_interface.CacheInstance) bool {
			return false
		},
		Items:      []string{param.Item},
		SampleFunc: param.SampleFunc,
		Start:      param.Start,
		End:        param.End,
		Interval:   param.Interval,
	}

	resp, err := GetInstancesHistoryMonquery(ctx, getMonParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get instances history monquery error", logit.Error("error", err))
		return 0, err
	}
	if resp == nil {
		resource.LoggerTask.Warning(ctx, "get instances history monquery response is nil")
		return 0, fmt.Errorf("get instances history monquery response is nil")
	}
	if len(resp.Data) == 0 {
		resource.LoggerTask.Warning(ctx, "get instances history monquery result data is nil",
			logit.String("resp", base_utils.Format(resp)),
		)
		return 0, fmt.Errorf("get instances history monquery result is nil")
	}
	resource.LoggerTask.Trace(ctx, "get instances history monquery result",
		logit.String("appId", app.AppId),
		logit.String("monquery_req", base_utils.Format(getMonParams)),
		logit.String("monquery_resp", base_utils.Format(resp)),
	)

	// 计算总和
	var sumOfItem float64
	item := param.Item
	for _, namespaceData := range resp.Data {
		if itemDatas, ok := namespaceData.Items[item]; ok {
			if len(itemDatas) > 0 {
				sumOfItem += itemDatas[0].Value
			}
		}
	}

	// 根据阈值计算最少的实例数量
	if param.Threshold <= 0 {
		resource.LoggerTask.Warning(ctx, "threshold is less than or equal to 0", logit.Error("error", err))
		return 0, err
	}
	leastInsCnt := int(math.Ceil(sumOfItem / float64(param.Threshold)))
	if leastInsCnt <= 0 {
		leastInsCnt = 1 // 至少保留1实例
	}

	resource.LoggerTask.Trace(ctx, "get least instances cnt by monquery",
		logit.String("item", item),
		logit.Float64("sumOfItem", sumOfItem),
		logit.Int("threshold", param.Threshold),
		logit.Int("leastInsCnt", leastInsCnt))

	return leastInsCnt, nil
}
