package namespace_task

import (
	"context"
	"errors"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	ns "icode.baidu.com/baidu/scs/x1-base/component/namespace"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func calculateProgressPercent(num, maxNum int, progressCur float64) float64 {
	return (100.0-progressCur)*(float64(num)/float64(maxNum)) + progressCur
}

// IngestNamespacePrepare 注入任务准备工作
func IngestNamespacePrepare(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if err = validateParam(ctx, param); err != nil {
		resource.LoggerTask.Error(ctx, "validate params error", logit.Error("error", err))
		return err
	}

	if err = updateTaskStatus(ctx, param.NamespaceParam.TaskID, x1model.NamespaceTaskStatusRunning); err != nil {
		resource.LoggerTask.Warning(ctx, "update task status error", logit.Error("error", err))
		return err
	}

	return nil
}

// DownloadPrecheckMeta 检测元信息
func DownloadPrecheckMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	namespaces, err := x1model.NamespaceGetAllByAppID(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err))
		return err
	}

	found := false
	reingest := false
	for _, namespace := range namespaces {
		// 确保目标命名空间已存在，且状态为注入中
		if namespace.Namespace == param.NamespaceParam.TargetNamespace {
			if namespace.Status == x1model.NamespaceStatusReIngesting {
				reingest = true
				found = true
				break
			} else if namespace.Status == x1model.NamespaceStatusIngesting {
				reingest = false
				found = true
				break
			}
		}
	}
	if !found {
		resource.LoggerTask.Warning(ctx, "namespace is not exist", logit.String("namespace",
			param.NamespaceParam.TargetNamespace))
		return errors.New("namespace is not exist")
	}
	//在1个namespace offline场景,再新注入1个namespace还是ingesting,所以无法准确区分
	for _, namespace := range namespaces {
		if namespace.Namespace == param.NamespaceParam.TargetNamespace {
			continue
		}
		if !reingest && namespace.Status != x1model.NamespaceStatusOnline &&
			namespace.Status != x1model.NamespaceStatusPurged &&
			namespace.Status != x1model.NamespaceStatusOffline {
			resource.LoggerTask.Warning(ctx, "namespace status is wrong",
				logit.String("namespace", namespace.Namespace), logit.String("status", namespace.Status))
			return errors.New("namespace status is wrong")
		}
		if reingest && namespace.Status != x1model.NamespaceStatusOnline &&
			namespace.Status != x1model.NamespaceStatusOffline {
			resource.LoggerTask.Warning(ctx, "namespace status is wrong",
				logit.String("namespace", namespace.Namespace), logit.String("status", namespace.Status))
			return errors.New("namespace status is wrong")
		}
	}

	return nil
}

// DownloadPrecheckFileNum 检查每个slot sst文件是否和该slot的meta文件一致
func DownloadPrecheckFileNum(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	task, err := x1model.NamespaceTaskGetByTaskID(ctx, param.NamespaceParam.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get task error", logit.Error("error", err))
		return err
	}
	var utils *ns.DownloadUtils
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "s3",
			IamUserID:   app.UserId,
			S3Param: ns.S3Param{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
			},
		})
	} else if task.MirrorBosBucket == "" {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "bos",
			IamUserID:   app.UserId,
			BosParam: ns.BosParam{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
			},
		})
	} else {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "mirrorBos",
			IamUserID:   app.UserId,
			MirrorBosParam: ns.MirrorBosParam{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
				MirrorBucket: task.MirrorBosBucket,
			},
		})
	}
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace bos util error", logit.Error("error", err))
		return err
	}

	err = utils.BucketAndObjectPrefixExist(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check bos fail", logit.Error("error", err))
		return err
	}

	allSlotSstFile, err := utils.ListSstFiles(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list sst files fail", logit.Error("error", err))
		return err
	}

	g := gtask.Group{
		Concurrent: 50,
	}
	for _, singleSlotSstFile := range allSlotSstFile {
		singleSlotSstFile := singleSlotSstFile
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				err := utils.SlotSstFileNumMatch(ctx, singleSlotSstFile)
				if err != nil {
					resource.LoggerTask.Error(ctx, "check slot sst file num fail", logit.Error("error", err))
					return err
				}
				return nil
			})
		})
	}
	_, checkErr := g.Wait()
	if checkErr != nil {
		resource.LoggerTask.Error(ctx, "check slot sst file num error", logit.Error("error", checkErr))
		if cerrs.Is(checkErr, cerrs.ErrNamespaceError) {
			task.ErrorMessage = "SlotSSTNumPrecheckError:" + checkErr.Error()
		}
	}

	total := 0
	for _, singleSlotSstFile := range allSlotSstFile {
		total += singleSlotSstFile.FileNum
	}
	task.SstFileNum = total
	task.UpdateAt = time.Now()

	if err := x1model.NamespaceTaskSaveAll(ctx, []*x1model.NamespaceTask{task}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace task error", logit.Error("error", err))
		return err
	}
	if checkErr != nil {
		return checkErr
	}
	return nil
}

// GetDownloadNodeMeta 获取下载任务的元信息
func GetDownloadNodeMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	task, err := x1model.NamespaceTaskGetByTaskID(ctx, param.NamespaceParam.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get task error", logit.Error("error", err))
		return err
	}

	var utils *ns.DownloadUtils
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "s3",
			IamUserID:   app.UserId,
			S3Param: ns.S3Param{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
			},
		})
	} else if task.MirrorBosBucket == "" {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "bos",
			IamUserID:   app.UserId,
			BosParam: ns.BosParam{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
			},
		})
	} else {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "mirrorBos",
			IamUserID:   app.UserId,
			MirrorBosParam: ns.MirrorBosParam{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
				MirrorBucket: task.MirrorBosBucket,
			},
		})
	}
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace bos util error", logit.Error("error", err))
		return err
	}

	allSlotSstFile, err := utils.ListSstFiles(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list sst files fail", logit.Error("error", err))
		return err
	}

	totalNum := 0
	totalSize := int64(0)
	for _, singleSlotSstFile := range allSlotSstFile {
		totalNum += singleSlotSstFile.FileNum
		totalSize += singleSlotSstFile.FileTotalSize
	}
	if totalNum != task.SstFileNum {
		return errors.New("sst file num is not equal")
	}

	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, app.AppId, param.NamespaceParam.TargetNamespace)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return err
	}

	namespace.IngestSstSizeMB = totalSize / 1024 / 1024
	namespace.UpdateAt = time.Now()

	if err := x1model.NamespaceSaveAll(ctx, []*x1model.Namespace{namespace}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace error", logit.Error("error", err))
		return err
	}

	clusterInfo, err := getClusterSlotInfo(ctx, app)
	if err != nil {
		return err
	}

	size := sumFileTotalSizeByShard(ctx, clusterInfo, allSlotSstFile)
	num := sumFileTotalNumByShard(ctx, clusterInfo, allSlotSstFile)

	downloadProgress := make(DownloadProgress, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			downloadProgress[node.NodeId] = &NodeProgress{
				SlotOffset: -1,
				FileSize:   size[cluster.ClusterShortID],
				FileNum:    num[cluster.ClusterShortID],
			}
		}
	}

	dp, err := base_utils.Marshal(downloadProgress)
	if err != nil {
		resource.LoggerTask.Error(ctx, "marshal download progress error", logit.Error("error", err))
		return err
	}

	task.UpdateAt = time.Now()
	task.Progress = string(dp)

	if err := x1model.NamespaceTaskSaveAll(ctx, []*x1model.NamespaceTask{task}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace task error", logit.Error("error", err))
		return err
	}

	return nil
}

// DownloadPrecheckDisk 预检测硬盘
func DownloadPrecheckDisk(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	task, err := x1model.NamespaceTaskGetByTaskID(ctx, param.NamespaceParam.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get task error", logit.Error("error", err))
		return err
	}

	var progress DownloadProgress
	if err := base_utils.Unmarshal([]byte(task.Progress), &progress); err != nil {
		resource.LoggerTask.Error(ctx, "unmarshal download progress error", logit.Error("error", err))
		return err
	}

	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusInUse {
				host := node.FloatingIP
				port := node.Port
				size := progress[node.NodeId].FileSize

				g.Go(func() error {
					return gtask.NoPanic(func() error {
						op, err := ns.GetOperationClient(ctx, host, port, "")
						defer op.Close(ctx)
						if err != nil {
							resource.LoggerTask.Warning(ctx, "get operation client error", logit.Error("error", err))
							return err
						}
						return op.CheckPegaSizeEnough(ctx, size)
					})
				})
			}
		}
	}

	if _, err := g.Wait(); err != nil {
		task.ErrorMessage = "CapacityPrecheckError:not enough capacity to download"
		task.UpdateAt = time.Now()

		if errInner := x1model.NamespaceTaskSaveAll(ctx, []*x1model.NamespaceTask{task}); errInner != nil {
			resource.LoggerTask.Error(ctx, "save namespace task error", logit.Error("error", errInner))
			return errInner
		}
		resource.LoggerTask.Error(ctx, "check disk enough error", logit.Error("error", err))
		return err
	}
	return nil
}

// DownloadOnAllNode 所有节点下载
func DownloadOnAllNode(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	task, err := x1model.NamespaceTaskGetByTaskID(ctx, param.NamespaceParam.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get task error", logit.Error("error", err))
		return err
	}

	if err = checkAllInUseNodeAlive(ctx, app); err != nil {
		resource.LoggerTask.Error(ctx, "check all inuse node alive error", logit.Error("error", err))
		return err
	}

	var utils *ns.DownloadUtils
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "s3",
			IamUserID:   app.UserId,
			S3Param: ns.S3Param{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
			},
		})
	} else if task.MirrorBosBucket == "" {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "bos",
			IamUserID:   app.UserId,
			BosParam: ns.BosParam{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
			},
		})
	} else {
		utils, err = ns.GetDownloadUtils(ctx, &ns.StorageParam{
			StorageType: "mirrorBos",
			IamUserID:   app.UserId,
			MirrorBosParam: ns.MirrorBosParam{
				Bucket:       task.BosBucket,
				ObjectPrefix: task.BosObjectPrefix,
				MirrorBucket: task.MirrorBosBucket,
			},
		})
	}
	if err != nil {
		resource.LoggerTask.Error(ctx, "get download util error", logit.Error("error", err))
		return err
	}
	allSlotSstFile, err := utils.ListSstFiles(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list sst error", logit.Error("error", err))
		return err
	}

	total := 0
	for _, singleSlotSstFile := range allSlotSstFile {
		total += singleSlotSstFile.FileNum
	}
	if total != task.SstFileNum {
		return errors.New("sst file num is not equal")
	}

	var progress DownloadProgress

	if err = base_utils.Unmarshal([]byte(task.Progress), &progress); err != nil {
		resource.LoggerTask.Error(ctx, "unmarshal progress error", logit.Error("error", err))
		return err
	}

	downloadInfos, err := utils.GetAllSlotDownloadInfo(ctx, allSlotSstFile)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get download info error", logit.Error("error", err))
		return err
	}

	downloadTasks, err := generateBatchDownloadTask(ctx, app, progress, downloadInfos,
		utils.GetMaxSlotNum(ctx), utils.GetMaxSstFileSize(ctx))
	if err != nil {
		resource.LoggerTask.Error(ctx, "generate download task", logit.Error("error", err))
		return err
	}
	maxItemNum := 0
	for i := 0; i < len(downloadTasks); i++ { //fmt.Println(downloadTask.ItemNum)
		if downloadTasks[i].ItemNum > maxItemNum {
			maxItemNum = downloadTasks[i].ItemNum
		}
	}

	//任务续传，进度也续做
	progressCurPercent := task.ProgressPercent
	trafficLimit := min(utils.GetTrafficLimitMB(ctx), BosMaxDownloadRateMB/len(downloadTasks))
	for i := 0; i < maxItemNum; i++ {
		xagentParams := make([]sstOperationParam, 0)
		xagentAddrs := make([]*xagent.Addr, 0)
		for _, downloadTask := range downloadTasks {
			if downloadTask.ItemNum <= i {
				continue
			}
			xagentParams = append(xagentParams, sstOperationParam{
				Action:                    "download",
				DownloadDir:               ns.GetDownloadDir(ctx, task.Namespace),
				DiskUsagePercentThreshold: utils.GetDiskUsagePercentThreshold(ctx),
				DiskFreeMbytesThreshold:   utils.GetDiskFreeBytesThreshold(ctx) / 1024 / 1024,
				FileItems:                 downloadTask.Items[i].Files,
				TrafficLimit:              trafficLimit,
			})
			xagentAddrs = append(xagentAddrs, &xagent.Addr{
				Host: downloadTask.NodeFloatingIP,
				Port: cast.ToInt32(downloadTask.NodeXagentPort),
			})
		}
		if err := batchDownload(ctx, xagentParams, xagentAddrs, false); err != nil {
			resource.LoggerTask.Error(ctx, "batch download error", logit.Error("error", err))
			return err
		}

		for _, downloadTask := range downloadTasks {
			if downloadTask.ItemNum <= i {
				continue
			}
			if downloadTask.Items[i].SlotNum < 1 {
				continue
			}
			progress[downloadTask.NodeID].SlotOffset = downloadTask.Items[i].SlotIDs[downloadTask.Items[i].SlotNum-1]
		}
		if err := updateTaskProgress(ctx, param.NamespaceParam.TaskID, progress,
			calculateProgressPercent(i+1, maxItemNum, progressCurPercent)); err != nil {
			resource.LoggerTask.Error(ctx, "update task progress error", logit.Error("error", err))
			return err
		}
	}
	return nil
}

// RollbackDownloadOnSlave 回滚slave上的sst文件
func RollbackDownloadOnSlave(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Role != x1model.RoleTypeSlave || node.Status != x1model.NodeOrProxyStatusInUse {
		return nil
	}

	if err = cleanupFileOnNode(ctx, node, param.NamespaceParam.TargetNamespace); err != nil {
		resource.LoggerTask.Warning(ctx, "cleanup file fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	return nil
}

// RollbackDownloadOnMaster 回滚master上的sst文件
func RollbackDownloadOnMaster(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Role != x1model.RoleTypeMaster || node.Status != x1model.NodeOrProxyStatusInUse {
		return nil
	}

	if err = cleanupFileOnNode(ctx, node, param.NamespaceParam.TargetNamespace); err != nil {
		resource.LoggerTask.Warning(ctx, "cleanup file fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	return nil
}

// IngestNamespacePrecheckNode 注入前检查
func IngestNamespacePrecheckNode(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Role == x1model.RoleTypeSlave && node.Status == x1model.NodeOrProxyStatusInUse {
		if err := checkNodeSync(ctx, node); err != nil {
			resource.LoggerTask.Error(ctx, "check sync fail",
				logit.String("node", base_utils.Format(node)))
			return err
		}
	}

	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, node.AppId, param.NamespaceParam.TargetNamespace)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	namespaceDetail, err := getNodeNamespaceDetail(ctx, node)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace detail fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}

	_, found := namespaceDetail[param.NamespaceParam.TargetNamespace]
	// 非重复注入则报错
	if found && namespace.Status == x1model.NamespaceStatusIngesting {
		resource.LoggerTask.Error(ctx, "namespace is exist",
			logit.String("node", base_utils.Format(node)),
			logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return errors.New("namespace is exist")
	}
	return nil
}

// IngestNamespaceOnAllShard 所有分片执行注入命令
func IngestNamespaceOnAllShard(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	cluster, err := x1model.ClusterGetByClusterId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster fail", logit.String("cluster_id", teu.Entity))
		return err
	}

	var masterNode *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
			masterNode = node
		}
	}
	if masterNode == nil {
		resource.LoggerTask.Notice(ctx, "no find master node", logit.String("cluster_id", teu.Entity))
		return errors.New("no find master node")
	}

	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, cluster.AppId, param.NamespaceParam.TargetNamespace)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := ingestNamespaceOnNode(ctx, masterNode, param.NamespaceParam.TargetNamespace,
		param.NamespaceParam.TaskID, namespace.Status == x1model.NamespaceStatusReIngesting); err != nil {
		resource.LoggerTask.Warning(ctx, "ingest namespace fail", logit.String("node", base_utils.Format(masterNode)),
			logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return err
	}

	return nil
}

// SumKeysNum 计算注入命名空间的总数量
func SumKeysNum(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	keysNum := int64(0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
				nodeKeysNum, err := getNamespaceKeysNum(ctx, node)
				if err != nil {
					return err
				}
				keysNum += nodeKeysNum
			}
		}
	}

	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, app.AppId, param.NamespaceParam.TargetNamespace)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return err
	}

	namespace.IngestKeysNum = keysNum
	namespace.UpdateAt = time.Now()

	if err := x1model.NamespaceSaveAll(ctx, []*x1model.Namespace{namespace}); err != nil {
		resource.LoggerTask.Error(ctx, "save ingest task error", logit.Error("error", err))
		return err
	}

	return nil
}

// CheckDownloadedSstFileNum 检查下载的sst文件数量是否符合预期
func CheckDownloadedSstFileNum(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	task, err := x1model.NamespaceTaskGetByTaskID(ctx, param.NamespaceParam.TaskID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get task error", logit.Error("error", err))
		return err
	}
	var progress DownloadProgress
	if err := base_utils.Unmarshal([]byte(task.Progress), &progress); err != nil {
		resource.LoggerTask.Error(ctx, "unmarshal download progress error", logit.Error("error", err))
		return err
	}
	xagentParams := make([]sstOperationParam, 0)
	xagentAddrs := make([]*xagent.Addr, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			// 如果分片内预期下载的sst文件数量为0，则不需要检查
			if progress[node.NodeId].FileNum == 0 {
				continue
			}
			xagentParams = append(xagentParams, sstOperationParam{
				Action:      "check_num",
				DownloadDir: ns.GetDownloadDir(ctx, task.Namespace),
				FileNum:     progress[node.NodeId].FileNum,
			})
			xagentAddrs = append(xagentAddrs, &xagent.Addr{
				Host: node.FloatingIP,
				Port: cast.ToInt32(node.XagentPort),
			})
		}
	}
	if err := batchCheckFileNum(ctx, xagentParams, xagentAddrs); err != nil {
		resource.LoggerTask.Error(ctx, "batch check file num error", logit.Error("error", err))
		return err
	}

	return nil
}

// RollbackIngestNamespaceOnAllShard 删除注入的命名空间
func RollbackIngestNamespaceOnAllShard(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	cluster, err := x1model.ClusterGetByClusterId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster fail", logit.String("cluster_id", teu.Entity))
		return err
	}

	var masterNode *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
			masterNode = node
		}
	}
	if masterNode == nil {
		resource.LoggerTask.Notice(ctx, "no find master node", logit.String("cluster_id", teu.Entity))
		return errors.New("no find master node")
	}

	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, cluster.AppId,
		param.NamespaceParam.TargetNamespace)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace list fail", logit.Error("err", err))
		return err
	}
	// 重复注入不删除namespace
	if namespace.Status == x1model.NamespaceStatusIngesting {
		if err = deleteNamespaceOnNode(ctx, masterNode, param.NamespaceParam.TargetNamespace); err != nil {
			resource.LoggerTask.Warning(ctx, "delete namespace on master node fail", logit.String("node", base_utils.Format(masterNode)),
				logit.String("cluster_id", teu.Entity), logit.String("namespace", param.NamespaceParam.TargetNamespace))
			return err
		}
	}

	return nil
}

// IngestNamespaceSuccessCb 成功回调
func IngestNamespaceSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := updateMetaAfterIngest(ctx, app.AppId, param.NamespaceParam.TargetNamespace, param.NamespaceParam.TaskID,
		x1model.NamespaceStatusOffline, x1model.NamespaceTaskStatusSuccess); err != nil {
		resource.LoggerTask.Error(ctx, "update meta info error", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

// IngestNamespaceFailCb 失败回调
func IngestNamespaceFailCb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, app.AppId, param.NamespaceParam.TargetNamespace)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return err
	}
	if namespace.Status == x1model.NamespaceStatusIngesting {
		if err := x1model.NamespaceDeleteOne(ctx, namespace.ID); err != nil {
			resource.LoggerTask.Error(ctx, "delete namespace error", logit.Error("error", err))
			return err
		}
	} else if namespace.Status == x1model.NamespaceStatusReIngesting {
		namespace.Status = x1model.NamespaceStatusOffline
		if err := x1model.NamespaceSaveAll(ctx, []*x1model.Namespace{namespace}); err != nil {
			resource.LoggerTask.Error(ctx, "save namespace error", logit.Error("error", err))
			return err
		}
	} else if namespace.Status != x1model.NamespaceStatusOffline {
		//reingesting->offline重入判断
		return errors.New("namespace status error")
	}

	if err := updateTaskStatus(ctx, param.NamespaceParam.TaskID, x1model.NamespaceTaskStatusFail); err != nil {
		resource.LoggerTask.Error(ctx, "update task status error", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusModifyFailed,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

// RollbackDownloadCheckSync 检查同步
func RollbackDownloadCheckSync(ctx context.Context, teu *workflow.TaskExecUnit) error {

	_, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Role == x1model.RoleTypeSlave && node.Status == x1model.NodeOrProxyStatusInUse {
		if err := checkNodeSync(ctx, node); err != nil {
			resource.LoggerTask.Error(ctx, "check sync fail",
				logit.String("node", base_utils.Format(node)))
			return err
		}
	}
	return nil
}

// CreateDefaultNamespace 创建默认命名空间
func CreateDefaultNamespace(ctx context.Context, teu *workflow.TaskExecUnit) error {

	_, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	//// 初期灌库功能默认不开放,等待内核发布主干版本
	//if !privatecloud.IsPrivateENV() || privatecloud.GetPrivateEnvType() != privatecloud.DBStackPrefix {
	//	return nil
	//}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if util.GetEngine(app) != x1model.EnginePegaDB {
		return nil
	}

	ingestNamespace := &x1model.Namespace{
		AppID:           app.AppId,
		Namespace:       DefaultNamespace,
		BosBucket:       "",
		Status:          x1model.NamespaceStatusOnline,
		BosObjectPrefix: "",
		CreateAt:        time.Now(),
		UpdateAt:        time.Now(),
	}

	if err := x1model.NamespaceSaveAll(ctx, []*x1model.Namespace{ingestNamespace}); err != nil {
		resource.LoggerTask.Warning(ctx, "save namespace fail", logit.Error("err", err),
			logit.String("app_id", app.AppId), logit.String("namespace", DefaultNamespace))
		return err
	}

	return nil
}

// RemoveEmptyDirOnNode 清理注入成功后的空文件夹
func RemoveEmptyDirOnNode(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Status != x1model.NodeOrProxyStatusInUse {
		return nil
	}

	if err = RemoveEmptyDownloadDir(ctx, node, param.NamespaceParam.TargetNamespace); err != nil {
		resource.LoggerTask.Warning(ctx, "remove empty dir fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	return nil
}

// CreateEmptyDirOnNode 创建空文件夹
func CreateEmptyDirOnNode(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Status != x1model.NodeOrProxyStatusInUse {
		return nil
	}

	if err = CreateEmptyDownloadDir(ctx, node, param.NamespaceParam.TargetNamespace); err != nil {
		resource.LoggerTask.Warning(ctx, "create empty dir fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	return nil
}
