package namespace_task

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	ns "icode.baidu.com/baidu/scs/x1-base/component/namespace"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// SwitchNamespacePrepare 准备工作
func SwitchNamespacePrepare(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if err = validateParam(ctx, param); err != nil {
		resource.LoggerTask.Error(ctx, "validate params error", logit.Error("error", err))
		return err
	}

	if err = updateTaskStatus(ctx, param.NamespaceParam.TaskID, x1model.NamespaceTaskStatusRunning); err != nil {
		resource.LoggerTask.Error(ctx, "update task status error", logit.Error("error", err))
		return err
	}

	return nil
}

// SwitchNamespacePrecheckMeta 检查元数据
func SwitchNamespacePrecheckMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	namespaces, err := x1model.NamespaceGetAllByAppID(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err))
		return err
	}

	var target, origin *x1model.Namespace
	for _, namespace := range namespaces {
		if (namespace.Status == x1model.NamespaceStatusOffline || namespace.Status == x1model.NamespaceStatusOnlining) &&
			namespace.Namespace == param.NamespaceParam.TargetNamespace {
			target = namespace
			continue
		}
		if (namespace.Status == x1model.NamespaceStatusOnline || namespace.Status == x1model.NamespaceStatusOfflining) &&
			namespace.Namespace == param.NamespaceParam.OriginNamespace {
			origin = namespace
			continue
		}
		if namespace.Status != x1model.NamespaceStatusPurged {
			resource.LoggerTask.Warning(ctx, "namespace status is wrong",
				logit.String("namespace", namespace.Namespace), logit.String("status", namespace.Status))
			return errors.New("namespace status is wrong")
		}
	}
	if origin == nil || target == nil {
		resource.LoggerTask.Warning(ctx, "namespace is not exist")
		return errors.New("namespace is not exist")
	}

	if err := updateNamespaceStatus(ctx, app.AppId, param.NamespaceParam.OriginNamespace, x1model.NamespaceStatusOfflining); err != nil {
		resource.LoggerTask.Error(ctx, "update namespace status error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.OriginNamespace),
			logit.String("status", x1model.NamespaceStatusOfflining))
		return err
	}

	if err := updateNamespaceStatus(ctx, app.AppId, param.NamespaceParam.TargetNamespace, x1model.NamespaceStatusOnlining); err != nil {
		resource.LoggerTask.Error(ctx, "update namespace status error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.TargetNamespace),
			logit.String("status", x1model.NamespaceStatusOnlining))
		return err
	}

	return nil
}

// SwitchNamespacePrecheckNode 检查节点命名空间和延迟
func SwitchNamespacePrecheckNode(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Role == x1model.RoleTypeSlave && node.Status == x1model.NodeOrProxyStatusInUse {
		if err := checkNodeSync(ctx, node); err != nil {
			return err
		}
	}

	namespaceDetail, err := getNodeNamespaceDetail(ctx, node)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace detail fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	detail, found := namespaceDetail[param.NamespaceParam.TargetNamespace]
	if !found {
		resource.LoggerTask.Error(ctx, "namespace is not exist",
			logit.String("node", base_utils.Format(node)),
			logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return errors.New("namespace is not exist")
	}
	if detail != ns.NamespaceDetailOffline {
		return errors.New("namespace detail is not offline")
	}

	detail, found = namespaceDetail[param.NamespaceParam.OriginNamespace]
	if !found {
		resource.LoggerTask.Error(ctx, "namespace is not exist",
			logit.String("node", base_utils.Format(node)),
			logit.String("namespace", param.NamespaceParam.OriginNamespace))
		return errors.New("namespace is not exist")
	}
	if detail != ns.NamespaceDetailOnline {
		return errors.New("namespace detail is not online")
	}

	return nil
}

// SwitchNamespaceOnAllCluster 所有分片执行切换
func SwitchNamespaceOnAllCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	cluster, err := x1model.ClusterGetByClusterId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster fail", logit.String("cluster_id", teu.Entity))
		return err
	}

	var masterNode *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
			masterNode = node
		}
	}
	if masterNode == nil {
		resource.LoggerTask.Notice(ctx, "no find master node", logit.String("cluster_id", teu.Entity))
		return errors.New("no find master node")
	}

	if err = switchNamespaceOnNode(ctx, masterNode, param.NamespaceParam.TargetNamespace); err != nil {
		resource.LoggerTask.Warning(ctx, "switch namespace on master node fail", logit.String("node", base_utils.Format(masterNode)),
			logit.String("cluster_id", teu.Entity), logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return err
	}
	return nil

}

// RollbackSwitchNamespaceOnAllCluster 回滚切换
func RollbackSwitchNamespaceOnAllCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	cluster, err := x1model.ClusterGetByClusterId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster fail", logit.String("cluster_id", teu.Entity))
		return err
	}

	var masterNode *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
			masterNode = node
		}
	}
	if masterNode == nil {
		resource.LoggerTask.Notice(ctx, "no find master node", logit.String("cluster_id", teu.Entity))
		return errors.New("no find master node")
	}

	if err = switchNamespaceOnNode(ctx, masterNode, param.NamespaceParam.OriginNamespace); err != nil {
		resource.LoggerTask.Warning(ctx, "switch namespace on master node fail", logit.String("node", base_utils.Format(masterNode)),
			logit.String("cluster_id", teu.Entity), logit.String("namespace", param.NamespaceParam.TargetNamespace))
		return err
	}
	return nil
}

// SwitchNamespaceSuccessCb 切换成功回调
func SwitchNamespaceSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := updateNamespaceStatus(ctx, app.AppId, param.NamespaceParam.TargetNamespace, x1model.NamespaceStatusOnline); err != nil {
		resource.LoggerTask.Error(ctx, "update namespace status error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.OriginNamespace),
			logit.String("status", x1model.NamespaceStatusOnline))
		return err
	}

	if err := updateNamespaceStatus(ctx, app.AppId, param.NamespaceParam.OriginNamespace, x1model.NamespaceStatusOffline); err != nil {
		resource.LoggerTask.Error(ctx, "update namespace status error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.TargetNamespace),
			logit.String("status", x1model.NamespaceStatusOffline))
		return err
	}

	if err := updateTaskStatus(ctx, param.NamespaceParam.TaskID, x1model.NamespaceTaskStatusSuccess); err != nil {
		resource.LoggerTask.Error(ctx, "update task status error", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}

	return nil
}

// SwitchNamespaceFailCb 切换失败回调
func SwitchNamespaceFailCb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if err := updateNamespaceStatus(ctx, app.AppId, param.NamespaceParam.OriginNamespace, x1model.NamespaceStatusOnline); err != nil {
		resource.LoggerTask.Error(ctx, "update namespace status error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.OriginNamespace),
			logit.String("status", x1model.NamespaceStatusOnline))
		return err
	}

	if err := updateNamespaceStatus(ctx, app.AppId, param.NamespaceParam.TargetNamespace, x1model.NamespaceStatusOffline); err != nil {
		resource.LoggerTask.Error(ctx, "update namespace status error", logit.Error("error", err),
			logit.String("app_id", app.AppId), logit.String("namespace", param.NamespaceParam.TargetNamespace),
			logit.String("status", x1model.NamespaceStatusOffline))
		return err
	}

	if err := updateTaskStatus(ctx, param.NamespaceParam.TaskID, x1model.NamespaceTaskStatusFail); err != nil {
		resource.LoggerTask.Error(ctx, "update task status error", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusModifyFailed,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}
