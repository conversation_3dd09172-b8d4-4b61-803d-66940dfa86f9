package namespace_task

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	ns "icode.baidu.com/baidu/scs/x1-base/component/namespace"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type sstOperationParam struct {
	Action                    string                 `json:"action"`
	DownloadDir               string                 `json:"download_dir"`
	DiskUsagePercentThreshold int64                  `json:"disk_usage_percent_threshold"`
	DiskFreeMbytesThreshold   int64                  `json:"disk_free_mbytes_threshold"`
	FileItems                 []*ns.FileDownloadInfo `json:"file_items"`
	TrafficLimit              int                    `json:"traffic_limit"`
	Step                      int                    `json:"step"`
	FileNum                   int                    `json:"file_num"`
}

type sstOperationResp struct {
	Result bool `json:"result"`
}

type NodeDownloadSstTaskItem struct {
	SlotNum       int                    `json:"slotNum"`
	FileTotalSize int64                  `json:"fileTotalSize"`
	SlotIDs       []int                  `json:"slotIDs"`
	Files         []*ns.FileDownloadInfo `json:"files"`
}

type NodeDownloadSstTask struct {
	NodeID         string                     `json:"nodeID"`
	NodeFloatingIP string                     `json:"nodeFloatingIP"`
	NodeXagentPort int                        `json:"nodeXagentPort"`
	ItemNum        int                        `json:"itemNum"`
	Items          []*NodeDownloadSstTaskItem `json:"items"`
}

type NodeProgress struct {
	SlotOffset int   `json:"slotOffset"`
	FileSize   int64 `json:"fileSize"`
	FileNum    int   `json:"fileNum"`
}

type DownloadProgress map[string]*NodeProgress

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// updateTaskStatus 更新任务状态
func updateTaskStatus(ctx context.Context, taskID string, taskStatus string) error {

	namespaceTask, err := x1model.NamespaceTaskGetByTaskID(ctx, taskID)

	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace task error", logit.Error("error", err))
		return err
	}

	if namespaceTask.Status == taskStatus {
		return nil
	}

	namespaceTask.Status = taskStatus
	namespaceTask.UpdateAt = time.Now()
	if err := x1model.NamespaceTaskSaveAll(ctx, []*x1model.NamespaceTask{namespaceTask}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace task error", logit.Error("error", err))
		return err
	}

	return nil
}

// updateTaskProgress 更新任务进度
func updateTaskProgress(ctx context.Context, taskID string, progress DownloadProgress, progressPercent float64) error {

	namespaceTask, err := x1model.NamespaceTaskGetByTaskID(ctx, taskID)

	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace task error", logit.Error("error", err))
		return err
	}

	if namespaceTask.Status == x1model.NamespaceTaskStatusCancel {
		return cerrs.ErrorTaskManual.Errorf("%s: namespace task status is cancel", taskID)
	}

	dp, err := base_utils.Marshal(progress)
	if err != nil {
		resource.LoggerTask.Error(ctx, "marshal download progress error", logit.Error("error", err))
		return err
	}

	namespaceTask.Progress = string(dp)
	namespaceTask.ProgressPercent = progressPercent
	namespaceTask.UpdateAt = time.Now()
	if err := x1model.NamespaceTaskSaveAll(ctx, []*x1model.NamespaceTask{namespaceTask}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace task error", logit.Error("error", err))
		return err
	}

	return nil
}

// updateNamespaceStatus 更新命名空间状态
func updateNamespaceStatus(ctx context.Context, appID string, namespaceName string, status string) error {

	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, appID, namespaceName)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err),
			logit.String("app_id", appID), logit.String("namespace", namespaceName))
		return err
	}
	if namespace.Status == status {
		return nil
	}

	namespace.Status = status
	namespace.UpdateAt = time.Now()

	if err := x1model.NamespaceSaveAll(ctx, []*x1model.Namespace{namespace}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace error", logit.Error("error", err))
		return err
	}

	return nil
}

// updateMetaAfterIngest 更新命名空间状态
func updateMetaAfterIngest(ctx context.Context, appID, namespaceName, taskID, nsstatus, taskstatus string) error {

	namespaceTask, err := x1model.NamespaceTaskGetByTaskID(ctx, taskID)

	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace task error", logit.Error("error", err))
		return err
	}
	namespace, err := x1model.NamespaceGetByAppIDAndNamespace(ctx, appID, namespaceName)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get namespace error", logit.Error("error", err),
			logit.String("app_id", appID), logit.String("namespace", namespaceName))
		return err
	}

	if namespace.Status != nsstatus {
		namespace.Status = nsstatus
		namespace.UpdateAt = time.Now()
		namespace.BosBucket = namespaceTask.BosBucket
		namespace.BosObjectPrefix = namespaceTask.BosObjectPrefix
		namespace.MirrorBosBucket = namespaceTask.MirrorBosBucket

		if err := x1model.NamespaceSaveAll(ctx, []*x1model.Namespace{namespace}); err != nil {
			resource.LoggerTask.Error(ctx, "save namespace error", logit.Error("error", err))
			return err
		}
	}

	if namespaceTask.Status == taskstatus {
		return nil
	}

	namespaceTask.Status = taskstatus
	namespaceTask.UpdateAt = time.Now()
	if err := x1model.NamespaceTaskSaveAll(ctx, []*x1model.NamespaceTask{namespaceTask}); err != nil {
		resource.LoggerTask.Error(ctx, "save namespace task error", logit.Error("error", err))
		return err
	}
	return nil
}

// validateParam
func validateParam(ctx context.Context, param *iface.Parameters) error {

	if param.NamespaceParam == nil {
		resource.LoggerTask.Error(ctx, "namespace param is nil")
		return errors.New("namespace param is nil")
	}
	return nil

}

// sumFileTotalSizeByShard 计算每个分片总的文件大小
func sumFileTotalSizeByShard(ctx context.Context, clusterInfo *metaserver.Cluster, allSlotSstFile []*ns.SingleSlotSstFile) map[int]int64 {

	shardFileTotalSize := make(map[int]int64, 0)
	for shardID, slotRanges := range clusterInfo.Slot {
		shardFileTotalSize[shardID] = 0
		for _, slotRange := range slotRanges {
			for i := slotRange[0]; i <= slotRange[1]; i++ {
				shardFileTotalSize[shardID] += allSlotSstFile[i].FileTotalSize
			}
		}
	}
	return shardFileTotalSize
}

// sumFileTotalNumByShard 计算每个分片总的文件数量
func sumFileTotalNumByShard(ctx context.Context, clusterInfo *metaserver.Cluster, allSlotSstFile []*ns.SingleSlotSstFile) map[int]int {

	shardFileTotalSize := make(map[int]int, 0)
	for shardID, slotRanges := range clusterInfo.Slot {
		shardFileTotalSize[shardID] = 0
		for _, slotRange := range slotRanges {
			for i := slotRange[0]; i <= slotRange[1]; i++ {
				shardFileTotalSize[shardID] += allSlotSstFile[i].FileNum
			}
		}
	}
	return shardFileTotalSize
}

// getNodeNamespaceDetail 获取命名空间状态
func getNodeNamespaceDetail(ctx context.Context, node *x1model.Node) (map[string]string, error) {

	op, err := ns.GetOperationClient(ctx, node.FloatingIP, node.Port, "")
	defer op.Close(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check operation client", logit.Error("error", err))
		return nil, err
	}
	return op.NamespaceDetails(ctx)
}

// 检测节点同步状态
func checkNodeSync(ctx context.Context, node *x1model.Node) error {
	replicationInfo, err := util.GetReplicationInfo(ctx, node.FloatingIP, node.Port, "")
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get replication info failed",
			logit.String("node", base_utils.Format(node)),
			logit.Error("error", err))
		return err
	}

	if replicationInfo.MasterLinkStatus != "up" ||
		replicationInfo.SlaveReplOffset-replicationInfo.MasterReplOffset > cast.ToInt64(ns.GetOperationReplicationOffset(ctx)) {
		resource.LoggerTask.Notice(ctx, "check sync fail", logit.String("node", base_utils.Format(node)))
		return errors.New("node sync check not pass")
	}
	return nil
}

// 删除命名空间
func deleteNamespaceOnNode(ctx context.Context, node *x1model.Node, namespace string) error {

	op, err := ns.GetOperationClient(ctx, node.FloatingIP, node.Port, "")
	defer op.Close(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get operation client error", logit.Error("error", err))
		return err
	}
	namespaceDetail, err := op.NamespaceDetails(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace detail", logit.Error("error", err))
		return err
	}
	_, found := namespaceDetail[namespace]
	// 未找到表示已成功删除
	if !found {
		resource.LoggerTask.Notice(ctx, "namespace already delete", logit.String("namespace", namespace))
		return nil
	}
	task, err := op.GetDelTask(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get del task error", logit.Error("error", err))
		return err
	}

	doDelete := true
	if task.Namespace == namespace {
		if task.Status == ns.OperationTaskStatusFail {
			return cerrs.ErrorTaskManual.Errorf("%s: delete namespace task status is fail", node.NodeId)
		} else if task.Status == ns.OperationTaskStatusSuccess {
			// 解决多次注入多次删除场景问题
			namespaceDetail, err := op.NamespaceDetails(ctx)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "delete namespace detail error", logit.Error("error", err))
				return err
			}
			_, found := namespaceDetail[namespace]
			if !found {
				return nil
			}
		} else {
			doDelete = false
		}
	}
	if doDelete {
		err := op.DelNamespace(ctx, namespace)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "del namespace error", logit.Error("error", err))
			return err
		}
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(TaskTimeout)*time.Second)
	defer cancel()

delLoop:
	for {
		result, err := op.GetDelTask(ctx)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get del namespace task error", logit.Error("error", err))
		} else {
			switch result.Status {
			case ns.OperationTaskStatusFail:
				return cerrs.ErrorTaskManual.Errorf("%s: del namespace task status is fail", node.NodeId)
			case ns.OperationTaskStatusSuccess:
				break delLoop
			}
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(CheckTaskInterval):
			break
		}
	}
	return nil
}

// switchNamespaceOnNode 切换命名空间
func switchNamespaceOnNode(ctx context.Context, node *x1model.Node, namespace string) error {

	op, err := ns.GetOperationClient(ctx, node.FloatingIP, node.Port, "")
	defer op.Close(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get operation client error", logit.Error("error", err))
		return err
	}
	namespaceDetail, err := op.NamespaceDetails(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace detail", logit.Error("error", err))
		return err
	}
	detail, found := namespaceDetail[namespace]
	if found && detail == ns.NamespaceDetailOnline {
		return nil
	}
	task, err := op.GetSwitchTask(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get switch task error", logit.Error("error", err))
		return err
	}
	if task.Namespace == namespace {
		// 切换是互斥的，不需要单独处理成功状态的任务
		if task.Status == ns.OperationTaskStatusFail {
			return errors.Errorf("%s switch status is fail", node.NodeId)
		}
	} else {
		err := op.SwitchNamespace(ctx, namespace)
		if err != nil {
			resource.LoggerTask.Error(ctx, "delete namespace detail error", logit.Error("error", err))
			return err
		}
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(TaskTimeout)*time.Second)
	defer cancel()

switchLoop:
	for {
		result, err := op.GetSwitchTask(ctx)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get del namespace task error", logit.Error("error", err))
		} else {
			switch result.Status {
			case ns.OperationTaskStatusFail:
				return cerrs.ErrorTaskManual.Errorf("%s: del namespace task status is fail", node.NodeId)
			case ns.OperationTaskStatusSuccess:
				break switchLoop
			}
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(CheckTaskInterval):
			break
		}
	}
	return nil
}

// 注入命名空间
func ingestNamespaceOnNode(ctx context.Context, node *x1model.Node, namespace, taskID string, reingest bool) error {

	op, err := ns.GetOperationClient(ctx, node.FloatingIP, node.Port, "")
	defer op.Close(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get operation client error", logit.Error("error", err))
		return err
	}
	namespaceDetail, err := op.NamespaceDetails(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get namespace detail error", logit.Error("error", err))
		return err
	}
	_, found := namespaceDetail[namespace]
	if found && !reingest {
		return nil
	}
	task, err := op.GetIngestTask(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get ingest task error", logit.Error("error", err))
		return err
	}

	doIngest := true
	if task.Namespace == namespace {
		if task.Status == ns.OperationTaskStatusFail {
			return cerrs.ErrorTaskManual.Errorf("%s: ingest namespace task status is fail", node.NodeId)
		} else if task.Status == ns.OperationTaskStatusSuccess {
			// 解决多次注入多次删除场景问题
			namespaceDetail, err := op.NamespaceDetails(ctx)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get namespace detail error", logit.Error("error", err))
				return err
			}
			_, found := namespaceDetail[namespace]
			if found && (task.TaskID == "" || task.TaskID == taskID) {
				return nil
			}
		} else {
			doIngest = false
		}
	}

	if doIngest {
		err := op.IngestDirWithTaskID(ctx, ns.GetDownloadDir(ctx, namespace), namespace, taskID)
		if err != nil {
			if !strings.Contains(err.Error(), "ERR BULKLOAD subcommand") {
				resource.LoggerTask.Error(ctx, "ingest namespace error", logit.Error("error", err))
				return err
			}
			resource.LoggerTask.Warning(ctx, "ingest not support task id", logit.Error("error", err))

			err := op.IngestDir(ctx, ns.GetDownloadDir(ctx, namespace), namespace)
			if err != nil {
				resource.LoggerTask.Error(ctx, "ingest namespace error", logit.Error("error", err))
				return err
			}
		}
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(TaskTimeout)*time.Second)
	defer cancel()

ingestLoop:
	for {
		result, err := op.GetIngestTask(ctx)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get ingest namespace task error", logit.Error("error", err))
		} else {
			switch result.Status {
			case ns.OperationTaskStatusFail:
				return cerrs.ErrorTaskManual.Errorf("%s: ingest namespace task status is fail", node.NodeId)
			case ns.OperationTaskStatusSuccess:
				break ingestLoop
			}
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(CheckTaskInterval):
			break
		}
	}
	return nil
}

// cleanupFileOnNode 清理文件
func cleanupFileOnNode(ctx context.Context, node *x1model.Node, namespace string) error {

	xagentParams := sstOperationParam{
		Action:      "cleanup",
		DownloadDir: ns.GetDownloadDir(ctx, namespace),
		Step:        ns.GetCleanupStep(ctx),
	}
	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}
	downloadReq := xagent.AsyncRequest{
		Addr:       &xAgentAddr,
		Action:     "sst_operation",
		Params:     xagentParams,
		TimeoutSec: 50,
	}
	var cleanRsp sstOperationResp
cleanupLoop:
	for {
		t := xagent.Instance().DoAsync(ctx, &downloadReq)
		rsp, err := t.Wait()
		if err != nil {
			return err
		}
		if err = json.Unmarshal([]byte(rsp.TaskResult), &cleanRsp); err != nil {
			resource.LoggerTask.Warning(ctx, "parse response error", logit.Error("error", err))
			return err
		}
		switch cleanRsp.Result {
		case true:
			break cleanupLoop
		case false:
			time.Sleep(time.Second * 1)
			continue
		default:
			return errors.New("unexpect response")
		}

	}
	return nil
}

// getNamespaceKeysNum 获取key数量
func getNamespaceKeysNum(ctx context.Context, node *x1model.Node) (int64, error) {

	op, err := ns.GetOperationClient(ctx, node.FloatingIP, node.Port, "")
	defer op.Close(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check operation client", logit.Error("error", err))
		return 0, err
	}
	task, err := op.GetIngestTask(ctx)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get ingest task", logit.Error("error", err))
		return 0, err
	}
	return cast.ToInt64(task.KeyNum), nil
}

// batchDownload 批量下载
func batchDownload(ctx context.Context, params []sstOperationParam, addrs []*xagent.Addr, reentry bool) error {

	if len(params) != len(addrs) {
		resource.LoggerTask.Warning(ctx, "param len not match")
		return errors.New("param len not match")
	}

	asyncTasks := make(map[int]*xagent.TaskContext, 0)
	for i, param := range params {
		if len(param.FileItems) == 0 {
			continue
		}
		downloadReq := &xagent.AsyncRequest{
			Addr:       addrs[i],
			Action:     "sst_operation",
			Params:     param,
			TimeoutSec: cast.ToInt32(TaskTimeout),
		}
		asyncTasks[i] = xagent.Instance().DoAsync(ctx, downloadReq)
	}

	//对于失败的任务,小成本快速重试下
	retryParam := make([]sstOperationParam, 0)
	retryAddr := make([]*xagent.Addr, 0)

	for index, task := range asyncTasks {
		_, err := task.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "download sst file fail", logit.String("floating ip", addrs[index].Host),
				logit.Error("err", err))
			if reentry {
				return err
			} else {
				retryParam = append(retryParam, params[index])
				retryAddr = append(retryAddr, addrs[index])
			}
		}
	}
	if len(retryParam) == 0 {
		return nil
	}

	return batchDownload(ctx, retryParam, retryAddr, true)
}

// batchDownload 获取key数量
func batchCheckFileNum(ctx context.Context, params []sstOperationParam, addrs []*xagent.Addr) error {

	if len(params) != len(addrs) {
		resource.LoggerTask.Warning(ctx, "param len not match")
		return errors.New("param len not match")
	}

	asyncTasks := make(map[string]*xagent.TaskContext, 0)
	for i, param := range params {
		downloadReq := &xagent.AsyncRequest{
			Addr:       addrs[i],
			Action:     "sst_operation",
			Params:     param,
			TimeoutSec: cast.ToInt32(TaskTimeout),
		}
		asyncTasks[addrs[i].Host] = xagent.Instance().DoAsync(ctx, downloadReq)
	}

	var checkRsp sstOperationResp
	for host, task := range asyncTasks {
		rsp, err := task.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "download sst file fail", logit.String("floating ip", host),
				logit.Error("err", err))
			return err
		}
		if err = json.Unmarshal([]byte(rsp.TaskResult), &checkRsp); err != nil {
			resource.LoggerTask.Warning(ctx, "parse response error", logit.Error("error", err))
			return err
		}
		if !checkRsp.Result {
			resource.LoggerTask.Warning(ctx, "check sst file fail", logit.String("floating ip", host))
			return errors.New("check sst file num fail")
		}
	}
	return nil
}

func getClusterSlotInfo(ctx context.Context, app *x1model.Application) (*metaserver.Cluster, error) {

	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get meta cluster fail", logit.Error("error", err))
		return nil, err
	}

	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get meta client fail", logit.Error("error", err))
		return nil, err
	}

	clusterInfo, err := metaCli.GetCluster(ctx, app.AppShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster info fail", logit.Error("error", err))
		return nil, err
	}
	return clusterInfo, nil
}

func generateDownloadTaskItems(ctx context.Context, allSlotDownloadInfo []*ns.SingleSlotDownloadInfo,
	slotInfo metaserver.Slots, offset int, maxSlot int, maxSize int64) []*NodeDownloadSstTaskItem {

	taskItems := make([]*NodeDownloadSstTaskItem, 0)

	for _, slotRange := range slotInfo {
		for i := slotRange[0]; i <= slotRange[1]; i++ {
			if i <= offset {
				continue
			}
			if len(taskItems) == 0 ||
				cast.ToInt64(taskItems[len(taskItems)-1].FileTotalSize) > maxSize ||
				taskItems[len(taskItems)-1].SlotNum >= maxSlot {
				taskItems = append(taskItems, &NodeDownloadSstTaskItem{
					SlotIDs: make([]int, 0),
					Files:   make([]*ns.FileDownloadInfo, 0),
				})
			}
			// 忽略没有文件的slot
			if len(allSlotDownloadInfo[i].Files) == 0 {
				continue
			}
			taskItems[len(taskItems)-1].SlotIDs = append(taskItems[len(taskItems)-1].SlotIDs, i)
			taskItems[len(taskItems)-1].SlotNum += 1
			taskItems[len(taskItems)-1].FileTotalSize += allSlotDownloadInfo[i].FileTotalSize
			taskItems[len(taskItems)-1].Files = append(taskItems[len(taskItems)-1].Files,
				allSlotDownloadInfo[i].Files...)
		}
	}

	return taskItems
}

func generateBatchDownloadTask(ctx context.Context, app *x1model.Application,
	progress DownloadProgress, allSlotDownloadInfo []*ns.SingleSlotDownloadInfo,
	maxSlot int, maxSize int64) ([]*NodeDownloadSstTask, error) {

	clusterInfo, err := getClusterSlotInfo(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster info fail", logit.Error("error", err))
		return nil, err
	}

	nodeDownloadSstTasks := make([]*NodeDownloadSstTask, 0)
	var offset int
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			if v, found := progress[node.NodeId]; found {
				offset = v.SlotOffset
			} else {
				offset = -1
			}
			taskItems := generateDownloadTaskItems(ctx, allSlotDownloadInfo, clusterInfo.Slot[cluster.ClusterShortID],
				offset, maxSlot, maxSize)
			nodeDownloadSstTasks = append(nodeDownloadSstTasks, &NodeDownloadSstTask{
				NodeID:         node.NodeId,
				NodeFloatingIP: node.FloatingIP,
				NodeXagentPort: node.XagentPort,
				Items:          taskItems,
				ItemNum:        len(taskItems),
			})
		}
	}
	return nodeDownloadSstTasks, nil
}

func checkAllInUseNodeAlive(ctx context.Context, app *x1model.Application) error {

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			if err := util.PingTest(ctx, node.FloatingIP, node.Port, pingTimeoutSec, nil); err != nil && !strings.Contains(err.Error(), "NOAUTH") {
				resource.LoggerTask.Warning(ctx, "ping redis failed",
					logit.String("nodeId", node.NodeId),
					logit.String("floatingIp", node.FloatingIP),
					logit.Int("port", node.Port),
					logit.Error("err", err),
				)
				return err
			}
		}
	}
	return nil
}

// RemoveEmptyDownloadDir 删除注入成功后的空文件夹,避免坑内空文件夹残留
func RemoveEmptyDownloadDir(ctx context.Context, node *x1model.Node, namespace string) error {

	xagentParams := sstOperationParam{
		Action:      "remove_empty_dir",
		DownloadDir: ns.GetDownloadDir(ctx, namespace),
	}
	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}
	Req := xagent.AsyncRequest{
		Addr:       &xAgentAddr,
		Action:     "sst_operation",
		Params:     xagentParams,
		TimeoutSec: 50,
	}
	t := xagent.Instance().DoAsync(ctx, &Req)
	_, err := t.Wait()
	if err != nil {
		return err
	}
	return nil
}

// CreateEmptyDownloadDir 所有节点下载前先创建空文件夹
func CreateEmptyDownloadDir(ctx context.Context, node *x1model.Node, namespace string) error {

	xagentParams := sstOperationParam{
		Action:      "create_empty_dir",
		DownloadDir: ns.GetDownloadDir(ctx, namespace),
	}
	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}
	Req := xagent.AsyncRequest{
		Addr:       &xAgentAddr,
		Action:     "sst_operation",
		Params:     xagentParams,
		TimeoutSec: 50,
	}
	t := xagent.Instance().DoAsync(ctx, &Req)
	_, err := t.Wait()
	if err != nil {
		return err
	}
	return nil
}
