/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_cluster.go */
/*
modification history
--------------------
2022/08/19 , by <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com) , create
*/
/*
DESCRIPTION
todo
*/

package precheck

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/syncgroup"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessPrecheckForCreateGlobalGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetGlobalParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if len(param.Metaserver) == 0 {
		resource.LoggerTask.Error(ctx, "metaserver is empty")
		return errors.Errorf("metaserver is empty")
	}
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "groupid is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}
	if len(app.AppGroupID) != 0 {
		resource.LoggerTask.Warning(ctx, "app is already in group")
		return errors.Errorf("app is already in group")
	}

	shards, err := gmaster.GlobalMasterOp().GetShards(ctx, &gmaster.GetShardsParams{
		AppGroupID: param.GroupId,
		AppID:      app.AppId,
		UserID:     app.UserId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shards info fail", logit.Error("err", err))
		return err
	}
	if len(shards) != len(app.Clusters) {
		resource.LoggerTask.Warning(ctx, "global shard num not eq to app cluster")
		return errors.Errorf("global shard num not eq to app cluster")
	}

	return nil
}

func ProcessPrecheckForJoinGlobalGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetGlobalParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if len(param.Metaserver) == 0 {
		resource.LoggerTask.Error(ctx, "metaserver is empty")
		return errors.Errorf("metaserver is empty")
	}
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "groupid is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}
	if len(app.AppGroupID) != 0 {
		resource.LoggerTask.Warning(ctx, "app is already in group")
		return errors.Errorf("app is already in group")
	}
	shards, err := gmaster.GlobalMasterOp().GetShards(ctx, &gmaster.GetShardsParams{
		AppGroupID: param.GroupId,
		AppID:      app.AppId,
		UserID:     app.UserId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shards info fail", logit.Error("err", err))
		return err
	}
	if len(shards) != len(app.Clusters) {
		resource.LoggerTask.Warning(ctx, "global shard num not eq to app cluster")
		return errors.Errorf("global shard num not eq to app cluster")
	}

	return nil
}

func ProcessPrecheckForCreateSyncGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "sync group id is empty")
		return errors.Errorf("group id is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.Clusters[0].Engine == x1model.EnginePegaDB {
		if app.UseNewAgent != "yes" {
			return errors.Errorf("pega sync group only support newagent")
		}
	}

	if len(app.SyncGroupID) != 0 {
		resource.LoggerTask.Warning(ctx, "app is already in sync group")
		return errors.Errorf("app is already in sync group")
	}

	app.SyncGroupID = param.GroupId

	if syncgroup.IsShouldUseBdrpDataserver(ctx, app) {
		if app.DataserverType == "" {
			app.DataserverType = "bdrp"
		}
	} else {
		resource.LoggerTask.Trace(ctx, "is in black list, skip mark use bdrp dataserver")
	}


	if isShouldUseNewSyncAgent(ctx, app) {
		if app.SyncAgentBigver == "" {
			app.SyncAgentBigver = "2.0"
		}
	} else {
		resource.LoggerTask.Trace(ctx, "is in black list, skip mark use new syncagent")
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}

	return nil
}

// isShouldUseNewSyncAgent
// 是否命中new sync-agent 小流量
func isShouldUseNewSyncAgent(ctx context.Context, app *x1model.Application) bool {
	if privatecloud.IsPrivateENV() {
		return false
	}
	// 目前new syncagent不支持pegadb
	//if app.Clusters[0].Engine == x1model.EnginePegaDB {
	//	return false
	//}

	flagVal, err := resource.CsmasterOpAgent.GetFlag(ctx, "use_new_syncagent",
		map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get use_new_syncagent flag fail", logit.String("userid", app.UserId),
			logit.String("vpcID", app.VpcId), logit.Error("err", err))
		return false
	}
	resource.LoggerTask.Trace(ctx, "get use_new_syncagent flag success", logit.String("userid", app.UserId),
		logit.String("vpcID", app.VpcId), logit.String("val", flagVal))
	return flagVal == "yes"
}
