/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* global_cluster.go */
/*
modification history
--------------------
2024/07/08 , by <PERSON><PERSON><PERSON> (wang<PERSON><PERSON>@baidu.com) , create
*/
/*
DESCRIPTION
*/
package precheck

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// 检查是否为 newagent
func ProcessPrecheckForModifyType(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("Precheck for modifyType fail : teu is nilptr")
	}

	// app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	// check newagent
	if app.UseNewAgent == "" {
		resource.LoggerTask.Warning(ctx, "app is not newagent", logit.String("appId", teu.Entity))
		err = errors.New("app is not newagent")
		return cerrs.ErrorTaskManual.Wrap(err)
	}

	return nil
}
