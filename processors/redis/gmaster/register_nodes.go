/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* register_nodes.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (<PERSON><PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package gmaster

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessRegisterNodesToGlobalMasterAsLeader(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	return callGmRegisterNodes(ctx, teu.Entity, true)
}

func ProcessRegisterNodesToGlobalMasterAsSlave(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	return callGmRegisterNodes(ctx, teu.Entity, false)
}

func callGmRegisterNodes(ctx context.Context, appId string, useRealRole bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	nodesList := make([]*global_model.AppGroupRedis, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			role := node.Role
			// 不适用真实角色，就默认全是从节点
			if !useRealRole {
				role = x1model.RoleTypeSlave
			}
			nodesList = append(nodesList, &global_model.AppGroupRedis{
				RedisId:    node.NodeId,
				AppId:      node.AppId,
				GroupId:    app.AppGroupID,
				Region:     app.Region,
				ShardId:    cluster.GlobalID,
				Status:     node.Status,
				Role:       role,
				Ip:         node.Ip,
				FloatingIp: node.FloatingIP,
				Port:       node.Port,
				CreatedAt:  time.Now(),
			})
		}
	}

	proxyList := make([]*global_model.AppGroupProxy, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxyList = append(proxyList, &global_model.AppGroupProxy{
				ProxyId:    proxy.ProxyId,
				AppId:      proxy.AppId,
				GroupId:    app.AppGroupID,
				Region:     app.Region,
				Status:     proxy.Status,
				Ip:         proxy.Ip,
				FloatingIp: proxy.FloatingIP,
				Port:       proxy.Port,
				CreatedAt:  time.Now(),
			})
		}
	}

	err = gmaster.GlobalMasterOp().AddNodes(ctx, &gmaster.AddNodesParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
		Nodes:      nodesList,
	})

	if err != nil {
		resource.LoggerTask.Warning(ctx, "add nodes fail", logit.Error("err", err))
		return err
	}

	err = gmaster.GlobalMasterOp().AddProxies(ctx, &gmaster.AddProxiesParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
		Proxies:    proxyList,
	})

	if err != nil {
		resource.LoggerTask.Warning(ctx, "add proxy fail", logit.Error("err", err))
		return err
	}

	// 把global seq id写到本地数据库
	getProxyReq := gmaster.GetProxiesParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
	}
	gProxies, err := gmaster.GlobalMasterOp().GetProxies(ctx, &getProxyReq)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy fail", logit.Error("err", err))
		return err
	}

	mapProxyId2GlobalSeqId := make(map[string]int64, 0)
	for _, gp := range gProxies {
		mapProxyId2GlobalSeqId[gp.ProxyId] = gp.Id
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy.GlobalSeqID = cast.ToInt(mapProxyId2GlobalSeqId[proxy.ProxyId])
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}

	return nil
}
