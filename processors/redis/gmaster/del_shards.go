package gmaster

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
)

func ProcessInitDelShardsModifyStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("error", err))
		return err
	}

	// 将global master状态改为迁移中
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(
		ctx,
		app.AppGroupID,
		[]string{global_model.StatusDelShardsMigration, global_model.ShardStatusRunning},
		global_model.StatusDelShardsMigration,
		app.UserId,
	); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}

	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list cache group detail fail", logit.Error("error", err))
		return err
	}
	if app.AppId != groupDetail.Leader.ClusterShowId {
		resource.LoggerTask.Notice(ctx, "app is not leader, skip")
		if !isLeaderHasRegisterRightTask(ctx, app, global_model.ModifyStageMigration, global_model.ModifyTypeDelShards, params.NodeType, params.ShardCount) {
			return fmt.Errorf("leader not register right task,err:%w", err)
		}
		return nil
	}
	return addModifyStatus(ctx, app, global_model.ModifyStageMigration, global_model.ModifyTypeDelShards, params.NodeType, params.ShardCount)
}

func ProcessSetDelShardMigrationComplete(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	// 从地域不设置
	if len(app.AppGroupID) != 0 && !metaserver.IsGlobalLeader(ctx, app) {
		resource.LoggerTask.Trace(ctx, "follower region ,skip")
		return nil
	}

	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get group detail fail", logit.Error("error", err))
		return err
	}
	// 非主直接跳过
	if groupDetail.Leader.ClusterShowId != app.AppId {
		resource.LoggerTask.Warning(ctx, "follower should not in this process,pls check")
		return nil
	}

	members := append(groupDetail.Followers, groupDetail.Leader)

	for _, m := range members {
		fakeX1model := &x1model.Application{
			AppId:      m.ClusterShowId,
			UserId:     app.UserId,
			AppGroupID: app.AppGroupID,
		}
		if err := setMemberProgress(ctx, fakeX1model, global_model.ModifyStageMigration, "100%"); err != nil {
			resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessCheckDelShardMigrationComplete(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}

	if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageMigration, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members progress timeout", logit.Error("error", err))
		return err
	}
	if err := setStage(ctx, app, global_model.ModifyStageReleaseResource); err != nil {
		resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCheckDelShardReleaseResourceComplete(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}

	if err := setMemberProgress(ctx, app, global_model.ModifyStageReleaseResource, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
		return err
	}
	if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageReleaseResource, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members progress timeout", logit.Error("error", err))
		return err
	}
	if err := setStage(ctx, app, global_model.ModifyStageComplete); err != nil {
		resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
		return err
	}
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(ctx, app.AppGroupID, []string{global_model.StatusDelShardsMigration, global_model.ShardStatusRunning},
		global_model.ShardStatusRunning, app.UserId); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}
	return nil
}
