/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* use_rsid_psync.go */
/*
modification history
--------------------
2024/03/24 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo 需要内核给出详细文档，这个配置应该是需要重启生效
*/

package gmaster

import (
	"context"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// TurnOnPegaUseRsidPsyncAndBuildMetaForRestart 打开use-rsid-psync，并把节点状态改为torestart
// 原理上，这个配置应该配置成pega的默认参数打开。但是在当前时间，newagent正在刷存量，此时设置默认参数可能引入意外故障
// 因此只在创建/加入热活时打开，等newagent刷完且稳定后，直接修改newagent的pega默认值
func TurnOnPegaUseRsidPsyncAndBuildMetaForRestart(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}
	isNeedConfigset, err := util.NeedConfigSetForJoinGroup(ctx, app)
	if err != nil {
		return err
	}
	if !isNeedConfigset {
		return nil
	}

	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "use-rsid-psync",
			ConfModule: csmaster_model_interface.CONF_MODULE_REDIS,
			ConfValue:  "yes",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "open use-rsid-psync success")

	for _, c := range app.Clusters {
		for _, n := range c.Nodes {
			if n.Status != x1model.NodeOrProxyStatusInUse {
				resource.LoggerTask.Trace(ctx, "node status is not inuse,skip",
					logit.String("nodeid", n.NodeId), logit.String("status", n.Status))
				continue
			}
			n.Status = x1model.NodeOrProxyStatusToRestart
		}
	}
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "change node status to restart success")
	return nil
}

// 与内核同学确认后，这个配置不需要关闭
// func TurnOffUserRsidPsync(ctx context.Context, teu *workflow.TaskExecUnit) error {
//	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
//	if err != nil {
//		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
//		return err
//	}
//	if app.Clusters[0].Engine != x1model.EnginePegaDB {
//		resource.LoggerTask.Trace(ctx, "not pega,skip")
//		return nil
//	}
//	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
//		ConfItem: &csmaster.ConfItem{
//			ConfName:   "use-rsid-psync",
//			ConfModule: csmaster_model_interface.CONF_MODULE_REDIS,
//			ConfValue:  "no",
//		},
//		UserID: app.UserId,
//		AppID:  app.AppId,
//		From:   "admin",
//	}); err != nil {
//		resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
//		return err
//	}
//	resource.LoggerTask.Trace(ctx, "close use-rsid-psync success")
//	return nil
// }
