/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_slave_of.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package gmaster

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessGlobalSlaveOf 调用global-master-api获取主redis信息，将slave挂载到对应主，并修改数据库中元数据信息
func ProcessGlobalSlaveOf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if len(app.AppGroupID) == 0 {
		return nil
	}

	globalNodes, err := gmaster.GlobalMasterOp().GetNodes(ctx, &gmaster.GetNodesParams{
		AppGroupID: app.AppGroupID,
		UserID:     app.UserId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get global nodes fail", logit.Error("err", err))
		return err
	}

	shardToNodes := make(map[string][]*global_model.AppGroupRedis, 0)

	for _, node := range globalNodes {
		if node.AppId != app.AppId && node.Role != x1model.RoleTypeMaster {
			continue
		}
		if _, ok := shardToNodes[node.ShardId]; ok {
			shardToNodes[node.ShardId] = append(shardToNodes[node.ShardId], node)
		} else {
			shardToNodes[node.ShardId] = []*global_model.AppGroupRedis{node}
		}
	}
	// realtodo 好像不用改了，忘了
	err = bindGlobalMasterNode(ctx, app.Clusters, shardToNodes, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "bind global master node fail", logit.Error("err", err))
		return err
	}

	return nil
}

func bindGlobalMasterNode(ctx context.Context, clusters []*x1model.Cluster, shardToNodes map[string][]*global_model.AppGroupRedis,
	userId string) error {
	g := gtask.Group{
		Concurrent: 20,
	}
	for _, cluster := range clusters {
		cluster := cluster
		globalNodes, ok := shardToNodes[cluster.GlobalID]
		if !ok {
			resource.LoggerTask.Warning(ctx, "no global nodes")
			return errors.Errorf("no global nodes")
		}
		var globalMasterNode *global_model.AppGroupRedis
		for _, gNode := range globalNodes {
			if gNode.Role == x1model.RoleTypeMaster {
				globalMasterNode = gNode
				break
			}
		}
		if globalMasterNode == nil {
			resource.LoggerTask.Warning(ctx, "no master node")
			return errors.Errorf("no master node")
		}

		g.Go(func() error {
			return gtask.NoPanic(func() error {
				if err := CallCsmasterSlaveofMaster(ctx, globalMasterNode.Ip, globalMasterNode.Port, cluster, userId); err != nil {
					resource.LoggerTask.Warning(ctx, "call csmaster slaveof master failed", logit.Error("error", err))
					return err
				}
				return nil
			})
		})
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parallel execution error", logit.Error("error", err))
		return err
	}
	return nil
}

func CallCsmasterSlaveofMaster(ctx context.Context, masterIp string, masterPort int, cluster *x1model.Cluster, userId string) error {
	req := csmaster.SetSlaveOfParam{
		UserID:        userId,
		ShardGlobalID: cluster.GlobalID,
		MasterIp:      masterIp,
		MasterPort:    masterPort,
		AppID:         cluster.AppId,
	}
	resource.LoggerTask.Warning(ctx, "[DEBUGLOG]call csmaster slave of req", logit.String("req", base_utils.Format(req)))

	err := csmaster.CsmasterOp().SlaveOfMaster(ctx, &req)
	return err
}
