package gmaster

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

type ApplyResourceProgress struct {
	AppID    string
	Progress string
}

var nextStageMap = map[string]string{
	global_model.ModifyStageApplyResource:   global_model.ModifyStageMigration,
	global_model.ModifyStageMigration:       global_model.ModifyStageComplete,
	global_model.ModifyStageRollback:        global_model.ModifyStageComplete,
	global_model.ModifyStageReleaseResource: global_model.ModifyStageComplete,
}

var shrinkStageMap = map[string]string{
	global_model.ModifyStageMigration: global_model.ModifyStageReleaseResource,
}

func checkAllMembersProgress(ctx context.Context, app *x1model.Application, expectedStage, targetProgress string) error {
	var ferr error
	if err := base_utils.LoopForErr(ctx, func() error {
		mStatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, false, app.UserId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get modify status fail", logit.Error("error", err))
			return err
		}
		if len(mStatus) == 0 {
			// 已经完成了，就没必要检查了
			if targetProgress == "100%" && nextStageMap[expectedStage] == global_model.ModifyStageComplete {
				recheckMstatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, true, app.UserId)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "recheck modify status fail", logit.Error("error", err))
					return err
				}
				if len(recheckMstatus) > 0 && recheckMstatus[0].Stage == global_model.ModifyStageComplete {
					return nil
				}
			}
			resource.LoggerTask.Warning(ctx, "modify status is empty")
			return errors.New("modify status is empty")
		}
		if targetProgress == "100%" && mStatus[0].Stage == nextStageMap[expectedStage] && len(mStatus[0].Stage) != 0 {
			return nil
		}
		if (mStatus[0].Type == global_model.ModifyTypeDelShards || mStatus[0].Type == global_model.ModifyTypeModifySpec) &&
			targetProgress == "100%" &&
			mStatus[0].Stage == shrinkStageMap[expectedStage] && len(mStatus[0].Stage) != 0 {
			return nil
		}

		if mStatus[0].Stage != expectedStage {
			ferr = fmt.Errorf("stage is not %s", expectedStage)
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("stage is not %s", expectedStage), logit.Error("error", ferr))
			return nil
		}
		var progress []*ApplyResourceProgress
		if len(mStatus[0].Progress) != 0 {
			if err := json.Unmarshal([]byte(mStatus[0].Progress), &progress); err != nil {
				resource.LoggerTask.Warning(ctx, "unmarshal progress fail", logit.Error("error", err))
				ferr = err
				return nil
			}
		}
		pMap := make(map[string]string)
		for _, p := range progress {
			pMap[p.AppID] = p.Progress
		}

		groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get group detail fail", logit.Error("error", err))
			return err
		}
		members := append(groupDetail.Followers, groupDetail.Leader)
		for _, member := range members {
			p, ok := pMap[member.ClusterShowId]
			if !ok || p != targetProgress {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("member %s progress not reach target", member.ClusterShowId))
				return fmt.Errorf("member %s progress not reach target", member.ClusterShowId)
			}
		}
		return nil
	}, time.Second*1); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	if ferr != nil {
		return ferr
	}
	return nil
}

func setMemberProgress(ctx context.Context, app *x1model.Application, expectedStage, targetProgress string) error {
	var ferr error
	if err := base_utils.LoopForErr(ctx, func() error {
		mStatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, false, app.UserId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get modify status fail", logit.Error("error", err))
			return err
		}
		if len(mStatus) == 0 {
			// 已经完成了，就没必要设置了
			if targetProgress == "100%" && nextStageMap[expectedStage] == global_model.ModifyStageComplete {
				recheckMstatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, true, app.UserId)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "recheck modify status fail", logit.Error("error", err))
					return err
				}
				if len(recheckMstatus) > 0 && recheckMstatus[0].Stage == global_model.ModifyStageComplete {
					return nil
				}
			}
			resource.LoggerTask.Warning(ctx, "modify status is empty")
			return errors.New("modify status is empty")
		}
		if targetProgress == "100%" && mStatus[0].Stage == nextStageMap[expectedStage] && len(mStatus[0].Stage) != 0 {
			return nil
		}
		if (mStatus[0].Type == global_model.ModifyTypeDelShards || mStatus[0].Type == global_model.ModifyTypeModifySpec) &&
			targetProgress == "100%" &&
			mStatus[0].Stage == shrinkStageMap[expectedStage] && len(mStatus[0].Stage) != 0 {
			return nil
		}
		if mStatus[0].Stage != expectedStage {
			ferr = fmt.Errorf("stage is not %s", expectedStage)
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("stage is not %s", expectedStage), logit.Error("error", ferr))
			return nil
		}
		var progress []*ApplyResourceProgress
		if len(mStatus[0].Progress) != 0 {
			if err := json.Unmarshal([]byte(mStatus[0].Progress), &progress); err != nil {
				resource.LoggerTask.Warning(ctx, "unmarshal progress fail", logit.Error("error", err))
				ferr = err
				return nil
			}
		}
		var myProgress *ApplyResourceProgress
		for _, p := range progress {
			if p.AppID == app.AppId {
				myProgress = p
				break
			}
		}
		if myProgress == nil {
			myProgress = &ApplyResourceProgress{AppID: app.AppId, Progress: targetProgress}
			progress = append(progress, myProgress)
		} else {
			myProgress.Progress = targetProgress
		}
		if err := gmaster.GlobalMasterOp().UpdateModifyStatusStage(ctx, &gmaster.UpdateModifyStatusStageParams{
			AppGroupID: app.AppGroupID,
			UserID:     app.UserId,
			ID:         mStatus[0].ID,
			Stage:      mStatus[0].Stage,
			Progress:   base_utils.Format(progress),
			Version:    mStatus[0].Version,
		}); err != nil {
			resource.LoggerTask.Notice(ctx, "update modify status stage fail", logit.Error("error", err))
			return err
		}
		return nil
	}, time.Second*1); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	return nil
}

func setStage(ctx context.Context, app *x1model.Application, targetStage string) error {
	if err := base_utils.LoopForErr(ctx, func() error {
		mStatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, false, app.UserId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get modify status fail", logit.Error("error", err))
			return err
		}
		// 已完成的不用管
		if len(mStatus) == 0 {
			if targetStage == global_model.ModifyStageComplete {
				recheckMstatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, true, app.UserId)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "recheck modify status fail", logit.Error("error", err))
					return err
				}
				if len(recheckMstatus) > 0 && recheckMstatus[0].Stage == global_model.ModifyStageComplete {
					mStatus = recheckMstatus
				} else {
					resource.LoggerTask.Warning(ctx, "modify status is empty")
					return errors.New("modify status is empty")
				}
			} else {
				resource.LoggerTask.Warning(ctx, "modify status is empty")
				return errors.New("modify status is empty")
			}
		}
		// if mStatus[0].Stage != targetStage {
		if err := gmaster.GlobalMasterOp().UpdateModifyStatusStage(ctx, &gmaster.UpdateModifyStatusStageParams{
			AppID:      app.AppId,
			AppGroupID: app.AppGroupID,
			UserID:     app.UserId,
			ID:         mStatus[0].ID,
			Stage:      targetStage,
			Version:    mStatus[0].Version,
		}); err != nil {
			resource.LoggerTask.Notice(ctx, "update modify status stage fail", logit.Error("error", err))
			return err
		}
		// }
		return nil
	}, time.Second*5); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	return nil
}

func addModifyStatus(ctx context.Context, app *x1model.Application, stage string, t string, targetNodeType string, targetShardCount int) error {
	if err := base_utils.LoopForErr(ctx, func() error {
		mStatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, false, app.UserId)
		if err != nil {
			resource.LoggerTask.Notice(ctx, "get modify status fail", logit.Error("error", err))
			return err
		}
		if len(mStatus) == 0 {
			if err := gmaster.GlobalMasterOp().AddModifyStatus(ctx, &gmaster.AddModifyStatusParams{
				AppGroupID:       app.AppGroupID,
				Type:             t,
				Stage:            stage,
				Progress:         base_utils.Format([]*ApplyResourceProgress{{AppID: app.AppId, Progress: "0%"}}),
				TargetNodeType:   targetNodeType,
				TargetShardCount: targetShardCount,
			}, app.UserId); err != nil {
				resource.LoggerTask.Notice(ctx, "add modify status fail", logit.Error("error", err))
				return err
			}
		} else {
			if mStatus[0].TargetShardCount != targetShardCount ||
				mStatus[0].TargetNodeType != targetNodeType {
				return errors.New("not same with processing global task")
			}
		}

		return nil
	}, time.Second*5); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessInitGlobalAddShardsStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("error", err))
		return err
	}

	// 将global master状态改为迁移中
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(
		ctx,
		app.AppGroupID,
		[]string{global_model.StatusAddShardsMigration, global_model.ShardStatusRunning},
		global_model.StatusAddShardsMigration,
		app.UserId,
	); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}

	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list cache group detail fail", logit.Error("error", err))
		return err
	}
	if app.AppId != groupDetail.Leader.ClusterShowId {
		resource.LoggerTask.Notice(ctx, "app is not leader, skip")
		if !isLeaderHasRegisterRightTask(ctx, app, global_model.ModifyStageApplyResource, global_model.ModifyTypeAddShards, params.NodeType, params.ShardCount) {
			return fmt.Errorf("leader not register right task,err:%w", err)
		}
		return nil
	}
	return addModifyStatus(ctx, app, global_model.ModifyStageApplyResource, global_model.ModifyTypeAddShards, params.NodeType, params.ShardCount)
}

func ProcessCheckModifyStatusAfterApplyResource(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	if err := setMemberProgress(ctx, app, global_model.ModifyStageApplyResource, "50%"); err != nil {
		resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
		return err
	}
	// timer的用法
	// 参考golang源码：https://github.com/golang/go/blob/78755f6b8c5f18b0014e9dcac383898047ff14fe/src/net/http/server.go#L3024
	retryTimer := time.NewTimer(5 * time.Second)
	// 这个应该是垃圾回收的考虑
	defer retryTimer.Stop()
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-retryTimer.C:
			err := checkAllMembersProgress(ctx, app, global_model.ModifyStageApplyResource, "50%")
			if err == nil {
				return nil
			}
			resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
			retryTimer.Reset(5 * time.Second)
		}
	}
}

func ProcessCheckModifyStatusBeforeMigration(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	if err := setMemberProgress(ctx, app, global_model.ModifyStageApplyResource, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
		return err
	}
	if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageApplyResource, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	return setStage(ctx, app, global_model.ModifyStageMigration)
}

func ProcessSetRollbackStage(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	if err := setStage(ctx, app, global_model.ModifyStageRollback); err != nil {
		resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
		return err
	}
	if err := setMemberProgress(ctx, app, global_model.ModifyStageRollback, "10%"); err != nil {
		resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
		return err
	}
	if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageRollback, "10%"); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCompleteRollback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	if err := setMemberProgress(ctx, app, global_model.ModifyStageRollback, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
		return err
	}
	if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageRollback, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
		return err
	}
	if err := setStage(ctx, app, global_model.ModifyStageComplete); err != nil {
		resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
		return err
	}
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(ctx, app.AppGroupID, []string{global_model.StatusAddShardsMigration, global_model.ShardStatusRunning},
		global_model.StatusRuning, app.UserId); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessCompleteAddShardsMigration 因为扩容只有主角色进行，所以完成的时候需要整体改成success
func ProcessCompleteAddShardsMigration(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get group detail fail", logit.Error("error", err))
		return err
	}
	// 非主直接跳过
	if groupDetail.Leader.ClusterShowId != app.AppId {
		resource.LoggerTask.Warning(ctx, "follower should not in this process,pls check")
		return nil
	}

	members := append(groupDetail.Followers, groupDetail.Leader)

	for _, m := range members {
		fakeX1model := &x1model.Application{
			AppId:      m.ClusterShowId,
			UserId:     app.UserId,
			AppGroupID: app.AppGroupID,
		}
		if err := setMemberProgress(ctx, fakeX1model, global_model.ModifyStageMigration, "100%"); err != nil {
			resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
			return err
		}
	}

	for _, m := range members {
		fakeX1model := &x1model.Application{
			AppId:      m.ClusterShowId,
			UserId:     app.UserId,
			AppGroupID: app.AppGroupID,
		}
		if err := setStage(ctx, fakeX1model, global_model.ModifyStageComplete); err != nil {
			resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
			return err
		}
	}

	// if err := setMemberProgress(ctx, app, global_model.ModifyStageMigration, "100%"); err != nil {
	//	resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
	//	return err
	// }
	// if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageMigration, "100%"); err != nil {
	//	resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
	//	return err
	// }
	// if err := setStage(ctx, app, global_model.ModifyStageComplete); err != nil {
	//	resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
	//	return err
	// }
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(ctx, app.AppGroupID, []string{global_model.StatusAddShardsMigration, global_model.ShardStatusRunning},
		global_model.StatusRuning, app.UserId); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}
	return nil
}

func isLeaderHasRegisterRightTask(ctx context.Context, app *x1model.Application, stage string, t string, targetNodeType string, targetShardCount int) bool {
	mStatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, false, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get modify status fail", logit.Error("error", err))
		return false
	}
	if len(mStatus) == 0 {
		resource.LoggerTask.Trace(ctx, "no mstatus")
		return false
	}
	if len(mStatus) > 1 {
		resource.LoggerTask.Warning(ctx, "more than 1 mstatus ,pls check")
		return false
	}
	if mStatus[0].Type == t &&
		mStatus[0].TargetShardCount == targetShardCount &&
		mStatus[0].TargetNodeType == targetNodeType {
		return true
	}
	resource.LoggerTask.Warning(ctx, "not right task")
	return false
}
