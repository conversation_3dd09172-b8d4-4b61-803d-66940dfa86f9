/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/01/10
 * File: set_pega_configs.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package gmaster TODO package function desc
package gmaster

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessResetPegaHotConfigs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if err := pegaResetConf(ctx, app); err != nil {
		//if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
		//	resource.LoggerTask.Warning(ctx, "update task to manual failed", logit.Error("error", err2))
		//	return err2
		//}
		return cerrs.ErrorTaskManual.Errorf("pega reset conf fail,err:%s", err.Error())
	}

	return nil
}

// 检查所有真实版本都>=2.2.3.2，内核才支持set
func checkIsPegaVersionSupportConfSet(ctx context.Context, app *x1model.Application) error {
	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node := node
			g.Go(func() error {
				realV, err := x1model.GetRealVersion(ctx, node.NodeId, []string{x1model.PegaDBPkg}, "")
				if err != nil {
					return err
				}
				for _, pkg := range realV {
					if pkg.Name == x1model.PegaDBPkg {
						// bjtest的测试包放行
						if env.IDC() == "bjtest" && base_utils.IsDevVer(pkg.FullVersion) {
							return nil
						}
						// 内核需要>=2.2.3.2
						if base_utils.IsVersionLt(pkg.FullVersion, "2.2.3.2") {
							return fmt.Errorf("version is lt 2.2.3.2,pkgversion:%s", pkg.FullVersion)
						}
						return nil
					}
				}
				return errors.New("not found pega pkg")
			})
		}
	}
	_, err := g.Wait()
	if err != nil {
		return err
	}
	return nil
}

func pegaResetConf(ctx context.Context, app *x1model.Application) error {
	if len(app.Clusters) == 0 {
		return nil
	}
	if app.Clusters[0].Engine != x1model.EnginePegaDB {
		return nil
	}

	metaserver := app.LocalMetaserver
	clusterID := app.AppShortID
	if app.AppGroupID != "" {
		metaserver = app.GlobalMetaserver
		clusterID = app.AppGroupSeqID
	}

	if clusterID == 0 {
		resource.LoggerTask.Warning(ctx, "clusterid is 0")
		return errors.New("clusterid is 0")
	}

	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, metaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaEntrance := strings.Split(metaCluster.Entrance, ":")
	if len(metaEntrance) != 2 {
		return fmt.Errorf("meta entrance not valid:%s", metaCluster.Entrance)
	}

	if err := checkIsPegaVersionSupportConfSet(ctx, app); err != nil {
		if app.AppGroupID == "" {
			resource.LoggerTask.Notice(ctx, "pega not support set config,but not hot group, skip", logit.Error("err", err))
			return nil
		}
		return fmt.Errorf("check version false,err:%w", err)
	}

	resource.LoggerTask.Trace(ctx, "pega start to reset config",
		logit.Int("clusterid", clusterID), logit.String("entrance", metaCluster.Entrance))
	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "cluster-id",
			ConfModule: 1,
			ConfValue:  cast.ToString(clusterID),
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
			logit.String("request", base_utils.Format(modifyReq)))
		return err
	}
	modifyReq = csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "metaserverhost",
			ConfModule: 1,
			ConfValue:  metaEntrance[0],
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
			logit.String("request", base_utils.Format(modifyReq)))
		return err
	}
	modifyReq = csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "metaserverport",
			ConfModule: 1,
			ConfValue:  metaEntrance[1],
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
			logit.String("request", base_utils.Format(modifyReq)))
		return err
	}
	return nil
}
