/*
 * Copyright(C) 2024 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2024/01/29
 * File: ha.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package gmaster TODO package function desc
package gmaster

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func TryToGetGlobalSelfHealLock(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	// 将global master状态改为迁移中
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(
		ctx,
		app.AppGroupID,
		[]string{global_model.ShardStatusRunning},
		global_model.StatusInnerSelfHeal,
		app.UserId,
	); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}
	return nil
}

func TryToReleaseGlobalSelfHealLock(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	// 将global master状态改为迁移中
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(
		ctx,
		app.AppGroupID,
		[]string{global_model.StatusInnerSelfHeal},
		global_model.ShardStatusRunning,
		app.UserId,
	); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}
	return nil
}
