/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/14 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file register_standalone_nodes.go
 * <AUTHOR>
 * @date 2022/07/14 13:38:41
 * @brief register standalone nodes
 *
 **/

package gmaster

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessRegisterStandaloneNodesToGlobalMasterAsLeader register nodes
func ProcessRegisterStandaloneNodesToGlobalMasterAsLeader(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	return callGmRegisterStandaloneNodes(ctx, teu.Entity, true)
}

// ProcessRegisterStandaloneNodesToGlobalMasterAsSlave register nodes as slave
func ProcessRegisterStandaloneNodesToGlobalMasterAsSlave(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	return callGmRegisterStandaloneNodes(ctx, teu.Entity, false)
}

// callGmRegisterStandaloneNodes func to register nodes
func callGmRegisterStandaloneNodes(ctx context.Context, appID string, useRealRole bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	nodesList := make([]*global_model.AppGroupRedis, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Engine == x1model.EngineRedis {
				role := node.Role
				// 不适用真实角色，就默认全是从节点
				if !useRealRole {
					role = x1model.RoleTypeSlave
				}
				nodesList = append(nodesList, &global_model.AppGroupRedis{
					RedisId:    node.NodeId,
					AppId:      node.AppId,
					GroupId:    app.AppGroupID,
					Region:     app.Region,
					ShardId:    cluster.GlobalID,
					Status:     node.Status,
					Role:       role,
					Ip:         node.Ip,
					FloatingIp: node.FloatingIP,
					Port:       node.Port,
					CreatedAt:  time.Now(),
				})
			}
		}
	}

	err = gmaster.GlobalMasterOp().AddNodes(ctx, &gmaster.AddNodesParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
		Nodes:      nodesList,
	})

	if err != nil {
		resource.LoggerTask.Warning(ctx, "add nodes fail", logit.Error("err", err))
		return err
	}

	return nil
}
