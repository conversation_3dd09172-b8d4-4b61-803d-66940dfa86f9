/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* rebuild_sg.go */
/*
modification history
--------------------
2022/05/31 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package gmaster

// func ProcessRebuildGlobalSecurityGroupCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
//	if teu == nil {
//		resource.LoggerTask.Warning(ctx, "teu is nilptr")
//		return errors.Errorf("teu is nilptr")
//	}
//
//	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
//	if err != nil {
//		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
//		return err
//	}
//
//	var toAddIpList []string
//	var toAddPortList []int32
//	for _, cluster := range app.Clusters {
//		for _, node := range cluster.Nodes {
//			ipHasIn, _ := base_utils.InArray(node.Ip, toAddIpList)
//			if !ipHasIn {
//				toAddIpList = append(toAddIpList, node.Ip)
//			}
//			portHasIn, _ := base_utils.InArray(node.Port, toAddPortList)
//			if !portHasIn {
//				toAddPortList = append(toAddPortList, cast.ToInt32(node.Port))
//			}
//		}
//	}
//
//	for _, itf := range app.Interfaces {
//		for _, proxy := range itf.Proxys {
//			ipHasIn, _ := base_utils.InArray(proxy.Ip, toAddIpList)
//			if !ipHasIn {
//				toAddIpList = append(toAddIpList, proxy.Ip)
//			}
//		}
//	}
//
//	err = gmaster.GlobalMasterOp().UpdateInnerSecurity(ctx, &gmaster.UpdateInnerSecurityParams{
//		AppGroupID:  app.AppGroupID,
//		AppID:       app.AppId,
//		UserID:      app.UserId,
//		ToAddIpList: toAddIpList,
//		PortList:    toAddPortList,
//	})
//	if err != nil {
//		resource.LoggerTask.Warning(ctx, "call gmaster rebuild innersg fail", logit.Error("err", err))
//		return err
//	}
//	return nil
// }
