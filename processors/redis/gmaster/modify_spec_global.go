/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* modify_spec.go */
/*
modification history
--------------------
2023/01/08 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package gmaster

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessInitGlobalModifySpecStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("error", err))
		return err
	}

	// 将global master状态改为变配中
	if err := gmaster.GlobalMasterOp().GroupStatusCAS(
		ctx,
		app.AppGroupID,
		[]string{global_model.StatusModifySpecMigration, global_model.ShardStatusRunning},
		global_model.StatusModifySpecMigration,
		app.UserId,
	); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}

	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list cache group detail fail", logit.Error("error", err))
		return err
	}
	if groupDetail.Leader.ShardNum != params.ShardCount {
		return fmt.Errorf("shard count not match,global cluster num:%d", groupDetail.ClusterNum)
	}

	if app.AppId != groupDetail.Leader.ClusterShowId {
		resource.LoggerTask.Notice(ctx, "app is not leader, skip")
		if !isLeaderHasRegisterRightTask(ctx, app, global_model.ModifyStageApplyResource, global_model.ModifyTypeModifySpec, params.NodeType, params.ShardCount) {
			return fmt.Errorf("leader not register right task,err:%w", err)
		}
		return nil
	}
	return addModifyStatus(ctx, app, global_model.ModifyStageApplyResource, global_model.ModifyTypeModifySpec, params.NodeType, params.ShardCount)
}

func ProcessModifySpecGlobalHandoverCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}

	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get group detail fail", logit.Error("error", err))
		return err
	}

	// 从角色循环检查
	if groupDetail.Leader.ClusterShowId != app.AppId {
		if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageMigration, "100%"); err != nil {
			resource.LoggerTask.Warning(ctx, "check all members achieve target timeout", logit.Error("error", err))
			return err
		}
		if err := util.SetSwitchableFlagsAfterModifySpecHandover(ctx, app.AppId); err != nil {
			resource.LoggerTask.Warning(ctx, "set switchable flags fail", logit.Error("error", err))
			return err
		}
	} else {
		// 如果主角色这里过快，时间没配合好会有问题，增加sleep降低其概率，后续如果根治需要大改同步机制
		time.Sleep(5 * time.Second)
		members := append(groupDetail.Followers, groupDetail.Leader)
		for _, m := range members {
			fakeX1model := &x1model.Application{
				AppId:      m.ClusterShowId,
				UserId:     app.UserId,
				AppGroupID: app.AppGroupID,
			}
			if err := setMemberProgress(ctx, fakeX1model, global_model.ModifyStageMigration, "100%"); err != nil {
				resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
				return err
			}
		}
	}
	if err := setStage(ctx, app, global_model.ModifyStageReleaseResource); err != nil {
		resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCheckModifySpecGlobalCB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		resource.LoggerTask.Notice(ctx, "app group id is empty, skip")
		return nil
	}

	if err := setMemberProgress(ctx, app, global_model.ModifyStageReleaseResource, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "set member progress fail", logit.Error("error", err))
		return err
	}
	if err := checkAllMembersProgress(ctx, app, global_model.ModifyStageReleaseResource, "100%"); err != nil {
		resource.LoggerTask.Warning(ctx, "check all members progress timeout", logit.Error("error", err))
		return err
	}

	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get group detail fail", logit.Error("error", err))
		return err
	}
	// 非主直接跳过
	if groupDetail.Leader.ClusterShowId != app.AppId {
		resource.LoggerTask.Warning(ctx, "follower should not in this process,pls check")
		return nil
	}

	members := append(groupDetail.Followers, groupDetail.Leader)
	for _, m := range members {
		fakeX1model := &x1model.Application{
			AppId:      m.ClusterShowId,
			UserId:     app.UserId,
			AppGroupID: app.AppGroupID,
		}
		if err := setStage(ctx, fakeX1model, global_model.ModifyStageComplete); err != nil {
			resource.LoggerTask.Warning(ctx, "set stage fail", logit.Error("error", err))
			return err
		}
	}

	if err := gmaster.GlobalMasterOp().GroupStatusCAS(ctx, app.AppGroupID,
		[]string{global_model.StatusModifySpecMigration, global_model.ShardStatusRunning}, global_model.ShardStatusRunning, app.UserId); err != nil {
		resource.LoggerTask.Warning(ctx, "cas group status fail", logit.Error("error", err))
		return err
	}
	return nil
}
