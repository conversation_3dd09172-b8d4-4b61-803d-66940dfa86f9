/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/01/18 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file deploy_filebeat.go
 * <AUTHOR>
 * @date 2023/01/18 14:53:52
 * @brief
 *
 **/

package deploy

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

type installFilebeatParams struct {
	WorkDir        string `json:"workdir"`
	DBAPackageName string `json:"DBAPackageName"`
	DBAPackageUri  string `json:"DBAPackageUri"`
	DBAKafkaAddr   string `json:"DBAKafkaAddr"`
}

func ProcessDeployFilebeat(ctx context.Context, teu *workflow.TaskExecUnit) error {
	for i := 0; i < 3; i++ {
		err := processDeployFilebeat(ctx, teu)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "install filebeat fail", logit.Error("err", err))
			continue
		}
	}
	return nil
}

func processDeployFilebeat(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取node信息
	node, err := util.GetNodeByNodeID(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node.Status != x1model.ACLStatusToCreate && node.Status != x1model.NodeOrProxyStatusInUse {
		return nil
	}

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, node.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if app.UseNewPackage == 1 {
		resource.LoggerTask.Notice(ctx, "use new package, skip install filebeat")
		return nil
	}

	if app.ResourceType == "container" {
		return nil
	}

	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}
	packageName := util.GetDBAPackageName(conf.ScsMainConf.PackageVersion)
	installFilebeatReq := xagent.AsyncRequest{
		Addr:   &xAgentAddr,
		Action: "startFileBeat",
		Params: installFilebeatParams{
			WorkDir:        node.Basedir,
			DBAPackageName: packageName,
			DBAPackageUri:  util.GetPackageURI(packageName),
			DBAKafkaAddr:   conf.ScsMainConf.KafkaAddr,
		},
		TimeoutSec: 50,
	}
	_, err = xagent.Instance().DoAsync(ctx, &installFilebeatReq).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "install filebeat fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))
	}
	return err
}
