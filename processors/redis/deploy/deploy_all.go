package deploy

import (
	"context"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
	"icode.baidu.com/baidu/scs/x1-base/component/pkg_manager"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/getvars"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/render"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

const (
	AgentDeployDir                      = "/root"
	KernelDeployDIr                     = "/root/agentbin/"
	Slotredis60majorversion             = "6.2"
	SamePkgVersionServerIDUseAppVersion = "APP"
)

const (
	PegaDB256       = "pega_databases_256"
	PegaDB256Module = 1
	PegaDB256Value  = "yes"
)

var (
	DefaultPkgs          = []string{"agent", "csagent", "cron", "monitor-agent", "xagent", "opbin"}
	CoreBin              = []string{PegaDBPkg, BaiduRedisPkg, OpensourceRedisPkg, ProxyPkg, SyncAgentPkg}
	RedisModules         = []string{JsonModulePkg, CascadModulePkg, BloomfilterModulePkg}
	SmartDBApkg          = "smartdba"
	PegaDBPkg            = "PegaDB2"
	BaiduRedisPkg        = "slot-redis"
	OpensourceRedisPkg   = "redis"
	ProxyPkg             = "proxy-slot"
	CascadModulePkg      = "cascad-module"
	BloomfilterModulePkg = "bloomfilter-module"
	JsonModulePkg        = "json-module"
	SyncAgentPkg         = "sync-agent"
)

type DeployNodeOfAllTypeParams struct {
	AppID                  string
	ClusterID              string
	ServerID               string
	MaxDiskSize            int64
	XmasterEndPoint        string
	EngineType             string
	Kernel                 string
	KernelMajorVersion     string
	AppType                string
	DisableCommands        string
	BaseDir                string
	XagentHost             string
	XagentPort             int
	PackageVersion         string
	NodeIP                 string
	NodePort               int
	GlobalSeqID            int
	AppGlobalSeqID         int
	TaskID                 string
	IsUpgrade              bool
	IsMigrateTask          bool
	ForceRestart           bool
	NeedSyncAgent          bool
	NeedSmartDba           bool
	Password               string
	SamePkgVersionServerID string // 用来标注需要跟这个节点一致，也可以是 "APP" 用来标注是app维度最低版本
	AllSameTypeServerIDs   []string
	NodeFixID              string
	UserID                 string
	VpcID                  string
	UseNewAgent            bool
	Entity                 string
	NoUpdate               bool
	SyncAgentBigVer        string
}

type DeployRequestNew struct {
	PkgsToInstall []*PkgToInstall        `json:"pkgs_to_install"`
	Meta          *xagent.Meta           `json:"meta"`
	WorkDir       string                 `json:"work_dir"`
	EnvVars       any                    `json:"env_vars"`
	ForceRestart  bool                   `json:"force_restart"`
	IsUpgrade     bool                   `json:"is_upgrade"`
	IsMigrateTask bool                   `json:"is_migrate_task"`
	UseNewAgent   bool                   `json:"use_new_agent"`
	AgentRecovers *getvars.AgentRecovers `json:"agent_recovers"`
}

type PkgToInstall struct {
	DownloadURL   string                   `json:"download_url"`
	MD5           string                   `json:"md5"`
	Name          string                   `json:"name"`
	Version       string                   `json:"version"`
	DeployPath    string                   `json:"deploy_path"`
	NoNeedExecute bool                     `json:"no_need_execute"`
	RenderedConfs []*render.RenderedConfig `json:"rendered_confs"`
}

func deployNodeOfAllType(ctx context.Context, params *DeployNodeOfAllTypeParams) error {
	if err := util.PingTest(ctx, params.NodeIP, params.NodePort, 1, nil); err == nil || strings.Contains(err.Error(), "NOAUTH") {
		resource.LoggerTask.Trace(ctx, "ping node success, will skip deploy node",
			logit.String("nodeId", params.ServerID),
			logit.String("floatingIp", params.NodeIP),
			logit.Int("port", params.NodePort),
		)
		return nil
	}
	envVars := map[string]any{
		"APP_ID":           params.AppID,
		"CLUSTER_ID":       params.ClusterID,
		"SERVER_ID":        params.ServerID,
		"MAX_SPACE":        params.MaxDiskSize,
		"XMASTER_ENDPOINT": params.XmasterEndPoint,
		InstanceEngineKey:  params.EngineType,
	}
	if len(params.DisableCommands) != 0 {
		envVars["agent.disabled_commands"] = params.DisableCommands
	}
	if params.GlobalSeqID >= 0 && params.AppGlobalSeqID >= 0 {
		envVars["global_seq_id"] = params.GlobalSeqID
		envVars["app_global_seq_id"] = params.AppGlobalSeqID
	}
	if params.EngineType == InstanceEngineValuePegadb {
		envVars["xagent.enable_logic_inspection"] = "true"
	} else {
		envVars["xagent.enable_logic_inspection"] = "false"
	}
	deployer := deploy.NewDeployer(ctx,
		deploy.OptWaitReady(configWaitReady),
		deploy.OptWaitTask(configWaitTask),
	)
	deployReq := &deploy.DeployServerRequest{
		XagentAddr: &deploy.XagentAddr{
			Host: params.XagentHost,
			Port: int32(params.XagentPort),
		},
		ServerConf: []*deploy.CommonServerConf{
			{
				PackageTag: "xcache",
				Version:    params.PackageVersion,
				WorkDir:    params.BaseDir,
				PORT:       int32(params.NodePort),
				EnvVars:    envVars,
			},
		},
	}
	if err := deployer.Do(deployReq); err != nil &&
		!cerrs.ErrDeployWorkDirExists.Is(err) &&
		!cerrs.ErrDeployPortAlreadyUsed.Is(err) {
		resource.LoggerTask.Warning(ctx, "node deploy failed",
			logit.String("nodeId", params.ServerID),
			logit.Error("deployError", err),
		)
		return err
	}
	if err := util.PingTest(ctx, params.NodeIP, params.NodePort, pingTimeoutSec, nil); err != nil && !strings.Contains(err.Error(), "NOAUTH") {
		resource.LoggerTask.Warning(ctx, "ping redis failed",
			logit.String("nodeId", params.ServerID),
			logit.String("floatingIp", params.NodeIP),
			logit.Int("port", params.NodePort),
			logit.Error("err", err),
		)
		return err
	}
	return nil
}

func DeployNodeOfAllTypeNew(ctx context.Context, params *DeployNodeOfAllTypeParams) error {
	// todo 先都全强制重启，防止重启后有无密码混沌态
	params.ForceRestart = true
	toDeployPkgsRet, err := GetAndUpdateToDeployPkgs(ctx, params)
	if err != nil {
		return err
	}
	pkgs := toDeployPkgsRet.ToDeployPkgInfos
	if !params.IsUpgrade {
		isAlive, err := util.RedisAlivePing(ctx, params.NodeIP, params.NodePort)
		if isAlive {
			resource.LoggerTask.Trace(ctx, "node is alive, skip deploy", logit.Error("pingErr", err))
			if err := x1model.SaveDeployTime(ctx, params.ServerID, toDeployPkgsRet.PkgsRequire,
				toDeployPkgsRet.ToDeployPkgInfos); err != nil {
				return err
			}
			if err := x1model.SaveDeployTime(ctx, params.AppID, toDeployPkgsRet.PkgsRequire,
				toDeployPkgsRet.ToDeployPkgInfos); err != nil {
				return err
			}
			return nil
		}
	}
	xagentReq, err := GetXagentRequest(ctx, params, pkgs)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get xagent request failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "package manager xagent req", logit.String("req", base_utils.Format(xagentReq)))
	aCtx := xagent.Instance().DoAsync(ctx, xagentReq)
	resp, err := aCtx.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy node failed", logit.Error("err", err),
			logit.String("resp", base_utils.Format(resp)), logit.String("xagentreq", base_utils.Format(xagentReq)))
		return err
	}
	isAlive, err := util.RedisAlivePing(ctx, params.NodeIP, params.NodePort)
	if !isAlive {
		resource.LoggerTask.Warning(ctx, "node is not alive", logit.Error("error", err))
		return err
	}
	// 维护部署时间，用于真实版本获取
	if err := x1model.SaveDeployTime(ctx, params.ServerID, toDeployPkgsRet.PkgsRequire,
		toDeployPkgsRet.ToDeployPkgInfos); err != nil {
		return err
	}
	if err := x1model.SaveDeployTime(ctx, params.AppID, toDeployPkgsRet.PkgsRequire,
		toDeployPkgsRet.ToDeployPkgInfos); err != nil {
		return err
	}
	return nil
}

func GetXagentRequest(ctx context.Context, params *DeployNodeOfAllTypeParams, pkgs []*x1model.Package) (*xagent.AsyncRequest, error) {
	var err error
	dReq := &DeployRequestNew{
		WorkDir:      AgentDeployDir,
		EnvVars:      getEnvVars(params),
		ForceRestart: params.ForceRestart,
		Meta: &xagent.Meta{
			Engine:        params.Kernel,
			EngineVersion: params.KernelMajorVersion,
			Basedir:       params.BaseDir,
			Port:          int32(params.NodePort),
			AccountName:   "",
			Password:      params.Password,
		},
		UseNewAgent:   params.UseNewAgent,
		IsUpgrade:     params.IsUpgrade,
		IsMigrateTask: params.IsMigrateTask,
	}
	var getVarsParams *getvars.Parameter
	if params.UseNewAgent {
		getVarsParams, err = getvars.GetParamters(ctx, params.AppID, params.Entity)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get getvars params failed", logit.Error("err", err))
			return nil, err
		}
	}
	for _, pkg := range pkgs {
		// resource.LoggerTask.Trace(ctx, "get pkg info success", logit.String("pkg", base_utils.Format(pkg)))
		bosURL := pkg_manager.GetDownloadUrl(fmt.Sprintf("%s_%s.tar.gz", pkg.Name, pkg.FullVersion), 3600)
		resource.LoggerTask.Trace(context.Background(), "get bos url success", logit.String("bosURL", bosURL))
		var renderedConfs []*render.RenderedConfig
		if params.UseNewAgent && conf.IsCorePkg(pkg.Name) {
			renderedConfs, err = render.GetRenderdConf(ctx, getVarsParams, pkg.TplID, pkg.FullVersion)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get rendered conf failed",
					logit.Error("err", err), logit.String("pkg", base_utils.Format(pkg)))
				return nil, err
			}
		}
		dReq.PkgsToInstall = append(dReq.PkgsToInstall, &PkgToInstall{
			DownloadURL: bosURL,
			MD5:         pkg.MD5,
			Name:        pkg.Name,
			Version:     pkg.FullVersion,
			DeployPath: func() string {
				if conf.IsAgentsBin(pkg.Name) {
					return dReq.WorkDir + "/agentbin"
				}
				return dReq.WorkDir
			}(),
			NoNeedExecute: func() bool {
				return !conf.IsNeedExuctePkg(pkg.Name)
			}(),
			RenderedConfs: renderedConfs,
		})
	}
	if params.UseNewAgent {
		dReq.AgentRecovers, err = getvars.GetAgentRecover(ctx, getVarsParams)
		if err != nil {
			return nil, err
		}
	}
	xagentReq := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: params.XagentHost,
			Port: int32(params.XagentPort),
		},
		Action:     getAction(params),
		Params:     dReq,
		TimeoutSec: 180,
	}
	return xagentReq, nil
}

type ToDeployPkgsResp struct {
	ToDeployPkgInfos []*x1model.Package
	PkgsRequire      []string
	VersionFilter    map[string]string
}

func GetAndUpdateToDeployPkgs(ctx context.Context, params *DeployNodeOfAllTypeParams) (*ToDeployPkgsResp, error) {
	var toDeployPkgInfos []*x1model.Package
	var err error
	// 获取这个节点需要的包名字和版本过滤条件。
	pkgsRequire, versionFilter := x1model.GetRequirePkgNameAndVersionFilter(params.Kernel,
		params.KernelMajorVersion, params.AppType, params.NeedSmartDba, params.NeedSyncAgent,
		&x1model.GetRequirePkgNameMoreParams{SyncAgentBigVersion: params.SyncAgentBigVer})

	resource.LoggerTask.Trace(ctx, "get pkg name list success", logit.String("toDeployPkgInfos", base_utils.Format(pkgsRequire)),
		logit.String("versionFilter", base_utils.Format(versionFilter)), logit.String("kenel", params.Kernel),
		logit.String("kenelMajorVersion", params.KernelMajorVersion), logit.String("appType", params.AppType),
		logit.Bool("needSmartDba", params.NeedSmartDba), logit.Bool("needSyncAgent", params.NeedSyncAgent))

	if params.SamePkgVersionServerID != "" {
		// 如果是版本固定，需要找到参考节点，要么是一个具体节点，要么是APPID
		referEntity := params.SamePkgVersionServerID
		if params.SamePkgVersionServerID == SamePkgVersionServerIDUseAppVersion {
			// 如果是APP维度的，则用appid作为参考值
			referEntity = params.AppID
		}
		resource.LoggerTask.Trace(ctx, "this is refer deploy task", logit.String("params", base_utils.Format(params)),
			logit.String("refer entity", referEntity))
		toDeployPkgInfos, err = x1model.GetCurrentPackageRecord(ctx, referEntity, pkgsRequire, "")
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err))
			return nil, err
		}
	} else {
		// 不需要版本固定，是一个全新的节点，一般用于升级到最新版或全新部署
		resource.LoggerTask.Trace(ctx, "this is simple deploy task", logit.String("params", base_utils.Format(params)))
		toDeployPkgInfos, err = x1model.GetLatestPackages(ctx, pkgsRequire, versionFilter)
		// 替换为灰度版本包（内部判断必要性）
		toDeployPkgInfos = util.MergeGreyBoxPkgs(ctx, &util.ParmasGetGreyBoxFilter{
			ServerID:    params.ServerID,
			AppID:       params.AppID,
			UserID:      params.UserID,
			VpcID:       params.VpcID,
			UseNewAgent: params.UseNewAgent,
		}, toDeployPkgInfos)

		if err != nil {
			resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err))
			return nil, err
		}
		// 只有新增的检查，而版本固定的节点不检查，因为可能默认包进行了调整
		if !checkIsAllPkgGeted(ctx, toDeployPkgInfos, pkgsRequire, versionFilter) {
			return nil, errors.New("get toDeployPkgInfos fail")
		}
	}
	if !params.NoUpdate {
		// 维护这个节点的版本记录
		if err := x1model.UpgradeEntityPackegeRecord(ctx, params.ServerID, pkgsRequire, toDeployPkgInfos, "task-"+params.TaskID); err != nil {
			return nil, err
		}
		if err := x1model.UpgradeEntityPackegeRecord(ctx, params.AppID, pkgsRequire, toDeployPkgInfos, "task-"+params.TaskID); err != nil {
			return nil, err
		}
	}

	resource.LoggerTask.Trace(ctx, "get package info and upgrade pkg record success", logit.String("toDeployPkgInfos", base_utils.Format(toDeployPkgInfos)))

	return &ToDeployPkgsResp{
		ToDeployPkgInfos: toDeployPkgInfos,
		PkgsRequire:      pkgsRequire,
		VersionFilter:    versionFilter,
	}, err
}

func checkIsAllPkgGeted(ctx context.Context, getedPkgs []*x1model.Package, requiredPkgs []string, versionFilter map[string]string) bool {
	if len(getedPkgs) != len(requiredPkgs) {
		resource.LoggerTask.Warning(ctx, "package records not match require",
			logit.String("pkgs", base_utils.Format(getedPkgs)), logit.String("pkgs_require", base_utils.Format(requiredPkgs)))
		return false
	}
	for _, p := range requiredPkgs {
		get := false
		for _, toDeployPkgs := range getedPkgs {
			if toDeployPkgs.Name == p {
				get = true
				if version, ok := versionFilter[toDeployPkgs.Name]; ok {
					if version != toDeployPkgs.MajorVersion {
						resource.LoggerTask.Warning(ctx, "major version not match",
							logit.String("geted", base_utils.Format(toDeployPkgs)),
							logit.String("require version", version))
						return false
					}
				}
				break
			}
		}
		if !get {
			return false
		}
	}
	return true
}

func getEnvVars(params *DeployNodeOfAllTypeParams) map[string]any {
	envVars := map[string]any{
		"APP_ID":           params.AppID,
		"CLUSTER_ID":       params.ClusterID,
		"SERVER_ID":        params.ServerID,
		"MAX_SPACE":        params.MaxDiskSize,
		"XMASTER_ENDPOINT": params.XmasterEndPoint,
		InstanceEngineKey:  params.EngineType,
	}
	staticEnv := resource.PackageConf.GetStaticEnvVars()
	for k, v := range staticEnv {
		envVars[k] = v
	}
	if len(params.DisableCommands) != 0 {
		envVars["agent.disabled_commands"] = params.DisableCommands
	}
	if params.GlobalSeqID >= 0 && params.AppGlobalSeqID >= 0 {
		envVars["global_seq_id"] = params.GlobalSeqID
		envVars["app_global_seq_id"] = params.AppGlobalSeqID
	}
	if params.EngineType == InstanceEngineValuePegadb {
		envVars["xagent.enable_logic_inspection"] = "true"
		// pega也下发热活id，用于替换clusterid配置
		if params.AppGlobalSeqID >= 0 {
			envVars["app_global_seq_id"] = params.AppGlobalSeqID
		}
	} else {
		envVars["xagent.enable_logic_inspection"] = "false"
	}
	envVars["dba_kafka_addr"] = conf.ScsMainConf.KafkaAddr
	return envVars
}

func getAction(params *DeployNodeOfAllTypeParams) string {
	return "package_install"
}

func CommonGetPackageInfos(engineType string, kernelMajorVersion string, appType string, needSyncAgent bool,
	nonbin bool, needSmartDba bool) ([]string, map[string]string) {
	requirePkgs, versionFilter := x1model.GetRequirePkgNameAndVersionFilter(engineType,
		kernelMajorVersion, appType, needSyncAgent, needSmartDba, &x1model.GetRequirePkgNameMoreParams{SyncAgentBigVersion: ""})

	var retPkgs []string
	var retVersionFilter = make(map[string]string, 0)
	if nonbin {
		for _, pkg := range requirePkgs {
			isCoreBin, _ := base_utils.InArray(pkg, CoreBin)
			if !isCoreBin {
				retPkgs = append(retPkgs, pkg)
				if v, ok := versionFilter[pkg]; ok {
					retVersionFilter[pkg] = v
				}
			}
		}
	} else {
		return requirePkgs, versionFilter
	}

	return retPkgs, retVersionFilter
}

func GetNodeDeployParams(app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node,
	cmdList string, password string) *DeployNodeOfAllTypeParams {
	ret := &DeployNodeOfAllTypeParams{
		AppID:              app.AppId,
		ClusterID:          cluster.ClusterId,
		ServerID:           node.NodeId,
		MaxDiskSize:        cluster.DiskSize,
		XmasterEndPoint:    util.GetXmasterEndpoint(),
		EngineType:         cluster.Engine,
		Kernel:             cluster.Engine,
		KernelMajorVersion: cluster.EngineVersion,
		AppType:            app.Type,
		DisableCommands:    cmdList,
		BaseDir:            node.Basedir,
		XagentHost:         node.FloatingIP,
		XagentPort:         node.XagentPort,
		PackageVersion:     util.GetVersion(app),
		NodeIP:             node.FloatingIP,
		NodePort:           node.Port,
		Password:           password,
		NodeFixID:          node.NodeFixID,
		UserID:             app.UserId,
		VpcID:              app.VpcId,
		UseNewAgent:        app.UseNewAgent == "yes",
		Entity:             node.NodeId,
		AppGlobalSeqID:     -1,
		SyncAgentBigVer:    app.SyncAgentBigver,
	}
	if len(ret.BaseDir) == 0 {
		ret.BaseDir = "/root"
	}
	if len(app.SyncGroupID) != 0 {
		ret.NeedSyncAgent = true
	}
	if app.ResourceType != "container" {
		ret.NeedSmartDba = true
	}
	if util.NeedGlobalInfo(app) {
		ret.AppGlobalSeqID = app.AppGroupSeqID
	}
	return ret
}

func GetProxyDeployParams(app *x1model.Application, itf *x1model.Interface, proxy *x1model.Proxy) *DeployNodeOfAllTypeParams {
	ret := &DeployNodeOfAllTypeParams{
		AppID:              app.AppId,
		ClusterID:          app.AppId,
		ServerID:           proxy.ProxyId,
		MaxDiskSize:        itf.DiskSize,
		XmasterEndPoint:    util.GetXmasterEndpoint(),
		EngineType:         InstanceEngineValueProxy,
		Kernel:             itf.Engine,
		KernelMajorVersion: itf.EngineVersion,
		AppType:            app.Type,
		BaseDir:            proxy.Basedir,
		XagentHost:         proxy.FloatingIP,
		XagentPort:         proxy.XagentPort,
		PackageVersion:     util.GetVersion(app),
		NodeIP:             proxy.FloatingIP,
		NodePort:           proxy.Port,
		GlobalSeqID:        -1,
		AppGlobalSeqID:     -1,
		UserID:             app.UserId,
		VpcID:              app.VpcId,
		UseNewAgent:        app.UseNewAgent == "yes",
		Entity:             proxy.ProxyId,
		SyncAgentBigVer:    app.SyncAgentBigver,
	}
	if len(ret.BaseDir) == 0 {
		ret.BaseDir = "/root"
	}
	if util.NeedGlobalInfo(app) {
		ret.GlobalSeqID = proxy.GlobalSeqID
		ret.AppGlobalSeqID = app.AppGroupSeqID
	}
	return ret
}

func GetEachEntityDeployParams(ctx context.Context, app *x1model.Application, isforce bool) (map[string]*DeployNodeOfAllTypeParams, error) {
	ret := make(map[string]*DeployNodeOfAllTypeParams)
	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return nil, err
	}
	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return nil, err
	}
	// 存储 NodeFixID => serverID 的map
	fakeDeleteNodeFixIdMap := make(map[string]string, 0)

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				fakeDeleteNodeFixIdMap[node.NodeFixID] = node.NodeId
			}
			if !isforce && node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			ret[node.NodeId] = GetNodeDeployParams(app, cluster, node, cmdList, password)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				fakeDeleteNodeFixIdMap[proxy.NodeFixID] = proxy.ProxyId
			}
			if !isforce && proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			ret[proxy.ProxyId] = GetProxyDeployParams(app, itf, proxy)
		}
	}
	//	 如果tofakedelete状态的节点中有跟即将部署节点一样NodeFixID的节点，说明是继承来的，所以需要继承他的包版本
	for _, deployInfo := range ret {
		if deployInfo.SamePkgVersionServerID != "" {
			continue
		}
		if serverID, ok := fakeDeleteNodeFixIdMap[deployInfo.NodeFixID]; ok {
			resource.LoggerTask.Trace(ctx, "get the old tofake delete node", logit.String("server_id", serverID))
			deployInfo.SamePkgVersionServerID = serverID
		}
	}

	return ret, nil
}

func ProcessDeployAllForNewCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := initDefaultConfigs(ctx, app); err != nil {
		return err
	}
	err = deployall(ctx, teu.TaskID, app, false)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deployall fail", logit.String("appId", teu.Entity),
			logit.Error("Error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

func initDefaultConfigs(ctx context.Context, app *x1model.Application) error {
	if app.UseNewAgent != "yes" {
		return nil
	}
	csmasterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("err", err))
		return err
	}
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(csmasterModel.Id))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get config by app short id failed", logit.Error("err", err))
		return err
	}
	for _, confRecord := range confRecords {
		if confRecord.ConfName == PegaDB256 && csmasterModel.StoreType == 3 {
			resource.LoggerTask.Trace(ctx, "pega_databases_256 is already in csmaster", logit.String("confName", PegaDB256))
			return nil
		}
	}
	var confDefs []*csmaster_model_interface.UserConfList
	if err := resource.CsmasterModel.GetAllByCond(ctx, &confDefs, "1 = 1"); err != nil {
		resource.LoggerTask.Warning(ctx, "load csmaster config def from db failed", logit.Error("error", err))
		return err
	}
	for _, confDef := range confDefs {
		if confDef.ConfName == PegaDB256 && csmasterModel.StoreType == 3 {
			confRecord := &csmaster_model_interface.ConfRecordList{
				ClusterID:  int(csmasterModel.Id),
				ConfName:   PegaDB256,
				ConfModule: csmaster_model_interface.CONF_MODULE_REDIS,
				Value:      PegaDB256Value,
				Effected:   1,
			}
			if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, []*csmaster_model_interface.ConfRecordList{confRecord}); err != nil {
				resource.LoggerTask.Warning(ctx, "save csmaster config record failed", logit.Error("error", err))
				return err
			}
			resource.LoggerTask.Notice(ctx, "save csmaster config record success", logit.String("confName", PegaDB256))
		}
	}
	return nil
}

// ProcessDeployAll 用来给变配任务用，这个方法会使用整个APP中最老的版本部署所有包
func ProcessDeployAll(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return deployall(ctx, teu.TaskID, app, true)
}

// needFixVersion : 是否需要版本固定，如果false就会默认用所有包的最新版，true的话会根据是否有继承的节点来判断用什么版本
func deployall(ctx context.Context, taskID string, app *x1model.Application, needFixVersion bool) error {
	deployParamMap, err := GetEachEntityDeployParams(ctx, app, false)
	if err != nil {
		return err
	}
	g := gtask.Group{Concurrent: 200}
	for serverId, params := range deployParamMap {
		serverId := serverId
		params := params
		params.TaskID = taskID
		if needFixVersion {
			if params.SamePkgVersionServerID == "" {
				params.SamePkgVersionServerID = SamePkgVersionServerIDUseAppVersion
			} else {
				resource.LoggerTask.Trace(ctx, "use app ver , but this node has old node, skip", logit.String("params", base_utils.Format(params)))
			}
		}
		g.Go(func() error {
			var deployErr error
			start := time.Now()
			resource.LoggerTask.Trace(ctx, "begin to deploy", logit.String("nodeId", serverId), logit.Time("start", start))
			if app.UseNewPackage == 1 {
				deployErr = DeployNodeOfAllTypeNew(ctx, params)
			} else {
				deployErr = deployNodeOfAllType(ctx, params)
			}
			end := time.Now()
			resource.LoggerTask.Trace(ctx, "end to deploy", logit.String("nodeId", serverId),
				logit.Time("start", start), logit.Time("end", end), logit.Duration("cost", end.Sub(start)), logit.Error("err", deployErr))
			return deployErr
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy failed", logit.Error("err", err))
		return err
	}

	return nil
}
