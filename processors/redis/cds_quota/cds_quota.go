package cdsquota

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	crmComp "icode.baidu.com/baidu/scs/x1-base/component/crm"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	crmSdk "icode.baidu.com/baidu/scs/x1-base/sdk/crm"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func GetPegaCdsConf(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app is nilptr")
		return errors.Errorf("app is nilptr")
	}

	if param == nil {
		resource.LoggerTask.Warning(ctx, "param is nilptr")
		return errors.Errorf("param is nilptr")
	}

	// 判断引擎类型,如果是Redis则不做处理,如果是Pega,则进一步校验用户类型
	if param.Engine == "redis" {
		resource.LoggerTask.Notice(ctx, "engine is redis, do nothing")
		return nil
	} else if param.Engine == "pegadb" {
		userID := app.UserId
		// 默认是外部用户
		InternalFlag := false
		var rsp *crmSdk.CustomerDetailInfo
		rsp, err := crmComp.CrmResourceOp().GetCustomerDetailInfo(ctx, userID)
		if err != nil {
			resource.LoggerTask.Error(ctx, userID+" getCustomerDetailInfo failed", logit.Error("error", err))
		} else {
			resource.LoggerTask.Trace(ctx, userID+" getCustomerDetailInfo success, IsInternalFlag:"+cast.ToString(rsp.IsInternal))
			if rsp.IsInternal {
				resource.LoggerTask.Trace(ctx, userID+" is internal user")
				InternalFlag = true
			}
			// 测试账号模拟外部客户进行功能验证
			if userID == "4fa25b1e8fb64b06b472e49c876ce24a" {
				InternalFlag = false
			}
		}
		if InternalFlag {
			// 如果是内部用户 磁盘1.2倍预留
			cdsQuota := []*x1model.CdsQuota{{
				AppID:             app.AppId,
				ReservePercentage: 20, // 内部用户1.2倍预留
				ReserveMaxQuota:   80, // 内部用户最大预留量80G
				CreateAt:          time.Now(),
			}}
			if err := x1model.CdsQuotaSave(ctx, cdsQuota); err != nil {
				resource.LoggerTask.Error(ctx, "save cds quota error", logit.Error("error", err))
				return err
			}

		} else {
			// 如果是外部用户 磁盘1.5倍预留
			cdsQuota := []*x1model.CdsQuota{{
				AppID:             app.AppId,
				ReservePercentage: 50, // 外部用户1.5倍预留
				ReserveMaxQuota:   -1, // 外部用户不设此限制
				CreateAt:          time.Now(),
			}}
			if err := x1model.CdsQuotaSave(ctx, cdsQuota); err != nil {
				resource.LoggerTask.Error(ctx, "save cds quota error", logit.Error("error", err))
				return err
			}
		}
	} else {
		// param.Engine 不支持
		msg := "engine type:" + param.Engine + " is not supported"
		resource.LoggerTask.Warning(ctx, msg)
		return errors.Errorf(msg)
	}
	return nil
}
