package faultInjection

import (
	"errors"
	"testing"
)

func TestExtractShardIndex(t *testing.T) {
	testCases := []struct {
		name      string
		shardID   string
		size      int
		want      int
		wantError error
	}{
		{
			name:      "valid shardID and size",
			shardID:   "test-test-test-2",
			size:      5,
			want:      2,
			wantError: nil,
		},
		{
			name:      "invalid shardID format",
			shardID:   "invalid-shardID",
			size:      5,
			want:      0,
			wantError: errors.New("shard id invalid, invalid-shardID"),
		},
		{
			name:      "shardID number larger than size",
			shardID:   "test-test-test-5",
			size:      5,
			want:      0,
			wantError: errors.New("shard id invalid, shard idx 5"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := extractShardIndex(tc.shardID, tc.size)
			if got != tc.want || func() bool {
				if tc.wantError == nil && err == nil {
					return false
				}
				if tc.wantError != nil && err != nil {
					return tc.wantError.Error() != err.Error()
				}
				return true
			}() {
				t.Fatalf("extractShardIndex(%q, %v) = %v, %v; want %v, %v", tc.shardID, tc.size, got, err, tc.want, tc.wantError)
			}
		})
	}
}
