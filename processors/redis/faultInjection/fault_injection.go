/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* flush_cluster_app.go - workflow for flush bdrp app */

/*
Modification History
--------------------
2022/5/19, by <PERSON><PERSON><PERSON><PERSON>, create
*/

/*
DESCRIPTION
进行故障注入
*/

package faultInjection

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	tkResource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	FaultInjectionAction       = "fault_injection"
	FaultInjectionEvent        = "fault_injection_event"
	FaultInjectionEventSuccess = "success"
	FaultInjectionEventFailed  = "failed"
)

type Params struct {
	UserID  string `json:"user_id"`
	AppID   string `json:"app_id"`
	ShardID string `json:"shard_id"`
	NodeID  string `json:"node_id"`
}

type ExecParams struct {
	ClusterID string `json:"cluster_id"`
	NodeID    string `json:"node_id"`
}

type XagentParams struct {
	WorkDir string `json:"work_dir"`
}

func ProcessFaultInjectionStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, FaultInjectionEvent); err != nil {
		resource.LoggerTask.Warning(ctx, "add event start failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessFaultInjectionPreCheck 任务中再次调用检查集群状态，由该检查&任务的mutex确保不与其他任务冲突
func ProcessFaultInjectionPreCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusWaiting, true); err != nil {
		resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
		return err
	}
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	detail, err := csmaster.CsmasterOp().CsmasterGetClusterDetail(ctx, &csmaster.ListCacheClusterInstancesParam{
		UserID: app.UserId,
		AppID:  app.AppId,
	})
	if (clusterModel.Status != 5 && clusterModel.Status != 16) || detail.Status != "running" {
		resource.LoggerTask.Warning(ctx, "cluster is not running", logit.Error("error", err))
		return errors.New("cluster is not running")
	}
	req := csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: 24,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &req); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessFaultInjectionStartExecution 启动执行注入的workflow
// 执行注入的workflow mutex确保与切换任务互斥
func ProcessFaultInjectionStartExecution(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	var params Params
	if err := json.Unmarshal([]byte(teu.Parameters), &params); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal parameters failed", logit.Error("error", err))
		return err
	}
	nodeId := ""
	if params.NodeID != "" {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				if node.NodeFixID == params.NodeID {
					nodeId = node.NodeId
					break
				}
			}
		}
	}
	// Create a new slice to store all app.Clusters elements
	clusters := make([]*x1model.Cluster, len(app.Clusters))
	copy(clusters, app.Clusters)
	// Sort the clusters slice by ClusterShortID in ascending order
	sort.Slice(clusters, func(i, j int) bool {
		return clusters[i].ClusterShortID < clusters[j].ClusterShortID
	})
	shardIndex, err := extractShardIndex(params.ShardID, len(clusters))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "extract shard index failed", logit.Error("error", err))
		return err
	}
	cluster := clusters[shardIndex]
	if err := tkResource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{
		{
			WorkFlow:   "scs-fault-injection-exec",
			Schedule:   time.Now(),
			Timeout:    24 * time.Hour,
			Mutex:      "c_" + app.AppId + strconv.Itoa(cluster.ClusterShortID),
			Entity:     app.AppId,
			Parameters: base_utils.Format(&ExecParams{ClusterID: cluster.ClusterId, NodeID: nodeId}),
		},
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "create sub tasks failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessFaultInjectionExecute 执行注入
func ProcessFaultInjectionExecute(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	var params ExecParams
	if err := json.Unmarshal([]byte(teu.Parameters), &params); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal parameters failed", logit.Error("error", err))
		return err
	}
	// 仅第一次执行时选取注入节点, 记录到broker-redis中, 过期20min
	// 后续执行时, 获取第一次写入到broker-redis的node
	var toInjectNode *x1model.Node
	if util.GetRetry(teu.TaskBatchID) == 0 {
		toInjectNode, err = getToInjectNode(ctx, &params, app, "")
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get to inject node failed", logit.Error("error", err))
			return err
		}
		if _, err := resource.RedisClient.Set(ctx, teu.TaskID+"-fault_injection_node_id", toInjectNode.NodeId, 20*time.Minute).Result(); err != nil {
			resource.LoggerTask.Warning(ctx, "set fault injection node id failed", logit.Error("error", err))
			return err
		}
	} else {
		nodeId, err := resource.RedisClient.Get(ctx, teu.TaskID+"-fault_injection_node_id").Result()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get fault injection node id failed", logit.Error("error", err))
			return err
		}
		if nodeId == "" {
			resource.LoggerTask.Warning(ctx, "node id not found")
			return errors.New("node id not found")
		}
		toInjectNode, err = getToInjectNode(ctx, &params, app, nodeId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get to inject node failed", logit.Error("error", err))
			return err
		}
	}

	if err := inject(ctx, toInjectNode); err != nil {
		resource.LoggerTask.Warning(ctx, "inject failed", logit.Error("error", err))
		return err
	}
	if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusInjected, false); err != nil {
		resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
		return err
	}
	return nil
}

func getToInjectNode(ctx context.Context, params *ExecParams, app *x1model.Application, curNodeId string) (*x1model.Node, error) {
	if curNodeId != "" {
		for _, cluster := range app.Clusters {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.NodeId == curNodeId {
					return node, nil
				}
			}
		}
		return nil, fmt.Errorf("cur node not found, node id: %s", curNodeId)
	}
	if params.NodeID != "" {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				if node.NodeId == params.NodeID {
					return util.ChangeRoNode2Node(node), nil
				}
			}
		}
	} else {
		for _, cluster := range app.Clusters {
			if cluster.ClusterId != params.ClusterID {
				continue
			}
			// 如果是热活从，随便选择一个注入故障
			for _, node := range cluster.Nodes {
				if node.Role == x1model.RoleTypeMaster || isAppGroupSlave(app) {
					return node, nil
				}
			}
		}
	}
	return nil, errors.New("node not found")
}

func ProcessFaultInjectionEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, FaultInjectionEvent, FaultInjectionEventSuccess); err != nil {
		resource.LoggerTask.Warning(ctx, "add event end failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessFaultInjectionEndEventError(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, FaultInjectionEvent, FaultInjectionEventFailed); err != nil {
		resource.LoggerTask.Warning(ctx, "add event end failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessFaultInjectionCheckTasks 检查注入任务成功后，检查切换任务的触发，更新相应状态
func ProcessFaultInjectionCheckTasks(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	var params Params
	if err := json.Unmarshal([]byte(teu.Parameters), &params); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal parameters failed", logit.Error("error", err))
		return err
	}
	defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	for {
		time.Sleep(5 * time.Second)
		execTaskSucc, execTask, err := checkExecTaskSuccess(ctx, teu)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check exec task success failed", logit.Error("error", err))
			return err
		}
		if !execTaskSucc {
			continue
		}
		taskSucc, err := checkFailoverTasksSuccess(ctx, execTask, app, defaultACL, &params)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check failover tasks success failed", logit.Error("error", err))
			return err
		}
		if !taskSucc {
			continue
		}
		break
	}
	return nil
}

func ProcessFaultInjectionCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	req := csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: 5,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &req); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessFaultInjectionWaitSelfHealing(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	execTasks, err := tkResource.TaskOperator.RetrieveTasks(ctx, "p_task_id = ?", teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "retrieve tasks failed", logit.Error("error", err))
		return err
	}
	if len(execTasks) == 0 {
		resource.LoggerTask.Notice(ctx, "exec tasks not found")
		return errors.New("exec tasks not found")
	}
	execTask := execTasks[len(execTasks)-1]
	for {
		time.Sleep(5 * time.Second)
		selfHealingSucc, err := checkSelfHealingTaskSucc(ctx, execTask, app)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check self healing tasks success failed", logit.Error("error", err))
			return err
		}
		if !selfHealingSucc {
			continue
		}
		break
	}
	return nil
}

func inject(ctx context.Context, node *x1model.Node) error {
	xAgentReq := xagent.Request{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: FaultInjectionAction,
		Params: &XagentParams{
			WorkDir: node.Basedir,
		},
	}
	_, err := xagent.Instance().Do(ctx, &xAgentReq)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "do fault injection failed", logit.Error("error", err))
		return err
	}
	return nil
}

func checkExecTaskSuccess(ctx context.Context, teu *workflow.TaskExecUnit) (bool, *iface.Task, error) {
	execTasks, err := tkResource.TaskOperator.RetrieveTasks(ctx, "p_task_id = ?", teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "retrieve tasks failed", logit.Error("error", err))
		return false, nil, err
	}
	if len(execTasks) == 0 {
		resource.LoggerTask.Notice(ctx, "exec tasks not found")
		return false, nil, errors.New("exec tasks not found")
	}
	execTask := execTasks[len(execTasks)-1]
	if execTask.Status != iface.TaskStatusSuccess {
		resource.LoggerTask.Notice(ctx, "exec tasks not success")
		return false, execTask, nil
	}
	return true, execTask, nil
}

func checkFailoverTasksSuccess(ctx context.Context, execTask *iface.Task, app *x1model.Application,
	defaultACL *x1model.RedisAcl, parameters *Params) (bool, error) {
	wf, err := getFailoverWorkflow(ctx, app)
	if err != nil {
		return false, err
	}
	// 如果是热活从，直接更新整体成功
	if isAppGroupSlave(app) {
		if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusInSelfHealing, false); err != nil {
			resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
			return false, err
		}
		resource.LoggerTask.Notice(ctx, "slave node injection, skip failover")
		return true, nil
	}
	if parameters.NodeID != "" {
		if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusInSelfHealing, false); err != nil {
			resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
			return false, err
		}
		resource.LoggerTask.Notice(ctx, "readonly node injection, skip failover")
		return true, nil
	}
	tasks, err := tkResource.TaskOperator.RetrieveTasks(
		ctx, "entity = ? and work_flow = ? and status != ? and id > ?", app.AppId, wf, "fake", execTask.ID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "retrieve tasks failed", logit.Error("error", err))
		return false, err
	}
	if len(tasks) == 0 {
		resource.LoggerTask.Notice(ctx, "failover tasks not found")
		return false, nil
	}
	if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusInFailover, false); err != nil {
		resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
		return false, err
	}
	for _, t := range tasks {
		if t.Status != iface.TaskStatusSuccess {
			resource.LoggerTask.Notice(ctx, "failover tasks not success")
			return false, nil
		}
	}
	var params ExecParams
	if err := json.Unmarshal([]byte(execTask.Parameters), &params); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal parameters failed", logit.Error("error", err))
		return false, err
	}
	// 切换完成，重新获取一下app的信息
	app, err = x1model.ApplicationGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return false, err
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterId != params.ClusterID {
			continue
		}
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if err := util.PingTest(ctx, node.FloatingIP, node.Port, 5, defaultACL); err != nil {
					resource.LoggerTask.Warning(ctx, "ping test failed, failover failed", logit.Error("error", err))
					return false, err
				}
				break
			}
		}
		break
	}
	if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusInSelfHealing, false); err != nil {
		resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
		return false, err
	}
	return true, nil
}

func getFailoverWorkflow(ctx context.Context, app *x1model.Application) (string, error) {
	var wf string
	switch app.Type {
	case x1model.AppTypeStandalone:
		wf = "scs-standalone-failover"
	case x1model.AppTypeCluster:
		wf = "scs-shard-failover"
	default:
		resource.LoggerTask.Warning(ctx, "app type invalid", logit.String("app_type", app.Type))
		return "", fmt.Errorf("app type invalid, %s", app.Type)
	}
	return wf, nil
}

func checkSelfHealingTaskSucc(ctx context.Context, execTask *iface.Task, app *x1model.Application) (bool, error) {
	wf, err := getSelfHealingWorkflow(ctx, app)
	if err != nil {
		return false, err
	}
	tasks, err := tkResource.TaskOperator.RetrieveTasks(
		ctx, "entity = ? and work_flow = ? and status != ? and id > ?", app.AppId, wf, "fake", execTask.ID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "retrieve tasks failed", logit.Error("error", err))
		return false, err
	}
	if len(tasks) == 0 {
		resource.LoggerTask.Notice(ctx, "self healing tasks not found")
		return false, nil
	}
	for _, t := range tasks {
		if t.Status != iface.TaskStatusSuccess {
			resource.LoggerTask.Notice(ctx, "self healing tasks not success")
			return false, nil
		}
	}
	if err := updateInjectionStatus(ctx, app, x1model.InjectionStatusNormal, false); err != nil {
		resource.LoggerTask.Warning(ctx, "update injection status failed", logit.Error("error", err))
		return false, err
	}
	return true, nil
}

func getSelfHealingWorkflow(ctx context.Context, app *x1model.Application) (string, error) {
	var wf string
	switch app.Type {
	case x1model.AppTypeStandalone:
		wf = "scs-failover-standalone-app"
	case x1model.AppTypeCluster:
		wf = "scs-failover-cluster-app"
	default:
		resource.LoggerTask.Warning(ctx, "app type invalid", logit.String("app_type", app.Type))
		return "", fmt.Errorf("app type invalid, %s", app.Type)
	}
	return wf, nil
}

func extractShardIndex(shardID string, size int) (int, error) {
	pattern := `^[a-z]+-[a-z]+-[a-z]+-(\d)$`
	re := regexp.MustCompile(pattern)
	match := re.FindStringSubmatch(shardID)
	if match == nil {
		return 0, fmt.Errorf("shard id invalid, %s", shardID)
	}
	shardIDNum, err := strconv.Atoi(match[1])
	if err != nil {
		return 0, fmt.Errorf("shard id invalid, %s, %w", shardID, err)
	}
	if shardIDNum >= size {
		return 0, fmt.Errorf("shard id invalid, shard idx %d", shardIDNum)
	}
	return shardIDNum, nil
}

func updateInjectionStatus(ctx context.Context, app *x1model.Application, status string, updateLastInjection bool) error {
	fis, err := x1model.InjectionGetAllByCond(ctx, "app_id = ?", app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get injection failed", logit.Error("error", err))
		return err
	}
	if len(fis) == 0 || fis[0].Status == x1model.InjectionStatusNotAvailable {
		resource.LoggerTask.Warning(ctx, "app not in whitelist")
		return errors.New("app not in whitelist")
	}
	fis[0].Status = status
	if updateLastInjection {
		fis[0].LastInjection = time.Now()
	}
	if err := x1model.InjectionSave(ctx, fis); err != nil {
		resource.LoggerTask.Warning(ctx, "update injection failed", logit.Error("error", err))
		return err
	}
	return nil
}

func isAppGroupSlave(app *x1model.Application) bool {
	if app.AppGroupID != "" {
		for _, node := range app.Clusters[0].Nodes {
			if node.Role == x1model.RoleTypeMaster {
				return false
			}
		}
		return true
	}
	return false
}
