/*
modification history
--------------------
2022/01/12, by s<PERSON><PERSON><PERSON>(<EMAIL>), first version
*/

/*
DESCRIPTION
	设置标准版ACL/Auth
*/

package acl

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessInitAclStandalone 设置初始acl/auth
func ProcessInitAclStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processSetAcl(ctx, teu.Entity, processTypeInitAcl)
}

// ProcessInitAclStandaloneForNewNode 设置初始acl/auth(新建节点)
func ProcessInitAclStandaloneForNewNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processSetAcl(ctx, teu.Entity, processTypeInitAclForNewNode)
}

// ProcessUpdateAcl 更新acl/auth
func ProcessUpdateAcl(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processSetAcl(ctx, teu.Entity, processTypeUpdateAcl)
}

func ProcessInitAclForCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return createCsmasterDefaultAcl(ctx, app)
}

// ProcessInitACLForRo 设置初始acl/auth
func ProcessInitACLForRo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processSetAcl(ctx, teu.Entity, processTypeInitAclForNewNode)
}

// ProcessInitAclStandaloneForVersionUpgrade
func ProcessInitAclStandaloneForVersionUpgrade(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processSetAcl(ctx, teu.Entity, processTypeInitAcl)
}

func processSetAcl(ctx context.Context, appId string, processType int) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	if processType == processTypeInitAclForNewNode && app.UseNewAgent == "yes" {
		resource.LoggerTask.Notice(ctx, "use new agent, skip init acl for new node")
		return nil
	}

	// 生效acl/auth
	updatedAclList, err := setUserAcls(ctx, app, processType)
	if err != nil {
		return err
	}

	// 如果是init，通知csmaster 创建默认user acl
	if processType == processTypeInitAcl {
		return createCsmasterDefaultAcl(ctx, app)
	}

	// 其他情况，通知csmaster 更新acl status
	if len(updatedAclList) == 0 {
		return nil
	}
	return updateCsmasterAclStatus(ctx, app, updatedAclList)
}

// 生效user acl，返回updated acl list
func setUserAcls(ctx context.Context, app *x1model.Application, processType int) ([]*x1model.RedisAcl, error) {
	// acl设置过程可能在各个阶段出现异常并退出workflow，为保证重试可以正常进行，
	// 此处获取所有非删除状态的default user acl， 供后续tryGetEffectiveAcl使用
	defaultAclList, err := getDefaultUserAclList(ctx, app)
	if err != nil {
		return nil, err
	}

	// 获取app的acl列表
	aclList, err := getAppAclList(ctx, app)
	if err != nil {
		return nil, err
	}
	if len(aclList) == 0 {
		resource.LoggerTask.Notice(ctx, "no acl to set",
			logit.String("appId", app.AppId))
		return nil, nil
	}

	/*
		redis 7.0 acl不支持以下命令: "+POST"  "+HOST:"  "+STRALGO"
	*/

	if app.Clusters[0].EngineVersion == "7.0" {
		resource.LoggerTask.Notice(ctx, "user acl before",
			logit.String("appId", app.AppId),
			logit.String("aclList", base_utils.Format(aclList)))

		for _, aclConf := range aclList {
			if strings.Contains(aclConf.AllowedCmds, ";") {
				oldCmds := aclConf.AllowedCmds
				oldCmdsSplit := strings.Split(oldCmds, ";")
				newCmds := ""
				for _, cmd := range oldCmdsSplit {
					if strings.Contains(cmd, "POST") || strings.Contains(cmd, "HOST:") || strings.Contains(cmd, "STRALGO") {
						continue
					} else {
						newCmds += cmd
						newCmds += ";"
					}
				}
				aclConf.AllowedCmds = newCmds[:len(newCmds)-1]
			}
		}

		resource.LoggerTask.Notice(ctx, "user acl after",
			logit.String("appId", app.AppId),
			logit.String("aclList", base_utils.Format(aclList)))
	}

	// 调用xagent执行acl update
	if err := setUserAclsWithXagent(ctx, app, defaultAclList, aclList, processType); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to set user acls",
			logit.String("appId", app.AppId),
			logit.Error("err", err))
		return nil, err
	}

	// 生效后，刷新acl list
	updatedAclList := updateAclListAfterTakingEffect(aclList)
	if len(updatedAclList) == 0 {
		resource.LoggerTask.Notice(ctx, "no acl updated",
			logit.String("appId", app.AppId))
		return nil, nil
	}

	// 更新数据库状态
	if app.Type == x1model.AppTypeStandalone {
		if err := x1model.RedisAclSave(ctx, updatedAclList); err != nil {
			resource.LoggerTask.Warning(ctx, "fail to save acl after taking effect",
				logit.String("appId", app.AppId),
				logit.Error("err", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
	} else {
		if err := x1model.ProxyAclSave(ctx, RedisAclsToProxyAcls(updatedAclList)); err != nil {
			resource.LoggerTask.Warning(ctx, "fail to save proxy acl after taking effect",
				logit.String("appId", app.AppId),
				logit.Error("err", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return updatedAclList, nil
}

// 通过xagent更新所有节点的acl
func setUserAclsWithXagent(ctx context.Context, app *x1model.Application,
	defaultAclList []*x1model.RedisAcl, aclList []*x1model.RedisAcl, processType int) error {
	g := gtask.Group{
		Concurrent:    setAclConcurrent,
		AllowSomeFail: false,
	}

	aclListParam := make([]*iface.RedisAcl, 0)
	for _, acl := range aclList {
		if acl.Status == x1model.ACLStatusToDelete {
			continue
		}
		aclListParam = append(aclListParam, &iface.RedisAcl{
			AccountName:    acl.AccountName,
			Password:       acl.Password,
			AllowedCmds:    acl.AllowedCmds,
			AllowedSubCmds: acl.AllowedSubCmds,
			KeyPatterns:    acl.KeyPatterns,
		})
	}

	if app.Type == x1model.AppTypeStandalone {
		for _, cluster := range app.Clusters {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if (processType != processTypeUpdateAcl && node.Status != x1model.NodeOrProxyStatusToCreate) ||
					(processType == processTypeUpdateAcl && node.Status == x1model.NodeOrProxyStatusToCreate) {
					continue
				}
				node := node
				g.Go(func() error {
					return setUserAclsForOneRedisNodeWithXagent(ctx, node, defaultAclList, aclListParam)
				})
			}
		}
	} else if app.Type == x1model.AppTypeCluster {
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				if (processType != processTypeUpdateAcl && proxy.Status != x1model.NodeOrProxyStatusToCreate) ||
					(processType == processTypeUpdateAcl && proxy.Status == x1model.NodeOrProxyStatusToCreate) {
					continue
				}
				proxy := proxy
				g.Go(func() error {
					return setUserAclsForOneProxyWithXagent(ctx, proxy, defaultAclList, aclListParam)
				})
			}
		}
	}

	_, err := g.Wait()
	return err
}

// 通过xagent更新一个节点的acl
func setUserAclsForOneRedisNodeWithXagent(ctx context.Context, node *x1model.Node,
	defaultAclList []*x1model.RedisAcl, aclListParam []*iface.RedisAcl) error {
	defaultAcl, err := tryGetEffectiveAcl(ctx, node.FloatingIP, node.Port, defaultAclList)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get effective acl fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))
		return err
	}

	params := &SetAclParams{
		Meta: &xagent.Meta{
			Engine:        node.Engine,
			EngineVersion: node.EngineVersion,
			Basedir:       node.Basedir,
			Port:          int32(node.Port),
		},
		AclList: aclListParam,
	}
	if defaultAcl != nil {
		params.Meta.Password = defaultAcl.Password
	}

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action:     setAclActionName,
		Params:     params,
		TimeoutSec: setAclTimeoutSec,
	}

	_, err = xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set acl fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))
	}

	return err
}

// 通过xagent更新一个proxy的acl
func setUserAclsForOneProxyWithXagent(ctx context.Context, node *x1model.Proxy,
	defaultAclList []*x1model.RedisAcl, aclListParam []*iface.RedisAcl) error {
	defaultAcl, err := tryGetEffectiveAcl(ctx, node.FloatingIP, node.Port, defaultAclList)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get effective acl fail",
			logit.String("ProxyId", node.ProxyId),
			logit.Error("err", err))
		return err
	}

	params := &SetAclParams{
		Meta: &xagent.Meta{
			Engine:        node.Engine,
			EngineVersion: node.EngineVersion,
			Basedir:       node.Basedir,
			Port:          int32(node.Port),
		},
		AclList: aclListParam,
	}
	if defaultAcl != nil {
		params.Meta.Password = defaultAcl.Password
	}

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action:     setAclActionName,
		Params:     params,
		TimeoutSec: setAclTimeoutSec,
	}

	_, err = xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set acl fail",
			logit.String("nodeId", node.ProxyId),
			logit.Error("err", err))
	}

	return err
}

// SetStandaloneACL 用于重启/升级过程中设置单个节点的acl
func SetStandaloneACL(ctx context.Context, app *x1model.Application, node *x1model.Node) error {
	defaultAclList, err := getDefaultUserAclList(ctx, app)
	if err != nil {
		return err
	}
	aclList, err := getAppAclList(ctx, app)
	if err != nil {
		return err
	}

	if len(aclList) == 0 {
		resource.LoggerTask.Notice(ctx, "no acl to set",
			logit.String("appId", app.AppId))
		return nil
	}

	aclListParam := make([]*iface.RedisAcl, 0)
	for _, acl := range aclList {
		if acl.Status == x1model.ACLStatusToDelete {
			continue
		}
		aclListParam = append(aclListParam, &iface.RedisAcl{
			AccountName:    acl.AccountName,
			Password:       acl.Password,
			AllowedCmds:    acl.AllowedCmds,
			AllowedSubCmds: acl.AllowedSubCmds,
			KeyPatterns:    acl.KeyPatterns,
		})
	}

	if err := setUserAclsForOneRedisNodeWithXagent(ctx, node, defaultAclList, aclListParam); err != nil {
		return err
	}

	return nil
}
