/*
modification history
--------------------
2022/01/12, by s<PERSON><PERSON><PERSON>(<EMAIL>), first version
*/

/*
DESCRIPTION
	设置ACL通用方法
*/

package acl

import (
	"context"
	"sort"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type aclListSlice []*x1model.RedisAcl

func (list aclListSlice) Len() int {
	return len(list)
}

func (list aclListSlice) Less(i, j int) bool {
	aclWeightMap := map[string]int{
		x1model.ACLStatusInUse:    4,
		x1model.ACLStatusToUpdate: 3,
		x1model.ACLStatusToDelete: 2,
		x1model.ACLStatusToCreate: 1,
	}
	return aclWeightMap[list[i].Status] < aclWeightMap[list[j].Status]
}

func (list aclListSlice) Swap(i, j int) {
	list[i], list[j] = list[j], list[i]
}

var _ sort.Interface = aclListSlice(nil)

// getDefaultUserAclList 获取指定accountName所有非删除状态的acl，并按照一定优先级排序
func getDefaultUserAclList(ctx context.Context, app *x1model.Application) ([]*x1model.RedisAcl, error) {
	var aclList []*x1model.RedisAcl
	var err error
	if app.Type == x1model.AppTypeStandalone {
		aclList, err = x1model.RedisAclAllByCond(ctx,
			"app_id = ? AND account_name = ? AND status <> ?",
			app.AppId, x1model.DefaultACLUser, x1model.ACLStatusDeleted)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get acl list fail",
				logit.String("appId", app.AppId),
				logit.Error("err", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
	} else {
		proxyAclList, err := x1model.ProxyAclGetAllByCond(ctx,
			"app_id = ? AND account_name = ? AND status <> ?",
			app.AppId, x1model.DefaultACLUser, x1model.ACLStatusDeleted)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get proxy acl list fail",
				logit.String("appId", app.AppId),
				logit.Error("err", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
		aclList = ProxyAclsToRedisAcls(proxyAclList)
	}

	sort.Sort(aclListSlice(aclList))

	return aclList, nil
}

// tryGetEffectiveAcl 通过ping test，在候选acl list里验证并找到实际生效的acl
func tryGetEffectiveAcl(ctx context.Context, host string, port any,
	aclList []*x1model.RedisAcl) (*x1model.RedisAcl, error) {
	aclList = append([]*x1model.RedisAcl{nil}, aclList...)

	for _, acl := range aclList {
		err := util.EffectiveAclTest(ctx, host, port, pingTimeoutSec, acl)
		if err != nil {
			if !util.IsAuthFail(err) {
				return nil, err
			}
			resource.LoggerTask.Trace(ctx, "this acl auth fail,continue to try others", logit.String("acl", base_utils.Format(acl)))
			continue
		}
		return acl, nil
	}

	return nil, cerrs.ErrNotFound
}

// 获取数据库中指定appId所有非删除状态的acl list
func getAppAclList(ctx context.Context, app *x1model.Application) ([]*x1model.RedisAcl, error) {
	var aclList []*x1model.RedisAcl
	var err error
	if app.Type == x1model.AppTypeStandalone {
		aclList, err = x1model.RedisAclAllByCond(ctx, "app_id = ? AND status <> ?",
			app.AppId, x1model.ACLStatusDeleted)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get acl fail",
				logit.String("appId", app.AppId),
				logit.Error("err", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
	} else {
		proxyAclList, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND status <> ?",
			app.AppId, x1model.ACLStatusDeleted)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get proxy acl fail",
				logit.String("appId", app.AppId),
				logit.Error("err", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
		aclList = ProxyAclsToRedisAcls(proxyAclList)
	}
	return aclList, nil
}

// 获取生效后待更新acl list (同时也会在某些情况下修改原始的aclList节点的状态 todelete->deleted tocreate->inuse)
func updateAclListAfterTakingEffect(aclList []*x1model.RedisAcl) []*x1model.RedisAcl {
	var updatedAclList []*x1model.RedisAcl

	for _, acl := range aclList {
		if acl.Status == x1model.ACLStatusToCreate ||
			acl.Status == x1model.ACLStatusToUpdate {
			acl.Status = x1model.ACLStatusInUse
			updatedAclList = append(updatedAclList, acl)
		} else if acl.Status == x1model.ACLStatusToDelete {
			acl.Status = x1model.ACLStatusDeleted
			updatedAclList = append(updatedAclList, acl)
		}
	}

	return updatedAclList
}

// updateCsmasterAclStatus 更新csmaster acl status
func updateCsmasterAclStatus(ctx context.Context, app *x1model.Application, aclList []*x1model.RedisAcl) error {
	if !isUserAclSupported(app) {
		return nil
	}

	aclStatusList := make([]*csmaster.AclStatus, 0)
	for _, acl := range aclList {
		aclStatus := &csmaster.AclStatus{
			UserName: acl.AccountName,
			Status:   acl.Status,
		}
		aclStatusList = append(aclStatusList, aclStatus)
	}

	req := &csmaster.UpdateAclParams{
		UserID:        app.UserId,
		AppID:         app.AppId,
		AclStatusList: aclStatusList,
	}

	if err := csmaster.CsmasterOp().UpdateAclStatus(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update csmaster acl status",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UpdateCsmasterAclStatusFail.Wrap(err)
	}

	return nil
}

// createCsmasterDefaultAcl csmaster创建默认acl
func createCsmasterDefaultAcl(ctx context.Context, app *x1model.Application) error {
	if !isUserAclSupported(app) {
		resource.LoggerTask.Notice(ctx, "csmaster acl not supported, skip")
		return nil
	}

	// csmaster内部会读取默认password，不用传相关参数
	req := &csmaster.CsmasterAclActionsParams{
		UserID:    app.UserId,
		AppID:     app.AppId,
		AclAction: csmaster.CsmasterAclActionCreateDefaultUser,
		UserName:  x1model.DefaultACLUser,
	}

	if err := csmaster.CsmasterOp().CsmasterAclActions(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to create default acl for csmaster",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UpdateCsmasterAclStatusFail.Wrap(err)
	}

	return nil
}

func isUserAclSupported(app *x1model.Application) bool {
	if len(app.Clusters) == 0 {
		panic("no cluster for app: " + app.AppId)
	}
	if app.Type == x1model.AppTypeCluster {
		return true
	}
	return base_utils.CompareVersion(app.Clusters[0].EngineVersion, "6.0") >= 0
}

func ProxyAclsToRedisAcls(pacls []*x1model.ProxyAcl) []*x1model.RedisAcl {
	if pacls == nil {
		return nil
	}
	var r []*x1model.RedisAcl
	for _, pacl := range pacls {
		r = append(r, &x1model.RedisAcl{
			Id:             pacl.Id,
			AppID:          pacl.AppID,
			AccountName:    pacl.AccountName,
			CreateAt:       pacl.CreateAt,
			UpdateAt:       pacl.UpdateAt,
			Version:        pacl.Version,
			Engine:         pacl.Engine,
			Password:       pacl.Password,
			AllowedCmds:    pacl.AllowedCmds,
			AllowedSubCmds: pacl.AllowedSubCmds,
			KeyPatterns:    pacl.KeyPatterns,
			Properties:     pacl.Properties,
			Status:         pacl.Status,
		})
	}
	return r
}

func RedisAclsToProxyAcls(racls []*x1model.RedisAcl) []*x1model.ProxyAcl {
	if racls == nil {
		return nil
	}
	var r []*x1model.ProxyAcl
	for _, racl := range racls {
		r = append(r, &x1model.ProxyAcl{
			Id:             racl.Id,
			AppID:          racl.AppID,
			AccountName:    racl.AccountName,
			CreateAt:       racl.CreateAt,
			UpdateAt:       racl.UpdateAt,
			Version:        racl.Version,
			Engine:         racl.Engine,
			Password:       racl.Password,
			AllowedCmds:    racl.AllowedCmds,
			AllowedSubCmds: racl.AllowedSubCmds,
			KeyPatterns:    racl.KeyPatterns,
			Properties:     racl.Properties,
			Status:         racl.Status,
		})
	}
	return r
}
