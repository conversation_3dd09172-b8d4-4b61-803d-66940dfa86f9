/**
* @Copyright 2022 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2022/7/14 16:45
**/

package xmaster

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// imports

// const

// typedefs

// vars

// functions

func ProcessUpdateAppTopologyInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app has not init in xmaster, skip update",
			logit.String("appId", app.AppId))
		return nil
	}

	if err := util.UpdateXMasterApplicationTopology(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "update xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if err := util.SetXMasterDefaultMonitorStrategy(ctx, app.AppId, false); err != nil {
		resource.LoggerTask.Error(ctx, "set xmaster app monitor strategy failed", logit.Error("error", err))
		return err
	}

	// 这里主要是设置新增分片的开关，xmaster有分片粒度的开关，所以需要设置；此时csmaster开关已经设置过，所以不需要处理csmaster。
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.Status == csmaster_model_interface.CACHE_CLUSTER_DELETED ||
		clusterModel.Status == csmaster_model_interface.CACHE_CLUSTER_DELETING {
		resource.LoggerTask.Notice(ctx, "app has been deleted, skip update",
			logit.String("appId", app.AppId))
		return nil
	}

	// 设置xmaster高可用开关
	enable := true
	if clusterModel.NotEnableReplace == 1 {
		enable = false
	}
	if err := util.SetXMasterMonitorSwitch(ctx, app.AppId, enable); err != nil {
		resource.LoggerTask.Error(ctx, "set xmaster app monitor switch failed", logit.Error("error", err))
		return err
	}

	// 设置fake开关，若cache_cluster.use_xmaster为1，表示使用xmaster高可用，设置对应开关；
	if clusterModel.UseXmaster == csmaster.CsmasterSetHAServicerUseCsmaster {
		resource.LoggerTask.Notice(ctx, "Default HA servicer is csmaster and target HA server is csmaster,"+
			" so there is nothing to do",
			logit.Int("use_xmaster", clusterModel.UseXmaster))
	} else {
		resource.LoggerTask.Notice(ctx, "Default HA servicer is csmaster and target HA server is xmaster,"+
			" so we should enable xmaster and disable csmaster",
			logit.Int("use_xmaster", clusterModel.UseXmaster))

		// set task fake flag to false，means enable ha in xmaster
		if err := util.SetXMasterTaskFakeSwitch(ctx, teu.Entity, false); err != nil {
			resource.LoggerTask.Error(ctx, "switch on xmaster task fake switch failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessUpdateAppTopologyInXmasterForShardScaleIn(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app has not init in xmaster, skip update",
			logit.String("appId", app.AppId))
		return nil
	}

	if err := util.UpdateXMasterApplicationTopologyForShardScaleIn(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "update xmaster app topology for shard scale in failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessBlockAppMonitorInXmasterPingAvailable(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app is not registered in xmaster, skip",
			logit.String("appId", app.AppId))
		return nil
	}

	monsToBlock := []string{
		util.AppMonitorNamePingAvailable,
	}
	if err := util.BlockAppMonitorInXmaster(ctx, app.AppId, monsToBlock); err != nil {
		resource.LoggerTask.Error(ctx, "block app monitor in xmaster failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessBlockAppMonitorInXmasterTopologySlave(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app is not registered in xmaster, skip",
			logit.String("appId", app.AppId))
		return nil
	}

	monsToBlock := []string{
		util.AppMonitorNameTopologySlave,
	}
	if err := util.BlockAppMonitorInXmaster(ctx, app.AppId, monsToBlock); err != nil {
		resource.LoggerTask.Error(ctx, "block app monitor in xmaster failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessUnblockAppMonitorInXmasterAll(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	queryRsp, err := util.QueryClusterTopoFromXmaster(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "query xmaster app topology failed", logit.Error("error", err))
		return err
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Notice(ctx, "app is not registered in xmaster, skip",
			logit.String("appId", app.AppId))
		return nil
	}

	if err := util.UnblockAppMonitorInXmaster(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "unblock app monitor in xmaster failed", logit.Error("error", err))
		return err
	}

	return nil
}
