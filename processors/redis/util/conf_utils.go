package util

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

var PegaDBUserConfName = []string{"cluster-id", "metaserverhost", "metaserverport", "use-rsid-psync", "slowlog-log-slower-than",
	"use-op-header", "compaction-checker-range", "migrate-batch-rate-limit-mb", "disable_commands_pega"}

type ConfigItem struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type GetUpdateConfigParams struct {
	Engine             string                                     `json:"engine"`
	EngineVersion      string                                     `json:"engine_version"`
	Role               string                                     `json:"role"`
	ConfDefs           []*csmaster_model_interface.UserConfList   `json:"conf_defs"`
	ConfRecords        []*csmaster_model_interface.ConfRecordList `json:"conf_records"`
	UpdateConfigParams []*iface.ConfigItem                        `json:"update_config_params"`
	NeedFilter         bool                                       `json:"need_filter"`
}

func getConfDef(key string, params *GetUpdateConfigParams) *csmaster_model_interface.UserConfList {
	confModule := 1 // redis
	if params.Engine == x1model.EngineBDRPProxy {
		confModule = 2
	}
	for _, confDef := range params.ConfDefs {
		if confDef.ConfName == key && (confModule == confDef.ConfModule || confDef.ConfModule == 3) {
			// 如果配置定义不符合当前实例版本，则跳过
			if params.Engine == x1model.EngineRedis {
				if confDef.ConfRedisVersion != "all" && confDef.ConfRedisVersion != params.EngineVersion {
					continue
				}
				if confDef.Engine != "" && confDef.Engine != params.Engine {
					continue
				}
			}
			if params.Engine == x1model.EnginePegaDB {
				if confDef.Engine != x1model.EnginePegaDB {
					if in, _ := base_utils.InArray(confDef.ConfName, PegaDBUserConfName); !in {
						continue
					}
				}
			}
			// proxy配置暂不校验版本
			return confDef
		}
	}
	return nil
}

func GetUpdateConfigList(ctx context.Context, params *GetUpdateConfigParams) ([]*ConfigItem, error) {
	var configList []*ConfigItem
	for _, userRecord := range params.ConfRecords {
		if !isUserRecordModuleMatchEngine(userRecord.ConfModule, params.Engine, userRecord.ConfName) {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("[GetUpdateConfigList] conf %s module %d not match engine %s, skip",
				userRecord.ConfName, userRecord.ConfModule, params.Engine))
			continue
		}
		if userRecord.ConfName == "disable_commands_pega" && params.Engine != x1model.EnginePegaDB {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("[GetUpdateConfigList] conf %s is pega conf, skip",
				userRecord.ConfName), logit.String("intput engine", params.Engine))
			continue
		}
		confDef := getConfDef(userRecord.ConfName, params)
		if confDef == nil {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("[GetUpdateConfigList] get confDef of %s for engine %s fail",
				userRecord.ConfName, params.Engine))
			continue
		}
		if !isStandardConf(userRecord.ConfName) {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("[GetUpdateConfigList] conf %s is not standard, skip",
				userRecord.ConfName))
			continue
		}
		if !inParams(userRecord.ConfName, params) && params.NeedFilter {
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("[GetUpdateConfigList] conf %s is not in params, skip",
				userRecord.ConfName))
			continue
		}
		transConf := transConf(userRecord.ConfName, userRecord.Value, params.Engine, params.Role, confDef)
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("[GetUpdateConfigList] trans conf %s to %s",
			userRecord.ConfName, base_utils.Format(transConf)))
		if len(transConf) > 0 {
			configList = append(configList, transConf...)
		}
	}
	return configList, nil
}

func isUserRecordModuleMatchEngine(confModule int, engine string, confName string) bool {
	if confModule == 3 {
		return true
	}
	if (engine == x1model.EngineRedis || engine == x1model.EnginePegaDB) && confModule == 1 {
		return true
	}
	if engine == x1model.EngineBDRPProxy && confModule == 2 {
		return true
	}
	return false
}

func inParams(confName string, params *GetUpdateConfigParams) bool {
	if len(params.UpdateConfigParams) == 0 {
		return false
	}
	for _, p := range params.UpdateConfigParams {
		if p.Name == confName {
			return true
		}
	}
	return false
}

func isStandardConf(confName string) bool {
	nonStandardConfs := []string{
		"disable_commands",
		"support_multi_active",
		"user_filter_cmds",
		"support_second_exec",
		"proxy_mbuf_size",
		"g_shard_id_as_shard_id",
		"load-modules",
		"consistency_check_allow_run",
		"consistency_check_keep_files",
	}
	for _, nonStandardConf := range nonStandardConfs {
		if confName == nonStandardConf {
			return false
		}
	}
	return true
}

func transConf(confName, confVal, engine, role string, confDef *csmaster_model_interface.UserConfList) []*ConfigItem {
	switch confName {
	case "appendonly":
		if engine == x1model.EngineRedis {
			if confVal == "partial" {
				if role == "master" {
					return []*ConfigItem{
						{
							Name:  "appendonly",
							Value: "no",
						},
					}
				}
				return []*ConfigItem{
					{
						Name:  "appendonly",
						Value: "yes",
					},
				}
			}
			return []*ConfigItem{
				{
					Name:  "appendonly",
					Value: confVal,
				},
			}
		}
		return []*ConfigItem{}
	case "hashtag_enable":
		if engine == x1model.EngineBDRPProxy {
			if confVal == "yes" {
				return []*ConfigItem{
					{
						Name:  "hash_tag",
						Value: "\"{}\"",
					},
				}
			}
			return []*ConfigItem{
				{
					Name:  "hash_tag",
					Value: "",
				},
			}
		} else if engine == x1model.EngineRedis {
			return []*ConfigItem{
				{
					Name:  "use-hash-tag",
					Value: confVal,
				},
			}
		}
		return []*ConfigItem{}
	case "select_cmd_enable":
		if engine == x1model.EngineBDRPProxy {
			if confVal == "yes" {
				return []*ConfigItem{
					{
						Name:  "support_multi_db",
						Value: "true",
					},
					{
						Name:  "server_connections",
						Value: "16",
					},
					{
						Name:  "need_reserved_conn",
						Value: "true",
					},
				}
			}
			return []*ConfigItem{
				{
					Name:  "support_multi_db",
					Value: "false",
				},
				{
					Name:  "server_connections",
					Value: "1",
				},
				{
					Name:  "db_count",
					Value: "1",
				},
				{
					Name:  "need_reserved_conn",
					Value: "false",
				},
			}
		}
		return []*ConfigItem{}
	case "db-slot-check":
		if engine == x1model.EngineRedis {
			return []*ConfigItem{
				{
					Name:  "db-slot-check",
					Value: confVal,
				},
			}
		}
		return []*ConfigItem{}
	case "disable_commands_pega":
		if engine == x1model.EnginePegaDB {
			return []*ConfigItem{
				{
					Name:  "disable_commands_pega",
					Value: fmt.Sprintf("%s|%s", confVal, strings.ReplaceAll(confDef.ConfRange, "|", ",")),
				},
			}
		}
		return []*ConfigItem{}
	default:
		if engine == x1model.EngineBDRPProxy {
			confName = strings.ReplaceAll(confName, "scsproxy_", "")
		}
		return []*ConfigItem{
			{
				Name:  confName,
				Value: confVal,
			},
		}
	}
}
