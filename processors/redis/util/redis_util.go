package util

import (
	"context"
	"errors"
	"fmt"
	"io"
	"regexp"
	"strconv"
	"strings"
	"time"

	r "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	pingDelay             = 1 * time.Second
	RoleMaster            = "master"
	RedisCmdCertification = "fbbef3155aa64306ba4df82721fc039a"
)

type ReplicationInfoSlave struct {
	IP     string `json:"ip"`
	Port   int    `json:"port"`
	State  string `json:"state"`
	Offset int64  `json:"offset"`
	Lag    int64  `json:"lag"`
}

type ReplicationInfo struct {
	Role                       string                  `json:"role"`
	ConnectedSlaves            int                     `json:"connected_slaves"`
	Slaves                     []*ReplicationInfoSlave `json:"slave"`
	MasterReplOffset           int64                   `json:"master_repl_offset"`
	SlaveReplOffset            int64                   `json:"slave_repl_offset"`
	ReplBacklogActive          bool                    `json:"repl_backlog_active"`
	ReplBacklogSize            int64                   `json:"repl_backlog_size"`
	ReplBacklogFirstByteOffset int64                   `json:"repl_backlog_first_byte_offset"`
	ReplBacklogHistLen         int64                   `json:"repl_backlog_histlen"`
	MasterHost                 string                  `json:"master_host"`
	MasterPort                 int                     `json:"master_port"`
	MasterLinkStatus           string                  `json:"master_link_status"`
}

type KeySpaceDBInfo struct {
	DBName  string `json:"dbname"`
	Keys    int    `json:"keys"`
	Expires int    `json:"expires"`
	AvgTTL  int    `json:"avg_ttl"`
}

type KeyspaceInfo struct {
	Db []*KeySpaceDBInfo `json:"db"`
}

type PegaRocksDBInfo struct {
	NumBackgroundErrors  int    `json:"num_background_errors"`
	InBackgroundError    bool   `json:"in_background_error"`
	Backups              int    `json:"backups"`
	IsCaculatingChecksum int    `json:"is_caculating_checksum"`
	ChecksumOffset       int    `json:"checksumoffset"`
	Checksum             string `json:"checksum"`
	LastBgsaveTime       int    `json:"last_bgsave_time"`
}

type RedisCmdParams struct {
	Host          string   `json:"host,omitempty"`
	Port          int32    `json:"port,omitempty"`
	Password      string   `json:"password,omitempty"`
	Timeout       int32    `json:"timeout,omitempty"`
	Certification string   `json:"certification,omitempty"`
	Cmds          []string `json:"cmds,omitempty"`
}

// PingTest 检查redis能否ping成功
// 注意，开启了sentinel的用户，ping会免密
func PingTest(ctx context.Context, host string, port any, timeoutSec int, acl *x1model.RedisAcl) error {
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithRetry(0),
	)
	defer c.Close()

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeoutSec)*time.Second)
	defer cancel()

	for {
		result, err := c.Ping(ctx).Result()
		if err != nil {
			if !shouldRetry(err) {
				return errConvert(err)
			}
		} else if strings.EqualFold(result, "PONG") {
			break
		}

		select {
		case <-ctx.Done():
			return errConvert(ctx.Err())
		case <-time.After(pingDelay):
			break
		}
	}

	return nil
}

// EffectiveAclTest 检查哪个acl是正确的密码
func EffectiveAclTest(ctx context.Context, host string, port any, timeoutSec int, acl *x1model.RedisAcl) error {
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithRetry(0),
	)
	defer c.Close()

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeoutSec)*time.Second)
	defer cancel()
	getTestKey := fmt.Sprintf("SCS_TRYEFFECTIVE_ACL_GET_TEST_KEY:%d", time.Now().UnixMicro())
	resource.LoggerTask.Trace(ctx, "build test key for acl auth test", logit.String("key", getTestKey),
		logit.String("pwd", password),
		logit.String("acl", base_utils.Format(acl)))
	for {
		_, err := c.Get(ctx, getTestKey).Result()
		if err != nil {
			// GET命令的文档：
			// Get Redis `GET key` command. It returns redis.Nil error when key does not exist.
			// 所以这里判断如果返回的err是r.Nil，就认为成功了
			if errors.Is(err, r.Nil) {
				resource.LoggerTask.Trace(ctx, "redis response it not has this key,auth test pass",
					logit.String("pwd", password),
					logit.String("acl", base_utils.Format(acl)))
				break
			}
			if !shouldRetry(err) {
				return errConvert(err)
			}
		} else {
			// 这里其实概率极低，即正好用户用户集群里有一个名字叫 "SCS_TRYEFFECTIVE_ACL_GET_TEST_KEY:$当前UNIX时间戳" 的key
			resource.LoggerTask.Trace(ctx, "wow,just has this key,auth test pass",
				logit.String("pwd", password),
				logit.String("acl", base_utils.Format(acl)))
			break
		}

		select {
		case <-ctx.Done():
			return errConvert(ctx.Err())
		case <-time.After(pingDelay):
			break
		}
	}

	return nil
}

func RedisAlivePing(ctx context.Context, host string, port any) (bool, error) {
	c := single_redis.NewClient(host, port,
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 100 * time.Millisecond,
			Read:    200 * time.Millisecond,
			Write:   200 * time.Millisecond,
		}),
		single_redis.WithRetry(2),
	)
	_, err := c.Ping(ctx).Result()
	defer c.Close()
	if err != nil && !isAliveError(err) && !IsAuthFail(err) {
		resource.LoggerTask.Trace(ctx, "redis ping fail", logit.Error("error", err))
		return false, err
	}
	return true, err
}

func isAliveError(err error) bool {
	s := err.Error()
	if s == "ERR max number of clients reached" {
		return true
	}
	if strings.HasPrefix(s, "LOADING ") {
		return true
	}
	if strings.HasPrefix(s, "READONLY ") {
		return true
	}
	if strings.HasPrefix(s, "CLUSTERDOWN ") {
		return true
	}
	if strings.HasPrefix(s, "TRYAGAIN ") {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("ERR restoring the db from backup")) {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("ERR wrong number of arguments")) {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("Redis is loading the dataset in memory")) {
		return true
	}
	return false
}

func errConvert(err error) error {
	if cerrs.Is(err, context.Canceled) {
		return cerrs.ErrCanceled.Wrap(err)
	} else if cerrs.Is(err, context.DeadlineExceeded) {
		return cerrs.ErrTimeout.Wrap(err)
	}
	return cerrs.ErrRedisCallFail.Wrap(err)
}

// 与gdp-redis基本一致
func shouldRetry(err error) bool {
	if err == nil ||
		cerrs.Is(err, context.Canceled) ||
		cerrs.Is(err, context.DeadlineExceeded) {
		return false
	}

	if sdk_utils.IsConnectFail(err) {
		return true
	}

	if cerrs.Is(err, io.EOF) ||
		cerrs.Is(err, io.ErrUnexpectedEOF) ||
		cerrs.Is(err, r.ErrClosed) {
		return true
	}

	if _, ok := err.(interface{ Timeout() bool }); ok {
		return true
	}

	s := err.Error()
	if s == "ERR max number of clients reached" {
		return true
	}
	if strings.HasPrefix(s, "LOADING ") {
		return true
	}
	if strings.HasPrefix(s, "READONLY ") {
		return true
	}
	if strings.HasPrefix(s, "CLUSTERDOWN ") {
		return true
	}
	if strings.HasPrefix(s, "TRYAGAIN ") {
		return true
	}
	if strings.Contains(s, "timeout") {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("ERR restoring the db from backup")) {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("ERR wrong number of arguments")) {
		return true
	}
	if strings.Contains(strings.ToUpper(s), strings.ToUpper("Redis is loading the dataset in memory")) {
		return true
	}

	return false
}

// -ERR invalid password
// -NOAUTH Authentication required
// -ERR Client sent AUTH, but no password
// -ERR handle request AUTH failed
// -WRONGPASS invalid username-password pair
var regexpAuthFail = regexp.MustCompile(`(?i)auth|password`)

func IsAuthFail(err error) bool {
	return regexpAuthFail.MatchString(err.Error())
}

func parseReplicationInfoRaw(ctx context.Context, raw string) (*ReplicationInfo, error) {
	var err error
	ret := &ReplicationInfo{}
	raw = strings.ReplaceAll(raw, "\r\n", "\n")
	raw = strings.ReplaceAll(raw, "\r", "")
	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}
		// resource.LoggerTask.Notice(ctx, "REPLICATION LINE", logit.String("line", base_utils.Format(line)))
		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		if strings.Contains(line, ",state") {
			slave := &ReplicationInfoSlave{}
			for _, item := range strings.Split(kv[1], ",") {
				itemKv := strings.Split(item, "=")
				if len(itemKv) < 2 {
					return nil, fmt.Errorf("invalid format itemKv %s", line)
				}
				switch itemKv[0] {
				case "ip":
					slave.IP = itemKv[1]
				case "port":
					if slave.Port, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				case "state":
					slave.State = itemKv[1]
				case "offset":
					if slave.Offset, err = strconv.ParseInt(itemKv[1], 10, 64); err != nil {
						return nil, err
					}
				case "lag":
					if slave.Lag, err = strconv.ParseInt(itemKv[1], 10, 64); err != nil {
						return nil, err
					}
				}
			}
			ret.Slaves = append(ret.Slaves, slave)
		} else {
			switch kv[0] {
			case "role":
				ret.Role = kv[1]
			case "connected_slaves":
				if ret.ConnectedSlaves, err = strconv.Atoi(kv[1]); err != nil {
					return nil, err
				}
			case "master_repl_offset":
				if ret.MasterReplOffset, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "slave_repl_offset":
				if ret.SlaveReplOffset, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "repl_backlog_active":
				replBacklogActive, err := strconv.Atoi(kv[1])
				if err != nil {
					return nil, err
				}
				ret.ReplBacklogActive = replBacklogActive == 1
			case "repl_backlog_size":
				if ret.ReplBacklogSize, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "repl_backlog_first_byte_offset":
				if ret.ReplBacklogFirstByteOffset, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "repl_backlog_histlen":
				if ret.ReplBacklogHistLen, err = strconv.ParseInt(kv[1], 10, 64); err != nil {
					return nil, err
				}
			case "master_host":
				ret.MasterHost = kv[1]
			case "master_port":
				if ret.MasterPort, err = strconv.Atoi(kv[1]); err != nil {
					return nil, err
				}
			case "master_link_status":
				ret.MasterLinkStatus = kv[1]
			}
		}
	}
	return ret, nil
}

// GetReplicationInfo 向redis发送replication info命令，获取相关结果
func GetReplicationInfo(ctx context.Context, host string, port any, password string) (*ReplicationInfo, error) {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 500 * time.Millisecond,
			Read:    1000 * time.Millisecond,
			Write:   1000 * time.Millisecond,
		}),
	)
	defer c.Close()

	raw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		return nil, err
	}
	return parseReplicationInfoRaw(ctx, raw)
}

// SetSlaveOf 向redis发送slave of， 如果masterHost == "no" masterPort == "one", 发送slaveof on one
func SetSlaveOf(ctx context.Context, host string, port any, password string, masterHost string, masterPort string) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	return SlaveOfRenamed(ctx, c, masterHost, masterPort)
}

func formatMs(ctx context.Context, dur time.Duration) int64 {
	if dur > 0 && dur < time.Millisecond {
		return 1
	}
	return int64(dur / time.Millisecond)
}

// SetClientPause
func SetClientPause(ctx context.Context, host string, port any, password string, dur time.Duration) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 500 * time.Millisecond,
			Read:    1000 * time.Millisecond,
			Write:   1000 * time.Millisecond,
		}),
	)
	defer c.Close()

	if err := c.ClientPause(ctx, dur).Err(); err != nil {
		resource.LoggerTask.Trace(ctx, "set client pause failed", logit.Error("error", err))
		return c.Do(ctx, "aae420ac56ef116058218c11d8b35b30CLIENT", "PAUSE", formatMs(ctx, dur)).Err()
	}
	return nil
}

func SetClientPauseWithMode(ctx context.Context, host string, port any, password string, dur time.Duration, mode string) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 500 * time.Millisecond,
			Read:    1000 * time.Millisecond,
			Write:   1000 * time.Millisecond,
		}),
	)
	defer c.Close()

	if mode != "ALL" && mode != "WRITE" {
		return errors.New("mode must be ALL or WRITE")
	}

	if err := c.Do(ctx, "CLIENT", "PAUSE", formatMs(ctx, dur), mode).Err(); err != nil {
		resource.LoggerTask.Trace(ctx, "set client pause failed", logit.Error("error", err))
		return c.Do(ctx, "aae420ac56ef116058218c11d8b35b30CLIENT", "PAUSE", formatMs(ctx, dur), mode).Err()
	}
	return nil
}

func ClientKillAll(ctx context.Context, host string, port any, password string) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()
	return c.ClientKillByFilter(ctx, "type", "normal").Err()
}

func GetRedisRole(ctx context.Context, host string, port any, password string) (role string, err error) {
	replicationInfo, err := GetReplicationInfo(ctx, host, port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Get ReplicationInfo Fail", logit.String("host", host))
		return "", err
	}
	role = replicationInfo.Role
	return
}

// IsMaster
// 对Redis发送info命令并解析replication段内容判断是否为master节点
func IsMaster(ctx context.Context, host string, port any, password string) (isMaster bool, err error) {
	role, err := GetRedisRole(ctx, host, port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Get RedisRole Fail", logit.String("host", host))
		return false, err
	}
	isMaster = role == RoleMaster
	return
}

func GetMasterIP(cluster *x1model.Cluster, node *x1model.Node, hybrid bool) string {
	if node.Role == x1model.RoleTypeMaster {
		return ""
	}
	var realMaster *x1model.Node
	var toCreateMaster *x1model.Node
	for _, n := range cluster.Nodes {
		if n.Role == x1model.RoleTypeMaster {
			if x1model.NodeOrProxyStatusToCreate == n.Status {
				toCreateMaster = n
			} else {
				realMaster = n
			}
		}
	}
	if realMaster == nil {
		realMaster = toCreateMaster
	}
	if toCreateMaster != nil && hybrid && node.Status == x1model.NodeOrProxyStatusToCreate {
		return toCreateMaster.Ip
	}
	if realMaster != nil {
		return realMaster.Ip
	}
	return ""
}

// SetSlaveOfOnOneIdempotent 幂等版本SlaveOfNoOne
func SetSlaveOfOnOneIdempotent(ctx context.Context, host string, port any, password string) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()
	raw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		return err
	}
	replicationInfo, err := parseReplicationInfoRaw(ctx, raw)
	if err != nil {
		return err
	}
	if replicationInfo.Role == RoleMaster {
		return nil
	}

	return SlaveOfRenamed(ctx, c, "no", "one")
}

// SetSlaveOfMasterIdempotent 幂等版本SlaveOfMaster
func SetSlaveOfMasterIdempotent(ctx context.Context, host string, port any, password string, masterHost string, masterPort string) error {
	if masterHost == "no" && masterPort == "one" {
		return SetSlaveOfOnOneIdempotent(ctx, host, port, password)
	}

	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	raw, err := c.Info(ctx, "replication").Result()
	if err != nil {
		return err
	}
	replicationInfo, err := parseReplicationInfoRaw(ctx, raw)
	if err != nil {
		return err
	}
	if replicationInfo.Role != RoleMaster && replicationInfo.MasterHost == masterHost && cast.ToString(replicationInfo.MasterPort) == masterPort {
		return nil
	}
	return SlaveOfRenamed(ctx, c, masterHost, masterPort)
}

func parseKeyspaceInfoRaw(ctx context.Context, raw string) (*KeyspaceInfo, error) {
	var err error
	ret := &KeyspaceInfo{}
	raw = strings.ReplaceAll(raw, "\r\n", "\n")
	raw = strings.ReplaceAll(raw, "\r", "")
	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}

		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		// eg. db0:keys=2,expires=0,avg_ttl=0
		if strings.Contains(line, ",expires") {
			dbInfo := &KeySpaceDBInfo{}
			dbInfo.DBName = kv[0]
			for _, item := range strings.Split(kv[1], ",") {
				itemKv := strings.Split(item, "=")
				if len(itemKv) < 2 {
					return nil, fmt.Errorf("invalid format itemKv %s", line)
				}
				switch itemKv[0] {
				case "keys":
					if dbInfo.Keys, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				case "expires":
					if dbInfo.Expires, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				case "avg_ttl":
					if dbInfo.AvgTTL, err = strconv.Atoi(itemKv[1]); err != nil {
						return nil, err
					}
				}
			}
			ret.Db = append(ret.Db, dbInfo)
		}
	}
	return ret, nil
}

func GetKeyspaceInfo(ctx context.Context, host string, port any, password string) (*KeyspaceInfo, error) {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	raw, err := c.Info(ctx, "keyspace").Result()
	if err != nil {
		return nil, err
	}
	return parseKeyspaceInfoRaw(ctx, raw)
}

func parsePegaRocksDBInfo(ctx context.Context, raw string) (*PegaRocksDBInfo, error) {
	var err error
	ret := &PegaRocksDBInfo{
		Backups:              -1,
		IsCaculatingChecksum: -1,
	}
	raw = strings.ReplaceAll(raw, "\r\n", "\n")
	raw = strings.ReplaceAll(raw, "\r", "")
	for _, line := range strings.Split(raw, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}

		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		switch kv[0] {
		case "num_background_errors":
			if ret.NumBackgroundErrors, err = strconv.Atoi(kv[1]); err != nil {
				return nil, err
			}
		case "in_background_error":
			ret.InBackgroundError = strings.Contains(kv[1], "yes")
		case "backups":
			if ret.Backups, err = strconv.Atoi(kv[1]); err != nil {
				return nil, err
			}
		case "is_caculating_checksum":
			if ret.IsCaculatingChecksum, err = strconv.Atoi(kv[1]); err != nil {
				return nil, err
			}
		case "checksumoffset":
			if ret.ChecksumOffset, err = strconv.Atoi(kv[1]); err != nil {
				return nil, err
			}
		case "checksum":
			ret.Checksum = kv[1]
		case "last_bgsave_time":
			if ret.LastBgsaveTime, err = strconv.Atoi(kv[1]); err != nil {
				return nil, err
			}
		}
	}
	return ret, nil
}

func GetPegaRocksDBInfo(ctx context.Context, host string, port any, password string) (*PegaRocksDBInfo, error) {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	raw, err := c.Info(ctx, "RocksDB").Result()
	if err != nil {
		return nil, err
	}
	return parsePegaRocksDBInfo(ctx, raw)
}

func PegaConsistencyBgsave(ctx context.Context, host string, port any, password string) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	// 这里的命令是为了兼容kvrocks
	if err := c.Do(ctx, "consistentbgsave", "caculate_checksum").Err(); err != nil {
		resource.LoggerTask.Warning(ctx, "PegaConsistencyBgsave failed", logit.Error("error", err))
		return err
	}
	return nil
}

// TryCorrectSlaveReplication, 尝试检查从节点的真实角色并挂载到主节点；若目标cluster中不存在主节点，不做操作并返回nil；
// 切换之后，强制触发一个自愈任务来保证旧主得到处理；自愈任务中，该函数返回nil表示可以跳过自愈，返回错误表示需要自愈；
func TryCorrectSlaveReplication(ctx context.Context, cluster *x1model.Cluster, node *x1model.Node, password string) error {
	// 仅支持检查并修复从节点
	if node.Role != "slave" {
		return nil
	}

	// 获取目标主节点
	var master *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
			master = node
		}
	}
	if master == nil {
		// 1. 元数据异常: 找不到主节点，无法修复
		// 2. 热活从地域: 直接替换
		resource.LoggerTask.Error(ctx, "no master node found", logit.String("cluster_id", cluster.ClusterId))
		return cerrs.ErrNotFound.Errorf("no master node found for node %s", node.NodeId)
	}

	nodeReplInfo, err := GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
	if err != nil {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("get replicationInfo from node %s failed",
			node.NodeId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("get replicationInfo from node %s succ",
		node.NodeId), logit.String("replication_info", base_utils.Format(nodeReplInfo)))

	if nodeReplInfo.Role == "slave" && nodeReplInfo.MasterHost == master.Ip && nodeReplInfo.MasterPort == master.Port {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("node %s is already slave of master %s",
			node.NodeId, master.NodeId))
		return nil
	}
	// TODO: 增加异常从节点master_replid的检查，确认该异常从节点和当前主库属于同一个复制组，才搭建复制关系；否则直接替换，以保留该节点数据。

	if err := SetSlaveOf(ctx, node.FloatingIP, node.Port, password, master.Ip, strconv.Itoa(master.Port)); err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("send slave of master %s to node %s failed",
			master.NodeId, node.NodeId), logit.Error("error", err))
		return err
	}

	masterReplInfo, err := GetReplicationInfo(ctx, master.FloatingIP, master.Port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("get replicationInfo from master %s failed",
			master.NodeId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("get replicationInfo from master %s succ",
		master.NodeId), logit.String("replication_info", base_utils.Format(masterReplInfo)))

	var targetSlave *ReplicationInfoSlave
	for _, slave := range masterReplInfo.Slaves {
		if slave.IP == node.Ip && slave.Port == node.Port {
			targetSlave = slave
			break
		}
	}
	if targetSlave == nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("node %s not found in replica list %s",
			node.NodeId, base_utils.Format(masterReplInfo.Slaves)),
			logit.String("replication_info", base_utils.Format(masterReplInfo)))
		return cerrs.ErrNotFound.Errorf("node %s not in replica list", node.NodeId)
	}
	return nil
}

func CheckSlaveReplication(ctx context.Context, cluster *x1model.Cluster, node *x1model.Node, password string) error {
	// 仅支持检查并修复从节点
	if node.Role != "slave" {
		return nil
	}

	// 获取目标主节点
	var master *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusInUse {
			master = node
		}
	}
	if master == nil {
		// 1. 元数据异常: 找不到主节点，无法修复
		// 2. 热活从地域: 直接替换
		resource.LoggerTask.Warning(ctx, "no master node found", logit.String("cluster_id", cluster.ClusterId))
		return cerrs.ErrNotFound.Errorf("no master node found for node %s", node.NodeId)
	}

	nodeReplInfo, err := GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
	if err != nil {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("get replicationInfo from node %s failed",
			node.NodeId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("get replicationInfo from node %s succ",
		node.NodeId), logit.String("replication_info", base_utils.Format(nodeReplInfo)))

	if nodeReplInfo.Role != "slave" || nodeReplInfo.MasterHost != master.Ip || nodeReplInfo.MasterPort != master.Port {
		resource.LoggerTask.Warning(ctx, "node replication is abnormal", logit.String("node_id", node.NodeId),
			logit.String("master_id", master.NodeId), logit.String("node_repl_info", base_utils.Format(nodeReplInfo)))
		return cerrs.ErrNotFound.Errorf("node %s replication is abnormal", node.NodeId)
	}
	return nil
}

func SlaveOfRenamed(ctx context.Context, c *single_redis.SingleClient, masterHost, masterPort string) error {
	if err := c.SlaveOf(ctx, masterHost, masterPort).Err(); err != nil {
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("cmd slaveof %s %s failed", masterHost, masterPort), logit.Error("error", err))
		return c.Do(ctx, "aae420ac56ef116058218c11d8b35b30SLAVEOF", masterHost, masterPort).Err()
	}
	return nil
}
