package util

import (
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func Test_IsNodeSwitchable(t *testing.T) {
	t.Run("NodeSwitchableByStatus", func(t *testing.T) {
		node := &x1model.Node{Status: x1model.NodeOrProxyStatusInUse}
		if !IsNodeSwitchable(node) {
			t.<PERSON>("Expected true, got false")
		}
	})

	t.Run("NodeSwitchableByTag", func(t *testing.T) {
		node := &x1model.Node{Tags: NodeSwitchableTag}
		if !IsNodeSwitchable(node) {
			t.<PERSON>("Expected true, got false")
		}
	})

	t.Run("NodeNotSwitchable", func(t *testing.T) {
		node := &x1model.Node{}
		if IsNodeSwitchable(node) {
			t.<PERSON>rf("Expected false, got true")
		}
	})
}

func Test_SetNodeSwitchable(t *testing.T) {
	t.Run("SetNodeSwitchableTrue", func(t *testing.T) {
		node := &x1model.Node{}
		SetNodeSwitchable(node, true)
		if !IsNodeSwitchable(node) {
			t.<PERSON>("Expected true, got false")
		}
	})

	t.Run("SetNodeSwitchableFalse", func(t *testing.T) {
		node := &x1model.Node{Tags: NodeSwitchableTag}
		SetNodeSwitchable(node, false)
		if IsNodeSwitchable(node) {
			t.Errorf("Expected false, got true")
		}
	})
}
