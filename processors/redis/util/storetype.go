// Copyright(C) 2025 Baidu Inc. All Rights Reserved.
// Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
// Date: 2025/04/08
// Doc: PEGA支持本地盘 方案设计 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/Rf1lBSBHY96CBa

package util

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

type GetStoreTypeParmas struct {
	NodeType string `json:"nodeType"`
}

// GetStoreType 获取storetype
// 这个方法是在做【pega本地盘】需求新增的，在这个需求之前，SCS的storetype写死了DRAM，本地盘新增一种LOCALDISK，在这里进行判断返回
func GetStoreType(ctx context.Context, params *GetStoreTypeParmas) string {
	for _, nodeType := range conf.StoreTypeConfIns.LocalDiskNodeType {
		if nodeType == params.NodeType {
			return x1model.StoreTypeLOCALDISK
		}
	}

	return x1model.StoreTypeDRAM
}
