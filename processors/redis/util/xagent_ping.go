/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* xagent_ping.go */
/*
modification history
--------------------
2023/01/01 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package util

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	livenessProbeTimeout = 30
)

var (
	ErrCallXagentFail           = errors.New("call xagent fail")
	ErrXagentCallRedisIOTimeout = errors.New("ping redis io timeout")
)

type livenessProbeParams struct {
	Meta *meta `json:"meta"`
}

type meta struct {
	Host        string `json:"host"`
	Port        int    `json:"port"`
	AccountName string `json:"account_name"`
	Password    string `json:"password"`
}

//	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
//	if err != nil && !x1model.IsNotFound(err) {
//		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
//		return err
//	}
//
// var password string
func LivenessProbe(ctx context.Context, xagentAddr *xagent.Addr, target *x1model.Node, password string) (bool, error) {
	encryptPwd := ""
	if password != "" {
		encryptPwd, _ = crypto_utils.EncryptKey(password)
		resource.LoggerTask.Trace(ctx, "get encryptkey pwd", logit.String("pwd", password),
			logit.String("new pwd", encryptPwd))
	}
	pingReq := xagent.Request{
		Addr:   xagentAddr,
		Action: "livenessprobe",
		Params: livenessProbeParams{Meta: &meta{
			Host:     target.Ip,
			Port:     target.Port,
			Password: encryptPwd,
		}},
	}

	resp, err := xagent.Instance().Do(ctx, &pingReq)
	// 调用xagent失败
	if err != nil {
		resource.LoggerTask.Trace(ctx, "call xagent fail", logit.Error("err", err))
		if strings.Contains(err.Error(), "timeout") {
			return false, ErrXagentCallRedisIOTimeout
		}
		return false, ErrCallXagentFail
	}
	strResp := base_utils.Format(resp.Result)
	resource.LoggerTask.Trace(ctx, "ping via xagent success", logit.String("xagentAddr", base_utils.Format(xagentAddr)),
		logit.String("target", target.NodeId), logit.String("resp", strResp))

	if target.Engine == x1model.EnginePegaDB {
		if !IsPegaAlive(strResp) {
			return false, fmt.Errorf("not alive,resp :%s", strResp)
		}
	} else {
		if !IsRedisAlive(strResp) {
			return false, fmt.Errorf("not alive,resp :%s", strResp)
		}
	}

	return true, nil
}

func IsRedisAlive(resp string) bool {
	if strings.Contains(strings.ToUpper(resp), "PONG") {
		return true
	}
	if strings.Contains(strings.ToUpper(resp), "NOAUTH") {
		return true
	}
	if strings.Contains(strings.ToUpper(resp), strings.ToUpper("ERR max number of clients reached")) {
		return true
	}
	if strings.Contains(strings.ToUpper(resp), strings.ToUpper("Redis is loading the dataset in memory")) {
		return true
	}

	return false
}

func IsPegaAlive(resp string) bool {
	if redisRet := IsRedisAlive(resp); redisRet {
		return true
	}
	if strings.Contains(strings.ToUpper(resp), strings.ToUpper("ERR restoring the db from backup")) {
		return true
	}
	if strings.Contains(strings.ToUpper(resp), strings.ToUpper("ERR wrong number of arguments")) {
		return true
	}

	return false
}
