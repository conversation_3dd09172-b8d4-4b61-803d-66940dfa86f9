/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* package_manager.go */
/*
modification history
--------------------
2023/04/19 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package util

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	PkgNameAgent = "agent"

	GeryTagBloom = "bloomfilter"
	GeryNewAgent = "newagent"
)

type PkgManConf struct {
}

type ParmasGetGreyBoxFilter struct {
	ServerID    string
	AppID       string
	UserID      string
	VpcID       string
	UseNewAgent bool
}

// MergeGreyBoxPkgs 自适应替换为灰度包
// toDeployPkgInfos 是原始 包列表
// 返回值是已经替换为所需灰度包的 包列表
func MergeGreyBoxPkgs(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter, toDeployPkgInfos []*x1model.Package) []*x1model.Package {
	greyBoxPkgsMap := getLatestGreyBoxPkgsWithFilter(ctx, getGreyBoxFilter(ctx, getGreyBoxParam))
	if len(greyBoxPkgsMap) > 0 {
		resource.LoggerTask.Trace(ctx, "get grey box pkgs success", logit.String("serverID", getGreyBoxParam.ServerID),
			logit.String("grey box pkgs", base_utils.Format(greyBoxPkgsMap)))
	} else {
		return toDeployPkgInfos
	}
	var toDeployPkgInfosWithGreyBoxPackage []*x1model.Package
	for _, toDeployPkg := range toDeployPkgInfos {
		if greyPkg, ok := greyBoxPkgsMap[toDeployPkg.Name]; ok {
			// 灰度map里有就用灰度的
			if greyPkg.MajorVersion == toDeployPkg.MajorVersion {
				resource.LoggerTask.Warning(ctx, "switch to grey box pkg",
					logit.String("grey box pkg", base_utils.Format(greyPkg)),
					logit.String("online pkg", base_utils.Format(toDeployPkg)))
				toDeployPkgInfosWithGreyBoxPackage = append(toDeployPkgInfosWithGreyBoxPackage, greyPkg)
			} else {
				// 大版本不一样的话有问题，用老得并报错
				resource.LoggerTask.Warning(ctx, "grey box pkgs major version not match",
					logit.String("grey box pkg", base_utils.Format(greyPkg)),
					logit.String("online pkg", base_utils.Format(toDeployPkg)))
				toDeployPkgInfosWithGreyBoxPackage = append(toDeployPkgInfosWithGreyBoxPackage, toDeployPkg)
			}
		} else {
			// 灰度map里没有就用原来的
			toDeployPkgInfosWithGreyBoxPackage = append(toDeployPkgInfosWithGreyBoxPackage, toDeployPkg)
		}
	}
	resource.LoggerTask.Trace(ctx, "merge grey box pkgs success",
		logit.String("online pkgs", base_utils.Format(toDeployPkgInfos)),
		logit.String("after merge grey pkgs", base_utils.Format(toDeployPkgInfosWithGreyBoxPackage)))
	return toDeployPkgInfosWithGreyBoxPackage
}

func getLatestGreyBoxPkgsWithFilter(ctx context.Context, greyBoxPkgFilter map[string]string) map[string]*x1model.Package {
	mapGrepBoxPkgs := make(map[string]*x1model.Package, 0)
	for needGreyBoxPackageName, GreyBoxTag := range greyBoxPkgFilter {
		greyPkgs, err := x1model.GetGreyBoxPackages(ctx, needGreyBoxPackageName, GreyBoxTag)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get grey box pkgs info fail",
				logit.String("pkg name", needGreyBoxPackageName),
				logit.String("grey box tag", GreyBoxTag),
				logit.Error("err", err))
			continue
		}
		if len(greyPkgs) == 0 {
			resource.LoggerTask.Warning(ctx, "get grey box pkgs info empty",
				logit.String("pkg name", needGreyBoxPackageName),
				logit.String("grey box tag", GreyBoxTag))
			continue
		}
		mapGrepBoxPkgs[needGreyBoxPackageName] = nil
		for _, grepPkg := range greyPkgs {
			if mapGrepBoxPkgs[needGreyBoxPackageName] == nil {
				mapGrepBoxPkgs[needGreyBoxPackageName] = grepPkg
			} else {
				if grepPkg.Id > mapGrepBoxPkgs[needGreyBoxPackageName].Id {
					mapGrepBoxPkgs[needGreyBoxPackageName] = grepPkg
				}
			}
		}
	}
	return mapGrepBoxPkgs
}

// getGreyBoxFilter 获取灰度tag map
// 返回的map key是package name value是需要的灰度tag
// 这个内部实现可以再改，目前先简单实现支持设置布隆过滤器
func getGreyBoxFilter(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter) map[string]string {
	greyBoxFilter := make(map[string]string, 0)
	// bloomfilter
	fillBloomFilterGreyFilter(ctx, getGreyBoxParam, greyBoxFilter)
	// newagent
	// fillNewAgentGreyFilter(ctx, getGreyBoxParam, greyBoxFilter)
	return greyBoxFilter
}

func fillBloomFilterGreyFilter(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter, greyBoxFilter map[string]string) {
	isNeedBloomfilter, err := resource.CsmasterOpAgent.GetFlag(ctx, "pkg_man_greybox_bloomfilter",
		map[string]string{"iam_user_id": getGreyBoxParam.UserID, "vpc_id": getGreyBoxParam.VpcID}, "no")
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get pkg_man_greybox_bloomfilter flag failed", logit.Error("error", err))
		isNeedBloomfilter = "no"
	}
	if isNeedBloomfilter == "yes" {
		greyBoxFilter[PkgNameAgent] = GeryTagBloom
	}
	// 多活加地域验证
	isNeedSyncAgentTest, err := resource.CsmasterOpAgent.GetFlag(ctx, "pkg_man_greybox_sync_agent_20230724",
		map[string]string{"iam_user_id": getGreyBoxParam.UserID, "vpc_id": getGreyBoxParam.VpcID}, "no")
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get pkg_man_greybox_sync_agent_20230724 flag failed", logit.Error("error", err))
		isNeedSyncAgentTest = "no"
	}
	if isNeedSyncAgentTest == "yes" {
		greyBoxFilter["sync-agent"] = "cpb20230724"
	}
}

func fillNewAgentGreyFilter(ctx context.Context, getGreyBoxParam *ParmasGetGreyBoxFilter, greyBoxFilter map[string]string) {
	if getGreyBoxParam.UseNewAgent {
		greyBoxFilter[PkgNameAgent] = GeryNewAgent
	}
}
