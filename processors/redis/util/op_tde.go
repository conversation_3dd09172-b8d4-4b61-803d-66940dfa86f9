package util

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	baseCsmaster "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type InfoServerDetail struct {
	RedisIP          string
	RedisPort        int
	Role             string
	MasterLinkStatus string
	RdbTdeEnabled    string
}

func CreateTDE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return errors.New("app " + teu.Entity + " not found")
	}

	// 获取集群tde信息
	redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tde conf error", logit.Error("error", err))
		return err
	}

	if len(redisTdeInfos) > 0 {
		// 已经有TDE记录,不允许再次创建
		resource.LoggerTask.Warning(ctx, "len(redisTdeInfos) > 0", logit.String("appId", teu.Entity))
		return errors.New("len(redisTdeInfos) > 0")
	}

	// 重新生成tde信息,落库
	var redisTde x1model.RedisTde
	// 生成32位16进制字符串
	tdeKey := GenerateTDEKey(32)
	resource.LoggerTask.Notice(ctx, "generate TDE Key success",
		logit.String("appId", app.AppId),
		logit.String("tdeKey", tdeKey))

	redisTde.AppID = app.AppId
	redisTde.TdeKey = tdeKey
	redisTde.CreateAt = time.Now()
	redisTde.UpdateAt = time.Time{}
	redisTde.Status = x1model.TdeStatusToCreate

	err = x1model.RedisTdeSave(ctx, []*x1model.RedisTde{&redisTde})
	if err != nil {
		errorMessage := "save tde info failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppId)))
		return err
	}
	return nil
}

func DeliverTdeConf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return errors.New("app " + teu.Entity + " not found")
	}

	redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tde conf error", logit.Error("error", err))
		return err
	}

	if len(redisTdeInfos) == 0 || len(redisTdeInfos) > 1 {
		// tde配置数量不符合预期
		resource.LoggerTask.Warning(ctx, "len(redisTdeInfos) == 0 || len(redisTdeInfos) > 1",
			logit.String("appId", teu.Entity))
		return errors.New("deliverTdeConf: len(redisTdeInfos) == 0 || len(redisTdeInfos) > 1")
	}

	tdeKey := redisTdeInfos[0].TdeKey

	// 调用x1-api modifyConfigInfo接口，关闭多DB
	/*
		ConfModule 参数说明:
			CONF_MODULE_REDIS = 1,
			CONF_MODULE_PROXY = 2,
			CONF_MODULE_REDIS_PROXY = 3,
			CONF_MODULE_META = 4,
	*/
	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "tde-key",
			ConfModule: 1,
			ConfValue:  tdeKey,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}

	return nil
}

func OpenTdePostCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail",
			logit.String("clusterShowId", base_utils.Format(teu.Entity)),
			logit.Error("err", err))
	}
	if cacheCluster == nil {
		resource.LoggerTask.Warning(ctx, "cacheCluster not found", logit.String("clusterShowId", teu.Entity))
		return errors.New("cacheCluster " + teu.Entity + " not found")
	}

out:
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(5 * time.Second)

			// redisPass, err := getRedisAuthFromCsmaster(ctx, app.UserId, app.AppId)
			redisPass, err := getRedisAuthFromCsmaster(ctx, cacheCluster.UserInfo.IamUserId, cacheCluster.ClusterShowId)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get redis auth from csmaster failed",
					logit.String("clusterShowID:", base_utils.Format(cacheCluster.ClusterShowId)), logit.Error("error", err))
				return err
			}

			var instanceModels []*csmaster_model_interface.CacheInstance
			if err := resource.CsmasterModel.GetAllByCondNoTx(ctx, &instanceModels, "cluster_id = ?", cacheCluster.Id); err != nil {
				errorMessage := "get instances failed"
				resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(cacheCluster.ClusterShowId)))
				return err
			}

			var clusterAllInstanceStatus []*InfoServerDetail
			for _, instanceModels := range instanceModels {
				if instanceModels.CacheInstanceType != csmaster_model_interface.INSTANCE_TYPE_PROXY {
					instanceStatus, err := GetRedisOpenTdeStatus(ctx, instanceModels.FloatingIp, instanceModels.Port, redisPass)
					if err != nil {
						resource.LoggerTask.Error(ctx, "get redis open tde status failed",
							logit.String("instanceModels.FloatingIp:", base_utils.Format(instanceModels.FloatingIp)),
							logit.String("instanceModels.Port:", base_utils.Format(instanceModels.Port)))
						return err
					}
					clusterAllInstanceStatus = append(clusterAllInstanceStatus, instanceStatus)
				}
			}
			resource.LoggerTask.Notice(ctx, "get redis open tde status success",
				logit.String("clusterAllInstanceStatus:", base_utils.Format(clusterAllInstanceStatus)))

			// 遍历&check主从同步状态和tde开启状态
			instanceAvailableNum := 0
			for _, instanceStatus := range clusterAllInstanceStatus {
				if instanceStatus.Role == "master" {
					if instanceStatus.RdbTdeEnabled != "1" {
						resource.LoggerTask.Notice(ctx, "cluster exists abnormal instance",
							logit.String("instance.FloatingIp:", base_utils.Format(instanceStatus.RedisIP)),
							logit.String("instance.Port:", base_utils.Format(instanceStatus.RedisPort)),
							logit.String("instanceStatus.Role:", base_utils.Format(instanceStatus.Role)),
							logit.String("instance.MasterLinkStatus:", base_utils.Format(instanceStatus.MasterLinkStatus)),
							logit.String("instance.RdbTdeEnabled:", base_utils.Format(instanceStatus.RdbTdeEnabled)))
					} else {
						resource.LoggerTask.Notice(ctx, "cluster instance open tde success",
							logit.String("instance.FloatingIp:", base_utils.Format(instanceStatus.RedisIP)),
							logit.String("instance.Port:", base_utils.Format(instanceStatus.RedisPort)))
						instanceAvailableNum++
					}

				} else if instanceStatus.Role == "slave" {
					if instanceStatus.MasterLinkStatus != "up" || instanceStatus.RdbTdeEnabled != "1" {
						resource.LoggerTask.Notice(ctx, "cluster exists abnormal instance",
							logit.String("instance.FloatingIp:", base_utils.Format(instanceStatus.RedisIP)),
							logit.String("instance.Port:", base_utils.Format(instanceStatus.RedisPort)),
							logit.String("instanceStatus.Role:", base_utils.Format(instanceStatus.Role)),
							logit.String("instance.MasterLinkStatus:", base_utils.Format(instanceStatus.MasterLinkStatus)),
							logit.String("instance.RdbTdeEnabled:", base_utils.Format(instanceStatus.RdbTdeEnabled)))
					} else {
						resource.LoggerTask.Notice(ctx, "cluster instance open tde success",
							logit.String("instance.FloatingIp:", base_utils.Format(instanceStatus.RedisIP)),
							logit.String("instance.Port:", base_utils.Format(instanceStatus.RedisPort)))
						instanceAvailableNum++
					}
				}
			}
			if instanceAvailableNum < len(clusterAllInstanceStatus) {
				continue
			}

			break out
		}
	}

	return nil
}

func GetRedisOpenTdeStatus(ctx context.Context, redisIP string, redisPort int, redisPwd string) (*InfoServerDetail, error) {
	ret := InfoServerDetail{}
	ret.RedisIP = redisIP
	ret.RedisPort = redisPort

	var cli *single_redis.SingleClient
	if redisPwd != "" {
		cli = single_redis.NewClient(redisIP, redisPort, single_redis.WithPassword(redisPwd))
	} else {
		cli = single_redis.NewClient(redisIP, redisPort)
	}
	defer cli.Close()

	infoServer, err := cli.Info(ctx, "persistence").Result()
	if err != nil {
		return nil, fmt.Errorf("redisIp:%s redisPort:%s get redis infoServer fail,err:%w", redisIP, cast.ToString(redisPort), err)
	}
	infoServer = strings.ReplaceAll(infoServer, "\r\n", "\n")
	infoServer = strings.ReplaceAll(infoServer, "\r", "")
	for _, line := range strings.Split(infoServer, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}
		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		if kv[0] == "rdb_tde_enabled" {
			ret.RdbTdeEnabled = kv[1]
		}
	}

	infoReplication, err := GetReplicationInfo(ctx, redisIP, redisPort, redisPwd)
	if err != nil {
		return nil, fmt.Errorf("redisIp:%s redisPort:%s get redis info replication fail,err:%w", redisIP, cast.ToString(redisPort), err)
	}
	ret.MasterLinkStatus = infoReplication.MasterLinkStatus
	ret.Role = infoReplication.Role
	return &ret, nil
}

func getRedisAuthFromCsmaster(ctx context.Context, iamUserID string, clusterShowID string) (string, error) {
	csmasterModelWithPasswd, err := baseCsmaster.CsmasterOp().GetClusterModel(ctx, iamUserID, clusterShowID)
	if err != nil {
		// 获取密码失败
		errorMessage := "get cacheCluster password failed"
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(clusterShowID)), logit.Error("error", err))
		return "", err
	}
	// 获取Redis密码
	redisPassword := csmasterModelWithPasswd.RedisAuth
	resource.LoggerTask.Notice(ctx, "get cacheCluster password success",
		logit.String("clusterShowId:", base_utils.Format(clusterShowID)),
		logit.String("redisPassword:", base_utils.Format(redisPassword)),
	)
	return redisPassword, nil
}
