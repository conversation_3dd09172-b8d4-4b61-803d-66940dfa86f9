package util

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	csService "icode.baidu.com/baidu/scs/x1-api/httpserver/services/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	baseCsmaster "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	subnet "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/subnet"
	"icode.baidu.com/baidu/scs/x1-base/component/repo"
	zoneComponent "icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lcc_zone"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/common"
)

var (
	NoPasswordRedisAcl = &x1model.RedisAcl{
		AccountName: "default",
		Password:    "",
	}
)

const (
	backupRepo = "backup_bos"
)

func GetToCreateRedisIds(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	ret := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				ret = append(ret, node.NodeId)
			}
		}
	}
	return ret, nil
}

func GetStep(teu *workflow.TaskExecUnit) string {
	chunks := strings.Split(teu.TaskBatchID, "|")
	if len(chunks) > 1 {
		return chunks[1]
	}
	return ""
}

func GetToCreateProxyIds(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	ret := []string{}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				ret = append(ret, proxy.ProxyId)
			}
		}
	}
	return ret, nil
}

func GetToCreateRoNodesIds(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	ret := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				ret = append(ret, node.NodeId)
			}
		}
	}
	return ret, nil
}

func GetImageIdAndVersion(dbImageId string) (imageId string, version string) {
	if strings.Contains(dbImageId, ":") {
		imageId = strings.Split(dbImageId, ":")[0]
		version = strings.Split(dbImageId, ":")[1]
	} else {
		imageId = dbImageId
		version = ""
	}
	return
}

// Returns: AZONE-xxxx, zoneX, err
func GetZoneBySubnetID(ctx context.Context, userID string, subnetID string) (azone string, logicZone string, err error) {
	var zoneName string // cn-<region>-<x>
	var zoneNameSlice []string

	subnetDetail, err := subnet.SubnetResourceOp().GetSubnetDetail(ctx, &subnet.GetSubnetDetailReq{
		UserID:   userID,
		SubnetID: subnetID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get subnet detail failed", logit.Error("error", err))
		return "", "", err
	}

	zoneName = subnetDetail.Subnet.ZoneName
	zoneNameSlice = strings.Split(zoneName, "-")
	logicZone = "zone" + strings.ToUpper(zoneNameSlice[len(zoneNameSlice)-1])

	zoneMapperFunc, err := zoneComponent.ZoneOp().GetZoneMap(ctx, userID)
	azone, found := zoneMapperFunc(logicZone, true)
	if !found {
		return "", "", errors.New("zone not found")
	}
	return azone, logicZone, nil
}

func GetInUseRedisInsts(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	var ret []string
	for _, cluster := range app.Clusters {
		for _, node := range FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status == x1model.NodeOrProxyStatusInUse {
				ret = append(ret, node.NodeId)
			}
		}
	}
	return ret, nil
}

// blb & endpoint 创建之后均会调用此函数
// (1) 检查是否需要服务网卡 (container + !PrivateUserId = have_endpoint)
// (2) blb 创建之后调用此函数，但是此时还没有创建服务网卡, 此时会先跳过
func UpdateEntranceInfo(ctx context.Context, app *x1model.Application) error {
	// 检查是否需要服务网卡(根据 user_id + resourcetype 判断)
	env := blbv2.Instance().GetEnv(ctx)
	var useEndpoint bool
	useEndpoint = !privatecloud.IsPrivateENV() &&
		app.ResourceType == "container" &&
		app.UserId != env.ResourcePrivateUserId

	var entrance string
	for _, blb := range app.BLBs {
		if blb.Status == x1model.BLBStatusDeleted {
			continue
		}

		// app_entrance
		if blb.Type != x1model.BLBTypeAppEntrance {
			continue
		}

		// useEndpoint
		if useEndpoint {
			if len(blb.EndpointIp) != 0 {
				entrance = blb.EndpointIp + ":" + strconv.Itoa(app.Port)
			}
		} else {
			entrance = blb.Ovip + ":" + strconv.Itoa(app.Port)
		}
	}

	if len(entrance) == 0 {
		return nil
	}

	cluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		return err
	}

	// 设置entrance
	cluster.Entrance = entrance
	if err := resource.CsmasterOpAgent.UpdateClusterModel(ctx, cluster); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update cs master cluster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return err
	}

	app.Entrance = entrance
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppId),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func GetToAnalysisRedisIds(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	ret := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status == x1model.NodeOrProxyStatusToAnalyze {
				ret = append(ret, node.NodeId)
			}
		}
	}
	return ret, nil
}

func GetToRecoverRedisShardIds(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	shardIds := []string{}
	for _, cluster := range app.Clusters {
		// 获取集群的分片id列表,备份表里保存的是集群分片id信息,因此,此处汇总shardId列表,然后分别处理每个分片的恢复任务
		shardIds = append(shardIds, cluster.ClusterId)
	}
	return shardIds, nil
}

type RecoverOriginalParams struct {
	ClusterShowID string `json:"clusterShowId"`
	AppBackupID   string `json:"appBackupId"`
}

func GetRecoverOriginalParams(ctx context.Context, params string) (param *RecoverOriginalParams, err error) {
	param = &RecoverOriginalParams{}
	err = errors.Wrap(json.Unmarshal([]byte(params), param), "json decode error")
	return
}

type RecoverNewClusterParams struct {
	SrcClusterShowID  string `json:"srcClusterShowId"`
	AppBackupID       string `json:"appBackupId"`
	AppDataMoment     string `json:"appDataMoment"`
	DestClusterShowID string `json:"destClusterShowId"`
	RestoreType       int    `json:"restoreType"` // 区分rdb数据恢复[0]和时间点数据恢复[1],默认为0
}

func GetRecoverNewClusterParams(ctx context.Context, params string) (param *RecoverNewClusterParams, err error) {
	param = &RecoverNewClusterParams{}
	err = errors.Wrap(json.Unmarshal([]byte(params), param), "json decode error")
	return
}

func CloneTdeConfIfNeeded(ctx context.Context, appID string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appID)),
			logit.Error("err", err))
		return err
	}

	srcClusterShowID := cacheCluster.CloneDataClusterId
	destClusterShowID := cacheCluster.ClusterShowId

	resource.LoggerTask.Notice(ctx, "check cluster is clone or not",
		logit.String("srcClusterShowID:", base_utils.Format(srcClusterShowID)),
		logit.String("destClusterShowID:", base_utils.Format(destClusterShowID)))

	if srcClusterShowID != "" {
		redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, srcClusterShowID)
		if err != nil {
			errorMessage := "get src cluster tde info failed"
			resource.LoggerTask.Error(ctx, errorMessage, logit.String("srcClusterShowID:", base_utils.Format(srcClusterShowID)),
				logit.Error("error", err))
			return err
		}
		if len(redisTdeInfos) > 0 {
			// 集群已经开启tde加密,clone配置至目标集群
			errorMessage := "src cluster already open tde, clone tde conf"
			resource.LoggerTask.Notice(ctx, errorMessage,
				logit.String("srcClusterShowID:", base_utils.Format(srcClusterShowID)),
				logit.String("destClusterShowID:", base_utils.Format(destClusterShowID)))

			srcTdeKey := redisTdeInfos[0].TdeKey
			// 重新生成tde信息,落库
			var redisTde x1model.RedisTde

			redisTde.AppID = destClusterShowID
			redisTde.TdeKey = srcTdeKey
			redisTde.CreateAt = time.Now()
			redisTde.UpdateAt = time.Time{}
			redisTde.Status = x1model.TdeStatusInUse

			err = x1model.RedisTdeSave(ctx, []*x1model.RedisTde{&redisTde})
			if err != nil {
				errorMessage := "save tde info failed."
				resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppId)))
				return err
			}

			modifyReq := baseCsmaster.ModifyConfigParam{
				ConfItem: &baseCsmaster.ConfItem{
					ConfName:   "tde-key",
					ConfModule: 1,
					ConfValue:  srcTdeKey,
				},
				UserID: app.UserId,
				AppID:  app.AppId,
				From:   "admin",
			}
			if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
				resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
				return err
			}
			return nil
		}
		resource.LoggerTask.Notice(ctx, "src cluster did not open tde, do nothing",
			logit.String("srcClusterShowID:", base_utils.Format(srcClusterShowID)))
	}
	return nil
}

func CreateCloneRecoverTaskIfNeeded(ctx context.Context, appId string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}

	useNewBackupFlag := cacheCluster.UseNewBackup
	backupStatus := cacheCluster.BackupStatus
	srcClusterShowID := cacheCluster.CloneDataClusterId
	appBackupID := cacheCluster.CloneDataBackupId
	appDataMoment := cacheCluster.CloneDataMoment
	destClusterShowID := cacheCluster.ClusterShowId
	defaultRestoreType := cacheCluster.RestoreType // 0 基于RDB数据恢复, 1 是时间点恢复

	resource.LoggerTask.Notice(ctx, "check cluster is clone recover or not",
		logit.String("useNewBackupFlag:", base_utils.Format(useNewBackupFlag)),
		logit.String("backupStatus:", base_utils.Format(backupStatus)),
		logit.String("srcClusterShowID:", base_utils.Format(srcClusterShowID)),
		logit.String("appBackupID:", base_utils.Format(appBackupID)),
		logit.String("appDataMoment:", base_utils.Format(appDataMoment)),
		logit.String("destClusterShowID:", base_utils.Format(destClusterShowID)),
		logit.String("defaultRestoreType:", base_utils.Format(defaultRestoreType)))

	if srcClusterShowID != "" {
		srcCacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, srcClusterShowID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
				logit.Error("err", err))
			return err
		}

		if srcCacheCluster.UseNewBackup == 1 {
			if err := csService.UpdateClusterModel(ctx, &baseCsmaster.UpdateClusterModelParams{
				Model: &baseCsmaster.CsmasterCluster{
					UseNewBackup: 1,
				},
				UserID: app.UserId,
				AppID:  app.AppId,
			}); err != nil {
				errorMessage := "update cluster status failed."
				resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
				return err
			}

			/*
				1、集群处于数据恢复的标记:cache_cluster.backup_status = 9
				2、集群使用新备份的标记:cache_cluster.use_new_backup = 1
			*/
			if srcCacheCluster.UseNewBackup == 1 && backupStatus == common.BackupClusterWaitRecover {
				// 创建数据恢复任务
				recoverParams := RecoverNewClusterParams{
					SrcClusterShowID:  srcClusterShowID,
					AppBackupID:       appBackupID,
					AppDataMoment:     appDataMoment,
					DestClusterShowID: destClusterShowID,
					RestoreType:       defaultRestoreType,
				}
				parameter, err := json.Marshal(recoverParams)
				if err != nil {
					errMsg := "recover task params marshal failed"
					resource.LoggerTask.Error(ctx, errMsg, logit.String("params:", base_utils.Format(recoverParams)), logit.Error("err", err))
					return cerrs.ErrInvalidParams.Errorf(errMsg)
				}

				workFlow := "scs-recover-in-new-cluster"
				if defaultRestoreType == 1 {
					workFlow = "scs-recover-in-new-cluster-by-flashback"
				}
				if cacheCluster.StoreType == csService.STORE_PEGA {
					workFlow = "scs-recover-pega-in-new-cluster"
				}
				// 创建x1-task 任务
				tk := &iface.Task{
					TaskID:     uuid.NewString(),
					WorkFlow:   workFlow,
					Entity:     cacheCluster.ClusterShowId,
					EntityDim:  "app",
					Status:     iface.TaskStatusWaiting,
					CreatedAt:  time.Now(),
					Schedule:   time.Now().Add(1 * time.Second),
					Deadline:   time.Now().Add(12 * time.Hour),
					Mutex:      "n_recover_" + cacheCluster.ClusterShowId,
					Parameters: string(parameter),
				}
				if err := resource.TaskOperator.CreateTask(ctx, tk); err != nil {
					errorMessage := "create recover task failed."
					resource.LoggerTask.Error(ctx, errorMessage,
						logit.String("appId:", base_utils.Format(cacheCluster.ClusterShowId)), logit.Error("err", err))
					return err
				}
			}
		}
	}
	return nil
}

func GetStorage(ctx context.Context, dataType string, isBackupLogPolicy bool, cacheCluster *csmaster_model_interface.CacheCluster, appID string) (
	dataStoragesType string, dataStoragesID string, err error) {
	// dataStorages 赋值
	region := env.IDC()
	repoConf := repo.GetRepo(backupRepo).Conf

	// AOF 备份
	if dataType == "Redis" && isBackupLogPolicy {
		dataStoragesType = "bos"
		dataStoragesID = region + "-redis-logbackup-0000"
		return dataStoragesType, dataStoragesID, nil
	}

	// DBstack 备份
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		dataStoragesType = "s3"
		dataStoragesID = region + "-databackup"
		return dataStoragesType, dataStoragesID, nil
	}

	// RDB && 快照备份
	if dataType == "Redis" {
		dataStoragesType = "bos"
		dataStoragesID = repoConf.BosBucket
		isHaveLCC, err := lcc_zone.IsHaveLccAzone(ctx, cacheCluster.UserInfo.IamUserId, cacheCluster.AzDeployInfo)
		if err != nil {
			errMsg := "get lcc zone failed"
			resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
			return "", "", cerrs.ErrInvalidParams.Errorf(errMsg)
		}

		if isHaveLCC {
			dataStoragesID = repoConf.BosBucket + "-lcc-0000"
		}
	} else if dataType == "PegaDB" {
		dataStoragesType = "cds"
		dataStoragesID = region + "-public-pegadb-data"

		// 兼容pegadb本地盘
		// doc：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/Rf1lBSBHY96CBa#anchor-1150d370-04a5-11f0-87cc-63ffbe9f13b5
		// 获取pega的storetype信息
		app, err := x1model.ApplicationGetByAppId(ctx, appID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get app model failed", logit.Error("error", err))
			return "", "", err
		}
		storeType := ""
		for _, cluster := range app.Clusters {
			if storeType == "" {
				storeType = cluster.StoreType
			} else {
				if storeType != cluster.StoreType {
					return "", "", errors.New("has multi store type in app")
				}
			}
		}
		if storeType == x1model.StoreTypeLOCALDISK {
			dataStoragesType = "bos"
		}
		if cacheCluster.Resource_type == "container" {
			// 容器化部署的pegadb, 目前只支持本地盘
			dataStoragesType = "bos"
		}
		// end 兼容pegadb本地盘
	}

	return dataStoragesType, dataStoragesID, nil

}

func RegisterBackupPolicyIfNeeded(ctx context.Context, appId string) error {
	// 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}

	useNewBackupFlag := cacheCluster.UseNewBackup
	// 如果使用新备份,需要向dbrs注册备份策略
	if useNewBackupFlag == 1 {
		dataType := "Redis"
		if cacheCluster.StoreType == 3 && cacheCluster.ClusterType == "cluster" {
			dataType = "PegaDB"
		}

		backupConfig := cacheCluster.BackupConfig
		if backupConfig == "" {
			errMsg := "req.AutoBackupConfig is null, please check request"
			resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(cacheCluster)))
			return cerrs.ErrInvalidParams.Errorf(errMsg)
		}
		// backup_config 格式转化 utc
		backupConfigUtc := BackupConfigFormatUtc(backupConfig)
		resource.LoggerTask.Notice(ctx, "backupConfigUtc", logit.String("backupConfigUtc:", base_utils.Format(backupConfigUtc)))
		if backupConfigUtc == "" {
			errMsg := backupConfig + " BackupConfigFormatUtc failed"
			resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(cacheCluster)))
			return cerrs.ErrInvalidParams.Errorf(errMsg)
		}
		// dbrs注册备份配置
		backupConfigUtcSplit := strings.Split(backupConfigUtc, ";")

		region := env.IDC()
		dataStoragesType, dataStoragesID, err := GetStorage(ctx, dataType, false, cacheCluster, appId)
		if err != nil {
			errMsg := "get storage failed"
			resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
			return cerrs.ErrInvalidParams.Errorf(errMsg)
		}

		createAppBackupParams := &dbrsComp.CreatePolicyConf{
			DataType:              dataType,
			AppID:                 appId,
			DataBackupTime:        backupConfigUtcSplit[1] + "Z",
			DayLevelRetentionTime: cast.ToInt64(backupConfigUtcSplit[2]),
			Type:                  dataStoragesType,
			BucketId:              dataStoragesID,
			Region:                region,
		}

		// 区分没有开启备份的集群
		if backupConfigUtcSplit[0] != "" {
			createAppBackupParams.DataBackupWeekDay = GetWeekday(backupConfigUtcSplit[0])
		}
		resource.LoggerTask.Notice(ctx, "CreateBackupPolicy params",
			logit.String("createAppBackupParams:", base_utils.Format(createAppBackupParams)))

		createAppBackupRsp, err := dbrsComp.DbrsResourceOp().CreateBackupPolicy(ctx, createAppBackupParams)
		if err != nil {
			errMsg := "call dbrs api create app backup policy failed"
			resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
			return cerrs.ErrInvalidParams.Errorf(errMsg)
		}
		resource.LoggerTask.Notice(ctx, "call dbrs api create app backup policy success",
			logit.String("createAppBackupRsp", base_utils.Format(createAppBackupRsp)))
	}

	return nil
}

func DeleteBackupPolicyIfNeeded(ctx context.Context, appId string) error {
	// 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}

	useNewBackupFlag := cacheCluster.UseNewBackup
	// 如果使用新备份,需要向dbrs注册备份策略
	if useNewBackupFlag == 1 {
		dataType := "Redis"
		if cacheCluster.StoreType == 3 && cacheCluster.ClusterType == "cluster" {
			dataType = "PegaDB"
		}

		deleteAppBackupParams := &dbrsComp.CommonPolicyParams{
			AppID:    appId,
			DataType: dataType,
		}
		deleteAppBackupRsp, err := dbrsComp.DbrsResourceOp().DeleteBackupPolicy(ctx, deleteAppBackupParams)
		if err != nil {
			errMsg := "call dbrs api delete app backup policy failed"
			resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
			return cerrs.ErrInvalidParams.Errorf(errMsg)
		}
		resource.LoggerTask.Notice(ctx, "call dbrs api delete app backup policy success",
			logit.String("deleteAppBackupRsp", base_utils.Format(deleteAppBackupRsp)))
	}

	return nil
}

func GetTimeUptoNow(startTime time.Time) int {
	now := time.Now()
	return cast.ToInt(now.Sub(startTime).Seconds())
}

func BackupConfigFormatUtc(localBackupConfig string) string {
	backupConfigSplit := strings.Split(localBackupConfig, ";")
	if len(backupConfigSplit) != 3 {
		return ""
	}

	timeConfigs := strings.Split(backupConfigSplit[1], ":")
	backupConfigUtc := ""
	backupConfigUtc += backupConfigSplit[0]
	backupConfigUtc += ";"
	backupConfigUtc += modifyTime(timeConfigs[0])
	backupConfigUtc += ":"
	backupConfigUtc += modifyTime(timeConfigs[1])
	backupConfigUtc += ":"
	backupConfigUtc += modifyTime(timeConfigs[2])
	backupConfigUtc += ";"
	backupConfigUtc += backupConfigSplit[2]
	return backupConfigUtc
}

func modifyTime(time string) string {
	formatTime := time
	if len(time) == 1 {
		formatTime = "0" + time
	}
	return formatTime
}

func GetWeekday(scsWeekday string) []string {
	weekdayMap := map[string]string{
		"Mon": "Monday",
		"Tue": "Tuesday",
		"Wed": "Wednesday",
		"Thu": "Thursday",
		"Fri": "Friday",
		"Sta": "Saturday",
		"Sun": "Sunday",
	}
	dataBackupWeekDay := []string{}
	for _, day := range strings.Split(scsWeekday, ",") {
		dataBackupWeekDay = append(dataBackupWeekDay, weekdayMap[day])
	}
	return dataBackupWeekDay
}

func GetAllClusterIds(ctx context.Context, task *workflow.Task) ([]string, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, task.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	var ret []string
	for _, cluster := range app.Clusters {
		ret = append(ret, cluster.ClusterId)
	}
	return ret, nil
}

func GetRetry(batchId string) int {
	if strings.Contains(batchId, "|") {
		if strings.Contains(strings.Split(batchId, "|")[1], "__") {
			if retry, err := strconv.Atoi(strings.Split(strings.Split(batchId, "|")[1], "__")[1]); err == nil {
				return retry
			}
		}
	}
	return 0
}
