/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* check_status.go */
/*
modification history
--------------------
2022/07/11 , by <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com) , create
*/
/*
DESCRIPTION
todo
*/

package util

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	NodeSwitchableTag = "switchable"
)

func IsInCsmasterDelProcess(ctx context.Context, appId string, userId string) (bool, error) {
	status, err := csmaster.CsmasterOp().GetClusterStatus(ctx, appId, userId)
	if err != nil {
		return false, err
	}
	if status == 9 || status == 10 {
		return true, nil
	}
	return false, nil
}

func IsNodeSwitchable(node *x1model.Node) bool {
	if node.Status == x1model.NodeOrProxyStatusInUse {
		return true
	}
	if strings.Contains(node.Tags, NodeSwitchableTag) {
		return true
	}
	return false
}

func SetNodeSwitchable(node *x1model.Node, switchable bool) {
	tags := strings.Split(node.Tags, ",")
	if switchable && !IsNodeSwitchable(node) {
		tags = append(tags, NodeSwitchableTag)
	} else if !switchable && IsNodeSwitchable(node) {
		for i, tag := range tags {
			if tag == NodeSwitchableTag {
				tags = append(tags[:i], tags[i+1:]...)
				break
			}
		}
	}
	node.Tags = strings.Join(tags, ",")
}

func ResetAllNodesSwichable(ctx context.Context, appId string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			SetNodeSwitchable(node, false)
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		return err
	}
	return nil
}

func SetSwitchableFlagsAfterModifySpecHandover(ctx context.Context, appId string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete || node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				SetNodeSwitchable(node, false)
			}
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				SetNodeSwitchable(node, true)
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "update app error", logit.Error("error", err))
		return err
	}
	return nil
}
