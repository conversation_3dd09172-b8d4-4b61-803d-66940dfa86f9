/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/24, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
反序列化数据库的字段
*/

package util

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func DeserializationReplicas(ctx context.Context, str string) (map[string]*iface.Replica, error) {
	var replicas []iface.Replica
	err := json.Unmarshal([]byte(str), &replicas)
	if err != nil {
		return nil, err
	}
	ret := map[string]*iface.Replica{}
	for i := range replicas {
		if _, found := ret[replicas[i].Zone]; !found {
			ret[replicas[i].Zone] = &replicas[i]
		} else {
			for _, subnetId := range replicas[i].SubnetIDs {
				if in, _ := base_utils.InArray(subnetId, ret[replicas[i].Zone].SubnetIDs); !in {
					ret[replicas[i].Zone].SubnetIDs = append(ret[replicas[i].Zone].SubnetIDs, subnetId)
				}
				ret[replicas[i].Zone].Count += replicas[i].Count
				if replicas[i].Role == "master" {
					ret[replicas[i].Zone].Role = "master"
				}
			}
		}
	}
	return ret, nil
}
