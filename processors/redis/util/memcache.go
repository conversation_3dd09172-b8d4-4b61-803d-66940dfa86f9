/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/25
 * File: memcache.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package util TODO package function desc
package util

import (
	"context"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func AddNewMcNodesForReplacing(ctx context.Context, app *x1model.Application, action string, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserId, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	nodeIdx, err := GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node max index failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			nodeIdx++
			needNew, err := checkNodeFixId(app, node.NodeFixID, node.ClusterId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check node fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of node %s had been created", node.NodeId))
				continue
			}

			newNode := &x1model.Node{
				NodeId:        cluster.ClusterId + "." + strconv.Itoa(nodeIdx),
				ClusterId:     cluster.ClusterId,
				AppId:         app.AppId,
				Engine:        cluster.Engine,
				EngineVersion: cluster.EngineVersion,
				Port:          cluster.Port,
				Region:        node.Region,
				LogicZone:     node.LogicZone,
				Azone:         node.Azone,
				Role:          x1model.RoleTypeMaster,
				VpcId:         node.VpcId,
				SubnetId:      node.SubnetId,
				Pool:          node.Pool,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       DefaultBaseDir,
				NodeFixID:     node.NodeFixID,
			}

			// 指定了子网，将新 node 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newNode.SubnetId = subnetID
				newNode.Azone = azone
				newNode.LogicZone = logicZone
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("create new node %s replace of old node %s", newNode.NodeId, node.NodeId))
			cluster.Nodes = append(cluster.Nodes, newNode)
		}
	}
	return nil
}

func AddNewMcProxyForReplacing(ctx context.Context, app *x1model.Application, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserId, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	proxyIdx, err := GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy max index failed", logit.Error("error", err))
		return err
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToDelete && proxy.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			proxyIdx++
			needNew, err := checkProxyFixId(app, proxy.NodeFixID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check proxy fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of proxy %s had been created", proxy.ProxyId))
				continue
			}

			newProxy := &x1model.Proxy{
				ProxyId:       itf.InterfaceId + "." + strconv.Itoa(proxyIdx),
				InterfaceId:   itf.InterfaceId,
				AppId:         app.AppId,
				Engine:        itf.Engine,
				EngineVersion: itf.EngineVersion,
				Port:          itf.Port,
				Region:        proxy.Region,
				LogicZone:     proxy.LogicZone,
				Azone:         proxy.Azone,
				VpcId:         proxy.VpcId,
				SubnetId:      proxy.SubnetId,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       proxy.Basedir,
				McpackPort:    itf.Port + 1,
				StatPort:      22222,
				NodeFixID:     proxy.NodeFixID,
			}

			// 指定了子网，将新 proxy 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newProxy.SubnetId = subnetID
				newProxy.Azone = azone
				newProxy.LogicZone = logicZone
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("create new proxy %s replace of old proxy %s", newProxy.ProxyId, proxy.ProxyId))
			itf.Proxys = append(itf.Proxys, newProxy)
		}
	}
	return nil
}
