/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/25 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file get_meta_info.go
 * <AUTHOR>
 * @date 2022/11/25 16:44:06
 * @brief 返回各地域的meta信息
 *
 **/

package util

import "fmt"

var dataServerMap map[string]*DataServerInfo

type DataServerInfo struct {
	Host     string
	Port     string
	Password string
}

func init() {
	dataServerMap = map[string]*DataServerInfo{"sandbox": {
		Host:     "*************",
		Port:     "6590",
		Password: "x1taskmetaserverpppwwwxnxb8",
	}, "preonline": {
		Host:     "*************",
		Port:     "6590",
		Password: "x1taskmetaserverpppwwwxnxb8",
	}, "bj": {
		Host:     "scsmetaserver.bj.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "bd": {
		Host:     "scsmetaserver.bd.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "sin": {
		Host:     "scsmetaserver.sin.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "bdfsg": {
		Host:     "scsmetaserver.hb-fsg.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "bjfsg": {
		Host:     "scsmetaserver.bjfsg.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "fsh": {
		Host:     "scsmetaserver.fsh.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "gz": {
		Host:     "scsmetaserver.gz.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "hkg": {
		Host:     "scsmetaserver.hkg.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "su": {
		Host:     "scsmetaserver.su.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "whgg": {
		Host:     "scsmetaserver.fwh.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "sv": {
		Host:     "scsmetaservervip01.su.baidubce.com",
		Port:     "7500",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	}, "bjtest": {
		Host:     "*************", // task给其他集群下发的dataServer地址，是entrance地址
		Port:     "7400",
		Password: "da0406a080b1cd600810ddf38ec77e4f",
	},
	}
}

// GetDataInfo will return data server info
func GetDataInfo(region string) string {
	ds := dataServerMap[region]
	return fmt.Sprintf("%s:%s|%s", ds.Host, ds.Port, ds.Password)
}
