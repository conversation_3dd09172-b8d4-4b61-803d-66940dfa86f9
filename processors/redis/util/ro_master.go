/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/08/04 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file ro_master.go
 * <AUTHOR>
 * @date 2022/08/04 16:08:53
 * @brief 获取只读组的主节点
 *
 **/

package util

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// isNodeAvailable
func isNodeAvailable(node *x1model.Node) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate
}

// GetShardMasterNode will return first avaiable node if app in group or return real master
func GetShardMasterNode(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) (*x1model.Node, error) {
	// 区分主从角色来获取shard master节点
	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return nil, err
	}

	for _, node := range cluster.Nodes {
		node := node
		if len(app.AppGroupID) != 0 && csCluster.GroupRole == 2 {
			if isNodeAvailable(node) {
				return node, nil
			}
		} else {
			if isNodeAvailable(node) && node.Role == x1model.RoleTypeMaster {
				return node, nil
			}
		}
	}

	return nil, nil
}
