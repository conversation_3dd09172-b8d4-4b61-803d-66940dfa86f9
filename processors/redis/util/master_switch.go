/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/20
 * File: master_switch.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package util TODO package function desc
package util

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	gmasterSdk "icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	SwitchTypeStandalone         = "standalone"
	SwitchTypeGlobalStandalone   = "global_standalone"
	SwitchTypeLocalCluster       = "local_cluster"
	SwitchTypeGlobalCluster      = "global_cluster"
	ForbidWriteTimeout           = 5 * time.Second
	OffsetCheckTimout            = 7 * time.Second
	OffsetCheckDiff              = 1024
	DefaultOffsetDiffForbidWrite = 0

	DataReliabilityTypeUndefined = 0 // 未定义，默认根据引擎类型判断，pega严格禁写，其他不严格禁写
	DataReliabilityTypeLoosely   = 1 // 不严格禁写，禁写失败后继续切换
	DataReliabilityTypeStrictly  = 2 // 严格禁写，禁写失败后持续重试
)

type MasterSwitchReq struct {
	Acl                   *x1model.RedisAcl
	ClusterInfo           *x1model.Cluster
	App                   *x1model.Application
	ManualCandidateSlaves []*x1model.Node
	IsManualSwitch        bool
	SwitchType            string
	UseForbidWrite        bool
}

type checkSwitchMasterNecessityReq struct {
	Acl                   *x1model.RedisAcl
	ClusterInfo           *x1model.Cluster
	ManualCandidateSlaves []*x1model.Node
}

type handoverPrepareData struct {
	Candidates              []*x1model.Node
	NoCandidateIsRealMaster bool
	RealMaster              *x1model.Node
	DbMaster                *x1model.Node
}

type executeSwtichingMasterUsingRedissdkReq struct {
	Dbmaster       *x1model.Node
	Newmaster      *x1model.Node
	Acl            *x1model.RedisAcl
	ClusterInfo    *x1model.Cluster
	App            *x1model.Application
	IsManualSwitch bool
	UseForbidWrite bool
	SwitchType     string
}

// MasterSwitchShard 主从节点切换
// 1 判断切换类型（标准版 or 本地集群 or 热活实例组集群）
// 2 getPrepareDataForHandover：
//
//	2-1 检查是否确实需要切换
//	2-2 找到实际的主
//	2-3 找到数据库中记录的主
//	2-4 构造候选从节点list
//
// 3 切主：
//
//		    3-1 选主：遍历候选slave节点list，找到其中SlaveReplOffset最大的节点（与主最同步的一个）
//			3-2 if 标准版、本地集群:
//				1) 给新选出来的主发送 slave of no one
//				2) 遍历其他节点发送slave of 新选出来的主
//	         if 热活实例组
//				1)调用x1-global-api  local failover接口
//
// 4 更新拓扑：
//
//	4-1 if 本地集群：
//		1) 通知metaserver
//		if 	标准版:
//		1) 换绑BLB
func MasterSwitchShard(ctx context.Context, req *MasterSwitchReq) (newMaster *x1model.Node, err error) {
	resource.LoggerTask.Notice(ctx, "Master Switch Standalone Start",
		logit.String("app_id", req.App.AppId), logit.String("cluster_id", req.ClusterInfo.ClusterId),
		logit.String("manual_candidate_slaves", base_utils.Format(base_utils.SelectColumn(req.ManualCandidateSlaves, "NodeId"))),
		logit.String("switch_type", req.SwitchType),
		logit.Bool("is_manual_switch", req.IsManualSwitch),
		logit.Bool("use_forbid_write", req.UseForbidWrite))

	// 未制定切换类型的话程序判断
	if len(req.SwitchType) == 0 {
		if req.App.Type == x1model.AppTypeStandalone {
			// 标准版
			req.SwitchType = SwitchTypeStandalone
			if len(req.App.AppGroupID) != 0 {
				req.SwitchType = SwitchTypeGlobalStandalone
			}
		} else if req.App.Type == x1model.AppTypeCluster {
			// 集群
			req.SwitchType = SwitchTypeLocalCluster // 本地域
			if len(req.App.AppGroupID) != 0 {
				req.SwitchType = SwitchTypeGlobalCluster // 热活实例组（优先级更高）
			}
		}
		resource.LoggerTask.Notice(ctx, "auto set switch type", logit.String("switch_type", req.SwitchType))
	}
	// step2 准备候选列表并判断是否有必要切换
	//		1、检查是否确实需要切换
	// 		2、找到实际的主
	// 		3、找到数据库中记录的主
	// 		4、构造候选从节点list
	checkNecessityReq := checkSwitchMasterNecessityReq{
		Acl:                   req.Acl,
		ClusterInfo:           req.ClusterInfo,
		ManualCandidateSlaves: req.ManualCandidateSlaves,
	}
	prepareData, err := getPrepareDataForHandover(ctx, &checkNecessityReq)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Check Switch Master Necessity Fail", logit.Error("err", err),
			logit.String("cluster_id", req.ClusterInfo.ClusterId),
			logit.String("manual_candidate_slaves", base_utils.Format(base_utils.SelectColumn(req.ManualCandidateSlaves, "NodeId"))))
		return
	}
	resource.LoggerTask.Notice(ctx, "Check Switch Master Necessity Suc",
		logit.String("app_id", req.App.AppId),
		logit.String("cluster_id", req.ClusterInfo.ClusterId),
		logit.String("db_master", prepareData.DbMaster.NodeId),
		logit.String("manual_candidate_slaves", base_utils.Format(base_utils.SelectColumn(req.ManualCandidateSlaves, "NodeId"))),
		logit.String("candidates", base_utils.Format(base_utils.SelectColumn(prepareData.Candidates, "NodeId"))),
		logit.Bool("need_switch", prepareData.NoCandidateIsRealMaster))

	// 3-1 选主
	if !prepareData.NoCandidateIsRealMaster {
		// 若候选者中已经有节点成为事实主，则newMaster就是这个事实主
		newMaster = prepareData.RealMaster
		resource.LoggerTask.Notice(ctx, "Real Master Is In Candidates ,Skip Master Choose", logit.String("new_master", newMaster.NodeId))
	} else {
		// 遍历候选slave节点list，找到其中SlaveReplOffset最大的节点（与主最同步的一个）
		newMaster, err = getBigestSlaveReplOffsetSlave(ctx, prepareData.Candidates, req)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Choose New Master Fail",
				logit.String("candidates", base_utils.Format(base_utils.SelectColumn(prepareData.Candidates, "NodeId"))), logit.Error("err", err))
			return
		}
		resource.LoggerTask.Notice(ctx, "Choose New Master Suc", logit.String("new_master", newMaster.NodeId))
	}
	if newMaster == nil {
		resource.LoggerTask.Warning(ctx, "Choose New Master Fail, No Master Choosed",
			logit.String("candidates", base_utils.Format(base_utils.SelectColumn(prepareData.Candidates, "NodeId"))))
		return nil, errors.New("promote new redis master failed")
	}
	// step3 切主
	// 3-2 切主
	switch req.SwitchType {
	// 标准版 、 本地集群
	// 给新选出来的主发送 slave of no one
	// 遍历其他节点发送slave of 新选出来的主
	case SwitchTypeStandalone, SwitchTypeLocalCluster:
		switchMasterReq := executeSwtichingMasterUsingRedissdkReq{
			Dbmaster:       prepareData.DbMaster,
			Newmaster:      newMaster,
			Acl:            req.Acl,
			ClusterInfo:    req.ClusterInfo,
			IsManualSwitch: req.IsManualSwitch,
			UseForbidWrite: req.UseForbidWrite,
			SwitchType:     req.SwitchType,
			App:            req.App,
		}
		resource.LoggerTask.Notice(ctx, "Start To Switch",
			logit.String("old_master", switchMasterReq.Dbmaster.NodeId),
			logit.String("new_master", switchMasterReq.Newmaster.NodeId),
			logit.String("cluster_id", req.ClusterInfo.ClusterId),
			logit.Bool("is_manual", req.IsManualSwitch),
			logit.Bool("use_forbid_write", req.UseForbidWrite))
		err = executeSwtichingMasterUsingRedissdk(ctx, &switchMasterReq)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Switch Master Fail",
				logit.String("old_master", switchMasterReq.Dbmaster.NodeId),
				logit.String("new_master", switchMasterReq.Newmaster.NodeId),
				logit.String("cluster_id", req.ClusterInfo.ClusterId),
				logit.Bool("is_manual", req.IsManualSwitch),
				logit.Bool("use_forbid_write", req.UseForbidWrite),
				logit.Error("Err", err))
			return
		}
		resource.LoggerTask.Notice(ctx, "Switch Master Suc",
			logit.String("old_master", switchMasterReq.Dbmaster.NodeId),
			logit.String("new_master", switchMasterReq.Newmaster.NodeId),
			logit.String("cluster_id", req.ClusterInfo.ClusterId),
			logit.Bool("is_manual", req.IsManualSwitch),
			logit.Bool("use_forbid_write", req.UseForbidWrite),
		)
	// 热活实例组
	// 调用x1-global-api  local failover接口
	case SwitchTypeGlobalCluster, SwitchTypeGlobalStandalone:
		var resp *gmasterSdk.LocalFailoverResponse
		if resp, err = gmaster.GlobalMasterOp().LocalFailover(ctx, &gmaster.LocalFailoverParams{
			AppGroupID:     req.App.AppGroupID,
			AppID:          req.App.AppId,
			UserID:         req.App.UserId,
			ShardGlobalID:  req.ClusterInfo.GlobalID,
			NewNodeID:      newMaster.NodeId,
			IsManualSwitch: req.IsManualSwitch,
			UseForbidWrite: req.UseForbidWrite,
			SyncOffsetDiff: DefaultOffsetDiffForbidWrite,
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "global master local switch master failed",
				logit.String("old_master", prepareData.DbMaster.NodeId),
				logit.String("new_master", newMaster.NodeId),
				logit.String("cluster_id", req.ClusterInfo.ClusterId),
				logit.Bool("is_manual", req.IsManualSwitch),
				logit.Bool("use_forbid_write", req.UseForbidWrite),
				logit.Error("err", err),
				logit.String("resp", base_utils.Format(resp)),
			)
			return
		}
		if resp != nil && strings.Contains(strings.ToLower(resp.SwitchMasterErrMsg), "need manual") {
			resource.LoggerTask.Warning(ctx, "global master local switch master failed and need manual",
				logit.String("old_master", prepareData.DbMaster.NodeId),
				logit.String("new_master", newMaster.NodeId),
				logit.String("cluster_id", req.ClusterInfo.ClusterId),
				logit.Bool("is_manual", req.IsManualSwitch),
				logit.Bool("use_forbid_write", req.UseForbidWrite),
				logit.Error("err", err),
				logit.String("resp", base_utils.Format(resp)),
			)
			return nil, cerrs.ErrorTaskManual.Errorf(
				"forbid write fail but app needs forbid write success, please check replication status. old_master: %s, new_master: %s",
				prepareData.DbMaster.NodeId, newMaster.NodeId)
		}
		resource.LoggerTask.Notice(ctx, "Switch Master Suc",
			logit.String("old_master", prepareData.DbMaster.NodeId),
			logit.String("new_master", newMaster.NodeId),
			logit.String("cluster_id", req.ClusterInfo.ClusterId),
			logit.Bool("is_manual", req.IsManualSwitch),
			logit.Bool("use_forbid_write", req.UseForbidWrite),
		)
	}

	// 标准版|热活标准版 && 实际主与数据库记录的主不一致
	// 需要立即切换blb绑定
	if (req.SwitchType == SwitchTypeStandalone || req.SwitchType == SwitchTypeGlobalStandalone) && newMaster.NodeId != prepareData.DbMaster.NodeId {
		if len(req.App.BLBs) == 0 {
			resource.LoggerTask.Warning(ctx, "app has no blb", logit.String("app id:", req.App.AppId))
			err = fmt.Errorf("app has no blb , app id : %s", req.App.AppId)
			return
		}
		err = SwitchBlbBackend(ctx, req.App, prepareData.DbMaster, newMaster)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Switch Blb Fail", logit.Error("Err", err))
			return
		}
		resource.LoggerTask.Notice(ctx, "Switch Blb Success")

		// 向旧主发送client kill断开可能的长链接
		if req.ClusterInfo.EngineVersion != "2.8" {
			var password string
			if req.Acl != nil && len(req.Acl.Password) != 0 {
				password, _ = crypto_utils.DecryptKey(req.Acl.Password)
			}
			if ierr := ClientKillAll(ctx, prepareData.DbMaster.FloatingIP, prepareData.DbMaster.Port, password); ierr != nil {
				resource.LoggerTask.Warning(ctx, "client kill all fail",
					logit.String("redis info ", fmt.Sprintf("%s:%d", prepareData.DbMaster.FloatingIP, prepareData.DbMaster.Port)),
					logit.Error("Error", ierr))
			}
		}
	}
	return
}

func switchMetaserverShardMaster(ctx context.Context, app *x1model.Application, shardId, newMasterId int) error {
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}

	return metaCli.ManualHandover(ctx, shardId, newMasterId)
}

// getPrepareDataForHandover
// 做的工作：
//
//	1、检查是否确实需要切换
//	2、找到实际的主
//	3、找到数据库中记录的主
//	4、构造候选从节点list
func getPrepareDataForHandover(ctx context.Context, req *checkSwitchMasterNecessityReq) (*handoverPrepareData, error) {
	candidateSlaves := req.ManualCandidateSlaves
	needFillCandidateSlaves := base_utils.Empty(req.ManualCandidateSlaves) // 需要填充候选从节点（没有指定候选从节点）
	noCandidateIsRealMaster := true
	var realMaster *x1model.Node = nil // 实际的主(链接Redis并发送info指令)
	var dbMaster *x1model.Node = nil   // 数据库中记录的主
	// 遍历node找到实际的主，和数据库中记录的主
	for _, node := range req.ClusterInfo.Nodes {
		isMaster, err := isMasterNode(ctx, node, req.Acl) // 链接redis并发送info，通过replication内容判断是否为主
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Check Is Master Fail", logit.String("node info", base_utils.Format(node)))
			isMaster = false
		}
		if isMaster {
			if realMaster != nil {
				resource.LoggerTask.Warning(ctx, "has multiple real master", logit.String("master1", realMaster.NodeId),
					logit.String("master2", node.NodeId))
			}
			realMaster = node
		}
		if node.Role == x1model.RoleTypeMaster && (node.Status != x1model.NodeOrProxyStatusToCreate || dbMaster == nil) {
			dbMaster = node
		}
		// 角色为从 && 未制定候选从节点 && 状态为正常/创建中
		if node.Role == x1model.RoleTypeSlave && needFillCandidateSlaves && IsNodeSwitchable(node) {
			candidateSlaves = append(candidateSlaves, node)
		}
	}
	if dbMaster == nil {
		return nil, errors.New("can not find master redis in this shard")
	}

	// 如果实际主在候选从节点里面，说明已经生米煮成熟饭，但数据库没记录正确
	if realMaster != nil {
		for _, candidateSlave := range candidateSlaves {
			if candidateSlave == realMaster {
				noCandidateIsRealMaster = false
				break
			}
		}
	}
	// 兜底，候选节点已经有被实际主了，但实际主是nil，按常理走不到这里
	if !noCandidateIsRealMaster && realMaster == nil {
		return nil, errors.New("real master should not be null when shard no need to switch")
	}

	return &handoverPrepareData{
		Candidates:              candidateSlaves,
		NoCandidateIsRealMaster: noCandidateIsRealMaster,
		RealMaster:              realMaster,
		DbMaster:                dbMaster,
	}, nil
}

// isMasterNode
// 对Redis发送info命令并解析replication段内容判断是否为master节点
func isMasterNode(ctx context.Context, nodeInfo *x1model.Node, acl *x1model.RedisAcl) (isMaster bool, err error) {
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	return IsMaster(ctx, nodeInfo.FloatingIP, nodeInfo.Port, password)
}

// getBigestSlaveReplOffsetSlave
// 遍历候选slave节点list，找到其中SlaveReplOffset最大的节点（与主最同步的一个）
func getBigestSlaveReplOffsetSlave(ctx context.Context, slaves []*x1model.Node, req *MasterSwitchReq) (newMaster *x1model.Node, err error) {
	acl := req.Acl
	app := req.App
	newMaster = nil
	var maxSlaveReplOffset int64 = -1
	if app != nil && global_model.IsFlagSet(app.Properties, x1model.AppPropertiesNoSyncBlockFailover) {
		// 从0开始会在所有candidate都没有复制进度时候阻塞
		maxSlaveReplOffset = 0
	}

	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	for _, node := range slaves {
		replicationInfo, err := GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
		if err != nil {
			resource.LoggerTask.Notice(ctx, "Get Replication Info Fail", logit.String("Floating Ip", node.FloatingIP),
				logit.String("Port", base_utils.Format(node.Port)), logit.String("pwd", password), logit.Error("Err", err))
			continue
		}
		if replicationInfo.Role != "slave" {
			resource.LoggerTask.Notice(ctx, "redis role shoule be slave ,actual is master.", logit.String("node info ", base_utils.Format(node)))
			if newMaster == nil {
				newMaster = node
			}
			continue
		}
		if replicationInfo.SlaveReplOffset > maxSlaveReplOffset {
			newMaster = node
			maxSlaveReplOffset = replicationInfo.SlaveReplOffset
			continue
		}
	}
	if newMaster == nil {
		err = fmt.Errorf("can not find a new master for promotion, candidates : %s", base_utils.SelectColumn(slaves, "NodeId"))
		resource.LoggerTask.Warning(ctx, "can not find a new master for promotion", logit.Error("error", err))
		return nil, err
	}
	resource.LoggerTask.Notice(ctx, "master promotion", logit.String("new master", base_utils.Format(newMaster)),
		logit.String("max_slave_repl_offset:%d", cast.ToString(maxSlaveReplOffset)))
	return newMaster, nil
}

func isAppNeedForbidWriteSucc(ctx context.Context, req *executeSwtichingMasterUsingRedissdkReq) (need bool) {
	// 考虑该场景最多重试3次，故没有实时获取最新元数据；若调整实例禁写策略，可将任务置为人工实例后再重试，以获取最新元数据
	// app, err := x1model.ApplicationGetByAppId(ctx, req.App.AppId)
	// if err != nil {
	// 	resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
	// 	return false
	// }

	// 若元数据中不为默认值，则以元数据为准
	if req.App.DataReliabilityType != DataReliabilityTypeUndefined {
		return req.App.DataReliabilityType == DataReliabilityTypeStrictly
	}

	// 元数据中未定义，则 pegadb例行切换严格禁写，其它场景尽力禁写
	if req.ClusterInfo.Engine == "pegadb" && req.IsManualSwitch {
		return true
	}

	return false
}

func ForbidWriteBeforeSwitchMaster(ctx context.Context, req *executeSwtichingMasterUsingRedissdkReq) (forbidWriteSuccess bool, err error) {
	forbidWriteSuccess = false
	needForbidWriteSucc := false
	var password string
	if req.Acl != nil && len(req.Acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(req.Acl.Password)
	}
	for retry := 0; retry < 3; retry++ {
		gForbidWrite := gtask.Group{
			Concurrent: 25,
		}
		// 1. 先禁写, 另起一个协程等待禁写完成
		gForbidWrite.Go(func() error {
			return gtask.NoPanic(func() error {
				err = ForbidWrite(ctx, &ForbidWriteParams{
					Node:           req.Dbmaster,
					Password:       password,
					CmdTimeoutSec:  int32(ForbidWriteTimeout / time.Second),
					TaskTimeoutSec: int32(ForbidWriteTimeout/time.Second) * 2,
				})
				if err != nil {
					resource.LoggerTask.Warning(ctx, "send forbid write fail", logit.Error("error", err), logit.String("node_id", req.Dbmaster.NodeId))
				}
				resource.LoggerTask.Notice(ctx, "send forbid write success", logit.String("node_id", req.Dbmaster.NodeId))
				return nil
			})
		})
		// 2. 禁写成功后检查offset
		if err := CheckReplOffsetDiff(ctx, &CheckReplOffsetDiffParams{
			SrcNode:      req.Dbmaster,
			DestNode:     req.Newmaster,
			Password:     password,
			TotalTimeout: OffsetCheckTimout,
			Diff:         DefaultOffsetDiffForbidWrite,
		}); err != nil {
			// 禁写失败，判断是否需要严格禁写
			needForbidWriteSucc = isAppNeedForbidWriteSucc(ctx, req)
			if needForbidWriteSucc {
				// 需要严格禁写，则等待本次发送禁写动作完成后再重试
				resource.LoggerTask.Warning(ctx, "check offset diff fail but current app needs forbid write success, wait forbid write complete and retry",
					logit.Error("error", err),
					logit.String("app_id", req.App.AppId),
					logit.String("old_node_id", req.Dbmaster.NodeId),
					logit.String("new_node_id", req.Newmaster.NodeId),
					logit.Int("retry", retry),
				)
				_, _ = gForbidWrite.Wait()
				continue
			} else {
				// 不需要严格禁写，继续执行
				resource.LoggerTask.Warning(ctx, "check offset diff fail but current app does not need forbid write success, continue to switch master",
					logit.Error("error", err),
					logit.String("app_id", req.App.AppId),
					logit.String("old_node_id", req.Dbmaster.NodeId),
					logit.String("new_node_id", req.Newmaster.NodeId),
				)
				break
			}
		} else {
			// 禁写成功且数据追齐，不继续等待发送禁写完成，直接继续执行
			forbidWriteSuccess = true
			resource.LoggerTask.Notice(ctx, "check offset diff success",
				logit.String("app_id", req.App.AppId),
				logit.String("old_node_id", req.Dbmaster.NodeId),
				logit.String("new_node_id", req.Newmaster.NodeId),
			)
			break
		}
	}
	if !forbidWriteSuccess && needForbidWriteSucc {
		// 需要严格禁写，但禁写失败，则返回错误使任务进入人工处理
		resource.LoggerTask.Warning(ctx, "forbid write fail but current app needs forbid write success, turn to manual processing",
			logit.Error("error", err),
			logit.String("app_id", req.App.AppId),
			logit.String("old_node_id", req.Dbmaster.NodeId),
			logit.String("new_node_id", req.Newmaster.NodeId),
		)
		return forbidWriteSuccess, cerrs.ErrorTaskManual.Errorf(
			"forbid write fail but app needs forbid write success, please check replication status. old_master: %s, new_master: %s",
			req.Dbmaster.NodeId, req.Newmaster.NodeId)
	}
	return forbidWriteSuccess, nil
}

// executeSwtichingMasterUsingRedissdk 通过redis sdk执行切主操作
// 给新选出来的主发送 slave of no one
// 遍历其他节点发送slave of 新选出来的主
func executeSwtichingMasterUsingRedissdk(ctx context.Context, req *executeSwtichingMasterUsingRedissdkReq) (err error) {
	var password string
	if req.Acl != nil && len(req.Acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(req.Acl.Password)
	}
	needClientPause := req.IsManualSwitch &&
		req.ClusterInfo.Engine != "pegadb" &&
		(req.ClusterInfo.EngineVersion == "6.0" || req.ClusterInfo.EngineVersion == "7.0")
	forbidWriteSuccess := false

	if req.UseForbidWrite {
		if forbidWriteSuccess, err = ForbidWriteBeforeSwitchMaster(ctx, req); err != nil {
			resource.LoggerTask.Warning(ctx, "forbid write fail and error is not nil", logit.Error("error", err))
			return err
		}
		if forbidWriteSuccess {
			resource.LoggerTask.Notice(ctx, "forbid write success and skip sending client pause")
			if needClientPause {
				needClientPause = false
			}
		}
	}

	if needClientPause {
		if err := SetClientPauseWithMode(ctx, req.Dbmaster.FloatingIP, req.Dbmaster.Port, password, time.Second*2, "WRITE"); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("sent client pause to old master %s(%s:%d) failed",
				req.Dbmaster.NodeId, req.Dbmaster.FloatingIP, req.Dbmaster.Port), logit.Error("error", err))
			// return err // 经沟通，此处不返回错误，不影响后续流程
		}
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("sent client pause to old master %s(%s:%d) success",
			req.Dbmaster.NodeId, req.Dbmaster.FloatingIP, req.Dbmaster.Port))
		// 等待1秒，使主从完全同步
		time.Sleep(time.Second * 1)
	}

	if !req.UseForbidWrite || (req.UseForbidWrite && !forbidWriteSuccess) {
		// 不禁写 或 禁写失败时，进行摘流
		if req.SwitchType == SwitchTypeLocalCluster {
			// 切换meta拓扑，将新主设置为master，旧主自动变为slave
			err = switchMetaserverShardMaster(ctx, req.App, req.ClusterInfo.ClusterShortID, req.Newmaster.NodeShortID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "switch metaserver shard master fail", logit.Error("error", err))
				return
			}
			resource.LoggerTask.Notice(ctx, "before send slaveof, switch metaserver shard master success",
				logit.String("new master", base_utils.Format(req.Newmaster)))
		} else if req.SwitchType == SwitchTypeGlobalStandalone {
			// 换绑BLB后端
			if len(req.App.BLBs) == 0 {
				resource.LoggerTask.Warning(ctx, "app has no blb", logit.String("app id:", req.App.AppId))
				err = fmt.Errorf("app has no blb , app id : %s", req.App.AppId)
				return
			}
			err = SwitchBlbBackend(ctx, req.App, req.Dbmaster, req.Newmaster)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "switch blb fail", logit.Error("Err", err))
				return
			}
			resource.LoggerTask.Notice(ctx, "before send slaveof, switch blb success")

			// 向旧主发送client kill断开可能的长链接
			if req.ClusterInfo.EngineVersion != "2.8" {
				var password string
				if req.Acl != nil && len(req.Acl.Password) != 0 {
					password, _ = crypto_utils.DecryptKey(req.Acl.Password)
				}
				if ierr := ClientKillAll(ctx, req.Dbmaster.FloatingIP, req.Dbmaster.Port, password); ierr != nil {
					resource.LoggerTask.Warning(ctx, "client kill all fail",
						logit.String("redis info ", fmt.Sprintf("%s:%d", req.Dbmaster.FloatingIP, req.Dbmaster.Port)),
						logit.Error("Error", ierr))
				}
				resource.LoggerTask.Notice(ctx, "before send slaveof, send client kill success")
			}
		}
	}

	err = SetSlaveOfOnOneIdempotent(ctx, req.Newmaster.FloatingIP, req.Newmaster.Port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "send slaveof no one fail", logit.String("redis info ", fmt.Sprintf("%s:%d", req.Newmaster.FloatingIP, req.Newmaster.Port)))
		return
	}
	if req.SwitchType == SwitchTypeLocalCluster {
		err = switchMetaserverShardMaster(ctx, req.App, req.ClusterInfo.ClusterShortID, req.Newmaster.NodeShortID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "switch metaserver shard master fail", logit.Error("error", err))
			return
		}
		resource.LoggerTask.Notice(ctx, "switch metaserver shard master success",
			logit.String("new master", base_utils.Format(req.Newmaster)))
	}
	gSlaveof := gtask.Group{
		Concurrent: 25,
	}
	for _, node := range FetchAllNodesOfCluster(ctx, req.ClusterInfo) {
		// for _, node := range req.ClusterInfo.Nodes {
		if node.NodeId == req.Newmaster.NodeId {
			continue
		}
		node := node
		gSlaveof.Go(func() error {
			_ = gtask.NoPanic(func() error {
				// 旧主整体pause 2秒，这里再等待1秒
				if needClientPause && node.NodeId == req.Dbmaster.NodeId {
					time.Sleep(time.Second * 1)
				}
				if err := SetSlaveOfMasterIdempotent(ctx, node.FloatingIP, node.Port, password, req.Newmaster.Ip, cast.ToString(req.Newmaster.Port)); err != nil {
					resource.LoggerTask.Warning(ctx, "send slaveof master fail",
						logit.String("master info ", fmt.Sprintf("%s:%d", req.Newmaster.Ip, req.Newmaster.Port)),
						logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)),
						logit.Error("error", err))
					return nil
				}
				resource.LoggerTask.Notice(ctx, "send slaveof master suc", logit.String("master info ", fmt.Sprintf("%s:%d", req.Newmaster.Ip, req.Newmaster.Port)),
					logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)))
				return nil
			})
			return nil
		})
	}
	_, _ = gSlaveof.Wait()
	return nil
}

// SwitchBlbBackend 换绑BLB的RS
// 容器化使用ContainerId
func SwitchBlbBackend(ctx context.Context, app *x1model.Application, dbMaster *x1model.Node, newMaster *x1model.Node) (err error) {
	if newMaster.ResourceId == dbMaster.ResourceId {
		resource.LoggerTask.Notice(ctx, "update blb backend success", logit.String("from:%s", dbMaster.ResourceId),
			logit.String("to:", newMaster.ResourceId))
		return nil
	}

	var newMasterRs string
	var dbMasterRs string
	if app.ResourceType == "container" {
		newMasterRs = newMaster.ContainerId
		dbMasterRs = dbMaster.ContainerId
	} else {
		newMasterRs = newMaster.ResourceId
		dbMasterRs = dbMaster.ResourceId
	}
	// 区分不同的blb类型进行处理
	var userID string
	normalBlbList := make([]*x1model.BLB, 0)
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}
		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == x1model.BLBTypeApp {
			appBlbList = append(appBlbList, b)
		}
		if b.Type == x1model.BLBTypeNormal {
			normalBlbList = append(normalBlbList, b)
		}
	}
	if len(normalBlbList) != 0 {
		err = blb.Instance().UpdateElbsRs(ctx, &blb.UpdateElbRsParams{
			UserID:       userID,
			ToAddRsUuids: []string{newMasterRs},
			ToDelRsUuids: []string{dbMasterRs},
			BLbs:         app.BLBs,
			Port:         cast.ToInt32(app.Port),
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "update blb backend fail", logit.String("from:%s", dbMasterRs),
				logit.String("to:", newMasterRs))
			return
		}
	}
	if len(appBlbList) != 0 {
		// 更新应用型blb的后端rs
		for _, blb := range appBlbList {
			err = blbv2.Instance().BindRs(ctx, &blbv2.BindRsParams{
				UserID:  userID,
				BLBID:   blb.BlbId,
				IPGroup: blb.IPGroupID,
				Rss: []*blbv2.Rs{
					{
						UUID:   newMasterRs,
						IP:     newMaster.Ip,
						Port:   newMaster.Port,
						Weight: 1,
					},
				},
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "bind app blb backend fail", logit.String("bind:", newMasterRs))
				return
			}
			dbMasterIPPort := fmt.Sprintf("%s:%d", dbMaster.Ip, dbMaster.Port)
			err = blbv2.Instance().UnbindRs(ctx, &blbv2.UnbindRsParams{
				UserID:     userID,
				BLBID:      blb.BlbId,
				IPGroup:    blb.IPGroupID,
				MemberList: []string{dbMasterIPPort},
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "unbind app blb backend fail", logit.String("unbind:", dbMasterRs))
				return
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "update blb backend success", logit.String("from:%s", dbMasterRs),
		logit.String("to:", newMasterRs))
	return nil
}

func HandoverUpdateCsmaster(ctx context.Context, userId string, cluster *x1model.Cluster, oldMaster *x1model.Node, newMaster *x1model.Node) error {
	if newMaster.NodeId != oldMaster.NodeId {
		newMaster.Role = x1model.RoleTypeMaster
		oldMaster.Role = x1model.RoleTypeSlave
		csmasterInstanceFounder, err := GetCsmasterInstanceFounder(ctx, userId, cluster.AppId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get csmaster instance founder failed", logit.Error("error", err))
			return err
		}
		creq := &csmaster.SaveInstancesParams{
			AppID:          cluster.AppId,
			UserID:         userId,
			RequiredFields: []string{"master_redis", "slaver_redis"},
		}
		for _, n := range cluster.Nodes {
			if csmasterInstanceFounder(n.ResourceId) == nil {
				continue
			}
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:              n.ResourceId,
				CacheInstanceType: int32(GetCacheInstanceType(n.Engine, n.Role)),
				MasterRedis:       GetMasterRedis(cluster, n),
				SlaverRedis:       GetSlaveRedis(cluster, n),
			})
		}
		for _, roNode := range cluster.RoNodes {
			if csmasterInstanceFounder(roNode.ResourceId) == nil {
				continue
			}
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:              roNode.ResourceId,
				CacheInstanceType: int32(GetCacheInstanceType(roNode.Engine, roNode.Role)),
				MasterRedis:       GetMasterRedisForRoNode(cluster, roNode),
				SlaverRedis:       "",
			})
		}
		resource.LoggerTask.Trace(ctx, "cbcsmaster", logit.String("creq", base_utils.Format(creq)))

		if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
			resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func UpdateMeta(ctx context.Context, userId string, cluster *x1model.Cluster, oldMaster *x1model.Node, newMaster *x1model.Node) error {
	csmasterInstanceFounder, err := GetCsmasterInstanceFounder(ctx, userId, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get csmaster instance founder failed", logit.Error("error", err))
		return err
	}
	if newMaster.NodeId != oldMaster.NodeId {
		newMaster.Role = x1model.RoleTypeMaster
		oldMaster.Role = x1model.RoleTypeSlave
		creq := &csmaster.SaveInstancesParams{
			AppID:          cluster.AppId,
			UserID:         userId,
			RequiredFields: []string{"master_redis", "slaver_redis"},
		}
		for _, node := range FetchAllNodesOfCluster(nil, cluster) {
			if csmasterInstanceFounder(node.ResourceId) == nil {
				continue
			}
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:              node.ResourceId,
				CacheInstanceType: int32(GetCacheInstanceType(node.Engine, node.Role)),
				MasterRedis:       GetMasterIP(cluster, node, true),
				SlaverRedis:       GetSlaveRedis(cluster, node),
			})
		}
		if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
			resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

type ForbidWriteParams struct {
	Node           *x1model.Node
	Password       string
	CmdTimeoutSec  int32
	TaskTimeoutSec int32
}

func ForbidWrite(ctx context.Context, params *ForbidWriteParams) error {
	// 对于同一个节点，最多只有一条命令可执行成功，所以不存在重复禁写的封线
	cmdsList := [][]string{
		{"CONFIG", "SET", "forbid_write", "yes"},
		{"aae420ac56ef116058218c11d8b35b30CONFIG", "SET", "forbid_write", "yes"},
	}
	for _, cmds := range cmdsList {
		xagentAddr := xagent.Addr{
			Host: params.Node.FloatingIP,
			Port: cast.ToInt32(params.Node.XagentPort),
		}
		redisCmdReq := xagent.AsyncRequest{
			Addr:   &xagentAddr,
			Action: "rediscmd",
			Params: RedisCmdParams{
				Host:          "127.0.0.1",
				Port:          int32(params.Node.Port),
				Password:      params.Password,
				Timeout:       params.CmdTimeoutSec,
				Certification: RedisCmdCertification,
				Cmds:          cmds,
			},
			TimeoutSec: params.TaskTimeoutSec,
		}
		asyncCtx := xagent.Instance().DoAsync(ctx, &redisCmdReq)
		_, err := asyncCtx.Wait()
		if err != nil {
			resource.LoggerTask.Error(ctx, "send forbid write failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

type CheckReplOffsetDiffParams struct {
	SrcNode      *x1model.Node
	DestNode     *x1model.Node
	Password     string
	TotalTimeout time.Duration
	Diff         int64
}

func CheckReplOffsetDiff(ctx context.Context, params *CheckReplOffsetDiffParams) error {
	ctx, cancel := context.WithTimeout(ctx, params.TotalTimeout)
	defer cancel()
	interval := 10 * time.Millisecond
	maxTimes := int(params.TotalTimeout/interval) + 1
	forbidWriteFlag := false
	for i := 0; i < maxTimes; i++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// do nothing
		}
		if !forbidWriteFlag {
			forbidWriteFlag = CheckForbidWrite(ctx, params.SrcNode, params.Password)
			if !forbidWriteFlag {
				resource.LoggerTask.Notice(ctx, "check forbid write failed, retry")
				time.Sleep(interval)
				continue
			}
		}

		g := gtask.Group{
			Concurrent: 25,
		}
		var (
			srcReplInfo, destReplInfo *ReplicationInfo
			srcErr, destErr           error
		)
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				srcReplInfo, srcErr = GetReplicationInfo(ctx, params.SrcNode.FloatingIP, params.SrcNode.Port, params.Password)
				if srcErr != nil {
					resource.LoggerTask.Warning(ctx, "get replication info of src node failed", logit.Error("error", srcErr),
						logit.String("node_id", params.SrcNode.NodeId),
						logit.String("floating_ip", params.SrcNode.FloatingIP),
						logit.Int("port", params.SrcNode.Port),
					)
					return srcErr
				}
				return nil
			})
		})
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				destReplInfo, destErr = GetReplicationInfo(ctx, params.DestNode.FloatingIP, params.DestNode.Port, params.Password)
				if destErr != nil {
					resource.LoggerTask.Warning(ctx, "get replication info of dest node failed", logit.Error("error", destErr),
						logit.String("node_id", params.DestNode.NodeId),
						logit.String("floating_ip", params.DestNode.FloatingIP),
						logit.Int("port", params.DestNode.Port),
					)
					return destErr
				}
				return nil
			})
		})
		if _, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "get replication info failed", logit.Error("error", err))
			time.Sleep(interval)
			continue
		}
		if srcReplInfo == nil {
			resource.LoggerTask.Warning(ctx, "src repl info is nil")
			time.Sleep(interval)
			continue
		}
		if destReplInfo == nil {
			resource.LoggerTask.Warning(ctx, "dest repl info is nil")
			time.Sleep(interval)
			continue
		}

		currentOffsetDiff := srcReplInfo.MasterReplOffset - destReplInfo.SlaveReplOffset
		if currentOffsetDiff <= params.Diff {
			resource.LoggerTask.Notice(ctx, "check replication offset diff success",
				logit.Int64("master_repl_offset", srcReplInfo.MasterReplOffset),
				logit.Int64("slave_repl_offset", destReplInfo.SlaveReplOffset),
				logit.Int64("cur_diff", currentOffsetDiff),
				logit.Int64("sync_offset_diff", params.Diff),
			)
			return nil
		} else {
			resource.LoggerTask.Notice(ctx, "check replication offset diff failed",
				logit.Int64("master_repl_offset", srcReplInfo.MasterReplOffset),
				logit.Int64("slave_repl_offset", destReplInfo.SlaveReplOffset),
				logit.Int64("cur_diff", currentOffsetDiff),
				logit.Int64("sync_offset_diff", params.Diff),
			)
		}
		time.Sleep(interval)
	}
	return errors.New("check replication offset diff timeout")
}

type GetForbidWriteConfigRet struct {
	CmdRet        []string
	CmdErr        error
	RenamedCmdRet []string
	RenamedCmdErr error
}

func GetForbidWriteConfig(ctx context.Context, node *x1model.Node, password string) *GetForbidWriteConfigRet {
	cmdClient := single_redis.NewClient(node.FloatingIP, node.Port,
		single_redis.WithPassword(password),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 500 * time.Millisecond,
			Read:    1000 * time.Millisecond,
			Write:   1000 * time.Millisecond,
		}),
		single_redis.WithRetry(0),
	)
	defer cmdClient.Close()
	renamedCmdClient := single_redis.NewClient(node.FloatingIP, node.Port,
		single_redis.WithPassword(password),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 500 * time.Millisecond,
			Read:    1000 * time.Millisecond,
			Write:   1000 * time.Millisecond,
		}),
		single_redis.WithRetry(0),
	)
	defer renamedCmdClient.Close()
	cmd := redis.NewStringSliceCmd(ctx, "CONFIG", "GET", "forbid_write")
	renamedCmd := redis.NewStringSliceCmd(ctx, "aae420ac56ef116058218c11d8b35b30CONFIG", "GET", "forbid_write")
	g := gtask.Group{}
	g.Go(func() error {
		return gtask.NoPanic(func() error {
			_ = cmdClient.Process(ctx, cmd)
			return nil
		})
	})
	g.Go(func() error {
		return gtask.NoPanic(func() error {
			_ = renamedCmdClient.Process(ctx, renamedCmd)
			return nil
		})
	})
	_, _ = g.Wait()
	ret := &GetForbidWriteConfigRet{}
	ret.CmdRet, ret.CmdErr = cmd.Result()
	ret.RenamedCmdRet, ret.RenamedCmdErr = renamedCmd.Result()
	return ret
}

func CheckForbidWrite(ctx context.Context, node *x1model.Node, password string) bool {
	ret := GetForbidWriteConfig(ctx, node, password)
	return isForbidWriteYes(ctx, ret.CmdRet, ret.CmdErr) || isForbidWriteYes(ctx, ret.RenamedCmdRet, ret.RenamedCmdErr)
}

func isForbidWriteYes(ctx context.Context, ret []string, err error) bool {
	if err != nil {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.Error("error", err))
		return false
	}
	if len(ret) != 2 {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.String("ret", base_utils.Format(ret)))
		return false
	}
	if ret[0] != "forbid_write" {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.String("ret", base_utils.Format(ret)))
		return false
	}
	if ret[1] != "yes" && ret[1] != "1" {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.String("ret", base_utils.Format(ret)))
		return false
	}
	return true
}

func CheckSupportForbidWrite(ctx context.Context, node *x1model.Node, password string) bool {
	ret := GetForbidWriteConfig(ctx, node, password)
	resource.LoggerTask.Notice(ctx, "check support forbid write", logit.String("ret", base_utils.Format(ret)))
	return hasForbidWriteConfig(ctx, ret.CmdRet, ret.CmdErr) || hasForbidWriteConfig(ctx, ret.RenamedCmdRet, ret.RenamedCmdErr)
}

func hasForbidWriteConfig(ctx context.Context, ret []string, err error) bool {
	if err != nil {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.Error("error", err))
		return false
	}
	if len(ret) != 2 {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.String("ret", base_utils.Format(ret)))
		return false
	}
	if ret[0] != "forbid_write" {
		resource.LoggerTask.Trace(ctx, "get forbid write failed", logit.String("ret", base_utils.Format(ret)))
		return false
	}
	return true
}

func TryCorrectMasterRole(ctx context.Context, cluster *x1model.Cluster) error {
	var insts []*csmaster_model_interface.CacheInstance
	if err := resource.CsmasterModel.GetAllByCond(ctx, &insts, "shard_id = ?", cluster.ClusterShortID); err != nil {
		resource.LoggerTask.Warning(ctx, "get insts error", logit.Error("error", err))
		return err
	}
	for _, inst := range insts {
		for _, node := range cluster.Nodes {
			if int64(node.NodeShortID) == inst.Id {
				node.Role = func() string {
					switch inst.CacheInstanceType {
					case 3:
						return x1model.RoleTypeMaster
					case 2:
						return x1model.RoleTypeSlave
					default:
						return node.Role
					}
				}()
			}
		}
	}
	return nil
}

func GetCsmasterInstanceFounder(ctx context.Context, userId, appId string) (func(uuid string) *csmaster.CsmasterInstance, error) {
	csmasterInstances, err := csmaster.CsmasterOp().GetInstanceModels(ctx, userId, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster instance models error", logit.Error("error", err))
		return nil, err
	}
	return func(uuid string) *csmaster.CsmasterInstance {
		for _, inst := range csmasterInstances {
			if inst.Uuid == uuid {
				return inst
			}
		}
		return nil
	}, nil
}
