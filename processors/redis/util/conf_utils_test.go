package util

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func Test_transConf(t *testing.T) {
	type args struct {
		confName string
		confVal  string
		engine   string
		role     string
	}
	tests := []struct {
		name string
		args args
		want []*ConfigItem
	}{
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "yes",
				engine:   "redis",
				role:     "master",
			},
			want: []*ConfigItem{
				{
					Name:  "appendonly",
					Value: "yes",
				},
			},
		},
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "no",
				engine:   "redis",
				role:     "master",
			},
			want: []*ConfigItem{
				{
					Name:  "appendonly",
					Value: "no",
				},
			},
		},
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "yes",
				engine:   "redis",
				role:     "slave",
			},
			want: []*ConfigItem{
				{
					Name:  "appendonly",
					Value: "yes",
				},
			},
		},
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "no",
				engine:   "redis",
				role:     "slave",
			},
			want: []*ConfigItem{
				{
					Name:  "appendonly",
					Value: "no",
				},
			},
		},
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "partial",
				engine:   "redis",
				role:     "slave",
			},
			want: []*ConfigItem{
				{
					Name:  "appendonly",
					Value: "yes",
				},
			},
		},
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "partial",
				engine:   "redis",
				role:     "master",
			},
			want: []*ConfigItem{
				{
					Name:  "appendonly",
					Value: "no",
				},
			},
		},
		{
			name: "appendonly",
			args: args{
				confName: "appendonly",
				confVal:  "partial",
				engine:   "proxy",
				role:     "master",
			},
			want: []*ConfigItem{},
		},
		{
			name: "hashtag_enable",
			args: args{
				confName: "hashtag_enable",
				confVal:  "yes",
				engine:   "proxy",
				role:     "",
			},
			want: []*ConfigItem{
				{
					Name:  "hash_tag",
					Value: "\"{}\"",
				},
			},
		},
		{
			name: "hashtag_enable",
			args: args{
				confName: "hashtag_enable",
				confVal:  "yes",
				engine:   "proxy",
				role:     "",
			},
			want: []*ConfigItem{
				{
					Name:  "hash_tag",
					Value: "\"{}\"",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := transConf(tt.args.confName, tt.args.confVal, tt.args.engine, tt.args.role); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("transConf() = %v, want %v", got, tt.want)
			}
		})
	}
}

type LogMock struct {
}

func (l LogMock) Debug(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("Debug: %s\n", message)
}

func (l LogMock) Trace(ctx context.Context, message string, fields ...logit.Field) {
	fmt.Printf("Trace: %s\n", message)
}

func (l LogMock) Notice(ctx context.Context, message string, fields ...logit.Field) {
	// TODO implement me
	panic("implement me")
}

func (l LogMock) Warning(ctx context.Context, message string, fields ...logit.Field) {
	// TODO implement me
	panic("implement me")
}

func (l LogMock) Error(ctx context.Context, message string, fields ...logit.Field) {
	// TODO implement me
	panic("implement me")
}

func (l LogMock) Fatal(ctx context.Context, message string, fields ...logit.Field) {
	// TODO implement me
	panic("implement me")
}

func (l LogMock) Output(ctx context.Context, level logit.Level, callDepth int, message string, fields ...logit.Field) {
	// TODO implement me
	panic("implement me")
}

func TestGetUpdateConfigList(t *testing.T) {
	resource.LoggerTask = &LogMock{}
	raw := `{
  "engine": "redis",
  "engine_version": "5.0",
  "role": "master",
  "need_filter": false,
  "update_config_params": [
    {
      "app_id": "scs-bj-sceermlmlaqw",
      "type": "redis",
      "name": "hash-max-ziplist-entries",
      "value": "0"
    },
	{
      "app_id": "scs-bj-sceermlmlaqw",
      "type": "redis",
      "name": "lazyfree-lazy-user-del",
      "value": "no"
    }
  ],
  "conf_records": [
    {
      "id": 2940,
      "cluster_id": 200004739,
      "conf_name": "support_scan",
      "conf_module": 2,
      "value": "true",
      "effected": 1
    },
    {
      "id": 2943,
      "cluster_id": 200004739,
      "conf_name": "hash-max-ziplist-entries",
      "conf_module": 1,
      "value": "0",
      "effected": 1
    },
    {
      "id": 2944,
      "cluster_id": 200004739,
      "conf_name": "lazyfree-lazy-user-del",
      "conf_module": 1,
      "value": "no",
      "effected": 1
    },
    {
      "id": 2945,
      "cluster_id": 200004739,
      "conf_name": "maxmemory-policy",
      "conf_module": 1,
      "value": "allkeys-lfu",
      "effected": 1
    },
    {
      "id": 2946,
      "cluster_id": 200004739,
      "conf_name": "disable_commands",
      "conf_module": 1,
      "value": "",
      "effected": 1
    },
    {
	  "id": 4100,
	  "cluster_id": 200004739,
	  "conf_name": "hashtag_enable",
	  "conf_module": 3,
	  "value": "yes",
	  "effected": 1
	}
  ],
  "conf_defs": [
    {
      "id": 1,
      "conf_name": "scsproxy_auto_auth_address",
      "conf_module": 2,
      "conf_desc": "cHJveHkgYXV0b19hdXRoX2FkZHJlc3M=",
      "conf_type": 1,
      "conf_range": "",
      "conf_default": "",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 4,
      "conf_name": "auto-auth-address",
      "conf_module": 1,
      "conf_desc": "YXV0by1hdXRoLWFkZHJlc3M=",
      "conf_type": 1,
      "conf_range": "",
      "conf_default": "",
      "conf_cache_version": 7001,
      "conf_redis_version": "6.0",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 5,
      "conf_name": "support_scan",
      "conf_module": 2,
      "conf_desc": "c3VwcG9ydCBzY2FuIG9wdGlvbg==",
      "conf_type": 1,
      "conf_range": "true|false",
      "conf_default": "false",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 6,
      "conf_name": "support_multi_active",
      "conf_module": 1,
      "conf_desc": "bXVsdGkgYWN0aXZlIG9wdGlvbg==",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 7,
      "conf_name": "appendonly",
      "conf_module": 1,
      "conf_desc": "5piv5ZCm5ZCv55SoIEFPRiDmjIHkuYXljJY=",
      "conf_type": 1,
      "conf_range": "yes|no|partial",
      "conf_default": "yes",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 8,
      "conf_name": "use-op-header",
      "conf_module": 1,
      "conf_desc": "dXNlLW9wLWhlYWRlcg==",
      "conf_type": 2,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 9,
      "conf_name": "aof-shift",
      "conf_module": 1,
      "conf_desc": "YW9mLXNoaWZ0",
      "conf_type": 2,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 10,
      "conf_name": "maxmemory-policy",
      "conf_module": 1,
      "conf_desc": "5b2T5YaF5a2Y6LaF6YeP5pe255qE6ZSu5reY5rGw562W55Wl44CC5Y+v6YCJOjxici8+dm9sYXRpbGU
tbHJ177ya5Y+q5LuO5bey6K6+572u6L+H5pyf5pe26Ze055qE5pWw5o2u6ZuG5Lit5oyR6YCJ5pyA6L+R5pyA5bCR5L2/55So55
qE5pWw5o2u5reY5rGwPGJyLz5hbGxrZXlzLWxyde+8muaMkemAieacgOi/keacgOWwkeS9v+eUqOeahOaVsOaNrua3mOaxsDxic
i8+dm9sYXRpbGUtcmFuZG9t77ya5Y+q5LuO5bey6K6+572u6L+H5pyf5pe26Ze055qE5pWw5o2u6ZuG5Lit5Lu75oSP6YCJ5oup
5pWw5o2u5reY5rGwPGJyLz5hbGxrZXlzLXJhbmRvbe+8muS7u+aEj+mAieaLqeaVsOaNrua3mOaxsDxici8+dm9sYXRpbGUtdHR
s77ya5Y+q5LuO5bey6K6+572u6L+H5pyf5pe26Ze055qE5pWw5o2u6ZuG5Lit5oyR6YCJ5bCG6KaB6L+H5pyf55qE5pWw5o2u5r
eY5rGwPGJyLz5ub2V2aWN0aW9u77ya5LiN6L+b6KGM5pWw5o2u5reY5rGwPGJyLz52b2xhdGlsZS1sZnU6IOS7juaJgOaciemFj
ee9ruS6hui/h+acn+aXtumXtOeahOmUruS4rempsemAkOS9v+eUqOmikeeOh+acgOWwkeeahOmUrjxici8+YWxsa2V5cy1sZnU6
IOS7juaJgOaciemUruS4rempsemAkOS9v+eUqOmikeeOh+acgOWwkeeahOmUrg==",
      "conf_type": 1,
      "conf_range": "volatile-lru|allkeys-lru|volatile-random|allkeys-random|volatile-ttl|volatile-lfu|allkeys-lfu|noeviction",
      "conf_default": "volatile-ttl",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 11,
      "conf_name": "hash-max-ziplist-entries",
      "conf_module": 1,
      "conf_desc": "5b2TIGhhc2gg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMIGhhc2gg5a+
56LGh5L2/55SoIHppcGxpc3Qg57yW56CB77yaPGJyLz4xLiBoYXNoIOWvueixoeS/neWtmOeahOmUruWAvOWvueaVsOmHj+Wwj+
S6juaIluiAheetieS6jiBoYXNoLW1heC16aXBsaXN0LWVudHJpZXMg5Y+C5pWw5YC8PGJyLz4yLiBoYXNoIOWvueixoeS/neWtm
OeahOaJgOaciemUruWAvOWvueeahOmUruWSjOWAvOeahOWtl+espuS4sumVv+W6pumDveWwj+S6juaIluiAheetieS6jiBoYXNo
LW1heC16aXBsaXN0LXZhbHVlIOWPguaVsOWAvA==",
      "conf_type": 2,
      "conf_range": "0-999999999999999",
      "conf_default": "512",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 12,
      "conf_name": "hash-max-ziplist-value",
      "conf_module": 1,
      "conf_desc": "5b2TIGhhc2gg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMIGhhc2gg5a
+56LGh5L2/55SoIHppcGxpc3Qg57yW56CB77yaCjEuIGhhc2gg5a+56LGh5L+d5a2Y55qE6ZSu5YC85a+55pWw6YeP5bCP5LqO
5oiW6ICF562J5LqOIGhhc2gtbWF4LXppcGxpc3QtZW50cmllcyDlj4LmlbDlgLwKMi4gaGFzaCDlr7nosaHkv53lrZjnmoTmiY
DmnInplK7lgLzlr7nnmoTplK7lkozlgLznmoTlrZfnrKbkuLLplb/luqbpg73lsI/kuo7miJbogIXnrYnkuo4gaGFzaC1tYXgt
emlwbGlzdC12YWx1ZSDlj4LmlbDlgLw=",
      "conf_type": 2,
      "conf_range": "0-999999999999999",
      "conf_default": "64",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 13,
      "conf_name": "set-max-intset-entries",
      "conf_module": 1,
      "conf_desc": "5b2TIHNldCDlr7nosaHlkIzml7bmu6HotrPku6XkuIvkuKTkuKrmnaHku7bml7bvvIwgc2V0IOWvu
eixoeS9v+eUqCBpbnRzZXQg57yW56CB77yaCjEuIHNldCDlr7nosaHkuK3nmoTlhYPntKDmlbDph4/lsI/kuo7miJbogIXnrY
nkuo4gc2V0LW1heC1pbnRzZXQtZW50cmllcyDlj4LmlbDlgLwKMi4gc2V0IOWvueixoeS4reeahOaJgOacieWFg+e0oOmDvea
YryA2NCDkvY3mnInnrKblj7fljYHov5vliLbmlbTmlbA=",
      "conf_type": 2,
      "conf_range": "0-999999999999999",
      "conf_default": "512",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 14,
      "conf_name": "zset-max-ziplist-entries",
      "conf_module": 1,
      "conf_desc": "5b2TIHpzZXQg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMenNldCDl
r7nosaHkvb/nlKggemlwbGlzdCDnvJbnoIHvvJoKMS4genNldCDlr7nosaHkv53lrZjnmoTplK7lgLzlr7nmlbDph4/lsI/k
uo7miJbogIXnrYnkuo4genNldC1tYXgtemlwbGlzdC1lbnRyaWVzIOWPguaVsOWAvAoyLiB6c2V0IOWvueixoeS/neWtmOea
hOaJgOaciemUruWAvOWvueeahOmUruWSjOWAvOeahOWtl+espuS4sumVv+W6pumDveWwj+S6juaIluiAheetieS6jiB6c2V0
LW1heC16aXBsaXN0LXZhbHVlIOWPguaVsOWAvA==",
      "conf_type": 2,
      "conf_range": "0-999999999999999",
      "conf_default": "128",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 15,
      "conf_name": "zset-max-ziplist-value",
      "conf_module": 1,
      "conf_desc": "5b2TIHpzZXQg5a+56LGh5ZCM5pe25ruh6Laz5Lul5LiL5Lik5Liq5p2h5Lu25pe277yMenNldCD
lr7nosaHkvb/nlKggemlwbGlzdCDnvJbnoIHvvJoKMS4genNldCDlr7nosaHkv53lrZjnmoTplK7lgLzlr7nmlbDph4/lsI
/kuo7miJbogIXnrYnkuo4genNldC1tYXgtemlwbGlzdC1lbnRyaWVzIOWPguaVsOWAvAoyLiB6c2V0IOWvueixoeS/neWtm
OeahOaJgOaciemUruWAvOWvueeahOmUruWSjOWAvOeahOWtl+espuS4sumVv+W6pumDveWwj+S6juaIluiAheetieS6jiB6
c2V0LW1heC16aXBsaXN0LXZhbHVlIOWPguaVsOWAvA==",
      "conf_type": 2,
      "conf_range": "0-999999999999999",
      "conf_default": "64",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 16,
      "conf_name": "timeout",
      "conf_module": 1,
      "conf_desc": "5pat5byA56m66Zey5a6i5oi356uv5LmL5YmN562J5b6F55qE56eS5pWw44CC6Zu25YC86KGo56S
65LuO5LiN5pat5byA56m66Zey5a6i5oi356uv",
      "conf_type": 2,
      "conf_range": "0-100000",
      "conf_default": "0",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 17,
      "conf_name": "hz",
      "conf_module": 1,
      "conf_desc": "6K6+572uIFJlZGlzIOWQjuWPsOS7u+WKoeaJp+ihjOmikeeOh++8jOavlOWmgua4hemZpOi/h
+acn+mUruS7u+WKoeOAguWPguaVsOWAvOi2iuWkp++8jENQVSDmtojogJfotorlpKfvvIzlu7bov5/otorlsI/vvIzlu7
rorq7kuI3opoHotoXov4cgMTAw",
      "conf_type": 2,
      "conf_range": "1-500",
      "conf_default": "10",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 18,
      "conf_name": "disable_commands",
      "conf_module": 1,
      "conf_desc": "55So5oi36Ieq5a6a5LmJ56aB55So5ZG95Luk",
      "conf_type": 3,
      "conf_range": "flushall|flushdb|keys|hgetall",
      "conf_default": "flushall,flushdb",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 1
    },
    {
      "id": 19,
      "conf_name": "user_filter_cmds",
      "conf_module": 1,
      "conf_desc": "dXNlciBmaWx0ZXIgY21kcw==",
      "conf_type": 4,
      "conf_range": "set|del",
      "conf_default": "",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 20,
      "conf_name": "qpsquota",
      "conf_module": 2,
      "conf_desc": "cmVzdHJpY3Rpb25zIG9uIGVhY2ggcHJveHkgcXBz",
      "conf_type": 2,
      "conf_range": "0-500000",
      "conf_default": "100000",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 21,
      "conf_name": "print_access_log_control",
      "conf_module": 2,
      "conf_desc": "YWNjZXNzIGxvZyBvcHRpb24=",
      "conf_type": 1,
      "conf_range": "true|false",
      "conf_default": "false",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 22,
      "conf_name": "select_cmd_enable",
      "conf_module": 3,
      "conf_desc": "c2VsZWN0IGNtZCBvcHRpb24=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 23,
      "conf_name": "hashtag_enable",
      "conf_module": 3,
      "conf_desc": "aGFzaHRhZyBvcHRpb24=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 24,
      "conf_name": "client-output-buffer-limit",
      "conf_module": 1,
      "conf_desc": "cmVkaXMgb3V0cHV0IGJ1Zg==",
      "conf_type": 1,
      "conf_range": "normal 0 0 0",
      "conf_default": "normal 0 0 0",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 25,
      "conf_name": "scsproxy_timeout",
      "conf_module": 2,
      "conf_desc": "cHJveHkgdGltZW91dA==",
      "conf_type": 2,
      "conf_range": "0-100000",
      "conf_default": "2000",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 26,
      "conf_name": "support_second_exec",
      "conf_module": 1,
      "conf_desc": "bXVsdGkgYWN0aXZlIHNlY29uZCBleGVjIG9wdGlvbg==",
      "conf_type": 1,
      "conf_range": "true|false",
      "conf_default": "false",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 27,
      "conf_name": "proxy_mbuf_size",
      "conf_module": 2,
      "conf_desc": "cHJveHkgbWJ1ZiBzaXpl",
      "conf_type": 2,
      "conf_range": "512-16777216",
      "conf_default": "16384",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 28,
      "conf_name": "always-propagate-del",
      "conf_module": 1,
      "conf_desc": "ZGVs5oyH5Luk6JC9YW9m5paH5Lu25bm25Lyg5pKt",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 29,
      "conf_name": "database-enable-on-loading",
      "conf_module": 1,
      "conf_desc": "5L2/55So5aSaZGI=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 30,
      "conf_name": "lazyfree-lazy-eviction",
      "conf_module": 1,
      "conf_desc": "5piv5ZCm5byA5ZCv5Z+65LqObGF6eWZyZWXnmoTpqbHpgJDlip/og70=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 31,
      "conf_name": "lazyfree-lazy-expire",
      "conf_module": 1,
      "conf_desc": "5piv5ZCm5byA5ZCv5Z+65LqObGF6eWZyZWXnmoTov4fmnJ9LZXnliKDpmaTlip/og70=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "yes",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 32,
      "conf_name": "lazyfree-lazy-server-del",
      "conf_module": 1,
      "conf_desc": "REVM5ZG95Luk5piv5ZCm5Z+65LqObGF6eWZyZWXlvILmraXliKDpmaTmlbDmja4=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "yes",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 33,
      "conf_name": "lazyfree-lazy-user-del",
      "conf_module": 1,
      "conf_desc": "5omn6KGMREVM5ZG95Luk5pe25piv5ZCm5Z+65LqObGF6eWZyZWXlvILmraXliKDpmaTmlbDmja4=",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "yes",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 34,
      "conf_name": "forbid-rewrite-set-command",
      "conf_module": 1,
      "conf_desc": "5piv5ZCmcmV3cml0ZSBzZXRleA==",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 5001,
      "conf_redis_version": "6.0",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 35,
      "conf_name": "appendfsync",
      "conf_module": 1,
      "conf_desc": "YW9m5byC5q2l5YyW5byA5YWz",
      "conf_type": 1,
      "conf_range": "yes|no",
      "conf_default": "yes",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 36,
      "conf_name": "scsproxy_slowlog_log_slower_than",
      "conf_module": 2,
      "conf_desc": "cHJveHkgc2xvd2xvZ19sb2dfc2xvd2VyX3RoYW4=",
      "conf_type": 2,
      "conf_range": "1-10000",
      "conf_default": "300",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 37,
      "conf_name": "slowlog-log-slower-than",
      "conf_module": 1,
      "conf_desc": "6K6+572u5oWi5pel5b+X55qE6K6w5b2V6ZiI5YC877yM5Y2z6K6+572u5a+55omn6KGM5pe26Ze05aSn
5LqO5aSa5bCR5q+r56eS55qE5pON5L2c6L+b6KGM6K6w5b2V",
      "conf_type": 2,
      "conf_range": "10000-10000000",
      "conf_default": "20000",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 0,
      "need_reboot": 0
    },
    {
      "id": 38,
      "conf_name": "compaction-checker-range",
      "conf_module": 1,
      "conf_desc": "Y29tcGFjdGlvbi1jaGVja2VyLXJhbmdl",
      "conf_type": 1,
      "conf_range": "|0-7",
      "conf_default": "0-7",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 39,
      "conf_name": "client_idle_timeout",
      "conf_module": 2,
      "conf_desc": "5a6i5oi356uv56m66Zey6ZO+5o6l6LaF5pe2",
      "conf_type": 2,
      "conf_range": "0-36000",
      "conf_default": "0",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 40,
      "conf_name": "save",
      "conf_module": 1,
      "conf_desc": "5piv5ZCm5ZCv55SoIHJkYiDmjIHkuYXljJY=",
      "conf_type": 1,
      "conf_range": "|900 1|300 10|60 10000",
      "conf_default": "yes",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 41,
      "conf_name": "hotkeys-period",
      "conf_module": 1,
      "conf_desc": "6K6+572u54Ota2V56YeH5qC35ZGo5pyf",
      "conf_type": 2,
      "conf_range": "0-60",
      "conf_default": "10000",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 42,
      "conf_name": "hotkeys-top-size",
      "conf_module": 1,
      "conf_desc": "6K6+572u54Ota2V5IHRvcCBO55qE5YC8",
      "conf_type": 2,
      "conf_range": "0-10000",
      "conf_default": "10000",
      "conf_cache_version": 10001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 43,
      "conf_name": "migrate-type",
      "conf_module": 1,
      "conf_desc": "cGVnYeaVsOaNrui/geenu+aWueW8jw==",
      "conf_type": 1,
      "conf_range": "redis_command|raw_key_value",
      "conf_default": "redis_command",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 44,
      "conf_name": "audit-log",
      "conf_module": 1,
      "conf_desc": "YXVkaXQgbG9n",
      "conf_type": 2,
      "conf_range": "yes|no",
      "conf_default": "no",
      "conf_cache_version": 10001,
      "conf_redis_version": "4.0",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 45,
      "conf_name": "notify-keyspace-events",
      "conf_module": 1,
      "conf_desc": "5a6i5oi356uv5Y+v5Lul5Yip55So6ZSu56m66Ze06YCa55+l5p2l5o6l5pS26YKj5Lqb5b2x5ZONIFJlZ
GlzIOaVsOaNrumbhueahOS6i+S7tumAmuefpe+8jG5vdGlmeS1rZXlzcGFjZS1ldmVudHMg5Y+C5pWw5oyH5a6a5LqG5o6l5pS26Y
Ca55+l55qE57G75Z6L44CC5Y+C5pWw5YC85Y+v55Sx5aSa5Liq5a2X56ym57uE5oiQ77yM56m65YC86KGo56S656aB55So6YCa55+
l44CC5ZCE5a2X56ym5ZCr5LmJ5aaC5LiL77yaPGJyLz5L77ya6ZSu56m66Ze05LqL5Lu2PGJyLz5F77ya6ZSu5LqL5Lu25LqL5Lu2
PGJyLz5n77ya6YCa55So5ZG95Luk77yI6Z2e54m55a6a57G75Z6L77yJ77yM5L6L5aaCIERFTOOAgSBFWFBJUkUg44CBIFJFTkFNR
SDnrYk8YnIvPiTvvJrlrZfnrKbkuLLlkb3ku6Q8YnIvPmzvvJrliJfooajlkb3ku6Q8YnIvPnPvvJrpm4blkIjlkb3ku6Q8YnIvPm
jvvJrlk4jluIzlkb3ku6Q8YnIvPnrvvJrmnInluo/pm4blkIjlkb3ku6Q8YnIvPnjvvJrplK7ov4fmnJ/kuovku7Y8YnIvPmXvvJr
plK7mt5jmsbDkuovku7Y8YnIvPkHvvJrlj4LmlbAgZyRsc2h6eGUg55qE5Yir5ZCN77yM5Zug5q2kICJBS0UiIOWtl+espiDkuLLo
oajnpLrmiYDmnInnmoTkuovku7Y8YnIvPg==",
      "conf_type": 4,
      "conf_range": "K|E|g|$|l|s|h|z|x|e|A",
      "conf_default": "",
      "conf_cache_version": 7001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    },
    {
      "id": 46,
      "conf_name": "stale_slave_readable",
      "conf_module": 2,
      "conf_desc": "c3RhbGVfc2xhdmVfcmVhZGFibGU=",
      "conf_type": 2,
      "conf_range": "true|false",
      "conf_default": "false",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 47,
      "conf_name": "rqpsquota",
      "conf_module": 2,
      "conf_desc": "cnFwc3F1b3Rh",
      "conf_type": 2,
      "conf_range": "0-20000",
      "conf_default": "20000",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 48,
      "conf_name": "wqpsquota",
      "conf_module": 2,
      "conf_desc": "d3Fwc3F1b3Rh",
      "conf_type": 2,
      "conf_range": "0-10000",
      "conf_default": "10000",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 1
    },
    {
      "id": 49,
      "conf_name": "migrate-batch-rate-limit-mb",
      "conf_module": 1,
      "conf_desc": "cGVnYSBtaWdyYXRlLWJhdGNoLXJhdGUtbGltaXQtbWI=",
      "conf_type": 2,
      "conf_range": "0-1024",
      "conf_default": "4",
      "conf_cache_version": 5001,
      "conf_redis_version": "all",
      "conf_user_visible": 1,
      "need_reboot": 0
    }
  ]
}`
	params := &GetUpdateConfigParams{}
	err := json.Unmarshal([]byte(raw), params)
	if err != nil {
		t.Fatal(err)
	}
	r, err := GetUpdateConfigList(context.Background(), params)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(base_utils.Format(r))
}
