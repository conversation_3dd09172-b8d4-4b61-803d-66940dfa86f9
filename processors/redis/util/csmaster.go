package util

import (
	"context"

	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

const (
	OpTypeNormal       = 0
	OpTypeMasterChange = 3
)

func ModifyCsmasterOpType(ctx context.Context, app *x1model.Application, opType int) error {
	req := &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			OpType: int32(opType),
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		RequiredFields: func() []string {
			if opType == 0 {
				return []string{"op_type"}
			}
			return nil
		}(),
	}
	return csmaster.CsmasterOp().UpdateClusterModel(ctx, req)
}
