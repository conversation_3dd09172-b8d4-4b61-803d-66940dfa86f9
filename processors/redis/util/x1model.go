/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/02/18
 * File: x1model.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package util TODO package function desc
package util

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	DefaultBaseDir = "/root"
)

const (
	GetNewNodeForReplacingAction = "replacing"
	GetNewNodeForModifyingAction = "modifying"
)

func GetCacheInstanceType(engine string, role string) int {
	switch engine {
	case x1model.EngineBDRPRedis, x1model.EnginePegaDB, x1model.EngineRedis:
		switch role {
		case x1model.RoleTypeMaster:
			return 3
		case x1model.RoleTypeSlave:
			return 2
		}
	case x1model.EngineBDRPProxy:
		return 0
	case x1model.EngineMc:
		return 4
	}
	return -1
}

func GetMasterRedis(cluster *x1model.Cluster, node *x1model.Node) string {
	if node.Role == x1model.RoleTypeMaster {
		return ""
	}
	var toCreateMaster *x1model.Node
	var inUseMaster *x1model.Node
	for _, n := range FetchAllNodesOfCluster(nil, cluster) {
		if n.Role == x1model.RoleTypeMaster {
			switch n.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = n
			case x1model.NodeOrProxyStatusInUse:
				inUseMaster = n
			}
		}
	}
	if toCreateMaster != nil && inUseMaster != nil {
		// 变更节点规格时出现这样的情况
		if node.Status == x1model.NodeOrProxyStatusToCreate {
			return toCreateMaster.Ip
		}
		return inUseMaster.Ip
	} else if toCreateMaster != nil && inUseMaster == nil {
		// 创建实例
		return toCreateMaster.Ip
	} else if toCreateMaster == nil && inUseMaster != nil {
		// 其他情况
		return inUseMaster.Ip
	} else {
		return ""
	}
}

func GetMasterRedisForRoNode(cluster *x1model.Cluster, roNode *x1model.RoNode) string {
	if roNode.Role == x1model.RoleTypeMaster {
		return ""
	}
	var toCreateMaster *x1model.Node
	var inUseMaster *x1model.Node
	for _, n := range FetchAllNodesOfCluster(nil, cluster) {
		if n.Role == x1model.RoleTypeMaster {
			switch n.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = n
			case x1model.NodeOrProxyStatusInUse:
				inUseMaster = n
			}
		}
	}
	if toCreateMaster != nil && inUseMaster != nil {
		// 变更节点规格时出现这样的情况
		if roNode.Status == x1model.NodeOrProxyStatusToCreate {
			return toCreateMaster.Ip
		}
		return inUseMaster.Ip
	} else if toCreateMaster != nil && inUseMaster == nil {
		// 创建实例
		return toCreateMaster.Ip
	} else if toCreateMaster == nil && inUseMaster != nil {
		// 其他情况
		return inUseMaster.Ip
	} else {
		return ""
	}
}

func GetSlaveRedis(cluster *x1model.Cluster, node *x1model.Node) string {
	if node.Role == x1model.RoleTypeSlave {
		return ""
	}
	slaveIps := []string{}
	var toCreateMaster *x1model.Node
	var inUseMaster *x1model.Node
	for _, n := range FetchAllNodesOfCluster(nil, cluster) {
		if n.Role == x1model.RoleTypeMaster {
			switch n.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = n
			case x1model.NodeOrProxyStatusInUse:
				inUseMaster = n
			}
		}
	}
	if toCreateMaster != nil && inUseMaster != nil {
		// 变更节点规格时出现这样的情况
		for _, n := range FetchAllNodesOfCluster(nil, cluster) {
			if n.Status == node.Status && n.Role == x1model.RoleTypeSlave {
				slaveIps = append(slaveIps, n.Ip)
			}
		}
	} else if toCreateMaster != nil && inUseMaster == nil {
		// 创建实例
		for _, n := range FetchAllNodesOfCluster(nil, cluster) {
			if n.Role == x1model.RoleTypeSlave {
				slaveIps = append(slaveIps, n.Ip)
			}
		}
	} else if toCreateMaster == nil && inUseMaster != nil {
		// 其他情况
		for _, n := range FetchAllNodesOfCluster(nil, cluster) {
			if (n.Status == x1model.NodeOrProxyStatusInUse || n.Status == x1model.NodeOrProxyStatusToCreate) && n.Role == x1model.RoleTypeSlave {
				slaveIps = append(slaveIps, n.Ip)
			}
		}
	} else {
		return ""
	}
	return strings.Join(slaveIps, ",")
}

func GetHashName(clusterId string, appName string) string {
	clusterIdChunks := strings.Split(clusterId, "-")
	idx := clusterIdChunks[len(clusterIdChunks)-1]
	return appName + "_" + idx
}

func GetHashID(cluster *x1model.Cluster) string {
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			return node.ResourceId
		}
	}
	return ""
}

func GetResFlavor(cluster *x1model.Cluster) string {
	return strings.Join([]string{
		strconv.Itoa(cluster.Cpu),
		strconv.Itoa(cluster.MemSize * 1024),
		strconv.Itoa(cluster.SysDiskSize),
		strconv.Itoa(int(cluster.DiskSize)),
	}, "_")
}

func GetProxyResFlavor(itf *x1model.Interface) string {
	return strings.Join([]string{
		strconv.Itoa(itf.Cpu),
		strconv.Itoa(itf.ActualMemSize * 1024),
		strconv.Itoa(itf.SysDiskSize),
		strconv.Itoa(int(itf.DiskSize)),
	}, "_")
}

func GetCsmasterNodeCuster(cnodes []*csdk.CsmasterInstance, node *x1model.Node) (*csdk.CsmasterInstance, error) {
	for _, cnode := range cnodes {
		if cnode.FixIp == node.Ip && cnode.Port == int32(node.Port) {
			return cnode, nil
		}
	}
	return nil, cerrs.ErrNotFound.Errorf("csmaster node not found for x1node %s", node.NodeId)
}

func GetCsmasterNodeProxy(cnodes []*csdk.CsmasterInstance, proxy *x1model.Proxy) (*csdk.CsmasterInstance, error) {
	for _, cnode := range cnodes {
		if cnode.FixIp == proxy.Ip && cnode.Port == int32(proxy.Port) {
			return cnode, nil
		}
	}
	return nil, cerrs.ErrNotFound.Errorf("csmaster proxy not found for x1 proxy %s", proxy.ProxyId)
}

func checkNodeFixId(app *x1model.Application, nodeFixId string, clusterID string) (bool, error) {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeFixID == nodeFixId {
				// 双保险，防止把其他分片的node影响
				if node.ClusterId != clusterID {
					return false, cerrs.Errorf("nodefixId %s clusterID is node match node %s", nodeFixId, node.NodeId)
				}
				switch node.Status {
				case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
					continue
				case x1model.NodeOrProxyStatusToCreate:
					return false, nil
				default:
					return false, cerrs.Errorf("nodefixid %s is used by inuse node %s", nodeFixId, node.NodeId)
				}
			}
		}
	}
	return true, nil
}

func checkProxyFixId(app *x1model.Application, proxyFixId string) (bool, error) {
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.NodeFixID == proxyFixId {
				switch proxy.Status {
				case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
					continue
				case x1model.NodeOrProxyStatusToCreate:
					return false, nil
				default:
					return false, cerrs.Errorf("proxyfixid %s is used by inuse proxy %s", proxyFixId, proxy.ProxyId)
				}
			}
		}
	}
	return true, nil
}

func AddNewNodesForReplacing(ctx context.Context, app *x1model.Application, action string, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserId, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	nodeIdx, err := GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node max index failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			nodeIdx++

			var role string
			switch action {
			case GetNewNodeForReplacingAction:
				role = x1model.RoleTypeSlave
				if node.Role == x1model.RoleTypeMaster && len(cluster.Nodes) == 1 {
					role = x1model.RoleTypeMaster
				}
			case GetNewNodeForModifyingAction:
				role = node.Role
			default:
				return cerrs.ErrInvalidParams.Errorf("invalid action %s", action)
			}

			resource.LoggerTask.Notice(ctx, "add new node for replacing", logit.String("node_id", node.NodeId), logit.String("role", role))

			needNew, err := checkNodeFixId(app, node.NodeFixID, node.ClusterId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check node fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of node %s had been created", node.NodeId))
				continue
			}

			newNode := &x1model.Node{
				NodeId:        cluster.ClusterId + "." + strconv.Itoa(nodeIdx),
				ClusterId:     cluster.ClusterId,
				AppId:         app.AppId,
				Engine:        cluster.Engine,
				EngineVersion: cluster.EngineVersion,
				Port:          cluster.Port,
				Region:        node.Region,
				LogicZone:     node.LogicZone,
				Azone:         node.Azone,
				Role:          role,
				VpcId:         node.VpcId,
				SubnetId:      node.SubnetId,
				Pool:          node.Pool,
				XagentPort:    x1model.DefaultXagentPort,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       DefaultBaseDir,
				NodeFixID:     node.NodeFixID,
			}

			// 指定了子网，将新 node 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newNode.SubnetId = subnetID
				newNode.Azone = azone
				newNode.LogicZone = logicZone
			}
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("create new node %s replace of old node %s", newNode.NodeId, node.NodeId))
			cluster.Nodes = append(cluster.Nodes, newNode)
		}
	}
	return nil
}

func AddNewProxyForReplacing(ctx context.Context, app *x1model.Application, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserId, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	proxyIdx, err := GetMaxProxyIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy max index failed", logit.Error("error", err))
		return err
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToDelete && proxy.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			proxyIdx++
			needNew, err := checkProxyFixId(app, proxy.NodeFixID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check proxy fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of proxy %s had been created", proxy.ProxyId))
				continue
			}

			newProxy := &x1model.Proxy{
				ProxyId:          itf.InterfaceId + "." + strconv.Itoa(proxyIdx),
				InterfaceId:      itf.InterfaceId,
				AppId:            app.AppId,
				Engine:           itf.Engine,
				EngineVersion:    itf.EngineVersion,
				Port:             itf.Port,
				Region:           proxy.Region,
				LogicZone:        proxy.LogicZone,
				Azone:            proxy.Azone,
				VpcId:            proxy.VpcId,
				SubnetId:         proxy.SubnetId,
				XagentPort:       x1model.DefaultXagentPort,
				Status:           x1model.NodeOrProxyStatusToCreate,
				Basedir:          proxy.Basedir,
				McpackPort:       itf.Port + 1,
				StatPort:         22222,
				NodeFixID:        proxy.NodeFixID,
				BcmInstanceGroup: proxy.BcmInstanceGroup,
			}

			// 指定了子网，将新 proxy 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newProxy.SubnetId = subnetID
				newProxy.Azone = azone
				newProxy.LogicZone = logicZone
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("create new proxy %s replace of old proxy %s", newProxy.ProxyId, proxy.ProxyId))
			itf.Proxys = append(itf.Proxys, newProxy)
		}
	}
	return nil
}

// AddNewRoNodesForReplacing will add new node for ro replacing
func AddNewRoNodesForReplacing(ctx context.Context, app *x1model.Application, action string, subnetID string) error {
	// 解析 subnetID 获取 Azone & LogicZone, 若传 "" 空字符串，则使用原节点信息进行创建
	azone, logicZone := "", ""
	if len(subnetID) != 0 {
		azoneTmp, logicZoneTmp, err := GetZoneBySubnetID(ctx, app.UserId, subnetID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get zone by subnet failed", logit.Error("error", err))
			return err
		}
		azone = azoneTmp
		logicZone = logicZoneTmp
	}

	nodeIdx, err := GetMaxNodeIndex(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node max index failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			nodeIdx++

			var role string
			switch action {
			case GetNewNodeForReplacingAction:
				role = x1model.RoleTypeSlave
				if node.Role == x1model.RoleTypeMaster && len(cluster.Nodes) == 1 {
					role = x1model.RoleTypeMaster
				}
			case GetNewNodeForModifyingAction:
				role = node.Role
			default:
				return cerrs.ErrInvalidParams.Errorf("invalid action %s", action)
			}

			needNew, err := checkNodeFixId(app, node.NodeFixID, node.ClusterId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check node fix id failed", logit.Error("error", err))
				return err
			}
			if !needNew {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("replacement of node %s had been created", node.NodeId))
				continue
			}

			newNode := &x1model.RoNode{
				NodeId:           app.Clusters[0].ClusterId + "." + strconv.Itoa(nodeIdx),
				ClusterId:        app.Clusters[0].ClusterId,
				AppId:            app.AppId,
				Engine:           app.Clusters[0].Engine,
				EngineVersion:    app.Clusters[0].EngineVersion,
				Port:             app.Clusters[0].Port,
				Region:           node.Region,
				LogicZone:        node.LogicZone,
				Azone:            node.Azone,
				Role:             role,
				VpcId:            node.VpcId,
				SubnetId:         node.SubnetId,
				Pool:             node.Pool,
				XagentPort:       x1model.DefaultXagentPort,
				Status:           x1model.NodeOrProxyStatusToCreate,
				Basedir:          DefaultBaseDir,
				NodeFixID:        node.NodeFixID,
				RoGroupID:        node.RoGroupID,
				RoGroupStatus:    node.RoGroupStatus,
				RoGroupWeight:    node.RoGroupWeight,
				BcmInstanceGroup: node.BcmInstanceGroup,
			}
			// 指定了子网，将新 node 的 subnetid/azone/logiczone 进行更换
			if len(subnetID) != 0 {
				newNode.SubnetId = subnetID
				newNode.Azone = azone
				newNode.LogicZone = logicZone
			}
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("create new node %s replace of old node %s", newNode.NodeId, node.NodeId))
			cluster.RoNodes = append(cluster.RoNodes, newNode)
		}
	}
	return nil
}

func IsSingleReplica(ctx context.Context, cluster *x1model.Cluster) bool {
	if cluster == nil {
		return false
	}
	return len(cluster.Nodes) == 1
}

// FetchAllNodesOfCluster return all nodes of cluster include roNode
func FetchAllNodesOfCluster(ctx context.Context, cluster *x1model.Cluster) []*x1model.Node {
	nodes := make([]*x1model.Node, 0)
	for _, node := range cluster.Nodes {
		nodes = append(nodes, node)
	}
	for _, node := range cluster.RoNodes {
		n := &x1model.Node{
			Id:               node.Id,
			AppId:            node.AppId,
			ClusterId:        node.ClusterId,
			NodeId:           node.NodeId,
			ContainerId:      node.ContainerId,
			Engine:           node.Engine,
			EngineVersion:    node.EngineVersion,
			Port:             node.Port,
			Basedir:          node.Basedir,
			Datadir:          node.Datadir,
			InnodbBufferSize: node.InnodbBufferSize,
			Ip:               node.Ip,
			XagentPort:       node.XagentPort,
			Region:           node.Region,
			LogicZone:        node.LogicZone,
			Azone:            node.Azone,
			VpcId:            node.VpcId,
			SubnetId:         node.SubnetId,
			Pool:             node.Pool,
			Tags:             node.Tags,
			Role:             node.Role,
			DestRole:         node.DestRole,
			Status:           node.Status,
			DestStatus:       node.DestStatus,
			TaskId:           node.TaskId,
			Properties:       node.Properties,
			ResourceOrderId:  node.ResourceOrderId,
			ResourceId:       node.ResourceId,
			FloatingIP:       node.FloatingIP,
			IPv6:             node.IPv6,
			RootPassword:     node.RootPassword,
			NodeShortID:      node.NodeShortID,
			HostName:         node.HostName,
			NodeFixID:        node.NodeFixID,
			TempFlags:        node.TempFlags,
			GlobalID:         node.GlobalID,
			GlobalSeqID:      node.GlobalSeqID,
		}
		nodes = append(nodes, n)
	}
	return nodes
}

// ChangeRoNode2Node will return one node
func ChangeRoNode2Node(node *x1model.RoNode) *x1model.Node {
	return &x1model.Node{
		Id:               node.Id,
		AppId:            node.AppId,
		ClusterId:        node.ClusterId,
		NodeId:           node.NodeId,
		ContainerId:      node.ContainerId,
		Engine:           node.Engine,
		EngineVersion:    node.EngineVersion,
		Port:             node.Port,
		Basedir:          node.Basedir,
		Datadir:          node.Datadir,
		InnodbBufferSize: node.InnodbBufferSize,
		Ip:               node.Ip,
		XagentPort:       node.XagentPort,
		Region:           node.Region,
		LogicZone:        node.LogicZone,
		Azone:            node.Azone,
		VpcId:            node.VpcId,
		SubnetId:         node.SubnetId,
		Pool:             node.Pool,
		Tags:             node.Tags,
		Role:             node.Role,
		DestRole:         node.DestRole,
		Status:           node.Status,
		DestStatus:       node.DestStatus,
		TaskId:           node.TaskId,
		Properties:       node.Properties,
		ResourceOrderId:  node.ResourceOrderId,
		ResourceId:       node.ResourceId,
		FloatingIP:       node.FloatingIP,
		IPv6:             node.IPv6,
		RootPassword:     node.RootPassword,
		NodeShortID:      node.NodeShortID,
		HostName:         node.HostName,
		NodeFixID:        node.NodeFixID,
		TempFlags:        node.TempFlags,
		GlobalID:         node.GlobalID,
		GlobalSeqID:      node.GlobalSeqID,
	}
}

// GetRoGroupID return rogroup id
func GetRoGroupID(node *x1model.RoNode) int64 {
	rogroupID := node.RoGroupID
	rogroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(nil, rogroupID)
	if err != nil {
		return 0
	}
	return rogroup.ID
}

func GetEngineVersion(app *x1model.Application) string {
	engineVersion := ""
	for _, cluster := range app.Clusters {
		engineVersion = cluster.EngineVersion
		if len(engineVersion) != 0 {
			break
		}
	}
	return engineVersion
}

func GetEngine(app *x1model.Application) string {
	engine := ""
	for _, cluster := range app.Clusters {
		engine = cluster.Engine
		break
	}
	return engine
}

// GetNodeByNodeID 返回根据节点id获取节点信息，不可用于修改操作
func GetNodeByNodeID(ctx context.Context, nodeID string) (*x1model.Node, error) {
	var roNode *x1model.RoNode
	node, err := x1model.NodeGetByNodeId(ctx, nodeID)
	if err != nil {
		// 查询是否是只读节点
		roNode, err = x1model.RoNodeGetByNodeID(ctx, nodeID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", nodeID),
				logit.Error("dbError", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
		if roNode == nil {
			resource.LoggerTask.Warning(ctx, "get ro node fail", logit.String("nodeId", nodeID),
				logit.Error("dbError", err))
			return nil, cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	if node == nil && roNode == nil {
		resource.LoggerTask.Warning(ctx, "node not found", logit.String("nodeId", nodeID))
		return nil, cerrs.ErrNotFound.Errorf("node(%s) not found", nodeID)
	}

	if node != nil {
		return node, nil
	}

	return ChangeRoNode2Node(roNode), nil
}

func LockForX1modelModify(ctx context.Context, appID string) (func(), error) {
	resource.LoggerTask.Trace(ctx, "x1model try to lock", logit.String("appID", appID))
	unlock, err := lock.BlockLock(ctx, "save_app_"+appID, 30*time.Second, 30*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "x1model lock fail", logit.String("appID", appID),
			logit.Error("lockError", err))
		return nil, err
	}
	resource.LoggerTask.Trace(ctx, "x1model lock success", logit.String("appID", appID))
	return func() {
		resource.LoggerTask.Trace(ctx, "x1model unlock", logit.String("appID", appID))
		unlock()
	}, err
}

func GetRedisCryptedPassword(ctx context.Context, app *x1model.Application) (string, error) {
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		return "", errors.Errorf("get acl fail,err:%s", err.Error())
	}
	password := ""
	if acl != nil && len(acl.Password) != 0 {
		password = acl.Password
	}
	return password, nil
}

func GetRedisDecryptedPassword(ctx context.Context, app *x1model.Application) (string, error) {
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		return "", errors.Errorf("get acl fail,err:%s", err.Error())
	}

	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	return password, nil
}

func GetMasterNodeInUse(ctx context.Context, cluster *x1model.Cluster) *x1model.Node {
	return GetMasterNodeOfCluster(ctx, cluster, x1model.NodeOrProxyStatusInUse)
}

// GetMasterNodeOfCluster, 获取集群中主节点
// 主节点状态可以为 inuse 和 tocreate, 两种均存在时根据 statusWhenDup 进行选择
func GetMasterNodeOfCluster(ctx context.Context, cluster *x1model.Cluster, statusWhenDup string) *x1model.Node {
	var toCreateMaster *x1model.Node
	var inUseMaster *x1model.Node
	for _, n := range FetchAllNodesOfCluster(ctx, cluster) {
		if n.Role == x1model.RoleTypeMaster {
			switch n.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = n
			case x1model.NodeOrProxyStatusInUse:
				inUseMaster = n
			}
		}
	}
	if toCreateMaster != nil && inUseMaster != nil {
		// 变更节点规格时出现这样的情况
		if statusWhenDup == x1model.NodeOrProxyStatusToCreate {
			return toCreateMaster
		}
		return inUseMaster
	} else if toCreateMaster != nil && inUseMaster == nil {
		// 创建实例
		return toCreateMaster
	} else if toCreateMaster == nil && inUseMaster != nil {
		// 其他情况
		return inUseMaster
	} else {
		return nil
	}
}
