/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/07/25 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file check_mcpack_user.go
 * <AUTHOR>
 * @date 2022/07/25 15:30:51
 * @brief check mcpack user
 *
 **/

package util

import "icode.baidu.com/baidu/scs/x1-task/utils/conf"

// UseMcpackProtocol will return true if user in mcpackUserList
func UseMcpackProtocol(iamUserID string) bool {
	for _, user := range conf.ScsMainConf.McpackUserList {
		if user == iamUserID {
			return true
		}
	}
	return false
}
