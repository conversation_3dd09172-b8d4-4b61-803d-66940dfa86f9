package util

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

var defaultDisableCommands = map[string]bool{
	"SHUTDOWN":     true,
	"MIGRATE":      true,
	"BGREWRITEAOF": true,
	"CONFIG":       true,
	"DEBUG":        true,
	"LASTSAVE":     true,
	"SAVE":         true,
	"SYNC":         true,
	"BGSAVE":       true,
	"SLAVEOF":      true,
}

var defaultDisableCommandsPega = map[string]bool{
//	"SLAVEOF":      true,
}

func GetVersion(app *x1model.Application) string {
	_, version := GetImageIdAndVersion(app.ImageID)
	return version
}

func GetDisableCommands(ctx context.Context, appID string) (string, error) {
	model, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appID)
	if err != nil {
		return "", err
	}
	confs, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(model.Id))
	if err != nil {
		return "", err
	}
	disableCommands := map[string]bool{}
	isPega := model.StoreType == 3
	disableCommandConfName := "disable_commands"
	if !isPega {
		for cmd := range defaultDisableCommands {
			disableCommands[cmd] = true
		}
	} else {
		for cmd := range defaultDisableCommandsPega {
			disableCommands[cmd] = true
		}
		disableCommandConfName = "disable_commands_pega"
	}
	needDefault := true
	for _, conf := range confs {
		if conf.ConfName == disableCommandConfName {
			needDefault = false
			resource.LoggerTask.Trace(ctx, "csmaster disable cmd get suc", logit.String("cmds", conf.Value))
			configCmds := strings.Split(conf.Value, ",")
			for _, cmd := range configCmds {
				cmd = strings.ToUpper(strings.TrimSpace(cmd))
				disableCommands[cmd] = true
			}
			break
		}
	}
	// flushall,flushdb
	if !isPega {
		if needDefault {
			disableCommands[strings.ToUpper("flushdb")] = true
			disableCommands[strings.ToUpper("flushall")] = true
		}
	}

	cmdList := ""
	for cmd := range disableCommands {
		if len(cmdList) != 0 {
			cmdList += ","
		}
		cmdList += cmd
	}

	return cmdList, nil
}
