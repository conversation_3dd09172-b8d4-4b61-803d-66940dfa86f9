package util

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	OrderOperating int = iota
	OrderSuccess
	OrderFailed
	OrderDeleted
	OrderPrecreate
)

type OrderModel struct {
	ID          int       `json:"id" gorm:"column:id"`
	OrderID     string    `json:"orderId" gorm:"column:order_id"`
	Status      int8      `json:"status" gorm:"column:status"`
	CreateTime  time.Time `json:"createTime" gorm:"column:create_time"`
	IamUserID   string    `json:"iamUserId" gorm:"column:iam_user_id"`
	ClusterName string    `json:"clusterName" gorm:"column:cluster_name"`
	EngineType  int8      `json:"engineType" gorm:"column:engine_type"`
	ClusterNum  int       `json:"clusterNum" gorm:"column:cluster_num"`
	InstanceNum int       `json:"instanceNum" gorm:"column:instance_num"`
	Port        int       `json:"port" gorm:"column:port"`
	Flavor      int       `json:"flavor" gorm:"column:flavor"`
	Persistence int       `json:"persistence" gorm:"column:persistence"`
	ErrorCode   int       `json:"errorCode" gorm:"column:error_code"`
	Action      int       `json:"action" gorm:"column:action"`
	ClusterType string    `json:"clusterType" gorm:"column:cluster_type"`
	TaskIDs     string    `json:"taskIds" gorm:"column:task_ids"`
	Parameters  string    `json:"parameters" gorm:"column:parameters"`
}

func (m OrderModel) TableName() string {
	return "order_list"
}

func UpdateOrderStatus(ctx context.Context, orderModel *OrderModel) error {
	unlock, err := lock.BlockLock(ctx, "order_lock"+orderModel.OrderID, 30*time.Second, 30*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "block lock for order failed", logit.Error("err", err))
		return err
	}
	defer unlock()
	resource.LoggerTask.Notice(ctx, "check and update order status", logit.String("orderID", orderModel.OrderID))
	tasks, err := resource.TaskOperator.RetrieveTasks(ctx, "task_id IN ?", strings.Split(orderModel.TaskIDs, ","))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get tasks failed", logit.Error("err", err))
		return err
	}
	if len(tasks) != orderModel.InstanceNum {
		resource.LoggerTask.Notice(ctx, "task num not match", logit.Int("taskNum", len(tasks)),
			logit.Int("instanceNum", orderModel.InstanceNum))
		return nil
	}
	succCount := 0
	for _, t := range tasks {
		if t.Status == iface.TaskStatusError {
			orderModel.Status = int8(OrderFailed)
			break
		} else if t.Status == iface.TaskStatusSuccess {
			succCount++
		} else {
			break
		}
	}
	if len(tasks) == succCount {
		orderModel.Status = int8(OrderSuccess)
	}
	if orderModel.Status != int8(OrderOperating) {
		if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, []*OrderModel{orderModel}); err != nil {
			resource.LoggerTask.Warning(ctx, "save order model failed", logit.Error("err", err))
			return err
		}
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("update order status to %d", orderModel.Status), logit.String("orderID", orderModel.OrderID))
	}
	return nil
}

func UpdateRunningOrdersStatus(ctx context.Context) error {
	unlock, err := lock.BlockLock(ctx, "check_running_orders_status", 60*time.Second, 60*time.Second)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "block lock for check running orders failed", logit.Error("err", err))
		return err
	}
	defer unlock()
	var orderModels []*OrderModel
	if err := resource.CsmasterModel.GetAllByCond(ctx, &orderModels, "status = ?", OrderOperating); err != nil {
		resource.LoggerTask.Warning(ctx, "get order models failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "check running orders status", logit.Int("orderNum", len(orderModels)))
	if len(orderModels) <= 0 {
		return nil
	}
	g := gtask.Group{Concurrent: 50, AllowSomeFail: true}
	for _, orderModel := range orderModels {
		// 使用重构后api创建的订单,Parameters不为空
		if orderModel.Parameters == "" {
			continue
		}
		orderModel := orderModel
		g.Go(func() error {
			return UpdateOrderStatus(ctx, orderModel)
		})
	}
	errCount, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update order status failed", logit.Error("err", err), logit.Int("errCount", errCount))
		return err
	}
	return nil
}

func RunUpdateRunningOrdersStatus(ctx context.Context) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)
			_ = UpdateRunningOrdersStatus(ctx)
		}
	}
}
