package util

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func GetMaxNodeIndex(ctx context.Context, app *x1model.Application) (int, error) {
	index := 0
	for _, cluster := range app.Clusters {
		for _, node := range FetchAllNodesOfCluster(ctx, cluster) {
			chunks := strings.Split(node.NodeId, ".")
			curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
			if err != nil {
				return 0, fmt.Errorf("extract node index from id %s failed, err:%s", node.NodeId, err.Error())
			}
			if curIdx > index {
				index = curIdx
			}
		}
	}
	return index, nil
}

func GetMaxProxyIndex(ctx context.Context, app *x1model.Application) (int, error) {
	index := 0
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			chunks := strings.Split(proxy.ProxyId, ".")
			curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
			if err != nil {
				return 0, fmt.Errorf("extract node index from id %s failed, err:%s", proxy.ProxyId, err.Error())
			}
			if curIdx > index {
				index = curIdx
			}
		}
	}
	return index, nil
}

// FindClusterByShortID
// 遍历app中的shard，返回要处理（cluster.ClusterShortID == shortId）的shard
func FindClusterByShortID(ctx context.Context, app *x1model.Application, shortId int64) (*x1model.Cluster, error) {
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == int(shortId) {
			return cluster, nil
		}
	}
	return nil, fmt.Errorf("cannot find cluster by short id %d", shortId)
}

// FindNodeByShortID
// 遍历shard中的node（一般为redis或pega），返回要处理（node.NodeShortID == shortId）的node
func FindNodeByShortID(ctx context.Context, cluster *x1model.Cluster, shortId int64) (*x1model.Node, error) {
	for _, node := range cluster.Nodes {
		if node.NodeShortID == int(shortId) {
			return node, nil
		}
	}
	return nil, fmt.Errorf("cannot find node by short id %d", shortId)
}

func GetMaxInterfaceIdx(ctx context.Context, itfs []*x1model.Interface) (int, error) {
	index := 0
	for _, itf := range itfs {
		chunks := strings.Split(itf.InterfaceId, "-")
		curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
		if err != nil {
			return 0, fmt.Errorf("extract interface index from id %s failed, err:%s", itf.InterfaceId, err.Error())
		}
		if curIdx > index {
			index = curIdx
		}
	}
	return index, nil
}

func GetMaxClusterIdx(ctx context.Context, clusters []*x1model.Cluster) (int, error) {
	index := 0
	for _, cluster := range clusters {
		chunks := strings.Split(cluster.ClusterId, "-")
		curIdx, err := strconv.Atoi(chunks[len(chunks)-1])
		if err != nil {
			return 0, fmt.Errorf("extract interface index from id %s failed, err:%s", cluster.ClusterId, err.Error())
		}
		if curIdx > index {
			index = curIdx
		}
	}
	return index, nil
}
