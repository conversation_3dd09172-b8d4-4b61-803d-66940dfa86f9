package util

import (
	"bytes"
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/xmaster"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

const (
	AppAttributeKeyClusterType        = "cluster_type"
	AppAttributeValueClusterTypeShard = "cluster_shard"
	AppAttributeValueClusterTypeApp   = "cluster_app"

	AppAttributeKeyShardIdList = "shard_id_list"
	AppAttributeKeyUserID      = "user_id"

	InstanceAttributeKeyClusterType = "role"
	InstanceAttributeValueMaster    = "master"
	InstanceAttributeValueSlave     = "slave"
	InstanceAttributeValueProxy     = "proxy"

	AppMonitorNameAlive          = "instance_alive"
	AppMonitorNamePingAvailable  = "instance_ping_available"
	AppMonitorNameTopologySlave  = "instance_topology_slave"
	AppMonitorNameLogicAvailable = "instance_logic_available"
)

var (
	XmasterEndpoint = map[string]string{
		"default":       "10.136.153.50:8878",
		"test":          "10.136.153.50:8878",
		"minicloud":     "10.136.153.50:8878",
		"testnew":       "10.136.153.50:8878",
		"preonline":     "*************:8900",
		"bjtest":        "*************:7401",
		"onlinebj":      "scs.bj.baidubce.com:8900",
		"onlinebd":      "scs.bd.baidubce.com:8900",
		"onlinesin":     "*************:8900",
		"onlinebdfsg":   "scs.hb-fsg.baidubce.com:8900",
		"onlinebjfsg":   "scs.bjfsg.baidubce.com:8900",
		"onlinefsh":     "scs.fsh.baidubce.com:8900",
		"onlinegz":      "scs.gz.baidubce.com:8900",
		"onlinehkg":     "scs.hkg.baidubce.com:8900",
		"onlinesu":      "scs.su.baidubce.com:8900",
		"onlinesuvip":   "scsvip01.su.baidubce.com:8900",
		"onlinewh":      "scs.fwh.baidubce.com:8900",
		"onlineyq":      "scs.yq.baidubce.com:8900",
		"onlinecd":      "scs.cd.baidubce.com:8900",
		"onlinenj":      "scs.nj.baidubce.com:8900",
		"licloudontest": "***********:8900",
		"licloudprod":   "***********:8900",
		"dbstack":       "*************:8900",
	}
)

func GetXmasterEndpoint() string {
	if env.IDC() == conf.XmasterHAConfIns.IdcName {
		return conf.XmasterHAConfIns.XmasterEndpoint
	}
	if endpoint, ok := XmasterEndpoint[env.IDC()]; ok {
		return endpoint
	}
	return XmasterEndpoint["default"]
}

// getMasterNodeOfCluster, 获取指定分片主节点，注意热活场景返回的主节点数据结构不完整，只填充了ip、port信息
func getMasterNodeOfCluster(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) *x1model.Node {
	if len(app.AppGroupID) != 0 {
		return getMasterNodeOfClusterGlobal(ctx, app, cluster)
	}
	return getMasterNodeOfClusterLocal(ctx, app, cluster)
}

func getMasterNodeOfClusterLocal(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) *x1model.Node {
	return GetMasterNodeOfCluster(ctx, cluster, x1model.NodeOrProxyStatusInUse)
}

// getMasterNodeOfClusterGlobal, 获取热活实例指定分片的主节点，注意这里节点数据结构不是完整的，只填充了ip、port信息
func getMasterNodeOfClusterGlobal(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) *x1model.Node {
	if len(app.AppGroupID) == 0 {
		return nil
	}

	globalNodes, err := gmaster.GlobalMasterOp().GetNodes(ctx, &gmaster.GetNodesParams{
		UserID:        app.UserId,
		AppGroupID:    app.AppGroupID,
		ShardGlobalID: cluster.GlobalID,
		WithoutLock:   true,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get nodes from global failed",
			logit.Error("err", err),
			logit.String("app_id", app.AppId))
		return nil
	}
	if len(globalNodes) == 0 {
		resource.LoggerTask.Warning(ctx, "nodes is empty from global",
			logit.String("app_id", app.AppId))
		return nil
	}
	var master *x1model.Node
	for _, gNode := range globalNodes {
		if gNode.ShardId != cluster.GlobalID {
			continue
		}
		if gNode.Role == x1model.RoleTypeMaster {
			master = &x1model.Node{
				AppId:      app.AppId,
				Ip:         gNode.Ip,
				FloatingIP: gNode.FloatingIp,
				Port:       gNode.Port,
				Role:       gNode.Role,
				Status:     gNode.Status,
			}
			break
		}
	}
	return master
}

func BuildXMaterClusterTopology(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) *xmaster.ClusterTopology {
	var masterIP, masterPort string
	master := getMasterNodeOfCluster(ctx, app, cluster)
	if master != nil {
		masterIP = master.Ip
		masterPort = base_utils.ToString(master.Port)
	}

	instances := make([]*xmaster.Instance, 0)
	for _, node := range cluster.Nodes {
		if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete && node.NodeShortID != 0 {
			instance := &xmaster.Instance{
				InstanceId:     node.NodeId,
				LastActiveTime: time.Now().Unix(),
				Attributes: map[string]string{
					"node_short_id": base_utils.ToString(node.NodeShortID),
					"role":          node.Role,
					"is_read_only":  base_utils.ToString(false),
					"master_ip":     masterIP, // 主从节点均添加主节点信息；便于一致性检查；主节点目前用不到该信息；
					"master_port":   masterPort,
				},
			}
			instances = append(instances, instance)
		}
	}

	/*
	   说明：热活实例，只读节点的master信息，从本地域获取。
	   原因：
	       含只读节点的实例加入热活时，只读节点仍然挂在原来的主节点上，形成级联；
	       后续重启或替换节点时，对于从地域，只读节点挂载到当前地域第一个节点上；对于主地域，直接挂载到主节点上；
	*/
	roNodeMasterIpMap := make(map[int64]string)
	nodeIpPortMap := make(map[string]int32)
	if len(app.AppGroupID) != 0 && len(cluster.RoNodes) > 0 {
		csmasterInstances, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get csmasterInstances failed", logit.Error("error", err))
		} else {
			for _, csmasterInstance := range csmasterInstances {
				if csmasterInstance.ShardId != int64(cluster.ClusterShortID) {
					continue
				}
				if csmasterInstance.IsReadOnly != 1 {
					nodeIpPortMap[csmasterInstance.FixIp] = csmasterInstance.Port
				} else {
					roNodeMasterIpMap[csmasterInstance.Id] = csmasterInstance.MasterRedis
				}
			}
			resource.LoggerTask.Notice(ctx, "get csmasterInstances from csmaster",
				logit.String("roNodeMasterIpMap", base_utils.Format(roNodeMasterIpMap)),
				logit.String("nodeIpPortMap", base_utils.Format(nodeIpPortMap)),
			)
		}
	}

	for _, node := range cluster.RoNodes {
		curRoNodeMasterIp := masterIP
		curRoNodeMasterPort := masterPort
		if ip, ok := roNodeMasterIpMap[int64(node.NodeShortID)]; ok && len(ip) > 0 {
			curRoNodeMasterIp = ip
			if port, ok := nodeIpPortMap[curRoNodeMasterIp]; ok && port > 0 {
				curRoNodeMasterPort = base_utils.ToString(port)
			}
		}
		if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete && node.NodeShortID != 0 {
			instance := &xmaster.Instance{
				InstanceId:     node.NodeId,
				LastActiveTime: time.Now().Unix(),
				Attributes: map[string]string{
					"node_short_id": base_utils.ToString(node.NodeShortID),
					"role":          node.Role,
					"is_read_only":  base_utils.ToString(true),
					"master_ip":     curRoNodeMasterIp,
					"master_port":   curRoNodeMasterPort,
				},
			}
			instances = append(instances, instance)
		}
	}

	t := &xmaster.ClusterTopology{
		Instances: instances,
		Attributes: map[string]string{
			"shard_short_id":           base_utils.ToString(cluster.ClusterShortID),
			"app_id":                   cluster.AppId,
			"region":                   env.IDC(),
			"engine":                   cluster.Engine,
			"type":                     app.Type,
			AppAttributeKeyClusterType: AppAttributeValueClusterTypeShard,
			AppAttributeKeyShardIdList: cluster.ClusterId,
			AppAttributeKeyUserID:      app.UserId,
		},
	}

	return t
}

func BuildXMaterProxyTopology(app *x1model.Application) *xmaster.ClusterTopology {
	instances := make([]*xmaster.Instance, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToDelete && proxy.Status != x1model.NodeOrProxyStatusToFakeDelete && proxy.ProxyShortID != 0 {
				instance := &xmaster.Instance{
					InstanceId:     proxy.ProxyId,
					LastActiveTime: time.Now().Unix(),
					Attributes: map[string]string{
						"node_short_id": base_utils.ToString(proxy.ProxyShortID),
						"role":          InstanceAttributeValueProxy,
						"is_read_only":  base_utils.ToString(false),
					},
				}
				instances = append(instances, instance)
			}
		}
	}

	t := &xmaster.ClusterTopology{
		Instances: instances,
	}

	return t
}

func buildXMaterAppTypeClusterTopology(ctx context.Context, app *x1model.Application) *xmaster.ClusterTopology {
	instances := make([]*xmaster.Instance, 0)
	appEngine := ""
	var appShardIdStr, appShardIdStrRaw string
	var appShardIdStrBuffer bytes.Buffer

	if len(app.Clusters) > 0 {
		appEngine = app.Clusters[0].Engine
	}

	for _, cluster := range app.Clusters {
		topoOfSingleShard := BuildXMaterClusterTopology(ctx, app, cluster)
		if len(topoOfSingleShard.Instances) > 0 {
			// //NOTE: 不再向app-cluster中注册Redis节点的拓扑，后续redis节点仅注册到相应的shard-cluster中，proxy节点统一注册到app-cluster中，app-cluster通过shard_id_list找到同集群的shard-cluster.
			// instances = append(instances, topoOfSingleShard.Instances...)
			appShardIdStrBuffer.WriteString(cluster.ClusterId)
			appShardIdStrBuffer.WriteString(",")
		}
	}

	topoOfProxy := BuildXMaterProxyTopology(app)
	instances = append(instances, topoOfProxy.Instances...)

	appShardIdStrRaw = appShardIdStrBuffer.String()
	if len(appShardIdStrRaw) > 0 {
		appShardIdStr = appShardIdStrRaw[0 : len(appShardIdStrRaw)-1]
	}

	t := &xmaster.ClusterTopology{
		Instances: instances,
		Attributes: map[string]string{
			"shard_short_id":           base_utils.ToString(app.AppShortID), // NOTE: 使用AppShortID作为 app-cluster 的 shard_short_id
			"app_id":                   app.AppId,
			"region":                   env.IDC(),
			"engine":                   appEngine,
			"type":                     app.Type,
			AppAttributeKeyClusterType: AppAttributeValueClusterTypeApp,
			AppAttributeKeyShardIdList: appShardIdStr,
			AppAttributeKeyUserID:      app.UserId,
		},
	}
	return t
}

func CreateXMasterApplicationTopology(ctx context.Context, appId string) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}
	resource.LoggerTask.Notice(ctx, "xmaster query app", logit.String("appId", appId),
		logit.AutoField("app", app))
	// 创建 clusters
	s := xmaster.NewDefaultXmasterSdk()
	for _, cluster := range app.Clusters {
		queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
			ClusterId: cluster.ClusterId,
		})
		resource.LoggerTask.Notice(ctx, "xmaster query cluster", logit.String("appId", appId),
			logit.String("clusterId", cluster.ClusterId),
			logit.AutoField("cluster queryRsp", queryRsp))
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return errors.XmasterQueryFail.Wrap(err)
		}
		if queryRsp.Code != xmaster.HttpErrClusterNotFound {
			continue
		}

		// create
		resource.LoggerTask.Notice(ctx, "xmaster create cluster", logit.String("appId", appId),
			logit.String("clusterId", cluster.ClusterId),
			logit.AutoField("cluster nodes", cluster.Nodes))
		opRsp, err := s.CreateCluster(ctx, &xmaster.CreateClusterRequest{
			ClusterId: cluster.ClusterId,
			Topo:      BuildXMaterClusterTopology(ctx, app, cluster),
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster create cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return errors.XmasterOpFail.Wrap(err)
		}
		if opRsp.Success != 1 {
			resource.LoggerTask.Warning(ctx, "xmaster create cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
			return errors.XmasterOpFail.Errorf("xmaster create cluster fail, code:%s, message:%s",
				opRsp.Code, opRsp.Message)
		}
	}

	if err = PutAppTypeClusterTopology(ctx, appId); err != nil {
		return err
	}

	return nil
}

func UpdateXMasterApplicationTopology(ctx context.Context, appId string) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// create or update clusters
	s := xmaster.NewDefaultXmasterSdk()
	for _, cluster := range app.Clusters {
		queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
			ClusterId: cluster.ClusterId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return errors.XmasterQueryFail.Wrap(err)
		}

		opFunc := s.CreateCluster
		if queryRsp.Code != xmaster.HttpErrClusterNotFound {
			// update
			opFunc = s.UpdateCluster
		}

		// create or update
		opRsp, err := opFunc(ctx, &xmaster.CreateClusterRequest{
			ClusterId: cluster.ClusterId,
			Topo:      BuildXMaterClusterTopology(ctx, app, cluster),
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster create/update cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return errors.XmasterOpFail.Wrap(err)
		}
		if opRsp.Success != 1 {
			resource.LoggerTask.Warning(ctx, "xmaster create/update cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
			return errors.XmasterOpFail.Errorf("xmaster create/update cluster fail, code:%s, message:%s",
				opRsp.Code, opRsp.Message)
		}
	}

	if err = PutAppTypeClusterTopology(ctx, appId); err != nil {
		return err
	}

	return nil
}

func UpdateXMasterApplicationTopologyForShardScaleIn(ctx context.Context, appId string) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// create or update clusters
	s := xmaster.NewDefaultXmasterSdk()
	for _, cluster := range app.Clusters {
		queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
			ClusterId: cluster.ClusterId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return errors.XmasterQueryFail.Wrap(err)
		}

		if queryRsp.Code == xmaster.HttpErrClusterNotFound {
			continue
		}

		shouldDeleteCluster := true
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				shouldDeleteCluster = false
				break
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				shouldDeleteCluster = false
				break
			}
		}
		if !shouldDeleteCluster {
			continue
		}

		// delete
		opRsp, err := s.DeleteCluster(ctx, &xmaster.DeleteClusterRequest{
			ClusterId: cluster.ClusterId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster delete cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return errors.XmasterOpFail.Wrap(err)
		}
		if opRsp.Success != 1 {
			resource.LoggerTask.Warning(ctx, "xmaster delete cluster fail", logit.String("appId", appId),
				logit.String("clusterId", cluster.ClusterId),
				logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
			return errors.XmasterOpFail.Errorf("xmaster delete cluster fail, code:%s, message:%s",
				opRsp.Code, opRsp.Message)
		}
	}

	if err = PutAppTypeClusterTopology(ctx, appId); err != nil {
		return err
	}

	return nil
}

func PutAppTypeClusterTopology(ctx context.Context, appId string) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// create or update clusters
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
		ClusterId: app.AppId, // NOTE: 使用 AppId 作为 app-cluster 的 cluster_id
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("appId", appId),
			logit.String("clusterId", app.AppId),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	opFunc := s.CreateCluster
	if queryRsp.Code != xmaster.HttpErrClusterNotFound {
		opFunc = s.UpdateCluster
	}

	// create or update
	opRsp, err := opFunc(ctx, &xmaster.CreateClusterRequest{
		ClusterId: app.AppId,
		Topo:      buildXMaterAppTypeClusterTopology(ctx, app),
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster create/update cluster fail", logit.String("appId", appId),
			logit.String("clusterId", app.AppId),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster create/update cluster fail", logit.String("appId", appId),
			logit.String("clusterId", app.AppId),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster create/update cluster fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}

	return nil
}

func DeleteXMasterApplicationTopology(ctx context.Context, appId string) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// 删除 clusters topo
	for _, cluster := range app.Clusters {
		if err = DeleteShardTypeClusterTopology(ctx, cluster.ClusterId); err != nil {
			return err
		}
	}

	// 删除 app type cluster topo
	if err = DeleteAppTypeClusterTopology(ctx, appId); err != nil {
		return err
	}

	return nil
}

func DeleteShardTypeClusterTopology(ctx context.Context, clusterId string) error {
	return DeleteClusterTopologyInXmaster(ctx, clusterId)
}

func DeleteAppTypeClusterTopology(ctx context.Context, appId string) error {
	return DeleteClusterTopologyInXmaster(ctx, appId)
}

func DeleteClusterTopologyInXmaster(ctx context.Context, clusterId string) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
		ClusterId: clusterId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster fail", logit.String("clusterId", clusterId), logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}

	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "delete cluster topology, target cluster is not exist ", logit.String("clusterId", clusterId))
		return nil
	}
	// delete
	opRsp, err := s.DeleteCluster(ctx, &xmaster.DeleteClusterRequest{
		ClusterId: clusterId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster delete cluster fail", logit.String("clusterId", clusterId), logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster delete cluster fail", logit.String("clusterId", clusterId), logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster delete cluster fail, code:%s, message:%s", opRsp.Code, opRsp.Message)
	}
	return nil
}

func SetXMasterDefaultMonitorStrategy(ctx context.Context, appId string, forceUpdate bool) error {
	var (
		mc  *xmaster.MonitorConfig
		err error
	)

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// 设置所有 clusters的monitor config
	for _, cluster := range app.Clusters {
		switch cluster.Engine {
		case x1model.EnginePegaDB:
			mc, err = GenXMasterClusterPegaMonitorConfig(ctx, cluster.ClusterId)
		default:
			mc, err = GenXMasterClusterDefaultMonitorConfig(ctx, cluster.ClusterId)
		}
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, get monitor strategy fail", logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return err
		}
		if err = SetXMasterClusterMonitorStrategy(ctx, cluster.ClusterId, mc, forceUpdate); err != nil {
			return err
		}
	}

	// 设置 app的monitor config，默认使用最后一个cluster的monitor config
	if err = SetXMasterClusterMonitorStrategy(ctx, appId, mc, forceUpdate); err != nil {
		return err
	}

	return nil
}

func SetXMasterClusterMonitorStrategy(ctx context.Context, clusterID string, mc *xmaster.MonitorConfig, forceUpdate bool) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetMonitorStrategy(ctx, &xmaster.GetMonitorStrategyRequest{
		ClusterId: clusterID,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query monitor strategy", logit.String("clusterID", clusterID),
		logit.String("clusterID", clusterID),
		logit.AutoField("monitor strategy queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query monitor strategy fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, target cluster is not exist ", logit.String("clusterID", clusterID))
		return nil
	}

	if len(queryRsp.Rules) > 0 && !forceUpdate {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, target cluster has default monitor strategy already ", logit.String("clusterID", clusterID))
		return nil
	}

	opRsp, err := s.SetMonitorStrategy(ctx, &xmaster.SetMonitorStrategyRequest{
		ClusterId: clusterID,
		Rule:      mc,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set monitor fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}

	return nil
}

func GenXMasterClusterDefaultMonitorConfig(ctx context.Context, clusterId string) (*xmaster.MonitorConfig, error) {
	var (
		err            error
		defaultRuleStr = conf.XmasterHAConfIns.DefaultMonitorRules
		defaultRules   = make([]*xmaster.MonitorItem, 0)
	)
	if err = json.Unmarshal([]byte(defaultRuleStr), &defaultRules); len(defaultRuleStr) == 0 || err != nil {
		resource.LoggerTask.Warning(ctx, "fail go get xmaster default monitor rules from config, use default",
			logit.String("clusterId", clusterId), logit.String("defaultRuleStr", defaultRuleStr), logit.Error("error", err))
		defaultRules = []*xmaster.MonitorItem{
			{
				Name:      "instance_alive",
				Cond:      "1==1",
				Expr:      "((time()-$lastActiveTime)>10) || ($instance_alive==0)", // NOTE: 经沟通，采用保守判断，监控项不存在时按健康处理
				Filter:    "repeat_time()>=2",
				MutexList: []*xmaster.MutexInfo{},
				Level:     1,
				Timeout:   300,
				Tag:       map[string]string{},
			},
		}
	}
	return &xmaster.MonitorConfig{Rules: defaultRules}, nil
}

func GenXMasterClusterPegaMonitorConfig(ctx context.Context, clusterId string) (*xmaster.MonitorConfig, error) {
	var (
		err            error
		defaultRuleStr = conf.XmasterHAConfIns.PegadbMonitorRules
		defaultRules   = make([]*xmaster.MonitorItem, 0)
	)
	if err = json.Unmarshal([]byte(defaultRuleStr), &defaultRules); len(defaultRuleStr) == 0 || err != nil {
		resource.LoggerTask.Warning(ctx, "fail go get xmaster default monitor rules from config, use default",
			logit.String("clusterId", clusterId), logit.String("defaultRuleStr", defaultRuleStr), logit.Error("error", err))
		defaultRules = []*xmaster.MonitorItem{
			{
				Name:      "instance_alive",
				Cond:      "1==1",
				Expr:      "((time()-$lastActiveTime)>10) || ($instance_alive==0)", // NOTE: 经沟通，采用保守判断，监控项不存在时按健康处理
				Filter:    "repeat_time()>=2",
				MutexList: []*xmaster.MutexInfo{},
				Level:     99,
				Timeout:   300,
				Tag:       map[string]string{},
			},
			{
				Name:      "instance_logic_available",
				Cond:      "(exist(\"instance_logic_available\")==1)",
				Expr:      "((time()-$lastLogicAvailTime)>30) && ((time()-$lastReportTime)<30)",
				Filter:    "",
				MutexList: []*xmaster.MutexInfo{},
				Level:     89,
				Timeout:   300,
				Tag:       map[string]string{},
			},
		}
	}
	return &xmaster.MonitorConfig{Rules: defaultRules}, nil
}

func SetXMasterMonitorSwitch(ctx context.Context, appId string, enable bool) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// 设置所有cluster的monitor switch
	for _, cluster := range app.Clusters {
		if err = SetXMasterClusterMonitorSwitch(ctx, cluster.ClusterId, enable); err != nil {
			return err
		}
	}

	if err = SetXMasterClusterMonitorSwitch(ctx, appId, enable); err != nil {
		return err
	}

	return nil
}

func SetXMasterClusterMonitorSwitch(ctx context.Context, clusterId string, enable bool) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetMonitorSwitch(ctx, &xmaster.GetMonitorSwitchRequest{
		ClusterId: clusterId,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query monitor switch", logit.String("appId", clusterId),
		logit.String("clusterId", clusterId),
		logit.AutoField("monitor switch queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query monitor switch fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor switch, target cluster is not exist ", logit.String("appId", clusterId), logit.String("clusterId", clusterId))
		return nil
	}

	opRsp, err := s.SetMonitorSwitch(ctx, &xmaster.SetMonitorSwitchRequest{
		ClusterId: clusterId,
		Enable:    enable,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor switch fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor switch fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set monitor switch fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}
	return nil
}

// BlockAppMonitorInXmaster, 屏蔽指定高可用策略，目前通过删除对应策略实现
func BlockAppMonitorInXmaster(ctx context.Context, appId string, monitorNames []string) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	for _, cluster := range app.Clusters {
		if err = BlockXMasterClusterMonitorStrategy(ctx, cluster.ClusterId, monitorNames); err != nil {
			return err
		}
	}

	if err = BlockXMasterClusterMonitorStrategy(ctx, appId, monitorNames); err != nil {
		return err
	}

	return nil
}

func BlockXMasterClusterMonitorStrategy(ctx context.Context, clusterID string, monitorNames []string) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetMonitorStrategy(ctx, &xmaster.GetMonitorStrategyRequest{
		ClusterId: clusterID,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query monitor strategy", logit.String("clusterID", clusterID),
		logit.String("clusterID", clusterID),
		logit.AutoField("monitor strategy queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query monitor strategy fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster block monitor strategy, target cluster is not exist ", logit.String("clusterID", clusterID))
		return nil
	}
	if len(queryRsp.Rules) == 0 {
		resource.LoggerTask.Warning(ctx, "xmaster block monitor strategy, target cluster has no monitor strategy, skip", logit.String("clusterID", clusterID))
		return nil
	}

	find := false
	newRules := make([]*xmaster.MonitorItem, 0)
	for i, rule := range queryRsp.Rules {
		for _, mn := range monitorNames {
			if rule.Name == mn {
				find = true
			} else {
				newRules = append(newRules, queryRsp.Rules[i])
			}
		}
	}
	if !find {
		resource.LoggerTask.Warning(ctx, "xmaster block monitor strategy, target cluster has no monitor strategy which need block, skip",
			logit.String("clusterID", clusterID))
		return nil
	}

	mc := &xmaster.MonitorConfig{
		Rules: newRules,
	}
	opRsp, err := s.SetMonitorStrategy(ctx, &xmaster.SetMonitorStrategyRequest{
		ClusterId: clusterID,
		Rule:      mc,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set monitor fail", logit.String("clusterID", clusterID),
			logit.String("clusterID", clusterID),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set monitor fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}

	return nil
}

// UnblockAppMonitorInXmaster, 解除屏蔽指定的高可用策略, 目前通过设置成默认策略实现
func UnblockAppMonitorInXmaster(ctx context.Context, appId string) error {
	var (
		mc  *xmaster.MonitorConfig
		err error
	)

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// 设置所有 clusters的monitor config
	for _, cluster := range app.Clusters {
		switch cluster.Engine {
		case x1model.EnginePegaDB:
			mc, err = GenXMasterClusterPegaMonitorConfig(ctx, cluster.ClusterId)
		default:
			mc, err = GenXMasterClusterDefaultMonitorConfig(ctx, cluster.ClusterId)
		}
		if err != nil {
			resource.LoggerTask.Warning(ctx, "xmaster set monitor strategy, get monitor strategy fail", logit.String("clusterId", cluster.ClusterId),
				logit.Error("err", err))
			return err
		}
		if err = SetXMasterClusterMonitorStrategy(ctx, cluster.ClusterId, mc, true); err != nil {
			return err
		}
	}

	// 设置 app的monitor config，默认使用最后一个cluster的monitor config
	if err = SetXMasterClusterMonitorStrategy(ctx, appId, mc, true); err != nil {
		return err
	}

	return nil
}

func QueryClusterTopoFromXmaster(ctx context.Context, clusterID string) (*xmaster.QueryClusterResponse, error) {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryCluster(ctx, &xmaster.QueryClusterRequest{
		ClusterId: clusterID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster fail",
			logit.String("clusterID", clusterID),
			logit.Error("err", err))
		return nil, err
	}
	return queryRsp, err
}

func SetXMasterTaskFakeSwitch(ctx context.Context, appId string, enable bool) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// 设置所有cluster的monitor switch
	for _, cluster := range app.Clusters {
		if err = SetXMasterClusterTaskFakeSwitch(ctx, cluster.ClusterId, enable); err != nil {
			return err
		}
	}

	if err = SetXMasterClusterTaskFakeSwitch(ctx, appId, enable); err != nil {
		return err
	}

	return nil
}

func SetXMasterClusterTaskFakeSwitch(ctx context.Context, clusterId string, enable bool) error {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.GetTaskFakeSwitch(ctx, &xmaster.GetTaskFakeSwitchRequest{
		ClusterId: clusterId,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query task fake switch", logit.String("appId", clusterId),
		logit.String("clusterId", clusterId),
		logit.AutoField("task fake switch queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query task fake switch fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.Error("err", err))
		return errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster set task fake switch, target cluster is not exist ",
			logit.String("appId", clusterId),
			logit.String("clusterId", clusterId))
		return nil
	}

	opRsp, err := s.SetTaskFakeSwitch(ctx, &xmaster.SetTaskFakeSwitchRequest{
		ClusterId: clusterId,
		Enable:    enable,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster set task fake switch fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.Error("err", err))
		return errors.XmasterOpFail.Wrap(err)
	}
	if opRsp.Success != 1 {
		resource.LoggerTask.Warning(ctx, "xmaster set task fake switch fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.String("code", opRsp.Code), logit.String("message", opRsp.Message))
		return errors.XmasterOpFail.Errorf("xmaster set task fake switch fail, code:%s, message:%s",
			opRsp.Code, opRsp.Message)
	}
	return nil
}

func ListAppUnhealthyInstances(ctx context.Context, appId string) ([]string, error) {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId),
			logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return nil, cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	var (
		unHealthyInsIDs = make([]string, 0)
		ins             []string
	)
	if ins, err = QueryClusterUnhealthyInstances(ctx, appId); err != nil {
		return nil, err
	}
	unHealthyInsIDs = append(unHealthyInsIDs, ins...)

	// 获取所有cluster的unhealthy instances
	for _, cluster := range app.Clusters {
		if ins, err = QueryClusterUnhealthyInstances(ctx, cluster.ClusterId); err != nil {
			return nil, err
		}
		unHealthyInsIDs = append(unHealthyInsIDs, ins...)
	}

	return unHealthyInsIDs, nil
}

func QueryClusterUnhealthyInstances(ctx context.Context, clusterId string) ([]string, error) {
	s := xmaster.NewDefaultXmasterSdk()
	queryRsp, err := s.QueryClusterHealthStatus(ctx, &xmaster.QueryClusterHealthStatusRequest{
		ClusterId: clusterId,
	})
	resource.LoggerTask.Notice(ctx, "xmaster query cluster healthy status", logit.String("appId", clusterId),
		logit.String("clusterId", clusterId),
		logit.AutoField("cluster healthy status queryRsp", queryRsp))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xmaster query cluster healthy status fail", logit.String("appId", clusterId),
			logit.String("clusterId", clusterId),
			logit.Error("err", err))
		return nil, errors.XmasterQueryFail.Wrap(err)
	}
	if queryRsp.Code == xmaster.HttpErrClusterNotFound {
		resource.LoggerTask.Warning(ctx, "xmaster get cluster healthy status, target cluster is not exist ",
			logit.String("appId", clusterId),
			logit.String("clusterId", clusterId))
		return nil, cerrs.ErrNotFound.Errorf("cluster not found in xmaster")
	}
	unHealthyInsIDs := make([]string, 0)
	for _, ins := range queryRsp.HealthInfo {
		if !ins.IsHealthy.InstanceAlive {
			unHealthyInsIDs = append(unHealthyInsIDs, ins.InstanceID)
		}
	}
	return unHealthyInsIDs, nil
}
