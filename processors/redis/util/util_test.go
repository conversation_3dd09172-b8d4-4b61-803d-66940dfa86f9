package util

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/sdk_utils"
)

func TestPingTest_ConnectFail(t *testing.T) {
	ctx := context.Background()

	err := PingTest(ctx, "localhost", 9527, 2, nil)

	if !sdk_utils.IsConnectFail(err) {
		t.Errorf("expect connect fail, got: %s", err)
	}
}

func TestGetMasterRedis(t *testing.T) {
	cluster := x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "3",
	})
	if ip := GetMasterRedis(&cluster, cluster.Nodes[0]); len(ip) != 0 {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[1]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[2]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster = x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "3",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "4",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "5",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "6",
	})
	if ip := GetMasterRedis(&cluster, cluster.Nodes[0]); len(ip) != 0 {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[1]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[2]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[3]); len(ip) != 0 {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[4]); ip != "4" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[5]); ip != "4" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster = x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToFakeDelete,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "3",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "4",
	})
	if ip := GetMasterRedis(&cluster, cluster.Nodes[0]); len(ip) != 0 {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[1]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[2]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToFakeDelete,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "3",
	})
	if ip := GetMasterRedis(&cluster, cluster.Nodes[0]); len(ip) != 0 {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[1]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[2]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToFakeDelete,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToDelete,
		Ip:     "3",
	})
	if ip := GetMasterRedis(&cluster, cluster.Nodes[0]); len(ip) != 0 {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[1]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetMasterRedis(&cluster, cluster.Nodes[2]); ip != "1" {
		t.Errorf("expect ip 1, actual %s", ip)
	}
}

func TestGetSlaveRedis(t *testing.T) {
	cluster := x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "3",
	})
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[0]); ip != "2,3" {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[1]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[2]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster = x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "3",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "4",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "5",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "6",
	})
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[0]); ip != "2,3" {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[1]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[2]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[3]); ip != "5,6" {
		t.Errorf("expect ip empty, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[4]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[5]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster = x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToFakeDelete,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "3",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "4",
	})
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[0]); ip != "3,4" {
		t.Errorf("expect ip 3,4, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[1]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[2]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster = x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToCreate,
		Ip:     "3",
	})
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[0]); ip != "2,3" {
		t.Errorf("expect ip 3,4, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[1]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[2]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	cluster = x1model.Cluster{
		Nodes: []*x1model.Node{},
	}
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeMaster,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "1",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusInUse,
		Ip:     "2",
	})
	cluster.Nodes = append(cluster.Nodes, &x1model.Node{
		Role:   x1model.RoleTypeSlave,
		Status: x1model.NodeOrProxyStatusToDelete,
		Ip:     "3",
	})
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[0]); ip != "2" {
		t.Errorf("expect ip 3,4, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[1]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
	if ip := GetSlaveRedis(&cluster, cluster.Nodes[2]); len(ip) != 0 {
		t.Errorf("expect ip 1, actual %s", ip)
	}
}
