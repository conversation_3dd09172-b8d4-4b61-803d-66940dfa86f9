/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* gmaster.go */
/*
modification history
--------------------
2022/07/12 , by <PERSON> (<PERSON><PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package util

import (
	"context"
	"github.com/pkg/errors"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type GlobalIps struct {
	Master string
	Slave  string
}

func GetGlobalIps(ctx context.Context, app *x1model.Application, gClusterId string) (*GlobalIps, error) {
	ret := GlobalIps{
		Master: "",
		Slave:  "",
	}
	if len(app.AppGroupID) != 0 {
		var gSlaves []string
		globalNodes, err := gmaster.GlobalMasterOp().GetNodes(ctx, &gmaster.GetNodesParams{
			AppGroupID: app.AppGroupID,
			UserID:     app.UserId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get global nodes fail", logit.Error("err", err))
			return nil, err
		}
		for _, gNode := range globalNodes {
			if gNode.ShardId != gClusterId {
				continue
			}

			if gNode.Role == x1model.RoleTypeMaster {
				ret.Master = gNode.Ip
			} else {
				gSlaves = append(gSlaves, gNode.Ip)
			}
		}
		ret.Slave = strings.Join(gSlaves, ",")
	}
	return &ret, nil
}

func NeedGlobalInfo(app *x1model.Application) bool {
	if len(app.AppGroupID) == 0 {
		return false
	}
	if app.Status == x1model.AppStatusFollowerQuit ||
		app.Status == x1model.AppStatusDeleteQuit {
		return false
	}
	return true
}

func NeedConfigSetForJoinGroup(ctx context.Context, app *x1model.Application) (bool, error) {
	if len(app.Clusters) == 0 {
		resource.LoggerTask.Warning(ctx, "cluster num cant be 0")
		return false, errors.New("cluster num cant be 0")
	}
	if app.UseNewAgent != "yes" {
		resource.LoggerTask.Trace(ctx, "only support newagent,skip")
		return false, nil
	}
	if app.Clusters[0].Engine != x1model.EnginePegaDB {
		resource.LoggerTask.Trace(ctx, "not pega,skip")
		return false, nil
	}
	if app.Type != x1model.AppTypeCluster {
		resource.LoggerTask.Trace(ctx, "not cluster,skip")
		return false, nil
	}
	resource.LoggerTask.Trace(ctx, "need config set")
	return true, nil
}
