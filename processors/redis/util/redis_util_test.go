package util

import (
	"testing"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func TestGetMasterIP(t *testing.T) {
	toCreateCluster := &x1model.Cluster{
		Nodes: []*x1model.Node{
			{
				Ip:     "1",
				Role:   x1model.RoleTypeMaster,
				Status: x1model.NodeOrProxyStatusToCreate,
			},
			{
				Ip:     "2",
				Role:   x1model.RoleTypeSlave,
				Status: x1model.NodeOrProxyStatusToCreate,
			},
		},
	}
	toSelfHealCluster := &x1model.Cluster{
		Nodes: []*x1model.Node{
			{
				Ip:     "1",
				Role:   x1model.RoleTypeMaster,
				Status: x1model.NodeOrProxyStatusInUse,
			},
			{
				Ip:     "2",
				Role:   x1model.RoleTypeSlave,
				Status: x1model.NodeOrProxyStatusToFakeDelete,
			},
			{
				Ip:     "3",
				Role:   x1model.RoleTypeSlave,
				Status: x1model.NodeOrProxyStatusToCreate,
			},
		},
	}
	toModifySpecCluster := &x1model.Cluster{
		Nodes: []*x1model.Node{
			{
				Ip:     "1",
				Role:   x1model.RoleTypeMaster,
				Status: x1model.NodeOrProxyStatusToDelete,
			},
			{
				Ip:     "2",
				Role:   x1model.RoleTypeSlave,
				Status: x1model.NodeOrProxyStatusToDelete,
			},
			{
				Ip:     "3",
				Role:   x1model.RoleTypeMaster,
				Status: x1model.NodeOrProxyStatusToCreate,
			},
			{
				Ip:     "4",
				Role:   x1model.RoleTypeSlave,
				Status: x1model.NodeOrProxyStatusToCreate,
			},
		},
	}
	type args struct {
		cluster *x1model.Cluster
		node    *x1model.Node
		hybrid  bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "master in create, hybrid",
			args: args{
				cluster: toCreateCluster,
				node:    toCreateCluster.Nodes[0],
				hybrid:  true,
			},
			want: "",
		},
		{
			name: "master in create, not hybrid",
			args: args{
				cluster: toCreateCluster,
				node:    toCreateCluster.Nodes[0],
				hybrid:  false,
			},
			want: "",
		},
		{
			name: "slave in create, hybrid",
			args: args{
				cluster: toCreateCluster,
				node:    toCreateCluster.Nodes[1],
				hybrid:  true,
			},
			want: "1",
		},
		{
			name: "slave in create, not hybrid",
			args: args{
				cluster: toCreateCluster,
				node:    toCreateCluster.Nodes[1],
				hybrid:  false,
			},
			want: "1",
		},
		{
			name: "master in self heal, hybrid",
			args: args{
				cluster: toSelfHealCluster,
				node:    toSelfHealCluster.Nodes[0],
				hybrid:  true,
			},
			want: "",
		},
		{
			name: "master in self heal, not hybrid",
			args: args{
				cluster: toSelfHealCluster,
				node:    toSelfHealCluster.Nodes[0],
				hybrid:  false,
			},
			want: "",
		},
		{
			name: "to del slave in self heal, hybrid",
			args: args{
				cluster: toSelfHealCluster,
				node:    toSelfHealCluster.Nodes[1],
				hybrid:  true,
			},
			want: "1",
		},
		{
			name: "to del slave in self heal, not hybrid",
			args: args{
				cluster: toSelfHealCluster,
				node:    toSelfHealCluster.Nodes[1],
				hybrid:  false,
			},
			want: "1",
		},
		{
			name: "to create slave in self heal, hybrid",
			args: args{
				cluster: toSelfHealCluster,
				node:    toSelfHealCluster.Nodes[2],
				hybrid:  true,
			},
			want: "1",
		},
		{
			name: "to create slave in self heal, not hybrid",
			args: args{
				cluster: toSelfHealCluster,
				node:    toSelfHealCluster.Nodes[2],
				hybrid:  false,
			},
			want: "1",
		},
		{
			name: "to delete master in modify spec, hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[0],
				hybrid:  true,
			},
			want: "",
		},
		{
			name: "to delete master in modify spec, not hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[0],
				hybrid:  false,
			},
			want: "",
		},
		{
			name: "to create master in modify spec, hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[2],
				hybrid:  true,
			},
			want: "",
		},
		{
			name: "to create master in modify spec, not hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[2],
				hybrid:  false,
			},
			want: "",
		},
		{
			name: "to delete slave in modify spec, hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[1],
				hybrid:  true,
			},
			want: "1",
		},
		{
			name: "to delete slave in modify spec, not hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[1],
				hybrid:  false,
			},
			want: "1",
		},
		{
			name: "to create slave in modify spec, hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[3],
				hybrid:  true,
			},
			want: "3",
		},
		{
			name: "to create slave in modify spec, not hybrid",
			args: args{
				cluster: toModifySpecCluster,
				node:    toModifySpecCluster.Nodes[3],
				hybrid:  false,
			},
			want: "1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetMasterIP(tt.args.cluster, tt.args.node, tt.args.hybrid); got != tt.want {
				t.Errorf("GetMasterIp() = %v, want %v", got, tt.want)
			}
		})
	}
}
