package util

import "testing"

func TestPackageVersionLargerEq(t *testing.T) {
	type args struct {
		l string
		r string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "EqualVersions",
			args: args{
				l: "1.2.3",
				r: "1.2.3",
			},
			want: true,
		},
		{
			name: "LeftVersionLarger",
			args: args{
				l: "1.2.4",
				r: "1.2.3",
			},
			want: true,
		},
		{
			name: "LeftVersionLarger",
			args: args{
				l: "1.2.45",
				r: "1.2.33",
			},
			want: true,
		},
		{
			name: "RightVersionLarger",
			args: args{
				l: "1.2.3",
				r: "1.2.4",
			},
			want: false,
		},
		{
			name: "LeftVersionLonger",
			args: args{
				l: "1.2.3.4",
				r: "1.2.3",
			},
			want: true,
		},
		{
			name: "RightVersionLonger",
			args: args{
				l: "1.2.3",
				r: "1.2.3.4",
			},
			want: false,
		},
		{
			name: "EmptyLeftVersion",
			args: args{
				l: "",
				r: "1.2.3",
			},
			want: false,
		},
		{
			name: "EmptyRightVersion",
			args: args{
				l: "1.2.3",
				r: "",
			},
			want: true,
		},
		{
			name: "BothVersionsEmpty",
			args: args{
				l: "",
				r: "",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := PackageVersionLargerEq(tt.args.l, tt.args.r); got != tt.want {
				t.Errorf("PackageVersionLargerEq() = %v, want %v", got, tt.want)
			}
		})
	}
}
