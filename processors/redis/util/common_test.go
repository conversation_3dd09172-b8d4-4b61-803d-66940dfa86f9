package util

import "testing"

func Test_getImageIdAndVersion(t *testing.T) {
	type args struct {
		dbImageId string
	}
	tests := []struct {
		name        string
		args        args
		wantImageId string
		wantVersion string
	}{
		{
			name: "normal",
			args: args{
				dbImageId: "e9e9af52-acad-4f4f-9f89-83f857e327ff:20220823102819",
			},
			wantImageId: "e9e9af52-acad-4f4f-9f89-83f857e327ff",
			wantVersion: "20220823102819",
		},
		{
			name: "only-image-id",
			args: args{
				dbImageId: "e9e9af52-acad-4f4f-9f89-83f857e327ff",
			},
			wantImageId: "e9e9af52-acad-4f4f-9f89-83f857e327ff",
			wantVersion: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotImageId, gotVersion := GetImageIdAndVersion(tt.args.dbImageId)
			if gotImageId != tt.wantImageId {
				t.Errorf("getImageIdAndVersion() gotImageId = %v, want %v", gotImageId, tt.wantImageId)
			}
			if gotVersion != tt.wantVersion {
				t.Errorf("getImageIdAndVersion() gotVersion = %v, want %v", gotVersion, tt.wantVersion)
			}
		})
	}
}
