package util

import (
	"bytes"
	"context"
	"crypto/md5"
	cr "crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"math/big"
	"math/rand"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	DefaultCertExpireTime = 1 // 证书默认过期时间1年
	pingTimeoutSec        = 300
)

type setTLSParams struct {
	Meta     *xagent.Meta  `json:"meta"`
	Operate  string        `json:"op"`
	ConfList []*paramsConf `json:"conf_list"`
}

type paramsConf struct {
	ConfName  string `json:"conf_name"`
	ConfValue string `json:"conf_value"`
}

type CERT struct {
	CERT       []byte
	CERTKEY    *rsa.PrivateKey
	CERTPEM    *bytes.Buffer
	CERTKEYPEM *bytes.Buffer
	CSR        *x509.Certificate
}

func CreateTLS(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return errors.New("app " + teu.Entity + " not found")
	}

	// 获取集群tls信息
	redisTLSInfos, err := x1model.RedisTlsGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tls conf error", logit.Error("error", err))
		return err
	}

	// 统计存量证书信息
	count := 0
	for _, redisTLSInfo := range redisTLSInfos {
		if redisTLSInfo.Status == x1model.TlsStatusDeleted {
			count++
		}
	}
	if len(redisTLSInfos) == 0 || count == len(redisTLSInfos) {
		// 重新生成tls信息,落库
		msg := "cluster " + app.AppId + " open tls"
		resource.LoggerTask.Notice(ctx, msg, logit.String("redisTlsInfos:", base_utils.Format(redisTLSInfos)))

		var redisTLS x1model.RedisTls

		cert, err := CreateCA(DefaultCertExpireTime)
		if err != nil {
			errorMessage := "create CA failed."
			resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppId)))
			return err
		}

		redisTLS.AppID = app.AppId
		key := cert.CERTKEYPEM.String()
		ca := cert.CERTPEM.String()

		redisTLS.CertKeyPem = key
		redisTLS.CertKeyPemMd5 = getMd5(ctx, key)
		redisTLS.CertPem = ca
		redisTLS.CertPemMd5 = getMd5(ctx, ca)
		redisTLS.CreateAt = time.Now()
		redisTLS.UpdateAt = time.Time{}
		redisTLS.CertExpireAt = time.Now().AddDate(DefaultCertExpireTime, 0, 0)
		redisTLS.Status = x1model.TlsStatusToCreate
		err = x1model.RedisTlsSave(ctx, []*x1model.RedisTls{&redisTLS})
		if err != nil {
			errorMessage := "save tls info failed."
			resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(app.AppId)))
			return err
		}
	}
	return nil
}

func DeliverTLSConf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return errors.New("app " + teu.Entity + " not found")
	}

	// 获取集群tls信息
	redisTLSInfos, err := x1model.RedisTlsGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tls conf error", logit.Error("error", err))
		return err
	}
	if len(redisTLSInfos) == 0 {
		resource.LoggerTask.Error(ctx, "redis tls conf is null, please check it.")
		return errors.New("redis tls conf is null")
	}

	// 查询当前属于tls 开启流程中还是关闭流程中
	// true:触发开启流程   false:触发关闭流程
	var tlsFlag bool
	var redisTLSInfoToCreate *x1model.RedisTls
	for _, redisTLSInfo := range redisTLSInfos {
		if redisTLSInfo.Status == x1model.TlsStatusToCreate {
			tlsFlag = true
			redisTLSInfoToCreate = redisTLSInfo
		} else if redisTLSInfo.Status == x1model.TlsStatusToDelete {
			tlsFlag = false
			redisTLSInfoToCreate = redisTLSInfo
		}
	}

	err = SetTLSConfWithXagent(ctx, app, redisTLSInfoToCreate, tlsFlag, false)
	if err != nil {
		resource.LoggerTask.Error(ctx, "set tls conf with xagent failed")
		return err
	}

	return nil
}

func UpdateTLSConfIfNeeded(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return errors.New("app " + teu.Entity + " not found")
	}

	// 获取集群tls信息
	redisTLSInfos, err := x1model.RedisTlsGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tls conf error", logit.Error("error", err))
		return err
	}

	// 查询当前属于tls 开启流程中还是关闭流程中
	// true:触发开启流程   false:触发关闭流程
	var tlsFlag bool
	var count int
	for _, redisTLSInfo := range redisTLSInfos {
		if redisTLSInfo.Status == x1model.TlsStatusInUse {
			tlsFlag = true
			count++
		}
	}

	// 集群有且仅有一条有效的tls配置
	if count == 1 && tlsFlag {
		resource.LoggerTask.Notice(ctx, "cluster: "+app.AppId+" only has one tls conf, need update proxy tls conf.")
		var redisTLSInfoNeedUpdate *x1model.RedisTls
		for _, redisTLSInfo := range redisTLSInfos {
			if redisTLSInfo.Status == x1model.TlsStatusInUse {
				redisTLSInfoNeedUpdate = redisTLSInfo
			}
		}
		err = SetTLSConfWithXagent(ctx, app, redisTLSInfoNeedUpdate, tlsFlag, false)
		if err != nil {
			resource.LoggerTask.Error(ctx, "set tls conf with xagent failed")
			return err
		}
	} else {
		resource.LoggerTask.Notice(ctx, "cluster: "+app.AppId+" inuse tls conf num: "+
			cast.ToString(count)+",do not need update proxy tls conf.")
	}

	return nil
}

func UpdateTLSConfIfNeededForNew(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return errors.New("app " + teu.Entity + " not found")
	}

	// 获取集群tls信息
	redisTLSInfos, err := x1model.RedisTlsGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tls conf error", logit.Error("error", err))
		return err
	}

	// 查询当前属于tls 开启流程中还是关闭流程中
	// true:触发开启流程   false:触发关闭流程
	var tlsFlag bool
	var count int
	for _, redisTLSInfo := range redisTLSInfos {
		if redisTLSInfo.Status == x1model.TlsStatusInUse {
			tlsFlag = true
			count++
		}
	}

	// 集群有且仅有一条有效的tls配置
	if count == 1 && tlsFlag {
		resource.LoggerTask.Notice(ctx, "cluster: "+app.AppId+" only has one tls conf, need update proxy tls conf.")
		var redisTLSInfoNeedUpdate *x1model.RedisTls
		for _, redisTLSInfo := range redisTLSInfos {
			if redisTLSInfo.Status == x1model.TlsStatusInUse {
				redisTLSInfoNeedUpdate = redisTLSInfo
			}
		}
		err = SetTLSConfWithXagent(ctx, app, redisTLSInfoNeedUpdate, tlsFlag, true)
		if err != nil {
			resource.LoggerTask.Error(ctx, "set tls conf with xagent failed")
			return err
		}
	} else {
		resource.LoggerTask.Notice(ctx, "cluster: "+app.AppId+" inuse tls conf num: "+
			cast.ToString(count)+",do not need update proxy tls conf.")
	}

	return nil
}

func SetTLSConfWithXagent(ctx context.Context, app *x1model.Application, redisTLSConf *x1model.RedisTls, tlsFlag bool, isNewInstance bool) error {
	// 先更新一个测试,若更新正常并且服务能正常启动,则再更新全量
	if err := SetTLSConfWithXagentOne(ctx, app, redisTLSConf, tlsFlag, isNewInstance); err != nil {
		resource.LoggerTask.Warning(ctx, "SetTLSConfWithXagentOne failed",
			logit.String("app", base_utils.Format(app)),
			logit.Error("err", err),
		)
		return err
	}
	// 更新全量
	if err := SetTLSConfWithXagentToAll(ctx, app, redisTLSConf, tlsFlag, isNewInstance); err != nil {
		resource.LoggerTask.Warning(ctx, "SetTLSConfWithXagentToAll failed",
			logit.String("app", base_utils.Format(app)),
			logit.Error("err", err),
		)
		return err
	}

	return nil
}

func SetTLSConfWithXagentOne(ctx context.Context, app *x1model.Application, redisTLSConf *x1model.RedisTls, tlsFlag bool, isNewInstance bool) error {
	/*
		下发tls配置
	*/
	var selectedProxy *x1model.Proxy
	for _, itf := range app.Interfaces {
		resource.LoggerTask.Notice(ctx, "app.Interfaces detail", logit.String("itf:", base_utils.Format(itf)))

		// 剔除故障节点
		for _, proxy := range itf.Proxys {
			if isNewInstance {
				// 自愈场景只操作tocreate的节点
				if proxy.Status != x1model.NodeOrProxyStatusToCreate {
					resource.LoggerTask.Notice(ctx, "isNewInstance:proxy status is not tocreate, skip update tls conf.", logit.String("proxy:", base_utils.Format(proxy)))
					continue
				}
				selectedProxy = proxy
			} else {
				if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
					proxy.Status == x1model.NodeOrProxyStatusToFakeDelete ||
					proxy.Status == x1model.NodeOrProxyStatusInFault {
					// 跳过执行
					resource.LoggerTask.Notice(ctx, "proxy status is error, skip update tls conf.", logit.String("proxy:", base_utils.Format(proxy)))
					continue
				}
				selectedProxy = proxy
			}

			break
		}
	}

	if selectedProxy != nil {
		proxy := selectedProxy
		resource.LoggerTask.Notice(ctx, "set app.Interfaces.proxy tls conf first", logit.String("proxy:", base_utils.Format(proxy)))

		// 更新配置
		if err := setTLSConfForOneInstanceWithXagent(ctx, proxy, redisTLSConf, tlsFlag); err != nil {
			resource.LoggerTask.Warning(ctx, "set tls conf to proxy failed",
				logit.String("proxy", base_utils.Format(proxy)),
				logit.Error("err", err),
			)
			return err
		}
		// ping test
		if err := PingTest(ctx, proxy.FloatingIP, proxy.Port, pingTimeoutSec, nil); err != nil && !strings.Contains(err.Error(), "NOAUTH") {
			resource.LoggerTask.Warning(ctx, "ping proxy failed",
				logit.String("proxy", base_utils.Format(proxy)),
				logit.Error("err", err),
			)
			return err
		}
	} else {
		// 如果没有挑选到需要操作的节点,则无需进行变更
		resource.LoggerTask.Notice(ctx, "app.Interfaces.proxy len is 0", logit.String("itf:", base_utils.Format(app.Interfaces)))
		// return errors.New("app " + app.AppId + " normal status proxy is 0")
	}

	return nil
}

func SetTLSConfWithXagentToAll(ctx context.Context, app *x1model.Application, redisTLSConf *x1model.RedisTls, tlsFlag bool, isNewInstance bool) error {
	g := gtask.Group{
		Concurrent:    20,
		AllowSomeFail: false,
	}

	/*
		下发tls配置
	*/
	for _, itf := range app.Interfaces {
		resource.LoggerTask.Notice(ctx, "app.Interfaces detail", logit.String("itf:", base_utils.Format(itf)))
		for _, proxy := range itf.Proxys {
			/*
				区分自愈场景
				自愈场景只操作 tocreate 的节点
				非自愈场景操作全部的节点
			*/
			if isNewInstance {
				if proxy.Status != x1model.NodeOrProxyStatusToCreate {
					resource.LoggerTask.Notice(ctx, "isNewInstance:proxy status is not tocreate, skip update tls conf.", logit.String("proxy:", base_utils.Format(proxy)))
					continue
				}
			} else {
				if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
					proxy.Status == x1model.NodeOrProxyStatusToFakeDelete ||
					proxy.Status == x1model.NodeOrProxyStatusInFault {
					resource.LoggerTask.Notice(ctx, "proxy status is error, skip update tls conf.", logit.String("proxy:", base_utils.Format(proxy)))
					continue
				}
			}

			proxy := proxy
			g.Go(func() error {
				return setTLSConfForOneInstanceWithXagent(ctx, proxy, redisTLSConf, tlsFlag)
			})
		}
	}
	_, err := g.Wait()

	// ping test
	for _, itf := range app.Interfaces {
		resource.LoggerTask.Notice(ctx, "app.Interfaces detail", logit.String("itf:", base_utils.Format(itf)))
		for _, proxy := range itf.Proxys {
			if isNewInstance {
				if proxy.Status != x1model.NodeOrProxyStatusToCreate {
					resource.LoggerTask.Notice(ctx, "isNewInstance:proxy status is not tocreate, skip execute ping test.", logit.String("proxy:", base_utils.Format(proxy)))
					continue
				}
			} else {
				if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
					proxy.Status == x1model.NodeOrProxyStatusToFakeDelete ||
					proxy.Status == x1model.NodeOrProxyStatusInFault {
					resource.LoggerTask.Notice(ctx, "proxy status is error, skip execute ping test.", logit.String("proxy:", base_utils.Format(proxy)))
					continue
				}
			}

			if err := PingTest(ctx, proxy.FloatingIP, proxy.Port, pingTimeoutSec, nil); err != nil && !strings.Contains(err.Error(), "NOAUTH") {
				resource.LoggerTask.Warning(ctx, "ping proxy failed",
					logit.String("proxyId", proxy.ProxyId),
					logit.String("floatingIp", proxy.FloatingIP),
					logit.Int("port", proxy.Port),
					logit.Error("err", err),
				)
				return err
			}
		}
	}

	return err
}

func setTLSConfForOneInstanceWithXagent(ctx context.Context, proxy *x1model.Proxy, redisTLSConf *x1model.RedisTls, tlsFlag bool) error {
	tlsCrt := redisTLSConf.CertPem
	tlsCrtBase64 := base64.StdEncoding.EncodeToString([]byte(tlsCrt))
	tlsCrtMd5 := redisTLSConf.CertPemMd5
	tlsKey := redisTLSConf.CertKeyPem
	tlsKeyBase64 := base64.StdEncoding.EncodeToString([]byte(tlsKey))
	tlsKeyMd5 := redisTLSConf.CertKeyPemMd5

	tlsConf := []*paramsConf{}
	var operate string
	if tlsFlag {
		// tlsConf = append(tlsConf, &paramsConf{
		// 	ConfName:  "tls_port",
		// 	ConfValue: cast.ToString(tlsPort),
		// })
		tlsConf = append(tlsConf, &paramsConf{
			ConfName:  "tls_key",
			ConfValue: tlsKeyBase64,
		})
		tlsConf = append(tlsConf, &paramsConf{
			ConfName:  "tls_key_md5",
			ConfValue: tlsKeyMd5,
		})
		tlsConf = append(tlsConf, &paramsConf{
			ConfName:  "tls_crt",
			ConfValue: tlsCrtBase64,
		})
		tlsConf = append(tlsConf, &paramsConf{
			ConfName:  "tls_crt_md5",
			ConfValue: tlsCrtMd5,
		})
		operate = "set"
	} else {
		// 关闭tls,下发默认值,节点启动时就不会生成证书文件,配置文件中也不会增加相关配置
		operate = "del"
	}

	params := &setTLSParams{
		Meta: &xagent.Meta{
			Basedir: "/root",
		},
		Operate:  operate,
		ConfList: tlsConf,
	}
	resource.LoggerTask.Notice(ctx, "set tls conf to xagent, xagent_ip: "+proxy.FloatingIP+", xagent_port: "+cast.ToString(proxy.XagentPort),
		logit.String("params:", base_utils.Format(params)))

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: proxy.FloatingIP,
			Port: int32(proxy.XagentPort),
		},
		Action:     "op_tls", // 变更env_vars
		Params:     params,
		TimeoutSec: 120, // 执行超时时间
	}

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set tls conf to xagent fail",
			logit.String("proxyId", proxy.ProxyId),
			logit.Error("err", err))
		return err
	}
	return nil
}

func CreateCA(expire int) (*CERT, error) {
	var (
		ca  = new(CERT)
		err error
	)

	if expire < 20 {
		expire = 20
	}
	// 为ca生成私钥
	ca.CERTKEY, err = rsa.GenerateKey(cr.Reader, 4096)
	if err != nil {
		return nil, err
	}

	// 对证书进行签名
	ca.CSR = &x509.Certificate{
		SerialNumber: big.NewInt(rand.Int63n(2000)),
		NotBefore:    time.Now(),                       // 生效时间
		NotAfter:     time.Now().AddDate(expire, 0, 0), // 过期时间
		IsCA:         true,                             // 表示用于CA
	}
	// 创建证书
	// caBytes 就是生成的证书
	ca.CERT, err = x509.CreateCertificate(cr.Reader, ca.CSR, ca.CSR, &ca.CERTKEY.PublicKey, ca.CERTKEY)
	if err != nil {
		return nil, err
	}
	ca.CERTPEM = new(bytes.Buffer)
	err = pem.Encode(ca.CERTPEM, &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: ca.CERT,
	})
	if err != nil {
		return nil, err
	}
	ca.CERTKEYPEM = new(bytes.Buffer)
	err = pem.Encode(ca.CERTKEYPEM, &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(ca.CERTKEY),
	})
	if err != nil {
		return nil, err
	}

	// 进行PEM编码，编码就是直接cat证书里面内容显示的东西
	return ca, nil
}

func getMd5(ctx context.Context, str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}
