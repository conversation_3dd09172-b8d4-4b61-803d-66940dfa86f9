package backup

import (
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
)

type BackupParams struct {
	Meta      *xagent.Meta `json:"meta"`
	BosConfig *BosConfig   `json:"bos_config"`
}

type BosConfig struct {
	Endpoint   string `json:"endpoint"`
	Ak         string `json:"ak"`
	Sk         string `json:"sk"`
	BucketName string `json:"bucket_name"`
	FileName   string `json:"file_name"`
}

type FileMetaData struct {
	ContentLength int64 `json:"content_length"`
}

type BackupResult struct {
	MetaData FileMetaData `json:"metadata"`
}

type CreateAutoBackupTaskRequest struct {
	ClusterShowID string `json:"clusterShowId"`
	EngineType    string `json:"engineType"`
	AppBackupID   string `json:"appBackupId"`
	Status        string `json:"status"`
}
