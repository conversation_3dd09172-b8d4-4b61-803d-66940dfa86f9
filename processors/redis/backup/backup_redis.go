package backup

import (
	"context"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/repo"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessBackupRedis
// Step-1 执行备份任务
// 1. 通过XAgent向节点发送BGSAVE
// 2. 将生成的rdb文件上传到BOS
// 3. 调用Csmaster接口，通知备份完成
func ProcessBackupRedis(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 校验集群状态为12，跳过
	clusterStatus, err := csmaster.CsmasterOp().GetClusterStatus(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster status error", logit.Error("error", err))
		return err
	}

	if clusterStatus == csmaster.CsmasterStatusCreatedFailed {
		resource.LoggerTask.Trace(ctx, "cluster has created failed", logit.Error("error", err), logit.String("cluster", app.AppId))
		return nil
	}

	// 获取nodes信息
	nodes := map[string]map[string]*x1model.Node{}
	for _, cluster := range app.Clusters {
		if _, has := nodes[cluster.ClusterId]; !has {
			nodes[cluster.ClusterId] = map[string]*x1model.Node{}
		}
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			nodes[cluster.ClusterId][node.NodeId] = node
		}
	}

	// 获取default acl
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}

	// 获取backup信息
	backup, err := x1model.BackupGetByCond(ctx, "app_id = ? AND status = ? AND expire_at > ?",
		app.AppId, x1model.BackupStatusDoing, time.Now())
	if err != nil {
		// 表明已经没有需要正在进行的备份任务，不应该返回record not found
		resource.LoggerTask.Notice(ctx, "no backup to do", logit.String("appId", teu.Entity))
		return nil
	}

	// 校验是否是本次任务需要执行的备份
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if backup.BackupID != param.BackupParam.BackupID {
		resource.LoggerTask.Warning(ctx, "backup id in db not match id in parameters", logit.Error("error", err),
			logit.String("backupIdInDB", backup.BackupID), logit.String("backupIdInParams", param.BackupParam.BackupID))
		return nil
	}

	resource.LoggerTask.Notice(ctx, "begin backup task", logit.String("appId", app.AppId),
		logit.String("backupId", backup.BackupID))

	// 通过xagent执行backup操作
	g := gtask.Group{
		Concurrent:    backupConcurrency,
		AllowSomeFail: true,
	}
	for _, item := range backup.Items {
		item := item
		node, has := nodes[item.ClusterId][item.NodeID]
		if !has {
			resource.LoggerTask.Warning(ctx, "node not exist", logit.String("appId", teu.Entity),
				logit.String("clusterId", item.ClusterId), logit.String("nodeId", item.NodeID))
			item.Status = x1model.BackupStatusError
			continue
		}

		g.Go(func() error {
			return backupRedisNodeByXagent(ctx, node, item, defaultAcl)
		})
	}

	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "gTask backup failed.", logit.String("appId", teu.Entity))
		time.Sleep(5 * time.Minute)
		return err
	}
	backup.Status = x1model.BackupStatusSucc

	backup.EndAt = time.Now()

	// 更新backup数据库
	if err := x1model.BackupsSave(ctx, []*x1model.Backup{backup}); err != nil {
		resource.LoggerTask.Warning(ctx, "save backup status fail", logit.String("appId", teu.Entity),
			logit.String("backupId", backup.BackupID), logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// 调用Csmaster接口，通知备份完成
	if err := updateCsmasterBackupStatus(ctx, app, backup); err != nil {
		return err
	}

	resource.LoggerTask.Notice(ctx, "backup task finished successfully", logit.String("appId", app.AppId),
		logit.String("backupId", backup.BackupID))

	return nil
}

func IsLccZone(PhysicalZone string) bool {
	if strings.Contains(PhysicalZone, "lcc") {
		return true
	} else {
		return false
	}
}

func backupRedisNodeByXagent(ctx context.Context, node *x1model.Node, item *x1model.BackupItem,
	defaultAcl *x1model.RedisAcl) error {
	bucketName, fileName, err := repo.ParseBosAccess(item.Access)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse bos access fail", logit.String("appId", node.AppId),
			logit.String("backupId", item.BackupID), logit.Error("dbError", err))
		return err
	}

	repoConf := repo.GetRepo(backupRepo).Conf
	endpoint := repoConf.Endpoint
	//if IsLccZone(node.Azone) {
	//	endpoint = "172.17.32.12:8080"
	//	bucketName = "scs-package-manager-lcc001"
	//}

	params := &BackupParams{
		Meta: &xagent.Meta{
			Engine:        node.Engine,
			EngineVersion: node.EngineVersion,
			Basedir:       node.Basedir,
			Port:          int32(node.Port),
		},
		BosConfig: &BosConfig{
			Endpoint:   endpoint,
			Ak:         repoConf.BosAk,
			Sk:         repoConf.BosSk,
			BucketName: bucketName,
			FileName:   fileName,
		},
	}
	if defaultAcl != nil {
		params.Meta.Password = defaultAcl.Password
	}

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action:     backupActionName,
		Params:     params,
		TimeoutSec: backupTimeoutSec,
	}

	rsp, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "xagent: run backup fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))

		item.Status = x1model.BackupStatusError
	} else {
		result := &BackupResult{}
		if err := rsp.ParseResult(result); err != nil {
			resource.LoggerTask.Warning(ctx, "invalid backup rsp",
				logit.String("nodeId", node.NodeId),
				logit.Reflect("rsp", rsp),
				logit.Error("err", err))
			return err
		}
		item.Size = result.MetaData.ContentLength
		item.Status = x1model.BackupStatusSucc
	}

	return err
}

// updateCsmasterBackupStatus 更新csmaster backup status
func updateCsmasterBackupStatus(ctx context.Context, app *x1model.Application, backup *x1model.Backup) error {
	nodes := map[string]*x1model.Node{}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			nodes[node.NodeId] = node
		}
	}

	backupRecordList := make([]*csdk.CsmasterBackupRecord, 0)
	for _, item := range backup.Items {
		backupRecord := &csdk.CsmasterBackupRecord{
			NodeShortID: int64(nodes[item.NodeID].NodeShortID),
			Size:        item.Size,
			Status:      item.Status,
		}
		backupRecordList = append(backupRecordList, backupRecord)
	}

	req := &csmaster.UpdateBackupRecordsStatusParams{
		UserID:  app.UserId,
		AppID:   app.AppId,
		Records: backupRecordList,
	}

	if err := csmaster.CsmasterOp().UpdateBackupRecordsStatus(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update csmaster backup records",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UpdateCsmasterBackupStatusFail.Wrap(err)
	}

	return nil
}
