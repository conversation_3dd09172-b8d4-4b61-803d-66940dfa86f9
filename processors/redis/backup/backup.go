package backup

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/component/repo"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessPegaManualBackupTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appId := teu.Entity

	// 获取备份集Id
	params := &dbrs.CreateBackupTaskResponse{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), params)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal request fail", logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal request fail,err:%s", unmarshalError.Error())
	}
	appBackupId := params.AppDataBackupID

	repoConf := repo.GetRepo(backupRepo).Conf

out:
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)

			getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
				DataType:             "PegaDB",
				AppID:                appId,
				AppDataBackupID:      params.AppDataBackupID,
				DownloadUrlExpireSec: 43200,
			}

			appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
			if err != nil {
				errMsg := "call dbrs api get app backup detail failed"
				resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
				return cerrs.ErrInvalidParams.Errorf(errMsg)
			}

			isAllDone := true
			isAllSuccess := true
			// 获取目标备份集
			validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, appId)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
				return err
			}
			var appBackup *x1model.AppBackup
			for _, backupRecord := range validBackupRecord {
				if backupRecord.AppBackupID == appBackupId {
					resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
					appBackup = backupRecord
				}
			}
			if appBackup == nil {
				errMsg := "get appBackup failed"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
				return cerrs.ErrInvalidParams.Errorf(errMsg)
			}

			if len(appBackupDetail.AppDataBackupShows) == 0 {
				resource.LoggerTask.Notice(ctx, "appBackup is doing", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
				isAllDone = false
				isAllSuccess = false
			} else {
				for _, backupRecord := range appBackupDetail.AppDataBackupShows {
					if appBackupDetail.AppDataBackupID == appBackupId {
						// 更新集群备份状态
						if backupRecord.StartDateTime != "" {
							if backupRecord.Status != x1model.BackupSuccess && backupRecord.Status != x1model.BackupFailed {
								isAllDone = false
							}
							if backupRecord.Status == x1model.BackupFailed {
								isAllSuccess = false
							}
						} else {
							resource.LoggerTask.Notice(ctx, "appBackup is doing", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
							isAllDone = false
							isAllSuccess = false
						}
					}
				}
			}
			// 判断是否全部备份完成，如果全部备份完成，修改isAllDone为true，并且修改appBackup.status字段
			if isAllDone {
				errMsg := "appBackup all done"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
			}

			if isAllSuccess {
				errMsg := "appBackup all success"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
				appBackup.Status = x1model.BackupSuccess
			}

			if !isAllDone {
				resource.LoggerTask.Notice(ctx, appId+" "+appBackupId+" is not all done, continue.")
				continue
			} else {
				// 更新信息到数据库
				for _, backupRecord := range appBackupDetail.AppDataBackupShows {
					if appBackupDetail.AppDataBackupID == appBackupId {
						// 更新集群备份状态
						startTime, err := time.Parse(time.RFC3339, backupRecord.StartDateTime)
						if err != nil {
							resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
							return err
						}
						access := ""
						if len(backupRecord.OuterLinks) > 0 {
							access = backupRecord.OuterLinks[0]
						}
						appBackup.AppBackupItems = append(appBackup.AppBackupItems, &x1model.AppBackupItem{
							AppBackupID: appBackupId,
							BackupID:    backupRecord.DataBackupID,
							ShardID:     backupRecord.ClusterID,
							StartTime:   startTime,
							Duration:    GetDuration(backupRecord.Duration),
							Status:      backupRecord.Status,
							Bucket:      repoConf.BosBucket,
							ObjectKey:   backupRecord.DstStorageObjectID,
							Access:      access,
							ObjectSize:  backupRecord.BackupSizeBytes,
						})
					}
				}
				// 保存数据库
				saveErr := x1model.SaveBackup(ctx, []*x1model.AppBackup{appBackup})
				if saveErr != nil {
					resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
					return err
				}
			}
			break out
		}
	}
	return nil
}

func ProcessRedisManualBackupTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appId := teu.Entity

	// 获取备份集Id
	params := &dbrs.CreateBackupTaskResponse{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), params)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal request fail", logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal request fail,err:%s", unmarshalError.Error())
	}
	appBackupId := params.AppDataBackupID

	repoConf := repo.GetRepo(backupRepo).Conf

out:
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)

			getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
				DataType:             "Redis",
				AppID:                appId,
				AppDataBackupID:      params.AppDataBackupID,
				DownloadUrlExpireSec: 43200,
			}

			appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
			if err != nil {
				errMsg := "call dbrs api get app backup detail failed"
				resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
				return cerrs.ErrInvalidParams.Errorf(errMsg)
			}

			isAllDone := true
			isAllSuccess := true
			// 获取目标备份集
			validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, appId)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
				return err
			}
			var appBackup *x1model.AppBackup
			for _, backupRecord := range validBackupRecord {
				if backupRecord.AppBackupID == appBackupId {
					resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
					appBackup = backupRecord
				}
			}
			if appBackup == nil {
				errMsg := "get appBackup failed"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
				return cerrs.ErrInvalidParams.Errorf(errMsg)
			}

			if len(appBackupDetail.AppDataBackupShows) == 0 {
				resource.LoggerTask.Notice(ctx, "appBackup is doing", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
				isAllDone = false
				isAllSuccess = false
			} else {
				for _, backupRecord := range appBackupDetail.AppDataBackupShows {
					if appBackupDetail.AppDataBackupID == appBackupId {
						// 更新集群备份状态
						if backupRecord.StartDateTime != "" {
							if backupRecord.Status != x1model.BackupSuccess && backupRecord.Status != x1model.BackupFailed {
								isAllDone = false
							}
							if backupRecord.Status == x1model.BackupFailed {
								isAllSuccess = false
							}
						} else {
							resource.LoggerTask.Notice(ctx, "appBackup is doing", logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
							isAllDone = false
							isAllSuccess = false
						}
					}
				}
			}
			// 判断是否全部备份完成，如果全部备份完成，修改isAllDone为true，并且修改appBackup.status字段
			if isAllDone {
				errMsg := "appBackup all done"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
			}

			if isAllSuccess {
				errMsg := "appBackup all success"
				resource.LoggerTask.Notice(ctx, errMsg, logit.String("appBackupDetail:", base_utils.Format(appBackupDetail)))
				appBackup.Status = x1model.BackupSuccess
			}

			if !isAllDone {
				resource.LoggerTask.Notice(ctx, appId+" "+appBackupId+" is not all done, continue.")
				continue
			} else {
				// 更新信息到数据库
				for _, backupRecord := range appBackupDetail.AppDataBackupShows {
					if appBackupDetail.AppDataBackupID == appBackupId {
						// 更新集群备份状态
						startTime, err := time.Parse(time.RFC3339, backupRecord.StartDateTime)
						if err != nil {
							resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
							return err
						}
						access := ""
						if len(backupRecord.OuterLinks) > 0 {
							access = backupRecord.OuterLinks[0]
						}
						appBackup.AppBackupItems = append(appBackup.AppBackupItems, &x1model.AppBackupItem{
							AppBackupID: appBackupId,
							BackupID:    backupRecord.DataBackupID,
							ShardID:     backupRecord.ClusterID,
							StartTime:   startTime,
							Duration:    GetDuration(backupRecord.Duration),
							Status:      backupRecord.Status,
							Bucket:      repoConf.BosBucket,
							ObjectKey:   backupRecord.DstStorageObjectID,
							Access:      access,
							ObjectSize:  backupRecord.BackupSizeBytes,
						})
					}
				}
				// 保存数据库
				saveErr := x1model.SaveBackup(ctx, []*x1model.AppBackup{appBackup})
				if saveErr != nil {
					resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
					return err
				}
			}
			break out
		}
	}
	return nil
}

// 例行备份已结束,更新备份结果至SCS数据库
func ProcessRefreshBackupTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appId := teu.Entity

	params := &CreateAutoBackupTaskRequest{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), params)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal teu.Parameters fail", logit.String("params:", base_utils.Format(params)), logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal teu.Parameters fail,err:%s", unmarshalError.Error())
	}
	appBackupId := params.AppBackupID
	dataType := params.EngineType

	// 调用dbrs接口获取备份结果
	getAppBackup := &dbrsComp.QueryAppBackupDetailParams{
		DataType:             dataType,
		AppID:                appId,
		AppDataBackupID:      params.AppBackupID,
		DownloadUrlExpireSec: 43200,
	}

	appBackupDetail, err := dbrsComp.DbrsResourceOp().QueryAppBackupDetailByAppBackupId(ctx, getAppBackup)
	if err != nil {
		errMsg := "call dbrs api get app backup detail failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	// 获取目标备份集
	validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
		return err
	}
	var appBackup *x1model.AppBackup
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == appBackupId {
			resource.LoggerTask.Notice(ctx, "get appBackup success", logit.String("backupRecord:", base_utils.Format(backupRecord)))
			appBackup = backupRecord
		}
	}
	if appBackup == nil {
		errMsg := "get appBackup failed"
		resource.LoggerTask.Notice(ctx, errMsg, logit.String("validBackupRecord:", base_utils.Format(validBackupRecord)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	repoConf := repo.GetRepo(backupRepo).Conf

	for _, backupRecord := range appBackupDetail.AppDataBackupShows {
		if appBackupDetail.AppDataBackupID == appBackupId {
			// 更新集群备份状态
			startTime, err := time.Parse(time.RFC3339, backupRecord.StartDateTime)
			if err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
				return err
			}

			// 备份下载链接
			access := ""
			if len(backupRecord.OuterLinks) > 0 {
				access = backupRecord.OuterLinks[0]
			}
			appBackup.AppBackupItems = append(appBackup.AppBackupItems, &x1model.AppBackupItem{
				AppBackupID: appBackupId,
				BackupID:    backupRecord.DataBackupID,
				ShardID:     backupRecord.ClusterID,
				StartTime:   startTime,
				Duration:    GetDuration(backupRecord.Duration),
				Status:      backupRecord.Status,
				Bucket:      repoConf.BosBucket,
				ObjectKey:   backupRecord.DstStorageObjectID,
				Access:      access, // 没有下载链接,此值为空
				ObjectSize:  backupRecord.BackupSizeBytes,
			})
		}
	}
	appBackup.Status = x1model.BackupSuccess
	if params.Status == "failed" {
		appBackup.Status = x1model.BackupFailed
	}

	saveErr := x1model.SaveBackup(ctx, []*x1model.AppBackup{appBackup})
	if saveErr != nil {
		resource.LoggerTask.Error(ctx, "save app backup record failed", logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessDeleteBackupPolicy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appId := teu.Entity
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}

	useNewBackupFlag := cacheCluster.UseNewBackup
	if useNewBackupFlag == 0 {
		resource.LoggerTask.Notice(ctx, "cacheCluster.UseNewBackup = 0, need not delete backup policy",
			logit.String("appId", base_utils.Format(appId)))
		return nil
	}

	if err := util.DeleteBackupPolicyIfNeeded(ctx, appId); err != nil {
		resource.LoggerTask.Error(ctx, "check and delete backup policy failed", logit.Error("error", err))
		return err
	}

	return nil

}

func ProcessCheckAndClearExpireBackupRecord(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// check_and_delete_expired_records
	// 获取app
	appId := teu.Entity
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}
	// 获取备份配置
	backupConfig := cacheCluster.BackupConfig
	if backupConfig == "" {
		errMsg := "cacheCluster.BackupConfig is null"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(cacheCluster)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	backupConfigSplit := strings.Split(backupConfig, ";")
	if len(backupConfigSplit) != 3 {
		errMsg := "cacheCluster.BackupConfig format error"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
			logit.String("backupConfig:", base_utils.Format(backupConfig)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	// eg. Mon,Tue,Wed,Thu;08:00:00;1
	retentionTime := cast.ToInt(backupConfigSplit[2])
	var backupModels []*csmaster_model_interface.BackupRecord
	// 获取自动备份数量
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "cluster_id = ? and backup_type = 0", cacheCluster.Id); err != nil {
		errMsg := "get backup record failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
			logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "backup record detail", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
		logit.String("len(backupModels):", base_utils.Format(len(backupModels))),
		logit.String("backupModels:", base_utils.Format(backupModels)))
	if len(backupModels) <= retentionTime {
		// 无需清理过期备份记录
		resource.LoggerTask.Notice(ctx, "need not clear backup record", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
			logit.String("len(backupModels):", base_utils.Format(len(backupModels))),
			logit.Int("retentionTime:", retentionTime))
	} else {
		/*
		   清理过期备份记录,清理策略:
		   1、保留时间*2 以外的备份记录直接删除
		   2、保留时间-保留时间*2 之间的备份记录标记为过期
		   每个 record 记录了备份时的 retentionTime(即 Expairation 字段), 当前和 DBRS 策略保存一致，均以当前的保留策略为准
		*/
		for _, backupRecord := range backupModels {
			// 自动备份 备份时间超过最大保留时间*2，则删除
			if backupRecord.BackupType == 0 &&
				util.GetTimeUptoNow(backupRecord.StartTime) > 2*retentionTime*86400 {
				resource.LoggerTask.Notice(ctx, "to delete backup record", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
				// 删除记录
				toDeleteRecordId := cast.ToString(backupRecord.ID)
				if err := resource.CsmasterModel.DeleteOneByUkey(ctx, toDeleteRecordId, backupRecord); err != nil {
					resource.LoggerTask.Error(ctx, "delete record failed", logit.Error("err", err))
					return err
				}
				resource.LoggerTask.Notice(ctx, "delete backup record success", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
			} else if backupRecord.BackupType == 0 &&
				util.GetTimeUptoNow(backupRecord.StartTime) > retentionTime*86400 {
				resource.LoggerTask.Notice(ctx, "expire backup record", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
				// 将备份记录设置为过期
				backupRecord.Status = 4
				if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, backupRecord); err != nil {
					resource.LoggerTask.Error(ctx, "update backup record failed", logit.String("backupRecord :",
						base_utils.Format(backupRecord)), logit.Error("err", err))
					return err
				}
				resource.LoggerTask.Notice(ctx, "expire backup record success", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
			} else {
				resource.LoggerTask.Notice(ctx, "backup record do nothing", logit.String("cacheCluster:", base_utils.Format(cacheCluster)),
					logit.String("backupRecord:", base_utils.Format(backupRecord)))
			}
		}
	}
	return nil
}

func GetDuration(duration string) int64 {
	//"0d 0h 2m 31s"   转成s
	split := strings.Split(duration, " ")
	dayStr := split[0]
	hourStr := split[1]
	minuteStr := split[2]
	secondStr := split[3]
	day := strings.Split(dayStr, "d")[0]
	hour := strings.Split(hourStr, "h")[0]
	minute := strings.Split(minuteStr, "m")[0]
	second := strings.Split(secondStr, "s")[0]
	cost := cast.ToInt64(day)*24*3600 + cast.ToInt64(hour)*3600 + cast.ToInt64(minute)*60 + cast.ToInt64(second)
	fmt.Println(cost)
	return cost
}
