package backup

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	dbrsComp "icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/config"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessCreateBackupPolicyForAOF(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app
	appID := teu.Entity

	// 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appID)),
			logit.Error("err", err))
		return err
	}

	// 注册策略
	region := env.IDC()
	dataType := "Redis"
	dataStoragesType, dataStoragesID, err := util.GetStorage(ctx, dataType, true, cacheCluster, appID)
	if err != nil {
		errMsg := "get storage failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	createPolicyParams := dbrsComp.CreatePolicyConf{
		DataType:   dataType,
		AppID:      cacheCluster.ClusterShowId,
		PolicyType: "logical_log_backup",
		Type:       dataStoragesType,
		BucketId:   dataStoragesID,
		Region:     region,
	}
	_, err = dbrsComp.DbrsResourceOp().CreateBackupPolicy(ctx, &createPolicyParams)
	if err != nil {
		errMsg := "call dbrs api create aof policy failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("params:", base_utils.Format(createPolicyParams)), logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}
	return nil
}

func ProcessDeleteBackupPolicyForAOF(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity
	return processDeleteBackupPolicyForAOF(ctx, appID)
}

func ProcessDeleteBackupPolicyForAOFAndCloseOpheader(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity
	err := processDeleteBackupPolicyForAOF(ctx, appID)
	if err != nil {
		return err
	}
	err = config.CloseOpHeader(ctx, appID)
	if err != nil {
		return err
	}
	return nil
}

func processDeleteBackupPolicyForAOF(ctx context.Context, appID string) error {
	// 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appID)),
			logit.Error("err", err))
		return err
	}

	if cacheCluster.EnableRestore == 0 {
		return nil
	}

	// 删除策略
	deletePolicyParams := dbrsComp.CommonPolicyParams{
		DataType:   "Redis",
		AppID:      appID,
		PolicyType: "logical_log_backup",
	}
	_, err = dbrsComp.DbrsResourceOp().DeleteBackupPolicy(ctx, &deletePolicyParams)
	if err != nil {
		errMsg := "call dbrs api delete aof policy failed"
		resource.LoggerTask.Error(ctx, errMsg, logit.String("params:", base_utils.Format(deletePolicyParams)), logit.Error("err", err))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	// 修改csmaster backup_config
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			EnableRestore: 0,
			RestoreTime:   "",
		},
		UserID:         cacheCluster.UserInfo.IamUserId,
		AppID:          appID,
		RequiredFields: []string{"enable_restore", "restore_time"},
	}); err != nil {
		errorMessage := "update cluster backup_config failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}
	return nil
}

func PorcessCheckRecoverableDatetimeRange(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appID := teu.Entity
	g := gtask.Group{
		Concurrent:    20,
		AllowSomeFail: false,
	}

	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		g.Go(func() error {
			return checkRecoverableDatetimeRangeSingleCluster(ctx, "Redis", appID, cluster.ClusterShortID)
		})
	}
	_, err = g.Wait()
	// 说明此时 AOF 备份策略还未生效，可进行等待, 等下次重试
	if err != nil {
		time.Sleep(30 * time.Second)
		return err
	}

	return nil
}

func checkRecoverableDatetimeRangeSingleCluster(ctx context.Context, dataType string, appID string, clusterShortID int) (err error) {
	params := dbrsComp.QueryPointInTimeRestoreRangeParams{
		DataType:  dataType,
		AppID:     appID,
		ClusterID: fmt.Sprintf("%d", clusterShortID),
	}
	rsp, err := dbrsComp.DbrsResourceOp().QueryPointInTimeRestoreRange(ctx, &params)
	if err != nil {
		return err
	}

	// check rsp 结果
	if len(rsp.RecoverableDateTimes) == 0 {
		resource.LoggerTask.Warning(ctx, "RecoverableDateTimes is empty", logit.String("appID", appID))
		return errors.Errorf("RecoverableDateTimes is empty")
	}

	for _, recoverableDateTime := range rsp.RecoverableDateTimes {
		_, err = time.Parse(time.RFC3339, recoverableDateTime.StartDateTime)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Error parsing start datetime", logit.String("appID", appID),
				logit.String("StartDateTime", recoverableDateTime.StartDateTime), logit.Error("error", err))
			return err
		}

		_, err = time.Parse(time.RFC3339, recoverableDateTime.EndDateTime)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Error parsing end datetime", logit.String("appID", appID),
				logit.String("EndDateTime", recoverableDateTime.EndDateTime), logit.Error("error", err))
			return err
		}
	}

	return nil
}
