/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessSelfHealingCb callback
func ProcessSelfHealingCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.UseXmaster == 0 {
		resource.LoggerTask.Notice(ctx, "not xmaster cluster, sleep for 30 seconds before update csmaster")
		time.Sleep(30 * time.Second)
	}

	targetCsmasterStatus := csmaster.CsmasterStatusRunning
	if clusterModel.IsolateStatus == 1 {
		targetCsmasterStatus = csmaster.CsmasterStatusPause
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: targetCsmasterStatus,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete ||
				node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}

		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete ||
				node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
					RoGroupID:     util.GetRoGroupID(node),
					RoGroupWeight: int32(node.RoGroupWeight),
					RoGroupStatus: int32(node.RoGroupStatus),
					IsReadOnly:    1,
				})
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
				proxy.Status == x1model.NodeOrProxyStatusToFakeDelete ||
				proxy.Status == x1model.NodeOrProxyStatusToCreate {
				proxy.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          proxy.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}

	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}
