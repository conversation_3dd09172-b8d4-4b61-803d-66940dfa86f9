/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file analysis_big_key.go
 * <AUTHOR>
 * @date 2023/03/22 15:09:20
 * @brief
 *
 **/

package callback

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

const (
	BigKeySuccess = 4
	BigKeyFailed  = 5
)

func ProcessAnalysisBigKeyCallBack(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.AppShortID != param.ClusterShortID {
		resource.LoggerTask.Error(ctx, "app short id not equal cluster short id", logit.String("app", app.AppId))
		return err
	}

	// 获取分析任务
	analysisTask, err := resource.CsmasterOpAgent.GetAnalysisTaskByClusterID(ctx, int64(app.AppShortID))
	if err != nil {
		resource.LoggerTask.Error(ctx, "get analysis task error", logit.Error("error", err))
		return err
	}

	complete := true
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeSlave && node.Status == x1model.NodeOrProxyStatusAnalyzeFailed {
				complete = false
				break
			}
		}
	}

	if complete {
		analysisTask.BigkeyStatus = BigKeySuccess
		err = resource.CsmasterOpAgent.UpdateAnalysisTask(ctx, analysisTask)
		if err != nil {
			resource.LoggerTask.Error(ctx, "update analysis task error", logit.Error("error", err))
			return err
		}
	} else {
		// 清空之前的分析结果
		err = resource.CsmasterOpAgent.DeleteAnalysisTaskResult(ctx, int64(app.AppShortID))
		if err != nil {
			resource.LoggerTask.Error(ctx, "delete analysis result error", logit.Error("error", err))
			return err
		}

		analysisTask.BigkeyStatus = BigKeyFailed
		err = resource.CsmasterOpAgent.UpdateAnalysisTask(ctx, analysisTask)
		if err != nil {
			resource.LoggerTask.Error(ctx, "update analysis task error", logit.Error("error", err))
			return err
		}
	}

	// 更新cs-master的restore_status
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model:          &csmaster.CsmasterCluster{},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"restore_status"},
	}); err != nil {
		resource.LoggerTask.Error(ctx, "update cs-master restore status error", logit.Error("error", err))
		return err
	}

	// 更新节点状态
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusAnalyzed || node.Status == x1model.NodeOrProxyStatusAnalyzeFailed {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}

	return nil
}
