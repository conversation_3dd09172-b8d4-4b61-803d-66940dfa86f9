/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	csService "icode.baidu.com/baidu/scs/x1-api/httpserver/services/csmaster"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/backup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/common"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessGeneralSuccessCb 通用成功回调
// 1. 调用csmaster接口，将实例状态改为运行中(5)
func ProcessGeneralSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessModifyProxySuccess(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				proxy.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          proxy.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessUpgradeOrRestartSuccessCb 升级/重启成功回调
func ProcessUpgradeOrRestartSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		if util.IsSingleReplica(ctx, cluster) {
			for _, node := range cluster.Nodes {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
			for _, node := range cluster.RoNodes {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessOpenTlsSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	// 更新proxy 状态
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy.Status = x1model.NodeOrProxyStatusInUse
		}
	}
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	// 更新数据库tls状态
	redisTLSInfos, err := x1model.RedisTlsGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tls conf error", logit.Error("error", err))
		return err
	}
	if len(redisTLSInfos) == 0 {
		resource.LoggerTask.Error(ctx, "redis tls conf is null, please check it.")
		return errors.New("redis tls conf is null")
	}
	for _, redisTLSInfo := range redisTLSInfos {
		if redisTLSInfo.Status == x1model.TlsStatusToCreate {
			redisTLSInfo.Status = x1model.TlsStatusInUse
		} else if redisTLSInfo.Status == x1model.TlsStatusToDelete {
			redisTLSInfo.Status = x1model.TlsStatusDeleted
		}
	}

	err = x1model.RedisTlsSave(ctx, redisTLSInfos)
	if err != nil {
		resource.LoggerTask.Error(ctx, "udate tls conf status failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessOpenTdeSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	// 更新数据库tde状态
	redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get redis tde conf error", logit.Error("error", err))
		return err
	}
	if len(redisTdeInfos) == 0 || len(redisTdeInfos) > 1 {
		resource.LoggerTask.Error(ctx, "redis tls conf is not correct, please check it.")
		return errors.New("redis tls conf is not correct")
	}
	for _, redisTdeInfo := range redisTdeInfos {
		if redisTdeInfo.Status == x1model.TdeStatusToCreate {
			redisTdeInfo.Status = x1model.TdeStatusInUse
		}
	}

	err = x1model.RedisTdeSave(ctx, redisTdeInfos)
	if err != nil {
		resource.LoggerTask.Error(ctx, "update tde conf status failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessManualBackupCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appId := teu.Entity
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppIdNoTx(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "get cluster model", logit.String("clusterModel :", base_utils.Format(cacheCluster)))

	validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
		return err
	}

	// 获取备份集Id
	params := &dbrs.CreateBackupTaskResponse{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), params)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal request fail", logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal request fail,err:%s", unmarshalError.Error())
	}

	// 回填备份记录
	currBackupType := "manual"
	var backupModels []*csmaster_model_interface.BackupRecord
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == params.AppDataBackupID {
			for _, backupItem := range backupRecord.AppBackupItems {
				shardId := backupItem.ShardID
				// 此处挑选一个从库作为备份节点，兼容csmaster数据组织逻辑
				instanceId := 0
				for _, cacheInstance := range cacheCluster.CacheInscances {
					if cast.ToString(cacheInstance.ShardId) == shardId &&
						cacheInstance.CacheInstanceType == csmaster_model_interface.INSTANCE_TYPE_SLAVE_REDIS {
						instanceId = int(cacheInstance.Id)
					}
				}
				if backupRecord.BackupType != currBackupType {
					currBackupType = backupRecord.BackupType
				}
				backupModels = append(backupModels, &csmaster_model_interface.BackupRecord{
					BatchID:     backupItem.AppBackupID,
					InstanceID:  instanceId,
					ClusterID:   int(cacheCluster.Id),
					StartTime:   backupItem.StartTime,
					Duration:    int(backupItem.Duration),
					Status:      cast.ToInt8(x1model.GetBackupStatus(backupItem.Status)),
					BackupType:  cast.ToInt8(x1model.GetBackupType(backupRecord.BackupType)),
					Bucket:      backupItem.Bucket,
					ObjectKey:   backupItem.ObjectKey,
					ObjectSize:  backupItem.ObjectSize,
					ShardName:   shardId,
					Comment:     backupRecord.Comment,
					Expairation: int(backupRecord.Expairation),
				})
			}

		}
	}
	resource.LoggerTask.Notice(ctx, "insert backupRecord", logit.String("backupModels :", base_utils.Format(backupModels)))

	if err := resource.CsmasterModel.CreateMulti(ctx, backupModels); err != nil {
		resource.LoggerTask.Error(ctx, "insert backupRecord", logit.String("backupModels :",
			base_utils.Format(backupModels)), logit.Error("err", err))
		return err
	}

	backupResult := common.BackupClusterSuccess
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == params.AppDataBackupID {
			// 获取本次备份任务的状态
			if backupRecord.Status == x1model.BackupSuccess {
				resource.LoggerTask.Notice(ctx, params.AppDataBackupID+" backup success")
			} else {
				// 备份失败
				resource.LoggerTask.Notice(ctx, params.AppDataBackupID+" backup failed")
				backupResult = common.BackupClusterFailed
			}
		}
	}

	// 更新cache_cluuster 备份状态
	if err := csService.UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			BackupStatus: cast.ToInt32(backupResult),
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"backup_status"},
	}); err != nil {
		errorMessage := "update cluster backup_status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}

	// 如果是大key分析任务，完成后更新大key分析任务状态
	if currBackupType == "analyze" {
		bigkeyTaskStatus := 2 //BIGKEY_READY
		if backupResult != common.BackupClusterSuccess {
			bigkeyTaskStatus = 5 // BIGKEY_FAILED
		}
		if err := UpdateBigKeyTaskStatus(ctx, cacheCluster.Id, bigkeyTaskStatus); err != nil {
			resource.LoggerTask.Error(ctx, "update big key task status failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessRefreshBackupCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	appId := teu.Entity
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	validBackupRecord, err := x1model.GetValidBackupByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get valid backup by appId failed", logit.Error("err", err))
		return err
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppIdNoTx(ctx, appId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get clusterid by showid fail", logit.String("appId", base_utils.Format(appId)),
			logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "get cluster model", logit.String("clusterModel :", base_utils.Format(cacheCluster)))

	params := &backup.CreateAutoBackupTaskRequest{}
	unmarshalError := json.Unmarshal([]byte(teu.Parameters), params)
	if unmarshalError != nil {
		resource.LoggerTask.Error(ctx, "unmarshal teu.Parameters fail", logit.String("params:", base_utils.Format(params)), logit.Error("err", unmarshalError))
		return errors.Errorf("unmarshal teu.Parameters fail,err:%s", unmarshalError.Error())
	}

	// 回填备份记录
	var backupModels []*csmaster_model_interface.BackupRecord
	for _, backupRecord := range validBackupRecord {
		if backupRecord.AppBackupID == params.AppBackupID {
			for _, backupItem := range backupRecord.AppBackupItems {
				shardId := backupItem.ShardID
				// 此处挑选一个从库作为备份节点，兼容csmaster数据组织逻辑
				instanceId := 0
				for _, cacheInstance := range cacheCluster.CacheInscances {
					if cast.ToString(cacheInstance.ShardId) == shardId &&
						cacheInstance.CacheInstanceType == csmaster_model_interface.INSTANCE_TYPE_SLAVE_REDIS {
						instanceId = int(cacheInstance.Id)
					}
				}
				backupModels = append(backupModels, &csmaster_model_interface.BackupRecord{
					BatchID:     backupItem.AppBackupID,
					InstanceID:  instanceId,
					ClusterID:   int(cacheCluster.Id),
					StartTime:   backupItem.StartTime,
					Duration:    int(backupItem.Duration),
					Status:      cast.ToInt8(x1model.GetBackupStatus(backupItem.Status)),
					BackupType:  cast.ToInt8(x1model.GetBackupType(backupRecord.BackupType)),
					Bucket:      backupItem.Bucket,
					ObjectKey:   backupItem.ObjectKey,
					ObjectSize:  backupItem.ObjectSize,
					ShardName:   shardId,
					Comment:     backupRecord.Comment,
					Expairation: int(backupRecord.Expairation),
				})
			}

		}
	}
	resource.LoggerTask.Notice(ctx, "insert backupRecord", logit.String("backupModels :", base_utils.Format(backupModels)))

	if err := resource.CsmasterModel.CreateMulti(ctx, backupModels); err != nil {
		resource.LoggerTask.Error(ctx, "insert backupRecord", logit.String("backupModels :",
			base_utils.Format(backupModels)), logit.Error("err", err))
		return err
	}

	backupResult := common.BackupClusterSuccess
	if params.Status == "failed" {
		backupResult = common.BackupClusterFailed
	}

	model := &csmaster.CsmasterCluster{
		BackupStatus: cast.ToInt32(backupResult),
	}

	resource.LoggerTask.Notice(ctx, "UpdateClusterModel model", logit.String("model", base_utils.Format(model)))

	// 更新cache_cluster 备份状态
	if err := csService.UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model:          model,
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"backup_status"},
	}); err != nil {
		errorMessage := "update cluster backup_status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}
	return nil
}

func UpdateBigKeyTaskStatus(ctx context.Context, clusterId int64, bigkeyTaskStatus int) error {
	var cacheClusterAnalyzeModels []*csmaster_model_interface.CacheClusterAnalyze
	if err := resource.CsmasterModel.GetAllByCond(ctx, &cacheClusterAnalyzeModels,
		"cluster_id = ? and type = 0 order by id desc", clusterId); err != nil {
		resource.LoggerTask.Warning(ctx, "cluster:"+cast.ToString(clusterId)+
			" query analyze inprocess task failed", logit.Error("error", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "get cache cluster analyze task ",
		logit.String("cacheClusterAnalyzeModels :", base_utils.Format(cacheClusterAnalyzeModels)))

	if len(cacheClusterAnalyzeModels) == 0 {
		msg := "cluster: " + cast.ToString(clusterId) + " has no big key task."
		errMsg := fmt.Errorf(msg)
		resource.LoggerTask.Warning(ctx, msg, logit.Error("error", errMsg))
		return errMsg
	}

	cacheClusterAnalyzeModel := cacheClusterAnalyzeModels[0]
	cacheClusterAnalyzeModel.BigkeyStatus = bigkeyTaskStatus
	resource.LoggerTask.Notice(ctx, "update cache cluster analyze task",
		logit.String("cacheClusterAnalyzeModel :", base_utils.Format(cacheClusterAnalyzeModel)))
	// 更新任务  save接口
	if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, cacheClusterAnalyzeModel); err != nil {
		resource.LoggerTask.Error(ctx, "update cacheClusterAnalyzeModel", logit.String("cacheClusterAnalyzeModel :",
			base_utils.Format(cacheClusterAnalyzeModel)), logit.Error("err", err))
		return err
	}

	return nil
}
