/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/01, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
标准版变配到集群版回滚

状态码：x1-base/model/csmaster/csmaster_model_interface/cache_cluster.go
*/

package callback

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func syncProxyACLToRedis(ctx context.Context, app *x1model.Application) error {
	redisACLList, err := x1model.RedisAclAllByCond(ctx, "app_id = ? AND status <> ?", app.AppId, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis acl list fail", logit.String("appId", app.AppId), logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	proxyACLList, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND status <> ?", app.AppId, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy acl list fail", logit.String("appId", app.AppId), logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	curRedisACLs := map[string]*x1model.RedisAcl{}
	for _, acl := range redisACLList {
		curRedisACLs[acl.AccountName] = acl
	}

	// create
	var toCreateACLList []*x1model.RedisAcl
	for _, pacl := range proxyACLList {
		_, has := curRedisACLs[pacl.AccountName]
		if has {
			continue
		}

		toCreateACLList = append(toCreateACLList, &x1model.RedisAcl{
			AppID:          pacl.AppID,
			AccountName:    pacl.AccountName,
			CreateAt:       pacl.CreateAt,
			UpdateAt:       pacl.UpdateAt,
			Version:        pacl.Version,
			Engine:         "redis",
			Password:       pacl.Password,
			AllowedCmds:    pacl.AllowedCmds,
			AllowedSubCmds: pacl.AllowedSubCmds,
			KeyPatterns:    pacl.KeyPatterns,
			Properties:     pacl.Properties,
			Status:         pacl.Status,
		})
	}

	if len(toCreateACLList) == 0 {
		resource.LoggerTask.Notice(ctx, "no proxy acl sync to redis", logit.String("appId", app.AppId))
		return nil
	}

	// 更新acl
	if err := x1model.RedisAclSave(ctx, toCreateACLList); err != nil {
		resource.LoggerTask.Warning(ctx, "save redis acl fail", logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func deleteProxyACL(ctx context.Context, app *x1model.Application) error {
	proxyACLList, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND status <> ?", app.AppId, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy acl list fail", logit.String("appId", app.AppId), logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// update
	var toUpdateACLList []*x1model.ProxyAcl
	for _, acl := range proxyACLList {
		acl.Status = x1model.ACLStatusDeleted
		acl.UpdateAt = time.Now()
		toUpdateACLList = append(toUpdateACLList, acl)
	}

	if len(toUpdateACLList) == 0 {
		resource.LoggerTask.Notice(ctx, "no proxy acl update", logit.String("appId", app.AppId))
		return nil
	}

	// 更新acl
	if err := x1model.ProxyAclSave(ctx, toUpdateACLList); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy acl fail", logit.Error("err", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func deleteDefaultACL(ctx context.Context, app *x1model.Application) error {
	if base_utils.CompareVersion(app.Clusters[0].EngineVersion, "6.0") >= 0 {
		resource.LoggerTask.Notice(ctx, "DefaultACLUser not change", logit.String("appId", app.AppId),
			logit.String("EngineVersion", app.Clusters[0].EngineVersion))
		return nil
	}

	// Del default ACL(低版本 Redis 不支持 ACL，故可删除所有账号)
	if err := resource.CsmasterModel.DeleteAllByCond(ctx, &csmaster_model_interface.ClusterAclUser{}, "cluster_id = ?", app.AppShortID); err != nil {
		resource.LoggerTask.Warning(ctx, "delete AclUser failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessModifyTypeErrorCb 处理修改类型错误的回调函数
// ctx context.Context 上下文信息
// teu *workflow.TaskExecUnit TaskExecUnit指针，包含了任务执行单元的相关信息
// 返回值 error 如果出现错误则返回该错误，否则返回nil
func ProcessModifyTypeErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return err
	}

	// App type
	app.Type = x1model.AppTypeStandalone

	// 删除 app.InnerPort
	app.InnerPort = 0

	// Standalone 使用 RedisAuth，Cluster 使用 ClientAuth, 以下操作用于幂等操作
	redisAuth := csCluster.RedisAuth
	if len(csCluster.ClientAuth) > 0 {
		redisAuth = csCluster.ClientAuth
	}

	// Update app status
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:      16,
			Version:     7001,
			RedisAuth:   redisAuth,
			ClientAuth:  "",
			ClusterType: "master_slave",
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"client_auth", "redis_auth"},
	}); err != nil {
		return err
	}

	for _, cluster := range app.Clusters {
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			cluster.ClusterId = fmt.Sprintf("%s-0", app.AppId)
		}
		for _, node := range cluster.Nodes {
			if node.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
				node.ClusterId = fmt.Sprintf("%s-0", app.AppId)
			}
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
				proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				proxy.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}

	// 将集群版 Proxy acl 同步到标准版 Redis
	err = syncProxyACLToRedis(ctx, app)
	if err != nil {
		return err
	}

	// 将集群版 Proxy acl 删除
	err = deleteProxyACL(ctx, app)
	if err != nil {
		return err
	}

	// 对低版本 Redis 删除 cluster_acl_user 表中 DefaultACLUser
	err = deleteDefaultACL(ctx, app)
	if err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}
