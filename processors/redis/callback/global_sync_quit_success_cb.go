/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/27 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file global_sync_quit_success_cb.go
 * <AUTHOR>
 * @date 2022/09/27 15:37:24
 * @brief delete sync group callback
 *
 **/

package callback

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessGlobalSyncQuitSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"sync_group_id", "sync_group_name"},
	}); err != nil {
		return err
	}

	app.Status = x1model.AppStatusInUse
	err = ClearSyncGroupId(ctx, app)
	if err != nil {
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ClearSyncGroupId(ctx context.Context, app *x1model.Application) error {
	x1dao, err := x1model.GetDbAgent(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get x1dao fail", logit.Error("err", err))
		return err
	}
	err = x1dao.UpdateOneByMap(ctx, app, map[string]any{
		"sync_group_id": "",
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "del sync groupid fail", logit.Error("err", err))
		return err
	}

	err = csmaster.CsmasterOp().DeleteGroupId(ctx, app.AppId, app.UserId, csdk.GlobalGroupRoleUnknow, "mockGroupId")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "del csmaster sync groupid fail", logit.Error("err", err))
		return err
	}
	return nil
}
