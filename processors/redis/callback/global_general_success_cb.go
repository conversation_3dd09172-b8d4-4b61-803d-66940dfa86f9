/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_general_success_cb.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package callback

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessGlobalGeneralSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy.Status = x1model.NodeOrProxyStatusInUse
		}
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node.Status = x1model.NodeOrProxyStatusInUse
		}
	}
	err = ModifyCsmasterMigrateStatus(ctx, app, MigrateStatusInUse)
	if err != nil {
		return err
	}

	app.Status = x1model.AppStatusInUse

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func CbCsmasterNode(ctx context.Context, app *x1model.Application, action string) error {
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}

	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		var gIps *util.GlobalIps = nil
		var err error
		if len(app.AppGroupID) != 0 {
			gIps, err = util.GetGlobalIps(ctx, app, cluster.GlobalID)
			if err != nil {
				return errors.Errorf("get global ips fail,%s", err.Error())
			}
		}
		for _, node := range cluster.Nodes {
			item := csdk.CsmasterInstance{
				Uuid:              node.ResourceId,
				MasterRedis:       util.GetMasterRedis(cluster, node),
				SlaverRedis:       util.GetSlaveRedis(cluster, node),
				CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
			}
			if action == "join" {
				if node.Role == x1model.RoleTypeMaster {
					item.SlaverRedis = gIps.Slave
					item.MasterRedis = ""
					item.CacheInstanceType = 3
				}
				if node.Role == x1model.RoleTypeSlave {
					item.MasterRedis = gIps.Master
					item.SlaverRedis = ""
					item.CacheInstanceType = 2
				}
			}
			creq.Models = append(creq.Models, &item)
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessGlobalGeneralQuitSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	// 优化状态修改逻辑，防止回收站成员被错误修改为5，导致billing删不掉
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}

	targetCsmasterStatus := csmaster.CsmasterStatusRunning
	if clusterModel.IsolateStatus == 1 {
		targetCsmasterStatus = csmaster.CsmasterStatusPause
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: targetCsmasterStatus,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy.Status = x1model.NodeOrProxyStatusInUse
		}
	}

	for _, cluster := range app.Clusters {
		cluster.GlobalID = ""
		cluster.GlobalSeqID = 0
		for _, node := range cluster.Nodes {
			node.Status = x1model.NodeOrProxyStatusInUse
		}
	}

	app.Status = x1model.AppStatusInUse

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ClearAppGroupId(ctx context.Context, app *x1model.Application) error {
	x1dao, err := x1model.GetDbAgent(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get x1dao fail", logit.Error("err", err))
		return err
	}
	err = x1dao.UpdateOneByMap(ctx, app, map[string]any{
		"app_group_id":      "",
		"app_group_seq_id":  0,
		"global_metaserver": "",
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "del groupid fail", logit.Error("err", err))
		return err
	}

	err = csmaster.CsmasterOp().DeleteGroupId(ctx, app.AppId, app.UserId, csdk.GlobalGroupRoleUnknow, "mockGroupId")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "del csmaster groupid fail", logit.Error("err", err))
		return err
	}
	return nil
}
