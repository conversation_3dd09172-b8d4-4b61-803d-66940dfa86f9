package callback

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessDeleteStandaloneCb callback
func ProcessDeleteStandaloneCb(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if app.Status != x1model.AppStatusDeleted {
		app.Status = x1model.AppStatusDeleted
		app.UpdateTime = time.Now()
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppId),
				logit.Error("dbError", err))
			return err
		}
	}

	if err != csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csdk.CsmasterCluster{
			Status: csmaster.CsmasterStatusDeleted,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}) {
		resource.LoggerTask.Warning(ctx, "fail update csmaster clustermodel status",
			logit.String("appId", app.AppId), logit.String("userId", app.UserId))
	}

	return nil
}

// ProcessDeleteRoGroupCb callback
func ProcessDeleteRoGroupCb(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// 删除只读组记录（标记删除）
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.DeleteRoGroupParams.ReadonlyGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get rogroup error", logit.Error("error", err))
		return err
	}

	roGroup.Status = x1model.RoGroupDeleted
	if err = resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
		resource.LoggerTask.Error(ctx, "update rogroup error", logit.Error("error", err))
		return err
	}

	// 修改集群replicationNum
	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return err
	}

	deleteRoInstNum := len(param.GroupParams.DeleteRoGroupParams.RoInstances)
	targetReplicasCount := csmasterModel.ReplicationNum - int32(deleteRoInstNum)

	if err != csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csdk.CsmasterCluster{
			Status:         csmaster.CsmasterStatusRunning,
			ReplicationNum: targetReplicasCount,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}) {
		resource.LoggerTask.Warning(ctx, "fail update csmaster clustermodel status",
			logit.String("appId", app.AppId), logit.String("userId", app.UserId))
	}

	return nil
}

// ProcessDeleteRoInstanceCb callback
func ProcessDeleteRoInstanceCb(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// 变更只读组状态
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.DeleteRoInstParams.ReadonlyGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get rogroup error", logit.Error("error", err))
		return err
	}

	roGroup.Status = x1model.RoGroupAvailable
	if err = resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
		resource.LoggerTask.Error(ctx, "update rogroup error", logit.Error("error", err))
		return err
	}

	// 修改集群replicationNum
	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return err
	}
	deleteRoInstNum := len(param.GroupParams.DeleteRoInstParams.RoInstances)
	targetReplicasCount := csmasterModel.ReplicationNum - int32(deleteRoInstNum)

	if err != csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csdk.CsmasterCluster{
			Status:         csmaster.CsmasterStatusRunning,
			ReplicationNum: targetReplicasCount,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}) {
		resource.LoggerTask.Warning(ctx, "fail update csmaster clustermodel status",
			logit.String("appId", app.AppId), logit.String("userId", app.UserId))
	}

	return nil
}

// ProcessDeleteAllRoGroupCb callback
func ProcessDeleteAllRoGroupCb(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	// 删除只读组记录（标记删除）
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroups, err := resource.CsmasterOpAgent.GetReadonlyGroupsByClusterID(ctx, param.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get rogroup error", logit.Error("error", err))
		return err
	}

	if len(roGroups) == 0 {
		return nil
	}

	for _, roGroup := range roGroups {
		roGroup.Status = x1model.RoGroupDeleted
		if err = resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
			resource.LoggerTask.Error(ctx, "update rogroup error", logit.Error("error", err))
			return err
		}
	}
	return nil
}
