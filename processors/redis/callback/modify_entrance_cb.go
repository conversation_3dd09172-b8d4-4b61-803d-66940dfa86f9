/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package callback

import (
	"context"
	"errors"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessModifyEntranceSuccessCb 修改入口成功回调
func ProcessModifyEntranceSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	req := &csmaster.UpdateClusterModelParams{
		Model: &csdk.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessModifyEntranceErrorCb 修改入口失败回调
func ProcessModifyEntranceErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	// 将blb信息存入csmaster数据库
	req := &csmaster.UpdateClusterModelParams{
		Model: &csdk.CsmasterCluster{
			Status: csmaster.CsmasterStatusModifyFailed,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessSwitchBlbTypeInDB 修改blb类型,同时更新csmaster中blb信息
func ProcessSwitchBlbTypeInDB(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	switchNew, switchOld := false, false
	var newBlb *x1model.BLB
	for _, b := range app.BLBs {
		if b.Type == x1model.BLBTypeAppToExchange {
			b.Type = x1model.BLBTypeApp
			switchNew = true
			newBlb = b
			continue
		}
		if b.Type == x1model.BLBTypeApp {
			b.Type = x1model.BLBTypeAppToDelete
			b.UpdateAt = time.Now()
			switchOld = true
			continue
		}
		if b.Type == x1model.BLBTypeNormal {
			b.Type = x1model.BLBTypeNormalToDelete
			b.UpdateAt = time.Now()
			switchOld = true
			continue
		}
	}

	if !switchNew && !switchOld {
		resource.LoggerTask.Warning(ctx, "blb type is wrong", logit.String("appId", app.AppId),
			logit.String("blbs", base_utils.Format(app.BLBs)))
		return errors.New("blb type is wrong")
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	req := &csmaster.UpdateClusterModelParams{
		Model:  &csdk.CsmasterCluster{},
		UserID: app.UserId,
		AppID:  app.AppId,
	}

	if newBlb.IpType == x1model.Ipv6 {
		req.Model.ElbIpv6Id = newBlb.BlbId
		req.Model.ElbIpv6 = newBlb.Ipv6
	} else {
		req.Model.ElbId = newBlb.BlbId
		req.Model.ElbPnetip = newBlb.Ovip
	}

	if newBlb.EndpointIp != "" {
		req.Model.EndpointId = newBlb.EndpointId
		req.Model.EndpointIp = newBlb.EndpointIp
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}
