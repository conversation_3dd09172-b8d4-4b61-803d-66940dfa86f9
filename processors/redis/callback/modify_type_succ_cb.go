/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/01, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
标准版变配到集群版
*/

package callback

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

const (
	MASTER = "master"
)

// AZDeployInfo eg: zoneD,59a224a3-1fc0-4409-a1b0-14ea5ecab04b,true,3;zoneX,xxxxx,false,x
func getAZDeployInfoByReplicas(replicas []*iface.Replica) string {
	AZDeployInfoList := make([]string, 0)
	for _, item := range replicas {
		if item.Role == MASTER {
			AZDeployInfoList = append(AZDeployInfoList, fmt.Sprintf("%s,%s,true,%d", item.Zone, item.SubnetIDs[0], item.Count))
		} else {
			AZDeployInfoList = append(AZDeployInfoList, fmt.Sprintf("%s,%s,false,%d", item.Zone, item.SubnetIDs[0], item.Count))
		}
	}
	return strings.Join(AZDeployInfoList, ";")
}

// ReplicasNum
func getReplicasNumByReplicas(replicas []*iface.Replica) int {
	replicasNum := 0
	for _, item := range replicas {
		replicasNum += item.Count
	}
	return replicasNum
}

// AvailabilityZone
func getAvailabilityZoneByReplicas(replicas []*iface.Replica) string {
	availabilityZone := ""
	for _, replica := range replicas {
		if replica.Role == x1model.RoleTypeMaster {
			availabilityZone += replica.Zone
		}
	}
	return availabilityZone
}

// 删除 Redis 免密访问配置
func clearRedisVpcPasswordFreeConfig(ctx context.Context, app *x1model.Application) error {
	// standalone auto-auth-address
	var confRedisRecords []*csmaster_model_interface.ConfRecordList
	if err := resource.CsmasterModel.GetAllByCond(ctx, &confRedisRecords, "cluster_id = ? AND conf_name = ?", app.AppShortID, "auto-auth-address"); err != nil {
		resource.LoggerTask.Warning(ctx, "get auto-auth-address config from csmaster failed", logit.Error("error", err))
		return fmt.Errorf("get auto-auth-address config from csmaster failed: %w", err)
	}
	if len(confRedisRecords) == 0 {
		return nil
	}

	ids := make([]any, 0)
	for _, confModel := range confRedisRecords {
		ids = append(ids, confModel.ID)
	}

	if err := resource.CsmasterModel.DeleteAllByCond(ctx, &csmaster_model_interface.ConfRecordList{}, "id IN ?", ids...); err != nil {
		resource.LoggerTask.Warning(ctx, "delete auto-auth-address config failed", logit.Error("error", err))
		return fmt.Errorf("delete auto-auth-address config from csmaster failed: %w", err)
	}
	return nil
}

// ProcessModifyTypeSuccCb 处理修改类型成功的回调函数
// ctx context.Context 上下文信息，包含日志记录等信息
// teu *workflow.TaskExecUnit TaskExecUnit指针，包含任务执行单元相关信息
// 返回值 error 如果出现错误则返回非nil，否则返回nil
func ProcessModifyTypeSuccCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	if subTasksStatus == taskIface.SubTasksStatusRunning {
		resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
		return fmt.Errorf("sub tasks status is not success")
	}

	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return err
	}

	// Init value
	destReplicas := []*iface.Replica{}
	if err := json.Unmarshal([]byte(app.DestReplicas), &destReplicas); err != nil {
		return err
	}
	targetFlavor := csmasterModel.Flavor
	targetNodeType := csmasterModel.NodeType
	targetInstanceNum := csmasterModel.InstanceNum
	targetDiskFlavor := csmasterModel.DiskFlavor

	targetAvailabilityZone := getAvailabilityZoneByReplicas(destReplicas)
	targetAZDeployInfo := getAZDeployInfoByReplicas(destReplicas)
	targetReplicasNum := getReplicasNumByReplicas(destReplicas)

	// Update cluster spec
	for _, cluster := range app.Clusters {
		if len(cluster.DestSpec) != 0 && cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
	}

	// App Flavor
	if csmasterModel.DestFlavor > 0 && csmasterModel.DestFlavor != targetFlavor {
		targetFlavor = csmasterModel.DestFlavor
	}

	if app.Type == x1model.EngineMc {
		targetFlavor = csmasterModel.Flavor
	}

	// App NodeType
	if len(csmasterModel.DestNodeType) != 0 && csmasterModel.DestNodeType != targetNodeType {
		targetNodeType = csmasterModel.DestNodeType
	}

	// App InstanceNum(分片数)
	if csmasterModel.DestInstanceNum != 0 && csmasterModel.DestInstanceNum != targetInstanceNum {
		targetInstanceNum = csmasterModel.DestInstanceNum
	}

	// App DiskFlavor
	if csmasterModel.DestDiskFlavor != 0 && csmasterModel.DestDiskFlavor != targetDiskFlavor {
		targetDiskFlavor = csmasterModel.DestDiskFlavor
	}

	// App Replicas
	if len(app.DestReplicas) != 0 && app.DestReplicas != app.Replicas {
		app.Replicas = app.DestReplicas
	}

	// Update App
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:           csmaster.CsmasterStatusRunning,
			NodeType:         targetNodeType,
			AzDeployInfo:     targetAZDeployInfo,
			Flavor:           targetFlavor,
			AvailabilityZone: targetAvailabilityZone,
			ReplicationNum:   int32(targetReplicasNum),
			InstanceNum:      targetInstanceNum,
			DiskFlavor:       targetDiskFlavor,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}

	// Update Node && Proxy
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				proxy.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          proxy.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	if err := clearRedisVpcPasswordFreeConfig(ctx, app); err != nil {
		resource.LoggerTask.Error(ctx, "clear RedisVpcPasswordFreeConfig failed", logit.Error("error", err))
		return err
	}
	return nil
}
