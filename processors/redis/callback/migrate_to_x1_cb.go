package callback

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessMigrateToX1Cb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			ClusterShowId: app.AppId,
			HitX1:         1,
			Status:        csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessMigrateToX1ErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			ClusterShowId: params.AppID,
			Status:        csmaster.CsmasterStatusRunning,
		},
		UserID: params.UserID,
		AppID:  params.AppID,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}
