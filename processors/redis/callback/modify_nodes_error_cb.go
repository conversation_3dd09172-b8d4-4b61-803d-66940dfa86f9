/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessModifyNodesErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: 16,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
				proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				proxy.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	if err := util.ResetAllNodesSwichable(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "reset all nodes switchable failed", logit.Error("error", err))
		return err
	}
	return nil
}
