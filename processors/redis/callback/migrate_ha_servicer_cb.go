package callback

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func MigHAToXmasterSuccessCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return setClusterHAServicer(ctx, teu, csmaster.CsmasterSetHAServicerUseXmaster)
}

func MigHAToCsmasterSuccessCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return setClusterHAServicer(ctx, teu, csmaster.CsmasterSetHAServicerUseCsmaster)
}

func setClusterHAServicer(ctx context.Context, teu *workflow.TaskExecUnit, useXmaster int32) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:     csmaster.CsmasterStatusRunning,
			UseXmaster: useXmaster,
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"use_xmaster"},
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}
