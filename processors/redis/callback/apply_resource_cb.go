/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版资源成功之后，将信息同步至csmaster
*/

package callback

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	MigrateStatusToCreate = 2
	MigrateStatusInUse    = 0
)

func saveNodes(ctx context.Context, app *x1model.Application) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		var gIps *util.GlobalIps = nil
		var err error
		if len(app.AppGroupID) != 0 {
			gIps, err = util.GetGlobalIps(ctx, app, cluster.GlobalID)
			if err != nil {
				return errors.Errorf("get global ips fail,%s", err.Error())
			}
		}

		for _, node := range cluster.Nodes {
			switch node.Status {
			case x1model.NodeOrProxyStatusInUse:
				item := csdk.CsmasterInstance{
					Uuid:        node.ResourceId,
					MasterRedis: util.GetMasterIP(cluster, node, false),
					SlaverRedis: util.GetSlaveRedis(cluster, node),
				}
				if len(app.AppGroupID) != 0 && gIps != nil {
					if node.Role == x1model.RoleTypeMaster {
						item.SlaverRedis = gIps.Slave
						item.MasterRedis = ""
						item.CacheInstanceType = 3
					}
					if node.Role == x1model.RoleTypeSlave {
						item.MasterRedis = gIps.Master
						item.SlaverRedis = ""
						item.CacheInstanceType = 2
					}
				}
				creq.Models = append(creq.Models, &item)
			case x1model.NodeOrProxyStatusToCreate:
				item := csdk.CsmasterInstance{
					ClusterId:         int64(app.AppShortID),
					UserId:            int64(app.UserShortID),
					Port:              int32(node.Port),
					CreateTime:        time.Now().UTC().Format("2006-01-02 15:04:05"),
					Flavor:            int32(cluster.AvailableVolume),
					Uuid:              node.ResourceId,
					CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
					MasterRedis:       util.GetMasterIP(cluster, node, false),
					SlaverRedis:       util.GetSlaveRedis(cluster, node),
					Status:            0,
					Persistence:       2,
					FixIp:             node.Ip,
					FloatingIp:        node.FloatingIP,
					Password:          node.RootPassword,
					HashName:          util.GetHashName(cluster.ClusterId, app.AppName),
					HostName:          node.HostName,
					IamUserId:         app.UserId,
					ShardId:           0,
					MigrateStatus:     MigrateStatusToCreate,
					HashId:            util.GetHashID(cluster),
					AvailabilityZone:  node.LogicZone,
					SubnetId:          node.SubnetId,
					Ipv6:              node.IPv6,
					ResFlavor:         util.GetResFlavor(cluster),
					StatPort:          22222,
					XagentPort:        int32(node.XagentPort),
					HomePath:          node.Basedir,
					ContainerId:       node.ContainerId,
					ContainerName:     node.ContainerName,
				}
				if len(app.AppGroupID) != 0 && gIps != nil {
					if node.Role == x1model.RoleTypeMaster {
						item.SlaverRedis = gIps.Slave
						item.MasterRedis = ""
						item.CacheInstanceType = 3
					}
					if node.Role == x1model.RoleTypeSlave {
						item.MasterRedis = gIps.Master
						item.SlaverRedis = ""
						item.CacheInstanceType = 2
						if item.MasterRedis == "" {
							// 热活实例组变配的时候，如果扩分片扩的足够多，会补proxy，proxy部署步骤时，cache_instance元数据的master_redis还没有回填（在slave of步骤才会回填）
							// 导致该字段是空的，这时候agent心跳中下发的信息格式不是一个合法ip，nutcracker（即为proxy）拒绝启动。
							// 因此这里在遇到这种case时候（虽然自己是从节点但是master ip是空的，且是热活）的时候，塞入127.0.0.1作为假地址保证格式合法。
							resource.LoggerTask.Trace(ctx, "global slave not have master ip,insert a fake one",
								logit.String("fake", item.FixIp))
							item.MasterRedis = "127.0.0.1"
						}
					}
				}
				creq.Models = append(creq.Models, &item)
			default:
				continue
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	cnodes, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get instance models failed", logit.Error("error", err))
		return err
	}
	clusterShortIDMap := make(map[int]bool, 0)
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID <= 0 {
			foundMaster := false
			var minNodeIdNode *x1model.Node = nil
			for _, node := range cluster.Nodes {
				if minNodeIdNode == nil || node.Id < minNodeIdNode.Id {
					minNodeIdNode = node
				}
				if node.Role == x1model.RoleTypeMaster {
					foundMaster = true
					cnode, err := util.GetCsmasterNodeCuster(cnodes, node)
					if err != nil {
						resource.LoggerTask.Error(ctx, "get csmaster node failed", logit.Error("error", err))
						return err
					}
					cluster.ClusterShortID = int(cnode.Id)
				}
			}
			if !foundMaster {
				if app.AppGroupID == "" {
					return fmt.Errorf("no master node in this shard:%s", cluster.ClusterId)
				}
				if minNodeIdNode == nil {
					return fmt.Errorf("no node in this shard:%s", cluster.ClusterId)
				}
				cnode, err := util.GetCsmasterNodeCuster(cnodes, minNodeIdNode)
				if err != nil {
					resource.LoggerTask.Error(ctx, "get csmaster node failed", logit.Error("error", err))
					return err
				}
				cluster.ClusterShortID = int(cnode.Id)
			}
		}

		if clusterShortIDMap[cluster.ClusterShortID] {
			resource.LoggerTask.Warning(ctx, "duplicate cluster id", logit.Int("cluster short id", cluster.ClusterShortID))
			return errors.New("duplicate cluster id")
		}
		clusterShortIDMap[cluster.ClusterShortID] = true
		resource.LoggerTask.Trace(ctx, "cluster get clustershortid", logit.String("clusterid", cluster.ClusterId),
			logit.Int("cluster shortid", cluster.ClusterShortID))

		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			cnode, err := util.GetCsmasterNodeCuster(cnodes, node)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get csmaster node failed", logit.Error("error", err))
				return err
			}
			node.NodeShortID = int(cnode.Id)
			cnode.ShardId = int64(cluster.ClusterShortID)
			if len(node.NodeFixID) == 0 {
				node.NodeFixID = app.AppId + "_redis_" + strconv.Itoa(cluster.ClusterShortID) + "_" + strconv.Itoa(cluster.MaxNodeIndex)
				cluster.MaxNodeIndex++
			}
			cnode.NodeShowId = node.NodeFixID
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, &csmaster.SaveInstancesParams{
		Models: cnodes,
		AppID:  app.AppId,
		UserID: app.UserId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "update instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

func saveProxys(ctx context.Context, app *x1model.Application) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:  app.AppId,
		UserID: app.UserId,
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			creq.Models = append(creq.Models, &csdk.CsmasterInstance{
				ClusterId:         int64(app.AppShortID),
				UserId:            int64(app.UserShortID),
				Port:              int32(proxy.Port),
				CreateTime:        time.Now().UTC().Format("2006-01-02 15:04:05"),
				Flavor:            int32(itf.AvailableVolume),
				Uuid:              proxy.ResourceId,
				CacheInstanceType: 0,
				Status:            0,
				Persistence:       2,
				FixIp:             proxy.Ip,
				FloatingIp:        proxy.FloatingIP,
				Password:          proxy.RootPassword,
				HostName:          proxy.HostName,
				IamUserId:         app.UserId,
				ShardId:           int64(app.AppShortID),
				MigrateStatus:     0,
				AvailabilityZone:  proxy.LogicZone,
				SubnetId:          proxy.SubnetId,
				Ipv6:              proxy.IPv6,
				ResFlavor:         util.GetProxyResFlavor(itf),
				StatPort:          int32(proxy.StatPort),
				XagentPort:        int32(proxy.XagentPort),
				McpackPort:        int32(proxy.McpackPort),
				HomePath:          proxy.Basedir,
				ContainerId:       proxy.ContainerId,
				ContainerName:     proxy.ContainerName,
			})
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	cnodes, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get instance models failed", logit.Error("error", err))
		return err
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			cnode, err := util.GetCsmasterNodeProxy(cnodes, proxy)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get csmaster node failed", logit.Error("error", err))
				return err
			}
			proxy.ProxyShortID = int(cnode.Id)
			cnode.ShardId = int64(app.AppShortID)
			if len(proxy.NodeFixID) == 0 {
				chunks := strings.Split(proxy.ProxyId, ".")
				offset, err := strconv.Atoi(chunks[len(chunks)-1])
				if err != nil {
					resource.LoggerTask.Warning(ctx, "get proxy offset failed", logit.Error("error", err))
					return err
				}
				proxy.NodeFixID = app.AppId + "_proxy_" + strconv.Itoa(offset)
			}
			cnode.NodeShowId = proxy.NodeFixID
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, &csmaster.SaveInstancesParams{
		Models: cnodes,
		AppID:  app.AppId,
		UserID: app.UserId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "update instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

// saveRoNodes save ro nodes
func saveRoNodes(ctx context.Context, app *x1model.Application) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			fakeNode := util.ChangeRoNode2Node(node)
			masterNode, err := util.GetShardMasterNode(ctx, app, cluster)
			if err != nil || masterNode == nil {
				resource.LoggerTask.Warning(ctx, "master node not found, wait last step to slaveof",
					logit.String("appId", app.AppId),
					logit.String("clusterId", cluster.ClusterId))
				return cerrs.ErrInvalidParams.Errorf("master node not found for app(%s) cluster(%s)",
					app.AppId, cluster.ClusterId)
			}
			masterRedis := masterNode.Ip
			if len(app.AppGroupID) == 0 {
				masterRedis = util.GetMasterRedis(cluster, fakeNode)
			}
			switch node.Status {
			case x1model.NodeOrProxyStatusInUse:
				creq.Models = append(creq.Models, &csdk.CsmasterInstance{
					Uuid:          node.ResourceId,
					MasterRedis:   masterRedis,
					SlaverRedis:   util.GetSlaveRedis(cluster, fakeNode),
					RoGroupID:     util.GetRoGroupID(node),
					RoGroupWeight: int32(node.RoGroupWeight),
					RoGroupStatus: int32(node.RoGroupStatus),
					IsReadOnly:    1,
				})
			case x1model.NodeOrProxyStatusToCreate:
				creq.Models = append(creq.Models, &csdk.CsmasterInstance{
					ClusterId:         int64(app.AppShortID),
					UserId:            int64(app.UserShortID),
					Port:              int32(node.Port),
					CreateTime:        time.Now().UTC().Format("2006-01-02 15:04:05"),
					Flavor:            int32(cluster.AvailableVolume),
					Uuid:              node.ResourceId,
					CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
					MasterRedis:       masterRedis,
					SlaverRedis:       util.GetSlaveRedis(cluster, fakeNode),
					Status:            0,
					Persistence:       2,
					FixIp:             node.Ip,
					FloatingIp:        node.FloatingIP,
					Password:          node.RootPassword,
					HashName:          util.GetHashName(cluster.ClusterId, app.AppName),
					HostName:          node.HostName,
					IamUserId:         app.UserId,
					ShardId:           0,
					MigrateStatus:     MigrateStatusToCreate,
					HashId:            util.GetHashID(cluster),
					AvailabilityZone:  node.LogicZone,
					SubnetId:          node.SubnetId,
					Ipv6:              node.IPv6,
					ResFlavor:         util.GetResFlavor(cluster),
					StatPort:          22222,
					XagentPort:        int32(node.XagentPort),
					HomePath:          node.Basedir,
					RoGroupID:         util.GetRoGroupID(node),
					RoGroupWeight:     int32(node.RoGroupWeight),
					RoGroupStatus:     int32(node.RoGroupStatus),
					IsReadOnly:        1,
					ContainerId:       node.ContainerId,
					ContainerName:     node.ContainerName,
				})
			default:
				continue
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	cnodes, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get instance models failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID <= 0 {
			for _, node := range cluster.RoNodes {
				fakeNode := util.ChangeRoNode2Node(node)
				if node.Role == x1model.RoleTypeMaster {
					cnode, err := util.GetCsmasterNodeCuster(cnodes, fakeNode)
					if err != nil {
						resource.LoggerTask.Error(ctx, "get csmaster node failed", logit.Error("error", err))
						return err
					}
					cluster.ClusterShortID = int(cnode.Id)
				}
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			fakeNode := util.ChangeRoNode2Node(node)
			cnode, err := util.GetCsmasterNodeCuster(cnodes, fakeNode)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get csmaster node failed", logit.Error("error", err))
				return err
			}
			node.NodeShortID = int(cnode.Id)
			cnode.ShardId = int64(cluster.ClusterShortID)
			if len(node.NodeFixID) == 0 {
				node.NodeFixID = app.AppId + "_redis_" + strconv.Itoa(cluster.ClusterShortID) + "_" + strconv.Itoa(cluster.MaxNodeIndex)
				cluster.MaxNodeIndex++
			}
			cnode.NodeShowId = node.NodeFixID
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, &csmaster.SaveInstancesParams{
		Models: cnodes,
		AppID:  app.AppId,
		UserID: app.UserId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "update instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessApplyResourceCallback 将申请的资源写入csmster
func ProcessApplyResourceCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	unlock, err := util.LockForX1modelModify(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := saveNodes(ctx, app); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	if err := saveProxys(ctx, app); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	if err := saveRoNodes(ctx, app); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessApplyResourceCallbackForModifyAZ(ctx context.Context, teu *workflow.TaskExecUnit) error {
	unlock, err := util.LockForX1modelModify(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if err := saveNodes(ctx, app); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	if err := saveProxys(ctx, app); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ModifyInstanceMasterSlaveInfo(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node
		var slaveNodes []*x1model.Node
		var slaveNodeIPs []string
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				} else if node.Role == x1model.RoleTypeSlave {
					slaveNodes = append(slaveNodes, node)
					slaveNodeIPs = append(slaveNodeIPs, node.Ip)
				}
			}
		}
		creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
			Uuid:        masterNode.ResourceId,
			MasterRedis: "",
			SlaverRedis: strings.Join(slaveNodeIPs, ","),
		})
		for _, slaveNode := range slaveNodes {
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:        slaveNode.ResourceId,
				MasterRedis: masterNode.Ip,
				SlaverRedis: "",
			})
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ModifyCsmasterMigrateStatus(ctx context.Context, app *x1model.Application, status int32) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	app.Status = x1model.AppStatusInUse
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:          node.ResourceId,
				MigrateStatus: status,
			})
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:          proxy.ResourceId,
				MigrateStatus: status,
			})
		}
	}

	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}
