package callback

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessSelfHealingCb callback
func ProcessEnableFlashback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}

	if clusterModel.Status == int(csmaster.CsmasterStatusRunning) && clusterModel.EnableRestore == 1 {
		return nil
	}

	utcTime := time.Now().UTC()
	restoreTime := utcTime.Format(time.RFC3339)
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:        csmaster.CsmasterStatusRunning,
			EnableRestore: 1,
			RestoreTime:   restoreTime,
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"enable_restore", "restore_time"},
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}
