/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file analysis_big_key.go
 * <AUTHOR>
 * @date 2023/03/22 15:09:20
 * @brief
 *
 **/

package callback

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	csService "icode.baidu.com/baidu/scs/x1-api/httpserver/services/csmaster"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/common"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"

	baseCsmaster "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessRecoverInOriginalClusterCallBack(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := util.GetRecoverOriginalParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	appBackupId := param.AppBackupID

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 更新cache_cluster表status状态
	if err := csService.UpdateClusterModel(ctx, &baseCsmaster.UpdateClusterModelParams{
		Model: &baseCsmaster.CsmasterCluster{
			Status: csmaster_model_interface.CACHE_CLUSTER_RUNNING,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		errorMessage := "update cluster status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}

	var backupModels []*csmaster_model_interface.BackupRecord
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "batch_id = ?", appBackupId); err != nil {
		return err
	}

	resource.LoggerTask.Notice(ctx, "modify status before", logit.String("backupModels:", base_utils.Format(backupModels)))
	for _, item := range backupModels {
		if cast.ToString(item.BatchID) == appBackupId {
			item.Status = 10 // 恢复成功
		}
	}
	resource.LoggerTask.Notice(ctx, "modify status after", logit.String("backupModels:", base_utils.Format(backupModels)))

	if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, backupModels); err != nil {
		resource.LoggerTask.Error(ctx, "modify status",
			logit.String("backupModels:", base_utils.Format(backupModels)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessRecoverInNewClusterCallBack(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := util.GetRecoverOriginalParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	appBackupId := param.AppBackupID

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 更新cache_cluster表status状态
	if err := csService.UpdateClusterModel(ctx, &baseCsmaster.UpdateClusterModelParams{
		Model: &baseCsmaster.CsmasterCluster{
			Status:       csmaster_model_interface.CACHE_CLUSTER_RUNNING,
			BackupStatus: common.BackupClusterSuccess,
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"backup_status"},
	}); err != nil {
		errorMessage := "update cluster status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}

	var backupModels []*csmaster_model_interface.BackupRecord
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "batch_id = ?", appBackupId); err != nil {
		return err
	}

	resource.LoggerTask.Notice(ctx, "modify status before", logit.String("backupModels:", base_utils.Format(backupModels)))
	for _, item := range backupModels {
		if cast.ToString(item.BatchID) == appBackupId {
			item.Status = 10 // 恢复成功
		}
	}
	resource.LoggerTask.Notice(ctx, "modify status after", logit.String("backupModels:", base_utils.Format(backupModels)))

	if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, backupModels); err != nil {
		resource.LoggerTask.Error(ctx, "modify status",
			logit.String("backupModels:", base_utils.Format(backupModels)),
			logit.Error("err", err))
		return err
	}

	return nil
}

func ProcessRecoverInNewClusterForFlashbackCallBack(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 更新cache_cluster表status状态
	if err := csService.UpdateClusterModel(ctx, &baseCsmaster.UpdateClusterModelParams{
		Model: &baseCsmaster.CsmasterCluster{
			Status:       csmaster_model_interface.CACHE_CLUSTER_RUNNING,
			BackupStatus: common.BackupClusterSuccess,
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"backup_status"},
	}); err != nil {
		errorMessage := "update cluster status failed."
		resource.LoggerTask.Error(ctx, errorMessage, logit.Error("err", err))
		return err
	}

	return nil
}
