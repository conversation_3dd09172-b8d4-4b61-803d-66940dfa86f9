/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* metaserver_cb.go */
/*
modification history
--------------------
2022/06/16 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package callback

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessCbGlobalMetaserverEntrance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	return processCbCsmaster(ctx, app.GlobalMetaserver, app.UserId, app.AppId)
}

func ProcessCbNodeRolesJoin(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	return CbCsmasterNode(ctx, app, "join")
}

func ProcessCbNodeRolesQuit(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	if err := CbCsmasterNode(ctx, app, "quit"); err != nil {
		resource.LoggerTask.Error(ctx, "call cs-master quit node failed.", logit.Error("error", err))
		return err
	}

	if err := ClearAppGroupId(ctx, app); err != nil {
		resource.LoggerTask.Error(ctx, "clear app group id failed.", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessCbLocalMetaserverEntrance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	return processCbCsmaster(ctx, app.LocalMetaserver, app.UserId, app.AppId)
}

func processCbCsmaster(ctx context.Context, metaserverId string, userId string, appid string) error {
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, metaserverId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get meta cluster model failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			MetaserverAddress: metaCluster.Entrance,
		},
		UserID: userId,
		AppID:  appid,
	}); err != nil {
		return err
	}
	return nil
}
