/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessCreateSuccessCb 创建标准版成功的操作
// 1. 将cluster、node表中status改为运行中
// 2. 调用csmaster接口，将实例状态改为运行中
func ProcessCreateSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// param, err := iface.GetParameters(ctx, teu.Parameters)
	// if err != nil {
	//	resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
	//	return err
	// }

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	if subTasksStatus != taskIface.SubTasksStatusSuccess {
		resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
		return fmt.Errorf("sub tasks status is not success")
	}

	// 判断集群是否是clone恢复的集群,如果是则进一步clone老集群的TDE配置
	if err := util.CloneTdeConfIfNeeded(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "clone tde conf failed", logit.Error("error", err))
		return err
	}

	// 判断集群是否是clone恢复的集群,如果是则进一步判断集群是否使用新备份策略,如果是,则创建克隆恢复的任务
	if err := util.CreateCloneRecoverTaskIfNeeded(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "create clone recover task failed", logit.Error("error", err))
		return err
	}

	// 如果集群是use_new_backup,则向dbrs注册备份策略
	if err := util.RegisterBackupPolicyIfNeeded(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "check and register backup policy failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}

	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	app.Status = x1model.AppStatusInUse
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				proxy.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          proxy.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}

	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		if len(cluster.DestSpec) != 0 && cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
	}

	/*
		新建集群后，更新集群cluster、interface表中规格信息，更新信息主要包括：
		mem_size 	<==> 	actual_mem_size
		disk_size 	<==> 	actual_disk_size
		cpu 		<==> 	actual_cpu
	*/
	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
		cluster.ActualCpu = cluster.Cpu
		cluster.ActualMemSize = cluster.MemSize
		cluster.ActualDiskSize = int(cluster.DiskSize)
	}
	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			itf.Spec = itf.DestSpec
		}
		itf.ActualCpu = itf.Cpu
		itf.ActualMemSize = itf.MemSize
		itf.ActualDiskSize = int(itf.DiskSize)
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	// if len(param.ConfTpl) != 0 {
	//	err = config.ApplyConfTpl(ctx, app, param.ConfTpl)
	//	if err != nil {
	//		resource.LoggerTask.Error(ctx, "apply conf tpl fail", logit.Error("err", err))
	//		return err
	//	}
	// }

	return nil
}

// ProcessCreateStandaloneErrorCb 创建标准版失败的操作
// 1. 调用csmaster接口，将实例状态改为创建失败
func ProcessCreateErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	if subTasksStatus == taskIface.SubTasksStatusRunning {
		resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
		return fmt.Errorf("sub tasks status is not success")
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusCreatedFailed,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}
	return nil
}

// ProcessCreateReadonlyInstSuccessCb 创建标准版只读实例成功的操作
func ProcessCreateReadonlyInstSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	// 修改集群replicationNum
	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return err
	}
	addRoInstNum := len(param.GroupParams.CreateRoInstParams.RoInstances)
	targetReplicasCount := csmasterModel.ReplicationNum + int32(addRoInstNum)

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:         csmaster.CsmasterStatusRunning,
			ReplicationNum: targetReplicasCount,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}

	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	app.Status = x1model.AppStatusInUse
	app.UpdateTime = time.Now()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				node.RoGroupStatus = x1model.RoInstAvailable
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
					RoGroupID:     util.GetRoGroupID(node),
					RoGroupWeight: int32(node.RoGroupWeight),
					RoGroupStatus: int32(node.RoGroupStatus),
					IsReadOnly:    1,
				})
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		if len(cluster.DestSpec) != 0 && cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	param, err = iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Error(ctx, "get param success", logit.String("param", base_utils.Format(param)))
	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 更新只读组状态
	if roGroup.Status == x1model.RoGroupCreating || roGroup.Status == x1model.RoGroupUpdating {
		roGroup.Status = x1model.RoGroupAvailable
		if err := resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
			return err
		}
	}
	return nil
}

// ProcessCreateReadonlyInstErrorCb 创建只读实例失败的操作
// 1. 调用csmaster接口，将实例状态改为运行中
func ProcessCreateReadonlyInstErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusModifyFailed,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Error(ctx, "get param success", logit.String("param", base_utils.Format(param)))
	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 更新只读组状态
	if roGroup.Status == x1model.RoGroupCreating {
		roGroup.Status = x1model.RoGroupCreateFail
	} else if roGroup.Status == x1model.RoGroupUpdating {
		roGroup.Status = x1model.RoGroupAvailable
	}
	if err := resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
		return err
	}
	return nil
}
