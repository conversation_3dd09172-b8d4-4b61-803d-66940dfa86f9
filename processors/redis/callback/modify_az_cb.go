/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package callback

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func getCsAZ(ctx context.Context, replicas []iface.Replica) string {
	csAZs := []string{}
	for _, replica := range replicas {
		if replica.Role == "master" {
			csAZs = append(csAZs, replica.Zone)
		}
	}
	for _, replica := range replicas {
		if replica.Role != "master" {
			csAZs = append(csAZs, replica.Zone)
		}
	}
	return strings.Join(csAZs, "+")
}

func getCsAZInfo(ctx context.Context, replicas []iface.Replica) string {
	csAZInfoes := []string{}
	for _, replica := range replicas {
		isMaster := "false"
		if replica.Role == "master" {
			isMaster = "true"
		}
		csAZInfoes = append(csAZInfoes, fmt.Sprintf(`%s,%s,%s,%s`, replica.Zone, replica.SubnetIDs[0],
			isMaster, base_utils.Format(replica.Count)))
	}
	return strings.Join(csAZInfoes, ";")
}

// correctInstanceSubnet 容器实例不迁移可用区迁移子网的场景修正子网信息
// 不修正的话影响控制台展示
func correctInstanceSubnet(ctx context.Context, app *x1model.Application, replicas []iface.Replica) error {
	if app.ResourceType != "container" {
		return nil
	}

	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"subnet_id"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			//新建实例子网都是对的
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			for _, replica := range replicas {
				if replica.Zone != node.LogicZone {
					continue
				}
				for _, subnetId := range replica.SubnetIDs {
					if subnetId != node.SubnetId {
						node.SubnetId = subnetId
						creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
							Uuid:     node.ResourceId,
							SubnetId: subnetId,
						})
					}
				}
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			for _, replica := range replicas {
				if replica.Zone != proxy.LogicZone {
					continue
				}
				for _, subnetId := range replica.SubnetIDs {
					if subnetId != proxy.SubnetId {
						proxy.SubnetId = subnetId
						creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
							Uuid:     proxy.ResourceId,
							SubnetId: subnetId,
						})
					}
				}
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessModifyAZSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("err", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	rss, err := json.Marshal(params.DestReplicas)
	if err != nil {
		return fmt.Errorf("marshal replicas failed,err:%w", err)
	}

	if app.Replicas != string(rss) {
		app.Replicas = string(rss)
		app.DestReplicas = string(rss)
	}

	if err := correctInstanceSubnet(ctx, app, params.DestReplicas); err != nil {
		return err
	}

	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				proxy.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          proxy.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:           csmaster.CsmasterStatusRunning,
			AvailabilityZone: getCsAZ(ctx, params.DestReplicas),
			AzDeployInfo:     getCsAZInfo(ctx, params.DestReplicas),
			DestAzDeployInfo: getCsAZInfo(ctx, params.DestReplicas),
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessModifyAZErrorCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("err", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	rss, err := json.Marshal(params.Replicas)
	if err != nil {
		return fmt.Errorf("marshal replicas failed,err:%w", err)
	}

	if app.Replicas != string(rss) {
		app.Replicas = string(rss)
		app.DestReplicas = string(rss)
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete ||
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				node.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToDelete ||
				proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				proxy.Status = x1model.NodeOrProxyStatusInUse
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:           csmaster.CsmasterStatusModifyFailed,
			AvailabilityZone: getCsAZ(ctx, params.Replicas),
			AzDeployInfo:     getCsAZInfo(ctx, params.Replicas),
			DestAzDeployInfo: getCsAZInfo(ctx, params.Replicas),
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
		return err
	}

	return nil
}
