/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/15, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建标准版成功后清理操作
*/

package callback

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/resize"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessModifyNodesSuccCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return err
	}
	if len(app.DestReplicas) != 0 && app.DestReplicas != app.Replicas {
		app.Replicas = app.DestReplicas
	}
	targetFlavor := csmasterModel.Flavor
	targetNodeType := csmasterModel.NodeType
	targetReplicas := csmasterModel.AzDeployInfo
	targetReplicasCount := csmasterModel.ReplicationNum
	targetInstanceNum := csmasterModel.InstanceNum
	targetDiskFlavor := csmasterModel.DiskFlavor
	targetAzInfo := ""
	replicas := []*iface.Replica{}
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		return err
	}
	for _, replica := range replicas {
		if replica.Role == x1model.RoleTypeMaster {
			targetAzInfo += replica.Zone
		}
	}
	for _, replica := range replicas {
		if replica.Role != x1model.RoleTypeMaster {
			targetAzInfo += "+" + replica.Zone
		}
	}
	for _, cluster := range app.Clusters {
		if len(cluster.DestSpec) != 0 && cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
	}

	// 更新replication_num 字段，需要加上只读实例个数
	readonly_instance_num := 0
	if app.Type == x1model.AppTypeStandalone {
		for _, cluster := range app.Clusters {
			readonly_instance_num += len(cluster.RoNodes)
		}
	}

	if csmasterModel.DestFlavor > 0 && csmasterModel.DestFlavor != targetFlavor {
		targetFlavor = csmasterModel.DestFlavor
	}
	if app.Type == x1model.EngineMc {
		targetFlavor = csmasterModel.Flavor
	}

	if len(csmasterModel.DestNodeType) != 0 && csmasterModel.DestNodeType != targetNodeType {
		targetNodeType = csmasterModel.DestNodeType
	}
	if len(csmasterModel.DestAzDeployInfo) != 0 && csmasterModel.DestAzDeployInfo != targetReplicas {
		targetReplicas = csmasterModel.DestAzDeployInfo
	}
	if csmasterModel.DestReplicationNum != 0 && csmasterModel.DestReplicationNum+int32(readonly_instance_num) != targetReplicasCount {
		targetReplicasCount = csmasterModel.DestReplicationNum + int32(readonly_instance_num)
	}
	if csmasterModel.DestInstanceNum != 0 && csmasterModel.DestInstanceNum != targetInstanceNum {
		targetInstanceNum = csmasterModel.DestInstanceNum
	}
	if csmasterModel.DestDiskFlavor != 0 && csmasterModel.DestDiskFlavor != targetDiskFlavor {
		targetDiskFlavor = csmasterModel.DestDiskFlavor
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:           csmaster.CsmasterStatusRunning,
			NodeType:         targetNodeType,
			AzDeployInfo:     targetReplicas,
			Flavor:           targetFlavor,
			AvailabilityZone: targetAzInfo,
			ReplicationNum:   targetReplicasCount,
			InstanceNum:      targetInstanceNum,
			DiskFlavor:       targetDiskFlavor,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"migrate_status"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if base_utils.FlagExists(node.TempFlags, resize.ResizeFlag) {
				node.TempFlags = base_utils.FlagDel(node.TempFlags, resize.ResizeFlag)
			}
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
		for _, node := range cluster.RoNodes {
			if base_utils.FlagExists(node.TempFlags, resize.ResizeFlag) {
				node.TempFlags = base_utils.FlagDel(node.TempFlags, resize.ResizeFlag)
			}
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				node.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          node.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if base_utils.FlagExists(proxy.TempFlags, resize.ResizeFlag) {
				proxy.TempFlags = base_utils.FlagDel(proxy.TempFlags, resize.ResizeFlag)
			}
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				proxy.Status = x1model.NodeOrProxyStatusInUse
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:          proxy.ResourceId,
					MigrateStatus: MigrateStatusInUse,
				})
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	if err := util.ResetAllNodesSwichable(ctx, app.AppId); err != nil {
		resource.LoggerTask.Error(ctx, "reset all nodes switchable failed", logit.Error("error", err))
		return err
	}
	return nil
}
