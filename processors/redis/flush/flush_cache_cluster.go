/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/10
 * File: flush_cache_cluster.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package flush TODO package function desc
package flush

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	r "github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	flushTimeoutSec = 1800

	flushWaitSlaveDefault = 30 * time.Second
	flushWaitSlaveMax     = 300 * time.Second
	flushWaitSlaveMin     = 1 * time.Second

	flushExpireScanPerSecord = 10000
	flushExpireMinSeconds    = 600
	flushExpireMaxSeconds    = 7000

	flushExpiredScanBatchSize = 100
	flushExpiredScanInterval  = 100 // milliseconds
	flushExpiredScanTimeout   = 6 * time.Hour
)

type Params struct {
	Meta    *xagent.Meta `json:"meta"`
	DbIndex int          `json:"db_index"`
}

type ExpiredParams struct {
	Meta          *xagent.Meta `json:"meta"`
	DbIndex       int          `json:"db_index"`
	ScanBatchSize int          `json:"scan_batch_size"`
	ScanInterval  float64      `json:"scan_interval"`
	Timeout       float64      `json:"timeout"`
}

func processFlush(ctx context.Context, app *x1model.Application, params *iface.Parameters, defaultAcl *x1model.RedisAcl) error {
	var err error
	dbIndex := -1
	if params.DbIndex >= 0 {
		dbIndex = params.DbIndex
	}
	asyncTasks := make(map[string]*xagent.TaskContext, 0)
	mapTaskIdToNode := make(map[string]*x1model.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			// flush 过滤从库
			if node.Role == x1model.RoleTypeMaster {
				params := &Params{
					Meta: &xagent.Meta{
						Engine:        node.Engine,
						EngineVersion: node.EngineVersion,
						Basedir:       node.Basedir,
						Port:          int32(node.Port),
					},
					DbIndex: dbIndex,
				}
				if defaultAcl != nil {
					params.Meta.Password = defaultAcl.Password
				}

				xagentAddr := xagent.Addr{
					Host: node.FloatingIP,
					Port: cast.ToInt32(node.XagentPort),
				}
				flushReq := xagent.AsyncRequest{
					Addr:       &xagentAddr,
					Action:     "flush",
					Params:     params,
					TimeoutSec: flushTimeoutSec,
				}
				resource.LoggerTask.Notice(ctx, "Add Flush Async Mission", logit.String("param:", cast.ToString(flushReq)))
				taskId := cluster.ClusterId + ":" + node.NodeId
				asyncCtx := xagent.Instance().DoAsync(ctx, &flushReq)
				asyncTasks[taskId] = asyncCtx
				mapTaskIdToNode[taskId] = node
			}
		}
	}

	for taskId, task := range asyncTasks {
		_, err = task.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "call xagent flush fail", logit.String("appId", app.AppId),
				logit.Error("err", err), logit.String("clusterId_nodeId", taskId))
			return err
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", app.AppId),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

func processFlushExpired(ctx context.Context, app *x1model.Application) error {
	maxKey, err := getShardMaxKeyCount(ctx, app)
	if err != nil {
		return err
	}
	flushExpireScanPerSecordFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "flush_expired_scan_per_second",
		map[string]string{"iam_user_id": app.UserId, "cluster_show_id": app.AppId}, "10000")
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get resize_pega_cds flag failed", logit.Error("error", err))
		flushExpireScanPerSecordFlag = "10000"
	}
	fesps, err := strconv.ParseInt(flushExpireScanPerSecordFlag, 10, 64)
	if err != nil {
		fesps = flushExpireScanPerSecord
	}
	resource.LoggerTask.Trace(ctx, "flush_expire_scan_per_second", logit.Int64("fesps", fesps))
	curTime := time.Now()
	cost := time.Duration(maxKey/fesps) * time.Second
	if cost < flushExpireMinSeconds*time.Second {
		cost = flushExpireMinSeconds * time.Second
	}
	if cost > flushExpireMaxSeconds*time.Second {
		cost = flushExpireMaxSeconds * time.Second
	}
	deadline := curTime.Add(cost)
	resource.LoggerTask.Trace(ctx,
		fmt.Sprintf("will set active-expire-effort to 10 and reset it to 1 at %s", deadline.Format("2006-01-02 15:04:05")))
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
		ConfItem: &csdk.ConfItem{
			ConfName:   "active-expire-effort",
			ConfModule: 1,
			ConfValue:  "10",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}
	for time.Now().Before(deadline) {
		time.Sleep(5 * time.Second)
	}
	return nil
}

func getShardMaxKeyCount(ctx context.Context, app *x1model.Application) (int64, error) {
	token, err := compo_utils.GetOpenapiToken(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get openapi token fail", logit.Error("err", err))
		return 0, err
	}
	resp, err := csdk.NewDefaultCsmasterSdk().GetClusterInfoKeyspace(ctx, &csdk.SimpleCacheClusterReq{
		Token:              token,
		CacheClusterShowId: app.AppId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster info keyspace fail", logit.Error("err", err))
		return 0, err
	}
	var maxKey int64
	for _, key := range resp.Keys {
		if key > maxKey {
			maxKey = key
		}
	}
	return maxKey, nil
}

func flushExpiredUsingScan(ctx context.Context, app *x1model.Application, taskID string) error {
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	var password string
	if defaultAcl != nil && defaultAcl.Password != "" {
		password, err = crypto_utils.DecryptKey(defaultAcl.Password)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "decrypt defaultAcl password fail", logit.Error("err", err))
			return err
		}
	}
	if err := SetScanDeadlineIfNotExists(ctx, time.Now().Add(flushExpiredScanTimeout), taskID); err != nil {
		resource.LoggerTask.Warning(ctx, "set scan deadline failed", logit.Error("err", err))
		return err
	}
	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node := node
			if node.Role != x1model.RoleTypeMaster {
				continue
			}
			g.Go(func() error {
				for db := 0; db < 256; db++ {
					if err := ScanAllKeysForExpiration(ctx, node.FloatingIP, node.Port, password, db, "*",
						flushExpiredScanBatchSize, flushExpiredScanInterval*time.Millisecond, 10, taskID); err != nil {
						resource.LoggerTask.Warning(ctx, "scan all keys for expiration fail", logit.Error("err", err))
						return err
					}
				}
				return nil
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "flush expired using scan fail", logit.Error("err", err))
		return err
	}
	return nil
}

func ProcessFlushStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.Error("error", err))
		return err
	}
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	if err := processFlush(ctx, app, param, defaultAcl); err != nil {
		resource.LoggerTask.Warning(ctx, "process flush fail", logit.Error("err", err))
		return err
	}
	return nil
}

func ProcessFlushCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	// 集群版redis没有密码
	defaultAcl := util.NoPasswordRedisAcl
	if err := processFlush(ctx, app, param, defaultAcl); err != nil {
		resource.LoggerTask.Warning(ctx, "process flush fail", logit.Error("err", err))
		return err
	}
	return nil
}

func ProcessFlushExpired(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.Error("error", err))
		return err
	}
	if app.Clusters[0].Engine != x1model.EngineRedis {
		resource.LoggerTask.Warning(ctx, "only support redis")
		return nil
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if param.FlushExpiredType == "" {
		param.FlushExpiredType, err = resource.CsmasterOpAgent.GetFlag(
			ctx, "flush_expired_type", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "default")
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get flush_expired_type flag failed", logit.Error("error", err))
			param.FlushExpiredType = "default"
		}
	}
	switch param.FlushExpiredType {
	case "default":
		if err := processFlushExpired(ctx, app); err != nil {
			resource.LoggerTask.Warning(ctx, "process flush expired fail", logit.Error("err", err))
			return err
		}
	case "scan":
		if err := flushExpiredUsingScan(ctx, app, teu.TaskID); err != nil {
			resource.LoggerTask.Warning(ctx, "flush expired using scan fail", logit.Error("err", err))
			return err
		}
	default:
		resource.LoggerTask.Warning(ctx, "flush expired type not supported", logit.String("type", param.FlushExpiredType))
		return fmt.Errorf("flush expired type %s not supported", param.FlushExpiredType)
	}
	return nil
}

func ProcessFinishFlushExpired(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.Error("error", err))
		return err
	}
	if app.Clusters[0].Engine == x1model.EngineRedis {
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
			ConfItem: &csdk.ConfItem{
				ConfName:   "active-expire-effort",
				ConfModule: 1,
				ConfValue:  "1",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
			From:   "admin",
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
			return err
		}
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: 5,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessWaitSlaveFlushComplete(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.Error("error", err))
		return err
	}
	if err := processWaitSlaveFlushComplete(ctx, app, teu.TaskID); err != nil {
		resource.LoggerTask.Warning(ctx, "process flush fail", logit.Error("err", err))
		return err
	}
	return nil
}

func processWaitSlaveFlushComplete(ctx context.Context, app *x1model.Application, taskID string) error {
	// 跳过单副本实例
	for _, cluster := range app.Clusters {
		if util.IsSingleReplica(ctx, cluster) {
			return nil
		}
	}

	// 评估主库flush执行耗时，这里使用 当前步骤开始时间 - 任务开始时间；获取失败时默认30s；获取成功时限制 1s~300s；
	taskDuration := flushWaitSlaveDefault
	taskDetail, err := resource.TaskOperator.GetTaskDetail(ctx, taskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get task detail failed", logit.Error("error", err))
		taskDetail = nil
	}
	if taskDetail != nil {
		taskDuration = taskDetail.StepStartAt.Sub(taskDetail.StartedAt)
	}
	if taskDuration > flushWaitSlaveMax {
		taskDuration = flushWaitSlaveMax
	} else if taskDuration < flushWaitSlaveMin {
		taskDuration = flushWaitSlaveMin
	}

	time.Sleep(taskDuration)
	resource.LoggerTask.Trace(ctx, "wait slave flush complete", logit.String("task_duration", taskDuration.String()))
	return nil
}

func WithDB(db int) single_redis.Option {
	return func(options *r.Options) {
		options.DB = db
	}
}

func ScanAllKeysForExpiration(ctx context.Context, host string, port any, password string,
	db int, pattern string, count int64, interval time.Duration, maxErrorCount int, runningConfigKey string) error {
	c := single_redis.NewClient(host, port,
		single_redis.WithPassword(password),
		single_redis.WithTimeout(&single_redis.ConfigTimeout{
			Connect: 500 * time.Millisecond,
			Read:    1000 * time.Millisecond,
			Write:   1000 * time.Millisecond,
		}),
		WithDB(db),
	)
	defer c.Close()
	nodeKey := fmt.Sprintf("%s_%s:%s", runningConfigKey, host, cast.ToString(port))
	cursor, err := GetCursor(ctx, db, nodeKey)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cursor failed", logit.Error("error", err),
			logit.String("node_key", nodeKey))
		return err
	}
	scanDone, err := GetDBScanDone(ctx, db, nodeKey)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get db scan done failed", logit.Error("error", err),
			logit.String("node_key", nodeKey))
		return err
	}
	if scanDone {
		resource.LoggerTask.Trace(ctx, "db scan done, skip scanning", logit.Int("db", db),
			logit.String("node_key", nodeKey))
		return nil
	}
	var errorCount int
	lastGetConfigTime := time.Now().Add(-2 * time.Minute)
	lastSaveCursorTime := time.Now().Add(-2 * time.Minute)
	lastCheckDeadlineTime := time.Now().Add(-2 * time.Minute) // 初始化为2分钟前，第一次即时检查
	for {
		select {
		case <-ctx.Done():
			// 执行到超时, 不返回错误
			return nil
		default:
			// pass
		}
		if time.Since(lastGetConfigTime) > 1*time.Minute {
			sbs, si, err := GetScanAllKeysForExpirationRunningConfigs(ctx, runningConfigKey)
			if err != nil {
				resource.LoggerTask.Trace(ctx, "get scan all keys for expiration running configs failed",
					logit.Error("error", err), logit.String("running_config_key", runningConfigKey))
			} else {
				if sbs > 0 {
					count = int64(sbs)
				}
				if si > 0 {
					interval = time.Duration(si) * time.Millisecond
				}
				resource.LoggerTask.Trace(ctx, "get scan all keys for expiration running configs",
					logit.Int64("count", count), logit.Duration("interval", interval),
					logit.String("running_config_key", runningConfigKey))
			}
			lastGetConfigTime = time.Now()
		}
		if time.Since(lastSaveCursorTime) > 1*time.Minute {
			if err := SaveCursor(ctx, db, cursor, nodeKey); err != nil {
				resource.LoggerTask.Trace(ctx, "save cursor failed",
					logit.Error("error", err), logit.String("node_key", nodeKey))
			} else {
				resource.LoggerTask.Trace(ctx, "save cursor",
					logit.Uint64("cursor", cursor), logit.Int("db", db),
					logit.String("node_key", nodeKey))
			}
			lastSaveCursorTime = time.Now()
		}
		if time.Since(lastCheckDeadlineTime) > 1*time.Minute {
			overDeadline, err := CheckScanOverDeadline(ctx, runningConfigKey)
			if err != nil {
				resource.LoggerTask.Trace(ctx, "check scan over deadline failed",
					logit.Error("error", err), logit.String("running_config_key", runningConfigKey))
			}
			if overDeadline {
				resource.LoggerTask.Warning(ctx, "scan over deadline, stop scanning",
					logit.Int("db", db), logit.String("running_config_key", runningConfigKey))
				return nil
			}
			lastCheckDeadlineTime = time.Now()
		}
		_, c, err := c.Scan(ctx, cursor, pattern, count).Result()
		if err != nil {
			resource.LoggerTask.Trace(ctx, "scan keys failed",
				logit.Error("error", err), logit.String("running_config_key", runningConfigKey))
			errorCount++
			if errorCount >= maxErrorCount {
				resource.LoggerTask.Warning(ctx, "scan keys failed too many times, stop scanning",
					logit.Int("max_error_count", maxErrorCount), logit.Error("error", err),
					logit.String("running_config_key", runningConfigKey))
				return err
			}
			time.Sleep(interval)
			continue
		}
		cursor = c
		if cursor == 0 {
			resource.LoggerTask.Trace(ctx, "scan all keys done", logit.Int("db", db))
			_ = SaveDBScanDone(ctx, db, nodeKey)
			break
		}
		time.Sleep(interval)
	}
	return nil
}

func GetScanAllKeysForExpirationRunningConfigs(ctx context.Context, runningConfigKey string) (int, int, error) {
	rawScanBatchSize, err := resource.RedisClient.Get(ctx, fmt.Sprintf("%s_scanBatchSize", runningConfigKey)).Result()
	if err != nil {
		return 0, 0, err
	}
	scanBatchSize, err := strconv.Atoi(rawScanBatchSize)
	if err != nil {
		return 0, 0, err
	}
	rawScanInterval, err := resource.RedisClient.Get(ctx, fmt.Sprintf("%s_scanInterval", runningConfigKey)).Result()
	if err != nil {
		return 0, 0, err
	}
	scanInterval, err := strconv.Atoi(rawScanInterval)
	if err != nil {
		return 0, 0, err
	}
	resource.LoggerTask.Trace(ctx, "get scan all keys for expiration running configs",
		logit.Int("scan_batch_size", scanBatchSize),
		logit.Int("scan_interval", scanInterval),
		logit.String("running_config_key", runningConfigKey))
	return scanBatchSize, scanInterval, nil
}

func GetCursor(ctx context.Context, db int, nodeKey string) (uint64, error) {
	cursorStr, err := resource.RedisClient.Get(ctx, fmt.Sprintf("%s_scan_cursor_%d", nodeKey, db)).Result()
	if err != nil {
		if errors.Is(err, r.Nil) {
			return 0, nil // 如果没有找到，返回0
		}
		return 0, err // 其他错误
	}
	cursor, err := strconv.ParseUint(cursorStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse cursor: %w", err)
	}
	return cursor, nil
}

func SaveCursor(ctx context.Context, db int, cursor uint64, nodeKey string) error {
	cursorStr := strconv.FormatUint(cursor, 10)
	err := resource.RedisClient.Set(ctx, fmt.Sprintf("%s_scan_cursor_%d", nodeKey, db), cursorStr, 24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to save cursor: %w", err)
	}
	return nil
}

func SaveDBScanDone(ctx context.Context, db int, nodeKey string) error {
	err := resource.RedisClient.Set(ctx, fmt.Sprintf("%s_scan_done_%d", nodeKey, db), "1", 24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to save db scan done: %w", err)
	}
	resource.LoggerTask.Trace(ctx, "save db scan done", logit.Int("db", db), logit.String("running_config_key", nodeKey))
	return nil
}

func GetDBScanDone(ctx context.Context, db int, nodeKey string) (bool, error) {
	done, err := resource.RedisClient.Get(ctx, fmt.Sprintf("%s_scan_done_%d", nodeKey, db)).Result()
	if err != nil {
		if errors.Is(err, r.Nil) {
			return false, nil // 如果没有找到，返回false
		}
		return false, err // 其他错误
	}
	return done == "1", nil
}

func SetScanDeadlineIfNotExists(ctx context.Context, deadline time.Time, runningConfigKey string) error {
	_, err := resource.RedisClient.Get(ctx, fmt.Sprintf("%s_scan_deadline", runningConfigKey)).Result()
	if err != nil {
		if errors.Is(err, r.Nil) {
			// 如果没有找到，设置deadline
			err = resource.RedisClient.Set(ctx, fmt.Sprintf("%s_scan_deadline", runningConfigKey), deadline.Format(time.RFC3339), 24*time.Hour).Err()
			if err != nil {
				return fmt.Errorf("failed to set scan deadline: %w", err)
			}
			resource.LoggerTask.Trace(ctx, "set scan deadline", logit.String("deadline", deadline.Format(time.RFC3339)),
				logit.String("running_config_key", runningConfigKey))
			return nil
		}
		return err // 其他错误
	}
	return nil
}

func CheckScanOverDeadline(ctx context.Context, runningConfigKey string) (bool, error) {
	deadlineStr, err := resource.RedisClient.Get(ctx, fmt.Sprintf("%s_scan_deadline", runningConfigKey)).Result()
	if err != nil {
		if errors.Is(err, r.Nil) {
			return false, nil // 如果没有找到，返回false
		}
		return false, err // 其他错误
	}
	deadline, err := time.Parse(time.RFC3339, deadlineStr)
	if err != nil {
		return false, fmt.Errorf("failed to parse scan deadline: %w", err)
	}
	return time.Now().After(deadline), nil
}
