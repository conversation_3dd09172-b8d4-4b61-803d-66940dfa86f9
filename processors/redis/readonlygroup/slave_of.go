/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/10 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file slave_of.go
 * <AUTHOR>
 * @date 2022/11/10 09:56:04
 * @brief slave of new master
 *
 **/

package readonlygroup

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessRoInstSlaveOf will execute slave of master
func ProcessRoInstSlaveOf(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}

	// 需要获取acl来访问
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if masterNode == nil {
					masterNode = node
				} else {
					resource.LoggerTask.Warning(ctx, "more than one master", logit.String("master1", base_utils.Format(masterNode)),
						logit.String("master2", base_utils.Format(node)), logit.String("appid", app.AppId), logit.String("clusterId", teu.Entity))
					return errors.Errorf("more than one master")
				}
			}
		}

		for _, node := range cluster.RoNodes {
			// 设置slave of
			err = util.SetSlaveOf(ctx, node.FloatingIP, node.Port, password, masterNode.Ip, cast.ToString(masterNode.Port))
			if err != nil {
				resource.LoggerTask.Warning(ctx, "send slave of master fail", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
					logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)), logit.Error("Error", err))
				return err
			}
			resource.LoggerTask.Notice(ctx, "send slave of master suc", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
				logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)))
		}
	}
	return nil
}
