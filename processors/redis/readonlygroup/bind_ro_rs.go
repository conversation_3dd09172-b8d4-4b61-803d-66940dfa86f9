/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/10 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file bind_ro_rs.go
 * <AUTHOR>
 * @date 2022/11/10 13:47:36
 * @brief bind one readonly instance rs
 *
 **/

package readonlygroup

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	blbV1 "icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbV2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func BindRoInstRs(ctx context.Context, app *x1model.Application, blb *x1model.BLB, node *x1model.RoNode) error {
	// 获取userID
	var userID string
	if len(blb.ResourceUserId) != 0 {
		userID = blb.ResourceUserId
	} else {
		userID = app.UserId
	}
	var uuid string
	if app.ResourceType == "container" {
		uuid = node.ContainerId
	} else {
		uuid = node.ResourceId
	}

	if blb.Type == x1model.BLBTypeReadOnly {
		rss := make([]*blbV1.Rs, 0)
		rss = append(rss, &blbV1.Rs{
			UUID:   uuid,
			Weight: int32(node.RoGroupWeight),
			Port:   int32(node.Port),
		})
		bindReq := &blbV1.BindRsParam{
			UserID: userID,
			BLBIDs: []string{blb.BlbId},
			Rss:    rss,
		}
		// 里面会做是否已经绑定的判断
		if err := blbV1.Instance().BindRs(ctx, bindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blb.BlbId)),
				logit.String("rss", base_utils.Format(rss)))
			return errors.BindRsFail.Wrap(err)
		}
	}

	if blb.Type == x1model.BLBTypeAppReadOnly {
		rss := make([]*blbV2.Rs, 0)
		rss = append(rss, &blbV2.Rs{
			UUID:   uuid,
			Weight: node.RoGroupWeight,
			Port:   node.Port,
			IP:     node.Ip,
		})
		bindReq := &blbV2.BindRsParams{
			UserID:  userID,
			BLBID:   blb.BlbId,
			IPGroup: blb.IPGroupID,
			Rss:     rss,
		}
		if err := blbV2.Instance().BindRs(ctx, bindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blb.BlbId)),
				logit.String("rss", base_utils.Format(rss)))
			return errors.BindRsFail.Wrap(err)
		}
	}
	return nil
}
