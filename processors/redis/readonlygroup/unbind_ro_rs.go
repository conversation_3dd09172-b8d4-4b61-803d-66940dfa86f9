/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/10 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file unbind_ro_rs.go
 * <AUTHOR>
 * @date 2022/11/10 13:47:48
 * @brief unbind one readonly instance rs
 *
 **/

package readonlygroup

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	blbV1 "icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbV2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func UnbindRoInstRs(ctx context.Context, app *x1model.Application, blb *x1model.BLB, node *x1model.RoNode) error {
	// 获取userID
	var userID string
	if len(blb.ResourceUserId) != 0 {
		userID = blb.ResourceUserId
	} else {
		userID = app.UserId
	}

	if blb.Type == x1model.BLBTypeReadOnly {
		uuidList := make([]string, 0)
		var uuid string
		if app.ResourceType == "container" {
			uuid = node.ContainerId
		} else {
			uuid = node.ResourceId
		}

		uuidList = append(uuidList, uuid)
		unbindReq := &blbV1.UnbindRsParam{
			UserID: userID,
			BLBIDs: []string{blb.BlbId},
			UUIDs:  uuidList,
		}
		if err := blbV1.Instance().UnbindRs(ctx, unbindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blb.BlbId)),
				logit.String("UUIDs", base_utils.Format(uuidList)))
			return errors.UnBindRsFail.Wrap(err)
		}
	}

	if blb.Type == x1model.BLBTypeAppReadOnly {
		IPList := make([]string, 0)
		rsIpPort := fmt.Sprintf("%s:%d", node.Ip, node.Port)
		IPList = append(IPList, rsIpPort)
		unbindReq := &blbV2.UnbindRsParams{
			UserID:     userID,
			BLBID:      blb.BlbId,
			IPGroup:    blb.IPGroupID,
			MemberList: IPList,
		}
		err := blbV2.Instance().UnbindRs(ctx, unbindReq)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "unbind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blb.BlbId)),
				logit.String("UUIDs", base_utils.Format(IPList)))
			return errors.UnBindRsFail.Wrap(err)
		}
	}

	return nil
}
