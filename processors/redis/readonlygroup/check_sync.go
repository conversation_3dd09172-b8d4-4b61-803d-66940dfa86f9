/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/10 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file check_sync.go
 * <AUTHOR>
 * @date 2022/11/10 13:47:19
 * @brief check one readonly instance sync info
 *
 **/

package readonlygroup

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func CheckRoInstSync(ctx context.Context, app *x1model.Application, node *x1model.RoNode) error {
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}

	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	for {
		complete := true
		if err := checkRoInstSync(ctx, node, password); err != nil {
			complete = false
			resource.LoggerTask.Notice(ctx, "cluster not sync", logit.Error("err", err))
		}
		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}

// 检查只读实例是否同步
func checkRoInstSync(ctx context.Context, node *x1model.RoNode, password string) error {
	replicationInfo, err := util.GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get replication info failed",
			logit.String("node_id", node.NodeId), logit.Error("error", err))
		return fmt.Errorf("node %s exec info replication failed", node.NodeId)
	}

	if replicationInfo.Role == "slave" && replicationInfo.MasterLinkStatus == "up" {
		return nil
	}

	return nil
}
