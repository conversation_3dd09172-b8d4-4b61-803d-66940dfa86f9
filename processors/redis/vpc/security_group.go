/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package vpc

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/component/vpc"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func getBlbLongID(ctx context.Context, blbID string, userID string) (string, error) {
	listParams := blbv2.BlbIDListParams{
		BlbID:  []string{blbID},
		UserID: userID,
	}
	mapping, err := blbv2.Instance().ExchangeBlbID(ctx, &listParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "exchage blb id faield", logit.Error("error", err))
		return "", err
	}
	var longID string
	for _, item := range mapping {
		if item.ShortID == blbID {
			longID = item.LongID
		}
	}
	if longID == "" {
		return longID, fmt.Errorf("blb long id not found, short id :%s", blbID)
	}
	return longID, nil
}

// copySgsForEndpoint copy sgs from src endpoint to dst endpoint
func copySgsForEndpoint(ctx context.Context, app *x1model.Application) error {
	var srcEndpointID, dstEndpointID string
	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeApp {
			if blb.EndpointId == "" || blb.Status != x1model.BLBStatusAvailable {
				continue
			}
			srcEndpointID = blb.EndpointId
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.EndpointId == "" || blb.Status != x1model.BLBStatusAvailable {
				continue
			}
			dstEndpointID = blb.EndpointId
		}
	}
	resource.LoggerTask.Notice(ctx, "copy sgs from endpoint to endpoint", logit.String("src_endpoint_id", srcEndpointID),
		logit.String("dst_endpoint_id", dstEndpointID))
	if srcEndpointID == "" || dstEndpointID == "" {
		return errors.New("src endpoint id or dst endpoint id is empty")
	}
	if err := vpc.Instance().CopyEndpointSgs(ctx,
		&vpc.CopyEndpointSgsParams{SrcEndpointID: srcEndpointID, DstEndpointID: dstEndpointID, UserID: app.UserId}); err != nil {
		resource.LoggerTask.Error(ctx, "copy sgs from endpoint to endpoint error", logit.String("src_endpoint_id", srcEndpointID),
			logit.String("dst_endpoint_id", dstEndpointID), logit.Error("error", err))
		return err
	}
	return nil
}

// copySgsForBlb copy sgs from src blb to dst blb
func copySgsForBlb(ctx context.Context, app *x1model.Application) error {
	var srcBlbID, dstBlbID string
	var err error
	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			srcBlbID = blb.BlbId
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				continue
			}
			// 线上application的blb id全部是短id
			srcBlbID, err = getBlbLongID(ctx, blb.BlbId, app.UserId)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get blb long id error", logit.String("blb_id", blb.BlbId),
					logit.Error("error", err))
				return err
			}
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				continue
			}
			dstBlbID, err = getBlbLongID(ctx, blb.BlbId, app.UserId)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get blb long id error", logit.String("blb_id", blb.BlbId),
					logit.Error("error", err))
				return err
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "copy sgs from blb to blb", logit.String("src_blb_id", srcBlbID),
		logit.String("dst_blb_id", dstBlbID))

	if srcBlbID == "" || dstBlbID == "" {
		return errors.New("src blb id or dst blb id is empty")
	}

	if err := vpc.Instance().CopyBlbSgs(ctx,
		&vpc.CopyBlbSgsParams{SrcBlbID: srcBlbID, DstBlbID: dstBlbID, UserID: app.UserId}); err != nil {
		resource.LoggerTask.Error(ctx, "copy sgs from blb to blb error", logit.String("src_blb_id", srcBlbID),
			logit.String("dst_blb_id", dstBlbID), logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessCopySgsToNewEntrance copy安全组到新入口
func ProcessCopySgsToNewEntrance(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if app.ResourceType == "container" {
		// 如果容器并且使用了服务网卡
		for _, blb := range app.BLBs {
			if blb.EndpointId != "" {
				return copySgsForEndpoint(ctx, app)
			}
		}
	}
	return copySgsForBlb(ctx, app)
}

func UnbindAllSecurityGroups(ctx context.Context, blb *x1model.BLB, app *x1model.Application) error {
	if blb.EndpointId != "" {
		return vpc.Instance().UnbindAllSgsFromEndpoint(ctx, &vpc.UnbindAllSgsFromEndpointParams{
			UserID:     app.UserId,
			EndpointID: blb.EndpointId,
		})
	}

	blbLongID := ""
	if strings.HasPrefix(blb.BlbId, "lb-") {
		longID, err := getBlbLongID(ctx, blb.BlbId, app.UserId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get long id failed",
				logit.String("id", blb.BlbId),
				logit.Error("err", err))
			return err
		}
		blbLongID = longID
	} else {
		blbLongID = blb.BlbId
	}
	return vpc.Instance().UnbindAllSgsFromBlb(ctx, &vpc.UnbindAllSgsFromBlbParams{
		UserID: app.UserId, LongBlbID: blbLongID})
}

// ProcessUnbindAllSecurityGroups unbind all security groups
func ProcessUnbindAllSecurityGroups(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, blb := range app.BLBs {
		if blb.Status == x1model.BLBStatusAvailable {
			if err := UnbindAllSecurityGroups(ctx, blb, app); err != nil {
				resource.LoggerTask.Error(ctx, "unbind all security groups error", logit.String("blb_id", blb.BlbId),
					logit.Error("error", err))
				return err
			}
		}
	}
	return nil
}
