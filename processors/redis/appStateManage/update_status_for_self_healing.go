package appStateManage

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// imports

// const

// typedefs

// vars

// functions

func UpdateAppStatusForSelfHealing(ctx context.Context, teu *workflow.TaskExecUnit) error {
	isFromXmaster, err := iface.IsTaskFromXmaster(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check task param item from error", logit.Error("error", err))
		return err
	}
	if isFromXmaster {
		app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
			return err
		}
		if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
			Model: &csmaster.CsmasterCluster{
				Status: csmaster.CsmasterStatusReplacing,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}); err != nil {
			resource.LoggerTask.Error(ctx, "cb csmaster err", logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Trace(ctx, "success to update app status to 17 for self-healing task from xmaster")
	}

	return nil
}
