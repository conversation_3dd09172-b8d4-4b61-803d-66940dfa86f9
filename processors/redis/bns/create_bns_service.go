/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package bns

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bns"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type BnsInfo struct {
	BaseName   string
	NodeName   string
	BnsService string
	BnsGroup   string
}

func ProcessCreateBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("error", err))
		return err
	}
	if len(params.BnsName) == 0 {
		resource.LoggerTask.Warning(ctx, "bns name is empty", logit.String("bns name", params.BnsName))
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	bnsInfo := &BnsInfo{
		BaseName:   params.BnsName,
		BnsService: generateBnsService(app.Region, params.BnsName),
		NodeName:   params.BnsName,
	}

	if _, err := bns.BnsResourceOp().BnsNodeCreate(ctx, bnsInfo.NodeName); err != nil {
		resource.LoggerTask.Warning(ctx, "create bns node fail", logit.Error("error", err))
		return err
	}

	if err := bns.BnsResourceOp().BnsServiceCreate(ctx, bnsInfo.BnsService, bnsInfo.NodeName); err != nil {
		resource.LoggerTask.Warning(ctx, "create bns failed", logit.Error("error", err))
		return err
	}

	bnsInfoStr, err := json.Marshal(bnsInfo)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "marshal bns info failed", logit.Error("error", err))
		return err
	}

	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	app.BnsService = string(bnsInfoStr)
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCreateBnsServiceCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.BnsService) == 0 {
		resource.LoggerTask.Notice(ctx, "skip init bns callback")
		return nil
	}

	bnsInfo := &BnsInfo{}
	if err := json.Unmarshal([]byte(app.BnsService), bnsInfo); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal bns info failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			BnsService:  bnsInfo.BnsService,
			BnsGroup:    bnsInfo.BnsGroup,
			BnsNodeName: bnsInfo.NodeName,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}
	return nil
}

func generateBnsService(region, baseName string) string {
	_ = bns.BnsResourceOp()
	return fmt.Sprintf("%s.scs.%s", baseName, bns.TransformBnsIdc(region))
}
