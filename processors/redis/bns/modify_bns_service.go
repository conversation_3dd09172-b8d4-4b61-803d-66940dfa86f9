package bns

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bns"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type ModifyBnsServiceTaskParams struct {
	NewBnsService string `json:"newBnsService"`
	OldBnsService string `json:"oldBnsService"`
}

func ProcessCreateNewBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &ModifyBnsServiceTaskParams{}
	if err := json.Unmarshal([]byte(teu.Parameters), params); err != nil {
		return err
	}
	nodeName := strings.Split(params.NewBnsService, ".scs.")[0]
	_, err := bns.BnsResourceOp().BnsNodeInfo(ctx, nodeName)
	if err != nil {
		resource.LoggerTask.Trace(ctx, "get bns node info failed, need to create", logit.Error("error", err))
		_, err = bns.BnsResourceOp().BnsNodeCreate(ctx, nodeName)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "create bns node failed", logit.Error("error", err))
		}
	}
	if err := bns.BnsResourceOp().BnsServiceCreate(ctx, params.NewBnsService, nodeName); err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestAlreadyExist) {
		resource.LoggerTask.Warning(ctx, "create bns service failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessSetBnsInstanceForNewBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &ModifyBnsServiceTaskParams{}
	if err := json.Unmarshal([]byte(teu.Parameters), params); err != nil {
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if len(app.BnsService) == 0 {
		resource.LoggerTask.Warning(ctx, "bns service is empty")
		return fmt.Errorf("bns service is empty")
	}
	bnsInfo := &BnsInfo{}
	if err := json.Unmarshal([]byte(app.BnsService), bnsInfo); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal bns info failed", logit.Error("error", err))
		return err
	}
	if err := processMoveBnsInstances(ctx, app, params.OldBnsService, params.NewBnsService); err != nil {
		resource.LoggerTask.Warning(ctx, "move bns instances failed", logit.Error("error", err))
		return err
	}
	bnsInfo.BnsService = params.NewBnsService
	bnsInfo.BaseName = strings.Split(params.NewBnsService, ".scs.")[0]
	bnsInfo.NodeName = bnsInfo.BaseName
	rawBnsService, err := json.Marshal(bnsInfo)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "marshal bns info failed", logit.Error("error", err))
		return err
	}
	app.BnsService = string(rawBnsService)
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app failed", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			BnsService:  bnsInfo.BnsService,
			BnsNodeName: bnsInfo.NodeName,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessUpdateLocalBnsGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &ModifyBnsServiceTaskParams{}
	if err := json.Unmarshal([]byte(teu.Parameters), params); err != nil {
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	bnsInfo := &BnsInfo{}
	if err := json.Unmarshal([]byte(app.BnsService), bnsInfo); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal bns info failed", logit.Error("error", err))
		return err
	}
	if bnsInfo.BnsGroup == "" {
		return nil
	}
	bnsGroupServices, err := bns.BnsResourceOp().BnsGroupServices(ctx, bnsInfo.BnsGroup)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get bns group services failed", logit.Error("error", err))
		return err
	}
	for _, service := range bnsGroupServices {
		if service == params.OldBnsService {
			if err := bns.BnsResourceOp().BnsGroupModifyServices(ctx, &bns.ModifyBnsGroupParam{
				BnsGroup:    bnsInfo.BnsGroup,
				BnsServices: []string{params.OldBnsService},
				Action:      "remove",
			}); err != nil {
				resource.LoggerTask.Warning(ctx, "modify bns group services failed", logit.Error("error", err))
				return err
			}
			break
		}
	}
	found := false
	for _, service := range bnsGroupServices {
		if service == params.NewBnsService {
			found = true
			break
		}
	}
	if !found {
		if err := bns.BnsResourceOp().BnsGroupModifyServices(ctx, &bns.ModifyBnsGroupParam{
			BnsGroup:    bnsInfo.BnsGroup,
			BnsServices: []string{params.NewBnsService},
			Action:      "add",
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "modify bns group services failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func ProcessUpdateGlobalBnsGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &ModifyBnsServiceTaskParams{}
	if err := json.Unmarshal([]byte(teu.Parameters), params); err != nil {
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	var groupID string
	if app.AppGroupID != "" {
		groupID = app.AppGroupID
	} else if app.SyncGroupID != "" {
		groupID = app.SyncGroupID
	}
	if groupID == "" {
		return nil
	}
	if err := gmaster.GlobalMasterOp().UpdateBnsService(ctx, &gmaster.UpdateBnsServiceParams{
		AppID:         app.AppId,
		GroupID:       groupID,
		OldBnsService: params.OldBnsService,
		NewBnsService: params.NewBnsService,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update bns service failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessDeleteOldBnsService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &ModifyBnsServiceTaskParams{}
	if err := json.Unmarshal([]byte(teu.Parameters), params); err != nil {
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	nodeName := strings.Split(params.OldBnsService, ".scs.")[0]
	if err := bns.BnsResourceOp().BnsServiceClearInstance(ctx, params.OldBnsService); err != nil {
		resource.LoggerTask.Warning(ctx, "clear bns service instance failed", logit.Error("error", err))
	}
	if err := bns.BnsResourceOp().BnsServiceDelete(ctx, params.OldBnsService); err != nil && !cerrs.Is(err, cerrs.ErrBNSRequestNotFound) {
		resource.LoggerTask.Warning(ctx, "delete bns service failed", logit.Error("error", err))
		return err
	}
	_, err = bns.BnsResourceOp().BnsNodeInfo(ctx, nodeName)
	if err == nil {
		if err := bns.BnsResourceOp().BnsNodeDelete(ctx, nodeName); err != nil {
			resource.LoggerTask.Warning(ctx, "delete bns node failed", logit.Error("error", err))
			return err
		}
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: 5,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}
