/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
SCS标准版实例创建过程中，生成对应的X1数据结构
*/

package bns

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bns"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	bnssdk "icode.baidu.com/baidu/scs/x1-base/sdk/bns"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type Port struct {
	Main       string `json:"main"`
	XAgentPort string `json:"xagent_port"`
	StatPort   string `json:"stat_port"`
	McpackPort string `json:"mcpack_port"`
}

func ProcessSetBnsInstances(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if len(app.BnsService) == 0 {
		resource.LoggerTask.Notice(ctx, "skip set bns instances")
		return nil
	}

	return processSetBnsInstances(ctx, app)
}

func processSetBnsInstances(ctx context.Context, app *x1model.Application) error {
	bnsInfo := &BnsInfo{}
	if err := json.Unmarshal([]byte(app.BnsService), bnsInfo); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal bns info failed", logit.Error("error", err))
		return err
	}

	bnsInstancesResp, err := bns.BnsResourceOp().BnsServiceInstanceInfo(ctx, bnsInfo.BnsService)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list bns service instances failed", logit.Error("error", err))
		return err
	}

	curBnsInstancesSet := make(map[string]*bnssdk.BnsInstanceInfoDecode)
	for _, bnsInst := range bnsInstancesResp.InstanceInfo {
		// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/l2h2MOSkO9/C2KN2sqmI3pLTU
		// https://blog.csdn.net/libinemail/article/details/120324281
		bnsInst := bnsInst
		port := &Port{}
		if err := json.Unmarshal([]byte(bnsInst.Port), port); err != nil {
			resource.LoggerTask.Warning(ctx, "parse port json string failed", logit.Error("error", err))
			return err
		}
		curBnsInstancesSet[bnsInst.HostName+":"+port.Main] = &bnsInst
	}

	// 删除 bns 不存在的 proxy
	curProxySet := make(map[string]*x1model.Proxy)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusInUse || proxy.Status == x1model.NodeOrProxyStatusToCreate {
				curProxySet[proxy.Ip+":"+strconv.Itoa(proxy.Port)] = proxy
			}
		}
	}

	for ipPort, bnsInst := range curBnsInstancesSet {
		if _, found := curProxySet[ipPort]; found {
			continue
		}
		instanceID, err := strconv.Atoi(bnsInst.Offset)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "parse instance id failed", logit.Error("error", err))
			return err
		}
		if err := bns.BnsResourceOp().BnsServiceDeleteInstance(ctx, &bns.BnsDeleteInstanceParam{
			BnsService: bnsInfo.BnsService,
			HostName:   bnsInst.HostName,
			InstanceId: instanceID,
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "del instance failed", logit.Error("error", err))
			return err
		}
	}

	// 创建 bns 节点
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse && proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := curBnsInstancesSet[proxy.Ip+":"+strconv.Itoa(proxy.Port)]; found {
				continue
			}
			instanceID, err := strconv.Atoi(strings.Split(proxy.ProxyId, ".")[len(strings.Split(proxy.ProxyId, "."))-1])
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse instance id failed", logit.Error("error", err))
				return err
			}

			// ContainerId 为 BCC UUID, 容器实例 ID 为 ResourceId
			containerID := proxy.ContainerId
			if app.ResourceType == "container" {
				containerID = proxy.ResourceId
			}
			if err := bns.BnsResourceOp().BnsServiceAddInstance(ctx, &bns.BnsAddInstanceParam{
				BnsService: bnsInfo.BnsService,
				Region:     app.Region,
				HostName:   proxy.Ip,
				Port: map[string]string{
					"main":        strconv.Itoa(proxy.Port),
					"stat_port":   strconv.Itoa(proxy.StatPort),
					"mcpack_port": strconv.Itoa(proxy.McpackPort),
					"xagent_port": strconv.Itoa(proxy.XagentPort),
				},
				Disable:         0,
				InstanceId:      instanceID,
				DeployPath:      proxy.Basedir,
				HealthCheckCmd:  "port:" + strconv.Itoa(proxy.Port),
				HealthCheckType: "proc",
				ContainerId:     containerID,
			}); err != nil {
				resource.LoggerTask.Warning(ctx, "add instance failed", logit.Error("error", err))
				return err
			}
		}
	}
	return nil
}

func processMoveBnsInstances(ctx context.Context, app *x1model.Application, oldBns, newBns string) error {
	oldBnsInstanceResp, err := bns.BnsResourceOp().BnsServiceInstanceInfo(ctx, oldBns)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list old bns service instances failed", logit.Error("error", err))
		return err
	}
	newBnsInstanceResp, err := bns.BnsResourceOp().BnsServiceInstanceInfo(ctx, newBns)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list new bns service instances failed", logit.Error("error", err))
		return err
	}
	oldBnsInstanceSet := make(map[string]*bnssdk.BnsInstanceInfoDecode)
	for _, bnsInst := range oldBnsInstanceResp.InstanceInfo {
		bnsInst := bnsInst
		port := &Port{}
		if err := json.Unmarshal([]byte(bnsInst.Port), port); err != nil {
			resource.LoggerTask.Warning(ctx, "parse port json string failed", logit.Error("error", err))
			return err
		}
		oldBnsInstanceSet[bnsInst.HostName+":"+port.Main] = &bnsInst
	}
	newBnsInstanceSet := make(map[string]*bnssdk.BnsInstanceInfoDecode)
	for _, bnsInst := range newBnsInstanceResp.InstanceInfo {
		bnsInst := bnsInst
		port := &Port{}
		if err := json.Unmarshal([]byte(bnsInst.Port), port); err != nil {
			resource.LoggerTask.Warning(ctx, "parse port json string failed", logit.Error("error", err))
			return err
		}
		newBnsInstanceSet[bnsInst.HostName+":"+port.Main] = &bnsInst
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			oldInfo, oldHas := oldBnsInstanceSet[proxy.Ip+":"+strconv.Itoa(proxy.Port)]
			instID, err := strconv.Atoi(oldInfo.Offset)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse instance id failed", logit.Error("error", err))
				return err
			}
			if oldHas {
				if err := bns.BnsResourceOp().BnsServiceDeleteInstance(ctx, &bns.BnsDeleteInstanceParam{
					BnsService: oldBns,
					HostName:   oldInfo.HostName,
					InstanceId: instID,
				}); err != nil {
					resource.LoggerTask.Warning(ctx, "del instance failed", logit.Error("error", err))
					return err
				}
			}
			_, newHas := newBnsInstanceSet[proxy.Ip+":"+strconv.Itoa(proxy.Port)]
			if !newHas {
				instanceID, err := strconv.Atoi(strings.Split(proxy.ProxyId, ".")[len(strings.Split(proxy.ProxyId, "."))-1])
				if err != nil {
					resource.LoggerTask.Warning(ctx, "parse instance id failed", logit.Error("error", err))
					return err
				}
				containerID := proxy.ContainerId
				if app.ResourceType == "container" {
					containerID = proxy.ResourceId
				}
				if err := bns.BnsResourceOp().BnsServiceAddInstance(ctx, &bns.BnsAddInstanceParam{
					BnsService: newBns,
					Region:     app.Region,
					HostName:   proxy.Ip,
					Port: map[string]string{
						"main":        strconv.Itoa(proxy.Port),
						"stat_port":   strconv.Itoa(proxy.StatPort),
						"mcpack_port": strconv.Itoa(proxy.McpackPort),
						"xagent_port": strconv.Itoa(proxy.XagentPort),
					},
					Disable:         0,
					InstanceId:      instanceID,
					DeployPath:      proxy.Basedir,
					HealthCheckCmd:  "port:" + strconv.Itoa(proxy.Port),
					HealthCheckType: "proc",
					ContainerId:     containerID,
				}); err != nil {
					resource.LoggerTask.Warning(ctx, "add instance failed", logit.Error("error", err))
					return err
				}
			}
		}
		// 每个itf的proxys都处理完后，等待500ms, 避免bns服务端处理过快导致的异常
		time.Sleep(500 * time.Millisecond)
	}
	return nil
}

func ProcessClearBnsInstances(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if len(app.BnsService) == 0 {
		resource.LoggerTask.Notice(ctx, "skip set bns instances")
		return nil
	}
	bnsInfo := &BnsInfo{}
	if err := json.Unmarshal([]byte(app.BnsService), bnsInfo); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal bns info failed", logit.Error("error", err))
		return err
	}
	if err := bns.BnsResourceOp().BnsServiceClearInstance(ctx, bnsInfo.BnsService); err != nil {
		resource.LoggerTask.Warning(ctx, "clear bns service instances failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessDeleteBnsInstancesForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if len(app.BnsService) == 0 {
		resource.LoggerTask.Notice(ctx, "skip set bns instances")
		return nil
	}

	return processDeleteBnsInstances(ctx, app)
}

func processDeleteBnsInstances(ctx context.Context, app *x1model.Application) error {
	bnsInfo := &BnsInfo{}
	if err := json.Unmarshal([]byte(app.BnsService), bnsInfo); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal bns info failed", logit.Error("error", err))
		return err
	}

	bnsInstancesResp, err := bns.BnsResourceOp().BnsServiceInstanceInfo(ctx, bnsInfo.BnsService)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list bns service instances failed", logit.Error("error", err))
		return err
	}

	curBnsInstancesSet := make(map[string]*bnssdk.BnsInstanceInfoDecode)
	for _, bnsInst := range bnsInstancesResp.InstanceInfo {
		// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/l2h2MOSkO9/C2KN2sqmI3pLTU
		// https://blog.csdn.net/libinemail/article/details/120324281
		bnsInst := bnsInst
		port := &Port{}
		if err := json.Unmarshal([]byte(bnsInst.Port), port); err != nil {
			resource.LoggerTask.Warning(ctx, "parse port json string failed", logit.Error("error", err))
			return err
		}
		curBnsInstancesSet[bnsInst.HostName+":"+port.Main] = &bnsInst
	}

	// 删除 bns 不存在的 proxy
	curProxySet := make(map[string]*x1model.Proxy)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusInUse || proxy.Status == x1model.NodeOrProxyStatusToCreate {
				curProxySet[proxy.Ip+":"+strconv.Itoa(proxy.Port)] = proxy // 若在申请资源之前执行该步骤，可能因节点ip为空而异常
			}
		}
	}

	for ipPort, bnsInst := range curBnsInstancesSet {
		if _, found := curProxySet[ipPort]; found {
			continue
		}
		instanceID, err := strconv.Atoi(bnsInst.Offset)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "parse instance id failed", logit.Error("error", err))
			return err
		}
		if err := bns.BnsResourceOp().BnsServiceDeleteInstance(ctx, &bns.BnsDeleteInstanceParam{
			BnsService: bnsInfo.BnsService,
			HostName:   bnsInst.HostName,
			InstanceId: instanceID,
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "del instance failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}
