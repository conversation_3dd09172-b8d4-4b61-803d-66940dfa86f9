package initmachineenv

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/vep"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

type XagentRequest struct {
	SetNameserverParam []string `json:"set_nameserver_param,omitempty"`
}

func ProcessInitMachineEnv(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processInitMachineEnv(ctx, teu.Entity); err != nil {
		resource.LoggerTask.Error(ctx, "Process Init Machine Env Fail",
			logit.String("appId", teu.Entity),
			logit.Error("err", err))
		return err
	}
	if err := processInitBcettmForEdge(ctx, teu.Entity); err != nil {
		resource.LoggerTask.Error(ctx, "Process Init Bcettm For Edge Fail",
			logit.String("appId", teu.Entity),
			logit.Error("err", err))
		return err
	}
	return nil
}

func processInitBcettmForEdge(ctx context.Context, appID string) error {
	if !common.IsEdgeRegion() {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}
	if err := checkMachineStarted(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "Check Machine Started Fail")
		return err
	}
	g := gtask.Group{Concurrent: 25}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			host, port, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(node.FloatingIP, 22)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse vep host fail",
					logit.String("floatingIP", node.FloatingIP),
					logit.Int("port", 22),
					logit.Error("err", err))
				return err
			}
			password := node.RootPassword
			g.Go(func() error {
				if err := InstallBCETTM(ctx, host+":"+strconv.Itoa(port), "root", PasswordAuth(password)); err != nil {
					resource.LoggerTask.Error(ctx, "Install BCETTM Fail",
						logit.String("host", host),
						logit.Int("port", port),
						logit.Error("err", err))
					return err
				}
				return nil
			})
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			host, port, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(proxy.FloatingIP, 22)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse vep host fail",
					logit.String("floatingIP", proxy.FloatingIP),
					logit.Int("port", 22),
					logit.Error("err", err))
				return err
			}
			password := proxy.RootPassword
			g.Go(func() error {
				if err := InstallBCETTM(ctx, host+":"+strconv.Itoa(port), "root", PasswordAuth(password)); err != nil {
					resource.LoggerTask.Error(ctx, "Install BCETTM Fail",
						logit.String("host", host),
						logit.Int("port", port),
						logit.Error("err", err))
					return err
				}
				return nil
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Error(ctx, "Install BCETTM Fail",
			logit.String("appId", appID),
			logit.Error("err", err))
		return err
	}
	return nil
}

func processInitMachineEnv(ctx context.Context, appID string) error {

	if conf.MachineEnvConfIns.Nameservers == "" {
		resource.LoggerTask.Notice(ctx, "No need to init machine env")
		return nil
	}
	nameservers := strings.Split(conf.MachineEnvConfIns.Nameservers, ",")

	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}

	// 检测机器是否已经启动
	if err := checkMachineStarted(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "Check Machine Started Fail")
		return err
	}

	reqList := make([]*xagent.AsyncRequest, 0)

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "init_machine_env",
				Params: &XagentRequest{
					SetNameserverParam: nameservers,
				},
			}
			reqList = append(reqList, req)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "init_machine_env",
				Params: &XagentRequest{
					SetNameserverParam: nameservers,
				},
			}
			reqList = append(reqList, req)
		}
	}
	g := gtask.Group{Concurrent: 25}
	for _, req := range reqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Error(ctx, "init machine fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Error(ctx, "Init machine step fail.")
		return err
	}

	return nil
}

// 检测实例均已连通
func checkMachineStarted(ctx context.Context, app *x1model.Application) error {

	reqList := make([]*xagent.AsyncRequest, 0)

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "echo",
				Params: &XagentRequest{},
			}
			reqList = append(reqList, req)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "echo",
				Params: &XagentRequest{},
			}
			reqList = append(reqList, req)
		}
	}

	var err error
	for i := 0; i < 30; i++ {
		g := gtask.Group{Concurrent: 25}
		for _, req := range reqList {
			req := req
			g.Go(func() error {
				_, err := xagent.Instance().DoAsync(ctx, req).Wait()
				if err != nil {
					resource.LoggerTask.Error(ctx, "Echo xagent fail.",
						logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
						logit.Error("err", err))
				}
				return err
			})
		}
		_, err = g.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "Echo xagent fail.", logit.Int("Retry", i+1))
			time.Sleep(time.Second * 10)
		} else {
			return nil
		}
	}

	return err
}
