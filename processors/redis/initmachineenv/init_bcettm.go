package initmachineenv

import (
	"bytes"
	"context"
	"fmt"
	"io/ioutil"
	"time"

	"golang.org/x/crypto/ssh"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// InstallBCETTM 通过SSH连接在远程服务器上安装BCETTM模块
// 参数:
//
//	host: 远程服务器地址 (如 "*************:22")
//	username: SSH用户名
//	authMethod: SSH认证方式，可以是密码或密钥
//
// 返回:
//
//	error: 如安装成功返回nil，否则返回相应错误
func InstallBCETTM(ctx context.Context, host, username string, authMethod ssh.AuthMethod) error {
	// 定义安装脚本内容
	installScript := `#!/usr/bin/env bash

cd /root

if [ $(lsmod | grep bcettm | wc -l) -gt 0 ]; then
    echo "BCETTM is already installed."
    exit 0
fi

[[ -d BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64 ]] && rm -rf BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64
[[ -f BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz ]] && rm -f BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz
wget -O 'BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz' 'http://************:35004/scs-pkgs-for-op-tools-sz/BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz?authorization=bce-auth-v1%2F91884b9e79a442f29f8b41106d060e72%2F2025-05-29T04%3A19%3A19Z%2F-1%2F%2F644c46dde9f3beaadd08431caa88cfae8f6aeaa3b408ac1e0b8c3d32161ebf18'
mkdir -p BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64
tar -xzf BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64.tgz -C BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64 --strip-components=1

mv ./BCETTM_Centos7_4.18.0-305.19.1.el8_4.x86_64/bcettm.ko /lib/modules/$(uname -r)/kernel/net/ipv4/
insmod /lib/modules/$(uname -r)/kernel/net/ipv4/bcettm.ko

if [ $(grep -c "bcettm.ko" /etc/rc.local) -eq 0 ]; then
    echo "insmod /lib/modules/$(uname -r)/kernel/net/ipv4/bcettm.ko" >> /etc/rc.local
fi

sleep 1

if [ $(lsmod | grep bcettm | wc -l) -gt 0 ]; then
    echo "BCETTM installed successfully."
    exit 0
else
    echo "BCETTM installation failed."
    exit 1
fi
`

	// 配置SSH客户端
	config := &ssh.ClientConfig{
		User: username,
		Auth: []ssh.AuthMethod{
			authMethod,
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         30 * time.Second,
	}

	// 连接到远程服务器
	client, err := ssh.Dial("tcp", host, config)
	if err != nil {
		return fmt.Errorf("failed to dial: %w", err)
	}
	defer client.Close()

	// 创建SSH会话
	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}
	defer session.Close()

	// 设置标准输出和标准错误的缓冲区
	var stdout, stderr bytes.Buffer
	session.Stdout = &stdout
	session.Stderr = &stderr

	// 执行安装脚本
	err = session.Run(installScript)
	if err != nil {
		return fmt.Errorf("script execution failed: %w\nStderr: %s", err, stderr.String())
	}

	// 打印输出
	resource.LoggerTask.Trace(ctx, "BCETTM installation output", logit.String("output", stdout.String()))

	// 检查是否安装成功
	if bytes.Contains(stdout.Bytes(), []byte("BCETTM installed successfully")) {
		return nil
	} else if bytes.Contains(stdout.Bytes(), []byte("BCETTM is already installed")) {
		return nil
	} else {
		return fmt.Errorf("installation did not complete successfully: %s", stdout.String())
	}
}

// 使用密码方式认证的辅助函数
func PasswordAuth(password string) ssh.AuthMethod {
	return ssh.Password(password)
}

// 使用私钥方式认证的辅助函数
func PrivateKeyAuth(keyPath string) (ssh.AuthMethod, error) {
	privateKey, err := ioutil.ReadFile(keyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read private key: %w", err)
	}

	signer, err := ssh.ParsePrivateKey(privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	return ssh.PublicKeys(signer), nil
}
