/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/12
 * File: restart_slaves.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package restart TODO package function desc
package restart

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/readonlygroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/upgrade"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	restartTimeoutSec = 50
	pingTimeoutSec    = 300
)

type restartParams struct {
	WorkDir string `json:"workdir"`
}

// ProcessRestartSlavesStandalone restart slave
func ProcessRestartSlavesStandalone(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}
	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	password := ""
	if app.UseNewPackage == 1 || app.UseNewAgent == "yes" {
		password, err = util.GetRedisCryptedPassword(ctx, app)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get redis password failed")
			return err
		}
	}
	if err := processRestartSlaves(ctx, teu, app, password, cmdList, teu.TaskID); err != nil {
		return err
	}

	return nil
}

func ProcessRestartSlavesCluster(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}
	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	if err := processRestartSlaves(ctx, teu, app, "", cmdList, teu.TaskID); err != nil {
		return err
	}

	return nil
}

func ProcessUpgradeProxies(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	deployClient := deploy.NewDefaultClient()
	itfMap := make(map[string]*x1model.Interface)
	var proxies []*x1model.Proxy
	for _, itf := range app.Interfaces {
		itfMap[itf.InterfaceId] = itf
		for _, proxy := range itf.Proxys {
			proxies = append(proxies, proxy)
		}
	}

	// 每次升级一半代理，避免升级过程中断流量
	if app.UseNewPackage == 1 {
		if err := upgrade.ProcessUpgradeProxiesNew(ctx, app, itfMap, proxies[0:len(proxies)/2], teu.TaskID); err != nil {
			resource.LoggerTask.Warning(ctx, "upgrade proxy failed", logit.Error("error", err))
			return err
		}
		if err := upgrade.ProcessUpgradeProxiesNew(ctx, app, itfMap, proxies[len(proxies)/2:], teu.TaskID); err != nil {
			resource.LoggerTask.Warning(ctx, "upgrade proxy failed", logit.Error("error", err))
			return err
		}
	} else {
		if err := processRestartProxiesForGlobal(ctx, app, proxies[0:len(proxies)/2], deployClient); err != nil {
			return err
		}
		if err := processRestartProxiesForGlobal(ctx, app, proxies[len(proxies)/2:], deployClient); err != nil {
			return err
		}
	}
	return nil
}

const (
	upgradeTimeoutSec = 500
)

func processRestartProxiesForGlobal(ctx context.Context, app *x1model.Application, proxys []*x1model.Proxy, deployClient deploy.Service) error {
	asyncTasks := make(map[string]*xagent.TaskContext, 0)
	mapTaskIdToProxy := make(map[string]*x1model.Proxy, 0)
	for _, proxy := range proxys {
		deployConfRsp, err := deployClient.GetXcacheDeployConf(ctx, &deploy.GetXcacheDeployConfRequest{
			Conf: &deploy.XcacheConf{
				PackageTag:     "xcache",
				Version:        "",
				WorkDir:        proxy.Basedir,
				PORT:           int32(proxy.Port),
				ServerId:       proxy.ProxyId,
				MaxSpace:       20,
				GlobalSeqId:    cast.ToInt64(proxy.GlobalSeqID),
				AppGlobalSeqId: cast.ToInt64(app.AppGroupSeqID),
			},
		})
		if !util.NeedGlobalInfo(app) {
			deployConfRsp.Conf.AppGlobalSeqId = 0
			deployConfRsp.Conf.GlobalSeqId = 0
		}
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get scs scache conf fail", logit.String("proxyId", proxy.ProxyId), logit.Error("error", err))
			return err
		}
		xagentAddr := xagent.Addr{
			Host: proxy.FloatingIP,
			Port: cast.ToInt32(proxy.XagentPort),
		}
		restartReq := xagent.AsyncRequest{
			Addr:   &xagentAddr,
			Action: "upgrade",
			Params: &upgrade.ScsDeployConf{
				PackageName: deployConfRsp.Package.PackageName,
				PackageUrl:  deployConfRsp.Package.PackageUri,
				XcacheConf:  deployConfRsp.Conf,
			},
			TimeoutSec: upgradeTimeoutSec,
		}
		asyncCtx := xagent.Instance().DoAsync(ctx, &restartReq)
		taskId := proxy.InterfaceId + ":" + proxy.ProxyId
		asyncTasks[taskId] = asyncCtx
		mapTaskIdToProxy[taskId] = proxy
	}

	var completeProxys []*x1model.Proxy
	for taskId, task := range asyncTasks {
		_, err := task.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "upgrade proxy fail", logit.String("proxyId", mapTaskIdToProxy[taskId].ProxyId), logit.Error("error", err))
			continue
		}
		resource.LoggerTask.Notice(ctx, "upgrade proxy success", logit.String("proxyId", mapTaskIdToProxy[taskId].ProxyId))
		mapTaskIdToProxy[taskId].Status = x1model.NodeOrProxyStatusInUse
		completeProxys = append(completeProxys, mapTaskIdToProxy[taskId])
	}
	if err := x1model.ProxysSave(ctx, completeProxys); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy status fail", logit.Error("error", err))
		return err
	}
	if len(completeProxys) != len(asyncTasks) {
		resource.LoggerTask.Warning(ctx, "upgrade proxy fail", logit.Int("success", len(completeProxys)), logit.Int("total", len(asyncTasks)))
		return fmt.Errorf("upgrade proxy fail,success:%d,total:%d", len(completeProxys), len(asyncTasks))
	}

	for _, proxy := range proxys {
		if err := util.PingTest(ctx, proxy.FloatingIP, proxy.Port, pingTimeoutSec, nil); err != nil && !strings.Contains(err.Error(), "NOAUTH") {
			resource.LoggerTask.Warning(ctx, "ping proxy failed",
				logit.String("proxyId", proxy.ProxyId),
				logit.String("floatingIp", proxy.FloatingIP),
				logit.Int("port", proxy.Port),
				logit.Error("err", err),
			)
			return err
		}
	}
	return nil
}

// ProcessRestartSlavesStandaloneInGroup restart slave in group
func ProcessRestartSlavesStandaloneInGroup(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}

	if len(app.AppGroupID) == 0 {
		return nil
	}
	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return err
	}
	if err := processRestartSlavesInGroup(ctx, teu, app, cmdList, password, teu.TaskID); err != nil {
		return err
	}
	return nil
}

// ProcessRestartSlavesClusterInGroup restart slave in group
func ProcessRestartSlavesClusterInGroup(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) == 0 {
		return nil
	}

	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return err
	}
	if err := processRestartSlavesInGroup(ctx, teu, app, cmdList, password, teu.TaskID); err != nil {
		return err
	}
	return nil
}

// ProcessRestartForCreateGroup 用于让配置生效
func ProcessRestartForCreateGroup(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}
	isNeedConfigset, err := util.NeedConfigSetForJoinGroup(ctx, app)
	if err != nil {
		return err
	}
	if !isNeedConfigset {
		return nil
	}
	return ProcessRestartSlavesCluster(ctx, teu)
}

// ProcessRestartForJoinGroup 用于让配置生效
func ProcessRestartForJoinGroup(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}
	isNeedConfigset, err := util.NeedConfigSetForJoinGroup(ctx, app)
	if err != nil {
		return err
	}
	if !isNeedConfigset {
		return nil
	}
	return ProcessRestartSlavesClusterInGroup(ctx, teu)
}

// ProcessRestartReadonlyInstances restart readonly instances slave
func ProcessRestartReadonlyInstances(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}

	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return err
	}
	if err := processRestartReadOnlyInstances(ctx, teu, app, cmdList, password, teu.TaskID); err != nil {
		return err
	}
	return nil
}

// processRestartSlaves will restart/upgrade slaves one by one
func processRestartSlaves(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, password string, cmdList string, taskID string) error {
	deployClient := deploy.NewDefaultClient()
	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if masterNode == nil {
					masterNode = node
				} else {
					resource.LoggerTask.Warning(ctx, "more than one master", logit.String("master1", base_utils.Format(masterNode)),
						logit.String("master2", base_utils.Format(node)), logit.String("appid", app.AppId), logit.String("clusterId", teu.Entity))
					return errors.Errorf("more than one master")
				}
			}
		}

		for _, node := range cluster.Nodes {
			if (node.Role == x1model.RoleTypeSlave && !util.IsSingleReplica(ctx, cluster)) &&
				(node.Status == x1model.NodeOrProxyStatusToRestart || node.Status == x1model.NodeOrPorxyStatusRestarted) {
				if err := processRestartOneSlave(ctx, app, cluster, node, masterNode, deployClient, false, cmdList, password, taskID); err != nil {
					return err
				}
			}
		}
	}

	// 保存节点状态
	err := x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

// processRestartOneSlave will restart one slave
func processRestartOneSlave(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster,
	node, masterNode *x1model.Node, deployClient deploy.Service, inGroup bool, cmdList string, password, taskID string) error {
	if node.Status == x1model.NodeOrProxyStatusToRestart {
		if app.UseNewPackage == 1 {
			if err := upgrade.ExecuteRestartNew(ctx, app, cluster, node, cmdList, password, taskID, true); err != nil {
				return err
			}
		} else {
			if err := upgrade.ExecuteUpgrade(ctx, app, cluster, node, deployClient); err != nil {
				return err
			}
		}

		resource.LoggerTask.Notice(ctx, "node restart suc", logit.String("nodeId", node.NodeId))

		node.Status = x1model.NodeOrPorxyStatusRestarted
		if err := x1model.NodesSave(ctx, []*x1model.Node{node}); err != nil {
			if err != nil {
				resource.LoggerTask.Warning(ctx, "save node fail", logit.String("nodeId", node.NodeFixID),
					logit.Error("dbError", err))
				return err
			}
		}
	}
	// 当前old agent还有设置topo的逻辑，需要等待5s，避免太快，agent设置topo覆盖了task设置的topo
	time.Sleep(5 * time.Second)
	if err := upgrade.SetSlaveOfForStandalone(ctx, app, node, masterNode, password, !inGroup); err != nil {
		resource.LoggerTask.Warning(ctx, "set slave of master fail",
			logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
			logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)), logit.Error("Error", err))
		return err
	}

	if !inGroup {
		// check one redis sync
		if password != "" {
			if pw, err := crypto_utils.DecryptKey(password); err == nil {
				password = pw
			}
		}
		if err := util.CheckOneRedisSyncWithPassword(ctx, app, node, password); err != nil {
			resource.LoggerTask.Warning(ctx, "check redis inst sync failed", logit.String("appId", app.AppId),
				logit.String("clusterId", app.AppId), logit.Error("err", err),
				logit.String("nodeId", node.NodeId))
			return err
		}
	}

	node.Status = x1model.NodeOrProxyStatusInUse
	if err := x1model.NodesSave(ctx, []*x1model.Node{node}); err != nil {
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save  ro node fail", logit.String("nodeId", node.NodeFixID),
				logit.Error("dbError", err))
			return err
		}
	}
	return nil
}

// processRestartReadOnlyInstances will only restart/upgrade readonly instances one by one
func processRestartReadOnlyInstances(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, cmdList, password, taskID string) error {
	deployClient := deploy.NewDefaultClient()
	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if masterNode == nil {
					masterNode = node
				} else {
					resource.LoggerTask.Warning(ctx, "more than one master", logit.String("master1", base_utils.Format(masterNode)),
						logit.String("master2", base_utils.Format(node)), logit.String("appid", app.AppId), logit.String("clusterId", teu.Entity))
					return errors.Errorf("more than one master")
				}
			}
		}

		for _, node := range cluster.RoNodes {
			if node.Role == x1model.RoleTypeSlave &&
				(node.Status == x1model.NodeOrProxyStatusToRestart || node.Status == x1model.NodeOrPorxyStatusRestarted) {
				if err := processRestartOneReadonlyInstance(ctx, app, cluster, node, masterNode, deployClient, false, cmdList, password, taskID); err != nil {
					return err
				}
			}
		}
	}

	// 保存节点状态
	err := x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

// processRestartOneReadonlyInstance will restart one readonly instance
// 获取只读组id, 查询对应blb, list ip group member, 存在则删除， 不存在则进行下一步
// 重启只读实例+ slave of
// 重启完检查同步
// 获取只读组id, 查询对应blb, list ip group member, 存在则返回成功，不存在则进行添加
// 完成单个只读实例的重启完整流程
func processRestartOneReadonlyInstance(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster,
	node *x1model.RoNode, masterNode *x1model.Node, deployClient deploy.Service, inGroup bool, cmdList, password, taskID string) error {
	// unbind rs
	var blb *x1model.BLB = nil
	for _, b := range app.BLBs {
		if b.RoGroupID == node.RoGroupID {
			blb = b
			break
		}
	}
	if blb == nil {
		resource.LoggerTask.Warning(ctx, "blb not found", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId),
			logit.String("nodeId", node.NodeId))
		return errors.Errorf("readonly inst not found blb")
	}

	if err := readonlygroup.UnbindRoInstRs(ctx, app, blb, node); err != nil {
		resource.LoggerTask.Warning(ctx, "unbind ro inst rs failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	// 重启节点
	if node.Status == x1model.NodeOrProxyStatusToRestart {
		if app.UseNewPackage == 1 {
			if err := upgrade.ExecuteRestartNew(ctx, app, cluster, util.ChangeRoNode2Node(node), cmdList, password, taskID, true); err != nil {
				return err
			}
		} else {
			if err := upgrade.ExecuteUpgrade(ctx, app, cluster, util.ChangeRoNode2Node(node), deployClient); err != nil {
				return err
			}
		}

		resource.LoggerTask.Notice(ctx, "node restart suc", logit.String("nodeId", node.NodeId))

		// 更新节点状态为已重启
		node.Status = x1model.NodeOrPorxyStatusRestarted
		if err := x1model.RoNodesSave(ctx, []*x1model.RoNode{node}); err != nil {
			if err != nil {
				resource.LoggerTask.Warning(ctx, "save  ro node fail", logit.String("nodeId", node.NodeFixID),
					logit.Error("dbError", err))
				return err
			}
		}
	}

	if inGroup {
		// 获取可用的master节点
		master, err := util.GetShardMasterNode(ctx, app, cluster)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "find readonly master fail", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
				logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)), logit.Error("Error", err))
			return err
		}
		masterNode = master
	}

	// 当前old agent还有设置topo的逻辑，需要等待5s，避免太快，agent设置topo覆盖了task设置的topo
	time.Sleep(5 * time.Second)
	if err := upgrade.SetSlaveOfForStandalone(ctx, app, util.ChangeRoNode2Node(node), masterNode, password, true); err != nil {
		resource.LoggerTask.Warning(ctx, "set slave of master fail",
			logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
			logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)), logit.Error("Error", err))
		return err
	}

	// check sync
	if err := readonlygroup.CheckRoInstSync(ctx, app, node); err != nil {
		resource.LoggerTask.Warning(ctx, "check ro inst sync failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	// bind rs
	if err := readonlygroup.BindRoInstRs(ctx, app, blb, node); err != nil {
		resource.LoggerTask.Warning(ctx, "rebind ro inst rs failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	node.Status = x1model.NodeOrProxyStatusInUse
	if err := x1model.RoNodesSave(ctx, []*x1model.RoNode{node}); err != nil {
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save ro node fail", logit.String("nodeId", node.NodeFixID),
				logit.Error("dbError", err))
			return err
		}
	}
	return nil
}

func processRestartSlavesInGroup(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, cmdList, password, taskID string) error {
	// 进行重启
	deployClient := deploy.NewDefaultClient()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if (node.Role == x1model.RoleTypeSlave && !util.IsSingleReplica(ctx, cluster)) &&
				(node.Status == x1model.NodeOrProxyStatusToRestart || node.Status == x1model.NodeOrPorxyStatusRestarted) {
				if err := processRestartOneSlave(ctx, app, cluster, node, nil, deployClient, true, cmdList, password, taskID); err != nil {
					return err
				}
			}
		}
	}

	// 保存节点状态
	err := x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

// ProcessRestartRoInstStandaloneInGroup restart readonly instance in group
func ProcessRestartRoInstStandaloneInGroup(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) == 0 {
		return nil
	}

	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}

	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return err
	}

	if err := processRestartRoInstInGroup(ctx, teu, app, cmdList, password, teu.TaskID); err != nil {
		return err
	}
	return nil
}

// processRestartRoInstInGroup will restart readonly inst
func processRestartRoInstInGroup(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application, cmdList, password, taskID string) error {
	// 进行重启
	deployClient := deploy.NewDefaultClient()
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Role == x1model.RoleTypeSlave &&
				(node.Status == x1model.NodeOrProxyStatusToRestart || node.Status == x1model.NodeOrPorxyStatusRestarted) {
				if err := processRestartOneReadonlyInstance(ctx, app, cluster, node, nil, deployClient, true, cmdList, password, taskID); err != nil {
					return err
				}
			}
		}
	}

	// 保存节点状态
	err := x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}
