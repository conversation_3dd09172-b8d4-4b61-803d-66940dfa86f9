/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/12
 * File: change_master.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package restart TODO package function desc
package restart

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessHandoverStandalone(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	if len(app.Clusters) == 0 {
		resource.LoggerTask.Warning(ctx, "app not has no cluser")
		err = errors.Errorf("app not has one cluser , app id:%s", app.AppId)
		return
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}

	// 只获取inuse的acl
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.String("app id:", app.AppId),
			logit.Error("error", err))
		return
	}
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	for _, cluster := range app.Clusters {
		if util.IsSingleReplica(ctx, cluster) {
			resource.LoggerTask.Notice(ctx, "is single replica,jumped", logit.String("clusterinfo:", base_utils.Format(cluster)))
			continue
		}
		var oldMaster *x1model.Node
		useForbidWrite := false
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				oldMaster = node
				break
			}
		}
		if oldMaster != nil {
			isMaster, err := util.IsMaster(ctx, oldMaster.FloatingIP, oldMaster.Port, password)
			if err != nil {
				return err
			}
			if !isMaster {
				resource.LoggerTask.Warning(ctx, "old master is not real master",
					logit.String("clusterinfo:", base_utils.Format(cluster)),
					logit.String("oldMaster:", base_utils.Format(oldMaster)),
				)
				oldMaster = nil
			}
		}
		if oldMaster != nil {
			useForbidWrite = handover.KernalSupportForbidWrite(ctx, cluster, app, oldMaster)
		} else {
			resource.LoggerTask.Warning(ctx, "master node not found, maybe has been switched", logit.String("clusterinfo:", base_utils.Format(cluster)))
		}
		var newMaster *x1model.Node = nil
		newMaster, err = util.MasterSwitchShard(ctx, &util.MasterSwitchReq{
			Acl:            acl,
			ClusterInfo:    cluster,
			App:            app,
			IsManualSwitch: true,
			UseForbidWrite: useForbidWrite,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "switch fail", logit.String("app id:", app.AppId),
				logit.Error("error", err))
			return err
		}

		for _, node := range cluster.Nodes {
			if node.NodeId == newMaster.NodeId {
				node.Role = x1model.RoleTypeMaster
			} else {
				node.Role = x1model.RoleTypeSlave
			}
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	// 通知csmaster
	err = cbCsmaster(ctx, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "save handover ret to csmaster failed", logit.Error("error", err))
		return err
	}

	return nil
}

func cbCsmaster(ctx context.Context, app *x1model.Application) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	csmasterInstanceFounder, err := util.GetCsmasterInstanceFounder(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster instance founder failed", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if csmasterInstanceFounder(node.ResourceId) == nil {
				continue
			}
			creq.Models = append(creq.Models, &csdk.CsmasterInstance{
				Uuid:              node.ResourceId,
				CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
				MasterRedis:       util.GetMasterRedis(cluster, node),
				SlaverRedis:       util.GetSlaveRedis(cluster, node),
			})
		}
		for _, node := range cluster.RoNodes {
			if csmasterInstanceFounder(node.ResourceId) == nil {
				continue
			}
			fakenode := util.ChangeRoNode2Node(node)
			creq.Models = append(creq.Models, &csdk.CsmasterInstance{
				Uuid:              node.ResourceId,
				CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
				MasterRedis:       util.GetMasterRedis(cluster, fakenode),
				SlaverRedis:       util.GetSlaveRedis(cluster, fakenode),
				RoGroupID:         util.GetRoGroupID(node),
				RoGroupWeight:     int32(node.RoGroupWeight),
				RoGroupStatus:     int32(node.RoGroupStatus),
				IsReadOnly:        1,
			})
		}
	}
	resource.LoggerTask.Notice(ctx, "start to cb csmaster", logit.String("req", base_utils.Format(creq)))
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.String("req", base_utils.Format(creq)), logit.Error("error", err))
		return err
	}

	return nil
}
