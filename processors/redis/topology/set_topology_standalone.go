/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
2021/12/27, by shangshuai02(<EMAIL>), 实现ProcessInitStandaloneTopo
*/

/*
DESCRIPTION
设置标准版集群拓扑
*/

package topology

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/resize"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessInitStandaloneTopo 初始化标准版集群的拓扑
// 1. 对所有新建的从角色的节点，发送slaveof 主节点
func processInitStandaloneTopoLocal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	if len(app.Clusters) == 0 {
		resource.LoggerTask.Warning(ctx, "clusters not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("clusters for app(%s) not found", teu.Entity)
	}

	// 获取acl信息
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
		return err
	}

	// 获取nodes信息 clusterId->node
	masterNodes := map[string]*x1model.Node{}
	slaveNodes := map[string][]*x1model.Node{}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Role == x1model.RoleTypeMaster && isNodeAvailable(node) {
				// 有多个主的时候选择新主
				if _, ok := masterNodes[cluster.ClusterId]; ok {
					if node.Status == x1model.NodeOrProxyStatusToCreate {
						masterNodes[node.ClusterId] = node
					}
				} else {
					masterNodes[node.ClusterId] = node
				}
				// 选择状态为ToCreate的从节点发送slave of； InUse状态不需要发送
			} else if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusToCreate ||
				(node.Status == x1model.NodeOrProxyStatusInUse && base_utils.FlagExists(node.TempFlags, resize.ResizeFlag))) {
				slaveNodes[node.ClusterId] = append(slaveNodes[node.ClusterId], node)
			}
		}
	}

	if len(slaveNodes) == 0 {
		return nil
	}

	// 检查master nodes
	for clusterId := range slaveNodes {
		if _, ok := masterNodes[clusterId]; !ok {
			resource.LoggerTask.Warning(ctx, "master node not found",
				logit.String("appId", app.AppId),
				logit.String("clusterId", clusterId))
			return cerrs.ErrInvalidParams.Errorf("master node not found for app(%s) cluster(%s)",
				app.AppId, clusterId)
		}
	}

	// 执行slave of
	g := gtask.Group{
		Concurrent:    slaveOfConcurrent,
		AllowSomeFail: false,
	}

	totalCount := 0
	for clusterId, nodes := range slaveNodes {
		master := masterNodes[clusterId]
		for _, node := range nodes {
			master := master
			node := node
			g.Go(func() error {
				return doSlaveOf(ctx, node, master, acl)
			})

			totalCount++
		}
	}
	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur during doSlaveOf",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doSlaveOfError", err))
		return err
	}

	return nil
}

// ProcessTopoForRo 只读实例处理拓扑关系
func ProcessTopoForRo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	if len(app.Clusters) == 0 {
		resource.LoggerTask.Warning(ctx, "clusters not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("clusters for app(%s) not found", teu.Entity)
	}

	// 获取acl信息
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
		return err
	}

	// 获取nodes信息 clusterId->node
	masterNodes := map[string]*x1model.Node{}
	readonlyNodes := map[string][]*x1model.RoNode{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			// inuse状态或者有switchable tag的才能作为只读实例的master
			if node.Role == x1model.RoleTypeMaster && util.IsNodeSwitchable(node) {
				masterNodes[node.ClusterId] = node
			}
		}
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Role == x1model.RoleTypeSlave && isRoNodeAvailable(node) {
				readonlyNodes[node.ClusterId] = append(readonlyNodes[node.ClusterId], node)
			}
		}
	}

	// 检查master nodes
	for clusterID := range readonlyNodes {
		if _, ok := masterNodes[clusterID]; !ok {
			resource.LoggerTask.Warning(ctx, "master node not found",
				logit.String("appId", app.AppId),
				logit.String("clusterId", clusterID))
			return cerrs.ErrInvalidParams.Errorf("master node not found for app(%s) cluster(%s)",
				app.AppId, clusterID)
		}
	}

	// 执行slave of
	g := gtask.Group{
		Concurrent:    slaveOfConcurrent,
		AllowSomeFail: false,
	}

	totalCount := 0
	for clusterID, nodes := range readonlyNodes {
		master := masterNodes[clusterID]
		for _, node := range nodes {
			master := master
			node := node
			g.Go(func() error {
				return RoInstancedoSlaveOf(ctx, node, master, acl)
			})

			totalCount++
		}
	}
	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur during doSlaveOf",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doSlaveOfError", err))
		return err
	}

	// 更新csmaster数据库里面的只读节点的masterredis字段
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:              node.ResourceId,
				CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
				MasterRedis:       masterNodes[node.ClusterId].Ip,
				SlaverRedis:       "",
			})
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	return nil
}

// ProcessInitStandaloneTopo will process standalone topo
func ProcessInitStandaloneTopo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if len(app.AppGroupID) == 0 {
		return processInitStandaloneTopoLocal(ctx, teu)
	}
	return nil
}

// ProcessTopoForRoInGroup 热活中只读实例处理拓扑关系
func ProcessTopoForRoInGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	if len(app.Clusters) == 0 {
		resource.LoggerTask.Warning(ctx, "clusters not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("clusters for app(%s) not found", teu.Entity)
	}

	// 集群不在热活实例组中，不用处理
	if len(app.AppGroupID) == 0 {
		return nil
	}

	// 如果没有只读实例，直接退出
	if len(app.Clusters[0].RoNodes) == 0 {
		return nil
	}

	// 获取acl信息
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
		return err
	}

	// 获取可以作为只读组的主节点
	masterNodes := map[string]*x1model.Node{}
	readonlyNodes := map[string][]*x1model.RoNode{}
	for _, cluster := range app.Clusters {
		masterNode, err := util.GetShardMasterNode(ctx, app, cluster)
		if err != nil || masterNode == nil {
			resource.LoggerTask.Warning(ctx, "master node not found, wait last step to slaveof",
				logit.String("appId", app.AppId),
				logit.String("clusterId", cluster.ClusterId))
			return cerrs.ErrInvalidParams.Errorf("master node not found for app(%s) cluster(%s)",
				app.AppId, cluster.ClusterId)
		}
		masterNodes[masterNode.ClusterId] = masterNode
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Role == x1model.RoleTypeSlave && isRoNodeAvailable(node) {
				readonlyNodes[node.ClusterId] = append(readonlyNodes[node.ClusterId], node)
			}
		}
	}

	// 检查master nodes
	for clusterID := range readonlyNodes {
		if _, ok := masterNodes[clusterID]; !ok {
			resource.LoggerTask.Warning(ctx, "master node not found",
				logit.String("appId", app.AppId),
				logit.String("clusterId", clusterID))
			return cerrs.ErrInvalidParams.Errorf("master node not found for app(%s) cluster(%s)",
				app.AppId, clusterID)
		}
	}

	// 执行slave of
	g := gtask.Group{
		Concurrent:    slaveOfConcurrent,
		AllowSomeFail: false,
	}

	totalCount := 0
	for clusterID, nodes := range readonlyNodes {
		master := masterNodes[clusterID]
		for _, node := range nodes {
			master := master
			node := node
			g.Go(func() error {
				return RoInstancedoSlaveOf(ctx, node, master, acl)
			})

			totalCount++
		}
	}
	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur during doSlaveOf",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doSlaveOfError", err))
		return err
	}

	// 更新csmaster数据库里面的只读节点的masterredis字段
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Role == x1model.RoleTypeSlave && isRoNodeAvailable(node) {
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:              node.ResourceId,
					CacheInstanceType: int32(util.GetCacheInstanceType(node.Engine, node.Role)),
					MasterRedis:       masterNodes[node.ClusterId].Ip,
					SlaverRedis:       "",
				})
			}
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessSingleReplicaMasterRedisCorrect(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return err
	}
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	count := 0
	for _, node := range app.Clusters[0].Nodes {
		if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusToCreate {
			item := csdk.CsmasterInstance{
				Uuid:        node.ResourceId,
				MasterRedis: "",
				SlaverRedis: "",
			}
			creq.Models = append(creq.Models, &item)
			count++
		}
	}
	if count == 1 {
		if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
			resource.LoggerTask.Error(ctx, "update instance models failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

// ProcessInitStandaloneGlobalTopo 热活组新节点拓扑处理，用于变配
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/p4L1DJjqguDBso
func ProcessInitStandaloneGlobalTopo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	// 非热活直接返回
	if len(app.AppGroupID) == 0 {
		return nil
	}
	if app.Type != x1model.AppTypeStandalone {
		return errors.New("app type is not standalone")
	}
	if len(app.Clusters) != 1 {
		return errors.New("clusters count is not 1")
	}

	if metaserver.IsGlobalLeader(ctx, app) {
		return processInitStandaloneTopoGlobal(ctx, app)
	}
	// 从地域无操作
	return nil
}

// processInitStandaloneTopoGlobal 初始化标准版热活组的拓扑
// 1. 对所有新建的从角色的节点，发送slaveof 主节点
func processInitStandaloneTopoGlobal(ctx context.Context, app *x1model.Application) error {
	if app == nil {
		return errors.New("app is nil")
	}
	// 获取nodes信息 clusterId->node
	toCreateMasterNode := map[string]*x1model.Node{}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Role == x1model.RoleTypeMaster &&
				node.Status == x1model.NodeOrProxyStatusToCreate {
				if _, ok := toCreateMasterNode[node.ClusterId]; ok {
					return fmt.Errorf("cluster %s has more than 1 new master node", cluster.ClusterId)
				}

				toCreateMasterNode[node.ClusterId] = node
			}
		}
	}

	// 检查master nodes
	for _, cluster := range app.Clusters {
		if _, ok := toCreateMasterNode[cluster.ClusterId]; !ok {
			resource.LoggerTask.Warning(ctx, "master node not found",
				logit.String("appId", app.AppId),
				logit.String("clusterId", cluster.ClusterId))
			return fmt.Errorf("master node not found for app(%s) cluster(%s)",
				app.AppId, cluster.ClusterId)
		}
		masterNode := toCreateMasterNode[cluster.ClusterId]
		gInitStandaloneTopoReq := &gmaster.InitStandaloneTopoReq{
			AppID:          app.AppId,
			UserID:         app.UserId,
			AppGroupID:     app.AppGroupID,
			ShardID:        cluster.GlobalID,
			NewMasterID:    masterNode.NodeId,
			NewMasterFixIP: masterNode.Ip,
			NewMasterPort:  masterNode.Port,
		}
		if err := gmaster.GlobalMasterOp().InitStandaloneTopo(ctx, gInitStandaloneTopoReq); err != nil {
			resource.LoggerTask.Warning(ctx, "call global-api init standalone group topo fail",
				logit.String("req", base_utils.Format(gInitStandaloneTopoReq)), logit.Error("err", err))
			return fmt.Errorf("call global-api init standalone group topo fail: %w", err)
		}
		resource.LoggerTask.Trace(ctx, "call global-api init standalone group topo success",
			logit.String("req", base_utils.Format(gInitStandaloneTopoReq)))
	}

	return nil
}
