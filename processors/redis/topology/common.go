package topology

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// isNodeAvailable
func isNodeAvailable(node *x1model.Node) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse ||
		node.Status == x1model.NodeOrProxyStatusToCreate
}

// isNodeAvailable
func isRoNodeAvailable(node *x1model.RoNode) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse ||
		node.Status == x1model.NodeOrProxyStatusToCreate
}

// isRoNodeInUse
func isRoNodeInUse(node *x1model.RoNode) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse
}

// doSlaveOf 执行slave of
func doSlaveOf(ctx context.Context, node, master *x1model.Node, acl *x1model.RedisAcl) error {
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	c := single_redis.NewClient(node.FloatingIP, node.Port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	if master != nil {
		if err := util.SlaveOfRenamed(ctx, c, master.Ip, cast.ToString(master.Port)); err != nil {
			resource.LoggerTask.Warning(ctx, "do slave of master fail",
				logit.String("nodeId", node.NodeId),
				logit.String("masterId", master.NodeId),
			)
			return err
		}

		resource.LoggerTask.Notice(ctx, "do slave of master successfully",
			logit.String("nodeId", node.NodeId),
			logit.String("masterId", master.NodeId))
		return nil
	}

	if err := util.SlaveOfRenamed(ctx, c, "NO", "ONE"); err != nil {
		resource.LoggerTask.Warning(ctx, "do slave of no one fail",
			logit.String("nodeId", node.NodeId))
		return err
	}

	resource.LoggerTask.Notice(ctx, "do slave of no one successfully",
		logit.String("nodeId", node.NodeId))
	return nil
}

// RoInstancedoSlaveOf 执行slave of
func RoInstancedoSlaveOf(ctx context.Context, node *x1model.RoNode, master *x1model.Node, acl *x1model.RedisAcl) error {
	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	c := single_redis.NewClient(node.FloatingIP, node.Port,
		single_redis.WithPassword(password),
	)
	defer c.Close()

	if master != nil {
		if err := util.SlaveOfRenamed(ctx, c, master.Ip, cast.ToString(master.Port)); err != nil {
			resource.LoggerTask.Warning(ctx, "do slave of master fail",
				logit.String("nodeId", node.NodeId),
				logit.String("masterId", master.NodeId),
			)
			return err
		}

		resource.LoggerTask.Notice(ctx, "do slave of master successfully",
			logit.String("nodeId", node.NodeId),
			logit.String("masterId", master.NodeId))
		return nil
	}

	if err := util.SlaveOfRenamed(ctx, c, "NO", "ONE"); err != nil {
		resource.LoggerTask.Warning(ctx, "do slave of no one fail",
			logit.String("nodeId", node.NodeId))
		return err
	}

	resource.LoggerTask.Notice(ctx, "do slave of no one successfully",
		logit.String("nodeId", node.NodeId))
	return nil
}
