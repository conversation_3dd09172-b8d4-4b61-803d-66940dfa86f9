package crossAzNearest

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	taskIface "icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
)

const (
	ProxyCPUThreshold = 60
)

func ProcessAddOpProxy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application error",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	var Replicas []*taskIface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &Replicas); err != nil {
		resource.LoggerTask.Error(ctx, "unmarshal replicas error", logit.Error("error", err))
		return fmt.Errorf("unmarshal replicas error, err: %w", err)
	}
	azSubnet := make(map[string]string)
	for _, rep := range Replicas {
		azSubnet[rep.Zone] = rep.SubnetIDs[0]
	}
	baseProxyShortIDs := make([]int64, 0)
	azProxyCnt := make(map[string]int)
	nonOpAzProxyCnt := make(map[string]int)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			baseProxyShortIDs = append(baseProxyShortIDs, int64(proxy.ProxyShortID))
			azProxyCnt[proxy.LogicZone]++
			if !strings.Contains(proxy.ProxyId, "-itfop-") {
				nonOpAzProxyCnt[proxy.LogicZone]++
			}
		}
	}

	monStartTime, monEndTime, interval := getHistoryTimeForMonquery(time.Now(), 600)
	monParam := &opmonitor.MonqueryLeastInsCntParam{
		BaseInsShortIDs: baseProxyShortIDs,
		Item:            opmonitor.ProcCPUUsage,
		SampleFunc:      opmonitor.SampleFuncAvg,
		Start:           monStartTime,
		End:             monEndTime,
		Interval:        interval,
		Threshold:       ProxyCPUThreshold,
	}
	leastInsCnt, err := opmonitor.GetLeastInsCntByMonquery(ctx, app, monParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get least ins cnt error", logit.Error("error", err))
		return fmt.Errorf("get least ins cnt error, err: %w", err)
	}
	parmas := taskIface.Parameters{
		TargetOpProxyInfos: []*taskIface.TargetOpProxyInfo{},
	}
	for az, cnt := range azProxyCnt {
		if cnt < leastInsCnt {
			parmas.TargetOpProxyInfos = append(parmas.TargetOpProxyInfos, &taskIface.TargetOpProxyInfo{
				Zone:     az,
				Count:    leastInsCnt - nonOpAzProxyCnt[az],
				SubnetID: azSubnet[az],
			})
		}
	}
	if len(parmas.TargetOpProxyInfos) == 0 {
		resource.LoggerTask.Notice(ctx, "no need to add op proxy",
			logit.String("appId", teu.Entity), logit.Int("leastInsCnt", leastInsCnt))
		return nil
	}
	if err := resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{{
		WorkFlow:   "scs-modify-proxies-cluster",
		Schedule:   time.Now(),
		Mutex:      fmt.Sprintf("n_%s_cross_az_nearest_sub", teu.Entity),
		Entity:     teu.Entity,
		Parameters: base_utils.Format(parmas),
	}}); err != nil {
		resource.LoggerTask.Warning(ctx, "create sub tasks for add op proxy fail",
			logit.Error("error", err), logit.String("appId", teu.Entity))
	}
	return nil
}

func getHistoryTimeForMonquery(endTime time.Time, intervalSec int) (string, string, string) {
	monStartTime := endTime.Add(-time.Duration(intervalSec) * time.Second).Format("20060102150405")
	monEndTime := endTime.Format("20060102150405")
	return monStartTime, monEndTime, strconv.Itoa(intervalSec)
}
