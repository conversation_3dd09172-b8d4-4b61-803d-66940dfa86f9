package crossAzNearest

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessEnableCrossAzNearestCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cacheCluster model by app id fail",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	var confRecords []*csmaster_model_interface.ConfRecordList
	if err := resource.CsmasterModel.GetAllByCond(
		ctx, &confRecords, "cluster_id = ? and conf_name = ?", cacheCluster.Id, CrossAzNearestConfName); err != nil {
		resource.LoggerTask.Warning(ctx, "get cross az nearest config fail",
			logit.Error("err", err), logit.String("appID", teu.Entity))
		return fmt.Errorf("get cross az nearest config fail, appID: %s, err: %w", teu.Entity, err)
	}
	if len(confRecords) > 1 {
		resource.LoggerTask.Warning(ctx, "cross az nearest config has multiple records, this is unexpected",
			logit.Int("count", len(confRecords)), logit.String("appID", teu.Entity))
		return fmt.Errorf("cross az nearest config has multiple records, appID: %s", teu.Entity)
	}
	if len(confRecords) == 0 {
		resource.LoggerTask.Notice(ctx, "cross az nearest config not found, creating new one",
			logit.String("appID", cacheCluster.ClusterShowId))
		confRecords = append(confRecords, &csmaster_model_interface.ConfRecordList{
			ClusterID:  int(cacheCluster.Id),
			ConfName:   CrossAzNearestConfName,
			ConfModule: CrossAzNearestConfModule,
			Value:      "yes",
			Effected:   1,
		})
		if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, confRecords); err != nil {
			resource.LoggerTask.Warning(ctx, "create cross az nearest config fail",
				logit.Error("err", err), logit.String("appID", teu.Entity))
			return fmt.Errorf("create cross az nearest config fail, appID: %s, err: %w", teu.Entity, err)
		}
	} else {
		resource.LoggerTask.Notice(ctx, "cross az nearest config already exists, updating value")
		confRecords[0].Value = "yes" // Enabling cross az nearest
		if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, confRecords); err != nil {
			resource.LoggerTask.Warning(ctx, "update cross az nearest config fail",
				logit.Error("err", err), logit.String("appID", teu.Entity))
			return fmt.Errorf("update cross az nearest config fail, appID: %s, err: %w", teu.Entity, err)
		}
	}
	cacheCluster.Status = 5
	if err := resource.CsmasterOpAgent.UpdateClusterModel(ctx, cacheCluster); err != nil {
		resource.LoggerTask.Warning(ctx, "update cacheCluster model status fail",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	return nil
}

func ProcessDisableCrossAzNearestCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cacheCluster model by app id fail",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	var confRecords []*csmaster_model_interface.ConfRecordList
	if err := resource.CsmasterModel.GetAllByCond(
		ctx, &confRecords, "cluster_id = ? and conf_name = ?", cacheCluster.Id, CrossAzNearestConfName); err != nil {
		resource.LoggerTask.Warning(ctx, "get cross az nearest config fail",
			logit.Error("err", err), logit.String("appID", teu.Entity))
		return fmt.Errorf("get cross az nearest config fail, appID: %s, err: %w", teu.Entity, err)
	}
	if len(confRecords) == 0 {
		resource.LoggerTask.Warning(ctx, "cross az nearest config not found",
			logit.String("appID", cacheCluster.ClusterShowId))
		return fmt.Errorf("cross az nearest config not found, appID: %s", teu.Entity)
	}
	confRecords[0].Value = "no" // Disabling cross az nearest
	if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, confRecords); err != nil {
		resource.LoggerTask.Warning(ctx, "update cross az nearest config fail",
			logit.Error("err", err), logit.String("appID", teu.Entity))
		return fmt.Errorf("update cross az nearest config fail, appID: %s, err: %w", teu.Entity, err)
	}
	cacheCluster.Status = 5
	if err := resource.CsmasterOpAgent.UpdateClusterModel(ctx, cacheCluster); err != nil {
		resource.LoggerTask.Warning(ctx, "update cacheCluster model status fail",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	return nil
}
