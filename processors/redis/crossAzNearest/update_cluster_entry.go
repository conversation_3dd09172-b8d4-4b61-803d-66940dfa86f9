package crossAzNearest

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessUpdateProxyClusterEntry(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("err", err))
		return err
	}
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model fail", logit.Error("err", err))
		return err
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if err := setProxyClusterEntry(ctx, proxy, clusterModel.ClientAuth, getClusterEntry(clusterModel, app, proxy)); err != nil {
				resource.LoggerTask.Warning(ctx, "set proxy cluster_entry fail",
					logit.String("proxyId", proxy.ProxyId), logit.Error("err", err))
				return fmt.Errorf("set proxy cluster_entry fail, proxyId: %s, err: %w", proxy.ProxyId, err)
			}
		}
	}
	return nil
}

func setProxyClusterEntry(ctx context.Context, proxy *x1model.Proxy, password string, clusterEntry string) error {
	c := single_redis.NewClient(proxy.FloatingIP, proxy.Port,
		single_redis.WithPassword(password),
		single_redis.WithRetry(0),
	)
	defer c.Close()

	r, err := c.Do(ctx, "proxyconfig", "set", "cluster_entry", clusterEntry).Result()
	rstr := cast.ToString(r)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set proxy fail", logit.Error("err", err), logit.String("result", rstr))
		return err
	}
	return nil
}

func getClusterEntry(clusterModel *csmaster_model_interface.CacheCluster, app *x1model.Application, proxy *x1model.Proxy) string {
	azResult := fmt.Sprintf("%s:%d", getClusterEntryIP(clusterModel), clusterModel.BlbListenerPort.Int32)
	for _, b := range app.BLBs {
		if b.Status != x1model.BLBStatusAvailable {
			continue
		}
		if b.Type != x1model.BLBTypeApp {
			continue
		}
		if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
			return fmt.Sprintf("%s:%d", getClusterEntryIP(clusterModel), clusterModel.BlbListenerPort.Int32)
		}
		if b.AzoneForCrossAzNearest == proxy.Azone {
			if b.EndpointIp != "" {
				azResult = fmt.Sprintf("%s:%d", b.EndpointIp, clusterModel.BlbListenerPort.Int32)
			} else {
				azResult = fmt.Sprintf("%s:%d", b.Ovip, clusterModel.BlbListenerPort.Int32)
			}
		}
	}
	return azResult
}

func getClusterEntryIP(clusterModel *csmaster_model_interface.CacheCluster) string {
	ip := clusterModel.ElbPnetip.String
	if len(clusterModel.EndpointIp) != 0 {
		ip = clusterModel.EndpointIp
	}
	return ip
}
