package crossAzNearest

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessBuildMetaForEnableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal replicas fail",
			logit.Error("unmarshalError", err), logit.String("appId", teu.Entity))
		return err
	}
	if len(replicas) < 2 {
		resource.LoggerTask.Warning(ctx, "replicas less than 2, not enable cross az nearest",
			logit.String("appId", teu.Entity), logit.Int("replicaCount", len(replicas)))
		return fmt.Errorf("replicas less than 2")
	}
	var masterAz string
	for _, replica := range replicas {
		if replica.Role == x1model.RoleTypeMaster {
			masterAz = replica.Zone
			break
		}
	}
	if masterAz == "" {
		resource.LoggerTask.Warning(ctx, "no master replica found, not enable cross az nearest",
			logit.String("appId", teu.Entity))
		return fmt.Errorf("no master replica found")
	}
	zoneFunc, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get zone map fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	masterAzone, ok := zoneFunc(masterAz, true)
	if !ok {
		resource.LoggerTask.Warning(ctx, "get master az info fail",
			logit.String("appId", teu.Entity), logit.String("masterAz", masterAz))
		return fmt.Errorf("get master az info fail")
	}
	for _, blb := range app.BLBs {
		if blb.Type != x1model.BLBTypeApp {
			continue
		}
		blb.AzoneForCrossAzNearest = masterAzone
	}
	for _, replica := range replicas {
		if replica.Zone == masterAz {
			continue
		}
		azone, ok := zoneFunc(replica.Zone, true)
		if !ok {
			resource.LoggerTask.Warning(ctx, "get replica az info fail",
				logit.String("appId", teu.Entity), logit.String("replicaZone", replica.Zone))
			return fmt.Errorf("get replica az info fail")
		}
		app.BLBs = append(app.BLBs, &x1model.BLB{
			AppId:                  app.AppId,
			Name:                   app.AppId,
			VpcId:                  app.VpcId,
			SubnetId:               replica.SubnetIDs[0],
			Type:                   x1model.BLBTypeApp,
			IpType:                 x1model.Ipv4,
			AzoneForCrossAzNearest: azone,
		})
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "save application fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	return nil
}

func ProcessBuildMetaForDisableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal replicas fail",
			logit.Error("unmarshalError", err), logit.String("appId", teu.Entity))
		return err
	}
	if len(replicas) < 2 {
		resource.LoggerTask.Warning(ctx, "replicas less than 2, not disable cross az nearest",
			logit.String("appId", teu.Entity), logit.Int("replicaCount", len(replicas)))
		return fmt.Errorf("replicas less than 2")
	}
	var masterAz string
	for _, replica := range replicas {
		if replica.Role == x1model.RoleTypeMaster {
			masterAz = replica.Zone
			break
		}
	}
	if masterAz == "" {
		resource.LoggerTask.Warning(ctx, "no master replica found, not disable cross az nearest",
			logit.String("appId", teu.Entity))
		return fmt.Errorf("no master replica found")
	}
	zoneFunc, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get zone map fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	masterAzone, ok := zoneFunc(masterAz, true)
	if !ok {
		resource.LoggerTask.Warning(ctx, "get master az info fail",
			logit.String("appId", teu.Entity), logit.String("masterAz", masterAz))
		return fmt.Errorf("get master az info fail")
	}
	foundMasterBlb := false
	for _, b := range app.BLBs {
		if b.AzoneForCrossAzNearest == masterAzone {
			b.AzoneForCrossAzNearest = x1model.GlobalForCrossAzNearest
			foundMasterBlb = true
			break
		}
	}
	if !foundMasterBlb {
		resource.LoggerTask.Warning(ctx, "no blb found for master az, not disable cross az nearest",
			logit.String("appId", teu.Entity), logit.String("masterAz", masterAz))
		return fmt.Errorf("no blb found for master az")
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "save application fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	return nil
}
