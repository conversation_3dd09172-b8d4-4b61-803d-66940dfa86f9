package crossAzNearest

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessUpdateMetaForEnableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application error",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver client error",
			logit.Error("error", err), logit.String("metaServerId", metaCluster.MetaClusterID))
		return fmt.Errorf("get metaserver client error: %w", err)
	}
	var redisParams []*metaserver.MriUpdateParam
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusInUse && node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if node.LogicZone == "" {
				resource.LoggerTask.Warning(ctx, "node zone is empty",
					logit.String("appId", app.AppId), logit.String("nodeId", node.NodeId))
				return fmt.Errorf("node zone is empty for node %s", node.NodeId)
			}
			redisParams = append(redisParams, &metaserver.MriUpdateParam{
				RedisID: node.NodeShortID,
				Field:   "az",
				Value:   node.LogicZone,
			})
		}
	}
	if err := metaCli.MriUpdatePipeline(ctx, redisParams); err != nil {
		resource.LoggerTask.Warning(ctx, "update metaserver az error",
			logit.Error("error", err), logit.String("appId", app.AppId))
		return fmt.Errorf("update metaserver az error: %w", err)
	}
	resource.LoggerTask.Trace(ctx, "update metaserver az for redis success",
		logit.String("appId", app.AppId), logit.String("params", base_utils.Format(redisParams)))
	var proxyParams []*metaserver.MpiUpdateParam
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse && proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if proxy.LogicZone == "" {
				resource.LoggerTask.Warning(ctx, "proxy zone is empty",
					logit.String("appId", app.AppId), logit.String("proxyId", proxy.ProxyId))
				return fmt.Errorf("proxy zone is empty for proxy %s", proxy.ProxyId)
			}
			proxyParams = append(proxyParams, &metaserver.MpiUpdateParam{
				ProxyInstID: proxy.ProxyShortID,
				Field:       "az",
				Value:       proxy.LogicZone,
			})
		}
	}
	if err := metaCli.MpiUpdatePipeline(ctx, proxyParams); err != nil {
		resource.LoggerTask.Warning(ctx, "update metaserver az error",
			logit.Error("error", err), logit.String("appId", app.AppId))
		return fmt.Errorf("update metaserver az error: %w", err)
	}
	resource.LoggerTask.Trace(ctx, "update metaserver az for proxy success",
		logit.String("appId", app.AppId), logit.String("params", base_utils.Format(proxyParams)))
	return nil
}

func ProcessUpdateMetaForDisableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application error",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver client error",
			logit.Error("error", err), logit.String("metaServerId", metaCluster.MetaClusterID))
		return fmt.Errorf("get metaserver client error: %w", err)
	}
	var proxyParams []*metaserver.MpiUpdateParam
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse && proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			proxyParams = append(proxyParams, &metaserver.MpiUpdateParam{
				ProxyInstID: proxy.ProxyShortID,
				Field:       "az",
				Value:       "",
			})
		}
	}
	if err := metaCli.MpiUpdatePipeline(ctx, proxyParams); err != nil {
		resource.LoggerTask.Warning(ctx, "update metaserver az error",
			logit.Error("error", err), logit.String("appId", app.AppId))
		return fmt.Errorf("update metaserver az error: %w", err)
	}
	resource.LoggerTask.Trace(ctx, "update metaserver az for proxy success",
		logit.String("appId", app.AppId), logit.String("params", base_utils.Format(proxyParams)))
	var redisParams []*metaserver.MriUpdateParam
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusInUse && node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			redisParams = append(redisParams, &metaserver.MriUpdateParam{
				RedisID: node.NodeShortID,
				Field:   "az",
				Value:   "",
			})
		}
	}
	if err := metaCli.MriUpdatePipeline(ctx, redisParams); err != nil {
		resource.LoggerTask.Warning(ctx, "update metaserver az error",
			logit.Error("error", err), logit.String("appId", app.AppId))
		return fmt.Errorf("update metaserver az error: %w", err)
	}
	resource.LoggerTask.Trace(ctx, "update metaserver az for redis success",
		logit.String("appId", app.AppId), logit.String("params", base_utils.Format(redisParams)))
	return nil
}
