package crossAzNearest

import (
	"context"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
)

func ProcessRecoverInterfacesForEnableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model fail",
			logit.Error("dbError", err), logit.String("appId", teu.Entity))
		return err
	}
	curItfCnt := 0
	proxyCntPerAz := 0
	for _, itf := range app.Interfaces {
		if strings.Contains(itf.InterfaceId, "-itfop-") {
			continue
		}
		curItfCnt++
		proxyCntPerAz = len(itf.Proxys)
	}
	expectProxyCnt, err := buildmeta.GetTotalProxyCount(ctx, &buildmeta.GetTotalProxyCountParams{
		App:        app,
		Engine:     app.Clusters[0].Engine,
		ShardCount: len(app.Clusters),
		NodeType:   clusterModel.NodeType,
		UserID:     app.UserId,
		VpcID:      app.VpcId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get total proxy count fail",
			logit.Error("error", err), logit.String("appId", teu.Entity))
		return err
	}
	if expectProxyCnt > proxyCntPerAz*curItfCnt {
		resource.LoggerTask.Warning(ctx, "expect proxy count is greater than current proxy count",
			logit.Int("expectProxyCnt", expectProxyCnt), logit.Int("curItfCnt", curItfCnt),
			logit.Int("proxyCntPerAz", proxyCntPerAz), logit.String("appId", teu.Entity))
		if err := resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{
			{
				WorkFlow: "scs-modify-interfaces-cluster",
				Schedule: time.Now(),
				Mutex:    fmt.Sprintf("n_%s_cross_az_nearest_sub", teu.Entity),
				Entity:   teu.Entity,
				Parameters: base_utils.Format(map[string]interface{}{
					"app_id": teu.Entity,
					"target_interface_count": func() int {
						if expectProxyCnt%proxyCntPerAz == 0 {
							return expectProxyCnt / proxyCntPerAz
						}
						return expectProxyCnt/proxyCntPerAz + 1
					}(),
				}),
			},
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "create sub tasks for enable cross az nearest fail",
				logit.Error("error", err), logit.String("appId", teu.Entity))
			return err
		}
	}
	return nil
}

func ProcessWaitSubtasksFinish(ctx context.Context, teu *workflow.TaskExecUnit) error {
	for {
		select {
		case <-ctx.Done():
			resource.LoggerTask.Warning(ctx, "context done, stop checking for enable cross az nearest",
				logit.String("appId", teu.Entity), logit.Error("error", ctx.Err()))
			return ctx.Err()
		default:
			status, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "check sub tasks status fail",
					logit.Error("error", err), logit.String("appId", teu.Entity))
				continue
			}
			if status == iface.TaskStatusSuccess {
				resource.LoggerTask.Notice(ctx, "sub tasks for enable cross az nearest completed successfully",
					logit.String("appId", teu.Entity))
				return nil
			}
		}
	}
}
