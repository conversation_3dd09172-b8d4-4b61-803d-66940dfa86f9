package migratebcmcycle

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bcm"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/remotessh"
)

const (
	NewCycle = 5
	OldCycle = 60
)

func isNeeDManualError(ctx context.Context, taskID string, originErr error) error {
	taskDetail, err := resource.TaskOperator.GetTaskDetail(ctx, taskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get task detail failed", logit.Error("error", err))
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	stepName := taskDetail.Step
	stepNameSplit := strings.Split(stepName, "__")
	if len(stepNameSplit) != 2 {
		err := errors.New("task step name error")
		resource.LoggerTask.Warning(ctx, "step name error", logit.Error("error", err))
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	if stepNameSplit[1] == "2" {
		return cerrs.ErrorTaskManual.Wrap(originErr)
	}
	return originErr
}

func migrateBcmCycleUpdateApp(ctx context.Context, appID string, cycle int) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}
	app.BcmCycle = cycle
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app error", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "migrate bcm cycle success")
	return nil
}

// ProcessMigrateBcmCycleUpdateApp
func ProcessMigrateBcmCycleUpdateApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return migrateBcmCycleUpdateApp(ctx, teu.Entity, NewCycle)
}

// ProcessRollbackMigrateBcmCycleUpdateApp
func ProcessRollbackMigrateBcmCycleUpdateApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return migrateBcmCycleUpdateApp(ctx, teu.Entity, OldCycle)
}

func migrateBcmCycleChangeConf(ctx context.Context, appID string, originCycle, targetCycle int) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	g := gtask.Group{Concurrent: 20}

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			g.Go(func() error {
				_, err := remotessh.ChangeBcmPushCycleBySSH(ctx, targetCycle, originCycle, node.RootPassword,
					app.ResourceType, node.ResourceId, node.FloatingIP)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "change cycle failed", logit.Error("error", err),
						logit.String("node", base_utils.Format(node)))
				}
				return err
			})
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy := proxy
			g.Go(func() error {
				_, err := remotessh.ChangeBcmPushCycleBySSH(ctx, targetCycle, originCycle, proxy.RootPassword,
					app.ResourceType, proxy.ResourceId, proxy.FloatingIP)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "change cycle failed", logit.Error("error", err),
						logit.String("node", base_utils.Format(proxy)))
				}
				return err
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "change cycle failed", logit.Error("error", err))
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	return nil
}

// ProcessMigrateBcmCycleChangeConf
func ProcessMigrateBcmCycleChangeConf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return migrateBcmCycleChangeConf(ctx, teu.Entity, OldCycle, NewCycle)
}

// ProcessRollbackMigrateBcmCycleChangeConf
func ProcessRollbackMigrateBcmCycleChangeConf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return migrateBcmCycleChangeConf(ctx, teu.Entity, NewCycle, OldCycle)
}

// ProcessMigrateBcmCycleFastCheck
func ProcessMigrateBcmCycleFastCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	err = bcmCycleFastCheck(ctx, app, NewCycle)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fast check failed", logit.Error("error", err))
		return isNeeDManualError(ctx, teu.TaskID, err)
	}
	resource.LoggerTask.Notice(ctx, "fast check success")
	return nil
}

// ProcessRollbackMigrateBcmCycleFastCheck
func ProcessRollbackMigrateBcmCycleFastCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	err = bcmCycleFastCheck(ctx, app, OldCycle)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fast check failed", logit.Error("error", err))
		return isNeeDManualError(ctx, teu.TaskID, err)
	}
	resource.LoggerTask.Notice(ctx, "fast check success")
	return nil
}

func bcmCycleFastCheck(ctx context.Context, app *x1model.Application, cycle int) error {

	g := gtask.Group{Concurrent: 50}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			g.Go(func() error {
				cycleStr, err := remotessh.GetBcmPushCycleBySSH(ctx, node.RootPassword,
					app.ResourceType, node.ResourceId, node.FloatingIP)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "change cycle failed", logit.Error("error", err),
						logit.String("node", base_utils.Format(node)))
					return err
				}
				resource.LoggerTask.Notice(ctx, "get cycle success", logit.String("cycle", cycleStr),
					logit.String("node_id", node.NodeId))
				cycleInt, err := strconv.Atoi(cycleStr)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "cycle["+cycleStr+"]not not int", logit.Error("error", err),
						logit.String("node", base_utils.Format(node)))
					return errors.New("cycle[" + cycleStr + "]not not int")
				}
				if cycleInt != cycle {
					resource.LoggerTask.Warning(ctx, "cycle["+cycleStr+"]not match new cycle", logit.Error("error", err),
						logit.String("node", base_utils.Format(node)))
					return errors.New("cycle[" + cycleStr + "]not match new cycle")
				}
				return nil
			})
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy := proxy
			g.Go(func() error {
				cycleStr, err := remotessh.GetBcmPushCycleBySSH(ctx, proxy.RootPassword,
					app.ResourceType, proxy.ResourceId, proxy.FloatingIP)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "change cycle failed", logit.Error("error", err),
						logit.String("node", base_utils.Format(proxy)))
					return err
				}
				resource.LoggerTask.Notice(ctx, "get cycle success", logit.String("cycle", cycleStr),
					logit.String("node_id", proxy.ProxyId))
				cycleInt, err := strconv.Atoi(cycleStr)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "cycle["+cycleStr+"]not not int", logit.Error("error", err),
						logit.String("node", base_utils.Format(proxy)))
					return errors.New("cycle[" + cycleStr + "]not not int")
				}
				if cycleInt != cycle {
					resource.LoggerTask.Warning(ctx, "cycle["+cycleStr+"]not match new cycle", logit.Error("error", err),
						logit.String("node", base_utils.Format(proxy)))
					return errors.New("cycle[" + cycleStr + "]not match new cycle")
				}
				return nil
			})
		}
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check cycle failed", logit.Error("error", err))
		return err
	}
	return nil
}

func migrateBcmCyclePrecheck(ctx context.Context, app *x1model.Application, cycle int) error {
	if app.UseNewAgent != "yes" {
		resource.LoggerTask.Error(ctx, "app is not new agent", logit.String("app", app.AppId))
		return errors.New("app is not new agent")
	}
	if app.BcmCycle == cycle {
		resource.LoggerTask.Error(ctx, "app is already new cycle", logit.String("app", app.AppId))
		return errors.New("app is already new cycle")
	}
	return nil
}

// ProcessMigrateBcmCyclePreCheck is the pre-check of migrate bcm cycle
func ProcessMigrateBcmCyclePreCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	return migrateBcmCyclePrecheck(ctx, app, NewCycle)
}

// ProcessRollbackMigrateBcmCyclePreCheck is the pre-check of rollback migrate bcm cycle
func ProcessRollbackMigrateBcmCyclePreCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	return migrateBcmCyclePrecheck(ctx, app, OldCycle)
}

func bcmCycleSlowCheck(ctx context.Context, app *x1model.Application, sampleCountMin, sampleCountMax int) error {

	g := gtask.Group{Concurrent: 50}
	op := bcm.Instance()
	startTime := time.Now().UTC().Add(-3 * time.Minute)
	endTime := time.Now().UTC()

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			g.Go(func() error {
				sampleCounts, err := op.QuerySampleCount(ctx, &bcm.QuerySampleCountParams{
					UserID:         app.UserId,
					AppID:          app.AppId,
					NodeFixID:      node.NodeFixID,
					StartTime:      startTime,
					EndTime:        endTime,
					PeriodInSecond: 60,
				})
				if err != nil {
					resource.LoggerTask.Warning(ctx, "query sample count failed", logit.Error("error", err),
						logit.String("node_id", node.NodeFixID))
					return err
				}
				if len(sampleCounts) > 0 && sampleCounts[len(sampleCounts)-1].SampleCount > sampleCountMin &&
					sampleCounts[len(sampleCounts)-1].SampleCount < sampleCountMax {
					return nil
				}
				resource.LoggerTask.Warning(ctx, "node not have enough sample", logit.String("node_id", node.NodeFixID))
				return errors.New("node not have enough sample")
			})
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxy := proxy
			g.Go(func() error {
				sampleCounts, err := op.QuerySampleCount(ctx, &bcm.QuerySampleCountParams{
					UserID:         app.UserId,
					AppID:          app.AppId,
					NodeFixID:      proxy.NodeFixID,
					StartTime:      startTime,
					EndTime:        endTime,
					PeriodInSecond: 60,
				})
				if err != nil {
					resource.LoggerTask.Warning(ctx, "query sample count failed", logit.Error("error", err),
						logit.String("node_id", proxy.NodeFixID))
					return err
				}
				if len(sampleCounts) > 0 && sampleCounts[len(sampleCounts)-1].SampleCount > sampleCountMin &&
					sampleCounts[len(sampleCounts)-1].SampleCount < sampleCountMax {
					return nil
				}
				resource.LoggerTask.Warning(ctx, "node not have enough sample", logit.String("node_id", proxy.NodeFixID))
				return errors.New("node not have enough sample")
			})
		}
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "slow check cycle failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessMigrateBcmCycleSlowCheck is the slow check of migrate bcm cycle
func ProcessMigrateBcmCycleSlowCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {

	time.Sleep(2 * time.Minute)
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	err = bcmCycleSlowCheck(ctx, app, 10, 15)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "slow check failed", logit.Error("error", err))
		return isNeeDManualError(ctx, teu.TaskID, err)
	}
	resource.LoggerTask.Notice(ctx, "slow check success")
	return nil
}

// ProcessRollbackMigrateBcmCycleSlowCheck
func ProcessRollbackMigrateBcmCycleSlowCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {

	time.Sleep(2 * time.Minute)
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("err", err))
		return err
	}
	err = bcmCycleSlowCheck(ctx, app, 0, 2)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "slow check failed", logit.Error("error", err))
		return isNeeDManualError(ctx, teu.TaskID, err)
	}
	resource.LoggerTask.Notice(ctx, "slow check success")
	return nil
}
