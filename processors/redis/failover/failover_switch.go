/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/06 <EMAIL> Exp
 *
 **************************************************************************/

package failover

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-api/apisdk"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessEnableFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	appID := teu.Entity
	apiSdk := apisdk.NewDefaultCsmasterSdk()
	err := apiSdk.SetAppFailoverSwitch(ctx, &apisdk.SetAppFailoverSwitchReq{
		AppID:  appID,
		Enable: true,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set failover enable failed")
		return errors.Errorf("set failover enable failed")
	}

	return nil
}

func ProcessDisableFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	appID := teu.Entity
	apiSdk := apisdk.NewDefaultCsmasterSdk()
	err := apiSdk.SetAppFailoverSwitch(ctx, &apisdk.SetAppFailoverSwitchReq{
		AppID:  appID,
		Enable: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set failover disable failed")
		return errors.Errorf("set failover disable failed")
	}

	return nil
}
