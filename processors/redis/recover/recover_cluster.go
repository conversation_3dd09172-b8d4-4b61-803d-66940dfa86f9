/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"strings"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	dbrsRepo "icode.baidu.com/baidu/scs/x1-base/component/repo"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	recoverTimeoutSec = 500
)

const (
	backupRepo        = "backup_bos"
	defaultExpireTime = 7200
)

type ParamsRecoverInOriginalCluster struct {
	Meta           *xagent.Meta  `json:"meta"`
	DownloadConfig *DownloadConf `json:"download_config"`
	RedisConfList  []*RedisConf  `json:"redis_conf_list"`
}

type RedisConf struct {
	ConfName  string `json:"conf_name"`
	ConfValue string `json:"conf_value"`
}

type DownloadConf struct {
	AppBackupId    string `json:"app_backup_id"`
	RdbDownloadUrl string `json:"rdb_download_url"`
	RdbSize        string `json:"rdb_size"`
}

func processRecoverClusterTask(ctx context.Context, cluster *x1model.Cluster, appBackupId string, isClone bool) error {
	// 获取备份信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("clusterShowId", cluster.AppId),
			logit.Error("queryError", err))
		return err
	}

	clusterId := cacheCluster.Id
	shardId := cluster.ClusterShortID
	var backupModels []*csmaster_model_interface.BackupRecord
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "cluster_id = ?", clusterId); err != nil {
		return err
	}

	targetBucket := ""
	targetObjectKey := ""
	targetRdbSize := cast.ToInt64(0)
	if isClone {
		for _, item := range backupModels {
			if item.ShardName == cast.ToString(shardId) {
				targetBucket = item.Bucket
				targetObjectKey = item.ObjectKey
				targetRdbSize = item.ObjectSize
			}
		}
	} else {
		for _, item := range backupModels {
			if item.ShardName == cast.ToString(shardId) && item.BatchID == appBackupId {
				targetBucket = item.Bucket
				targetObjectKey = item.ObjectKey
				targetRdbSize = item.ObjectSize
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "get target backup info",
		logit.String("clusterId:", base_utils.Format(clusterId)),
		logit.String("shardId:", base_utils.Format(shardId)),
		logit.String("targetBucket:", base_utils.Format(targetBucket)),
		logit.String("targetObjectKey:", base_utils.Format(targetObjectKey)))

	if targetBucket == "" || targetObjectKey == "" {
		errMsg := "backup info is not correct"
		resource.LoggerTask.Error(ctx, errMsg,
			logit.String("clusterId:", base_utils.Format(clusterId)),
			logit.String("shardId:", base_utils.Format(shardId)),
			logit.String("targetBucket:", base_utils.Format(targetBucket)),
			logit.String("targetObjectKey:", base_utils.Format(targetObjectKey)))
		return cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	// 生成bos外链
	repoConf := dbrsRepo.GetRepo(backupRepo).Conf

	c := dbrsRepo.NewBosClient(&dbrsRepo.Conf{
		BaseRepoConf: dbrsRepo.BaseRepoConf{
			Name:     "bos_client",
			Type:     "bos",
			Endpoint: repoConf.Endpoint,
		},
		BosRepoConf: dbrsRepo.BosRepoConf{
			BosAk:           repoConf.BosAk,
			BosSk:           repoConf.BosSk,
			BosBucket:       targetBucket,
			BosAuthDuration: 1800,
		},
	})
	backupUrl := c.GetFileUrlWithExpireTime(targetObjectKey, defaultExpireTime)
	resource.LoggerTask.Notice(ctx, "get backup rdb url",
		logit.String("targetBucket:", base_utils.Format(targetBucket)),
		logit.String("targetObjectKey:", base_utils.Format(targetObjectKey)),
		logit.String("backupUrl:", base_utils.Format(backupUrl)))

	// 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	app, err := x1model.ApplicationGetByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == shardId {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// 获取节点密码
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	password := ""
	if defaultAcl != nil {
		password = defaultAcl.Password
	}

	// 构造恢复参数
	paramsRecoverInOriginalCluster := ParamsRecoverInOriginalCluster{
		DownloadConfig: &DownloadConf{
			AppBackupId:    appBackupId,
			RdbDownloadUrl: strings.Replace(backupUrl, "https://", "http://", 1), // bos外链在内网机器上执行需要转成普通http协议，否则会报错
			RdbSize:        cast.ToString(targetRdbSize),
		},
		Meta: &xagent.Meta{
			Engine:        masterNode.Engine,
			EngineVersion: masterNode.EngineVersion,
			Basedir:       masterNode.Basedir,
			Port:          int32(masterNode.Port),
			Password:      password,
		},
	}

	// 获取TDE配置
	redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, cluster.AppId)
	if err != nil {
		errorMessage := "get cluster tde info failed"
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(cluster.AppId)),
			logit.Error("error", err))
		return err
	}
	var redisConfList []*RedisConf
	if len(redisTdeInfos) > 0 {
		tdeKey := redisTdeInfos[0].TdeKey
		redisConfList = append(redisConfList, &RedisConf{
			ConfName:  "tde-key",
			ConfValue: tdeKey,
		})
	}
	paramsRecoverInOriginalCluster.RedisConfList = redisConfList

	recoverReq := xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: masterNode.FloatingIP,
			Port: cast.ToInt32(masterNode.XagentPort)},
		Action:     "recover",
		Params:     paramsRecoverInOriginalCluster,
		TimeoutSec: recoverTimeoutSec,
	}

	resource.LoggerTask.Notice(ctx, "call xagent request", logit.String("recoverReq:", base_utils.Format(recoverReq)))

	asyncTask := xagent.Instance().DoAsync(ctx, &recoverReq)
	rsp, err := asyncTask.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "call x-agent recover fail", logit.Error("err", err), logit.String("xagent_rsp:", base_utils.Format(rsp)),
			logit.String("nodeId", masterNode.NodeId))
		return err
	}
	return nil
}
