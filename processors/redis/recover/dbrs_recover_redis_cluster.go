/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/12 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"
	"icode.baidu.com/baidu/scs/x1-base/utils/vep"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type ParamsRecoverRedis struct {
	Meta          *xagent.Meta `json:"meta"`
	DbrsConfig    *DbrsConf    `json:"dbrs_config"`
	RedisConfList []*RedisConf `json:"redis_conf_list"`
}

type DbrsConf struct {
	DataBackupID           string `json:"data_backup_id"`
	RestorePointInDatetime string `json:"restore_point_in_datetime"`
}

/*
原地恢复时，clusterShortID 为 sourceClusterShortID
克隆恢复时，InstanceID 为 sourceClusterShortID(克隆 build_meta 时将源 clusterShortID 存储到了 InstanceID 字段)
*/
func getSourceBackupInfo(ctx context.Context, appShortID int64,
	clusterShortID int, appBackupID string, isClone bool) (sourceClusterShortID string, sourceDataBackupID string, err error) {
	var backupModels []*csmaster_model_interface.BackupRecord
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "cluster_id = ?", appShortID); err != nil {
		return "", "", err
	}

	objectKey := ""
	if isClone {
		for _, item := range backupModels {
			if item.ShardName == cast.ToString(clusterShortID) {
				// 克隆集群时，将原分片 ID 存储在了 InstanceID 中
				sourceClusterShortID = cast.ToString(item.InstanceID)
				objectKey = item.ObjectKey
			}
		}
	} else {
		for _, item := range backupModels {
			if item.ShardName == cast.ToString(clusterShortID) && item.BatchID == appBackupID {
				// 原地恢复时，sourceClusterShortID = clusterShortID
				sourceClusterShortID = cast.ToString(clusterShortID)
				objectKey = item.ObjectKey
			}
		}
	}

	// eg: redis/scs-bj-smwannbbgwbv/62534/20240802233426/1722612866539877901_dump.rdb
	if !strings.HasPrefix(objectKey, "redis/") {
		errMsg := "backup info is not correct"
		resource.LoggerTask.Error(ctx, errMsg,
			logit.Int64("appShortID:", appShortID),
			logit.Int("clusterShortID:", clusterShortID),
			logit.String("sourceClusterShortID:", base_utils.Format(sourceClusterShortID)),
			logit.String("objectKey:", base_utils.Format(objectKey)))
		return "", "", cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	fileName := strings.Split(objectKey, "/")[len(strings.Split(objectKey, "/"))-1]
	sourceDataBackupID = strings.Split(fileName, "_")[0]

	resource.LoggerTask.Notice(ctx, "get target backup info",
		logit.String("appShortID:", base_utils.Format(appShortID)),
		logit.String("clusterShortID:", base_utils.Format(clusterShortID)),
		logit.String("sourceClusterShortID:", base_utils.Format(sourceClusterShortID)),
		logit.String("sourceDataBackupID:", base_utils.Format(sourceDataBackupID)))
	return sourceClusterShortID, sourceDataBackupID, nil
}

func loadDbrsRestoreServiceID(ctx context.Context, taskID string, nodeID string) (string, error) {
	key := "dbrs_restore_:" + taskID + ":" + nodeID
	restoreServiceID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return restoreServiceID, nil
}

func saveDbrsRestoreServiceID(ctx context.Context, taskID string, nodeID string, restoreServiceID string) error {
	key := "dbrs_restore_:" + taskID + ":" + nodeID
	if err := resource.RedisClient.SetNX(ctx, key, restoreServiceID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteDbrsRestoreServiceID(ctx context.Context, taskID string, nodeID string) error {
	key := "dbrs_restore_:" + taskID + ":" + nodeID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processDbrsRecoverRedisClusterTask(ctx context.Context, taskID string,
	sourceAppID string, cluster *x1model.Cluster, appBackupID string, isClone bool) error {
	// (1) 获取备份信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("clusterShowId", cluster.AppId),
			logit.Error("queryError", err))
		return err
	}

	appID := cacheCluster.ClusterShowId
	appShortID := cacheCluster.Id
	clusterShortID := cluster.ClusterShortID
	sourceClusterShortID, sourceDataBackupID, err := getSourceBackupInfo(ctx, appShortID, clusterShortID, appBackupID, isClone)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get sourceBackupInfo fail", logit.Error("Error", err))
		return err
	}

	// (2) 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	app, err := x1model.ApplicationGetByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == clusterShortID {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// (3) 尝试从 Redis 中获取 restoreServiceID
	restoreServiceID, err := loadDbrsRestoreServiceID(ctx, taskID, masterNode.NodeId)
	if err == nil && len(restoreServiceID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have create restoreServiceID: %s", restoreServiceID))
	}

	// (4) 请求 DBRS 创建恢复任务
	dbrsComponent := dbrs.DbrsResourceOp()
	if restoreServiceID == "" {
		createRestoreParams := dbrs.CreateRestoreParams{
			DataType:           "Redis",
			SourceAppID:        sourceAppID,
			SourceClusterID:    cast.ToString(sourceClusterShortID),
			SourceDataBackupID: cast.ToString(sourceDataBackupID),
			DestAppID:          appID,
		}
		dbrsDestInstance := dbrs.DestInstance{
			AgentPoolName:  "public-scs",
			InstanceID:     masterNode.ResourceId,
			XagentHost:     masterNode.FloatingIP,
			XagentPort:     masterNode.XagentPort,
			AgentHost:      masterNode.FloatingIP,
			AgentPort:      masterNode.Port + 5000, // x1-api 注册 dbrs 策略
			AgentLocalPort: masterNode.Port + 5000,
			DatabaseHost:   masterNode.FloatingIP,
			DatabasePort:   masterNode.Port,
			DatabasePath:   "/mnt/data/redis_" + strconv.Itoa(masterNode.Port),
		}
		if common.IsEdgeRegion() {
			xagentHost, xagentPort, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(dbrsDestInstance.XagentHost, dbrsDestInstance.XagentPort)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse vep xagent host fail", logit.Error("err", err))
				return err
			}
			dbrsDestInstance.XagentHost = xagentHost
			dbrsDestInstance.XagentPort = xagentPort
			agentHost, agentPort, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(dbrsDestInstance.AgentHost, dbrsDestInstance.AgentPort)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse vep agent host fail", logit.Error("err", err))
				return err
			}
			dbrsDestInstance.AgentHost = agentHost
			dbrsDestInstance.AgentPort = agentPort
			dbHost, dbPort, err := vep.GetVpcEndpointModelServices().ParseVpcEndpoint(dbrsDestInstance.DatabaseHost, dbrsDestInstance.DatabasePort)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "parse vep db host fail", logit.Error("err", err))
				return err
			}
			dbrsDestInstance.DatabaseHost = dbHost
			dbrsDestInstance.DatabasePort = dbPort
		}
		createRestoreParams.DestInstances = append(createRestoreParams.DestInstances, dbrsDestInstance)
		restoreServiceID, err = dbrsComponent.CreateBackupRestore(ctx, &createRestoreParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "create dbrs backup restore fail", logit.Error("err", err))
			return err
		}

		if err := saveDbrsRestoreServiceID(ctx, taskID, masterNode.NodeId, restoreServiceID); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("save restoreServiceID fail"), logit.Error("error", err))
		}
	}

	// (5) 查询 DBRS 恢复任务状态
	for {
		queryParams := dbrs.QueryRestoreParams{
			DataType:                   "Redis",
			DataBackupRestoreServiceID: restoreServiceID,
		}
		restoreServiceStatus, err := dbrsComponent.QueryBackupRestore(ctx, &queryParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "query dbrs backup restore fail", logit.Error("err", err))
			return err
		}

		if restoreServiceStatus == "doing" {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(10 * time.Second):
				continue
			}
		}

		if restoreServiceStatus == "failed" {
			resource.LoggerTask.Warning(ctx, "dbrs restore result is failed")
			return errors.New("dbrs restore result is failed")
		}

		if restoreServiceStatus == "success" {
			break
		}
	}

	// (6) 发送 xagent 请求重启 Redis
	// 获取节点密码
	defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultACL fail", logit.Error("err", err))
		return err
	}
	password := ""
	if defaultACL != nil {
		password = defaultACL.Password
	}

	// 构造恢复参数
	paramsRecoverRedis := ParamsRecoverRedis{
		DbrsConfig: &DbrsConf{
			DataBackupID: sourceDataBackupID,
		},
		Meta: &xagent.Meta{
			Engine:        masterNode.Engine,
			EngineVersion: masterNode.EngineVersion,
			Basedir:       masterNode.Basedir,
			Port:          int32(masterNode.Port),
			Password:      password,
		},
	}

	// 获取TDE配置(暂时不需要使用)
	redisTdeInfos, err := x1model.RedisTdeGetByAppId(ctx, cluster.AppId)
	if err != nil {
		errorMessage := "get cluster tde info failed"
		resource.LoggerTask.Error(ctx, errorMessage, logit.String("clusterShowID:", base_utils.Format(cluster.AppId)),
			logit.Error("error", err))
		return err
	}
	var redisConfList []*RedisConf
	if len(redisTdeInfos) > 0 {
		tdeKey := redisTdeInfos[0].TdeKey
		redisConfList = append(redisConfList, &RedisConf{
			ConfName:  "tde-key",
			ConfValue: tdeKey,
		})
	}
	paramsRecoverRedis.RedisConfList = redisConfList

	recoverReq := xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: masterNode.FloatingIP,
			Port: cast.ToInt32(masterNode.XagentPort)},
		Action:     "recover_redis_by_dbrs",
		Params:     paramsRecoverRedis,
		TimeoutSec: recoverTimeoutSec,
	}

	resource.LoggerTask.Notice(ctx, "call xagent request", logit.String("recoverRedisByDbrsReq:", base_utils.Format(recoverReq)))

	asyncTask := xagent.Instance().DoAsync(ctx, &recoverReq)
	rsp, err := asyncTask.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "call x-agent recover fail", logit.Error("err", err), logit.String("xagent_rsp:", base_utils.Format(rsp)),
			logit.String("nodeId", masterNode.NodeId))
		return err
	}
	return nil
}
