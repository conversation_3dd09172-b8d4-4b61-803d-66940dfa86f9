/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessRecoverInOriginalClusterDispatchTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := util.GetRecoverOriginalParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	appBackupId := param.AppBackupID

	// 获取cluster信息,注意:此处使用的是cluster表的cluster_id字段,备份表的shardId对应cluster_short_id,下发数据恢复任务时会做映射
	cluster, err := x1model.ClusterGetByClusterId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster fail", logit.String("clusterId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if cluster == nil {
		resource.LoggerTask.Warning(ctx, "cluster not found", logit.String("clusterId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("cluster(%s) not found", teu.Entity)
	}

	// 执行数据恢复任务
	if err := processRecoverClusterTask(ctx, cluster, appBackupId, false); err != nil {
		resource.LoggerTask.Warning(ctx, "process recover in original cluster fail", logit.String("clusterId", teu.Entity),
			logit.Error("error", err))
		return err
	}

	return nil
}
