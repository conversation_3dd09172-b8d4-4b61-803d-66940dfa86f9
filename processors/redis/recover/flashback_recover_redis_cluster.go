/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 * 2025/03/27 <EMAIL>
 *
 **************************************************************************/

package recover

import (
	"context"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func processFlashbackRecoverRedisClusterPrework(ctx context.Context, cluster *x1model.Cluster) error {
	// (1) 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	app, err := x1model.ApplicationGetByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	clusterShortID := cluster.ClusterShortID
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == clusterShortID {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// (2) 发送 xagent 请求重启 Redis
	// 获取节点密码
	defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultACL fail", logit.Error("err", err))
		return err
	}
	password := ""
	if defaultACL != nil {
		password = defaultACL.Password
	}

	// 构造恢复参数
	paramsRecoverRedis := ParamsRecoverRedis{
		DbrsConfig: &DbrsConf{},
		Meta: &xagent.Meta{
			Engine:        masterNode.Engine,
			EngineVersion: masterNode.EngineVersion,
			Basedir:       masterNode.Basedir,
			Port:          int32(masterNode.Port),
			Password:      password,
		},
	}

	preworkReq := xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: masterNode.FloatingIP,
			Port: cast.ToInt32(masterNode.XagentPort)},
		Action:     "recover_redis_prework",
		Params:     paramsRecoverRedis,
		TimeoutSec: recoverTimeoutSec,
	}

	resource.LoggerTask.Notice(ctx, "call xagent request", logit.String("recoverRedisPreworkReq:", base_utils.Format(preworkReq)))

	asyncTask := xagent.Instance().DoAsync(ctx, &preworkReq)
	rsp, err := asyncTask.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "call x-agent recover fail", logit.Error("err", err), logit.String("xagent_rsp:", base_utils.Format(rsp)),
			logit.String("nodeId", masterNode.NodeId))
		return err
	}
	return nil
}

func processFlashbackRecoverRedisClusterLoadData(ctx context.Context,
	cluster *x1model.Cluster, appDataMoment string) error {
	// (1) 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	app, err := x1model.ApplicationGetByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	clusterShortID := cluster.ClusterShortID
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == clusterShortID {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// (2) 发送 xagent 请求重启 Redis
	// 获取节点密码
	defaultACL, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultACL fail", logit.Error("err", err))
		return err
	}
	password := ""
	if defaultACL != nil {
		password = defaultACL.Password
	}

	// 构造恢复参数
	paramsRecoverRedis := ParamsRecoverRedis{
		DbrsConfig: &DbrsConf{
			RestorePointInDatetime: appDataMoment,
		},
		Meta: &xagent.Meta{
			Engine:        masterNode.Engine,
			EngineVersion: masterNode.EngineVersion,
			Basedir:       masterNode.Basedir,
			Port:          int32(masterNode.Port),
			Password:      password,
		},
	}

	recoverReq := xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: masterNode.FloatingIP,
			Port: cast.ToInt32(masterNode.XagentPort)},
		Action:     "recover_redis_by_flashback",
		Params:     paramsRecoverRedis,
		TimeoutSec: recoverTimeoutSec,
	}

	resource.LoggerTask.Notice(ctx, "call xagent request", logit.String("recoverRedisPreworkReq:", base_utils.Format(recoverReq)))

	asyncTask := xagent.Instance().DoAsync(ctx, &recoverReq)
	rsp, err := asyncTask.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "call x-agent recover fail", logit.Error("err", err), logit.String("xagent_rsp:", base_utils.Format(rsp)),
			logit.String("nodeId", masterNode.NodeId))
		return err
	}
	return nil
}
