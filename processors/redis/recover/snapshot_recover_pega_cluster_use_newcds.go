/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/02 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"errors"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type ParamsRecoverPega struct {
	CDSConfig *CDSConfig `json:"cds_config"`
}

type CDSConfig struct {
	NewCDSID string `json:"new_cds_id"`
}

// 请求 xagent 使用新 cds 盘
func processSnapshotRecoverPegaClusterUseNewCDS(ctx context.Context, taskID string, cluster *x1model.Cluster) error {
	// (1) 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("clusterShowId", cluster.AppId),
			logit.Error("queryError", err))
		return err
	}

	appID := cacheCluster.ClusterShowId
	clusterShortID := cluster.ClusterShortID

	// (2) 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == clusterShortID {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// (3) 获取 CDS ID
	volumeList, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
		VmID:      masterNode.ResourceId,
		UserID:    app.UserId,
		IsShortID: false,
	})

	newCDSID := ""
	for _, volume := range volumeList {
		if volume.Name == getNewCDSName(ctx, taskID, masterNode.NodeShortID) {
			newCDSID = volume.Id
			break
		}
	}

	if newCDSID == "" {
		resource.LoggerTask.Warning(ctx, "get new cds failed")
		return errors.New("get new cds failed")
	}

	// (4) 请求 Xagent 挂载新 cds，重启 pega
	paramsRecoverPega := ParamsRecoverPega{
		CDSConfig: &CDSConfig{
			NewCDSID: newCDSID,
		},
	}

	recoverReq := xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: masterNode.FloatingIP,
			Port: cast.ToInt32(masterNode.XagentPort)},
		Action:     "recover_pega_by_snapshot",
		Params:     paramsRecoverPega,
		TimeoutSec: recoverTimeoutSec,
	}

	resource.LoggerTask.Notice(ctx, "call xagent request", logit.String("recoverPegaBySnapshotReq:", base_utils.Format(recoverReq)))
	asyncTask := xagent.Instance().DoAsync(ctx, &recoverReq)
	rsp, err := asyncTask.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "call x-agent recover fail", logit.Error("err", err), logit.String("xagent_rsp:", base_utils.Format(rsp)),
			logit.String("nodeId", masterNode.NodeId))
		return err
	}
	return nil
}
