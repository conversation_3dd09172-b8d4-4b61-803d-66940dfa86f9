/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/12 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

/*
backup_record 表中 object_key 字段为快照 ID，如 s-F6vL1XJX
*/
func getSnapshotID(ctx context.Context, appShortID int64, clusterShortID int, appBackupID string, isClone bool) (snapshotID string, err error) {
	var backupModels []*csmaster_model_interface.BackupRecord
	if err := resource.CsmasterModel.GetAllByCond(ctx, &backupModels, "cluster_id = ?", appShortID); err != nil {
		return "", err
	}

	objectKey := ""
	if isClone {
		for _, item := range backupModels {
			if item.ShardName == cast.ToString(clusterShortID) {
				objectKey = item.ObjectKey
			}
		}
	} else {
		for _, item := range backupModels {
			if item.ShardName == cast.ToString(clusterShortID) && item.BatchID == appBackupID {
				objectKey = item.ObjectKey
			}
		}
	}

	// eg: s-F6vL1XJX
	if !strings.HasPrefix(objectKey, "s-") {
		errMsg := "backup info is not correct"
		resource.LoggerTask.Error(ctx, errMsg,
			logit.Int64("appShortID:", appShortID),
			logit.Int("clusterShortID:", clusterShortID),
			logit.String("objectKey:", base_utils.Format(objectKey)))
		return "", cerrs.ErrInvalidParams.Errorf(errMsg)
	}

	snapshotID = objectKey
	resource.LoggerTask.Notice(ctx, "get target backup info",
		logit.String("appShortID:", base_utils.Format(appShortID)),
		logit.String("clusterShortID:", base_utils.Format(clusterShortID)),
		logit.String("snapshotID:", base_utils.Format(snapshotID)))
	return snapshotID, nil
}

// 支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度1-65。
func getNewCDSName(ctx context.Context, taskID string, nodeShortID int) string {
	return "recover_" + taskID + "_" + strconv.Itoa(nodeShortID)
}

func loadSnapshotRestoreNewCDSID(ctx context.Context, taskID string, nodeID string) (string, error) {
	key := "snapshot_restore_newcds:" + taskID + ":" + nodeID
	newCDSID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return newCDSID, nil
}

func saveSnapshotRestoreNewCDSID(ctx context.Context, taskID string, nodeID string, newCDSID string) error {
	key := "snapshot_restore_newcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.SetNX(ctx, key, newCDSID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteSnapshotRestoreNewCDSID(ctx context.Context, taskID string, nodeID string) error {
	key := "snapshot_restore_newcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processSnapshotRecoverPegaClusterCreateNewCDS(ctx context.Context, taskID string, cluster *x1model.Cluster, appBackupID string, isClone bool) error {
	// (1) 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("clusterShowId", cluster.AppId),
			logit.Error("queryError", err))
		return err
	}

	appID := cacheCluster.ClusterShowId
	appShortID := cacheCluster.Id
	clusterShortID := cluster.ClusterShortID

	// (2) 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	curCluster := &x1model.Cluster{}
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == clusterShortID {
			curCluster = cluster
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// (3) 尝试从 Redis 中获取 newCDS ID
	newCDSID, err := loadSnapshotRestoreNewCDSID(ctx, taskID, masterNode.NodeId)
	if err == nil && len(newCDSID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have create newCDSID: %s", newCDSID))
	}

	// (4) 若无 newCDSID 则创建 CDS ID
	if newCDSID == "" {
		snapshotID, err := getSnapshotID(ctx, appShortID, clusterShortID, appBackupID, isClone)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get sourceBackupInfo fail",
				logit.Int("clusterShortID:", clusterShortID),
				logit.Error("Error", err))
			return err
		}

		// create cds
		newCDSID, err = bccresource.BccResourceOp().CreateCds(ctx, &bccresource.CreateCdsParam{
			UserID:      app.UserId,
			Name:        getNewCDSName(ctx, taskID, masterNode.NodeShortID),
			Description: "for recover",
			CdsSizeInGB: int(curCluster.DiskSize), // cluster.DiskSize
			SnapshotID:  snapshotID,
			ZoneName:    masterNode.LogicZone, // zoneD
		})

		if err != nil {
			resource.LoggerTask.Warning(ctx, "create cds fail",
				logit.String("NodeID:", masterNode.NodeId),
				logit.Int("clusterShortID:", clusterShortID),
				logit.Error("Error", err))
			return err
		}

		if err := saveSnapshotRestoreNewCDSID(ctx, taskID, masterNode.NodeId, newCDSID); err != nil {
			resource.LoggerTask.Warning(ctx, "save new cdsid to redis failed",
				logit.Int("clusterShortID:", clusterShortID),
				logit.String("newCDSID:", base_utils.Format(newCDSID)),
				logit.Error("error", err))
		}
	}

	// (5) 对 CDS 进行 attach 操作
	for {
		// 获取 CDS 详情
		resp, err := bccresource.BccResourceOp().GetCdsDetail(ctx, &bccresource.GetCdsDetailParam{
			UserID:   app.UserId,
			VolumeID: newCDSID,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cds detail faield",
				logit.Int("clusterShortID:", clusterShortID),
				logit.String("newCDSID:", base_utils.Format(newCDSID)),
				logit.Error("error", err))
			return err
		}

		switch resp.Status {
		case "Available":
			err := bccresource.BccResourceOp().AttachCds(ctx, &bccresource.AttachCdsParam{
				UserID:   app.UserId,
				VolumeID: newCDSID,
				VmID:     masterNode.ResourceId,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "cds detail failed",
					logit.Int("clusterShortID:", clusterShortID),
					logit.String("newCDSID:", base_utils.Format(newCDSID)),
					logit.Error("error", err))
				return err
			}
		case "InUse":
			resource.LoggerTask.Notice(ctx, "cds status is inuse",
				logit.Int("clusterShortID:", clusterShortID),
				logit.String("newCDSID:", base_utils.Format(newCDSID)))
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}
