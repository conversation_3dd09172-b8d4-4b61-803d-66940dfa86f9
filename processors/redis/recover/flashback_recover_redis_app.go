/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/08/12 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/dbrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func loadFlashbackRestoreServiceID(ctx context.Context, taskID string, nodeID string) (string, error) {
	key := "flashback_restore_:" + taskID + ":" + nodeID
	restoreServiceID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return restoreServiceID, nil
}

func saveFlashbackRestoreServiceID(ctx context.Context, taskID string, nodeID string, restoreServiceID string) error {
	key := "flashback_restore_:" + taskID + ":" + nodeID
	if err := resource.RedisClient.SetNX(ctx, key, restoreServiceID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteFlashbackRestoreServiceID(ctx context.Context, taskID string, nodeID string) error {
	key := "flashback_restore_:" + taskID + ":" + nodeID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processCreatePointInTimeRestore(ctx context.Context, taskID string, sourceAppID string, sourceClusterShortID int, appID string,
	appDataMoment string, masterNode *x1model.Node, engineVersion string) (restoreServiceID string, err error) {
	// 尝试从 Redis 中获取 restoreServiceID
	restoreServiceID, err = loadFlashbackRestoreServiceID(ctx, taskID, masterNode.NodeId)
	if err == nil && len(restoreServiceID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have create restoreServiceID: %s", restoreServiceID))
		return restoreServiceID, nil
	}

	// 请求 DBRS 创建恢复任务
	dbrsComponent := dbrs.DbrsResourceOp()
	createRestoreParams := dbrs.CreatePointInTimeRestoreParams{
		DataType:               "Redis",
		SourceAppID:            sourceAppID,
		SourceClusterID:        cast.ToString(sourceClusterShortID),
		RestorePointInDateTime: appDataMoment,
		DestAppID:              appID,
	}
	region := env.IDC()
	databasePath := "/mnt/data/redis_" + strconv.Itoa(masterNode.Port)
	if engineVersion == "7.0" {
		databasePath = "/mnt/data/redis_" + strconv.Itoa(masterNode.Port) + "/appendonlydir"
	}

	createRestoreParams.DestInstances = append(createRestoreParams.DestInstances, dbrs.DestInstance{
		AgentPoolName:  "public-scs",
		Region:         region,
		InstanceID:     masterNode.ResourceId,
		XagentHost:     masterNode.FloatingIP,
		XagentPort:     masterNode.XagentPort,
		AgentHost:      masterNode.FloatingIP,
		AgentPort:      masterNode.Port + 5000, // x1-api 注册 dbrs 策略
		AgentLocalPort: masterNode.Port + 5000,
		DatabaseHost:   masterNode.FloatingIP,
		DatabasePort:   masterNode.Port,
		DatabasePath:   databasePath,
	})
	restoreServiceID, err = dbrsComponent.CreatePointInTimeRestore(ctx, &createRestoreParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create dbrs backup point in time restore fail", logit.Error("err", err))
		return "", err
	}

	if err := saveFlashbackRestoreServiceID(ctx, taskID, masterNode.NodeId, restoreServiceID); err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("save restoreServiceID fail"), logit.Error("error", err))
	}
	return restoreServiceID, nil
}

func processFlashbackRecoverRedisClusterDownloadData(ctx context.Context, teu *workflow.TaskExecUnit) error {
	taskID := teu.TaskID
	// 1 解析参数
	param, err := util.GetRecoverNewClusterParams(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	sourceAppID := param.SrcClusterShowID
	appDataMoment := param.AppDataMoment
	destAppID := param.DestClusterShowID

	// 2 获取两个集群的分片
	// 2.1 获取原集群分片列表
	srcAppShardList, err := buildmeta.GetShardList(ctx, sourceAppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get shard list by appId failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("srcAppId:", base_utils.Format(sourceAppID)),
		logit.String("srcAppShardList:", base_utils.Format(srcAppShardList)))

	// 2.2 获取目标集群分片列表
	destAppShardList, err := buildmeta.GetShardList(ctx, destAppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get shard list by appId failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "get app shard list success", logit.String("destAppID:", base_utils.Format(destAppID)),
		logit.String("destAppShardList:", base_utils.Format(destAppShardList)))

	// 2.3 原分片和目标分片列表排序
	sort.Ints(srcAppShardList)
	sort.Ints(destAppShardList)

	// 2.4 cluster ID map
	clusterDest2SrcMap := make(map[int]int)
	for i, clusterShortID := range destAppShardList {
		clusterDest2SrcMap[clusterShortID] = srcAppShardList[i]
	}

	// 3 请求 DBRS 创建恢复任务
	// 3.1 获取数据恢复分片主节点的信息
	dbrsTaskMap := make(map[int]string)
	app, err := x1model.ApplicationGetByAppId(ctx, destAppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Role == x1model.RoleTypeMaster {
				sourceClusterShortID := clusterDest2SrcMap[cluster.ClusterShortID]
				masterNode := node
				restoreServiceID, err := processCreatePointInTimeRestore(ctx, taskID, sourceAppID, sourceClusterShortID, destAppID,
					appDataMoment, masterNode, cluster.EngineVersion)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "processCreatePointInTimeRestore fail", logit.Error("err", err))
					return err
				}
				dbrsTaskMap[cluster.ClusterShortID] = restoreServiceID
			}
		}
	}

	// 4 检查
	if len(dbrsTaskMap) != len(destAppShardList) {
		resource.LoggerTask.Warning(ctx, "dbrs task count not match", logit.Error("err", err))
		return errors.New("dbrs task count not match")
	}

	// (5) 查询 DBRS 恢复任务状态
	dbrsComponent := dbrs.DbrsResourceOp()
	for _, clusterShortID := range destAppShardList {
		for {
			queryParams := dbrs.QueryPointInTimeRestoreParams{
				DataType:                    "Redis",
				PointInTimeRestoreServiceID: dbrsTaskMap[clusterShortID],
			}
			restoreServiceStatus, err := dbrsComponent.QueryPointInTimeRestore(ctx, &queryParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "query dbrs backup restore fail",
					logit.String("PointInTimeRestoreServiceID", dbrsTaskMap[clusterShortID]),
					logit.Error("err", err))
				return err
			}

			if restoreServiceStatus == "doing" {
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(10 * time.Second):
					continue
				}
			}

			if restoreServiceStatus == "failed" {
				resource.LoggerTask.Warning(ctx, "dbrs restore result is failed",
					logit.String("PointInTimeRestoreServiceID", dbrsTaskMap[clusterShortID]))
				return errors.New("dbrs restore result is failed")
			}

			if restoreServiceStatus == "success" {
				break
			}
		}
	}

	return nil
}
