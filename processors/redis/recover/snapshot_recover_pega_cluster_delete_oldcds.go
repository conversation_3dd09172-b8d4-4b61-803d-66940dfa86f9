/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2024/09/02 <EMAIL> Exp
 *
 **************************************************************************/

package recover

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func loadSnapshotRestoreOldCDSID(ctx context.Context, taskID string, nodeID string) (string, error) {
	key := "snapshot_restore_oldcds:" + taskID + ":" + nodeID
	newCDSID, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return newCDSID, nil
}

func saveSnapshotRestoreOldCDSID(ctx context.Context, taskID string, nodeID string, oldCDSID string) error {
	key := "snapshot_restore_oldcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.SetNX(ctx, key, oldCDSID, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func deleteSnapshotRestoreOldCDSID(ctx context.Context, taskID string, nodeID string) error {
	key := "snapshot_restore_oldcds:" + taskID + ":" + nodeID
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func processSnapshotRecoverPegaClusterDeleteOldCDS(ctx context.Context, taskID string, cluster *x1model.Cluster) error {
	// (1) 获取集群信息
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, cluster.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cs master cluster model",
			logit.String("clusterShowId", cluster.AppId),
			logit.Error("queryError", err))
		return err
	}

	appID := cacheCluster.ClusterShowId
	clusterShortID := cluster.ClusterShortID

	// (2) 获取数据恢复分片主节点的信息
	masterNode := &x1model.Node{}
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == clusterShortID {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				}
			}
		}
	}

	// (3) 获取 CDS ID
	oldCDSID, err := loadSnapshotRestoreOldCDSID(ctx, taskID, masterNode.NodeId)
	if err == nil && len(oldCDSID) != 0 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("cache have oldCDSID: %s", oldCDSID))
	}

	// (4) 若无 oldCDSID 则查询 CDS ID
	if oldCDSID == "" {
		volumeList, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
			VmID:      masterNode.ResourceId,
			UserID:    app.UserId,
			IsShortID: false,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cds list faield",
				logit.Int("clusterShortID:", clusterShortID),
				logit.Error("error", err))
			return err
		}

		for _, volume := range volumeList {
			if volume.Type == "System" {
				continue
			}

			if volume.Name != getNewCDSName(ctx, taskID, masterNode.NodeShortID) {
				oldCDSID = volume.Id
				break
			}
		}

		if err = saveSnapshotRestoreOldCDSID(ctx, taskID, masterNode.NodeId, oldCDSID); err != nil {
			resource.LoggerTask.Warning(ctx, "save old cdsid to redis failed",
				logit.Int("clusterShortID:", clusterShortID),
				logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
				logit.Error("error", err))
		}
	}

	resource.LoggerTask.Notice(ctx, "old cds info",
		logit.Int("clusterShortID:", clusterShortID),
		logit.String("oldCDSID", oldCDSID))

	// (4) 删除旧 CDSID
	if oldCDSID == "" {
		return nil
	}

	for {
		// 获取 CDS 详情
		resp, err := bccresource.BccResourceOp().GetCdsDetail(ctx, &bccresource.GetCdsDetailParam{
			UserID:   app.UserId,
			VolumeID: oldCDSID,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cds detail faield",
				logit.Int("clusterShortID:", clusterShortID),
				logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
				logit.Error("error", err))
			return err
		}

		switch resp.Status {
		case "Available":
			// delete cds
			err = bccresource.BccResourceOp().DeleteCds(ctx, &bccresource.DeleteCdsParam{
				UserID:   app.UserId,
				VolumeID: oldCDSID,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "cds delete failed",
					logit.Int("clusterShortID:", clusterShortID),
					logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
					logit.Error("error", err))
				return err
			}

			if err = deleteSnapshotRestoreOldCDSID(ctx, taskID, masterNode.NodeId); err != nil {
				resource.LoggerTask.Warning(ctx, "delete old cdsid from redis failed",
					logit.Int("clusterShortID:", clusterShortID),
					logit.Error("error", err))
			}
			return nil
		case "InUse":
			// detach cds
			err = bccresource.BccResourceOp().DetachCds(ctx, &bccresource.DetachCdsParam{
				UserID:    app.UserId,
				VolumeID:  oldCDSID,
				VmID:      masterNode.ResourceId,
				IsShortID: false,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "cds detach failed",
					logit.Int("clusterShortID:", clusterShortID),
					logit.String("oldCDSID:", base_utils.Format(oldCDSID)),
					logit.String("VmID:", base_utils.Format(masterNode.ResourceId)),
					logit.Error("error", err))
				return err
			}
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}
