/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
恢复数据
*/

package recover

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/repo"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type RecoverParams struct {
	Meta      *xagent.Meta `json:"meta"`
	BosConfig *BosConfig   `json:"bos_config"`
}

type BosConfig struct {
	Endpoint   string `json:"endpoint"`
	Ak         string `json:"ak"`
	Sk         string `json:"sk"`
	BucketName string `json:"bucket_name"`
	FileName   string `json:"file_name"`
}

func recoverNode(ctx context.Context, cluster *x1model.Cluster, app *x1model.Application, password string) error {
	repoConf := repo.GetRepo("backup_bos").Conf
	bucketName, fileName, err := repo.ParseBosAccess(app.CloneDataAccess)
	if err != nil {
		return err
	}
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "recover",
				Params: &RecoverParams{
					Meta: &xagent.Meta{
						Engine:        node.Engine,
						EngineVersion: node.EngineVersion,
						Basedir:       node.Basedir,
						Port:          int32(node.Port),
						Password:      password,
					},
					BosConfig: &BosConfig{
						Endpoint:   repoConf.Endpoint,
						Ak:         repoConf.BosAk,
						Sk:         repoConf.BosSk,
						BucketName: bucketName,
						FileName:   fileName,
					},
				},
			}
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			return err
		}
	}
	return nil
}

func ProcessRecover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	password := ""
	if defaultAcl != nil {
		password = defaultAcl.Password
	}

	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		cluster := cluster
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return recoverNode(ctx, cluster, app, password)
			})
		})
	}
	_, err = g.Wait()
	return err
}
