/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/04/11 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file quit_sync_group.go
 * <AUTHOR>
 * @date 2023/04/11 17:24:09
 * @brief
 *
 **/

package syncgroup

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-global-api/gsdk"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessQuitSyncGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if len(app.SyncGroupID) == 0 {
		return nil
	}

	// gAPI获取异地多活成员
	stsToken, err := compo_utils.GetGMasterToken(ctx, app.UserId)
	if err != nil {
		return errors.Wrap(err, "get sts token fail")
	}

	// 请求global-api，退出异地多活组
	_, err = gsdk.GApiSdk.QuitSyncGroup(ctx, &gsdk.QuitSyncGroupRequest{
		Token:           stsToken,
		SyncGroupShowID: app.SyncGroupID,
		MemberID:        app.AppId,
		Region:          app.Region,
	})

	if err != nil {
		resource.LoggerTask.Warning(ctx, "quit sync group failed.",
			logit.String("syncGroupID", app.SyncGroupID))
		return err
	}

	return nil
}
