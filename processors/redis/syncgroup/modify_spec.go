/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/06 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file modify_spec.go
 * <AUTHOR>
 * @date 2022/12/06 16:10:27
 * @brief sync group support modify spec
 *
 **/

package syncgroup

import (
	"context"
	"path"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-global-api/gsdk"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type findOpHeaderParams struct {
	WorkDir    string `json:"workdir"`
	NodeStatus string `json:"nodeStatus"`
}

type FindOpHeaderResult struct {
	OpID int64 `json:"opId"`
}

func ProcessCheckModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 不是异地多活集群则跳过
	if len(app.SyncGroupID) == 0 {
		return nil
	}

	// gAPI获取异地多活成员
	stsToken, err := compo_utils.GetGMasterToken(ctx, app.UserId)
	if err != nil {
		return errors.Wrap(err, "get sts token fail")
	}

	resp, err := gsdk.GApiSdk.ListSyncGroupMember(ctx, &gsdk.ListSyncGroupMemberRequest{
		Token:           stsToken,
		SyncGroupShowID: app.SyncGroupID,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "list sync group member failed.", logit.String("syncGroupID", app.SyncGroupID))
		return err
	}
	destClusters := make([]csdk.DestInfo, 0)
	for _, member := range resp.Members {
		if member.MemberID == app.AppId {
			continue
		}
		destClusters = append(destClusters, csdk.DestInfo{
			DestCluster: member.MemberID,
			IP:          member.Addr,
			Port:        member.Port,
		})
	}
	for {
		complete := true
		// 调用cs-master sdk 获取该集群的同步延迟
		getSyncDelayParam := &csmaster.GetSyncGroupDelayParam{
			UserID:       app.UserId,
			AppID:        app.AppId,
			DestClusters: destClusters,
		}

		resp, err := csmaster.CsmasterOp().GetSyncGroupDelay(ctx, getSyncDelayParam)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get sync group delay failed", logit.Error("error", err))
			return err
		}

		resource.LoggerTask.Notice(ctx, "get sync delay success", logit.String("resp", base_utils.Format(resp)))
		delayResult := true
		for _, dest := range resp.DestClusters {
			if dest.Delay > 1000000 {
				resource.LoggerTask.Warning(ctx, "check sync delay greater than threshold.", logit.String("destCluster", dest.DestCluster))
				delayResult = false
			}
		}
		if !delayResult {
			if err := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), "check group can switch failed"); err != nil {
				resource.LoggerTask.Warning(ctx, "update task to manual failed", logit.Error("error", err))
			}
			return errors.Errorf("check sync delay greater than threshold.")
		}
		g := &gtask.Group{
			AllowSomeFail: false,
		}

		for _, cluster := range app.Clusters {
			cluster := cluster
			g.Go(func() error {
				return checkClusterSyncPointWithXAgent(ctx, cluster, app.UseNewPackage == 1)
			})
		}

		_, err = g.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check sync group sync op id failed.", logit.Error("error", err))
			complete = false
		}

		if complete {
			return nil
		}

		select {
		case <-ctx.Done():
			if err := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), "check group can switch failed"); err != nil {
				resource.LoggerTask.Warning(ctx, "update task to manual failed", logit.Error("error", err))
			}
			return ctx.Err()
		case <-time.After(10 * time.Second):
			continue
		}
	}
}

func checkClusterSyncPointWithXAgent(ctx context.Context, cluster *x1model.Cluster, useNewPackage bool) error {
	// 找到新主和旧主
	var toDeleteMaster *x1model.Node
	var toCreateMaster *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			switch node.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = node
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				toDeleteMaster = node
			}
		}
	}

	if toCreateMaster == nil {
		resource.LoggerTask.Notice(ctx, "no new master found.", logit.String("cluster", cluster.ClusterId))
		return nil
	}
	if toDeleteMaster == nil {
		resource.LoggerTask.Warning(ctx, "no old master found.", logit.String("cluster", cluster.ClusterId))
		return errors.New("no old master found")
	}

	// 获取对应节点的op id
	oldMasterOpID, err := checkNodeSyncPointWithXAgent(ctx, toDeleteMaster, useNewPackage)
	if err != nil {
		resource.LoggerTask.Trace(ctx, "check sync group sync op id failed.", logit.String("cluster", cluster.ClusterId))
		return err
	}

	var newMasterOpID int64
	if cluster.Engine == x1model.EnginePegaDB {
		newMasterOpID, err = checkPegaNewMasterOpIDWithXAgent(ctx, toCreateMaster)
		if err != nil {
			resource.LoggerTask.Trace(ctx, "check sync group sync op id failed.", logit.String("cluster", cluster.ClusterId))
			return err
		}
	} else {
		newMasterOpID, err = checkNodeSyncPointWithXAgent(ctx, toCreateMaster, useNewPackage)
		if err != nil {
			resource.LoggerTask.Trace(ctx, "check sync group sync op id failed.", logit.String("cluster", cluster.ClusterId))
			return err
		}
	}

	if oldMasterOpID <= newMasterOpID {
		resource.LoggerTask.Trace(ctx, "check sync group sync op id failed.", logit.String("cluster", cluster.ClusterId),
			logit.Int64("newMasterOpID", newMasterOpID), logit.Int64("oldMasterOpID", oldMasterOpID))
		return errors.Errorf("check cluster %s op id failed.", cluster.ClusterId)
	}

	resource.LoggerTask.Trace(ctx, "check sync group sync op id success.", logit.String("cluster", cluster.ClusterId),
		logit.Int64("newMasterOpID", newMasterOpID), logit.Int64("oldMasterOpID", oldMasterOpID))
	return nil
}

func checkNodeSyncPointWithXAgent(ctx context.Context, node *x1model.Node, useNewPackage bool) (int64, error) {
	// 通过x-agent进行查询op-id
	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}

	findOpHeaderReq := xagent.Request{
		Addr:   &xAgentAddr,
		Action: "findOpHeader",
		Params: &findOpHeaderParams{
			WorkDir:    node.Basedir,
			NodeStatus: node.Status,
		},
	}
	if useNewPackage {
		// 包管理的controller入口挪进去了
		findOpHeaderReq.Params = &findOpHeaderParams{
			WorkDir:    path.Join(node.Basedir, "/agent"),
			NodeStatus: node.Status,
		}
	}

	resp, err := xagent.Instance().Do(ctx, &findOpHeaderReq)
	if err != nil {
		resource.LoggerTask.Trace(ctx, "get op id by x-agent failed.", logit.Error("err", err))
		return 0, err
	}

	result := &FindOpHeaderResult{}
	if err := resp.ParseResult(result); err != nil {
		resource.LoggerTask.Trace(ctx, "parse op id from x-agent failed.", logit.Error("err", err))
		return 0, err
	}
	return result.OpID, nil
}

type findPegaNewMasterOpIDParams struct {
	Port string `json:"port"`
}

// checkPegaNewMasterOpIDWithXAgent 通过鹏博新开发的wal tools获取pega新主节点的opid，用于切主探测
func checkPegaNewMasterOpIDWithXAgent(ctx context.Context, node *x1model.Node) (int64, error) {
	// 通过x-agent进行查询op-id
	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}

	findPegaNewMasterOpIDReq := xagent.Request{
		Addr:   &xAgentAddr,
		Action: "find_pega_newmaster_opid",
		Params: &findPegaNewMasterOpIDParams{
			Port: cast.ToString(node.Port),
		},
	}

	resp, err := xagent.Instance().Do(ctx, &findPegaNewMasterOpIDReq)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "find_pega_newmaster_opid by x-agent failed.", logit.Error("err", err))
		return 0, err
	}

	result := &FindOpHeaderResult{}
	if err := resp.ParseResult(result); err != nil {
		resource.LoggerTask.Warning(ctx, "find_pega_newmaster_opid from x-agent failed.", logit.Error("err", err))
		return 0, err
	}
	resource.LoggerTask.Trace(ctx, "find_pega_newmaster_opid success.", logit.String("result", base_utils.Format(result)))
	return result.OpID, nil
}
