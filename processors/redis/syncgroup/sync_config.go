/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file open_sync_config.go
 * <AUTHOR>
 * @date 2022/09/07 16:38:19
 * @brief will open support_multi_active = yes config
 *
 **/

package syncgroup

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func CloseMultiDBConfig(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 调用x1-api modifyConfigInfo接口，关闭多DB
	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "select_cmd_enable",
			ConfModule: 3,
			ConfValue:  "no",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessOpenSyncConfig(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 调用x1-api modifyConfigInfo接口，打开support-multi-active
	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "support_multi_active",
			ConfModule: 1,
			ConfValue:  "yes",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}

	// 目前sync-agent不支持多db，所以暂时关闭，待支持后，再去掉
	if err = CloseMultiDBConfig(ctx, teu); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info select cmd enable failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessCloseSyncConfig(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}

	// 调用x1-api modifyConfigInfo接口，关闭support-multi-active
	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "support_multi_active",
			ConfModule: 1,
			ConfValue:  "no",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}

	return nil
}

// ProcessOpenOpHeader will set use-op-header yes
func ProcessOpenOpHeader(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	engineVersion := util.GetEngineVersion(app)
	engine := util.GetEngine(app)
	// pega 或 redis 6.0
	if engine == x1model.EnginePegaDB ||
		((engine == x1model.EngineRedis || engine == x1model.EngineBDRPRedis) && engineVersion == "6.0") {
		modifyReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "use-op-header",
				ConfModule: 1,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
	}
	// pegadb下调迁移速度
	if engine == x1model.EnginePegaDB {
		modifyMigrateBatchRateReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "migrate-batch-rate-limit-mb",
				ConfModule: 1,
				ConfValue:  "2",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyMigrateBatchRateReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyMigrateBatchRateReq)))
			return err
		}
	}

	// redis 6.0
	if (engine == x1model.EngineRedis || engine == x1model.EngineBDRPRedis) && engineVersion == "6.0" {
		modifyConfReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "aof-shift",
				ConfModule: 1,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyConfReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyConfReq)))
			return err
		}
	}

	return nil
}

// ProcessCloseOpHeader will set use-op-header no
func ProcessCloseOpHeader(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	engineVersion := util.GetEngineVersion(app)
	engine := util.GetEngine(app)
	// pega 或 redis 6.0
	if engine == x1model.EnginePegaDB ||
		((engine == x1model.EngineRedis || engine == x1model.EngineBDRPRedis) && engineVersion == "6.0") {
		modifyReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "use-op-header",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
			return err
		}
	}
	// pegadb回调迁移速度
	if engine == x1model.EnginePegaDB {
		modifyMigrateBatchRateReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "migrate-batch-rate-limit-mb",
				ConfModule: 1,
				ConfValue:  "4", // 更新版本的可能支持更大的值，但是历史上默认是4mb，所以这里先按4mb
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyMigrateBatchRateReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyMigrateBatchRateReq)))
			return err
		}
	}
	// redis 6.0
	if (engine == x1model.EngineRedis || engine == x1model.EngineBDRPRedis) && engineVersion == "6.0" {
		modifyConfReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "aof-shift",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyConfReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyConfReq)))
			return err
		}
	}

	return nil
}

// ProcessOpenAppendOnly will set use-op-header no
func ProcessOpenAppendOnly(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "appendonly",
			ConfModule: 1,
			ConfValue:  "yes",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}

	return nil
}

// ProcessCloseAppendOnly will set use-op-header no
func ProcessCloseAppendOnly(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	engineVersion := util.GetEngineVersion(app)
	if engineVersion == "6.0" {
		modifyReq := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "appendonly",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}
