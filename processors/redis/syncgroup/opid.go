/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* waitopid.go */
/*
modification history
--------------------
2023/07/03 , by <PERSON> (<PERSON><PERSON><PERSON><PERSON>@baidu.com) , create
*/
/*
DESCRIPTION
todo
*/

package syncgroup

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/sync_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessSyncGroupCheckOPID(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}
	for {
		if IsAllShardOPIDGt0(ctx, app) {
			resource.LoggerTask.Warning(ctx, "opid is empty", logit.Error("err", err))
			// 如果有空的，就过5秒重试
			return nil
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}

func IsAllShardOPIDGt0(ctx context.Context, app *x1model.Application) bool {
	opIDMap, err := getOpIDMapByApp(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get op id fail", logit.Error("err", err))
		return false
	}
	var notDoneCluster []string
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == 0 {
			resource.LoggerTask.Warning(ctx, "has no cluster short id pls fix", logit.String("clusterid", cluster.ClusterId))
			return false
		}
		gShortID := sync_group.GetSyncGroupShardIDByClusterShortID(cluster.ClusterShortID)
		opId, has := opIDMap[gShortID]
		if !has {
			resource.LoggerTask.Warning(ctx, "has no opid",
				logit.String("clusterid", cluster.ClusterId), logit.String("raw result", base_utils.Format(opIDMap)))
			return false
		}
		if opId < 0 {
			notDoneCluster = append(notDoneCluster, cluster.ClusterId)
		}
	}
	if len(notDoneCluster) != 0 {
		resource.LoggerTask.Notice(ctx, "some shard opid not gt 0", logit.String("shards", base_utils.ToString(notDoneCluster)))
		return false
	}
	return true
}

func getOpIDMapByApp(ctx context.Context, app *x1model.Application) (map[int64]int64, error) {
	opIDMap := make(map[int64]int64)
	for _, cluster := range app.Clusters {
		if cluster.ClusterShortID == 0 {
			return nil, fmt.Errorf("cluster:%s has no cluster short id pls fix", cluster.ClusterId)
		}
		gShortID := sync_group.GetSyncGroupShardIDByClusterShortID(cluster.ClusterShortID)
		if _, exist := opIDMap[gShortID]; exist {
			return nil, fmt.Errorf("cluster:%s is duplicate pls check,gShardID:%d", cluster.ClusterId, gShortID)
		}
		opIDMap[gShortID] = -1
		var m *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				m = node
				break
			}
		}
		if m == nil {
			return nil, fmt.Errorf("cluster:%s has no master node", cluster.ClusterId)
		}
		// 集群版没密码
		infoServer, err := getInfoServer(ctx, m.FloatingIP, m.Port)
		if err != nil {
			return nil, fmt.Errorf("cluster:%s get redis infoServer fail,err:%w", cluster.ClusterId, err)
		}
		infoServer = strings.ReplaceAll(infoServer, "\r\n", "\n")
		infoServer = strings.ReplaceAll(infoServer, "\r", "")
		for _, line := range strings.Split(infoServer, "\n") {
			if strings.HasPrefix(line, "#") {
				continue
			}
			// resource.LoggerTask.Notice(ctx, "REPLICATION LINE", logit.String("line", base_utils.Format(line)))
			kv := strings.Split(line, ":")
			if len(kv) < 2 {
				continue
			}
			if kv[0] == "op_id" {
				if opIDMap[gShortID] == -1 {
					opIDMap[gShortID] = cast.ToInt64(kv[1])
				}
				break
			}
		}
		if opIDMap[gShortID] == -1 {
			return nil, fmt.Errorf("cluster:%s not found its opid in redis info,redisInfo:%s", cluster.ClusterId, infoServer)
		}
	}
	resource.LoggerTask.Trace(ctx, "get opid map success", logit.String("result", base_utils.Format(opIDMap)))
	return opIDMap, nil
}

func getInfoServer(ctx context.Context, ip string, port int) (string, error) {
	cli := single_redis.NewClient(ip, port)
	defer cli.Close()
	return cli.Info(ctx, "server").Result()
}
