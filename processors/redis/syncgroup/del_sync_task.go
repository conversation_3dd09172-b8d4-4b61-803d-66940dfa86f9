/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file del_sync_task.go
 * <AUTHOR>
 * @date 2022/11/22 11:01:30
 * @brief
 *
 **/

package syncgroup

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-global-api/model/sync_group_model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessDeleteSyncTask will delete sync task to data server
func ProcessDeleteSyncTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var member *sync_group_model.SyncGroupMember
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusDelMember {
			member = m
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to add sync task")
		return errors.Errorf("no member to add sync")
	}
	// todo: call cs-master to add sync task into data server
	//
	return nil
}
