/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/11/21 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file add_sync_task.go
 * <AUTHOR>
 * @date 2022/11/21 17:01:04
 * @brief call x1-api to add sync task
 *
 **/

package syncgroup

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/sync_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-global-api/model/sync_group_model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/utils/dataserver"
)

var (
	G_SHARD_ID_IDC_CODE_SHIFT = 32
	SYNC_RDB_PREFIX           = "BDRP_SYNC_RDB_"
	SYNC_GROUP_PREFIX         = "BDRP_SYNC_GROUP_"
)

// ProcessAddSyncTask will add sync task to data server
func ProcessAddSyncTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var member *sync_group_model.SyncGroupMember
	syncMember := make([]*sync_group_model.SyncGroupMember, 0)
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusToSync {
			member = m
		} else {
			syncMember = append(syncMember, m)
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to add sync data task")
		return errors.Errorf("no member to add sync data task")
	}
	if member.MemberId != app.AppId {
		resource.LoggerTask.Warning(ctx, "to sync node not this node", logit.String("tosync node", member.MemberId))
		return errors.Errorf("to sync node not this node")
	}
	if err := CheckAllBgsaveDone(ctx, app); err != nil {
		return errors.Errorf("check bgsave done fail,err:%s", err.Error())
	}
	// // 添加相关key
	if err := createSyncInfo(ctx, app, syncMember); err != nil {
		resource.LoggerTask.Warning(ctx, "add sync info to data server failed.", logit.String("appId", teu.Entity))
		return errors.Errorf("add sync info to data server failed.")
	}
	return nil
}

func createSyncInfo(ctx context.Context, app *x1model.Application, syncMember []*sync_group_model.SyncGroupMember) error {
	var toAddMember *sync_group_model.SyncGroupMember = nil
	for _, m := range syncMember {
		if m.Status == sync_group_model.StatusInitail {
			toAddMember = m
		}
	}
	if toAddMember == nil {
		return errors.New("not found new member info")
	}

	if err := sync_group.DataserverIns(ctx, app).InitAJoinTask(ctx, app, toAddMember.Addr,
		cast.ToString(toAddMember.Port), toAddMember.Auth); err != nil {
		resource.LoggerTask.Warning(ctx, "register join member task fail", logit.Error("err", err))
		return err
	}
	return nil
}

func ProcessCheckAndDeleteSyncMember(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 查询参数，找出待退出节点的memberId
	// 为其检查并删除同步通道
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var member *sync_group_model.SyncGroupMember
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusDelMember {
			member = m
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to del sync channel")
		return errors.Errorf("no member to del sync")
	}

	// 获取cluster的members
	membersKey := SYNC_GROUP_PREFIX + app.AppId
	memberInfo, err := dataserver.Pick(ctx, app).SMembers(ctx, membersKey).Result()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get sync group members failed.", logit.Error("err", err))
		return errors.Errorf("get smembers failed.")
	}
	resource.LoggerTask.Trace(ctx, "get sync group members success.", logit.String("smembers", base_utils.Format(memberInfo)))
	// check and delete 同步通道
	toDelPrefix := member.Addr + ":" + cast.ToString(member.Port)
	resource.LoggerTask.Trace(ctx, "toDelPrefix", logit.String("toDelPrefix", toDelPrefix))
	for _, m := range memberInfo {
		if !strings.HasPrefix(m, toDelPrefix) {
			continue
		}
		// 存在则删除
		if err := dataserver.Pick(ctx, app).SRem(ctx, membersKey, m).Err(); err != nil {
			resource.LoggerTask.Warning(ctx, "del sync group members failed.", logit.Error("err", err), logit.String("member", m))
			return errors.Errorf("del smembers failed.")
		}
	}
	return nil
}
