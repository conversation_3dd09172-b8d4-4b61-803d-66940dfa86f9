/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file add_sync_channel.go
 * <AUTHOR>
 * @date 2022/09/07 17:16:51
 * @brief add sync channel
 *
 **/

package syncgroup

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-global-api/gsdk"
	"icode.baidu.com/baidu/scs/x1-global-api/model/sync_group_model"
	"icode.baidu.com/baidu/scs/x1-task/utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessAddSyncChannel(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 查询参数，找出除自己以外的所有集群
	// 为其创建同步通道
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var members []*sync_group_model.SyncGroupMember
	for _, member := range param.SyncGroupMembers {
		if member.MemberId != app.AppId {
			members = append(members, member)
		}
	}
	if len(members) == 0 {
		resource.LoggerTask.Warning(ctx, "no member to add sync channel")
		return errors.Errorf("no member to sync")
	}
	for _, m := range members {
		// 创建同步通道
		AddChannelReq := csmaster.ModifySyncChannelParam{
			UserID:            app.UserId,
			AppID:             app.AppId,
			Action:            "insert",
			PeerClusterShowID: m.MemberId,
			PeerIP:            m.Addr,
			PeerPort:          m.Port,
			PeerAuth:          m.Auth,
		}
		if err := csmaster.CsmasterOp().ModifySyncChannel(ctx, &AddChannelReq); err != nil {
			resource.LoggerTask.Warning(ctx, "add member to sync failed,", logit.String("member", base_utils.Format(m)))
			return errors.Errorf("add sync channel failed")
		}
	}
	return nil
}

func ProcessDelSyncChannel(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 查询参数，找出除自己以外的所有集群
	// 为其删除同步通道
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}

	var members []*sync_group_model.SyncGroupMember
	for _, member := range param.SyncGroupMembers {
		if member.MemberId != app.AppId {
			members = append(members, member)
		}
	}
	if len(members) == 0 {
		resource.LoggerTask.Warning(ctx, "no member to del sync channel")
		return errors.Errorf("no member to del sync")
	}
	for _, m := range members {
		// 删除同步通道
		AddChannelReq := csmaster.ModifySyncChannelParam{
			UserID:            app.UserId,
			AppID:             app.AppId,
			Action:            "delete",
			PeerClusterShowID: m.MemberId,
			PeerIP:            m.Addr,
			PeerPort:          m.Port,
			PeerAuth:          m.Auth,
		}
		if err := csmaster.CsmasterOp().ModifySyncChannel(ctx, &AddChannelReq); err != nil {
			resource.LoggerTask.Warning(ctx, "del member to sync failed,", logit.String("member", base_utils.Format(m)))
			return errors.Errorf("del sync channel failed")
		}
	}
	return nil
}

func ProcessModifyWhiteList(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if !utils.CheckSyncGroupField(app) {
		return nil
	}

	// 修改集群白名单为0.0.0.0/0
	modifyIPWhiteListReq := csmaster.ModifyClusterIPWhitelistParam{
		UserID:        app.UserId,
		AppID:         app.AppId,
		Action:        "insert",
		ClusterIpList: []string{"0.0.0.0/0"},
	}
	if err := csmaster.CsmasterOp().ModifyClusterIPWhitelist(ctx, &modifyIPWhiteListReq); err != nil {
		resource.LoggerTask.Warning(ctx, "modify cluster white list failed",
			logit.String("params", base_utils.Format(modifyIPWhiteListReq)))
		return errors.Errorf("modify cluster white list failed")
	}
	return nil
}

func ProcessQuitSyncChannel(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 查询参数，找出待退出节点的memberId
	// 为其删除同步通道
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var member *sync_group_model.SyncGroupMember
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusDelMember {
			member = m
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to del sync channel")
		return errors.Errorf("no member to del sync")
	}

	// 删除同步通道
	AddChannelReq := csmaster.ModifySyncChannelParam{
		UserID:            app.UserId,
		AppID:             app.AppId,
		Action:            "delete",
		PeerClusterShowID: member.MemberId,
		PeerIP:            member.Addr,
		PeerPort:          member.Port,
		PeerAuth:          member.Auth,
	}
	if err := csmaster.CsmasterOp().ModifySyncChannel(ctx, &AddChannelReq); err != nil {
		resource.LoggerTask.Warning(ctx, "quit member to sync failed,", logit.String("member", base_utils.Format(member)))
		return errors.Errorf("quit sync channel failed")
	}
	return nil
}

func ProcessJoinSyncChannel(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	// 待加入的集群
	var member *sync_group_model.SyncGroupMember
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusInitail {
			member = m
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to add sync channel")
		return errors.Errorf("no member to sync")
	}
	// 创建同步通道
	AddChannelReq := csmaster.ModifySyncChannelParam{
		UserID:            app.UserId,
		AppID:             app.AppId,
		Action:            "insert",
		PeerClusterShowID: member.MemberId,
		PeerIP:            member.Addr,
		PeerPort:          member.Port,
		PeerAuth:          member.Auth,
	}
	if err := csmaster.CsmasterOp().ModifySyncChannel(ctx, &AddChannelReq); err != nil {
		resource.LoggerTask.Warning(ctx, "add member to sync failed,", logit.String("member", base_utils.Format(member)),
			logit.String("appId", base_utils.Format(app.AppId)))
		return errors.Errorf("add sync channel failed")
	}
	return nil
}

// ProcessUpdateSyncGroupWhiteList will delete all ip white list and add only sync-agent ip
func ProcessUpdateSyncGroupWhiteList(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if !utils.CheckSyncGroupField(app) {
		return nil
	}

	// 获取集群所有的ip白名单
	clusterIPWhiteList, err := csmaster.CsmasterOp().GetWhiteIPs(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster white list failed",
			logit.String("appId", base_utils.Format(app.AppId)))
		return errors.Errorf("get cluster white list failed")
	}

	for i, ip := range clusterIPWhiteList {
		if ip == "*.*.*.*" {
			clusterIPWhiteList[i] = "0.0.0.0/0"
		}
	}

	// 删除所有的ip白名单
	modifyIPWhiteListReq := csmaster.ModifyClusterIPWhitelistParam{
		UserID:        app.UserId,
		AppID:         app.AppId,
		Action:        "delete",
		ClusterIpList: clusterIPWhiteList,
	}
	if err := csmaster.CsmasterOp().ModifyClusterIPWhitelist(ctx, &modifyIPWhiteListReq); err != nil {
		resource.LoggerTask.Warning(ctx, "modify cluster white list failed",
			logit.String("params", base_utils.Format(modifyIPWhiteListReq)))
		return errors.Errorf("modify cluster white list failed")
	}

	// 添加sync-agent ip白名单
	stsToken, err := compo_utils.GetGMasterToken(ctx, app.UserId)
	if err != nil {
		return errors.Wrap(err, "get sts token fail")
	}

	// 请求global-api，更新实例的白名单
	_, err = gsdk.GApiSdk.UpdateSyncGroupWhiteList(ctx, &gsdk.UpdateSyncGroupWhiteListRequest{
		Token:           stsToken,
		SyncGroupShowID: app.SyncGroupID,
		AppID:           app.AppId,
	})

	if err != nil {
		resource.LoggerTask.Warning(ctx, "add sync-agent ip into whitelist failed.",
			logit.String("syncGroupID", app.SyncGroupID))
		return err
	}
	return nil
}
