/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* check_sync.go */
/*
modification history
--------------------
2023/07/12 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package syncgroup

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/sync_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-global-api/model/sync_group_model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessCheckRdbSyncTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params", logit.String("param", base_utils.Format(param)))
	if len(param.GroupId) == 0 {
		resource.LoggerTask.Error(ctx, "group id is empty")
		return errors.Errorf("groupid is empty")
	}

	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return errors.Errorf("teu is nil ptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var member *sync_group_model.SyncGroupMember
	syncMember := make([]*sync_group_model.SyncGroupMember, 0)
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusToSync {
			member = m
		} else {
			syncMember = append(syncMember, m)
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to add sync data task")
		return errors.Errorf("no member to add sync data task")
	}
	if member.MemberId != app.AppId {
		resource.LoggerTask.Warning(ctx, "to sync node not this node", logit.String("tosync node", member.MemberId))
		return errors.Errorf("to sync node not this node")
	}

	var toAddMember *sync_group_model.SyncGroupMember = nil
	for _, m := range syncMember {
		if m.Status == sync_group_model.StatusInitail {
			toAddMember = m
		}
	}
	if toAddMember == nil {
		return errors.New("not found new member info")
	}

	for {
		isDone, err := sync_group.DataserverIns(ctx, app).IsAllRDBSynced(ctx, app, toAddMember.Addr,
			cast.ToString(toAddMember.Port), toAddMember.Auth)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check is all rdb synced fail", logit.Error("err", err))
			return err
		}
		if isDone {
			return nil
		}
		resource.LoggerTask.Notice(ctx, "rdb not sync done,retry")
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}
