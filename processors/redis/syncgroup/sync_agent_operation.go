/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/23 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file sync_agent_operation.go
 * <AUTHOR>
 * @date 2022/09/23 15:36:27
 * @brief sync agent operation
 *
 **/

package syncgroup

import (
	"context"
	"path"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type killSyncAgentParams struct {
	WorkDir string `json:"workdir"`
}

func ProcessKillSyncAgent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}

	g := gtask.Group{
		Concurrent:    5,
		AllowSomeFail: false,
	}

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete || node.Status == x1model.NodeOrProxyStatusToDelete {
				continue
			}
			g.Go(func() error {
				return killSyncAgentForOneRedisNodeWithXagent(ctx, node, app.UseNewPackage == 1)
			})
		}
	}

	_, err = g.Wait()
	return err
}

func killSyncAgentForOneRedisNodeWithXagent(ctx context.Context, node *x1model.Node, useNewPackage bool) error {
	xagentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}

	killSyncAgentReq := xagent.AsyncRequest{
		Addr:       &xagentAddr,
		Action:     "killSyncAgent",
		Params:     killSyncAgentParams{WorkDir: node.Basedir},
		TimeoutSec: 50,
	}
	if useNewPackage {
		killSyncAgentReq.Params = killSyncAgentParams{
			WorkDir: path.Join(node.Basedir, "/agent"),
		}
	}
	_, err := xagent.Instance().DoAsync(ctx, &killSyncAgentReq).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "kill sync agent fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))
	}
	return err
}

func ProcessDelSyncPoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}

	// 调用cs-master sdk 删除集群的异地多活同步点
	deleteSyncPointParam := &csmaster.DeleteSyncPointParam{
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := csmaster.CsmasterOp().DeleteSyncGroupPoint(ctx, deleteSyncPointParam); err != nil {
		resource.LoggerTask.Warning(ctx, "delete sync group point failed", logit.Error("error", err))
		return err
	}

	return nil
}
