package syncgroup

import (
	"context"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// IsShouldUseBdrpDataserver
// 是否命中BDRP托管dataserver标记
func IsShouldUseBdrpDataserver(ctx context.Context, app *x1model.Application) bool {
	if privatecloud.IsPrivateENV() {
		return false
	}
	flagVal, err := resource.CsmasterOpAgent.GetFlag(ctx, "sync_use_bdrp_dataserver",
		map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get sync_use_bdrp_dataserver flag fail", logit.String("userid", app.UserId),
			logit.String("vpcID", app.VpcId), logit.Error("err", err))
		return false
	}
	resource.LoggerTask.Trace(ctx, "get sync_use_bdrp_dataserver flag success", logit.String("userid", app.UserId),
		logit.String("vpcID", app.VpcId), logit.String("val", flagVal))
	return flagVal == "yes"
}
