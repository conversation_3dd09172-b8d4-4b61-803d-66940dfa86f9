package syncgroup

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-global-api/model/sync_group_model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

const RenameConfigBgsaveCmd = "aae420ac56ef116058218c11d8b35b30BGSAVE"

func CallSeedClusterToBGSAVE(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetSyncParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Warning(ctx, "get sync params fail", logit.String("param", base_utils.Format(param)))
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	if app.SyncGroupID == "" || app.SyncGroupID != param.GroupId {
		resource.LoggerTask.Warning(ctx, "app is not in the same sync group")
		return errors.Errorf("app is not in the same sync group")
	}

	var member *sync_group_model.SyncGroupMember
	syncMember := make([]*sync_group_model.SyncGroupMember, 0)
	for _, m := range param.SyncGroupMembers {
		if m.Status == sync_group_model.StatusToSync {
			member = m
		} else {
			syncMember = append(syncMember, m)
		}
	}
	if member == nil {
		resource.LoggerTask.Warning(ctx, "no member to add sync data task")
		return errors.Errorf("no member to add sync data task")
	}
	if member.MemberId != app.AppId {
		resource.LoggerTask.Warning(ctx, "to sync node not this node", logit.String("tosync node", member.MemberId))
		return errors.Errorf("to sync node not this node")
	}

	return CallBgsaveForCluster(ctx, app)
}

func CallBgsaveForCluster(ctx context.Context, app *x1model.Application) error {
	for _, cluster := range app.Clusters {
		var m *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				m = node
				break
			}
		}
		if m == nil {
			return fmt.Errorf("cluster:%s has no master node", cluster.ClusterId)
		}

		persistenceInfo, err := GetPersistenceSectionInfoWithAddress(ctx, m.FloatingIP, m.Port, "")
		if err != nil {
			return fmt.Errorf("cluster:%s get persistenceInfo fail ,err:%w", cluster.ClusterId, err)
		}
		if persistenceInfo.RdbBgsaveInProgress == "" ||
			persistenceInfo.RdbLastBgsaveStatus == "" ||
			persistenceInfo.AofRewriteInProgress == "" {
			return fmt.Errorf("cluster:%s get persistenceInfo some fail ,persistenceInfo:%s", cluster.ClusterId,
				base_utils.Format(persistenceInfo))
		}

		resource.LoggerTask.Trace(ctx, "get persistenceInfo success",
			logit.String("clusterID", cluster.ClusterId),
			logit.String("persistenceInfo", base_utils.Format(persistenceInfo)))
		// 上次bgsave必须成功
		if persistenceInfo.RdbLastBgsaveStatus != "ok" {
			return fmt.Errorf("cluster:%s last bgsave fail,RdbLastBgsaveStatus:%s",
				cluster.ClusterId, persistenceInfo.RdbLastBgsaveStatus)
		}
		// 不能在aof rewrite
		if persistenceInfo.AofRewriteInProgress != "0" {
			return fmt.Errorf("cluster:%s AofRewriteInProgress not 0,AofRewriteInProgress:%s",
				cluster.ClusterId, persistenceInfo.AofRewriteInProgress)
		}
		// 已经在bgsave的跳过
		if persistenceInfo.RdbBgsaveInProgress != "0" {
			resource.LoggerTask.Trace(ctx, "is in bgsave ,skip", logit.String("cluster id", cluster.ClusterId),
				logit.String("persistenceInfo", base_utils.Format(persistenceInfo)))
			continue
		}
		if err := DoRenameBgsaveCmd(ctx, m.FloatingIP, m.Port, ""); err != nil {
			return fmt.Errorf("cluster:%s call bgsave fail,err:%w", cluster.ClusterId, err)
		}
	}
	return nil
}

type PersistenceInfo struct {
	RdbLastBgsaveStatus  string
	RdbBgsaveInProgress  string
	AofRewriteInProgress string
}

func DoRenameBgsaveCmd(ctx context.Context, ip string, port int, password string) error {
	var cli *single_redis.SingleClient
	if password == "" {
		cli = single_redis.NewClient(ip, port)
	} else {
		cli = single_redis.NewClient(ip, port, single_redis.WithPassword(password))
	}
	defer cli.Close()
	if err := cli.Do(ctx, RenameConfigBgsaveCmd).Err(); err != nil {
		return err
	}
	return nil
}

func GetPersistenceSectionInfoWithAddress(ctx context.Context, ip string, port int, password string) (*PersistenceInfo, error) {
	var cli *single_redis.SingleClient
	if password == "" {
		cli = single_redis.NewClient(ip, port)
	} else {
		cli = single_redis.NewClient(ip, port, single_redis.WithPassword(password))
	}
	defer cli.Close()
	return GetPersistenceSectionInfo(ctx, cli)
}

func GetPersistenceSectionInfo(ctx context.Context, cli *single_redis.SingleClient) (*PersistenceInfo, error) {
	ret := PersistenceInfo{}
	infoServer, err := cli.Info(ctx, "persistence").Result()
	if err != nil {
		return nil, fmt.Errorf("get redis infoServer fail,err:%w", err)
	}
	infoServer = strings.ReplaceAll(infoServer, "\r\n", "\n")
	infoServer = strings.ReplaceAll(infoServer, "\r", "")
	for _, line := range strings.Split(infoServer, "\n") {
		if strings.HasPrefix(line, "#") {
			continue
		}
		// resource.LoggerTask.Notice(ctx, "REPLICATION LINE", logit.String("line", base_utils.Format(line)))
		kv := strings.Split(line, ":")
		if len(kv) < 2 {
			continue
		}
		if kv[0] == "rdb_last_bgsave_status" {
			ret.RdbLastBgsaveStatus = kv[1]
		}
		if kv[0] == "rdb_bgsave_in_progress" {
			ret.RdbBgsaveInProgress = kv[1]
		}
		if kv[0] == "aof_rewrite_in_progress" {
			ret.AofRewriteInProgress = kv[1]
		}
	}
	return &ret, nil
}

func CheckAllBgsaveDone(ctx context.Context, app *x1model.Application) error {
	for _, cluster := range app.Clusters {
		var m *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				m = node
				break
			}
		}
		if m == nil {
			return fmt.Errorf("cluster:%s has no master node", cluster.ClusterId)
		}
		for {
			persistenceInfo, err := GetPersistenceSectionInfoWithAddress(ctx, m.FloatingIP, m.Port, "")
			if err != nil {
				return fmt.Errorf("cluster:%s get persistenceInfo fail ,err:%w", cluster.ClusterId, err)
			}
			if persistenceInfo.RdbBgsaveInProgress == "" ||
				persistenceInfo.RdbLastBgsaveStatus == "" ||
				persistenceInfo.AofRewriteInProgress == "" {
				return fmt.Errorf("cluster:%s get persistenceInfo some fail ,persistenceInfo:%s", cluster.ClusterId,
					base_utils.Format(persistenceInfo))
			}
			resource.LoggerTask.Trace(ctx, "get persistenceInfo success",
				logit.String("clusterID", cluster.ClusterId),
				logit.String("persistenceInfo", base_utils.Format(persistenceInfo)))

			if persistenceInfo.RdbBgsaveInProgress == "0" {
				break
			}
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(5 * time.Second):
				continue
			}
		}
	}
	return nil
}
