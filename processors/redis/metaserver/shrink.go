/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* shrink.go - shrink */
/*
Modification History
--------------------
2022/5/20, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Send shrink command to metaserver
*/

package metaserver

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/timewindow"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessShrink(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}
	// 从地域不发缩分片任务
	if len(app.AppGroupID) != 0 && !IsGlobalLeader(ctx, app) {
		resource.LoggerTask.Trace(ctx, "follower region ,skip")
		return nil
	}

	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}

	metaId := app.AppShortID
	if len(app.AppGroupID) != 0 {
		metaId = app.AppGroupSeqID
	}

	inAutoBalance, err := metaCli.IsClusterInAutoBalance(ctx, metaId)
	if err != nil {
		return err
	}

	if inAutoBalance {
		return nil
	}

	hashtag_enable, err := IsSupportHashTag(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster hashtag config failed", logit.Error("error", err))
		return err
	}

	if app.SyncGroupID != "" {
		if err := metaCli.ShrinkClusterHashTagForSync(ctx, metaId, len(app.Clusters)-params.ShrinkCount,
			hashtag_enable, "yes"); err != nil {
			if strings.Contains(err.Error(), "is shriking") {
				resource.LoggerTask.Notice(ctx, "cluster is shrinking, bypass")
				return nil
			}
			resource.LoggerTask.Warning(ctx, "shrink cluster failed", logit.Error("error", err))
			return err
		}
		return nil
	}
	// 热活强制使用slot migrate模式
	if app.AppGroupID != "" &&
		(len(app.Clusters) > 0 && app.Clusters[0].Engine == x1model.EnginePegaDB) {
		if err := metaCli.SetClusterFlag(ctx, app.AppGroupSeqID, "hash_tag", hashtag_enable); err != nil {
			resource.LoggerTask.Warning(ctx, "set hash_tag flag failed", logit.Error("error", err))
			return err
		}
		if err := metaCli.ShrinkPegaHashTagForSync(ctx, metaId, len(app.Clusters)-params.ShrinkCount,
			hashtag_enable, "no"); err != nil {
			if strings.Contains(err.Error(), "is shriking") {
				resource.LoggerTask.Notice(ctx, "cluster is shrinking, bypass")
				return nil
			}
			resource.LoggerTask.Warning(ctx, "shrink cluster failed", logit.Error("error", err))
			return err
		}
	} else {
		if err := metaCli.ShrinkClusterHashTagForSync(ctx, metaId, len(app.Clusters)-params.ShrinkCount,
			hashtag_enable, "no"); err != nil {
			if strings.Contains(err.Error(), "is shriking") {
				resource.LoggerTask.Notice(ctx, "cluster is shrinking, bypass")
				return nil
			}
			resource.LoggerTask.Warning(ctx, "shrink cluster failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}

func ProcessShrinkCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}

	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}

	metaId := app.AppShortID
	if len(app.AppGroupID) != 0 {
		metaId = app.AppGroupSeqID
	}

out:
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(15 * time.Second)
			inAutoBalance, err := metaCli.IsClusterInAutoBalance(ctx, metaId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get auto balance flag failed", logit.Error("error", err))
				continue
			}
			if inAutoBalance {
				if err := updateRebalanceProgress(ctx, metaCli, metaId, teu.PTaskID); err != nil {
					resource.LoggerTask.Warning(ctx, "update rebalance progress failed", logit.Error("error", err))
				}
				continue
			}

			scaleStatus, err := metaCli.GetScaleStatus(ctx, metaId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get scale status failed", logit.Error("error", err))
				continue
			}
			if !scaleStatus.ScaleFinished {
				resource.LoggerTask.Notice(ctx, "scale not finished, try check next time")
				if err := timewindow.UpdateRebalanceProgress(ctx, teu.PTaskID, scaleStatus.TotalMigrateSlots, scaleStatus.FinishedMigrateSlots); err != nil {
					resource.LoggerTask.Warning(ctx, "update rebalance progress failed", logit.Error("error", err))
				}
				continue
			}
			if scaleStatus.ErrorCode != 0 {
				resource.LoggerTask.Warning(ctx, "scale failed", logit.Error("error", err))
				err := fmt.Errorf("scale failed, error code: %d", scaleStatus.ErrorCode)
				if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
					resource.LoggerTask.Warning(ctx, "update task to manual failed", logit.Error("error", err2))
					return err2
				}
				return err
			}
			if err := timewindow.UpdateRebalanceProgress(ctx, teu.PTaskID, scaleStatus.TotalMigrateSlots, scaleStatus.TotalMigrateSlots); err != nil {
				resource.LoggerTask.Warning(ctx, "update rebalance progress failed", logit.Error("error", err))
				continue
			}
			break out
		}
	}

	params := &csmaster.UpdateClusterModelParams{
		AppID:  app.AppId,
		UserID: app.UserId,
		Model: &csmaster.CsmasterCluster{
			MigrationStatus: MigrateSuccess,
		},
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, params); err != nil {
		resource.LoggerTask.Error(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessPegaShrinkCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := CheckIsNeedToManual(ctx, teu, 3); err != nil {
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}

	hashtag_enable, err := IsSupportHashTag(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster hashtag config failed", logit.Error("error", err))
		return err
	}

	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}

	metaId := app.AppShortID
	if len(app.AppGroupID) != 0 {
		metaId = app.AppGroupSeqID
	}

out:
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			time.Sleep(10 * time.Second)
			inAutoBalance, err := metaCli.IsClusterInAutoBalance(ctx, metaId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get auto balance flag failed", logit.Error("error", err))
				continue
			}
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("inAutoBalance: %s", base_utils.Format(inAutoBalance)))
			if inAutoBalance {
				if err := updateRebalanceProgress(ctx, metaCli, metaId, teu.PTaskID); err != nil {
					resource.LoggerTask.Warning(ctx, "update rebalance progress failed", logit.Error("error", err))
				}
				continue
			}

			scaleStatus, err := metaCli.GetScaleStatus(ctx, metaId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get scale status failed", logit.Error("error", err))
				continue
			}
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("scaleStatus: %s", base_utils.Format(scaleStatus)))

			if !scaleStatus.ScaleFinished {
				resource.LoggerTask.Notice(ctx, "scale not finished, try check next time")
				if err := timewindow.UpdateRebalanceProgress(ctx, teu.PTaskID, scaleStatus.TotalMigrateSlots, scaleStatus.FinishedMigrateSlots); err != nil {
					resource.LoggerTask.Warning(ctx, "update rebalance progress failed", logit.Error("error", err))
				}
				continue
			} else {
				/*
					针对pega扩容,当 scale_finish:1 时, 表示数据迁移任务已结束, 但是并不一定都成功了, 需要进一步校验数据迁移进度:scale_progress
					只有当 scale_finish:1 并且 scale_progress:100% 时,才表示pega扩缩容真正成功
					正常情况示例:
						1) "cluster_id:10201"
							……
						11) "scale_progress:100%"
						12) "scale_finish:1"
						13) "error_code:0"
					异常情况示例:
						1) "cluster_id:10201"
							……
						11) "scale_progress:99.93%"
						12) "scale_finish:1"
						13) "error_code:31"
				*/
				if scaleStatus.ScaleProgress == "100.00%" && scaleStatus.ErrorCode == 0 {
					// 扩容成功
					resource.LoggerTask.Notice(ctx, cast.ToString(scaleStatus.ClusterID)+" scale success")
					if err := timewindow.UpdateRebalanceProgress(ctx, teu.PTaskID, scaleStatus.TotalMigrateSlots, scaleStatus.TotalMigrateSlots); err != nil {
						resource.LoggerTask.Warning(ctx, "update rebalance progress failed", logit.Error("error", err))
						continue
					}
				} else {
					// 扩容失败, 发起重试
					resource.LoggerTask.Notice(ctx, cast.ToString(scaleStatus.ClusterID)+" scale failed", logit.Int("errorCode", scaleStatus.ErrorCode))
					// pega 扩缩容重试
					metaId := app.AppShortID
					if len(app.AppGroupID) != 0 {
						metaId = app.AppGroupSeqID
					}
					if app.SyncGroupID != "" {
						if err := metaCli.SetClusterAutoBalanceHashTagForSync(ctx, metaId, 1, hashtag_enable,
							"yes"); err != nil {
							if strings.Contains(err.Error(), "need not to balance slots") {
								resource.LoggerTask.Notice(ctx, "no need balance", logit.Error("error", err))
								return nil
							}
							return err
						}
					} else if app.AppGroupID != "" &&
						(len(app.Clusters) > 0 && app.Clusters[0].Engine == x1model.EnginePegaDB) {
						if err := metaCli.SetClusterFlag(ctx, app.AppGroupSeqID, "hash_tag", hashtag_enable); err != nil {
							resource.LoggerTask.Warning(ctx, "set hash_tag flag failed", logit.Error("error", err))
							return err
						}
						if err := metaCli.SetPegaAutoBalanceHashTagForSync(ctx, metaId, 1, hashtag_enable,
							"no"); err != nil {
							if strings.Contains(err.Error(), "need not to balance slots") {
								resource.LoggerTask.Notice(ctx, "no need balance", logit.Error("error", err))
								return nil
							}
							return err
						}
					} else {
						if err := metaCli.SetClusterAutoBalanceHashTagForSync(ctx, metaId, 1, hashtag_enable,
							"no"); err != nil {
							if strings.Contains(err.Error(), "need not to balance slots") {
								resource.LoggerTask.Notice(ctx, "no need balance", logit.Error("error", err))
								return nil
							}
							return err
						}
					}
					// 返回"触发重试"的错误,通过框架重试本步骤
					reTryErr := errors.New("pega scale failed, execute retry steps")
					return reTryErr
				}
			}
			break out
		}
	}

	params := &csmaster.UpdateClusterModelParams{
		AppID:  app.AppId,
		UserID: app.UserId,
		Model: &csmaster.CsmasterCluster{
			MigrationStatus: MigrateSuccess,
		},
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, params); err != nil {
		resource.LoggerTask.Error(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	return nil
}
