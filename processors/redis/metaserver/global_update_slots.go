/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_update_slots.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package metaserver

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessGlobalUpdateSlots(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	param, err := iface.GetGlobalParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}

	localMetaCli, err := getLocalMetaCli(ctx, app)
	if err != nil {
		return err
	}

	localCluster, err := localMetaCli.GetCluster(ctx, app.AppShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get local meta server cluster fail", logit.Error("err", err))
		return err
	}

	globalMetaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}

	mapLs2Gs := make(map[int]int)
	for _, cluster := range app.Clusters {
		mapLs2Gs[cluster.ClusterShortID] = cluster.GlobalSeqID
	}

	tarGSlot := make(map[int]metaserver.Slots, 0)
	for localShardId, lslot := range localCluster.Slot {
		tarGSlot[mapLs2Gs[localShardId]] = lslot
	}

	err = globalMetaCli.ForceUpdateClusterSlotsDist(ctx, int(param.Id), tarGSlot)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update global slots fail", logit.Error("err", err))
		return err
	}

	return nil
}
