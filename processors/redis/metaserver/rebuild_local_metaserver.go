/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* rebuild_local_metaserver.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package metaserver

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessRebuildLocalMetaserver(ctx context.Context, teu *workflow.TaskExecUnit) error {
	err := rebuildLocalMetaserver(ctx, teu)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "set slots same to global fail", logit.Error("err", err))
		return err
	}
	return nil
}

func rebuildLocalMetaserver(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	param, err := iface.GetGlobalParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "parse slot suc", logit.String("parsed slot", base_utils.Format(param.Slot)))
	localMetaCli, err := getLocalMetaCli(ctx, app)
	if err != nil {
		return err
	}

	err = processInitCluster(ctx, app, localMetaCli)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "init cluster fail", logit.Error("err", err))
		return err
	}

	mapGs2Ls := make(map[int]int)
	for _, cluster := range app.Clusters {
		mapGs2Ls[cluster.GlobalSeqID] = cluster.ClusterShortID
	}

	tarGSlot := make(map[int]metaserver.Slots, 0)
	for gShardId, gslot := range param.Slot {
		tarGSlot[mapGs2Ls[gShardId]] = gslot
	}

	err = localMetaCli.ForceUpdateClusterSlotsDist(ctx, app.AppShortID, tarGSlot)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update global slots fail", logit.Error("err", err))
		return err
	}
	return initNodesForRebuild(ctx, app, localMetaCli)
}

func initNodesForRebuild(ctx context.Context, app *x1model.Application, metaCli *metaserver.MetaserverClient) error {
	for _, cluster := range app.Clusters {
		hasMaster := false
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if hasMaster {
					resource.LoggerTask.Warning(ctx, "has multi master")
					return errors.Errorf("has multi master")
				}
				hasMaster = true
			}
		}
		if !hasMaster && len(cluster.Nodes) != 0 {
			cluster.Nodes[0].Role = x1model.RoleTypeMaster
		}
	}
	err := x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}
	return nil
	// return processRegisterNodes(ctx, app, metaCli)
}

// ProcessSelectNewMasterNode select new master for standalone
func ProcessSelectNewMasterNode(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	return initNodesForRebuild(ctx, app, nil)
}
