/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* reset_local_topology.go */
/*
modification history
--------------------
2022/05/30 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package metaserver

import (
	"context"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessResetLocalTopology(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("dbError", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	// 只获取inuse的acl
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get acl fail", logit.String("app id:", app.AppId),
			logit.Error("error", err))
		return err
	}

	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	clusterMasterMap := make(map[string]*x1model.Node)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if _, ok := clusterMasterMap[cluster.ClusterId]; ok {
					return errors.Errorf("clusterid:%s, has multi master", cluster.ClusterId)
				}
				clusterMasterMap[cluster.ClusterId] = node
			}
		}
	}

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Role == x1model.RoleTypeMaster {
				err = util.SetSlaveOf(ctx, node.FloatingIP, node.Port, password, "no", "one")
				if err != nil {
					resource.LoggerTask.Warning(ctx, "send slaveof no one fail")
					return err
				}
			}
			if node.Role == x1model.RoleTypeSlave {
				masterNode, ok := clusterMasterMap[cluster.ClusterId]
				if !ok {
					resource.LoggerTask.Warning(ctx, "no master node", logit.String("clusterid", cluster.ClusterId))
					return errors.Errorf("no master node")
				}

				err = util.SetSlaveOf(ctx, node.FloatingIP, node.Port, password, masterNode.Ip, cast.ToString(masterNode.Port))
				if err != nil {
					resource.LoggerTask.Warning(ctx, "send slaveof master fail", logit.Error("Error", err))
					continue
				}
			}
		}
	}

	return nil
}
