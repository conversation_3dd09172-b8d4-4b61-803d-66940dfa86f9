/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global_migrate_sync.go */
/*
modification history
--------------------
2022/08/16 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package metaserver

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessGlobalMigrateSync(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		return nil
	}
	if IsGlobalLeader(ctx, app) {
		return nil
	}

	if err = WaitForGlobalMigrateDone(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "wait for global migrate fail", logit.Error("err", err))
		return err
	}
	return ProcessMigrateStatusCallback(ctx, teu)
}

func IsGlobalLeader(ctx context.Context, app *x1model.Application) bool {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				return true
			}
		}
	}
	return false
}

func WaitForGlobalMigrateDone(ctx context.Context, app *x1model.Application) error {
	scheduleTimer := time.NewTimer(time.Second * 5)
	for {
		scheduleTimer.Reset(time.Second * 5)
		select {
		case <-ctx.Done():
			return errors.Errorf("WaitForGlobalMigrateDone  timeout")
		case <-scheduleTimer.C:
			{
				mStatus, err := gmaster.GlobalMasterOp().GetModifyStatus(ctx, app.AppGroupID, false, app.UserId)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "get mstatus fail", logit.Error("err", err))
					continue
				}
				if len(mStatus) == 0 {
					return nil
				}
				continue
			}
		}
	}
}
