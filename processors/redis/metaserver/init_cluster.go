/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

//	 csmaster_date_type.h
//		struct EnableReadOnly {
//		   enum {
//		       NO_CHANGE = 0,
//		       ENABLE = 1,
//		       DISABLE = 2
//		   };
//		};
const (
	CmEnableReplicaReadOnly  = 1
	CmDisableReplicaReadOnly = 2
)

func ProcessInitClusterLocal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	err = processInitCluster(ctx, app, metaCli)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "init cluster fail", logit.Error("err", err))
		return err
	}

	return processRegisterNodes(ctx, app, metaCli)
}

// processInitCluster 初始化集群信息，包括设置代理、设置集群、设置分片、初始化集群和设置集群标签等操作。
// ctx: 上下文对象，context.Context类型。
// app: 应用实例，*x1model.Application类型。包含了应用的相关信息，如用户ID、应用ID、端口号等。
// metaCli: Metaserver客户端，*metaserver.MetaserverClient类型。用于与MetaServer进行交互。
// 返回值：error类型，表示函数是否成功执行，如果执行失败则返回错误信息；如果执行成功则返回nil。
func processInitCluster(ctx context.Context, app *x1model.Application, metaCli *metaserver.MetaserverClient) error {
	initFlags := map[string]bool{
		metaserver.FlagsMasterFailover: metaserver.GetDefaultClusterFlag(ctx).MasterFailover,
		metaserver.FlagsMasterRead:     metaserver.GetDefaultClusterFlag(ctx).MasterRead,
		metaserver.FlagsSlaveRead:      metaserver.GetDefaultClusterFlag(ctx).SlaveRead,
	}
	csmasterCluster, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get csmaster cluster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("queryError", err))
		return errors.QueryCsmasterClusterFail.Wrap(err)
	}
	if csmasterCluster.EnableReadOnly == CmEnableReplicaReadOnly {
		initFlags[metaserver.FlagsSlaveRead] = true
	}

	if err := metaCli.SetProxy(ctx, app.AppShortID); err != nil {
		resource.LoggerTask.Warning(ctx, "set proxy error", logit.Error("error", err))
		return err
	}

	if err := metaCli.SetCluster(ctx, app.AppShortID, app.AppShortID, app.Port, initFlags); err != nil {
		resource.LoggerTask.Warning(ctx, "set cluster error", logit.Error("error", err))
		return err
	}

	if err := metaCli.SetAppClusterId(ctx, app.AppShortID, app.AppShortID); err != nil {
		resource.LoggerTask.Warning(ctx, "bind app error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}

		if err := metaCli.SetShard(ctx, cluster.ClusterShortID, app.AppShortID); err != nil {
			resource.LoggerTask.Warning(ctx, "set shard error", logit.Error("error", err), logit.String("shard_id", cluster.ClusterId))
			return err
		}
	}

	if err := metaCli.InitCluster(ctx, app.AppShortID); err != nil {
		resource.LoggerTask.Warning(ctx, "init cluster error", logit.Error("error", err))
		return err
	}

	hashtag_enable, err := IsSupportHashTag(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster hashtag config failed", logit.Error("error", err))
		return err
	}

	if err := metaCli.SetClusterFlag(ctx, app.AppShortID, "hash_tag", hashtag_enable); err != nil {
		resource.LoggerTask.Warning(ctx, "set hash_tag flag failed", logit.Error("error", err))
		return err
	}

	SetNewMcClusterType(ctx, app, metaCli)

	return nil
}

func SetNewMcClusterType(ctx context.Context, app *x1model.Application, metaCli *metaserver.MetaserverClient) {
	if (ShouldUseNewMetaserver(ctx, app) || strings.Contains(app.LocalMetaserver, "NewMetaserver")) &&
		len(app.Clusters) != 0 {
		resource.LoggerTask.Trace(ctx, "start to mctypeset", logit.String("local meta", app.LocalMetaserver))
		if err := metaCli.McTypeSet(ctx, app.AppShortID, app.Clusters[0].Engine); err != nil {
			// 这个功能非关键功能，设置失败就打印日志，不用阻塞后续任务
			resource.LoggerTask.Trace(ctx, "set new metaserver type fail,but can ignore,skip!", logit.Error("err", err))
		} else {
			resource.LoggerTask.Trace(ctx, "set new metaserver type success")
		}
	} else {
		resource.LoggerTask.Trace(ctx, "not hit new meta flag")
	}
}

func ShouldUseNewMetaserver(ctx context.Context, app *x1model.Application) bool {
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return true
	}
	useNewMetaserverFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "use_new_metaserver_flag",
		map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get useNewMetaserverFlag flag error", logit.Error("error", err))
		useNewMetaserverFlag = "no"
	}
	return useNewMetaserverFlag == "yes"
}

// processRegisterNodes 处理注册节点信息，包括主从节点和代理实例的注册
// ctx: 上下文对象
// app: 应用程序结构体指针，包含了所有需要注册的节点信息
// metaCli: MetaServer客户端指针，用于与MetaServer进行交互
// 返回值：error类型，如果发生错误则返回非nil错误
func processRegisterNodes(ctx context.Context, app *x1model.Application, metaCli *metaserver.MetaserverClient) error {
	for _, cluster := range app.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}

		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if err := metaCli.SetRedis(ctx, node.NodeShortID, cluster.ClusterShortID, &metaserver.MetaIp{
					FloatingIp: node.FloatingIP,
					Ip:         node.Ip,
				}, node.Port, metaserver.RedisMaster, 0, node.Region); err != nil {
					resource.LoggerTask.Warning(ctx, "set master redis error", logit.Error("error", err), logit.String("node_id", node.NodeId))
					return err
				}
			}
		}
	}

	for _, cluster := range app.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}

		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeSlave {
				if err := metaCli.SetRedis(ctx, node.NodeShortID, cluster.ClusterShortID, &metaserver.MetaIp{
					FloatingIp: node.FloatingIP,
					Ip:         node.Ip,
				}, node.Port, metaserver.RedisSlave, 0, node.Region); err != nil {
					resource.LoggerTask.Warning(ctx, "set slave redis error", logit.Error("error", err), logit.String("node_id", node.NodeId))
					return err
				}
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if err := metaCli.SetProxyInstance(ctx, proxy.ProxyShortID, app.AppShortID, &metaserver.MetaIp{
				FloatingIp: proxy.FloatingIP,
				Ip:         proxy.Ip,
			}, proxy.Region); err != nil {
				resource.LoggerTask.Warning(ctx, "set proxy error", logit.Error("error", err), logit.String("proxy_id", proxy.ProxyId))
				return err
			}
		}
	}
	return nil
}
