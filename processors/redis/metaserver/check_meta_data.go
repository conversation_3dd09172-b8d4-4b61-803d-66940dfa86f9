package metaserver

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	baseMeta "icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessTryCorrectShardMasterInMetaserver(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	cluster, err := util.FindClusterByShortID(ctx, app, param.ShardSwitchFromCsmsater.ShardShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("cannot find shard with short id %d", param.ShardSwitchFromCsmsater.ShardShortID),
			logit.Error("error", err))
		return err
	}

	shardShortId, masterShortId, err := getShardMasterInfo(ctx, app, cluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shard master info failed", logit.Error("error", err))
		return err
	}

	ok, err := isCorrectShardMasterInMetaserver(ctx, app, masterShortId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check shard master in metaserver failed", logit.Error("error", err))
		return err
	}
	if ok {
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("master %d in shard %d already correct", shardShortId, masterShortId))
		return nil
	}

	err = tryCorrectShardMasterInMetaServer(ctx, app, shardShortId, masterShortId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "correct shard master in metaserver failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("correct shard master in metaserver success, master %d in shard %d",
		shardShortId, masterShortId))

	return nil
}

func getShardMasterInfo(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) (shardShortId, masterShortId int, err error) {
	masterNode, err := getRealMasterNode(ctx, app, cluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get real master node failed", logit.Error("error", err))
		return shardShortId, masterShortId, err
	}
	if app.AppGroupID == "" {
		return cluster.ClusterShortID, masterNode.NodeShortID, nil
	}

	globalMaster, err := getMasterInGlobal(ctx, app, cluster, masterNode)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shard master in global failed", logit.Error("error", err))
		return shardShortId, masterShortId, err
	}
	return cluster.GlobalSeqID, int(globalMaster.Id), nil
}

func getRealMasterNode(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster) (*x1model.Node, error) {

	masterNode := util.GetMasterNodeInUse(ctx, cluster)
	if masterNode == nil {
		return nil, fmt.Errorf("master node is nil")
	}

	password, err := util.GetRedisDecryptedPassword(ctx, app)
	if err != nil {
		return nil, err
	}

	isMaster, err := util.IsMaster(ctx, masterNode.FloatingIP, masterNode.Port, password)
	if err != nil {
		return nil, err
	}
	if !isMaster {
		return nil, fmt.Errorf("master node is not master")
	}

	return masterNode, nil
}

func getMasterInGlobal(ctx context.Context,
	app *x1model.Application, cluster *x1model.Cluster, localMaster *x1model.Node) (*global_model.AppGroupRedis, error) {
	if len(app.AppGroupID) == 0 {
		return nil, fmt.Errorf("app group id is empty")
	}

	globalNodes, err := gmaster.GlobalMasterOp().GetNodes(ctx, &gmaster.GetNodesParams{
		UserID:        app.UserId,
		AppGroupID:    app.AppGroupID,
		ShardGlobalID: cluster.GlobalID,
		WithoutLock:   true,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get nodes from global failed",
			logit.Error("err", err),
			logit.String("app_id", app.AppId))
		return nil, err
	}
	if len(globalNodes) == 0 {
		resource.LoggerTask.Warning(ctx, "nodes is empty from global",
			logit.String("app_id", app.AppId))
		return nil, err
	}
	var globalMaster *global_model.AppGroupRedis
	for _, gNode := range globalNodes {
		if gNode.RedisId == localMaster.NodeId {
			globalMaster = gNode
			break
		}
	}
	if globalMaster == nil {
		resource.LoggerTask.Warning(ctx, "master node not found in global", logit.String("app_id", app.AppId))
		return nil, errors.Errorf("master node not found in global")
	}

	return globalMaster, nil
}

func isCorrectShardMasterInMetaserver(ctx context.Context, app *x1model.Application, masterId int) (bool, error) {
	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return false, err
	}

	ri, err := metaCli.GetRedis(ctx, masterId)
	if err != nil {
		return false, cerrs.ErrMetaFailed.Wrap(err)
	}
	if ri.Type != baseMeta.RedisMaster {
		return false, nil
	}

	return true, nil
}

func tryCorrectShardMasterInMetaServer(ctx context.Context, app *x1model.Application, shardId, masterId int) error {
	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}
	return metaCli.ManualHandover(ctx, shardId, masterId)
}
