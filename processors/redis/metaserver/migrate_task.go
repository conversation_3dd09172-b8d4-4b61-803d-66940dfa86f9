/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* migrate_task.go - start migrate task */
/*
Modification History
--------------------
2022/5/20, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Start migrate task for local metaserver
*/

package metaserver

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	NoOperation = iota
	Migrating
	MigrateSuccess
	MigrateFailed
)

const (
	WorkflowMigrateRedis = "scs-migrate-cluster-app"
	WorkflowMigratePega  = "scs-migrate-pega-cluster-app"
)

const (
	MigrateTaskMutex     = "migrate-task-mutex"
	MigrateTaskPegaMutex = "migrate-task-pega-mutex_"
)

func needStartMigrateTask(ctx context.Context, app *x1model.Application) (bool, error) {
	if len(app.AppGroupID) == 0 {
		return true, nil
	}
	groupDetail, err := gmaster.GlobalMasterOp().ListCacheGroupDetail(ctx, app.AppGroupID, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get group detail failed", logit.Error("error", err))
		return false, err
	}
	if groupDetail.Leader.ClusterShowId == app.AppId {
		return true, nil
	}
	for _, member := range groupDetail.Followers {
		if member.ClusterShowId == app.AppId {
			return false, nil
		}
	}
	return false, fmt.Errorf("app %s not found in group %s", app.AppId, app.AppGroupID)
}

func ProcessStartMigrateTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}
	pTaskParams, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get parameters failed", logit.Error("error", err))
		return err
	}
	params := &csmaster.UpdateClusterModelParams{
		AppID:  app.AppId,
		UserID: app.UserId,
		Model: &csmaster.CsmasterCluster{
			MigrationStatus: Migrating,
		},
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, params); err != nil {
		resource.LoggerTask.Error(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}

	// 从实例的热活实例组成员直接返回，下一步等着
	if len(app.AppGroupID) != 0 && !IsGlobalLeader(ctx, app) {
		resource.LoggerTask.Notice(ctx, "wait global master migrate")
		return nil
	}
	mutex := MigrateTaskMutex
	workflowMigrate := WorkflowMigrateRedis
	if app.Clusters[0].Engine == x1model.EnginePegaDB {
		workflowMigrate = WorkflowMigratePega
		if len(app.AppGroupID) != 0 {
			mutex = MigrateTaskPegaMutex + app.GlobalMetaserver + "_" + strconv.Itoa(util.GetRandomNumber())
		} else {
			mutex = MigrateTaskPegaMutex + app.LocalMetaserver + "_" + strconv.Itoa(util.GetRandomNumber())
		}
	}
	migrateTask := &workflow.Task{
		TaskID:    uuid.New().String(),
		WorkFlow:  workflowMigrate,
		Status:    "waiting",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Schedule:  time.Now(),
		Deadline:  time.Now().Add(time.Hour * 24),
		Mutex:     mutex,
		Entity:    app.AppId,
		PTaskID:   teu.TaskID,
	}
	taskParams := iface.Parameters{
		AppID:       app.AppId,
		DeferTaskID: pTaskParams.DeferTaskID,
	}
	migrateParamsBytes, _ := json.Marshal(taskParams)
	migrateTask.Parameters = string(migrateParamsBytes)

	if err := taskresource.TaskOperator.CreateTask(ctx, migrateTask); err != nil {
		resource.LoggerTask.Error(ctx, "create migrate task failed", logit.Error("error", err))
		return err
	}
	return nil
}
