/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessDeleteClusterLocal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	err = metaCli.DeleteCluster(ctx, app.AppShortID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "del cluster error", logit.Error("error", err))
		return err
	}

	return metaCli.DeleteProxy(ctx, app.AppShortID)
}

func ProcessDeleteClusterGlobal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.GlobalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	return metaCli.DeleteCluster(ctx, app.AppShortID)
}
