/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func AddNodesLocal(ctx context.Context, app *x1model.Application, isForce bool, isAddNode bool) error {
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	nodeCdnGetter, err := nodeCdnGetter(ctx, metaCli, app, isForce)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node cdn getter error", logit.Error("error", err))
		return err
	}
	proxyCdnGetter, err := proxyCdnGetter(ctx, metaCli, app, isForce)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy cdn getter error", logit.Error("error", err))
		return err
	}

	if isAddNode {
		for _, cluster := range app.Clusters {
			metaShard, err := metaCli.GetShard(ctx, cluster.ClusterShortID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get metaserver shard error", logit.Error("error", err))
				return err
			}
			var insertedMasterNode *x1model.Node
			if metaShard.Master == 0 {
				resource.LoggerTask.Notice(ctx, "no master node in metaserver shard", logit.String("shard", cluster.ClusterId))
				for _, node := range cluster.Nodes {
					if node.Role == x1model.RoleTypeMaster {
						if err := metaCli.SetRedis(ctx, node.NodeShortID, cluster.ClusterShortID, &metaserver.MetaIp{
							FloatingIp: node.FloatingIP,
							Ip:         node.Ip,
						}, node.Port, metaserver.RedisMaster, 0, nodeCdnGetter(node)); err != nil {
							resource.LoggerTask.Warning(ctx, "set master redis error", logit.Error("error", err), logit.String("node_id", node.NodeId))
							return err
						}
						insertedMasterNode = node
						break
					}
				}
			}
			for _, node := range cluster.Nodes {
				if !isForce && node.Status != x1model.NodeOrProxyStatusToCreate {
					continue
				}
				if insertedMasterNode != nil && node.NodeId == insertedMasterNode.NodeId {
					continue
				}
				if err := metaCli.SetRedis(ctx, node.NodeShortID, cluster.ClusterShortID, &metaserver.MetaIp{
					FloatingIp: node.FloatingIP,
					Ip:         node.Ip,
				}, node.Port, metaserver.RedisSlave, 0, nodeCdnGetter(node)); err != nil {
					resource.LoggerTask.Warning(ctx, "set slave redis error", logit.Error("error", err), logit.String("node_id", node.NodeId))
					return err
				}
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if !isForce && proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if err := metaCli.SetProxyInstance(ctx, proxy.ProxyShortID, app.AppShortID, &metaserver.MetaIp{
				FloatingIp: proxy.FloatingIP,
				Ip:         proxy.Ip,
			}, proxyCdnGetter(proxy)); err != nil {
				resource.LoggerTask.Warning(ctx, "set proxy instance error", logit.Error("error", err), logit.String("node_id", proxy.ProxyId))
				return err
			}
		}
	}
	return nil
}

func ProcessAddNodesGlobal(ctx context.Context, app *x1model.Application, isAddNode bool) error {
	if isAddNode {
		addNodesReq := &gmaster.AddNodesParams{
			AppID:      app.AppId,
			AppGroupID: app.AppGroupID,
			UserID:     app.UserId,
		}
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				if node.Status != x1model.NodeOrProxyStatusToCreate {
					continue
				}
				addNodesReq.Nodes = append(addNodesReq.Nodes, &global_model.AppGroupRedis{
					RedisId:    node.NodeId,
					AppId:      app.AppId,
					GroupId:    app.AppGroupID,
					Region:     app.Region,
					ShardId:    cluster.GlobalID,
					Role:       node.Role,
					Ip:         node.Ip,
					FloatingIp: node.FloatingIP,
					Port:       node.Port,
				})
			}
		}
		if err := gmaster.GlobalMasterOp().AddNodes(ctx, addNodesReq); err != nil {
			resource.LoggerTask.Warning(ctx, "update nodes to global master failed", logit.Error("error", err))
			return err
		}
	}

	addProxiesReq := &gmaster.AddProxiesParams{
		AppID:      app.AppId,
		AppGroupID: app.AppGroupID,
		UserID:     app.UserId,
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			addProxiesReq.Proxies = append(addProxiesReq.Proxies, &global_model.AppGroupProxy{
				ProxyId:    proxy.ProxyId,
				AppId:      app.AppId,
				GroupId:    app.AppGroupID,
				Region:     app.Region,
				FloatingIp: proxy.FloatingIP,
				Ip:         proxy.Ip,
				Port:       proxy.Port,
			})
		}
	}

	// standalone cluster has no proxy, skip add proxy process
	if len(addProxiesReq.Proxies) == 0 {
		return nil
	}

	if err := gmaster.GlobalMasterOp().AddProxies(ctx, addProxiesReq); err != nil {
		resource.LoggerTask.Warning(ctx, "update proxies to global master failed", logit.Error("error", err))
		return err
	}

	getProxyReq := gmaster.GetProxiesParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
	}
	gProxies, err := gmaster.GlobalMasterOp().GetProxies(ctx, &getProxyReq)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy fail", logit.Error("err", err))
		return err
	}

	mapProxyId2GlobalSeqId := make(map[string]int64, 0)
	for _, gp := range gProxies {
		mapProxyId2GlobalSeqId[gp.ProxyId] = gp.Id
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			proxy.GlobalSeqID = cast.ToInt(mapProxyId2GlobalSeqId[proxy.ProxyId])
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}

	return nil
}

// add node + proxy
func ProcessAddNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	isAddNode := true
	if len(app.AppGroupID) == 0 {
		return AddNodesLocal(ctx, app, false, isAddNode)
	}
	return nil
}

// add proxy(Proxy 启动时依赖 metaserver 注册，故实例替换 & 自愈场景，需要先将 proxy 组成到 metaserver, 再启动 proxy)
func ProcessAddProxys(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	isAddNode := false
	// 3.x 之前的版本，需要先加入metaserver，才能进行同步
	if app.Clusters[0].EngineVersion == "3.2" {
		isAddNode = true
	}

	if len(app.AppGroupID) == 0 {
		return AddNodesLocal(ctx, app, false, isAddNode)
	}
	return nil
}

// add nodes + proxys
func ProcessAddGlobalNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	isAddNode := true
	if len(app.AppGroupID) != 0 {
		return ProcessAddNodesGlobal(ctx, app, isAddNode)
	}
	return nil
}

// add proxy(Proxy 启动时依赖 metaserver 注册，故实例替换 & 自愈场景，需要先将 proxy 组成到 metaserver, 再启动 proxy)
func ProcessAddGlobalProxys(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	isAddNode := false
	// 3.x 之前的版本，需要先加入metaserver，才能进行同步
	if app.Clusters[0].EngineVersion == "3.2" {
		isAddNode = true
	}
	if len(app.AppGroupID) != 0 {
		return ProcessAddNodesGlobal(ctx, app, isAddNode)
	}
	return nil
}

func ProcessForceAddNodesLocal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	return AddNodesLocal(ctx, app, true, true)
}

func nodeCdnGetter(ctx context.Context, metaCli *metaserver.MetaserverClient, app *x1model.Application,
	isGlobal bool) (func(node *x1model.Node) string, error) {
	minIDProxyCdn, err := getMinIDProxyCdn(ctx, metaCli, app, isGlobal)
	if err != nil {
		return nil, err
	}
	return func(node *x1model.Node) string {
		if len(minIDProxyCdn) != 0 {
			return minIDProxyCdn
		}
		return node.Region
	}, nil
}

func proxyCdnGetter(ctx context.Context, metaCli *metaserver.MetaserverClient, app *x1model.Application,
	isGlobal bool) (func(proxy *x1model.Proxy) string, error) {
	minIDProxyCdn, err := getMinIDProxyCdn(ctx, metaCli, app, isGlobal)
	if err != nil {
		return nil, err
	}
	return func(proxy *x1model.Proxy) string {
		if len(minIDProxyCdn) != 0 {
			return minIDProxyCdn
		}
		return proxy.Region
	}, nil
}

func getMinIDProxyCdn(ctx context.Context, metaCli *metaserver.MetaserverClient, app *x1model.Application, isGlobal bool) (string, error) {
	minIDProxy := getMinIDProxy(app)
	if minIDProxy != nil && !isGlobal {
		minIDProxyMeta, err := metaCli.GetProxyInstance(ctx, minIDProxy.ProxyShortID)
		if err != nil {
			return "", err
		}
		return minIDProxyMeta.Cdn, nil
	}
	return "", nil
}

// ProcessAddGlobalNodesForModifySpec 热活变配AddNodes流程
// 这里新add到global的节点都将自己标注为slave，因为即将成为新master的节点若将自己标准为master
// 则会导致global metaserver判断有两个主，出现报错。
// 在后续切主流程中，Global元数据会把新主的role纠正为master，因此无需担心。
func ProcessAddGlobalNodesForModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) != 0 {
		// 都是从节点
		return processAddNodesGlobalForModifySpec(ctx, app, false)
	}
	return nil
}

func processAddNodesGlobalForModifySpec(ctx context.Context, app *x1model.Application, useRealRole bool) error {
	addNodesReq := &gmaster.AddNodesParams{
		AppID:      app.AppId,
		AppGroupID: app.AppGroupID,
		UserID:     app.UserId,
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			role := node.Role
			if !useRealRole {
				role = x1model.RoleTypeSlave
			}
			addNodesReq.Nodes = append(addNodesReq.Nodes, &global_model.AppGroupRedis{
				RedisId:    node.NodeId,
				AppId:      app.AppId,
				GroupId:    app.AppGroupID,
				Region:     app.Region,
				ShardId:    cluster.GlobalID,
				Role:       role,
				Ip:         node.Ip,
				FloatingIp: node.FloatingIP,
				Port:       node.Port,
			})
		}
	}
	if err := gmaster.GlobalMasterOp().AddNodes(ctx, addNodesReq); err != nil {
		resource.LoggerTask.Warning(ctx, "update nodes to global master failed", logit.Error("error", err))
		return err
	}

	addProxiesReq := &gmaster.AddProxiesParams{
		AppID:      app.AppId,
		AppGroupID: app.AppGroupID,
		UserID:     app.UserId,
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			addProxiesReq.Proxies = append(addProxiesReq.Proxies, &global_model.AppGroupProxy{
				ProxyId:    proxy.ProxyId,
				AppId:      app.AppId,
				GroupId:    app.AppGroupID,
				Region:     app.Region,
				FloatingIp: proxy.FloatingIP,
				Ip:         proxy.Ip,
				Port:       proxy.Port,
			})
		}
	}

	// standalone cluster has no proxy, skip add proxy process
	if len(addProxiesReq.Proxies) == 0 {
		return nil
	}

	if err := gmaster.GlobalMasterOp().AddProxies(ctx, addProxiesReq); err != nil {
		resource.LoggerTask.Warning(ctx, "update proxies to global master failed", logit.Error("error", err))
		return err
	}

	getProxyReq := gmaster.GetProxiesParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
	}
	gProxies, err := gmaster.GlobalMasterOp().GetProxies(ctx, &getProxyReq)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy fail", logit.Error("err", err))
		return err
	}

	mapProxyId2GlobalSeqId := make(map[string]int64, 0)
	for _, gp := range gProxies {
		mapProxyId2GlobalSeqId[gp.ProxyId] = gp.Id
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			proxy.GlobalSeqID = cast.ToInt(mapProxyId2GlobalSeqId[proxy.ProxyId])
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err
	}

	return nil
}

// 单副本集群在添加metaserver元数据增加新的redis node的add_nodes方法时以从节点的身份加入，需要在删除故障node
// 之前进行metaserver切主，否则metaserver不允许进行删除和后续操作
// 此方法进行场景严格判断，其他场景不适用
func ProcessMetaMasterChange(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster model error", logit.Error("error", err))
		return err
	}

	if csmasterModel.ReplicationNum != 1 {
		return nil
	}

	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		metaShard, err := metaCli.GetShard(ctx, cluster.ClusterShortID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get metaserver shard error", logit.Error("error", err))
			return err
		}

		if metaShard.SlaveNum != 1 || len(cluster.Nodes) != 2 {
			continue
		}

		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusToCreate {
				err = metaCli.ManualHandover(ctx, cluster.ClusterShortID, node.NodeShortID)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "switch metaserver shard master fail", logit.Error("error", err))
					return err
				}
				resource.LoggerTask.Notice(ctx, "switch metaserver shard master success",
					logit.String("new master", base_utils.Format(cluster.ClusterShortID)))
				break
			}

		}
	}

	return nil
}
