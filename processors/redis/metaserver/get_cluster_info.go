/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* mark_shrinked_shards.go -  */
/*
Modification History
--------------------
2022/5/21, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package metaserver

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func GetClusterTopo(ctx context.Context, appId string) (*metaserver.ClusterDetail, error) {
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application by appid failed", logit.Error("error", err))
		return nil, err
	}
	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return nil, err
	}
	metaserverClusterId := app.AppShortID
	if len(app.AppGroupID) != 0 {
		metaserverClusterId = app.AppGroupSeqID
	}

	clusterInfo, err := metaCli.GetClusterDetail(ctx, metaserverClusterId)
	resource.LoggerTask.Trace(ctx, "get cluster info", logit.String("clusterInfo", base_utils.Format(clusterInfo)))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster info failed", logit.Error("error", err))
		return nil, err
	}

	return clusterInfo, nil
}

func CloneClusterTopo(ctx context.Context, srcAppId string, srcTopo *metaserver.ClusterDetail, sortedSrcShardList []int,
	destAppId string, destTopo *metaserver.ClusterDetail, sortedDestShardList []int) error {

	// 获取目标集群的meta连接
	app, err := x1model.ApplicationGetByAppId(ctx, destAppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}
	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}
	metaserverClusterId := app.AppShortID
	if len(app.AppGroupID) != 0 {
		metaserverClusterId = app.AppGroupSeqID
	}

	srcSlotTopo := srcTopo.Slot
	destSlotTopo := destTopo.Slot

	// 遍历目标集群的拓扑分布，找到需要变更的slot，调用meta命令进行拓扑变更
	for i, shardId := range destSlotTopo {
		srcShardId := srcSlotTopo[i]
		destShardId := shardId
		srcIndex := GetIndexByShardId(srcShardId, sortedSrcShardList)
		if srcIndex >= len(sortedSrcShardList) {
			errMsg := "clone cluster_" + destAppId + " find inst failed"
			resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
			return cerrs.ErrInvalidParams.Errorf(errMsg)
		}

		targetDestShardId := sortedDestShardList[srcIndex]
		if destShardId != targetDestShardId {
			err := metaCli.UpdateSlotAndPublish(ctx, i, metaserverClusterId, destShardId, targetDestShardId, 1)
			if err != nil {
				errMsg := fmt.Sprintf("update cluster:%d slot:%d from srcShardId:%d to destShardId:%d failed",
					metaserverClusterId, i, destShardId, targetDestShardId)
				resource.LoggerTask.Error(ctx, errMsg, logit.Error("err", err))
				return err
			}
		}
	}
	return nil
}

func GetIndexByShardId(targetShardId int, sortedShardList []int) int {
	index := -1
	for i, shardId := range sortedShardList {
		if targetShardId == shardId {
			index = i
		}
	}
	return index
}
