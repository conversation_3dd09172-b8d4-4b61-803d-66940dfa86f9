/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* mark_shrinked_shards.go -  */
/*
Modification History
--------------------
2022/5/21, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package metaserver

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessMarkShrinkedShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}

	if err := checkMigrateStatus(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "check migrate status failed", logit.Error("error", err))
		return err
	}

	/*
		由于缩容耗时较长,在checkMigrateStatus执行期间集群可能发生切换、替换等操作
		如果缩容过程发生了切换,下述逻辑将节点标记为"todelete"状态时会将节点的主从状态修改为切换前的状态
			for _, node := range cluster.Nodes {
				node.Status = x1model.NodeOrProxyStatusToDelete
			}
		因此,在缩容后,需要获取集群最新的拓扑和状态
	*/
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("before shrink, app info: %s", base_utils.Format(app)))
	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("after shrink, app info: %s", base_utils.Format(app)))

	metaCli, err := getMetaCli(ctx, app)
	if err != nil {
		return err
	}
	metaserverClusterId := app.AppShortID
	if len(app.AppGroupID) != 0 {
		metaserverClusterId = app.AppGroupSeqID
	}

	shrinkedShards, err := metaCli.GetClusterShrinkedShards(ctx, metaserverClusterId)
	resource.LoggerTask.Notice(ctx, "get shrinked shards", logit.String("shrinkedShards", base_utils.Format(shrinkedShards)))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get shrinked shards failed", logit.Error("error", err))
		return err
	}

	clusterInfo, err := metaCli.GetCluster(ctx, metaserverClusterId)
	resource.LoggerTask.Trace(ctx, "get cluster info", logit.String("clusterInfo", base_utils.Format(clusterInfo)))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster info failed", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		for _, shardSeq := range shrinkedShards {
			tocheckClusterId := cluster.ClusterShortID
			if len(app.AppGroupID) != 0 {
				tocheckClusterId = cluster.GlobalSeqID
			}
			if shardSeq != tocheckClusterId {
				continue
			}
			resource.LoggerTask.Notice(ctx, "mark shrinked shards", logit.String("cluster", cluster.ClusterId))
			if _, has := clusterInfo.Slot[cluster.ClusterShortID]; has {
				resource.LoggerTask.Notice(ctx, "cluster has slots, should not be released")
				return errors.New("cluster has slots, should not be released")
			}
			for _, node := range cluster.Nodes {
				node.Status = x1model.NodeOrProxyStatusToDelete
			}
			break
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "update application failed", logit.Error("error", err))
		return err
	}
	return nil
}

func checkMigrateStatus(ctx context.Context, app *x1model.Application) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(15 * time.Second):
			clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
				continue
			}
			resource.LoggerTask.Notice(ctx, "cluster migrate status", logit.Int32("status", clusterModel.MigrationStatus))
			if clusterModel.MigrationStatus != MigrateSuccess {
				resource.LoggerTask.Notice(ctx, "app is in migrating, try next time")
				continue
			}
			return nil
		}
	}
}
