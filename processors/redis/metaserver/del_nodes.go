/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func isShardAllDelete(ctx context.Context, cluster *x1model.Cluster) bool {
	delCount := 0
	for _, node := range cluster.Nodes {
		if node.Status == x1model.NodeOrProxyStatusToDelete || node.Status == x1model.NodeOrProxyStatusToFakeDelete {
			delCount++
		}
	}
	return delCount == len(cluster.Nodes)
}

func processDelNodesLocal(ctx context.Context, app *x1model.Application) error {
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		if isShardAllDelete(ctx, cluster) {
			continue
		}
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			if err := metaCli.DeleteRedis(ctx, node.NodeShortID); err != nil {
				resource.LoggerTask.Warning(ctx, "delete redis failed", logit.Error("error", err), logit.String("node_id", node.NodeId))
				return err
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToDelete && proxy.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			if err := metaCli.DeleteProxyInstance(ctx, proxy.ProxyShortID); err != nil {
				resource.LoggerTask.Warning(ctx, "delete proxy instance failed", logit.Error("error", err), logit.String("proxy_id", proxy.ProxyId))
				return err
			}
		}
	}
	return nil
}

func processDelNodesGlobal(ctx context.Context, app *x1model.Application, isforce bool) error {
	delNodesReq := &gmaster.DeleteNodesParams{
		AppID:      app.AppId,
		AppGroupID: app.AppGroupID,
		UserID:     app.UserId,
	}
	var masterNodes []string
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if !isforce && node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			if node.Role == x1model.RoleTypeMaster {
				masterNodes = append(masterNodes, node.NodeId)
				continue
			}
			delNodesReq.NodeIds = append(delNodesReq.NodeIds, node.NodeId)
		}
	}
	if err := gmaster.GlobalMasterOp().DeleteNodes(ctx, delNodesReq); err != nil {
		resource.LoggerTask.Warning(ctx, "delete slave nodes from global master failed", logit.Error("error", err))
		return err
	}
	if len(masterNodes) != 0 {
		if err := gmaster.GlobalMasterOp().DeleteNodes(ctx, &gmaster.DeleteNodesParams{
			AppID:      app.AppId,
			AppGroupID: app.AppGroupID,
			UserID:     app.UserId,
			NodeIds:    masterNodes,
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "delete master nodes from global master failed", logit.Error("error", err))
			return err
		}
	}

	delProxiesReq := &gmaster.DeleteProxiesParams{
		AppID:      app.AppId,
		AppGroupID: app.AppGroupID,
		UserID:     app.UserId,
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToDelete && proxy.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			delProxiesReq.ProxyIDs = append(delProxiesReq.ProxyIDs, proxy.ProxyId)
		}
	}
	if len(delProxiesReq.ProxyIDs) == 0 {
		return nil
	}

	if err := gmaster.GlobalMasterOp().DeleteProxies(ctx, delProxiesReq); err != nil {
		resource.LoggerTask.Warning(ctx, "delete proxies from global master failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessDelNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		return processDelNodesLocal(ctx, app)
	}
	return processDelNodesGlobal(ctx, app, false)
}

func ProcessDelGlobalNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	return processDelNodesGlobal(ctx, app, true)
}

// ProcessDelNodesForStandalone del nodes for standalone group
func ProcessDelNodesForStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) != 0 {
		return processDelNodesGlobal(ctx, app, false)
	}
	return nil
}
