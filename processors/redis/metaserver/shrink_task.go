/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* shrink_task.go - File Description */
/*
Modification History
--------------------
2022/5/20, by <PERSON><PERSON><PERSON><PERSON>, create
*/
/*
DESCRIPTION
Detail Description
*/

package metaserver

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/google/uuid"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	WorkflowShrinkRedis = "scs-shrink-cluster-app"
	WorkflowShrinkPega  = "scs-shrink-pega-cluster-app"
)

const (
	ShrinkTaskMutex     = "shrink-task-mutex"
	ShrinkTaskPegaMutex = "shrink-task-pega-mutex"
)

func ProcessStartShrinkTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get application by appid failed", logit.Error("error", err))
		return err
	}
	pTaskParams, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get parameters failed", logit.Error("error", err))
		return err
	}
	params := &csmaster.UpdateClusterModelParams{
		AppID:  app.AppId,
		UserID: app.UserId,
		Model: &csmaster.CsmasterCluster{
			MigrationStatus: Migrating,
		},
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, params); err != nil {
		resource.LoggerTask.Error(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}
	// if app.AppGroupID != "" {
	//	resource.LoggerTask.Warning(ctx, "wait global master migrate")
	//	return nil
	// }

	mutex := ShrinkTaskMutex
	workflowShrink := WorkflowShrinkRedis
	if app.Clusters[0].Engine == x1model.EnginePegaDB {
		workflowShrink = WorkflowShrinkPega
		if len(app.AppGroupID) != 0 {
			mutex = ShrinkTaskPegaMutex + app.GlobalMetaserver + "_" + strconv.Itoa(util.GetRandomNumber())
		} else {
			mutex = ShrinkTaskPegaMutex + app.LocalMetaserver + "_" + strconv.Itoa(util.GetRandomNumber())
		}
	}
	migrateTask := &workflow.Task{
		TaskID:    uuid.New().String(),
		WorkFlow:  workflowShrink,
		Status:    "waiting",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Schedule:  time.Now(),
		Deadline:  time.Now().Add(time.Hour * 24),
		Mutex:     mutex,
		Entity:    app.AppId,
		PTaskID:   teu.TaskID,
	}
	taskParams := iface.Parameters{
		AppID:       app.AppId,
		ShrinkCount: len(app.Clusters) - pTaskParams.ShardCount,
		DeferTaskID: pTaskParams.DeferTaskID,
	}
	migrateParamsBytes, _ := json.Marshal(taskParams)
	migrateTask.Parameters = string(migrateParamsBytes)

	if err := taskresource.TaskOperator.CreateTask(ctx, migrateTask); err != nil {
		resource.LoggerTask.Error(ctx, "create migrate task failed", logit.Error("error", err))
		return err
	}

	return nil
}
