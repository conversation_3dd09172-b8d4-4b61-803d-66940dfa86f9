/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func getMetaCli(ctx context.Context, app *x1model.Application) (*metaserver.MetaserverClient, error) {
	metaServerName := app.LocalMetaserver
	if len(app.AppGroupID) != 0 {
		metaServerName = app.GlobalMetaserver
	}
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, metaServerName)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return nil, err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return nil, err
	}
	return metaCli, nil
}

func getLocalMetaCli(ctx context.Context, app *x1model.Application) (*metaserver.MetaserverClient, error) {
	metaServerName := app.LocalMetaserver
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, metaServerName)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return nil, err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return nil, err
	}
	return metaCli, nil
}

func getMinIDProxy(app *x1model.Application) *x1model.Proxy {
	minID := 0
	var minIDProxy *x1model.Proxy
	// 优先使用inuse状态节点
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusInUse {
				if minID == 0 || proxy.ProxyShortID < minID {
					minID = proxy.ProxyShortID
					minIDProxy = proxy
				}
			}
		}
	}
	if minIDProxy != nil {
		return minIDProxy
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if minID == 0 || proxy.ProxyShortID < minID {
				minID = proxy.ProxyShortID
				minIDProxy = proxy
			}
		}
	}
	return minIDProxy
}

func IsSupportHashTag(ctx context.Context, appID string) (string, error) {
	model, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appID)
	if err != nil {
		return "no", err
	}
	confs, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(model.Id))
	if err != nil {
		return "no", err
	}
	hashtag_value := "no"
	for _, conf := range confs {
		if conf.ConfName == "hashtag_enable" {
			resource.LoggerTask.Trace(ctx, "get hashtag_enable config", logit.String("cmds", conf.Value))
			if conf.Value == "yes" {
				hashtag_value = "yes"
			}
			break
		}
	}

	return hashtag_value, nil
}
