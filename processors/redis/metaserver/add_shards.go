/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func processAddShardLocalMetaserver(ctx context.Context, app *x1model.Application) error {
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		if err := metaCli.SetShard(ctx, cluster.ClusterShortID, app.AppShortID); err != nil {
			resource.LoggerTask.Warning(ctx, "add shard failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func processAddShardsGlobalMetaserver(ctx context.Context, app *x1model.Application) error {
	globalShards, err := gmaster.GlobalMasterOp().GetShards(ctx, &gmaster.GetShardsParams{
		AppGroupID: app.AppGroupID,
		AppID:      app.AppId,
		UserID:     app.UserId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get global shards list failed", logit.Error("error", err))
		return err
	}
	if len(globalShards) > len(app.Clusters) {
		resource.LoggerTask.Warning(ctx, "not enough local shards")
		return fmt.Errorf("not enough local shards, expect %d actual %d", len(globalShards), len(app.Clusters))
	} else if len(globalShards) <= len(app.Clusters) {
		if err := gmaster.GlobalMasterOp().AddShards(ctx, &gmaster.AddShardsParams{
			AppGroupID:       app.AppGroupID,
			AppID:            app.AppId,
			UserID:           app.UserId,
			TargetShardCount: len(app.Clusters),
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "add global shards list failed", logit.Error("error", err))
			return err
		}
		globalShards, err = gmaster.GlobalMasterOp().GetShards(ctx, &gmaster.GetShardsParams{
			AppGroupID: app.AppGroupID,
			AppID:      app.AppId,
			UserID:     app.UserId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get global shards list failed", logit.Error("error", err))
			return err
		}
	}

	occupiedShardMap := map[string]bool{}
	for _, cluster := range app.Clusters {
		if len(cluster.GlobalID) == 0 {
			continue
		}
		occupiedShardMap[cluster.GlobalID] = true
	}

	for _, cluster := range app.Clusters {
		if len(cluster.GlobalID) != 0 {
			continue
		}
		for _, globalShard := range globalShards {
			if _, has := occupiedShardMap[globalShard.ShardId]; !has {
				cluster.GlobalID = globalShard.ShardId
				cluster.GlobalSeqID = int(globalShard.Id)
				occupiedShardMap[cluster.GlobalID] = true
				break
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessAddShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		return processAddShardLocalMetaserver(ctx, app)
	}
	return nil
}

func ProcessAddGlobalShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		return nil
	}
	return processAddShardsGlobalMetaserver(ctx, app)
}
