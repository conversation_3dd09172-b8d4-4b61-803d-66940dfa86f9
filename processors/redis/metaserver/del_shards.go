/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package metaserver

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func getCheckShardNoSlotsFunc(ctx context.Context, app *x1model.Application, metaCli *metaserver.MetaserverClient) (func(ctx context.Context, cluster *x1model.Cluster) error, error) {
	clusterInfo, err := metaCli.GetCluster(ctx, app.AppShortID)
	if err != nil {
		return nil, err
	}
	return func(ctx context.Context, cluster *x1model.Cluster) error {
		if _, found := clusterInfo.Slot[cluster.ClusterShortID]; found {
			return fmt.Errorf("cluster %s has slot distribute", cluster.ClusterId)
		}
		return nil
	}, nil
}

func processDelShardsLocal(ctx context.Context, app *x1model.Application) error {
	metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, app.LocalMetaserver)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cluster error", logit.Error("error", err))
		return err
	}
	metaCli, err := metaserver.GetMetaserverClient(ctx, metaCluster)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get metaserver cli error", logit.Error("error", err))
		return err
	}
	checkShardNoSlotsFunc, err := getCheckShardNoSlotsFunc(ctx, app, metaCli)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster info error", logit.Error("error", err))
		return err
	}
	for i := len(app.Clusters) - 1; i >= 0; i-- {
		cluster := app.Clusters[i]
		if !isShardAllDelete(ctx, cluster) {
			continue
		}
		if err := checkShardNoSlotsFunc(ctx, cluster); err != nil {
			resource.LoggerTask.Warning(ctx, "shard cannot be deleted", logit.Error("error", err))
			return err
		}
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeSlave {
				if err := metaCli.DeleteRedis(ctx, node.NodeShortID); err != nil {
					resource.LoggerTask.Warning(ctx, "delete redis failed", logit.Error("error", err), logit.String("node_id", node.NodeId))
					return err
				}
			}
		}
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if err := metaCli.DeleteRedis(ctx, node.NodeShortID); err != nil {
					resource.LoggerTask.Warning(ctx, "delete redis failed", logit.Error("error", err), logit.String("node_id", node.NodeId))
					return err
				}
			}
		}
		if err := metaCli.DeleteShard(ctx, cluster.ClusterShortID); err != nil {
			resource.LoggerTask.Warning(ctx, "delete shard failed", logit.Error("error", err), logit.String("shard_id", cluster.ClusterId))
		}
	}
	return nil
}

func ProcessDelShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if len(app.AppGroupID) == 0 {
		return processDelShardsLocal(ctx, app)
	}
	// TODO 当app是appgroup成员时
	return nil
}
