/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package cron

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	blbSdk "icode.baidu.com/baidu/scs/x1-base/component/blb"
	blb_v2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	endpointComponent "icode.baidu.com/baidu/scs/x1-base/component/neutron/endpoint"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/vpc"
)

// ProcessDelToDeleteBLB 删除BLB
// 1. 从interface表中找到to exchange BLB
// 2. 发送请求进行删除
// 3. 删除blb元信息
func ProcessDelToDeleteBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var err error
	// 获取parameter信息
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if param.CleanAppEntranceParam == nil || param.CleanAppEntranceParam.HoursAgo < 1 {
		resource.LoggerTask.Warning(ctx, "clear app entrance hours ago lt 1")
		return errors.New("invalid param")
	}

	blbs, err := x1model.BLBGetAllByCond(ctx,
		"type in ? and status = ? and update_at < ?",
		[]string{x1model.BLBTypeAppToDelete, x1model.BLBTypeNormalToDelete}, x1model.BLBStatusAvailable,
		time.Now().Add(time.Duration(-param.CleanAppEntranceParam.HoursAgo)*time.Hour))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get blbs fail",
			logit.String("type",
				base_utils.Format([]string{x1model.BLBTypeAppToDelete, x1model.BLBTypeNormalToDelete})),
			logit.Error("error", err),
			logit.Int("hours", param.CleanAppEntranceParam.HoursAgo),
			logit.String("status", x1model.BLBStatusAvailable))
		return err
	}

	if len(blbs) == 0 {
		resource.LoggerTask.Notice(ctx, "no blb to delete",
			logit.String("type",
				base_utils.Format([]string{x1model.BLBTypeAppToDelete, x1model.BLBTypeNormalToDelete})),
			logit.Int("hours", param.CleanAppEntranceParam.HoursAgo),
			logit.String("status", x1model.BLBStatusAvailable))
		return nil
	}

	var finalErr error
	for _, blb := range blbs {
		app, err := x1model.ApplicationGetByAppId(ctx, blb.AppId)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get app fail",
				logit.String("app_id", blb.AppId), logit.Error("error", err))
			finalErr = fmt.Errorf("get app fail: %s", blb.AppId)
			continue
		}
		appEntrnaceAvailable := false
		for _, blbInner := range app.BLBs {
			if (blbInner.Type == x1model.BLBTypeNormal || blbInner.Type == x1model.BLBTypeApp) &&
				blbInner.Status == x1model.BLBStatusAvailable && blbInner.BlbId != blb.BlbId {
				appEntrnaceAvailable = true
				break
			}
		}
		if !appEntrnaceAvailable {
			resource.LoggerTask.Warning(ctx, "cat not find available app entrance",
				logit.String("app_id", blb.AppId))
			finalErr = fmt.Errorf("find available app entrance fail: %s", blb.AppId)
			continue
		}

		if err = vpc.UnbindAllSecurityGroups(ctx, blb, app); err != nil {
			resource.LoggerTask.Error(ctx, "unbind all security groups fail",
				logit.String("blb_id", blb.BlbId), logit.Error("error", err))
			finalErr = fmt.Errorf("unbind all security groups fail: %s", blb.BlbId)
			continue
		}

		if blb.Type == x1model.BLBTypeAppToDelete {
			if errerr := deleteAppBlb(ctx, blb, app); errerr != nil {
				finalErr = errerr
			}
		} else if blb.Type == x1model.BLBTypeNormalToDelete {
			if errerr := deleteNormalBlb(ctx, blb, app); errerr != nil {
				finalErr = errerr
			}
		}
		time.Sleep(time.Second * 1)
	}
	return finalErr
}

func deleteAppBlb(ctx context.Context, blb *x1model.BLB, app *x1model.Application) error {

	resource.LoggerTask.Notice(ctx, "start delete app blb", logit.String("blb", blb.BlbId))
	var userId string
	if len(blb.ResourceUserId) == 0 {
		userId = app.UserId
	} else {
		userId = blb.ResourceUserId
	}
	rss, err := blb_v2.Instance().ListAppIPGroupMember(ctx, &blb_v2.CommonIPGroupParams{
		UserID:    userId,
		IPGroupID: blb.IPGroupID,
		BlbID:     blb.BlbId,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "list rs fail",
			logit.String("blb_id", blb.BlbId), logit.Error("error", err))
		return err
	}
	if len(rss) > 0 {
		resource.LoggerTask.Warning(ctx, "blb still have rs",
			logit.String("blb_id", blb.BlbId),
			logit.String("rs", base_utils.Format(rss)))
		return fmt.Errorf("blb still have rs: %s", blb.BlbId)
	}
	env := blb_v2.Instance().GetEnv(ctx)
	if app.UserId != env.ResourcePrivateUserId && app.ResourceType == "container" {
		// 删除服务网卡
		var params endpointComponent.CommonEndpointParams
		params.UserID = app.UserId
		params.EndpointID = blb.EndpointId

		err := endpointComponent.Instance().DeleteEndpoint(ctx, &params)
		if err != nil {
			return err
		}

		blb.EndpointId = ""
		blb.EndpointIp = ""

		// 因为服务发布点被新继承了,因此不删除服务发布点
		//if err := endpoint.DeleteServicePublishEndpoint(ctx, app, "", x1model.BLBTypeAppToDelete); err != nil {
		//	resource.LoggerTask.Error(ctx, "execute delete service publish endpoint err",
		//		logit.String("appId", app.AppId),
		//		logit.Error("error", err),
		//	)
		//	return err
		//}
	}
	err = blb_v2.Instance().DeleteAppBLB(ctx, &blb_v2.CommonBLBParams{
		UserID: userId,
		ElbID:  blb.BlbId,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "delete blb fail",
			logit.String("blb_id", blb.BlbId), logit.Error("error", err))
		return err
	}
	blb.Status = x1model.BLBStatusDeleted
	blb.UpdateAt = time.Now()
	if err = x1model.BLBsSave(ctx, []*x1model.BLB{blb}); err != nil {
		resource.LoggerTask.Error(ctx, "save blb fail", logit.String("blb_id", blb.BlbId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "delete blb success", logit.String("blb_id", blb.BlbId))
	return nil
}

func deleteNormalBlb(ctx context.Context, blb *x1model.BLB, app *x1model.Application) error {

	resource.LoggerTask.Notice(ctx, "start delete normal blb", logit.String("blb", blb.BlbId))
	rss, err := blbSdk.Instance().ListRs(ctx, app.UserId, blb.BlbId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "list rs fail",
			logit.String("blb_id", blb.BlbId), logit.Error("error", err))
		return err
	}
	if len(rss) > 0 {
		resource.LoggerTask.Warning(ctx, "blb still have rs",
			logit.String("blb_id", blb.BlbId),
			logit.String("rs", base_utils.Format(rss)))
		return fmt.Errorf("blb still have rs: %s", blb.BlbId)
	}
	err = blbSdk.Instance().DeleteBLB(ctx, &blbSdk.DeleteBLBParam{
		UserID: app.UserId,
		BLBIDs: []string{blb.BlbId},
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "delete blb fail",
			logit.String("blb_id", blb.BlbId), logit.Error("error", err))
		return err
	}
	blb.Status = x1model.BLBStatusDeleted
	blb.UpdateAt = time.Now()
	if err = x1model.BLBsSave(ctx, []*x1model.BLB{blb}); err != nil {
		resource.LoggerTask.Error(ctx, "save blb fail", logit.String("blb_id", blb.BlbId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "delete blb success", logit.String("blb_id", blb.BlbId))
	return nil
}
