/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/10, by wang<PERSON><PERSON>(<EMAIL>), create
*/
package dts

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dts"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessDeleteTask 处理删除任务的函数
// ctx context.Context 上下文对象
// teu *workflow.TaskExecUnit TaskExecUnit指针，不能为nil
// 返回值 err error 如果出错则返回非nil错误
func ProcessDeleteTask(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("Dts DeleteTask fail : teu is nilptr")
	}

	// app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	// delete
	deleteTaskParam := dts.DeleteTaskParams{
		AppID: app.AppId,
	}
	err = dts.Instance().DeleteTask(ctx, &deleteTaskParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete dts task fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}
	return nil
}
