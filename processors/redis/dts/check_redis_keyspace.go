/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/12/03, by wang<PERSON><PERSON>(<EMAIL>), create
*/
package dts

import (
	"context"
	"fmt"
	"math"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type RedisMaster struct {
	IP   string
	Port int
}

// 返回 map，key 是 dbXX, value 是不带过期时间 key 的个数
func getAppKeyspaceInfo(ctx context.Context, redisMasters []*RedisMaster, password string) (map[string]int, error) {
	dbMap := make(map[string]int)
	for _, redisMaster := range redisMasters {
		keyspaceInfo, err := util.GetKeyspaceInfo(ctx, redisMaster.IP, redisMaster.Port, password)
		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("Get KeyspaceInfo from master %s:%d failed", redisMaster.IP, redisMaster.Port),
				logit.Error("error", err))
			return nil, err
		}

		for _, dbinfo := range keyspaceInfo.Db {
			dbMap[dbinfo.DBName] += dbinfo.Keys - dbinfo.Expires
		}
	}
	return dbMap, nil
}

// 检查两个 map 的 key 是否一致，同时对应的 value 差异小于 1%
func areMapsConsistent(m1, m2 map[string]int) (bool, bool) {
	// 1 检查键是否一致
	if len(m1) != len(m2) {
		return false, false
	}

	for key := range m1 {
		if _, ok := m2[key]; !ok {
			return false, false
		}
	}

	// 2 检查值的差异是否小于 1%
	for key, value1 := range m1 {
		value2, ok := m2[key]
		if !ok {
			// 这实际上不会发生，因为前面已经检查过键的一致性
			// 但为了代码的健壮性，还是保留这个检查
			return false, false
		}

		// 计算差异百分比
		if value1 == 0 {
			value1 = 1
		}
		if value2 == 0 {
			value2 = 1
		}
		diff := float64(math.Abs(float64(value1-value2))) / float64(math.Max(float64(value1), float64(value2)))

		// 使用差异百分比进行比较
		if diff > 0.01 {
			return true, false
		}
	}

	return true, true
}

// (1) 检查 db 是否一致
// (2) 检查 key 数量(不带过期时间的 key)差异是否小于 1%
func ProcessCheckRedisKeyspace(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("Dts CheckRedisKeyspace fail: teu is nilptr")
	}

	// 1 app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	// 2 password(buildMeta 时会清理 RedisACL 中的记录，故从 ProxyACL 中获取密码)
	defaultACL, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get defaultACL fail", logit.Error("err", err))
		return err
	}

	var password string
	if len(defaultACL) > 0 && defaultACL[0].Password != "" {
		password, _ = crypto_utils.DecryptKey(defaultACL[0].Password)
	}

	// 3 Node
	clusterIDToDelete := fmt.Sprintf("%s-0_todelete", app.AppId)
	var srcRedisMasters []*RedisMaster
	var dstRedisMasters []*RedisMaster

	for _, cluster := range app.Clusters {
		if cluster.ClusterId == clusterIDToDelete {
			for _, node := range cluster.Nodes {
				if node.Role == x1model.RoleTypeMaster {
					srcRedisMasters = append(srcRedisMasters, &RedisMaster{
						IP:   node.FloatingIP,
						Port: node.Port,
					})
					break
				}
			}
		} else {
			for _, node := range cluster.Nodes {
				if node.Role == x1model.RoleTypeMaster {
					dstRedisMasters = append(dstRedisMasters, &RedisMaster{
						IP:   node.FloatingIP,
						Port: node.Port,
					})
					break
				}
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "Get node succ", logit.String("srcRedisMasters", base_utils.Format(srcRedisMasters)),
		logit.String("dstRedisMasters", base_utils.Format(dstRedisMasters)))

	if len(srcRedisMasters) == 0 || len(dstRedisMasters) == 0 {
		resource.LoggerTask.Warning(ctx, "srcRedisMasters or dstRedisMasters length is 0")
		err = errors.New("srcRedisMasters or dstRedisMasters length is 0")
		return cerrs.ErrorTaskManual.Wrap(err)
	}

	// 4 Get Keyspace
	srcDBMap, err := getAppKeyspaceInfo(ctx, srcRedisMasters, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get srcDBMap fail", logit.Error("err", err))
		return err
	}

	// cluster redis not have password
	dstDBMap, err := getAppKeyspaceInfo(ctx, dstRedisMasters, "")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get dstDBMap fail", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "Get app keyspaceInfo succ", logit.String("srcDBMap", base_utils.Format(srcDBMap)),
		logit.String("dstDbMap", base_utils.Format(dstDBMap)))

	// 5 Check Keyspace
	dbsConsistent, keysWithinTolerance := areMapsConsistent(srcDBMap, dstDBMap)
	if !dbsConsistent {
		resource.LoggerTask.Warning(ctx, "db not match", logit.Error("err", err))
		err = errors.New("db not match")
		return err
	}
	if !keysWithinTolerance {
		resource.LoggerTask.Warning(ctx, "keys whitout expire not match", logit.Error("err", err))
		err = errors.New("keys whitout expire not match")
		return err
	}

	return nil
}
