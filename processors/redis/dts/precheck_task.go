/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/10, by wang<PERSON><PERSON>(<EMAIL>), create
*/
package dts

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dts"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessPrecheckTask 处理预检查任务，包括获取应用信息和调用预检查接口
// ctx context.Context 上下文对象
// teu *workflow.TaskExecUnit TaskExecUnit指针，不能为nil
// 返回值 error 如果为nil表示预检查成功，否则表示预检查失败
func ProcessPrecheckTask(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("Dts PrecheckTask fail : teu is nilptr")
	}

	// app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	// TODO: check 原集群 db 个数是否与 proxy db 个数一致

	// precheck
	precheckTaskParam := dts.PrecheckTaskParams{
		AppID: app.AppId,
	}
	err = dts.Instance().PrecheckTask(ctx, &precheckTaskParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "precheck dts task fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}
	return nil
}
