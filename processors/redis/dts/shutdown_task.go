/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/10, by wang<PERSON><PERSON>(<EMAIL>), create
*/
package dts

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dts"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessShutdownTask 处理关闭任务的函数，参数为context.Context和*workflow.TaskExecUnit类型，返回值为error类型
func ProcessShutdownTask(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("Dts CreateTask fail : teu is nilptr")
	}

	// app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	// TODO: check sync
	// TODO: 断开原主上连接

	// shutdown
	shutdownTaskParam := dts.ShutdownTaskParams{
		AppID: app.AppId,
	}
	err = dts.Instance().ShutdownTask(ctx, &shutdownTaskParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "shutdown dts task fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}
	return nil
}
