/* Copyright 2024 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2024/04/10, by wang<PERSON><PERSON>(<EMAIL>), create
*/
package dts

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dts"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessCreateTask 创建任务，包括获取应用信息、密码解密、dts源和目标节点信息，然后调用dts接口创建任务
// ctx context.Context 上下文对象
// teu *workflow.TaskExecUnit 任务执行单元，包含实体id等信息
// 返回值 error 错误信息，如果为nil表示创建成功
func ProcessCreateTask(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("Dts CreateTask fail : teu is nilptr")
	}

	// app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	// password(buildMeta 时会清理 RedisACL 中的记录，故从 ProxyACL 中获取密码)
	defaultACL, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusInUse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get defaultACL fail", logit.Error("err", err))
		return err
	}

	var password string
	if len(defaultACL) > 0 && defaultACL[0].Password != "" {
		password, _ = crypto_utils.DecryptKey(defaultACL[0].Password)
	}

	// dts src
	srcRedisIP := ""
	srcRedisPort := 0
	for _, cluster := range app.Clusters {
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			for _, node := range cluster.Nodes {
				node.ClusterId = node.ClusterId + "_" + "todelete"
				node.Status = x1model.NodeOrProxyStatusToFakeDelete
				if node.Role == x1model.RoleTypeSlave {
					srcRedisIP = node.FloatingIP
					srcRedisPort = node.Port
					break
				}
			}
			break
		}
	}

	// dts dst
	dstProxyIP := ""
	dstProxyPort := 0
	for _, itf := range app.Interfaces {
		itf := itf
		for _, proxy := range itf.Proxys {
			proxy := proxy
			dstProxyIP = proxy.FloatingIP
			dstProxyPort = proxy.Port
		}
	}

	if srcRedisIP == "" || dstProxyIP == "" {
		resource.LoggerTask.Warning(ctx, "srcRedisIP or dstProxyIP empty ", logit.String("appId", teu.Entity))
		return errors.Errorf("srcRedisIP or dstProxyIP empty")
	}

	// create
	createTaskParam := dts.CreateTaskParams{
		AppID:            app.AppId,
		SrcRedisIP:       srcRedisIP,
		SrcRedisPort:     srcRedisPort,
		SrcRedisPassword: password,
		DstRedisIP:       dstProxyIP,
		DstRedisPort:     dstProxyPort,
		DstRedisPassword: password,
	}
	err = dts.Instance().CreateTask(ctx, &createTaskParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create dts task fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}
	return nil
}
