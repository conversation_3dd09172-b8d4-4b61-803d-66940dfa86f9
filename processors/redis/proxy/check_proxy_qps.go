/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2023/07/10, by wangbin34
*/

/*
DESCRIPTION
检查 Proxy 流量
*/

package proxy

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"time"

	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type Stats struct {
	ProxyReadQPS  int    `json:"proxy_read_qps"`
	ProxyWriteQPS int    `json:"proxy_write_qps"`
	ProxyQPS      int    `json:"proxy_qps"`
	Version       string `json:"version"`
}

func fetchStats(ctx context.Context, addr string) (*Stats, error) {
	conn, err := net.Dial("tcp", addr)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	recvData := []byte{}
	for {
		// set SetReadDeadline
		err := conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		if err != nil {
			// do something else, for example create new conn
			return nil, err
		}

		recvBuf := make([]byte, 1024)
		n, err := conn.Read(recvBuf[:]) // recv data
		if err != nil && err != io.EOF {
			return nil, err
		}
		if n == 0 {
			break
		}
		recvData = append(recvData, recvBuf[:n]...)
	}

	var stats Stats
	err = json.Unmarshal(recvData, &stats)
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

func ProcessCheckProxyQPS(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	toCheckProxys := make([]string, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				toCheckProxys = append(toCheckProxys, fmt.Sprintf("%s:%d", proxy.FloatingIP, proxy.StatPort))
			}
		}
	}

	resource.LoggerTask.Notice(ctx, "start check proxy qps", logit.AutoField("proxys", toCheckProxys))
	for {
		checkProxyCount := 0
		checkProxyExceptCount := 0
		for _, addr := range toCheckProxys {
			stats, err := fetchStats(ctx, addr)
			if err != nil {
				// 要下线的 Proxy 可能正好宕机，无法获取到 qps
				resource.LoggerTask.Error(ctx, "get proxy stats failed", logit.String("addr", addr), logit.Error("error", err))
				checkProxyExceptCount++
				continue
			}

			if stats.ProxyQPS < 10 {
				checkProxyCount++
			}
		}

		// 正常退出, 所有 Proxy 均检查 qps 符合预期
		if checkProxyCount == len(toCheckProxys) {
			return nil
		}

		// 降级退出
		if enableDowngrade {
			if checkProxyCount > 0 && (checkProxyCount+checkProxyExceptCount) == len(toCheckProxys) {
				resource.LoggerTask.Warning(ctx, "check proxy enable downgrade",
					logit.Int("check_proxy_count", checkProxyCount),
					logit.Int("check_proxy_except_count", checkProxyExceptCount))
				return nil
			}
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}
