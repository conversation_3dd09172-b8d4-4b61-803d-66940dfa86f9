/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessRollbackBccResourcesMeta process rollback bcc resouce and meta
func ProcessRollbackMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	var toDeleteResources []string
	var toDeleteNodes []*x1model.Node
	var toDeleteProxies []*x1model.Proxy
	var toDeleteRoNodes []*x1model.RoNode
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				toDeleteNodes = append(toDeleteNodes, node)
				if len(node.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, node.ResourceId)
				}
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				toDeleteRoNodes = append(toDeleteRoNodes, node)
				if len(node.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, node.ResourceId)
				}
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate {
				toDeleteProxies = append(toDeleteProxies, proxy)
				if len(proxy.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, proxy.ResourceId)
				}
			}
		}
	}

	if len(toDeleteResources) > 0 {
		resource.LoggerTask.Notice(ctx, "to rollback resource", logit.String("nodes", base_utils.Format(toDeleteResources)))
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toDeleteResources,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "ready",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := x1model.NodeDeleteMulti(ctx, toDeleteNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteProxies) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("proxies", base_utils.Format(toDeleteProxies)))
		if err := x1model.ProxyDeleteMulti(ctx, toDeleteProxies); err != nil {
			resource.LoggerTask.Error(ctx, "delete proxy meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteRoNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteRoNodes)))
		if err := x1model.RoNodeDeleteMulti(ctx, toDeleteRoNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	if err := rollbackClusterAndInterface(ctx, app); err != nil {
		return err
	}

	return nil
}

func rollbackClusterAndInterface(ctx context.Context, app *x1model.Application) error {
	var toDeleteClusters []*x1model.Cluster
	var toDeleteInterfaces []*x1model.Interface
	for _, cluster := range app.Clusters {
		isEmpty := true
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			toDeleteClusters = append(toDeleteClusters, cluster)
		}
	}
	if len(toDeleteClusters) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("clusters", base_utils.Format(toDeleteClusters)))
		if err := x1model.ClusterDeleteMulti(ctx, toDeleteClusters); err != nil {
			resource.LoggerTask.Error(ctx, "delete cluster meta failed", logit.Error("error", err))
			return err
		}
	}

	for _, itf := range app.Interfaces {
		isEmpty := true
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			toDeleteInterfaces = append(toDeleteInterfaces, itf)
		}
	}
	if len(toDeleteInterfaces) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("interfaces", base_utils.Format(toDeleteInterfaces)))
		if err := x1model.InterfaceDeleteMulti(ctx, toDeleteInterfaces); err != nil {
			resource.LoggerTask.Error(ctx, "delete interface meta failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}
