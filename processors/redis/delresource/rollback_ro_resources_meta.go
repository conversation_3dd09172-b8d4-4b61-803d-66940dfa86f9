/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessRollbackReadonlyResourcesMeta process rollback bcc resouce and meta
func ProcessRollbackReadonlyResourcesMeta(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	toDeleteResources := []string{}
	toDeleteRoNodes := []*x1model.RoNode{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate {
				toDeleteRoNodes = append(toDeleteRoNodes, node)
				if len(node.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, node.ResourceId)
				}
			}
		}
	}

	if len(toDeleteResources) > 0 {
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toDeleteResources,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "ready",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteRoNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteRoNodes)))
		if err := x1model.RoNodeDeleteMulti(ctx, toDeleteRoNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete ro node meta failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}
