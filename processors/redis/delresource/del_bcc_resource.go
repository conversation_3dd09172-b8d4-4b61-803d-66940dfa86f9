package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/x1resource"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessDelBccResources 删除bcc资源
// 1. 遍历所有node、proxy，获取所有bcc
// 2. 调用bcc接口删除资源
func ProcessDelBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	delBccReq := &bccresource.DeleteBccResourceParams{
		UserID:    app.UserId,
		IsShortID: false,
	}
	delInstanceReq := &x1resource.DeleteInstanceParams{
		UserID:   app.UserId,
		X1TaskID: teu.TaskID,
	}
	toDeleteResources := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if len(node.ResourceId) != 0 {
				delBccReq.InstanceIds = append(delBccReq.InstanceIds, node.ResourceId)
				delInstanceReq.InstanceIds = append(delInstanceReq.InstanceIds, node.ResourceId)
				toDeleteResources = append(toDeleteResources, node.ResourceId)
			}
		}
		for _, node := range cluster.RoNodes {
			if len(node.ResourceId) != 0 {
				delBccReq.InstanceIds = append(delBccReq.InstanceIds, node.ResourceId)
				delInstanceReq.InstanceIds = append(delInstanceReq.InstanceIds, node.ResourceId)
				toDeleteResources = append(toDeleteResources, node.ResourceId)
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if len(proxy.ResourceId) != 0 {
				delBccReq.InstanceIds = append(delBccReq.InstanceIds, proxy.ResourceId)
				delInstanceReq.InstanceIds = append(delInstanceReq.InstanceIds, proxy.ResourceId)
				toDeleteResources = append(toDeleteResources, proxy.ResourceId)
			}
		}
	}

	// 集群仍在 csmaster 场景，集群释放时，会产生 scs-delete-all-instance-app workflow,
	// 调用 delete_instance_model 时，会返回 "Resource is not exist." 错误
	// 故而仅切到 x1-api 的时候，通过 to_delete 表进行清理
	if app.IsAPIFlagSet(x1model.APIFlagAll) {
		if len(toDeleteResources) > 0 {
			resource.LoggerTask.Notice(ctx, "to delete csmaster meta , status is ready", logit.String("nodes", base_utils.Format(toDeleteResources)))
			if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
				IDs:             toDeleteResources,
				UserID:          app.UserId,
				AppID:           app.AppId,
				StatusForDelete: "ready",
			}); err != nil {
				resource.LoggerTask.Error(ctx, "csmaster callback todelete resource failed", logit.Error("error", err))
				return err
			}
		}
	} else {
		if app.ResourceType == "container" {
			resource.LoggerTask.Notice(ctx, "send delinstance", logit.String("raw req", base_utils.Format(delInstanceReq)))
			err = x1resource.Instance().DeleteInstances(ctx, delInstanceReq)
		} else {
			resource.LoggerTask.Notice(ctx, "send delbcc", logit.String("raw params", base_utils.Format(delBccReq)))
			err = bccresource.BccResourceOp().DeleteBccResource(ctx, delBccReq)
		}
		if err != nil {
			resource.LoggerTask.Error(ctx, "delete resource failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}

// ProcessDelBccResourcesForRo 删除只读实例资源
func ProcessDelBccResourcesForRo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	delBccReq := &bccresource.DeleteBccResourceParams{
		UserID:    app.UserId,
		IsShortID: false,
	}
	delInstanceReq := &x1resource.DeleteInstanceParams{
		UserID:   app.UserId,
		X1TaskID: teu.TaskID,
	}
	toDeleteResources := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.RoGroupStatus == x1model.RoInstDeleting {
				if len(node.ResourceId) != 0 {
					delBccReq.InstanceIds = append(delBccReq.InstanceIds, node.ResourceId)
					delInstanceReq.InstanceIds = append(delInstanceReq.InstanceIds, node.ResourceId)
					toDeleteResources = append(toDeleteResources, node.ResourceId)
				}
			}
		}
	}

	// 集群仍在 csmaster 场景，集群释放时，会产生 scs-delete-all-ro-group-app workflow,
	// 调用 delete_instance_model 时，会返回 "Resource is not exist." 错误
	// 故而仅切到 x1-api 的时候，通过 to_delete 表进行清理
	if app.IsAPIFlagSet(x1model.APIFlagAll) {
		if len(toDeleteResources) > 0 {
			resource.LoggerTask.Notice(ctx, "to delete csmaster meta , status is ready", logit.String("nodes", base_utils.Format(toDeleteResources)))
			if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
				IDs:             toDeleteResources,
				UserID:          app.UserId,
				AppID:           app.AppId,
				StatusForDelete: "ready",
			}); err != nil {
				resource.LoggerTask.Error(ctx, "csmaster callback todelete resource failed", logit.Error("error", err))
				return err
			}
		}
	} else {
		if app.ResourceType == "container" {
			err = x1resource.Instance().DeleteInstances(ctx, delInstanceReq)
		} else {
			resource.LoggerTask.Notice(ctx, "send del bcc", logit.String("raw params", base_utils.Format(delBccReq)))
			err = bccresource.BccResourceOp().DeleteBccResource(ctx, delBccReq)
		}
		if err != nil {
			resource.LoggerTask.Error(ctx, "delete bcc resource failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}
