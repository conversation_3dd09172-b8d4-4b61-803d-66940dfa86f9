/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/17 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_todelete_readonly_nodes.go
 * <AUTHOR>
 * @date 2022/05/17 20:54:19
 * @brief delete readonly instance
 *
 **/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessDeleteToDeleteRoNodes process node which need delete
func ProcessDeleteToDeleteRoNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	toDeleteCsmasterMeta := []string{}
	toDeleteNodes := []x1model.RoNode{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.RoGroupStatus == x1model.RoInstDeleting {
				toDeleteNodes = append(toDeleteNodes, *node)
				if len(node.ResourceId) != 0 {
					toDeleteCsmasterMeta = append(toDeleteCsmasterMeta, node.ResourceId)
				}
			}
		}
	}

	if len(toDeleteCsmasterMeta) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete csmaster meta", logit.String("nodes", base_utils.Format(toDeleteCsmasterMeta)))
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toDeleteCsmasterMeta,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "ready",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := x1model.RoNodeDeleteMulti(ctx, toDeleteNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}

// ProcessDeleteAllRoNodes process node which need delete
func ProcessDeleteAllRoNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	toDeleteNodes := []x1model.RoNode{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			toDeleteNodes = append(toDeleteNodes, *node)
		}
	}

	if len(toDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := x1model.RoNodeDeleteMulti(ctx, toDeleteNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}
