/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessDeleteToDeleteNodes process node which need delete
func ProcessDeleteToDeleteNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	toDeleteResources := []string{}
	toFakeDeleteResources := []string{}

	toDeleteNodes := []*x1model.Node{}
	toDeleteProxies := []*x1model.Proxy{}
	toDeleteRoNodes := []*x1model.RoNode{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete {
				toDeleteNodes = append(toDeleteNodes, node)
				if len(node.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, node.ResourceId)
				}
			} else if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toDeleteNodes = append(toDeleteNodes, node)
				if len(node.ResourceId) != 0 {
					toFakeDeleteResources = append(toFakeDeleteResources, node.ResourceId)
				}
			}
		}
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToDelete {
				toDeleteRoNodes = append(toDeleteRoNodes, node)
				if len(node.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, node.ResourceId)
				}
			} else if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toDeleteRoNodes = append(toDeleteRoNodes, node)
				if len(node.ResourceId) != 0 {
					toFakeDeleteResources = append(toFakeDeleteResources, node.ResourceId)
				}
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToDelete {
				toDeleteProxies = append(toDeleteProxies, proxy)
				if len(proxy.ResourceId) != 0 {
					toDeleteResources = append(toDeleteResources, proxy.ResourceId)
				}
			} else if proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toDeleteProxies = append(toDeleteProxies, proxy)
				if len(proxy.ResourceId) != 0 {
					toFakeDeleteResources = append(toFakeDeleteResources, proxy.ResourceId)
				}
			}
		}
	}

	// 立即删除节点
	if len(toDeleteResources) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete csmaster meta , status is ready", logit.String("nodes", base_utils.Format(toDeleteResources)))
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toDeleteResources,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "ready",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback todelete resource failed", logit.Error("error", err))
			return err
		}
	}

	// 延迟删除节点
	if len(toFakeDeleteResources) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete csmaster meta , status is waiting", logit.String("nodes", base_utils.Format(toFakeDeleteResources)))
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toFakeDeleteResources,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "waiting",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback tofakedelete resource failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := x1model.NodeDeleteMulti(ctx, toDeleteNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteRoNodes) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("nodes", base_utils.Format(toDeleteNodes)))
		if err := x1model.RoNodeDeleteMulti(ctx, toDeleteRoNodes); err != nil {
			resource.LoggerTask.Error(ctx, "delete node meta failed", logit.Error("error", err))
			return err
		}
	}

	if len(toDeleteProxies) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("proxies", base_utils.Format(toDeleteProxies)))
		if err := x1model.ProxyDeleteMulti(ctx, toDeleteProxies); err != nil {
			resource.LoggerTask.Error(ctx, "delete proxy meta failed", logit.Error("error", err))
			return err
		}
	}

	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	toDeleteCluster := []*x1model.Cluster{}
	for _, cluster := range app.Clusters {
		if len(cluster.Nodes) == 0 {
			toDeleteCluster = append(toDeleteCluster, cluster)
		}
	}
	if len(toDeleteCluster) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("clusters", base_utils.Format(toDeleteCluster)))
		if err := x1model.ClusterDeleteMulti(ctx, toDeleteCluster); err != nil {
			resource.LoggerTask.Error(ctx, "delete cluster meta failed", logit.Error("error", err))
			return err
		}
	}

	toDeleteItf := []*x1model.Interface{}
	for _, itf := range app.Interfaces {
		if len(itf.Proxys) == 0 {
			toDeleteItf = append(toDeleteItf, itf)
		}
	}
	if len(toDeleteItf) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete from x1model", logit.String("interfaces", base_utils.Format(toDeleteItf)))
		if err := x1model.InterfaceDeleteMulti(ctx, toDeleteItf); err != nil {
			resource.LoggerTask.Error(ctx, "delete cluster meta failed", logit.Error("error", err))
			return err
		}
	}

	return nil
}
