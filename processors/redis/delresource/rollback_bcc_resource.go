/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
释放bcc资源
*/

package delresource

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessRollbackBccResources 申请bcc资源
// 1. 遍历所有node、proxy，获取需要删除的bcc
// 2. 调用bcc接口删除资源
func ProcessRollbackBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	toDeleteResources := []string{}
	inUseMap := make(map[string]bool)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				inUseMap[node.ResourceOrderId] = true
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				inUseMap[proxy.ResourceOrderId] = true
			}
		}
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate && len(node.ResourceId) != 0 {
				if _, has := inUseMap[node.ResourceOrderId]; !has {
					if len(node.ResourceId) != 0 {
						toDeleteResources = append(toDeleteResources, node.ResourceId)
					}
				}
			}
		}
	}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToCreate && len(proxy.ResourceId) != 0 {
				if _, has := inUseMap[proxy.ResourceOrderId]; !has {
					if len(proxy.ResourceId) != 0 {
						toDeleteResources = append(toDeleteResources, proxy.ResourceId)
					}
				}
			}
		}
	}

	// 立即删除节点
	if len(toDeleteResources) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete csmaster meta , status is ready", logit.String("nodes", base_utils.Format(toDeleteResources)))
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toDeleteResources,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "ready",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback todelete resource failed", logit.Error("error", err))
			return err
		}
	}
	// 若是部分成功，但是任务需要回滚时，需要进行清理下
	if err := bccresource.BccResourceOp().SendRollbackTask(ctx, &bccresource.RollbackBccResourceParams{
		TaskID: teu.TaskID,
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "send rollback bcc resource task failed", logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessRollbackRoBccResources 删除只读实例的bcc资源
func ProcessRollbackRoBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	toDeleteResources := []string{}
	inUseMap := make(map[string]bool)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				inUseMap[node.ResourceOrderId] = true
			}
		}
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToCreate && len(node.ResourceId) != 0 {
				if _, has := inUseMap[node.ResourceOrderId]; !has {
					if len(node.ResourceId) != 0 {
						toDeleteResources = append(toDeleteResources, node.ResourceId)
					}
				}
			}
		}
	}

	// 立即删除节点
	if len(toDeleteResources) > 0 {
		resource.LoggerTask.Notice(ctx, "to delete csmaster meta , status is ready", logit.String("nodes", base_utils.Format(toDeleteResources)))
		if err := csmaster.CsmasterOp().DeleteInstanceModels(ctx, &csmaster.DeleteInstanceParams{
			IDs:             toDeleteResources,
			UserID:          app.UserId,
			AppID:           app.AppId,
			StatusForDelete: "ready",
		}); err != nil {
			resource.LoggerTask.Error(ctx, "csmaster callback todelete resource failed", logit.Error("error", err))
			return err
		}
	}
	return err
}
