/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* tpl.go */
/*
modification history
--------------------
2022/08/08 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package config

// func applyConfTpl(ctx context.Context, app *x1model.Application, confTpl string) error {
//	if len(confTpl) == 0 {
//		resource.LoggerTask.Notice(ctx, "call apply conf tpl with out confTpl")
//		return nil
//	}
//	if err := gmaster.GlobalMasterOp().ApplyTemplate(ctx, &gmaster.ApplyTemplateRequest{
//		UserId:         app.UserId,
//		TemplateShowID: confTpl,
//		CacheClusterList: []*gsdk.ApplyItem{{
//			Region:             env.IDC(),
//			CacheClusterID:     app.AppShortID,
//			CacheClusterShowID: app.AppId,
//		}},
//		RebootType: 2,
//		// 2-立即重启,1-维护时间重启，0-不重启
//	}); err != nil {
//		return errors.Errorf("confTpl:%s,err:%s", confTpl, err.Error())
//	}
//	return nil
// }
