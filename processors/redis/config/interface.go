package config

import (
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
)

const (
	ConfigTypeNodeOnly  = "node_only"
	ConfigTypeProxyOnly = "proxy_only"
	ConfigTypeAll       = "all"
)

type ConfigItem struct {
	Name  string `json:"conf_name"`
	Value string `json:"conf_value"`
}

type UpdateConfigParams struct {
	Meta       *xagent.Meta  `json:"meta"`
	ConfigList []*ConfigItem `json:"conf_list"`
}
