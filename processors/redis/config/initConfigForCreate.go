package config

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	ProxyTimeout            = "scsproxy_timeout"
	ProxyTimeoutConfModule  = 2
	RedisConfModule         = 1
	ProxyTimeoutValue       = "2000"
	ClientIdleTimeout       = "client_idle_timeout"
	ClientIdleTimeoutModule = 2
	ClientIdleTimeoutValue  = "0"
)

func ProcessInitConfigForCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app info error", logit.Error("error", err))
		return err
	}
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model error", logit.Error("error", err))
		return err
	}
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(clusterModel.Id))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get config records error", logit.Error("error", err))
		return err
	}
	if !confAlreadyExist(confRecords, "support_scan") {
		if err := openProxySupportScan(ctx, app); err != nil {
			return err
		}
	}
	if !confAlreadyExist(confRecords, "hashtag_enable") {
		if err := openRedisEnableHashTag(ctx, app); err != nil {
			return err
		}
	}

	pocFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "poc_flag", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get poc flag error", logit.Error("error", err))
		pocFlag = "no"
	}
	if pocFlag != "yes" {
		return nil
	}
	pocProxyTimeout, err := resource.CsmasterOpAgent.GetFlag(ctx,
		"poc_proxy_timeout", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, ProxyTimeoutValue)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get flag error", logit.Error("error", err))
		return err
	}
	pocClientIdleTimeout, err := resource.CsmasterOpAgent.GetFlag(ctx,
		"poc_client_idle_timeout", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, ClientIdleTimeoutValue)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get flag error", logit.Error("error", err))
		return err
	}
	if !confAlreadyExist(confRecords, ProxyTimeout) {
		modifyProxyTimeoutRequest := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   ProxyTimeout,
				ConfModule: ProxyTimeoutConfModule,
				ConfValue:  pocProxyTimeout,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
			From:   "admin",
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyProxyTimeoutRequest); err != nil {
			resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
			return err
		}
	}
	if !confAlreadyExist(confRecords, ClientIdleTimeout) {
		modifyClientIdleTimeoutRequest := csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   ClientIdleTimeout,
				ConfModule: ClientIdleTimeoutModule,
				ConfValue:  pocClientIdleTimeout,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
			From:   "admin",
		}
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyClientIdleTimeoutRequest); err != nil {
			resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func confAlreadyExist(confRecords []*csmaster_model_interface.ConfRecordList, confName string) bool {
	for _, record := range confRecords {
		if record.ConfName == confName {
			return true
		}
	}
	return false
}

// 当前 x1-api 侧创建集群版 Redis or Pega 时，会增加 support_scan 配置
// 标准版升级集群版时，依赖此处更改 proxy 配置
func openProxySupportScan(ctx context.Context, app *x1model.Application) error {
	if app.Type == x1model.AppTypeCluster {
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "support_scan",
				ConfModule: 2,
				ConfValue:  "true",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
			From:   "admin",
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Trace(ctx, "open support scan success")
	}
	return nil
}

func IsNeedOpenHashTag(ctx context.Context, app *x1model.Application) bool {
	if privatecloud.IsPrivateENV() {
		return true
	}
	flagVal, err := resource.CsmasterOpAgent.GetFlag(ctx, ExpFlagUseHashTag,
		map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get refactor exp flag fail", logit.String("userid", app.UserId),
			logit.String("vpcID", app.VpcId), logit.String("flag", ExpFlagUseHashTag), logit.Error("err", err))
		return false
	}
	resource.LoggerTask.Trace(ctx, "get refactor flag success", logit.String("userid", app.UserId),
		logit.String("vpcID", app.VpcId), logit.String("flag", ExpFlagUseHashTag), logit.String("val", flagVal))
	return flagVal == "yes"
}

func openRedisEnableHashTag(ctx context.Context, app *x1model.Application) error {
	if !IsNeedOpenHashTag(ctx, app) {
		resource.LoggerTask.Trace(ctx, "no need hash tag,skip")
		return nil
	}

	if len(app.Clusters) > 0 && app.Clusters[0].Engine == x1model.EngineRedis {
		if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
			ConfItem: &csmaster.ConfItem{
				ConfName:   "hashtag_enable",
				ConfModule: 3,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
			From:   "admin",
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "modify redis enable hash failed", logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Trace(ctx, "open redis enable hash success")
	}
	return nil
}
