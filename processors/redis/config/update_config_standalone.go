package config

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessUpdateConfigStandalone CONFIG SET
func ProcessUpdateConfigStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if app.UseNewAgent == "yes" {
		resource.LoggerTask.Notice(ctx, "use new agent, skip update config")
		return nil
	}

	// 获取default acl
	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}

	// 数据库中获取config list
	configList, err := getConfigList(ctx, app.AppId, x1model.ConfigTypeRedis)
	if err != nil {
		return err
	}

	resource.LoggerTask.Notice(ctx, "begin config update", logit.String("appId", app.AppId))

	// 调用xagent执行acl update
	if err := updateConfigForStandaloneWithXagent(ctx, app, defaultAcl, configList); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update configs",
			logit.String("appId", app.AppId),
			logit.Error("err", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "config updated successfully", logit.String("appId", app.AppId))

	return nil
}

// 通过xagent更新所有节点的configs
func updateConfigForStandaloneWithXagent(ctx context.Context, app *x1model.Application,
	defaultAcl *x1model.RedisAcl, configList []*x1model.Config) error {
	if len(configList) == 0 {
		return nil
	}

	confListParam := make([]*ConfigItem, 0)
	for _, config := range configList {
		if config.Name == "disable_commands" {
			resource.LoggerTask.Trace(ctx, "x1 default  disabled_commands", logit.String(" disabled_commands", config.Value))
			cmdList, err := util.GetDisableCommands(ctx, app.AppId)
			if err != nil {
				return err
			}
			confListParam = append(confListParam, &ConfigItem{
				Name:  config.Name,
				Value: cmdList,
			})
			continue
		}
		confListParam = append(confListParam, &ConfigItem{
			Name:  config.Name,
			Value: config.Value,
		})
	}

	g := gtask.Group{
		Concurrent:    updateConfigConcurrent,
		AllowSomeFail: false,
	}

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete || node.Status == x1model.NodeOrProxyStatusToDelete {
				continue
			}
			g.Go(func() error {
				return updateConfigForOneRedisNodeWithXagent(ctx, node, defaultAcl, confListParam)
			})
		}
	}

	_, err := g.Wait()
	return err
}

// 通过xagent更新一个节点的configs
func updateConfigForOneRedisNodeWithXagent(ctx context.Context, node *x1model.Node,
	defaultAcl *x1model.RedisAcl, confListParam []*ConfigItem) error {
	params := &UpdateConfigParams{
		Meta: &xagent.Meta{
			Engine:        node.Engine,
			EngineVersion: node.EngineVersion,
			Basedir:       node.Basedir,
			Port:          int32(node.Port),
		},
		ConfigList: confListParam,
	}
	if defaultAcl != nil {
		params.Meta.Password = defaultAcl.Password
	}

	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action:     updateConfigActionName,
		Params:     params,
		TimeoutSec: updateConfigTimeoutSec,
	}

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update config fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))
	}

	return err
}
