package config

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/getvars"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/render"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

type UpdateConfigXagentRequest struct {
	Meta                     *xagent.Meta           `json:"meta"`
	PkgsToInstall            []*deploy.PkgToInstall `json:"pkgs_to_install"`
	ConfigList               []*util.ConfigItem     `json:"config_list"`
	AgentRecovers            *getvars.AgentRecovers `json:"agent_recovers"`
	ForceRestart             bool                   `json:"force_restart"`
	OnlyRefreshAgentRecovers bool                   `json:"only_refresh_agent_recovers"`
}

type UpdateConfigCtx struct {
	App                *x1model.Application
	DeployParams       *deploy.DeployNodeOfAllTypeParams
	Entity             string
	EntityType         string
	EntityStatus       string
	Role               string
	EncryptPassword    string
	Password           string
	Engine             string
	EngineVersion      string
	BaseDir            string
	Port               int
	FloatingIP         string
	XagentPort         int
	ConfDefs           []*csmaster_model_interface.UserConfList
	ConfRecords        []*csmaster_model_interface.ConfRecordList
	Action             string
	UpdateConfigParams []*iface.ConfigItem
}

func ProcessUpdateConfigNewForCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if privatecloud.IsPrivateENV() {
		subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
			return err
		}
		if subTasksStatus != taskIface.SubTasksStatusSuccess {
			resource.LoggerTask.Warning(ctx, "sub tasks status is not success", logit.String("status", subTasksStatus))
			return errors.New("sub tasks status is not success")
		}
	}

	return processUpdateConfigNew(ctx, teu, "create")
}

func ProcessUpdateConfigNew(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processUpdateConfigNew(ctx, teu, "update")
}

// ProcessRefreshAgentRecovers 只更新坑下/root/agent/recovers下文件
func ProcessRefreshAgentRecovers(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processUpdateConfigNew(ctx, teu, "refresh_agent_recovers")
}

// processUpdateConfigNew 更新配置，仅支持新代理模式的应用
// ctx 上下文信息
// teu *workflow.TaskExecUnit 任务执行单元，包含了任务相关的信息
// action string 操作类型，可以是 "add"、"modify"、"delete" 中的一种
// 返回值 error 如果出现错误则返回，否则返回nil
func processUpdateConfigNew(ctx context.Context, teu *workflow.TaskExecUnit, action string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}
	if app.UseNewAgent != "yes" {
		resource.LoggerTask.Notice(ctx, "app not use new agent, cannot use new config task",
			logit.String("appId", teu.Entity))
		return nil
	}
	var passwd string
	var encryptPasswd string
	if app.Type == x1model.AppTypeStandalone {
		defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
		if err != nil && !x1model.IsNotFound(err) {
			resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
			return err
		}
		if defaultAcl != nil && defaultAcl.Password != "" {
			encryptPasswd = defaultAcl.Password
			passwd, _ = crypto_utils.DecryptKey(defaultAcl.Password)
		}
	} else {
		defaultAcl, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
			app.AppId, x1model.DefaultACLUser, x1model.ACLStatusInUse)
		if err != nil && !x1model.IsNotFound(err) {
			resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
			return err
		}
		if len(defaultAcl) > 0 && defaultAcl[0].Password != "" {
			encryptPasswd = defaultAcl[0].Password
			passwd, _ = crypto_utils.DecryptKey(defaultAcl[0].Password)
		}
	}
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model fail", logit.Error("error", err))
		return err
	}
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(clusterModel.Id))
	if err != nil {
		return err
	}
	var confDefs []*csmaster_model_interface.UserConfList
	if err := resource.CsmasterModel.GetAllByCond(ctx, &confDefs, "1 = 1"); err != nil {
		return err
	}
	nodeGroup := gtask.Group{}
	for _, cluster := range app.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}
		if action == "update" && confListType(params.ConfigList) == ConfigTypeProxyOnly {
			continue
		}
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			nodeGroup.Go(func() error {
				return updateOneConfig(ctx, &UpdateConfigCtx{
					App:          app,
					DeployParams: deploy.GetNodeDeployParams(app, cluster, node, "", ""),
					Entity:       node.NodeId,
					EntityType:   "node",
					EntityStatus: node.Status,
					Role:         node.Role,
					Password: func() string {
						if app.Type == x1model.AppTypeStandalone {
							return passwd
						}
						return ""
					}(),
					EncryptPassword: func() string {
						if app.Type == x1model.AppTypeStandalone {
							return encryptPasswd
						}
						return ""
					}(),
					Engine:             cluster.Engine,
					EngineVersion:      cluster.EngineVersion,
					BaseDir:            node.Basedir,
					Port:               node.Port,
					FloatingIP:         node.FloatingIP,
					XagentPort:         node.XagentPort,
					ConfDefs:           confDefs,
					ConfRecords:        confRecords,
					Action:             action,
					UpdateConfigParams: params.ConfigList,
				})
			})
		}
	}
	proxyGroup := gtask.Group{
		Concurrent: getProxyCocurrent(app, action),
	}
	for _, itf := range app.Interfaces {
		if action == "update" && confListType(params.ConfigList) == ConfigTypeNodeOnly {
			continue
		}
		itf := itf
		for _, proxy := range itf.Proxys {
			proxy := proxy
			proxyGroup.Go(func() error {
				return updateOneConfig(ctx, &UpdateConfigCtx{
					App:                app,
					DeployParams:       deploy.GetProxyDeployParams(app, itf, proxy),
					Entity:             proxy.ProxyId,
					EntityType:         "proxy",
					EntityStatus:       proxy.Status,
					Password:           passwd,
					EncryptPassword:    encryptPasswd,
					Engine:             itf.Engine,
					EngineVersion:      itf.EngineVersion,
					BaseDir:            proxy.Basedir,
					Port:               proxy.Port,
					FloatingIP:         proxy.FloatingIP,
					XagentPort:         proxy.XagentPort,
					ConfDefs:           confDefs,
					ConfRecords:        confRecords,
					Action:             action,
					UpdateConfigParams: params.ConfigList,
				})
			})
		}
	}
	_, err = nodeGroup.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update redis/pega config fail", logit.Error("err", err))
		return err
	}
	_, err = proxyGroup.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update proxy config fail", logit.Error("err", err))
		return err
	}
	return nil
}

func confListType(confList []*iface.ConfigItem) string {
	if len(confList) != 1 {
		return ConfigTypeAll
	}
	switch confList[0].Type {
	case "redis":
		return ConfigTypeNodeOnly
	case "proxy":
		return ConfigTypeProxyOnly
	default:
		if strings.HasPrefix(confList[0].Name, "syncagent") {
			return ConfigTypeNodeOnly
		}
		return ConfigTypeAll
	}
}

func updateOneConfig(ctx context.Context, ucCtx *UpdateConfigCtx) error {
	// 这里没有传入真正的disable cmds，以及password，以为这个参数只是用来获取packages列表，不是用来进行真正的部署
	ucCtx.DeployParams.NoUpdate = true
	ucCtx.DeployParams.TaskID = "warning-no-expect-update"
	toDeployPkgsRet, err := deploy.GetAndUpdateToDeployPkgs(ctx, ucCtx.DeployParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get to deploy pkgs", logit.Error("err", err))
		return err
	}
	pkgs := toDeployPkgsRet.ToDeployPkgInfos
	getVarsParams, err := getvars.GetParamters(ctx, ucCtx.App.AppId, ucCtx.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get vars", logit.Error("err", err))
		return err
	}
	var pkgToInstall []*deploy.PkgToInstall
	for _, pkg := range pkgs {
		if conf.IsCorePkg(pkg.Name) {
			renderedConfs, err := render.GetRenderdConf(ctx, getVarsParams, pkg.TplID, pkg.FullVersion)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "fail to get rendered conf", logit.Error("err", err))
				return err
			}
			pkgToInstall = append(pkgToInstall, &deploy.PkgToInstall{
				Name:          pkg.Name,
				DeployPath:    ucCtx.BaseDir,
				NoNeedExecute: true,
				RenderedConfs: renderedConfs,
			})
		}
	}
	if len(pkgToInstall) == 0 {
		resource.LoggerTask.Warning(ctx, "no core pkg found")
		return errors.New("no core pkg found")
	}
	confItems, err := util.GetUpdateConfigList(ctx, &util.GetUpdateConfigParams{
		Engine:             ucCtx.Engine,
		EngineVersion:      ucCtx.EngineVersion,
		Role:               ucCtx.Role,
		ConfDefs:           ucCtx.ConfDefs,
		ConfRecords:        ucCtx.ConfRecords,
		UpdateConfigParams: ucCtx.UpdateConfigParams,
		NeedFilter:         ucCtx.Action != "create",
	})
	resource.LoggerTask.Trace(ctx, "get confItems succ",
		logit.String("engine", ucCtx.Engine),
		logit.String("engineVersion", ucCtx.EngineVersion),
		logit.String("entity", ucCtx.Entity),
		logit.String("params", base_utils.Format(ucCtx.UpdateConfigParams)),
		logit.String("confItems", base_utils.Format(confItems)),
		logit.String("confRecords", base_utils.Format(ucCtx.ConfRecords)))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get update config list", logit.Error("err", err))
		return err
	}
	agentRecovers, err := getvars.GetAgentRecover(ctx, getVarsParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get agent recover", logit.Error("err", err))
		return err
	}

	xagentReq := &UpdateConfigXagentRequest{
		Meta: &xagent.Meta{
			Engine:        ucCtx.Engine,
			EngineVersion: ucCtx.EngineVersion,
			Basedir:       ucCtx.BaseDir,
			Port:          int32(ucCtx.Port),
			AccountName:   x1model.DefaultACLUser,
			Password:      ucCtx.EncryptPassword,
		},
		PkgsToInstall:            pkgToInstall,
		ConfigList:               confItems,
		AgentRecovers:            agentRecovers,
		ForceRestart:             ucCtx.Action == "create",
		OnlyRefreshAgentRecovers: ucCtx.Action == "refresh_agent_recovers",
	}
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: ucCtx.FloatingIP,
			Port: int32(ucCtx.XagentPort),
		},
		Action:     updateConfigActionNameNew,
		Params:     xagentReq,
		TimeoutSec: updateConfigTimeoutSec,
	}

	_, err = xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update config fail",
			logit.String("entity", ucCtx.Entity),
			logit.Error("err", err))
	}
	if ucCtx.EntityType == "proxy" && ucCtx.EntityStatus == x1model.NodeOrProxyStatusInUse {
		if err := checkProxyAlive(ctx, ucCtx); err != nil {
			resource.LoggerTask.Warning(ctx, "proxy not alive", logit.String("entity", ucCtx.Entity), logit.Error("err", err))
			return err
		}
	}
	return nil
}

func getProxyCocurrent(app *x1model.Application, action string) int {
	if action == "create" {
		return 0
	}
	r := 0
	for _, itf := range app.Interfaces {
		r += len(itf.Proxys)
	}
	// 每次最多进行20%
	r /= 5
	if r <= 0 {
		r = 1
	}
	return r
}

func checkProxyAlive(ctx context.Context, ucCtx *UpdateConfigCtx) error {
	redisCli := single_redis.NewClient(ucCtx.FloatingIP, ucCtx.Port, single_redis.WithPassword(ucCtx.Password))
	defer redisCli.Close()
	for i := 0; i < 3; i++ {
		_, err := redisCli.Ping(ctx).Result()
		if err == nil {
			return nil
		}
	}
	return fmt.Errorf("proxy %s:%d not alive", ucCtx.FloatingIP, ucCtx.Port)
}

const ExpFlagUseSentinel = "need_use_sentinel"
const ExpFlagUseHashTag = "need_use_hashtag"

func IsNeedInitSenitnelConf(ctx context.Context, app *x1model.Application) bool {
	if privatecloud.IsPrivateENV() {
		return true
	}
	flagVal, err := resource.CsmasterOpAgent.GetFlag(ctx, ExpFlagUseSentinel,
		map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get refactor exp flag fail", logit.String("userid", app.UserId),
			logit.String("vpcID", app.VpcId), logit.String("flag", ExpFlagUseSentinel), logit.Error("err", err))
		return false
	}
	resource.LoggerTask.Trace(ctx, "get refactor flag success", logit.String("userid", app.UserId),
		logit.String("vpcID", app.VpcId), logit.String("flag", ExpFlagUseSentinel), logit.String("val", flagVal))
	return flagVal == "yes"
}

// ProcessHotSetSentinelConf
// ********后版本的标准版支持sentinel命令
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/CyUESrdXXe/6416dc59967b41
func ProcessHotSetSentinelConf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if !IsNeedInitSenitnelConf(ctx, app) {
		return nil
	}

	if app.Type != x1model.AppTypeStandalone ||
		app.Clusters[0].Engine != x1model.EngineRedis {
		resource.LoggerTask.Trace(ctx, "no need sentinel conf")
		return nil
	}

	redisPkg, err := x1model.GetCurrentPackageRecord(ctx, app.AppId, []string{"slot-redis"}, "")
	if err != nil {
		return err
	}
	if len(redisPkg) == 0 {
		return nil
	}

	version := strings.Split(redisPkg[0].FullVersion, ".")
	if len(version) != 4 ||
		!strings.HasPrefix(redisPkg[0].FullVersion, "6.2") ||
		cast.ToInt(version[2]) < 30 {
		resource.LoggerTask.Trace(ctx, "redis version not support sentinel",
			logit.String("verson", redisPkg[0].FullVersion))
		return nil
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get cache cluster fail,err:%w", err)
	}

	entryIP := cacheCluster.ElbPnetip.String
	if cacheCluster.EndpointIp != "" {
		resource.LoggerTask.Trace(ctx, "this is container,use endpoint ip")
		entryIP = cacheCluster.EndpointIp
	}
	if entryIP == "" {
		return errors.New("sentinel entry IP must not empty")
	}

	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "myself-ip",
			ConfModule: RedisConfModule,
			ConfValue:  entryIP,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "modify myself-ip failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "myself-port",
			ConfModule: RedisConfModule,
			ConfValue:  cast.ToString(cacheCluster.BlbListenerPort.Int32),
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "modify myself-port failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "skip-auth-commands",
			ConfModule: RedisConfModule,
			ConfValue:  "ping,sentinel,info,command,hello",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "modify myself-port failed", logit.Error("error", err))
		return err
	}

	return nil
}
