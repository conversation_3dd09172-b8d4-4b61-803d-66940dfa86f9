package config

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func getConfigList(ctx context.Context, appId, configType string) ([]*x1model.Config, error) {
	configList, err := x1model.ConfigAllByCond(ctx, "app_id = ? AND type = ?",
		appId, configType)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get config list fail",
			logit.String("appId", appId),
			logit.Error("err", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}

	return configList, nil
}
