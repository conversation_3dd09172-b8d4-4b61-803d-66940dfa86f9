/* Copyright 2025 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2025/03/28 , by wang<PERSON><PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package config

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	baseCsmaster "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/migrate"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessOpenOpHeader(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 1 检查配置项是否已经是开启状态
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(app.AppShortID))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get config by app short id failed", logit.Error("err", err))
		return err
	}

	isUseOpHeader := false
	isAOFShift := false
	isAOFUseRDBPreamble := false
	for _, record := range confRecords {
		if record.ConfName == "use-op-header" && record.Value == "yes" {
			isUseOpHeader = true
			continue
		}
		if record.ConfName == "aof-shift" && record.Value == "yes" {
			isAOFShift = true
			continue
		}
		if record.ConfName == "aof-use-rdb-preamble" && record.Value == "yes" {
			isAOFUseRDBPreamble = true
			continue
		}
	}

	// 2 修改配置, 开启 use-op-header & aof-shift 配置需要先将 appendonly 关闭
	if !isAOFShift || !isUseOpHeader {
		resource.LoggerTask.Trace(ctx, "opheader is disable", logit.String("appID", app.AppId))
		// 2.1 设置 appendonly 为 no
		modifyReq := baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "appendonly",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
		// 2.2 设置 use-op-header 为 yes
		modifyReq = baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "use-op-header",
				ConfModule: 1,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
		// 2.3 设置 aof-shift 为 yes
		modifyReq = baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "aof-shift",
				ConfModule: 1,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
	}

	// 3 修改 aof-use-rdb-preamble 参数，此参数需要在开启 appendonly 前配置
	// 否则开启 appendonly 后，Redis 会自动 rewrite, rewrite 将 aof 内容写到 base.rdb 文件
	if !isAOFUseRDBPreamble {
		// 设置 aof-use-rdb-preamble 为 yes(7.0 需要从 base.rdb 读取创建时间)
		modifyReq := baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "aof-use-rdb-preamble",
				ConfModule: 1,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
	}

	// 4 check config
	expectConfMap := make(map[string]string)
	expectConfMap["use-op-header"] = "yes"
	expectConfMap["aof-shift"] = "yes"
	expectConfMap["aof-use-rdb-preamble"] = "yes"
	err = checkAllRedisConf(ctx, app, expectConfMap)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check config failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessReopenAppendonly(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 1 检查配置项是否已经是开启状态
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(app.AppShortID))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get config by app short id failed", logit.Error("err", err))
		return err
	}

	isAppendonly := false
	for _, record := range confRecords {
		if record.ConfName == "appendonly" && record.Value == "yes" {
			isAppendonly = true
			continue
		}
	}

	// 2 开启配置
	if !isAppendonly {
		// 设置 appendonly 为 yes
		modifyReq := baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "appendonly",
				ConfModule: 1,
				ConfValue:  "yes",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
	}

	// 3 check config
	expectConfMap := make(map[string]string)
	expectConfMap["appendonly"] = "yes"
	err = checkAllRedisConf(ctx, app, expectConfMap)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check config failed", logit.Error("error", err))
		return err
	}

	return nil
}

func checkAllRedisConf(ctx context.Context, app *x1model.Application, expectConfMap map[string]string) error {
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	g := gtask.Group{
		Concurrent:    10,
		AllowSomeFail: true,
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			g.Go(func() error {
				return checkOneRedisConf(ctx, app, clusterModel, node.NodeId, expectConfMap)
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "g wait failed", logit.Error("error", err))
		return err
	}
	return nil
}
func checkOneRedisConf(ctx context.Context, app *x1model.Application,
	clusterModel *csmaster.CsmasterCluster, entity string,
	expectConfMap map[string]string) error {
	runtimeConfMap, err := migrate.GetRuntimeConf(ctx, app, clusterModel, entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get runtime conf map", logit.Error("err", err))
		return err
	}

	for configName := range expectConfMap {
		if expectConfMap[configName] != runtimeConfMap[configName] {
			resource.LoggerTask.Warning(ctx, "check config value not match", logit.String("configName", configName),
				logit.String("runtimeValue", runtimeConfMap[configName]),
				logit.String("expectValue", expectConfMap[configName]))
			return errors.Errorf("check config value not match")
		}
	}
	return nil
}
