/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package config

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

const (
	SetClusterEntryConfName = "cluster_entry"
	certification           = "fbbef3155aa64306ba4df82721fc039a"
	timeout                 = 5
	taskTimeout             = 120
)

type RedisCmdResp struct {
	Status string `json:"status"` // status
	Resp   string `json:"resp"`   // resp
}

func ModifyClusterEntry(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get cache cluster fail,err:%w", err)
	}

	port := cacheCluster.BlbListenerPort.Int32
	var toExchangeBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			toExchangeBlb = blb
		}
	}

	if toExchangeBlb == nil {
		resource.LoggerTask.Notice(ctx, "no ipv4 blb found, skip")
		return errors.New("no found blb")
	}
	ip := ""
	if toExchangeBlb.EndpointIp != "" {
		resource.LoggerTask.Trace(ctx, "this is container,use endpoint ip")
		ip = toExchangeBlb.EndpointIp
	} else {
		ip = toExchangeBlb.Ovip
	}
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(cacheCluster.Id))
	if err != nil {
		resource.LoggerTask.Error(ctx, "get config by app short id failed", logit.Error("error", err))
		return err
	}

	for _, confRecord := range confRecords {
		if confRecord.ConfName == SetClusterEntryConfName && confRecord.Value == ip+":"+cast.ToString(port) {
			resource.LoggerTask.Trace(ctx, "config is right in db")
			return nil
		}
	}
	modifyReq := csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfModule: 2, // module 1 proxy
			ConfName:   SetClusterEntryConfName,
			ConfValue:  ip + ":" + cast.ToString(port),
		},
		UserID: cacheCluster.UserInfo.IamUserId,
		AppID:  cacheCluster.ClusterShowId,
		From:   "admin",
	}

	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}

	return nil
}

func CheckClusterEntry(ctx context.Context, teu *workflow.TaskExecUnit) error {

	// sleep 1秒,避免查询任务与配置下发任务互斥
	time.Sleep(1 * time.Second)
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get cache cluster fail,err:%w", err)
	}

	port := cacheCluster.BlbListenerPort.Int32
	var toExchangeBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			toExchangeBlb = blb
		}
	}

	if toExchangeBlb == nil {
		resource.LoggerTask.Notice(ctx, "no ipv4 blb found, skip")
		return errors.New("no found blb")
	}
	ip := ""
	if toExchangeBlb.EndpointIp != "" {
		resource.LoggerTask.Trace(ctx, "this is container,use endpoint ip")
		ip = toExchangeBlb.EndpointIp
	} else {
		ip = toExchangeBlb.Ovip
	}

	expectClusterEntry := ip + ":" + cast.ToString(port)

	cmds := []string{"PROXYCONFIG", "GET", SetClusterEntryConfName}
	var passwd string
	defaultAcl, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status = ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusInUse)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	if len(defaultAcl) > 0 && defaultAcl[0].Password != "" {
		passwd, _ = crypto_utils.DecryptKey(defaultAcl[0].Password)
	}

	gg := gtask.Group{Concurrent: 10,
		AllowSomeFail: false}

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: cast.ToInt32(proxy.XagentPort),
				},
				Action: "rediscmd",
				Params: util.RedisCmdParams{
					Host:          "127.0.0.1",
					Port:          int32(proxy.Port),
					Password:      passwd,
					Timeout:       timeout,
					Certification: certification,
					Cmds:          cmds,
				},
				TimeoutSec: taskTimeout,
			}
			gg.Go(func() error {
				rsp, err := xagent.Instance().DoAsync(ctx, req).Wait()
				if err != nil {
					resource.LoggerTask.Warning(ctx, "proxyconfig get fail",
						logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
						logit.Error("err", err))
					return err
				}

				var redisCmdRsp RedisCmdResp
				err = rsp.ParseResult(&redisCmdRsp)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "parse redis cmd rsp err",
						logit.String("redis cmd rsp", base_utils.Format(rsp)),
						logit.Error("err", err))
					return err
				}
				if redisCmdRsp.Status == "success" && redisCmdRsp.Resp == expectClusterEntry {
					return nil
				} else {
					resource.LoggerTask.Warning(ctx, "redis cmd rsp is not expect",
						logit.String("redis cmd rsp", base_utils.Format(rsp)))
					return errors.New("redis cmd rsp is not expect")
				}
			})
		}
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check cluster entry",
			logit.Error("err", err))
		return err
	}
	return nil
}
