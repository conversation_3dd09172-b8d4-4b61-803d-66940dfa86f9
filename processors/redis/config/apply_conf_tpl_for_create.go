/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* apply_conf_tpl_for_create.go */
/*
modification history
--------------------
2022/11/02 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package config

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	gsdk "icode.baidu.com/baidu/scs/x1-base/sdk/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessApplyConfTplForCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if strings.HasPrefix(env.IDC(), "licloud") {
		if err := applyLiCloudTpl(ctx, teu.Entity); err != nil {
			resource.LoggerTask.Warning(ctx, "apply li cloud const tpl fail", logit.Error("err", err))
			return err
		}
		resource.LoggerTask.Trace(ctx, "apply li cloud const tpl success")
	}

	if len(param.ConfTpl) == 0 {
		return nil
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	if err := gmaster.GlobalMasterOp().ApplyTemplate(ctx, &gmaster.ApplyTemplateRequest{
		UserId:         app.UserId,
		TemplateShowID: param.ConfTpl,
		CacheClusterList: []*gsdk.ApplyItem{{
			Region:             env.IDC(),
			CacheClusterID:     app.AppShortID,
			CacheClusterShowID: app.AppId,
		}},
		RebootType: 0,
		// 2-立即重启,1-维护时间重启，0-不重启
	}); err != nil {
		return errors.Errorf("confTpl:%s,err:%s", param.ConfTpl, err.Error())
	}
	return nil
}

func applyLiCloudTpl(ctx context.Context, appID string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	if err := csmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &csmaster.ModifyConfigParam{
		ConfItem: &csmaster.ConfItem{
			ConfName:   "disable_commands",
			ConfModule: 1,
			ConfValue:  "flushall,flushdb,keys",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
		From:   "admin",
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "modify config info failed", logit.Error("error", err))
		return err
	}
	return nil
}
