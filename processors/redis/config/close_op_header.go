/* Copyright 2025 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2025/04/16 , by wang<PERSON><PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package config

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	baseCsmaster "icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func CloseOpHeader(ctx context.Context, appID string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 1 检查实例版本
	if app.Clusters[0].EngineVersion != "6.0" && app.Clusters[0].EngineVersion != "7.0" {
		resource.LoggerTask.Notice(ctx, "EngineVersion not 6.0 & 7.0")
		return nil
	}

	// 2 检查配置项是否已经是关闭状态, 6 & 7 默认是关闭状态
	confRecords, err := resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(app.AppShortID))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get config by app short id failed", logit.Error("err", err))
		return err
	}

	isUseOpHeader := false
	isAOFShift := false
	for _, record := range confRecords {
		if record.ConfName == "use-op-header" && record.Value == "yes" {
			isUseOpHeader = true
			continue
		}
		if record.ConfName == "aof-shift" && record.Value == "yes" {
			isAOFShift = true
			continue
		}
	}

	// 3 修改配置
	if isAOFShift || isUseOpHeader {
		resource.LoggerTask.Trace(ctx, "opheader is enable", logit.String("appID", app.AppId))
		// 3.1 设置 appendonly 为 no
		modifyReq := baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "appendonly",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
		// 3.2 设置 use-op-header 为 no
		modifyReq = baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "use-op-header",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
		// 3.3 设置 aof-shift 为 no
		modifyReq = baseCsmaster.ModifyConfigParam{
			ConfItem: &baseCsmaster.ConfItem{
				ConfName:   "aof-shift",
				ConfModule: 1,
				ConfValue:  "no",
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
			resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
				logit.String("request", base_utils.Format(modifyReq)))
			return err
		}
		time.Sleep(15 * time.Second)
	}

	// 4 check config
	expectConfMap := make(map[string]string)
	expectConfMap["use-op-header"] = "no"
	expectConfMap["aof-shift"] = "no"
	err = checkAllRedisConf(ctx, app, expectConfMap)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check config failed", logit.Error("error", err))
		return err
	}

	// 5 设置 appendonly 为 yes
	modifyReq := baseCsmaster.ModifyConfigParam{
		ConfItem: &baseCsmaster.ConfItem{
			ConfName:   "appendonly",
			ConfModule: 1,
			ConfValue:  "yes",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if err := baseCsmaster.CsmasterOp().CsmasterModifyConfigInfo(ctx, &modifyReq); err != nil {
		resource.LoggerTask.Error(ctx, "modify config info failed", logit.Error("error", err),
			logit.String("request", base_utils.Format(modifyReq)))
		return err
	}
	return nil
}
