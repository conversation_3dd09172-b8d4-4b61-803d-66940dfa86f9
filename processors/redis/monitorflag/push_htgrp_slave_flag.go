package monitorflag

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
)

func ProcessUpdateHTGRPSlaveFlagAll(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}
	if app.HtGrpMonAvailable != 1 {
		resource.LoggerTask.Notice(ctx, "skip push flag for htgrp slave mon", logit.String("appId", app.AppId))
		return nil
	}
	if app.AppGroupID == "" {
		resource.LoggerTask.Notice(ctx, "skip push flag for htgrp slave mon", logit.String("appId", app.AppId))
		return nil
	}
	// 热活从地域推送flag为true
	if !metaserver.IsGlobalLeader(ctx, app) {
		return processUpdateHTGRPSlaveFlag(ctx, teu, app, map[string]string{"*": "yes"})
	}
	return processUpdateHTGRPSlaveFlag(ctx, teu, app, map[string]string{"*": "no"})
}

func ProcessUpdateHTGRPSlaveFlagAllToNo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}
	if app.HtGrpMonAvailable != 1 {
		resource.LoggerTask.Notice(ctx, "skip push flag for htgrp slave mon", logit.String("appId", app.AppId))
		return nil
	}
	if app.AppGroupID == "" {
		resource.LoggerTask.Notice(ctx, "skip push flag for htgrp slave mon", logit.String("appId", app.AppId))
		return nil
	}
	return processUpdateHTGRPSlaveFlag(ctx, teu, app, map[string]string{"*": "no"})
}

func ProcessUpdateHTGRPSlaveFlagForReplaceNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// Agent重构后的场景其实不需要管todelete和tofakedelete的节点
	// 因为这两个类型的节点会停止推送bcm
	// 不过为了兼容老版本，这里还是需要处理一下,只能做到控制不推送1次汇聚数据
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}
	if app.HtGrpMonAvailable != 1 {
		resource.LoggerTask.Notice(ctx, "skip push flag for htgrp slave mon", logit.String("appId", app.AppId))
		return nil
	}
	if app.AppGroupID == "" || metaserver.IsGlobalLeader(ctx, app) {
		resource.LoggerTask.Notice(ctx, "skip push flag for htgrp slave mon", logit.String("appId", app.AppId))
		return nil
	}
	return processUpdateHTGRPSlaveFlag(ctx, teu, app, map[string]string{x1model.NodeOrProxyStatusToCreate: "yes",
		x1model.NodeOrProxyStatusToDelete: "no", x1model.NodeOrProxyStatusToFakeDelete: "no"})
}

func processUpdateHTGRPSlaveFlag(ctx context.Context, teu *workflow.TaskExecUnit,
	app *x1model.Application, statusHTGRPSlaveFlagMap map[string]string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}

	reqList := make([]*xagent.AsyncRequest, 0)
	toFakeDeleteReqList := make([]*xagent.AsyncRequest, 0)

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			_, allFound := statusHTGRPSlaveFlagMap["*"]
			_, statusFound := statusHTGRPSlaveFlagMap[node.Status]
			if !allFound && !statusFound {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "monitor_env_vars_op",
				Params: &XagentRequest{
					Op: "set",
					MonitorEnvVars: []*MonitorEnvVar{{
						Name:  "htgrp_slave_flag",
						Value: getHTGRPSlaveFlag(node.Status, statusHTGRPSlaveFlagMap),
					}},
				},
			}
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toFakeDeleteReqList = append(toFakeDeleteReqList, req)
			} else {
				reqList = append(reqList, req)
			}
		}
		for _, node := range cluster.RoNodes {
			_, allFound := statusHTGRPSlaveFlagMap["*"]
			_, statusFound := statusHTGRPSlaveFlagMap[node.Status]
			if !allFound && !statusFound {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "monitor_env_vars_op",
				Params: &XagentRequest{
					Op: "set",
					MonitorEnvVars: []*MonitorEnvVar{{
						Name:  "htgrp_slave_flag",
						Value: getHTGRPSlaveFlag(node.Status, statusHTGRPSlaveFlagMap),
					}},
				},
			}
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toFakeDeleteReqList = append(toFakeDeleteReqList, req)
			} else {
				reqList = append(reqList, req)
			}
		}
	}
	g := gtask.Group{Concurrent: 25}
	for _, req := range reqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update htgrp slave flag fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = g.Wait()
	if err != nil {
		return err
	}

	// 宕机场景下允许设置失败
	gg := gtask.Group{Concurrent: 25, AllowSomeFail: true}
	for _, req := range toFakeDeleteReqList {
		req := req
		gg.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update push flag fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "to fake delete node update push flag fail, ignore...",
			logit.Error("err", err))
		return nil
	}
	return nil
}

func getHTGRPSlaveFlag(status string, statusHTGRPSlaveFlagMap map[string]string) string {
	if _, found := statusHTGRPSlaveFlagMap[status]; found {
		return statusHTGRPSlaveFlagMap[status]
	}
	if _, found := statusHTGRPSlaveFlagMap["*"]; found {
		return statusHTGRPSlaveFlagMap["*"]
	}
	return "no"
}
