/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package resize

import (
	"context"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/x1resource"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

const (
	ResizeFlag = "resized"
)

// ProcessResizeVm 处理resize任务
// 由于bcc不在支持vm的resize，因此这里的resize只支持container的resize，以及pega节点cds的扩容
// container的resize不会失败，resize失败的节点会标记为【待删除】，同时创建一个新的节点
// pega节点的resize会失败， 如果 conf/scs/resize.conf中，AllowReplaceNodeWhenCdsResizeFails="yes"，
//
//		resize失败的节点会标记为【待删除】，同时创建一个新的节点
//		否则会返回cerr.ErrorTaskManual,任务会进入manual状态
//	 以上为默认情况，小流量pega_cds_resize_allow_fail可以覆盖上述配置
func ProcessResizeVm(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	g := gtask.Group{Concurrent: 25}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			cluster := cluster
			node := node
			sctx, cancelFunc := context.WithTimeout(ctx, time.Duration(conf.ResizeConfIns.ResizeTimeoutInSeconds)*time.Second)
			g.Go(func() error {
				defer cancelFunc()
				return gtask.NoPanic(func() error {
					return resizeNode(sctx, app, cluster, node, teu.TaskID)
				})
			})
		}
		for _, node := range cluster.RoNodes {
			cluster := cluster
			node := node
			sctx, cancelFunc := context.WithTimeout(ctx, time.Duration(conf.ResizeConfIns.ResizeTimeoutInSeconds)*time.Second)
			g.Go(func() error {
				defer cancelFunc()
				return gtask.NoPanic(func() error {
					return resizeRoNode(sctx, app, cluster, node, teu.TaskID)
				})
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resize nodes failed", logit.Error("error", err))
		return err
	}

	updateNodesSwitchableFlags(app)

	if err := updateResizedNodesFlavor(ctx, app); err != nil {
		return err
	}
	if err := util.AddNewNodesForReplacing(ctx, app, util.GetNewNodeForModifyingAction, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new nodes for replcaing failed", logit.Error("error", err))
		return err
	}

	if err := util.AddNewRoNodesForReplacing(ctx, app, util.GetNewNodeForModifyingAction, ""); err != nil {
		resource.LoggerTask.Warning(ctx, "add new ro nodes for replcaing failed", logit.Error("error", err))
		return err
	}

	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func updateNodesSwitchableFlags(app *x1model.Application) {
	for _, cluster := range app.Clusters {
		masterNotResized := false
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				masterNotResized = true
				break
			}
		}
		// 如果主resize成功了, 需要替换的从节点在变配过程中不能作为候选了
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				util.SetNodeSwitchable(node, true)
			}
			if node.Role == x1model.RoleTypeSlave && masterNotResized {
				util.SetNodeSwitchable(node, true)
			}
		}
	}
}

func updateResizedNodesFlavor(ctx context.Context, app *x1model.Application) error {
	creq := &csmaster.SaveInstancesParams{
		AppID:  app.AppId,
		UserID: app.UserId,
	}
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
					Uuid:   node.ResourceId,
					Flavor: int32(cluster.AvailableVolume),
				})
			}
		}
	}
	resource.LoggerTask.Trace(ctx, "update resized nodes flavor", logit.String("creq", base_utils.Format(creq)))
	if len(creq.Models) == 0 {
		return nil
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Error(ctx, "save instances failed", logit.Error("error", err))
		return err
	}
	return nil
}

func resizeContainerNode(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node, taskID string) error {
	x1Request := &x1resource.ResizeInstanceParam{
		InstanceID:           node.ResourceId,
		UserID:               app.UserId,
		TargetCPUCount:       int64(cluster.Cpu),
		TargetMemorySizeInMB: int64(cluster.MemSize * 1024),
		TargetDiskSizeInGB:   int64(cluster.DiskSize),
		Engine:               cluster.Engine,
		X1TaskID:             taskID,
		AZone:                node.Azone,
		NodeType:             cluster.Spec,
	}
	if cluster.DestSpec != "" && cluster.DestSpec != cluster.Spec {
		x1Request.NodeType = cluster.DestSpec
	}

	orderID, err := x1resource.Instance().ResizeInstance(ctx, x1Request)
	if err != nil {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("node %s send resize failed", node.NodeId))
		node.Status = x1model.NodeOrProxyStatusToFakeDelete
		return nil
	}

	resource.LoggerTask.Notice(ctx, fmt.Sprintf("node %s send resize success order_id %s", node.NodeId, orderID))

	for {
		err := x1resource.Instance().ShowResizeInstanceByOrder(ctx, &x1resource.ShowInstanceParams{
			UserID:  app.UserId,
			OrderID: orderID,
		})

		if err != nil {
			switch err {
			case x1resource.ErrInstanceOrderInOperation:
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("order %s in operation, try next", orderID))
			case x1resource.ErrInstanceOrderFailed:
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("order %s is error", orderID), logit.Error("error", err))
				node.Status = x1model.NodeOrProxyStatusToFakeDelete
				return nil
			default:
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("order %s unknown status, try next", orderID), logit.Error("error", err))
			}
			select {
			case <-ctx.Done():
				node.Status = x1model.NodeOrProxyStatusToFakeDelete
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("node %s resize failed order_id %s",
					node.NodeId, orderID), logit.Error("error", err))
				return nil
			case <-time.After(1 * time.Second):
				continue
			}
		} else {
			node.TempFlags = base_utils.FlagAdd(node.TempFlags, ResizeFlag)
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("node %s resize success order_id %s", node.NodeId, orderID))
			break
		}
	}

	return nil
}

func resizeNode(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node, taskID string) error {
	// 标准版热活如果热变配，可能触发全量同步，所以把标准版热活不走热变配
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/p4L1DJjqguDBso
	if app.AppGroupID != "" && app.Type == x1model.AppTypeStandalone {
		node.Status = x1model.NodeOrProxyStatusToFakeDelete
		resource.LoggerTask.Trace(ctx, "standalone group not support resize", logit.String("nodeid", node.NodeId))
		return nil
	}
	if app.ResourceType == "container" {
		return resizeContainerNode(ctx, app, cluster, node, taskID)
	}
	// pega本地盘跳过热变配
	if cluster.Engine == x1model.EnginePegaDB && cluster.StoreType != x1model.StoreTypeLOCALDISK {
		resizePegaCdsFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "resize_pega_cds", map[string]string{"iam_user_id": app.UserId, "vpc_id": app.VpcId}, "no")
		if err != nil {
			resource.LoggerTask.Notice(ctx, "get resize_pega_cds flag failed", logit.Error("error", err))
			resizePegaCdsFlag = "no"
		}
		resource.LoggerTask.Notice(ctx, "get resize_pega_cds flag success", logit.String("flag", resizePegaCdsFlag))
		if resizePegaCdsFlag == "yes" {
			return resizePegaCds(ctx, app, cluster, node)
		}
		node.Status = x1model.NodeOrProxyStatusToFakeDelete
		return nil
	}
	node.Status = x1model.NodeOrProxyStatusToFakeDelete
	return nil
}

func resizePegaCds(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node) error {
	toResizeVolume, err := getToResizeCds(ctx, app, cluster, node)
	if err != nil {
		return err
	}
	if len(toResizeVolume) == 0 {
		node.Status = x1model.NodeOrProxyStatusToFakeDelete
		return nil
	}
	for {
		select {
		case <-ctx.Done():
			// node.Status = x1model.NodeOrProxyStatusToDelete
			return cerrs.ErrorTaskManual.Errorf("resize pega %s cds timeout", node.NodeId)
		case <-time.After(1 * time.Second):
			// pass
		}
		op, err := doResizeCds(ctx, app, cluster, node, toResizeVolume)
		if err != nil {
			return err
		}
		if op == "retry" {
			continue
		}
		node.TempFlags = base_utils.FlagAdd(node.TempFlags, ResizeFlag)
		break
	}
	return nil
}

func doResizeCds(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node, volumeId string) (string, error) {
	volumes, err := bccresource.BccResourceOp().GetBccCdsInfo(ctx, &bccresource.GetBccCdsInfoRequest{
		VmID:      node.ResourceId,
		UserID:    app.UserId,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cds info failed", logit.Error("error", err))
		return "", err
	}
	if len(volumes) == 0 {
		resource.LoggerTask.Warning(ctx, "get cds info failed, no volumes")
		return "", err
	}
	for _, volume := range volumes {
		if volume.Id == volumeId {
			if volume.Status != "InUse" {
				resource.LoggerTask.Notice(ctx, "cds is not in use, try next", logit.String("volume", base_utils.Format(volume)))
				return "retry", nil
			}
			if int64(volume.DiskSizeInGB) < cluster.DiskSize {
				err := bccresource.BccResourceOp().ResizeCds(ctx, &bccresource.ResizeCdsParam{
					VolumeID:           volumeId,
					UserID:             app.UserId,
					TargetDataDiskSize: int(cluster.DiskSize),
				})
				if err != nil {
					resource.LoggerTask.Warning(ctx, "send resize cds failed", logit.Error("error", err))
					return "", cerrs.ErrorTaskManual.Wrap(err)
				}
				resource.LoggerTask.Notice(ctx, "send resize cds success", logit.String("volume", base_utils.Format(volume)))
				return "retry", nil
			}
			if int64(volume.DiskSizeInGB) == cluster.DiskSize {
				resource.LoggerTask.Notice(ctx, "cds is already resize", logit.String("volume", base_utils.Format(volume)))
				return "", nil
			}
			// 理论上不会出现这个问题
			resource.LoggerTask.Warning(ctx, "cds is bigger than cluster", logit.String("volume", base_utils.Format(volume)))
			return "", errors.New("cds is bigger than cluster")
		}
	}
	resource.LoggerTask.Warning(ctx, "cds not found", logit.String("volumeId", volumeId))
	return "", fmt.Errorf("cds not found, cdsId %s", volumeId)
}

func getToResizeCds(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node) (string, error) {
	vmInfo, err := bccresource.BccResourceOp().GetBccVmInfo(ctx, &bccresource.GetBccVmInfoRequest{
		VmID:      node.ResourceId,
		UserID:    app.UserId,
		IsShortID: false,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get vm info failed", logit.Error("error", err))
		return "", err
	}
	resource.LoggerTask.Trace(ctx, "get vm info success", logit.String("vmInfo", base_utils.Format(vmInfo)))
	if int(vmInfo.CpuCount) != cluster.Cpu || int(vmInfo.MemoryCapacityInGB) != cluster.MemSize {
		resource.LoggerTask.Notice(ctx, "cpu or memory chagne, can not use cds resize")
		return "", nil
	}
	for _, volume := range vmInfo.Volumes {
		// 找到数据盘，当前数据盘大于目标数据盘，不可以使用cds resize
		if !volume.IsSystemVolume {
			if int64(volume.DiskSizeInGB) > cluster.DiskSize {
				resource.LoggerTask.Notice(ctx, "disk shrink, can not use cds resize")
				return "", nil
			}
			return volume.VolumeId, nil
		}
	}
	return "", errors.New("can not find data disk")
}

func resizeRoNode(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, roNode *x1model.RoNode, taskID string) error {
	node := util.ChangeRoNode2Node(roNode)
	err := resizeNode(ctx, app, cluster, node, taskID)
	if err != nil {
		return err
	}
	roNode.Status = node.Status
	roNode.TempFlags = node.TempFlags
	return nil
}
