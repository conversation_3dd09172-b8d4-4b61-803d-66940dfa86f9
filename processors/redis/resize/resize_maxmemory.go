/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package resize

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	DefaultBacklogSize int64 = 200 * 1024 * 1024
)

type ResizeXagentRequest struct {
	Meta               *xagent.Meta `json:"meta"`
	TargetMaxmemory    int64        `json:"target_maxmemory"`
	TargetDataDiskSize int64        `json:"target_data_disk_size"`
	TargetMaxDBSize    int64        `json:"target_max_db_size"`
}

func sendResizeToXagent(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node, password string) error {
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: int32(node.XagentPort),
		},
		Action: "resize",
		Params: &ResizeXagentRequest{
			Meta: &xagent.Meta{
				Engine:        node.Engine,
				EngineVersion: node.EngineVersion,
				Basedir:       node.Basedir,
				Port:          int32(node.Port),
				Password:      password,
			},
			TargetMaxmemory:    int64(cluster.AvailableVolume)*1024*1024*1024 + DefaultBacklogSize,
			TargetDataDiskSize: cluster.DiskSize,
			TargetMaxDBSize:    int64(cluster.AvailableVolume),
		},
	}

	resource.LoggerTask.Trace(ctx, "send resize task", logit.String("raw params", base_utils.Format(req)))

	_, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resize maxmemory fail",
			logit.String("nodeId", node.NodeId),
			logit.Error("err", err))
	}
	return err
}

func ProcessResizeMaxmemory(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	defaultAcl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Warning(ctx, "get defaultAcl fail", logit.Error("err", err))
		return err
	}
	password := ""
	if defaultAcl != nil {
		password = defaultAcl.Password
	}

	resource.LoggerTask.Notice(ctx, fmt.Sprintf("app %s ProcessResizeMaxmemory", app.AppId))

	// 为了防止resizeFS时候，cds出现问题导致D住，先resize从，再resize主
	// redis则先设置从的maxmemory，再设置主的也是没有问题的
	g := gtask.Group{}
	if app.Clusters[0].Engine == x1model.EnginePegaDB {
		g = gtask.Group{
			Concurrent: 2,
		}
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if !base_utils.FlagExists(node.TempFlags, ResizeFlag) {
				continue
			}
			if node.Role == x1model.RoleTypeMaster {
				continue
			}
			node := node
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return sendResizeToXagent(ctx, app, cluster, node, password)
				})
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resize slaves maxmemory/max-db-size and resizefs failed", logit.Error("err", err))
		return err
	}

	g = gtask.Group{}
	if app.Clusters[0].Engine == x1model.EnginePegaDB {
		g = gtask.Group{
			Concurrent: 2,
		}
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if !base_utils.FlagExists(node.TempFlags, ResizeFlag) {
				continue
			}
			if node.Role != x1model.RoleTypeMaster {
				continue
			}
			node := node
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return sendResizeToXagent(ctx, app, cluster, node, password)
				})
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resize masters maxmemory/max-db-size and resizefs failed", logit.Error("err", err))
		return err
	}
	return nil
}
