package bcm

import (
	"context"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/bcm"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	// SkipBcmResource TODO 理论上已废弃, 跳过这些逻辑进行测试, 无问题后删除相关逻辑
	SkipBcmResource = true
)

func createBcmAppResource(ctx context.Context, app *x1model.Application, csClusterID int64) error {
	op := bcm.Instance()
	if err := op.CreateAppResource(ctx, &bcm.CreateAppResourceParams{
		UserID:     app.UserId,
		AppID:      app.AppId,
		AppShortID: csClusterID,
		AppType:    app.Type,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm app resource failed",
			logit.String("appId", app.AppId))
		return err
	}
	return nil
}

func deleteBcmAppResource(ctx context.Context, app *x1model.Application) error {
	op := bcm.Instance()
	if err := op.DeleteAppResourceIfExisted(ctx, &bcm.DeleteAppResourceIfExistedParams{
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm app resource failed",
			logit.String("appId", app.AppId))
		return err
	}
	return nil
}

func getToCreateClusterResourceParams(ctx context.Context, app *x1model.Application, csClusterID int64,
	nodeStatus string) ([]*bcm.CreateClusterResourceParams, error) {
	var createClusterParams []*bcm.CreateClusterResourceParams
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != nodeStatus {
				continue
			}
			if node.Role == x1model.RoleTypeMaster {
				createClusterParams = append(createClusterParams, &bcm.CreateClusterResourceParams{
					UserID:     app.UserId,
					AppID:      app.AppId,
					AppShortID: csClusterID,
					AppType:    app.Type,
					HashName:   util.GetHashName(cluster.ClusterId, app.AppName),
					HashID:     util.GetHashID(cluster),
				})
			}
		}
	}
	return createClusterParams, nil
}

func getToCreateNodeResourceParams(ctx context.Context, app *x1model.Application, csClusterID int64,
	nodeStatus string) ([]*bcm.CreateNodeResourceParams, error) {
	var createNodeParams []*bcm.CreateNodeResourceParams
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != nodeStatus {
				continue
			}
			createNodeParams = append(createNodeParams, &bcm.CreateNodeResourceParams{
				UserID:     app.UserId,
				AppID:      app.AppId,
				AppShortID: csClusterID,
				HashID:     util.GetHashID(cluster),
				NodeFixID:  node.NodeFixID,
			})
		}
		for _, node := range cluster.RoNodes {
			if node.Status != nodeStatus {
				continue
			}
			createNodeParams = append(createNodeParams, &bcm.CreateNodeResourceParams{
				UserID:     app.UserId,
				AppID:      app.AppId,
				AppShortID: csClusterID,
				HashID:     util.GetHashID(cluster),
				NodeFixID:  node.NodeFixID,
			})
		}
	}
	if app.Type == x1model.AppTypeCluster {
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxys {
				if proxy.Status != nodeStatus {
					continue
				}
				createNodeParams = append(createNodeParams, &bcm.CreateNodeResourceParams{
					UserID:     app.UserId,
					AppID:      app.AppId,
					AppShortID: csClusterID,
					HashID:     proxy.ResourceId,
					NodeFixID:  proxy.NodeFixID,
				})
			}
		}
	}
	return createNodeParams, nil
}

func createBcmClusterOrNodeResource(ctx context.Context, app *x1model.Application, csClusterID int64, nodeStatus string) error {
	op := bcm.Instance()

	createClusterParams, err := getToCreateClusterResourceParams(ctx, app, csClusterID, nodeStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get create bcm cluster resource params failed",
			logit.Error("error", err))
		return err
	}
	createNodeParams, err := getToCreateNodeResourceParams(ctx, app, csClusterID, nodeStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get create bcm node resource params failed",
			logit.Error("error", err))
		return err
	}

	g := gtask.Group{
		Concurrent: 5,
	}
	for _, item := range createClusterParams {
		createParam := item
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return op.CreateClusterResource(ctx, createParam)
			})
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster resource fail", logit.Error("err", err))
		return err
	}
	gg := gtask.Group{
		Concurrent: 5,
	}
	for _, item := range createNodeParams {
		createParam := item
		gg.Go(func() error {
			return gtask.NoPanic(func() error {
				return op.CreateNodeResource(ctx, createParam)
			})
		})
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster resource fail", logit.Error("err", err))
		return err
	}
	return nil
}

func getToDeleteClusterResourceParams(ctx context.Context, app *x1model.Application, nodeStatus string) ([]*bcm.DeleteClusterResourceIfExistedParams, error) {
	var deleteClusterParams []*bcm.DeleteClusterResourceIfExistedParams

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != nodeStatus {
				continue
			}
			if node.Role == x1model.RoleTypeMaster {
				deleteClusterParams = append(deleteClusterParams, &bcm.DeleteClusterResourceIfExistedParams{
					UserID:   app.UserId,
					AppID:    app.AppId,
					AppType:  app.Type,
					HashName: util.GetHashName(cluster.ClusterId, app.AppName),
				})
			}
		}
	}
	return deleteClusterParams, nil
}

func getToDeleteNodeResourceParams(ctx context.Context, app *x1model.Application, nodeStatus string) (
	[]*bcm.DeleteNodeResourceIfExistedParams, error) {
	var deleteNodeParams []*bcm.DeleteNodeResourceIfExistedParams

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status != nodeStatus {
				continue
			}
			deleteNodeParams = append(deleteNodeParams, &bcm.DeleteNodeResourceIfExistedParams{
				UserID:    app.UserId,
				AppID:     app.AppId,
				NodeFixID: node.NodeFixID,
			})
		}
		for _, node := range cluster.RoNodes {
			if node.Status != nodeStatus {
				continue
			}
			deleteNodeParams = append(deleteNodeParams, &bcm.DeleteNodeResourceIfExistedParams{
				UserID:    app.UserId,
				AppID:     app.AppId,
				NodeFixID: node.NodeFixID,
			})
		}
	}
	if app.Type == x1model.AppTypeCluster {
		for _, cluster := range app.Interfaces {
			for _, proxy := range cluster.Proxys {
				if proxy.Status != nodeStatus {
					continue
				}
				deleteNodeParams = append(deleteNodeParams, &bcm.DeleteNodeResourceIfExistedParams{
					UserID:    app.UserId,
					AppID:     app.AppId,
					NodeFixID: proxy.NodeFixID,
				})
			}
		}
	}
	return deleteNodeParams, nil
}

func deleteBcmClusterOrNodeResource(ctx context.Context, app *x1model.Application, nodeStatus string) error {
	op := bcm.Instance()

	deleteClusterParams, err := getToDeleteClusterResourceParams(ctx, app, nodeStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get delete bcm cluster resource params failed",
			logit.Error("err", err))
		return err
	}
	deleteNodeParams, err := getToDeleteNodeResourceParams(ctx, app, nodeStatus)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get delete bcm node resource params failed",
			logit.Error("err", err))
		return err
	}

	g := gtask.Group{
		Concurrent: 5,
	}
	for _, item := range deleteClusterParams {
		deleteParam := item
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return op.DeleteClusterResourceIfExisted(ctx, deleteParam)
			})
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm cluster resource fail", logit.Error("err", err))
		return err
	}
	gg := gtask.Group{
		Concurrent: 5,
	}
	for _, item := range deleteNodeParams {
		deleteParam := item
		gg.Go(func() error {
			return gtask.NoPanic(func() error {
				return op.DeleteNodeResourceIfExisted(ctx, deleteParam)
			})
		})
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm node resource fail", logit.Error("err", err))
		return err
	}
	return nil
}

// ProcessCreateBcmResourceWithinApp 创建实例时初始化监控对象
func ProcessCreateBcmResourceWithinApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if SkipBcmResource {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster model failed", logit.String("appId", app.AppId))
		return err
	}
	if err := createBcmAppResource(ctx, app, csCluster.Id); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm app resource failed", logit.String("appId", app.AppId))
		return err
	}
	if err := createBcmClusterOrNodeResource(ctx, app, csCluster.Id, x1model.NodeOrProxyStatusToCreate); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	return nil
}

// ProcessBcmResourceWithoutApp 日常变更中创建或删除监控对象
func ProcessBcmResourceWithoutApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if SkipBcmResource {
		return nil
	}
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster model failed", logit.String("appId", app.AppId))
		return err
	}
	if err = createBcmClusterOrNodeResource(ctx, app, csCluster.Id, x1model.NodeOrProxyStatusToCreate); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	// 忽略NodeOrProxyStatusToFakeDelete状态的实例,避免误删除
	if err = deleteBcmClusterOrNodeResource(ctx, app, x1model.NodeOrProxyStatusToDelete); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	return nil
}

// ProcessBcmResourceWithoutApp 日常变更中创建或删除监控对象
func ProcessRollbackBcmResourceWithoutApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if SkipBcmResource {
		return nil
	}
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get csmaster cluster model failed", logit.String("appId", app.AppId))
		return err
	}
	if err = createBcmClusterOrNodeResource(ctx, app, csCluster.Id, x1model.NodeOrProxyStatusToDelete); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	if err = deleteBcmClusterOrNodeResource(ctx, app, x1model.NodeOrProxyStatusToCreate); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	return nil
}

// ProcessRollbackBcmResourceWithinApp 实例创建失败回滚监控对象
func ProcessRollbackBcmResourceWithinApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if SkipBcmResource {
		return nil
	}
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if err := deleteBcmAppResource(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm app resource failed", logit.String("appId", app.AppId))
		return err
	}
	if err = deleteBcmClusterOrNodeResource(ctx, app, x1model.NodeOrProxyStatusToCreate); err != nil {
		resource.LoggerTask.Warning(ctx, "delete bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	return nil
}

// ProcessDeleteBcmResourceWithinApp 实例释放时删除监控对象
func ProcessDeleteBcmResourceWithinApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if SkipBcmResource {
		return nil
	}
	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
		return err
	}

	if err := deleteBcmAppResource(ctx, app); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm app resource failed", logit.String("appId", app.AppId))
		return err
	}
	if err := deleteBcmClusterOrNodeResource(ctx, app, x1model.NodeOrProxyStatusInUse); err != nil {
		resource.LoggerTask.Warning(ctx, "create bcm cluster or node resource failed", logit.String("appId", app.AppId))
		return err
	}
	return nil
}

// func getToCreateSyncGroupClusterResourceParams(ctx context.Context, app *x1model.Application, csClusterID int64,
//	nodeStatus, targetBlbIP string, targetBlbPort int64) ([]*bcm.CreateSyncFlowClusterResourceParams, error) {
//	var createClusterParams []*bcm.CreateSyncFlowClusterResourceParams
//	for _, cluster := range app.Clusters {
//		for _, node := range cluster.Nodes {
//			if node.Status != nodeStatus {
//				continue
//			}
//			if node.Role == x1model.RoleTypeMaster {
//				createClusterParams = append(createClusterParams, &bcm.CreateSyncFlowClusterResourceParams{
//					UserID:     app.UserId,
//					AppID:      app.AppId,
//					AppShortID: csClusterID,
//					TargetBlb:  targetBlbIP,
//					TargetPort: targetBlbPort,
//					AppType:    app.Type,
//					HashName:   util.GetHashName(cluster.ClusterId, app.AppName),
//					HashID:     util.GetHashID(cluster),
//				})
//			}
//		}
//	}
//	return createClusterParams, nil
// }

// func getToDeleteSyncGroupClusterResourceParams(ctx context.Context, app *x1model.Application,
//	nodeStatus, targetBlbIP string, targetBlbPort int64) ([]*bcm.DeleteSyncFlowClusterResourceParams, error) {
//	var deleteClusterParams []*bcm.DeleteSyncFlowClusterResourceParams
//	for _, cluster := range app.Clusters {
//		for _, node := range cluster.Nodes {
//			if node.Status != nodeStatus {
//				continue
//			}
//			if node.Role == x1model.RoleTypeMaster {
//				deleteClusterParams = append(deleteClusterParams, &bcm.DeleteSyncFlowClusterResourceParams{
//					UserID:     app.UserId,
//					AppID:      app.AppId,
//					TargetBlb:  targetBlbIP,
//					TargetPort: targetBlbPort,
//					AppType:    app.Type,
//					HashName:   util.GetHashName(cluster.ClusterId, app.AppName),
//				})
//			}
//		}
//	}
//	return deleteClusterParams, nil
// }

// func createSyncGroupBcmClusterResource(ctx context.Context, app *x1model.Application, csClusterID int64, nodeStatus string,
//	members []*gsdk.SyncGroupMember) error {
//
//	op := bcm.Instance()
//
//	var createClusterParams []*bcm.CreateSyncFlowClusterResourceParams
//	for _, member := range members {
//		//忽略自身集群
//		if member.MemberID == app.AppId {
//			continue
//		}
//		//单个实例退出集群与其他集群变更不互斥，因此需过滤退出状态的集群
//		if member.Status == sync_group_model.StatusDelMember {
//			continue
//		}
//		params, err := getToCreateSyncGroupClusterResourceParams(ctx, app, csClusterID, nodeStatus, member.Addr, member.Port)
//		if err != nil {
//			resource.LoggerTask.Warning(ctx, "get to create bcm cluster resource params failed",
//				logit.Int64("clusterId", csClusterID))
//			return err
//		}
//		createClusterParams = append(createClusterParams, params...)
//	}
//	g := gtask.Group{
//		Concurrent: 5,
//	}
//	for _, item := range createClusterParams {
//		createParam := item
//		g.Go(func() error {
//			return gtask.NoPanic(func() error {
//				return op.CreateSyncFlowClusterResource(ctx, createParam)
//			})
//		})
//	}
//	_, err := g.Wait()
//	if err != nil {
//		resource.LoggerTask.Warning(ctx, "create bcm cluster resource fail", logit.Error("err", err))
//		return err
//	}
//	return nil
// }

// func deleteSyncGroupBcmClusterResource(ctx context.Context, app *x1model.Application, csClusterID int64, nodeStatus string,
//	members []*gsdk.SyncGroupMember) error {
//
//	op := bcm.Instance()
//
//	var deleteClusterParams []*bcm.DeleteSyncFlowClusterResourceParams
//	for _, member := range members {
//		//忽略自身集群
//		if member.MemberID == app.AppId {
//			continue
//		}
//		params, err := getToDeleteSyncGroupClusterResourceParams(ctx, app, nodeStatus, member.Addr, member.Port)
//		if err != nil {
//			resource.LoggerTask.Warning(ctx, "get to delete bcm cluster resource params failed",
//				logit.Int64("clusterId", csClusterID))
//			return err
//		}
//		deleteClusterParams = append(deleteClusterParams, params...)
//	}
//	g := gtask.Group{
//		Concurrent: 5,
//	}
//	for _, item := range deleteClusterParams {
//		deleteParam := item
//		g.Go(func() error {
//			return gtask.NoPanic(func() error {
//				return op.DeleteSyncFlowClusterResourceIfExisted(ctx, deleteParam)
//			})
//		})
//	}
//	_, err := g.Wait()
//	if err != nil {
//		resource.LoggerTask.Warning(ctx, "delete bcm cluster resource fail", logit.Error("err", err))
//		return err
//	}
//	return nil
// }

// ProcessSyncGroupBcmResource 创建 or 删除多活组监控对象
func ProcessSyncGroupBcmResource(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	// if err != nil {
	//	resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
	//	return err
	// }
	//
	// //非多活组无需创建多活组监控对象
	// if len(app.SyncGroupID) == 0 {
	//	resource.LoggerTask.Notice(ctx, "sync group id is empty, skip")
	//	return nil
	// }
	//
	// // gAPI获取异地多活成员
	// stsToken, err := compo_utils.GetGMasterToken(ctx, app.UserId)
	// if err != nil {
	//	return errors.Wrap(err, "get sts token fail")
	// }
	//
	// rsp, err := gsdk.GApiSdk.ListSyncGroupMember(ctx, &gsdk.ListSyncGroupMemberRequest{
	//	Token:           stsToken,
	//	SyncGroupShowID: app.SyncGroupID,
	// })
	// if err != nil {
	//	resource.LoggerTask.Warning(ctx, "get sync group member failed", logit.String("appId", app.AppId))
	//	return err
	// }
	//
	// csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	// if err != nil {
	//	resource.LoggerTask.Warning(ctx, "get csmaster cluster model failed", logit.String("appId", app.AppId))
	//	return err
	// }
	//
	// if err = createSyncGroupBcmClusterResource(ctx, app, csCluster.Id, x1model.NodeOrProxyStatusToCreate, rsp.Members); err != nil {
	//	resource.LoggerTask.Warning(ctx, "create sync group bcm cluster resource failed", logit.String("appId", app.AppId))
	//	return err
	// }
	// if err = deleteSyncGroupBcmClusterResource(ctx, app, csCluster.Id, x1model.NodeOrProxyStatusToDelete, rsp.Members); err != nil {
	//	resource.LoggerTask.Warning(ctx, "delete sync group bcm cluster failed", logit.String("appId", app.AppId))
	//	return err
	// }
	return nil
}

// ProcessRollbackSyncGroupBcmResource 创建 or 删除多活组监控对象
func ProcessRollbackSyncGroupBcmResource(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	// if err != nil {
	//	resource.LoggerTask.Warning(ctx, "get app fail", logit.Error("error", err))
	//	return err
	// }

	// //非多活组无需创建多活组监控对象
	// if len(app.SyncGroupID) == 0 {
	//	return nil
	// }
	//
	// // gAPI获取异地多活成员
	// stsToken, err := compo_utils.GetGMasterToken(ctx, app.UserId)
	// if err != nil {
	//	return errors.Wrap(err, "get sts token fail")
	// }
	//
	// rsp, err := gsdk.GApiSdk.ListSyncGroupMember(ctx, &gsdk.ListSyncGroupMemberRequest{
	//	Token:           stsToken,
	//	SyncGroupShowID: app.SyncGroupID,
	// })
	// if err != nil {
	//	resource.LoggerTask.Warning(ctx, "get sync group member failed", logit.String("appId", app.AppId))
	//	return err
	// }
	//
	// csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	// if err != nil {
	//	resource.LoggerTask.Warning(ctx, "get csmaster cluster model failed", logit.String("appId", app.AppId))
	//	return err
	// }
	// if err = deleteSyncGroupBcmClusterResource(ctx, app, csCluster.Id, x1model.NodeOrProxyStatusToCreate, rsp.Members); err != nil {
	//	resource.LoggerTask.Warning(ctx, "delete sync group bcm cluster failed", logit.String("appId", app.AppId))
	//	return err
	// }
	return nil
}
