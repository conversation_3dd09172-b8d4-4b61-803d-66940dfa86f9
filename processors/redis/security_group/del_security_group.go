package securitygroup

import (
	"context"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// 当前无 workflow 调用此 step
// (1) 安全组 openapi 不支持解绑 BCC 操作
// (2) UnbindVpcSecurityGroups 是请求 scs console 解绑 blb/endpoint 的安全组，当前流程在 csmaster 上
func ProcessUnbindSecurityGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// get app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// unbind vpc security groups
	err = security_group.UnbindVpcSecurityGroups(ctx, &security_group.UnbindVpcSecurityGroupsParams{
		UserId: app.UserId,
		AppId:  app.AppId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "unbind vpc sg fail", logit.String("appId", app.AppId),
			logit.Error("error", err))
		return err
	}

	return nil
}

// (1) 仅删除短 ID 的安全组
// (2) 先删除 BCC，再进行删除安全组，因为 BCC 是异步删除，如果删除安全组失败，则通过旁路进行检查并删除
// (3) 若删除安全组成功，则加个 "_deleted" 后缀
func ProcessDelSecurityGroup(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// get app
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	if len(app.SecurityGroupId) == 0 {
		return nil
	}

	// delete app sg(Ignore errors)
	sgComponent := security_group_openapi.Instance()
	if strings.HasPrefix(app.SecurityGroupId, "g-") && !strings.HasSuffix(app.SecurityGroupId, "_deleted") {
		err = sgComponent.DeleteSecurityGroup(ctx, &security_group_openapi.DeleteSecurityGroupParams{
			UserID:          app.UserId,
			SecurityGroupID: app.SecurityGroupId,
		})
		if err == nil {
			app.SecurityGroupId = app.SecurityGroupId + "_deleted"
		} else {
			resource.LoggerTask.Warning(ctx, "delete sg fail", logit.String("appId", app.AppId), logit.String("SecurityGroupID", app.SecurityGroupId),
				logit.Error("error", err))
		}
	}

	// delete app shard sg(Ignore errors)
	if strings.HasPrefix(app.InternalSecurityGroupId, "g-") && !strings.HasSuffix(app.InternalSecurityGroupId, "_deleted") {
		err = sgComponent.DeleteSecurityGroup(ctx, &security_group_openapi.DeleteSecurityGroupParams{
			UserID:          app.UserId,
			SecurityGroupID: app.InternalSecurityGroupId,
		})
		if err == nil {
			app.InternalSecurityGroupId = app.InternalSecurityGroupId + "_deleted"
		} else {
			resource.LoggerTask.Warning(ctx, "delete sg fail", logit.String("appId", app.AppId), logit.String("InternalSecurityGroupID", app.InternalSecurityGroupId),
				logit.Error("error", err))
		}
	}

	return saveSecurityGroup(ctx, app)
}
