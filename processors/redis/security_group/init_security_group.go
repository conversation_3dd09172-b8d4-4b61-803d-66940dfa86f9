/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
2023/08/05, by wang<PERSON><PERSON>(wang<PERSON><EMAIL>), update
            (1) 容器化集群不创建安全组
            (2) 创建安全组使用 openapi 进行创建
*/

/*
DESCRIPTION
创建虚机安全组、初始化安全组规则
*/

package securitygroup

import (
	"context"
	"errors"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessInitSecurityGroupStandalone 初始化标准版redis的安全组
// 1. 创建安全组
// 2. 调用csmaster的callback API，更新cache_cluster表中的相关字段
// 3. 初始化基本规则p
// 4. 初始化服务端口的规则
// 相关代码在 NeutronComponents::create_sg_rules_port
func initSecurityGroup(ctx context.Context, app *x1model.Application, needInner bool) (err error) {
	isEnableIpv6 := app.IpType == x1model.Ipv6
	sgComponent := security_group_openapi.Instance()

	// 创建入口安全组(标准版给 Redis 用，集群版给 Proxy 用)
	if len(app.SecurityGroupId) == 0 {
		outerPorts := []int32{cast.ToInt32(app.Port)}
		// 集群版需要添加mcpack的端口
		if needInner {
			outerPorts = append(outerPorts, cast.ToInt32(app.McpackPort))
		}
		var securityGroupID string
		if useLegacySecurityGroup(app) {
			securityGroupID, err = security_group.Instance().InitSecurityGroupRules(ctx, &security_group.InitSecurityGroupRulesParams{
				UserID:       app.UserId,
				ServicePorts: nil,
				IsEnableIPV6: isEnableIpv6,
				VpcID:        app.VpcId,
				Ports:        outerPorts,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "init sg component error", logit.Error("error", err))
				return err
			}
		} else {
			securityGroupID, err = sgComponent.InitSecurityGroupRules(ctx, &security_group_openapi.InitSecurityGroupRulesParams{
				UserID:       app.UserId,
				ServicePorts: nil,
				IsEnableIPV6: isEnableIpv6,
				VpcID:        app.VpcId,
				Ports:        outerPorts,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "init sg component error", logit.Error("error", err))
				return err
			}
		}
		if len(securityGroupID) == 0 {
			resource.LoggerTask.Warning(ctx, "securityGroupID is empty")
			return errors.New("securityGroupID is empty")
		}
		app.SecurityGroupId = securityGroupID
	}

	// 创建redis的安全组
	if needInner && len(app.InternalSecurityGroupId) == 0 {
		var securityGroupID string
		if useLegacySecurityGroup(app) {
			securityGroupID, err = security_group.Instance().InitSecurityGroupRules(ctx, &security_group.InitSecurityGroupRulesParams{
				UserID:       app.UserId,
				ServicePorts: nil,
				IsEnableIPV6: isEnableIpv6,
				VpcID:        app.VpcId,
				Ports:        []int32{cast.ToInt32(app.InnerPort)},
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "init sg component error", logit.Error("error", err))
				return err
			}
		} else {
			securityGroupID, err = sgComponent.InitSecurityGroupRules(ctx, &security_group_openapi.InitSecurityGroupRulesParams{
				UserID:       app.UserId,
				ServicePorts: nil,
				IsEnableIPV6: isEnableIpv6,
				VpcID:        app.VpcId,
				Ports:        []int32{cast.ToInt32(app.InnerPort)},
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "init sg component error", logit.Error("error", err))
				return err
			}
		}
		if len(securityGroupID) == 0 {
			resource.LoggerTask.Warning(ctx, "securityGroupID is empty")
			return errors.New("securityGroupID is empty")
		}
		app.InternalSecurityGroupId = securityGroupID
	}

	// 回调csmster
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			SecurityGroupId:      app.SecurityGroupId,
			ShardSecurityGroupId: app.InternalSecurityGroupId,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "callback csmaster for update securityGroupID failed", logit.Error("error", err))
		return err
	}
	return nil
}

// 标准版
func ProcessInitSecurityGroupStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 容器化集群无需创建安全组
	if app.ResourceType == "container" {
		return nil
	}

	if err := initSecurityGroup(ctx, app, false); err != nil {
		return err
	}

	return saveSecurityGroup(ctx, app)
}

// 集群版
func ProcessInitSecurityGroupCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 容器化集群无需创建安全组
	if app.ResourceType == "container" {
		return nil
	}

	needInner := true
	if err := initSecurityGroup(ctx, app, needInner); err != nil {
		return err
	}
	return saveSecurityGroup(ctx, app)
}

func saveSecurityGroup(ctx context.Context, app *x1model.Application) error {
	sgId, innerSgId := app.SecurityGroupId, app.InternalSecurityGroupId
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = x1model.ApplicationGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	app.SecurityGroupId = sgId
	app.InternalSecurityGroupId = innerSgId
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err // todo 细化err
	}
	return nil
}

func useLegacySecurityGroup(app *x1model.Application) bool {
	// 目前只有edge需要使用legacy的安全组
	if common.IsEdgeRegion() {
		return true
	}
	return false
}
