/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/24
 * File: pause.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package securitygroup TODO package function desc
package securitygroup

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessPauseServ 删除安全组，暂停对外服务
func ProcessPauseServ(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("CreateAppDomain fail : teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	// 容器化集群无需操作安全组
	if app.ResourceType == "container" {
		return nil
	}

	if strings.HasPrefix(app.SecurityGroupId, "g-") {
		delParams := security_group_openapi.DeleteSecurityRulesPortParams{
			SecurityGroupID: app.SecurityGroupId,
			UserID:          app.UserId,
			Port:            cast.ToInt32(app.Port),
		}
		err = security_group_openapi.Instance().DeleteSecurityRulesPort(ctx, &delParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "del security group rule fail", logit.String("appId", teu.Entity),
				logit.String("params", fmt.Sprintf("%+v", delParams)), logit.Error("component error", err))
			return err
		}
	} else {
		delParams := security_group.DeleteSecurityRulesPortParams{
			SgId:   app.SecurityGroupId,
			UserId: app.UserId,
			Port:   cast.ToInt32(app.Port),
		}
		err = security_group.Instance().DeleteSecurityRulesPort(ctx, &delParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "del security group rule fail", logit.String("appId", teu.Entity),
				logit.String("params", fmt.Sprintf("%+v", delParams)), logit.Error("component error", err))
			return err
		}
	}

	return nil
}
