/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* update_inner_sg.go */
/*
modification history
--------------------
2022/08/18 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package securitygroup

import (
	"context"
	"encoding/json"
	"strings"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	redisOpIface "icode.baidu.com/baidu/scs/x1-api/httpserver/ifaces/redisop"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type UpdateInnerSecurityParams struct {
	Request *redisOpIface.UpdateInnerSecurityRequest
	AppID   string
	UserID  string
}

func ProcessUpdateInnerSg(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params := &redisOpIface.UpdateInnerSecurityRequest{}
	err := json.Unmarshal([]byte(teu.Parameters), params)
	if err != nil {
		resource.LoggerTask.Error(ctx, "unmarshal request fail", logit.Error("err", err))
		return errors.Errorf("unmarshal request fail,err:%s", err.Error())
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	updateSgParam := UpdateInnerSecurityParams{
		Request: params,
		AppID:   app.AppId,
		UserID:  app.UserId,
	}
	if err := UpdateInnerSecurity(ctx, &updateSgParam, app.ResourceType); err != nil {
		resource.LoggerTask.Warning(ctx, "update security group rules failed", logit.Error("err", err))
		return errors.Errorf("update security group rules failed,err:%s", err.Error())
	}
	return nil
}

func UpdateInnerSecurity(ctx context.Context, params *UpdateInnerSecurityParams, resourceType string) error {
	csmasterClusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, params.UserID, params.AppID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if len(csmasterClusterModel.ShardSecurityGroupId) == 0 {
		if resourceType == "container" {
			resource.LoggerTask.Notice(ctx, "container and no shardSecuritySg,skip", logit.String("appid", params.AppID))
			return nil
		}
		resource.LoggerTask.Warning(ctx, "shard security group id is empty", logit.Error("error", err))
		return err
	}

	if strings.HasPrefix(csmasterClusterModel.ShardSecurityGroupId, "g-") {
		err := security_group_openapi.Instance().ReopenSecurityGroupRules(ctx, &security_group_openapi.ReopenSecurityGroupRulesParams{
			SecurityGroupID: csmasterClusterModel.ShardSecurityGroupId,
			ServicePorts:    params.Request.PortList,
			WhitelistIPs:    params.Request.WhiteIPList,
			UserID:          params.UserID,
			IsEnableIpv6:    len(csmasterClusterModel.ElbIpv6Id) != 0,
		})

		if err != nil {
			resource.LoggerTask.Warning(ctx, "update security group rules failed", logit.Error("error", err))
			return err
		}

	} else {
		err := security_group.Instance().ReopenSecurityGroupRules(ctx, &security_group.ReopenSecurityGroupRulesParams{
			SecurityGroupID: csmasterClusterModel.ShardSecurityGroupId,
			ServicePorts:    params.Request.PortList,
			WhitelistIPs:    params.Request.WhiteIPList,
			UserId:          params.UserID,
			IsEnableIpv6:    len(csmasterClusterModel.ElbIpv6Id) != 0,
		})

		if err != nil {
			resource.LoggerTask.Warning(ctx, "update security group rules failed", logit.Error("error", err))
			return err
		}

	}
	return nil
}
