/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
创建虚机安全组、初始化安全组规则
*/

package securitygroup

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	security_group_openapi "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/security_group"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func processRebuildSecurityGroupStandaloneLocal(ctx context.Context, app *x1model.Application) error {
	if len(app.SecurityGroupId) == 0 && app.ResourceType == "container" {
		resource.LoggerTask.Notice(ctx, "container no need to rebuild", logit.String("appid", app.AppId))
		return nil
	}

	whitelist, err := csmaster.CsmasterOp().GetWhiteIPs(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get whitelist from csmaster fail", logit.String("appId", app.AppId),
			logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "whitelist", logit.String("appId", app.AppId), logit.String("before add node ip", base_utils.Format(whitelist)))

	nodeIpList := x1model.GetIpList(ctx, app)
	for _, ip := range nodeIpList {
		isExist, _ := base_utils.InArray(ip, whitelist)
		if !isExist {
			whitelist = append(whitelist, ip)
		}
	}
	resource.LoggerTask.Notice(ctx, "whitelist", logit.String("appId", app.AppId), logit.String("after add node ip", base_utils.Format(whitelist)))

	if strings.HasPrefix(app.SecurityGroupId, "g-") {
		err = security_group_openapi.Instance().ReopenSecurityGroupRules(ctx, &security_group_openapi.ReopenSecurityGroupRulesParams{
			SecurityGroupID: app.SecurityGroupId,
			ServicePorts:    []int32{cast.ToInt32(app.Port)},
			WhitelistIPs:    whitelist,
			UserID:          app.UserId,
			IsEnableIpv6:    app.IpType == x1model.Ipv6,
		})
	} else {
		err = security_group.Instance().ReopenSecurityGroupRules(ctx, &security_group.ReopenSecurityGroupRulesParams{
			SecurityGroupID: app.SecurityGroupId,
			ServicePorts:    []int32{cast.ToInt32(app.Port)},
			WhitelistIPs:    whitelist,
			UserId:          app.UserId,
			IsEnableIpv6:    app.IpType == x1model.Ipv6,
		})
	}

	if err != nil {
		resource.LoggerTask.Warning(ctx, "reopen sg fail", logit.String("appId", app.AppId),
			logit.Error("error", err))
		return err
	}

	return nil
}

func processRebuildSecurityGroupClusterLocal(ctx context.Context, app *x1model.Application) error {
	if len(app.InternalSecurityGroupId) == 0 && app.ResourceType == "container" {
		resource.LoggerTask.Notice(ctx, "container no need to rebuild", logit.String("appid", app.AppId))
		return nil
	}

	if len(app.InternalSecurityGroupId) == 0 {
		resource.LoggerTask.Notice(ctx, "no InternalSecurityGroupId skip,maybe migrate from csmaster")
		return nil
	}

	ipList := []string{}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			ipList = append(ipList, node.Ip)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			ipList = append(ipList, proxy.Ip)
		}
	}

	if strings.HasPrefix(app.InternalSecurityGroupId, "g-") {
		err := security_group_openapi.Instance().ReopenSecurityGroupRules(ctx, &security_group_openapi.ReopenSecurityGroupRulesParams{
			SecurityGroupID: app.InternalSecurityGroupId,
			ServicePorts:    []int32{cast.ToInt32(app.InnerPort)},
			WhitelistIPs:    ipList,
			UserID:          app.UserId,
			IsEnableIpv6:    app.IpType == x1model.Ipv6,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "reopen sg fail", logit.Error("error", err))
			return err
		}
	} else {
		err := security_group.Instance().ReopenSecurityGroupRules(ctx, &security_group.ReopenSecurityGroupRulesParams{
			SecurityGroupID: app.InternalSecurityGroupId,
			ServicePorts:    []int32{cast.ToInt32(app.InnerPort)},
			WhitelistIPs:    ipList,
			UserId:          app.UserId,
			IsEnableIpv6:    app.IpType == x1model.Ipv6,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "reopen sg fail", logit.Error("error", err))
			return err
		}
	}
	return nil
}

// processRebuildSecurityGroupClusterGlobal
// 【集群版】needDel为true是默认的操作，会把todelete等未来会删除的节点剔除掉
// false则只增加新ip，而不会剔除即将被删除的ip。
//
//	false的使用场景很少，例如热活组变配时，需要在中间过程新老节点同时开放，才需要使用
//	一般而言，使用过false以后，后续流程需要手工再把这里暂时没剔除的ip剔除掉。
func processRebuildSecurityGroupClusterGlobal(ctx context.Context, app *x1model.Application, needDel bool) error {
	if len(app.InternalSecurityGroupId) == 0 && app.ResourceType == "container" {
		resource.LoggerTask.Notice(ctx, "container no need to rebuild", logit.String("appid", app.AppId))
		return nil
	}

	var toAddIpList, toDelIpList []string
	var portList []int32
	portMap := make(map[int32]bool)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate {
				toAddIpList = append(toAddIpList, node.Ip)
			}
			if needDel {
				if node.Status == x1model.NodeOrProxyStatusToDelete || node.Status == x1model.NodeOrProxyStatusToFakeDelete {
					toDelIpList = append(toDelIpList, node.Ip)
				}
			}
			portMap[cast.ToInt32(app.Port)] = true
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusInUse || proxy.Status == x1model.NodeOrProxyStatusToCreate {
				toAddIpList = append(toAddIpList, proxy.Ip)
			}
			if proxy.Status == x1model.NodeOrProxyStatusToDelete || proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toDelIpList = append(toDelIpList, proxy.Ip)
			}
		}
	}
	for port := range portMap {
		portList = append(portList, port)
	}
	if err := gmaster.GlobalMasterOp().UpdateInnerSecurity(ctx, &gmaster.UpdateInnerSecurityParams{
		AppGroupID:  app.AppGroupID,
		AppID:       app.AppId,
		UserID:      app.UserId,
		ToAddIpList: toAddIpList,
		ToDelIpList: toDelIpList,
		PortList:    portList,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update inner security fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessRebuildSecurityGroupCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	// 私有话输出的不设置
	if privatecloud.IsPrivateENV() {
		return nil
	}

	if len(app.AppGroupID) == 0 {
		return processRebuildSecurityGroupClusterLocal(ctx, app)
	}
	return processRebuildSecurityGroupClusterGlobal(ctx, app, true)
}

// ProcessRebuildSecurityGroupClusterForModifySpec
// 【集群版】变配流程专属的安全组重建逻辑
//
//	目的是为了让热活组实例的安全组可以只增不减，新老节点都在安全组内
func ProcessRebuildSecurityGroupClusterForModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	if len(app.AppGroupID) == 0 {
		return processRebuildSecurityGroupClusterLocal(ctx, app)
	}
	return processRebuildSecurityGroupClusterGlobal(ctx, app, false)
}

// ProcessRebuildSecurityGroupResetClusterGlobalSg
// 【集群版】热活组安全组重置流程
//
//	一般对应ProcessRebuildSecurityGroupClusterForModifySpec成对使用
//	因为ProcessRebuildSecurityGroupClusterForModifySpec会导致新老节点都开放
//	这一步会把多余的老ip剔除
func ProcessRebuildSecurityGroupResetClusterGlobalSg(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	if len(app.AppGroupID) == 0 {
		return nil
	}
	return processRebuildSecurityGroupClusterGlobal(ctx, app, true)
}

func ProcessRebuildSecurityGroupClusterLocal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	return processRebuildSecurityGroupClusterLocal(ctx, app)
}

// ProcessRebuildSecurityGroupStandalone will deal with standalone security group
func ProcessRebuildSecurityGroupStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	if len(app.AppGroupID) == 0 {
		return processRebuildSecurityGroupStandaloneLocal(ctx, app)
	}
	return processRebuildSecurityGroupStandaloneGlobal(ctx, app, true)
}

// ProcessRebuildSecurityGroupStandaloneForModifySpec
// 【标准版】变配流程专属的安全组重建逻辑
//
//	目的是为了让热活组实例的安全组可以只增不减，新老节点都在安全组内
func ProcessRebuildSecurityGroupStandaloneForModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	if len(app.AppGroupID) == 0 {
		return processRebuildSecurityGroupStandaloneLocal(ctx, app)
	}
	return processRebuildSecurityGroupStandaloneGlobal(ctx, app, false)
}

// ProcessRebuildSecurityGroupResetStandaloneGlobalSg
// 【标准版】热活组安全组重置流程
//
//	一般对应ProcessRebuildSecurityGroupStandaloneForModifySpec成对使用
//	因为ProcessRebuildSecurityGroupStandaloneForModifySpec会导致新老节点都开放
//	这一步会把多余的老ip剔除
func ProcessRebuildSecurityGroupResetStandaloneGlobalSg(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	if len(app.AppGroupID) == 0 {
		return nil
	}
	return processRebuildSecurityGroupStandaloneGlobal(ctx, app, true)
}

func ProcessRebuildSecurityGroupStandaloneGlobal(ctx context.Context, app *x1model.Application) error {
	return processRebuildSecurityGroupStandaloneGlobal(ctx, app, true)
}

// processRebuildSecurityGroupStandaloneGlobal will deal with global standalone group security group
// 【标准版】needDel为true是默认的操作，会把todelete等未来会删除的节点剔除掉
//
//	 false则只增加新ip，而不会剔除即将被删除的ip。
//
//		false的使用场景很少，例如热活组变配时，需要在中间过程新老节点同时开放，才需要使用
//		一般而言，使用过false以后，后续流程需要手工再把这里暂时没剔除的ip剔除掉。
func processRebuildSecurityGroupStandaloneGlobal(ctx context.Context, app *x1model.Application, needDel bool) error {
	if len(app.SecurityGroupId) == 0 && app.ResourceType == "container" {
		resource.LoggerTask.Notice(ctx, "container no need to rebuild", logit.String("appid", app.AppId))
		return nil
	}

	var toAddIPList, toDelIPList []string
	var portList []int32
	portMap := make(map[int32]bool)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate {
				toAddIPList = append(toAddIPList, node.Ip)
			}
			if needDel {
				if node.Status == x1model.NodeOrProxyStatusToDelete || node.Status == x1model.NodeOrProxyStatusToFakeDelete {
					toDelIPList = append(toDelIPList, node.Ip)
				}
			}
			portMap[cast.ToInt32(app.Port)] = true
		}
	}

	for port := range portMap {
		portList = append(portList, port)
	}
	if err := gmaster.GlobalMasterOp().UpdateSecurity(ctx, &gmaster.UpdateInnerSecurityParams{
		AppGroupID:  app.AppGroupID,
		AppID:       app.AppId,
		UserID:      app.UserId,
		ToAddIpList: toAddIPList,
		ToDelIpList: toDelIPList,
		PortList:    portList,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update security fail", logit.Error("error", err))
		return err
	}

	return nil
}

// ProcessRebuildSecurityGroupStandaloneLocal will deal with quit group
func ProcessRebuildSecurityGroupStandaloneLocal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	return processRebuildSecurityGroupStandaloneLocal(ctx, app)
}
