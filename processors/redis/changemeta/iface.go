// Copyright(C) 2024 Baidu Inc. All Rights Reserved.
// Author: <PERSON><PERSON> (ca<PERSON><EMAIL>)
// Date: 2024/07/18

package changemeta

import (
	"context"
	"encoding/json"
)

type MigrateMetaserverParams struct {
	TargetMetaserverID string `json:"target_metaserver_id"`
	TargetAPIVer       int    `json:"target_api_ver"` // 1 or 2 ,new-metaserver is best set to 2, old-metaserver can only set to 1
	SourceAPIVer       int    `json:"source_api_ver"`
}

func GetMetaserverParams(ctx context.Context, parameter string) (*MigrateMetaserverParams, error) {
	var param MigrateMetaserverParams
	if err := json.Unmarshal([]byte(parameter), &param); err != nil {
		return nil, err
	}
	return &param, nil
}
