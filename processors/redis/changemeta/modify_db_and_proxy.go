// Copyright(C) 2024 Baidu Inc. All Rights Reserved.
// Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
// Date: 2024/07/18

package changemeta

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessChangeMetaserver(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := GetMetaserverParams(ctx, teu.Parameters)
	if err != nil {
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail")
		return err
	}

	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model fail", logit.Error("error", err))
		return fmt.Errorf("get cluster model fail: %s", err.Error())
	}

	targetMetaInfo, err := x1model.MetaClusterGetByIMetaClusterId(ctx, params.TargetMetaserverID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get target meta cluster error",
			logit.String("target id", params.TargetMetaserverID), logit.Error("error", err))
	}
	// 修改数据库
	if err := changeMetaserverDbRecord(ctx, app, clusterModel, targetMetaInfo); err != nil {
		return fmt.Errorf("change metaserver db record fail: %s", err.Error())
	}
	// 热设置proxy的metaserver地址

	for _, itf := range app.Interfaces {
		for _, p := range itf.Proxys {
			if err := HotSetProxyMetaserverAddr(ctx, p, clusterModel.ClientAuth, targetMetaInfo.Entrance); err != nil {
				resource.LoggerTask.Warning(ctx, "set proxy metaserver conf fail", logit.String("err", err.Error()))
				return err
			}
			time.Sleep(1 * time.Second)
			err := util.PingTest(ctx, p.FloatingIP, p.Port, 1, nil)
			if err != nil && !strings.Contains(err.Error(), "NOAUTH") {
				return fmt.Errorf("ping proxy %s fail: %s", p.ProxyId, err.Error())
			}
		}
	}

	return nil
}

func changeMetaserverDbRecord(ctx context.Context, app *x1model.Application,
	clusterModel *csdk.CsmasterCluster, targetMetaInfo *x1model.MetaCluster) error {
	// STEP1 修改csmaster
	if clusterModel.MetaserverId != targetMetaInfo.MetaClusterID ||
		clusterModel.MetaserverAddress != targetMetaInfo.Entrance {
		updateParams := &csmaster.UpdateClusterModelParams{
			Model: &csmaster.CsmasterCluster{
				MetaserverId:      targetMetaInfo.MetaClusterID,
				MetaserverAddress: targetMetaInfo.Entrance,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, updateParams); err != nil {
			resource.LoggerTask.Warning(ctx, "update cluster model fail", logit.Error("error", err))
			return fmt.Errorf("update cluster model fail: %s", err.Error())
		}
	} else {
		resource.LoggerTask.Trace(ctx, "csmaster already is new metaid ,skip")
	}

	// STEP2 修改X1
	if app.LocalMetaserver != targetMetaInfo.MetaClusterID {
		app.LocalMetaserver = targetMetaInfo.MetaClusterID
		if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
			resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
			return err
		}
	} else {
		resource.LoggerTask.Trace(ctx, "x1 already is new metaid ,skip")
	}

	return nil
}

func HotSetProxyMetaserverAddr(ctx context.Context, proxy *x1model.Proxy, password string, metaserverAddr string) error {
	c := single_redis.NewClient(proxy.FloatingIP, proxy.Port,
		single_redis.WithPassword(password),
		single_redis.WithRetry(0),
	)
	defer c.Close()

	metaResult, err := c.Do(ctx, "proxyconfig", "meta", "get").Result()
	if err != nil {
		resource.LoggerTask.Error(ctx, "proxy config get fail", logit.Error("err", err))
		return fmt.Errorf("proxy config get fail:%w", err)
	}
	strResult := cast.ToString(metaResult)
	if strResult == metaserverAddr {
		resource.LoggerTask.Trace(ctx, "proxy already is new metaid ,skip", logit.String("proxyid", proxy.ProxyId),
			logit.String("proxy meta", strResult))
		return nil
	}
	resource.LoggerTask.Trace(ctx, "start to hot set proxy metaconf", logit.String("from", strResult),
		logit.String("to", metaserverAddr))

	err = c.Do(ctx, "proxyconfig", "meta", "set", metaserverAddr).Err()

	if err != nil {
		resource.LoggerTask.Warning(ctx, "proxy config set fail", logit.Error("err", err))
		return err
	}

	return nil
}
