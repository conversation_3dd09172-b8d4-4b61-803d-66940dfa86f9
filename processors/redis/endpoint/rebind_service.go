/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package endpoint

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessRebindToExchangeBlb 绑定服务发布点到toexchange blb
func ProcessRebindToExchangeBlb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	var oldBlb, toExchangeBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeNormal {
			oldBlb = blb
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			oldBlb = blb
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			toExchangeBlb = blb
		}
	}

	if oldBlb == nil || toExchangeBlb == nil {
		resource.LoggerTask.Notice(ctx, "blb not found", logit.String("appId", app.AppId))
		return errors.New("blb not found")
	}

	if toExchangeBlb.ServicePublishEndpoint == "" {
		resource.LoggerTask.Notice(ctx, "service publish endpoint is empty", logit.String("blbId", toExchangeBlb.BlbId))
		return nil
	}

	if oldBlb.ResourceUserId != toExchangeBlb.ResourceUserId {
		resource.LoggerTask.Warning(ctx, "blb resource_user_id is diff",
			logit.String("oldblb", oldBlb.BlbId), logit.String("toexchangeblb", toExchangeBlb.BlbId))
		return fmt.Errorf("blb(%s) and blb(%s) resource_user_id is diff", oldBlb.BlbId, toExchangeBlb.BlbId)
	}

	if oldBlb.ResourceVpcId != toExchangeBlb.ResourceVpcId {
		resource.LoggerTask.Warning(ctx, "blb resource_vpc_id is diff",
			logit.String("oldblb", oldBlb.BlbId), logit.String("toexchangeblb", toExchangeBlb.BlbId))
		return fmt.Errorf("blb(%s) and blb(%s) resource_vpc_id is diff", oldBlb.BlbId, toExchangeBlb.BlbId)
	}

	var userId string
	if len(toExchangeBlb.ResourceUserId) == 0 {
		userId = app.UserId
	} else {
		userId = toExchangeBlb.ResourceUserId
	}

	if err := rebindService(ctx, userId, toExchangeBlb.ServicePublishEndpoint, toExchangeBlb.BlbId); err != nil {
		resource.LoggerTask.Error(ctx, "rebind service fail", logit.String("blbId", toExchangeBlb.BlbId),
			logit.Error("error", err))
		return err
	}
	return nil
}

// ProcessRebindToOriginBlb 绑定服务发布点到原BLB
func ProcessRebindToOriginBlb(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	var oldBlb, toExchangeBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeNormal {
			oldBlb = blb
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			oldBlb = blb
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			toExchangeBlb = blb
		}
	}

	if oldBlb == nil || toExchangeBlb == nil {
		resource.LoggerTask.Notice(ctx, "blb not found", logit.String("appId", app.AppId))
		return errors.New("blb not found")
	}

	if oldBlb.ServicePublishEndpoint == "" {
		resource.LoggerTask.Notice(ctx, "service publish endpoint is empty", logit.String("blbId", oldBlb.BlbId))
		return nil
	}

	if oldBlb.ResourceUserId != toExchangeBlb.ResourceUserId {
		resource.LoggerTask.Warning(ctx, "blb resource_user_id is diff",
			logit.String("oldblb", oldBlb.BlbId), logit.String("toexchangeblb", toExchangeBlb.BlbId))
		return fmt.Errorf("blb(%s) and blb(%s) resource_user_id is diff", oldBlb.BlbId, toExchangeBlb.BlbId)
	}

	if oldBlb.ResourceVpcId != toExchangeBlb.ResourceVpcId {
		resource.LoggerTask.Warning(ctx, "blb resource_vpc_id is diff",
			logit.String("oldblb", oldBlb.BlbId), logit.String("toexchangeblb", toExchangeBlb.BlbId))
		return fmt.Errorf("blb(%s) and blb(%s) resource_vpc_id is diff", oldBlb.BlbId, toExchangeBlb.BlbId)
	}

	var userId string
	if len(oldBlb.ResourceUserId) == 0 {
		userId = app.UserId
	} else {
		userId = oldBlb.ResourceUserId
	}

	if err := rebindService(ctx, userId, oldBlb.ServicePublishEndpoint, oldBlb.BlbId); err != nil {
		resource.LoggerTask.Error(ctx, "rebind service fail", logit.String("blbId", oldBlb.BlbId),
			logit.Error("error", err))
		return err
	}
	return nil
}
