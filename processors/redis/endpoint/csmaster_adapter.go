package endpoint

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// updateCsmasterEndpointInfo 调用Csmaster接口，将 endpoint 信息存入csmaster数据库
func updateCsmasterEndpointInfo(ctx context.Context, app *x1model.Application) error {
	req := &csmaster.UpdateClusterModelParams{
		Model:  &csdk.CsmasterCluster{},
		UserID: app.UserId,
		AppID:  app.AppId,
	}

	for _, blb := range app.BLBs {
		if len(blb.BlbId) == 0 {
			continue
		}
		// 跳过只读实例组
		if len(blb.RoGroupID) != 0 {
			continue
		}
		if blb.Status == x1model.BLBStatusDeleted {
			continue
		}
		// x1model.BLBTypeAppToDelete会延迟删除,因此单纯用x1model.BLBStatusDeleted过滤不掉
		if blb.Type == x1model.BLBTypeAppToDelete {
			continue
		}
		req.Model.EndpointId = blb.EndpointId
		req.Model.EndpointIp = blb.EndpointIp
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update csmaster cluster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UpdateCsmasterClusterModelFail.Wrap(err)
	}

	return nil
}
