package endpoint

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// updateRoGroupEndpointInfo 将 endpoint 信息存入 ro_group 数据库
func updateRoGroupEndpointInfo(ctx context.Context, app *x1model.Application, roGroupID string) error {
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, blb := range app.BLBs {
		if blb.Status == x1model.BLBStatusDeleted {
			continue
		}

		// 如果 roGroupID 非空，则需要匹配 roGroupID
		if blb.RoGroupID != roGroupID {
			continue
		}

		roGroup.EndpointId = blb.EndpointId
		roGroup.EndpointIp = blb.EndpointIp
	}

	if err := resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update csmaster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return err
	}
	return nil
}
