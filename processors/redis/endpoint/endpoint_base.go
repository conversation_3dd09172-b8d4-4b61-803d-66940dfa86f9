/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2022/06/27, by wang<PERSON><PERSON>(<EMAIL>), create
2024/07/22, by wang<PERSON><PERSON>(wang<PERSON><EMAIL>), add endpoint check
*/

/*
DESCRIPTION
服务网卡
*/

package endpoint

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbV2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	endpointComponent "icode.baidu.com/baidu/scs/x1-base/component/neutron/endpoint"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// 创建服务发布点, 并记录到数据库中
func createServicePublishEndpoint(ctx context.Context, app *x1model.Application, roGroupID string) (err error) {
	var newBlbList []*x1model.BLB
	var userId string

	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}

		// 如果 roGroupID 非空，则需要匹配 roGroupID
		if b.RoGroupID != roGroupID {
			continue
		}

		// 如果 ServicePublishEndpoint 非空，则无需再次创建
		if len(b.ServicePublishEndpoint) != 0 {
			continue
		}

		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}

		var params blbV2.CreatePublishEndpointParams
		params.UserID = userId
		// 大小写字母、数字长度1-65
		params.ServiceName = "scs"
		params.ElbID = b.BlbId
		params.AllowUserId = app.UserId
		service, err := blbV2.Instance().CreatePublishEndpoint(ctx, &params)
		if err != nil {
			return err
		}
		b.ServicePublishEndpoint = service
		newBlbList = append(newBlbList, b)
	}
	resource.LoggerTask.Notice(ctx, "execute ApplyServicePublishEndpoint func ok",
		logit.String("appid", app.AppId),
		logit.String("blb", base_utils.Format(newBlbList)),
	)
	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	// 更新数据库
	if len(newBlbList) > 0 {
		if err := x1model.BLBsSave(ctx, newBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}

// 创建服务网卡, 并记录到数据库中
func createEndpoint(ctx context.Context, app *x1model.Application, roGroupID string) error {
	var newBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}

		// 如果 roGroupID 非空，则需要匹配 roGroupID
		if b.RoGroupID != roGroupID {
			continue
		}

		// 如果 b.EndpointId 非空，则无需再次创建
		if len(b.EndpointId) != 0 {
			continue
		}

		var params endpointComponent.CreateEndpointParams
		// 用户的 vpc 等信息
		params.VpcID = b.VpcId
		params.SubnetID = b.SubnetId
		params.Service = b.ServicePublishEndpoint
		params.UserID = app.UserId

		endpointId, ipAddr, err := endpointComponent.Instance().CreateEndpoint(ctx, &params)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "create endpoint failed", logit.Error("error", err))
			return err
		}

		// check endpoint
		for {
			endpointDetail, err := endpointComponent.Instance().GetEndpointDetail(ctx, &endpointComponent.CommonEndpointParams{
				EndpointID: endpointId,
				UserID:     app.UserId,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get endpoint detail failed", logit.Error("error", err))
				return err
			}
			if endpointDetail.Status == "available" {
				break
			}

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(5 * time.Second):
				continue
			}
		}
		b.EndpointId = endpointId
		b.EndpointIp = ipAddr
		newBlbList = append(newBlbList, b)
	}
	resource.LoggerTask.Notice(ctx, "execute ApplyEndpoint func ok",
		logit.String("appid", app.AppId),
		logit.String("blb", base_utils.Format(newBlbList)),
	)

	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	// 更新数据库
	if len(newBlbList) > 0 {
		if err := x1model.BLBsSave(ctx, newBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}

// DeleteServicePublishEndpoint 删除服务发布点
func DeleteServicePublishEndpoint(ctx context.Context, app *x1model.Application, roGroupID string, blbType string) (err error) {
	return deleteServicePublishEndpoint(ctx, app, roGroupID, blbType)
}

// 删除服务发布点
func deleteServicePublishEndpoint(ctx context.Context, app *x1model.Application, roGroupID string, blbType string) (err error) {
	var newBlbList []*x1model.BLB
	var userId string

	for _, b := range app.BLBs {
		if len(b.ServicePublishEndpoint) == 0 {
			continue
		}

		// 如果 roGroupID 非空，则需要匹配 roGroupID
		if b.RoGroupID != roGroupID {
			continue
		}

		if b.Type != blbType {
			continue
		}

		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}

		var params blbV2.DeletePublishEndpointParams
		params.UserID = userId
		params.Service = b.ServicePublishEndpoint

		err := blbV2.Instance().DeletePublishEndpoint(ctx, &params)
		if err != nil {
			return err
		}
		b.ServicePublishEndpoint = ""
		newBlbList = append(newBlbList, b)
	}
	// 无需删除服务网卡
	if len(newBlbList) == 0 {
		return nil
	}

	// 更新数据库
	if err := x1model.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

// DeleteEndpoint 删除服务网卡
func DeleteEndpoint(ctx context.Context, app *x1model.Application, roGroupID, blbType string) error {
	return deleteEndpoint(ctx, app, roGroupID, blbType)
}

// 删除服务网卡
func deleteEndpoint(ctx context.Context, app *x1model.Application, roGroupID, blbType string) error {
	var newBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if len(b.EndpointId) == 0 {
			continue
		}

		// 如果 roGroupID 非空，则需要匹配 roGroupID
		if b.RoGroupID != roGroupID {
			continue
		}

		if b.Type != blbType {
			continue
		}

		var params endpointComponent.CommonEndpointParams
		params.UserID = app.UserId
		params.EndpointID = b.EndpointId

		err := endpointComponent.Instance().DeleteEndpoint(ctx, &params)
		if err != nil {
			return err
		}

		b.EndpointId = ""
		b.EndpointIp = ""
		newBlbList = append(newBlbList, b)
	}

	// 无需删除服务网卡
	if len(newBlbList) == 0 {
		return nil
	}

	// 更新数据库
	if err := x1model.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}
