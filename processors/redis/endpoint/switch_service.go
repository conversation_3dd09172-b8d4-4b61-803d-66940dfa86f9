/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2022/08/31, by wang<PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION

	交换服务发布点
*/
package endpoint

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/compo_utils"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	elbv2 "icode.baidu.com/baidu/scs/x1-base/sdk/elb_v2"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessSwitchService(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// (1) 获取
	param, err := iface.GetSwitchServiceParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	oldBlb, err := getBlbByAppId(ctx, param.OldAppId)
	if err != nil {
		return err
	}
	newBlb, err := getBlbByAppId(ctx, param.NewAppId)
	if err != nil {
		return err
	}

	// (2) 交换
	err = switchService(ctx, oldBlb, newBlb)
	if err != nil {
		return err
	}

	// (3) 保存 blb 表
	err = saveDb(ctx, oldBlb, newBlb)
	if err != nil {
		return err
	}

	// (4) 保存 csmaster
	err = saveCsmaster(ctx, param.OldAppId)
	if err != nil {
		return err
	}

	err = saveCsmaster(ctx, param.NewAppId)
	if err != nil {
		return err
	}
	return nil
}

func getBlbByAppId(ctx context.Context, appId string) (blb *x1model.BLB, err error) {
	// (1) 获取 app
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId), logit.Error("dbError", err))
		return nil, cerrs.ErrDbQueryFail.Wrap(err)
	}

	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return nil, cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	// (2) 获取 blb
	var curBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}
		// 跳过只读实例组
		if len(b.RoGroupID) != 0 {
			continue
		}
		if b.Status == x1model.BLBStatusDeleted {
			continue
		}
		if blb.Type == x1model.BLBTypeAppToDelete {
			continue
		}
		// 跳过没有服务发布点的 BLB
		if len(b.ServicePublishEndpoint) == 0 {
			continue
		}

		curBlbList = append(curBlbList, b)
	}

	if len(curBlbList) != 1 {
		resource.LoggerTask.Warning(ctx, "app blb not found", logit.String("appId", appId), logit.Int("curBlbNum", len(curBlbList)))
		return nil, cerrs.ErrNotFound.Errorf("app(%s) blb not found", appId)
	}

	var curBlb *x1model.BLB
	curBlb = curBlbList[0]
	resource.LoggerTask.Notice(ctx, "app blb get ok", logit.String("appId", appId),
		logit.String("BlbId", curBlb.BlbId),
		logit.String("ServicePublishEndpoint", curBlb.ServicePublishEndpoint),
		logit.String("EndpointId", curBlb.EndpointId),
		logit.String("EndpointIp", curBlb.EndpointIp),
	)
	return curBlb, nil
}

func switchService(ctx context.Context, oldBlb *x1model.BLB, newBlb *x1model.BLB) error {
	var userId string
	// (1) 检查 A 与 B 的资源账户、VPC 是否一致
	if oldBlb.ResourceUserId != newBlb.ResourceUserId {
		return cerrs.ErrNotFound.Errorf("app(%s) and app(%s) blb resource_user_id is diff", oldBlb.AppId, newBlb.AppId)
	}
	userId = oldBlb.ResourceUserId

	if oldBlb.ResourceVpcId != newBlb.ResourceVpcId {
		return cerrs.ErrNotFound.Errorf("app(%s) and app(%s) blb resource_vpc_id is diff", oldBlb.AppId, newBlb.AppId)
	}

	// (2) 绑定服务发布点 A 与 BLB B
	err := rebindService(ctx, userId, oldBlb.ServicePublishEndpoint, newBlb.BlbId)
	if err != nil {
		return err
	}

	// (3) 绑定服务发布点 B 与 BLB A
	err = rebindService(ctx, userId, newBlb.ServicePublishEndpoint, oldBlb.BlbId)
	if err != nil {
		return err
	}

	return nil
}

func rebindService(ctx context.Context, userId string, serviceName string, instanceId string) error {
	// (1) 初始化 service SDK
	serviceSdk := elbv2.NewDefaultElbEndpointSdk()

	// (2) 解绑服务发布点与旧 instanceId
	auth, err := compo_utils.GetOpenapiAuth(ctx, userId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Get auth Fail", logit.String("userid", userId))
		return err
	}

	//幂等检查
	rsp, err := serviceSdk.GetPublishEndpointDetail(ctx, &elbv2.GetPublishEndpointDetailRequest{
		Service: serviceName,
		Auth:    auth,
	})
	if err != nil {
		resource.LoggerTask.Error(ctx, "get publish endpoint detail Fail",
			logit.String("serviceName", serviceName), logit.Error("error", err),
			logit.String("rsp", base_utils.Format(rsp)))
		return err
	}

	if rsp.Service.InstanceID == instanceId {
		resource.LoggerTask.Warning(ctx, "service already bind to instance",
			logit.String("serviceName", serviceName), logit.String("instanceId", instanceId))
		return nil
	}

	unbindParam := elbv2.UnbindServiceRequest{
		Service: serviceName,
		Auth:    auth,
	}

	_, err = serviceSdk.UnbindService(ctx, &unbindParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, "unbind publish endpoint service Fail", logit.String("param", base_utils.Format(unbindParam)), logit.Error("error", err))
		return err
	}

	// (3) 绑定服务发布点与新 instanceId
	auth, err = compo_utils.GetOpenapiAuth(ctx, userId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "Get auth Fail", logit.String("userid", userId))
		return err
	}
	bindParam := elbv2.BindServiceRequest{
		Service:    serviceName,
		InstanceID: instanceId,
		Auth:       auth,
	}
	_, err = serviceSdk.BindService(ctx, &bindParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, "bind publish endpoint service Fail", logit.String("param", base_utils.Format(bindParam)), logit.Error("error", err))
		return err
	}
	return nil
}

func saveDb(ctx context.Context, oldBlb *x1model.BLB, newBlb *x1model.BLB) error {
	var changedBlbList []*x1model.BLB
	var tmpServicePublishEndpoint string
	var tmpEndpointId string
	var tmpEndpointIp string

	tmpServicePublishEndpoint = oldBlb.ServicePublishEndpoint
	oldBlb.ServicePublishEndpoint = newBlb.ServicePublishEndpoint
	newBlb.ServicePublishEndpoint = tmpServicePublishEndpoint

	tmpEndpointId = oldBlb.EndpointId
	oldBlb.EndpointId = newBlb.EndpointId
	newBlb.EndpointId = tmpEndpointId

	tmpEndpointIp = oldBlb.EndpointIp
	oldBlb.EndpointIp = newBlb.EndpointIp
	newBlb.EndpointIp = tmpEndpointIp

	changedBlbList = append(changedBlbList, oldBlb)
	changedBlbList = append(changedBlbList, newBlb)

	// 更新数据库
	if err := x1model.BLBsSave(ctx, changedBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func saveCsmaster(ctx context.Context, appId string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", appId), logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", appId))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", appId)
	}

	err = updateCsmasterEndpointInfo(ctx, app)
	return err
}
