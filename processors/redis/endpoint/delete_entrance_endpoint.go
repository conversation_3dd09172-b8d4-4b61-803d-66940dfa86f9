/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/29 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_entrance_endpoint.go
 * <AUTHOR>
 * @date 2022/12/29 19:59:54
 * @brief
 *
 **/

package endpoint

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2component "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessDeleteEntranceEndpoint(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 1. 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity), logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 2. 检查是否需要删除服务网卡(根据 user_id 判断)
	env := blbv2component.Instance().GetEnv(ctx)
	if privatecloud.IsPrivateENV() {
		return nil
	}
	if app.UserId == env.ResourcePrivateUserId {
		return nil
	}
	if app.ResourceType != "container" {
		return nil
	}

	// 3. 删除服务网卡
	if err := deleteEndpoint(ctx, app, "", x1model.BLBTypeAppEntrance); err != nil {
		resource.LoggerTask.Error(ctx, "execute DeleteEndpoint step err",
			logit.String("appId", app.AppId),
			logit.String("exe_step", "delete_endpoint"),
			logit.Error("error", err),
		)
		return err
	}

	// 4. 删除服务发布点
	if err := deleteServicePublishEndpoint(ctx, app, "", x1model.BLBTypeAppEntrance); err != nil {
		resource.LoggerTask.Error(ctx, "execute DeleteEndpoint step err",
			logit.String("appId", app.AppId),
			logit.String("exe_step", "delete_service_publish_endpoint"),
			logit.Error("error", err),
		)
		return err
	}

	return nil
}
