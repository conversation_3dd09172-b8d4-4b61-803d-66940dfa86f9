package timewindow

import (
	"testing"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

func getMockAppWithToCreateAndToDelete() *x1model.Application {
	return &x1model.Application{
		AppId: "mockAppId",
		Clusters: []*x1model.Cluster{
			{
				ClusterId: "mockClusterId-01",
				Nodes: []*x1model.Node{
					{
						NodeId: "mockNodeId-01",
						Status: x1model.NodeOrProxyStatusToCreate,
					},
					{
						NodeId: "mockNodeId-01",
						Status: x1model.NodeOrProxyStatusInUse,
					},
					{
						NodeId: "mockNodeId-01",
						Status: x1model.NodeOrProxyStatusToFakeDelete,
					},
				},
			},
		},
	}
}

func getMockAppWithToDelete() *x1model.Application {
	return &x1model.Application{
		AppId: "mockAppId",
		Clusters: []*x1model.Cluster{
			{
				ClusterId: "mockClusterId-01",
				Nodes: []*x1model.Node{
					{
						NodeId: "mockNodeId-01",
						Status: x1model.NodeOrProxyStatusToFakeDelete,
					},
				},
			},
		},
	}
}

func getMockAppWithNothing() *x1model.Application {
	return &x1model.Application{
		AppId: "mockAppId",
		Clusters: []*x1model.Cluster{
			{
				ClusterId: "mockClusterId-01",
				Nodes: []*x1model.Node{
					{
						NodeId: "mockNodeId-01",
						Status: x1model.NodeOrProxyStatusInUse,
					},
				},
			},
		},
	}
}

func Test_updateTaskDetailModel(t *testing.T) {
	t.Run(t.Name(), func(t *testing.T) {
		taskDetailModel := &csmaster_model_interface.TimeWindowTask{
			ClusterID:     123,
			TaskStatus:    TaskStatusExecuting,
			CreateTime:    time.Now(),
			TaskType:      ChangeReplicasTask,
			TaskParams:    "",
			TransactionID: "teu.TaskID",
			ExecuteTime:   time.Now(),
			TaskFrom:      "x1-api",
			TaskID:        "task-id",
		}
		params := []*updateTaskDetailPrams{
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepCreateNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepDeployNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepConfigNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepHandoverForSync,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepSync,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepReleaseNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    "",
				CurTaskStep: "",
				TaskStatus:  TaskStatusSuccess,
				App:         getMockAppWithToCreateAndToDelete(),
				Model:       taskDetailModel,
			},
		}
		for _, param := range params {
			err := updateTaskDetailModel(nil, param)
			if err != nil {
				t.Errorf("updateTaskDetailModel() error = %v", err)
			}
			t.Logf("updateTaskDetailModel() = %v", param.Model)
		}
	})
	t.Run(t.Name(), func(t *testing.T) {
		taskDetailModel := &csmaster_model_interface.TimeWindowTask{
			ClusterID:     123,
			TaskStatus:    TaskStatusExecuting,
			CreateTime:    time.Now(),
			TaskType:      ChangeReplicasTask,
			TaskParams:    "",
			TransactionID: "teu.TaskID",
			ExecuteTime:   time.Now(),
			TaskFrom:      "x1-api",
			TaskID:        "task-id",
		}
		params := []*updateTaskDetailPrams{
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepCreateNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepDeployNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepConfigNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepHandoverForSync,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepSync,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    ChangeReplicasTask,
				CurTaskStep: StepReleaseNodes,
				TaskStatus:  TaskStatusExecuting,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
			{
				TaskType:    "",
				CurTaskStep: "",
				TaskStatus:  TaskStatusSuccess,
				App:         getMockAppWithToDelete(),
				Model:       taskDetailModel,
			},
		}
		for _, param := range params {
			err := updateTaskDetailModel(nil, param)
			if err != nil {
				t.Errorf("updateTaskDetailModel() error = %v", err)
			}
			t.Logf("updateTaskDetailModel() = %v", param.Model)
		}
	})
}
