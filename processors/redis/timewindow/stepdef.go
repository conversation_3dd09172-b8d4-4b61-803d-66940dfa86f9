package timewindow

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	CreateTask         = "create"
	RelaunchTask       = "relaunch"
	UpgradeTask        = "upgrade"
	ModifyTask         = "modify"
	ModifySpecTask     = "modify-spec"
	AddShardTask       = "add-shard"
	DeleteShardTask    = "delete-shard"
	ChangeReplicasTask = "change_replicas"
	AddReplicasTask    = "add_replicas"
	DeleteReplicasTask = "delete_replicas"
	AddReadonlyTask    = "add_readonly"
	DeleteReadonlyTask = "delete_readonly"
	ChangeReadonlyTask = "change_readonly"
	RecoverTask        = "recover"
	ModifyAZTask       = "modify_az"
	ModifyEntranceTask = "modify_default_entrance"
	FlushExpired       = "flush_expired"
)

const (
	StepTryResizeNodes   = "try-resize-nodes"
	StepCreateNodes      = "create-nodes"
	StepDeployNodes      = "deploy-nodes"
	StepConfigNodes      = "config-nodes"
	StepRestartFollowers = "restart-followers"
	StepRestartMasters   = "restart-masters"
	StepHandover         = "handover"
	StepHandoverForSync  = "handover-for-sync"
	StepSync             = "sync"
	StepReleaseNodes     = "release-nodes"
	StepRebalance        = "rebalance"
	StepPrepareData      = "prepare-data"
	StepRecoverData      = "recover-data"
	StepUpgradeFollowers = "upgrade-followers"
	StepUpgradeMasters   = "upgrade-masters"
	StepUpgradeProxies   = "upgrade-proxies"
	StepCreateIP         = "create-ip"
	StepConfigIP         = "config-ip"
	StepExchangeIP       = "exchange-ip"
	StepFlushExpired     = "flush-expired"
)

const (
	TaskStatusExecuting = "executing"
	TaskStatusSuccess   = "success"
	TaskStatusError     = "error"
	TaskStatusWaiting   = "waiting"
	TaskStatusCancelled = "cancelled"
)

type TaskDetail struct {
	StepName  string    `json:"stepName"`
	StepDesc  string    `json:"stepDesc"`
	Status    string    `json:"status"`
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
	Detail    string    `json:"detail"`
}

var stepDesc = map[string]string{
	StepTryResizeNodes:   "尝试热变配节点",
	StepCreateNodes:      "创建新节点",
	StepDeployNodes:      "部署服务至新节点",
	StepConfigNodes:      "配置新节点",
	StepRestartFollowers: "重启从节点",
	StepRestartMasters:   "重启(旧的)主节点",
	StepHandover:         "主从切换",
	StepHandoverForSync:  "主从切换",
	StepSync:             "新节点同步数据",
	StepReleaseNodes:     "释放旧节点",
	StepRebalance:        "数据槽重分配",
	StepPrepareData:      "准备数据",
	StepRecoverData:      "恢复数据",
	StepUpgradeFollowers: "升级从节点",
	StepUpgradeMasters:   "升级(旧的)主节点",
	StepUpgradeProxies:   "升级代理",
	StepCreateIP:         "创建IP",
	StepConfigIP:         "配置IP",
	StepExchangeIP:       "切换域名指向IP",
	StepFlushExpired:     "清理过期数据",
}

var stepLists = map[string][]string{
	CreateTask:   {StepCreateNodes, StepDeployNodes, StepConfigNodes},
	RelaunchTask: {StepRestartFollowers, StepHandover, StepRestartMasters},
	UpgradeTask:  {StepUpgradeFollowers, StepHandover, StepUpgradeMasters},
	ModifySpecTask: {StepTryResizeNodes, StepCreateNodes, StepDeployNodes, StepConfigNodes,
		StepSync, StepHandoverForSync, StepReleaseNodes},
	AddShardTask:       {StepCreateNodes, StepDeployNodes, StepConfigNodes, StepRebalance},
	DeleteShardTask:    {StepRebalance, StepReleaseNodes},
	AddReplicasTask:    {StepCreateNodes, StepDeployNodes, StepConfigNodes, StepSync},
	DeleteReplicasTask: {StepReleaseNodes},
	AddReadonlyTask:    {StepCreateNodes, StepDeployNodes, StepConfigNodes, StepSync},
	DeleteReadonlyTask: {StepReleaseNodes},
	RecoverTask:        {StepPrepareData, StepRecoverData},
	ModifyAZTask:       {StepCreateNodes, StepDeployNodes, StepSync, StepHandover},
	ModifyEntranceTask: {StepCreateIP, StepConfigIP, StepExchangeIP},
	FlushExpired:       {StepFlushExpired},
}

func getShowTaskType(taskType string) string {
	switch taskType {
	case AddReplicasTask, DeleteReplicasTask:
		return ChangeReplicasTask
	case ModifySpecTask, AddShardTask, DeleteShardTask:
		return ModifyTask
	case AddReadonlyTask, DeleteReadonlyTask:
		return ChangeReadonlyTask
	default:
		return taskType
	}
}

func getStepList(ctx context.Context, taskType string, app *x1model.Application) []string {
	switch taskType {
	case ChangeReplicasTask:
		for _, cluster := range app.Clusters {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Status == x1model.NodeOrProxyStatusToCreate {
					return stepLists[AddReplicasTask]
				}
			}
		}
		return stepLists[DeleteReplicasTask]
	case UpgradeTask:
		if app.Type == x1model.AppTypeCluster {
			var newStepList []string
			for _, s := range stepLists[UpgradeTask] {
				newStepList = append(newStepList, s)
			}
			newStepList = append(newStepList, StepUpgradeProxies)
			return newStepList
		}
		return stepLists[UpgradeTask]
	default:
		return stepLists[taskType]
	}
}
