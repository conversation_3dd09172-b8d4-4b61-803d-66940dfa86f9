package timewindow

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	cs_model "icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

func ProcessScheduleWaitingTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	tasks, err := resource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(ctx,
		"task_status = ? and task_from = ?", cs_model.TW_TASK_STATUS_WAITING, cs_model.TW_TASK_FROM_X1)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get timewindow tasks failed", logit.Error("error", err))
		return err
	}

	for _, task := range tasks {
		cacheCluster, err := resource.CsmasterOpAgent.GetClusterModel(ctx, cast.ToInt64(task.ClusterID))
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cluster failed", logit.Error("error", err),
				logit.Int("cluster_id", task.ClusterID))
			return err
		}
		estMinutes := getTaskEstimatedMinutes(ctx, cacheCluster, task.TaskType)
		resource.LoggerTask.Notice(ctx, "get task estimated minutes", logit.Int("minutes", estMinutes))
		if !inTimeWindow(ctx, cacheCluster, getTaskEstimatedMinutes(ctx, cacheCluster, task.TaskType), time.Now()) {
			resource.LoggerTask.Notice(ctx, "task not in time window", logit.Int("minutes", estMinutes),
				logit.Int("taskID", task.ID))
			continue
		}
		if err = csmaster.CsmasterOp().DealTimeWindowTask(ctx, cacheCluster.UserInfo.IamUserId,
			cacheCluster.ClusterShowId,
			cast.ToInt64(task.ID), "executenow"); err != nil {
			resource.LoggerTask.Warning(ctx, "process timewindow task failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}

// 获取任务的预估时间，通过该预估时间来帮助确定任务最早和最晚的开始时间
func getTaskEstimatedMinutes(ctx context.Context, cacheCluster *cs_model.CacheCluster, taskType string) int {
	if taskType == cs_model.TW_TASK_TYPE_RELAUNCH {
		return 1
	}

	if taskType == cs_model.TW_TASK_TYPE_MODIFY {
		// pegadb 扩缩容时间不容易估算，暂时不做提前量
		if cacheCluster.StoreType == 3 {
			return 0
		}
		detail, err := csmaster.CsmasterOp().CsmasterGetClusterDetail(ctx, &csmaster.ListCacheClusterInstancesParam{
			UserID: cacheCluster.UserInfo.IamUserId,
			AppID:  cacheCluster.ClusterShowId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cluster detail fail",
				logit.String("cluster_show_id", cacheCluster.ClusterShowId), logit.Error("error", err))
			return 0
		}
		resource.LoggerTask.Notice(ctx, "get cluster detail success",
			logit.String("cluster_show_id", cacheCluster.ClusterShowId),
			logit.String("detail", base_utils.Format(detail)))
		return 5 + (cast.ToInt(detail.UsedMem) / (1024 * 1024 * 1024) / cast.ToInt(cacheCluster.InstanceNum) / 10)
	}
	return 0
}

func inWeekDay(ctx context.Context, day int, weekdays []string) bool {
	for _, weekday := range weekdays {
		if cast.ToInt(weekday) == cast.ToInt(day) {
			return true
		}
	}
	return false
}

// 判断当前时间是否可以开始任务
func inTimeWindow(ctx context.Context, cacheCluster *cs_model.CacheCluster, taskEstMin int, now time.Time) bool {
	if !timeWindowFormatCheck(ctx, cacheCluster.TimeWindow.String) {
		resource.LoggerTask.Warning(ctx, "cluster time window config is wrong",
			logit.String("cluster_show_id", cacheCluster.ClusterShowId))
		return false
	}
	configs := strings.Split(cacheCluster.TimeWindow.String, ";")
	weekdays := strings.Split(configs[0], ",")
	configDayTime, err := time.Parse("15:04", configs[1])
	if err != nil {
		return false
	}
	duration := cast.ToInt(configs[2])

	// 获取昨天今天明天三天的窗口，避免跨天的任务
	var schedulesTimes [][]time.Time
	if inWeekDay(ctx, cast.ToInt(now.Weekday()-1), weekdays) {
		schedulesTimes = append(schedulesTimes, []time.Time{
			time.Date(now.Year(), now.Month(), now.Day()-1, configDayTime.Hour(), configDayTime.Minute(), 0, 0, time.Local),
			time.Date(now.Year(), now.Month(), now.Day()-1, configDayTime.Hour(), configDayTime.Minute(), 0, 0, time.Local).Add(time.Duration(duration) * time.Hour),
		})
	}
	if inWeekDay(ctx, cast.ToInt(now.Weekday()), weekdays) {
		schedulesTimes = append(schedulesTimes, []time.Time{
			time.Date(now.Year(), now.Month(), now.Day(), configDayTime.Hour(), configDayTime.Minute(), 0, 0, time.Local),
			time.Date(now.Year(), now.Month(), now.Day(), configDayTime.Hour(), configDayTime.Minute(), 0, 0, time.Local).Add(time.Duration(duration) * time.Hour),
		})
	}
	if inWeekDay(ctx, cast.ToInt(now.Weekday())+1, weekdays) {
		schedulesTimes = append(schedulesTimes, []time.Time{
			time.Date(now.Year(), now.Month(), now.Day(), configDayTime.Hour(), configDayTime.Minute(), 0, 0, time.Local).Add(time.Duration(24) * time.Hour),
			time.Date(now.Year(), now.Month(), now.Day(), configDayTime.Hour(), configDayTime.Minute(), 0, 0, time.Local).Add(time.Duration(24+duration) * time.Hour),
		})
	}

	for _, schedulesTime := range schedulesTimes {
		if now.Add(time.Duration(taskEstMin)*time.Minute).After(schedulesTime[0]) && now.Add(time.Duration(taskEstMin)*time.Minute).Before(schedulesTime[1]) {
			resource.LoggerTask.Notice(ctx, "time is in time window", logit.Time("start_time", schedulesTime[0]),
				logit.Time("end_time", schedulesTime[1]),
				logit.Time("expect finish time", now.Add(time.Duration(taskEstMin)*time.Minute)),
			)
			return true
		}
		resource.LoggerTask.Notice(ctx, "time is not in time window", logit.Time("start_time", schedulesTime[0]),
			logit.Time("end_time", schedulesTime[1]),
			logit.Time("expect finish time", now.Add(time.Duration(taskEstMin)*time.Minute)))
	}
	return false
}

func timeWindowFormatCheck(ctx context.Context, timeWindow string) bool {
	// 判断格式是否合法
	strs := strings.Split(timeWindow, ";")
	if len(strs) != 3 {
		resource.LoggerTask.Warning(ctx, "timeWindow format error, len not equal 3",
			logit.String("timeWindow", timeWindow))
		return false
	}

	for _, str := range strs {
		if strings.Trim(str, " ") == "" {
			resource.LoggerTask.Warning(ctx, "timeWindow format error, field is empty",
				logit.String("timeWindow", timeWindow))
			return false
		}
	}

	// 判断weekday
	// 1.weekday是否为int
	// 2.weekday是否合法
	// 3.weekday是否重复
	weekdays := strings.Split(strs[0], ",")
	for index, weekday := range weekdays {
		weekdayInt, err := cast.ToIntE(weekday)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "timeWindow format error, weekday is not int",
				logit.String("timeWindow", timeWindow), logit.Error("err", err))
			return false
		}
		if weekdayInt < 0 || weekdayInt >= 7 {
			resource.LoggerTask.Warning(ctx, "timeWindow format error, weekday is wrong",
				logit.String("timeWindow", timeWindow))
			return false
		}
		for j := index + 1; j < len(weekdays); j++ {
			if weekdays[index] == weekdays[j] {
				resource.LoggerTask.Warning(ctx, "timeWindow format error, weekday is duplicate",
					logit.String("timeWindow", timeWindow))
				return false
			}
		}
	}

	// 判断daytime格式是否准确
	if _, err := time.Parse("15:04", strs[1]); err != nil {
		resource.LoggerTask.Warning(ctx, "timeWindow format error, daytime is wrong",
			logit.String("timeWindow", timeWindow), logit.Error("err", err))
		return false
	}

	// 判断duration
	// 1.duration是否为int
	// 2.duration是否合法
	duration, err := cast.ToIntE(strs[2])
	if err != nil {
		resource.LoggerTask.Warning(ctx, "timeWindow format error, duration is wrong",
			logit.String("timeWindow", timeWindow), logit.Error("err", err))
		return false
	}
	if duration <= 0 || duration > 8 {
		resource.LoggerTask.Warning(ctx, "timeWindow format error, duration is wrong",
			logit.String("timeWindow", timeWindow))
		return false
	}
	return true
}

func fetchTimeWindowTask(ctx context.Context, teu *workflow.TaskExecUnit) (*cs_model.TimeWindowTask, error) {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return nil, err
	}
	if param.DeferTaskID == 0 {
		resource.LoggerTask.Notice(ctx, "not scheduled by time window")
		return nil, nil // Returning nil to indicate no task needs processing.
	}
	tasks, err := resource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(ctx, "id = ?", param.DeferTaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get timewindow task failed", logit.Error("error", err))
		return nil, err
	}
	if len(tasks) != 1 {
		resource.LoggerTask.Warning(ctx, "get timewindow task failed", logit.Int("taskID", param.DeferTaskID))
		return nil, errors.New("get timewindow task by id failed, taskID:" + cast.ToString(param.DeferTaskID))
	}
	if tasks[0].TaskFrom != cs_model.TW_TASK_FROM_X1 {
		resource.LoggerTask.Notice(ctx, "task is not from x1", logit.Int("taskID", param.DeferTaskID))
		return nil, nil // Returning nil to indicate no task needs processing.
	}
	return tasks[0], nil
}

func ProcessSetTaskID(ctx context.Context, teu *workflow.TaskExecUnit) error {
	t, err := fetchTimeWindowTask(ctx, teu)
	if err != nil {
		return err
	}
	if t == nil {
		return nil
	}
	t.TaskID = teu.TaskID
	if err = resource.CsmasterOpAgent.TimeWindowTaskSave(ctx, []*cs_model.TimeWindowTask{t}); err != nil {
		resource.LoggerTask.Warning(ctx, "save timewindow task failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessSetTWTaskStatusSuccess(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return GetUpdateTaskDetailProcessor("", "", cs_model.TW_TASK_STATUS_SUCCESS)(ctx, teu)
}

func isGlobalLeader(ctx context.Context, app *x1model.Application) bool {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				return true
			}
		}
	}
	return false
}

// ProcessSetHtGrpSlaveTaskSuccess 修改热活从地域任务状态
func ProcessSetHtGrpSlaveTaskSuccess(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	// 仅热活从地域生效
	if len(app.AppGroupID) != 0 && !isGlobalLeader(ctx, app) {
		if err = GetUpdateTaskDetailProcessor(AddShardTask, StepRebalance, TaskStatusExecuting)(ctx, teu); err != nil {
			resource.LoggerTask.Warning(ctx, "update task detail fail", logit.Error("error", err))
			return err
		}
		return GetUpdateTaskDetailProcessor("", "", cs_model.TW_TASK_STATUS_SUCCESS)(ctx, teu)
	}
	return nil
}
