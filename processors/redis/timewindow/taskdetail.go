package timewindow

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type updateTaskDetailPrams struct {
	TaskType    string                                   `json:"task_type"`
	CurTaskStep string                                   `json:"task_step"`
	TaskStatus  string                                   `json:"task_status"`
	App         *x1model.Application                     `json:"app"`
	Model       *csmaster_model_interface.TimeWindowTask `json:"model"`
}

func GetUpdateTaskDetailProcessor(taskType, taskStep, taskStatus string) func(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return func(ctx context.Context, teu *workflow.TaskExecUnit) error {
		resource.LoggerTask.Notice(ctx, "update task detail", logit.String("taskType", taskType),
			logit.String("taskStep", taskStep), logit.String("taskStatus", taskStatus))
		app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get app info fail", logit.Error("err", err))
			return err
		}
		taskDetailModel, err := getTaskDetailModel(ctx, teu, taskType, taskStep)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get task detail fail", logit.Error("err", err))
			return err
		}
		if err := updateTaskDetailModel(ctx, &updateTaskDetailPrams{
			TaskType:    taskType,
			CurTaskStep: taskStep,
			TaskStatus:  taskStatus,
			App:         app,
			Model:       taskDetailModel,
		}); err != nil {
			resource.LoggerTask.Warning(ctx, "update task detail fail", logit.Error("err", err))
			return err
		}
		if err := resource.CsmasterOpAgent.TimeWindowTaskSave(ctx, []*csmaster_model_interface.TimeWindowTask{taskDetailModel}); err != nil {
			resource.LoggerTask.Warning(ctx, "save task detail fail", logit.Error("err", err))
			return err
		}
		return nil
	}
}

func getTaskDetailModel(ctx context.Context, teu *workflow.TaskExecUnit, taskType, taskStep string) (*csmaster_model_interface.TimeWindowTask, error) {
	taskId := teu.PTaskID
	if taskId == "" {
		taskId = teu.TaskID
	}
	taskDetailModels, err := resource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(ctx, "task_id = ?", taskId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get task detail fail", logit.Error("err", err))
		return nil, err
	}
	if len(taskDetailModels) == 0 {
		clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get cluster model fail", logit.Error("err", err))
			return nil, err
		}
		return &csmaster_model_interface.TimeWindowTask{
			ClusterID:     int(clusterModel.Id),
			TaskStatus:    TaskStatusExecuting,
			CreateTime:    time.Now(),
			TaskType:      getShowTaskType(taskType),
			TaskParams:    teu.Parameters,
			TransactionID: taskId,
			ExecuteTime:   time.Now(),
			TaskFrom:      "x1-api",
			TaskID:        taskId,
		}, nil
	}
	return taskDetailModels[0], nil
}

func updateTaskDetailModel(ctx context.Context, params *updateTaskDetailPrams) error {
	taskList := getStepList(ctx, params.TaskType, params.App)
	var taskDetails []*TaskDetail
	if params.Model.TaskDetail != "" {
		if err := json.Unmarshal([]byte(params.Model.TaskDetail), &taskDetails); err != nil {
			resource.LoggerTask.Warning(ctx, "unmarshal task detail fail", logit.Error("err", err))
			return err
		}
	} else {
		for _, step := range taskList {
			taskDetails = append(taskDetails, &TaskDetail{
				StepName:  step,
				StepDesc:  stepDesc[step],
				Status:    TaskStatusWaiting,
				StartTime: time.Time{},
				EndTime:   time.Time{},
				Detail:    "",
			})
		}
	}
	switch params.TaskStatus {
	case TaskStatusSuccess, TaskStatusError:
		for _, taskDetail := range taskDetails {
			if taskDetail.Status == TaskStatusExecuting {
				taskDetail.Status = params.TaskStatus
				taskDetail.EndTime = time.Now()
			}
		}
		params.Model.TaskStatus = params.TaskStatus
	case TaskStatusExecuting:
		if inTaskSteps(taskList, params.CurTaskStep) {
			for _, taskDetail := range taskDetails {
				idx, curIdx := getStepsIdxs(taskList, taskDetail.StepName, params.CurTaskStep)
				if idx < curIdx {
					taskDetail.Status = TaskStatusSuccess
					taskDetail.EndTime = time.Now()
				} else if idx == curIdx {
					taskDetail.Status = TaskStatusExecuting
					taskDetail.StartTime = time.Now()
					break
				}
			}
		}
	default:
		return fmt.Errorf("task status %s not support", params.TaskStatus)
	}
	taskDetailBytes, err := json.Marshal(taskDetails)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "marshal task detail fail", logit.Error("err", err))
		return err
	}
	params.Model.TaskDetail = string(taskDetailBytes)
	return nil
}

func UpdateRebalanceProgress(ctx context.Context, taskID string, totalSlotCnt, succSlotCnt int) error {
	taskDetailModels, err := resource.CsmasterOpAgent.TimeWindowTaskGetAllByCond(ctx, "task_id = ?", taskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get task detail fail", logit.Error("err", err))
		return err
	}
	if len(taskDetailModels) == 0 {
		return nil
	}
	taskDetailModel := taskDetailModels[0]
	var taskDetails []*TaskDetail
	if taskDetailModel.TaskDetail != "" {
		if err := json.Unmarshal([]byte(taskDetailModel.TaskDetail), &taskDetails); err != nil {
			resource.LoggerTask.Warning(ctx, "unmarshal task detail fail", logit.Error("err", err))
			return err
		}
	}
	for _, taskDetail := range taskDetails {
		if taskDetail.StepName == StepRebalance {
			if succSlotCnt == 0 {
				taskDetail.Detail = fmt.Sprintf("正在进行数据槽重分配，总槽位数：%d", totalSlotCnt)
			} else if succSlotCnt < totalSlotCnt {
				taskDetail.Detail = fmt.Sprintf("正在进行数据槽重分配，总槽位数：%d，已完成槽位数：%d, 预估还需要%s完成",
					totalSlotCnt, succSlotCnt, formatDuration(getSlotsRebalanceCostTime(totalSlotCnt, succSlotCnt, taskDetail.StartTime)))
			} else {
				taskDetail.Detail = fmt.Sprintf("数据槽重分配已完成，总槽位数：%d，总耗时：%s",
					totalSlotCnt, formatDuration(time.Since(taskDetail.StartTime)))
			}
		}
	}
	taskDetailBytes, err := json.Marshal(taskDetails)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "marshal task detail fail", logit.Error("err", err))
		return err
	}
	taskDetailModel.TaskDetail = string(taskDetailBytes)
	if err := resource.CsmasterOpAgent.TimeWindowTaskSave(ctx, []*csmaster_model_interface.TimeWindowTask{taskDetailModel}); err != nil {
		resource.LoggerTask.Warning(ctx, "save task detail fail", logit.Error("err", err))
		return err
	}
	return nil
}

func getSlotsRebalanceCostTime(totalSlotCnt, succSlotCnt int, startedAt time.Time) time.Duration {
	costPerSlot := time.Since(startedAt) / time.Duration(succSlotCnt)
	return time.Duration(totalSlotCnt-succSlotCnt) * costPerSlot
}

func formatDuration(d time.Duration) string {
	totalSeconds := int(d.Seconds())
	hours := totalSeconds / 3600
	minutes := (totalSeconds % 3600) / 60
	seconds := totalSeconds % 60

	if hours > 0 {
		return fmt.Sprintf("%d小时%d分钟%d秒", hours, minutes, seconds)
	} else if minutes > 0 {
		return fmt.Sprintf("%d分钟%d秒", minutes, seconds)
	} else {
		return fmt.Sprintf("%d秒", seconds)
	}
}

func inTaskSteps(taskSteps []string, taskStep string) bool {
	for _, step := range taskSteps {
		if step == taskStep {
			return true
		}
	}
	return false
}

func getStepsIdxs(taskSteps []string, taskStep1 string, taskStep2 string) (int, int) {
	idx1, idx2 := -1, -1
	for i, step := range taskSteps {
		if step == taskStep1 {
			idx1 = i
		}
		if step == taskStep2 {
			idx2 = i
		}
	}
	return idx1, idx2
}
