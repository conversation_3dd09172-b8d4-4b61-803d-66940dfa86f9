/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2021/12/29, by shangshuai02(<EMAIL>), first version
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/bcc"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessInitAppBLB 初始化appBLB
// 1. 创建BLB；如果app是ipv6的同时创建ipv6的BLB
// 2. 调用Csmaster接口，将blb信息存入csmaster数据库
// 3. 创建BLB Listener，Listener以及Backend均为app的port
// 相关代码 RedisV7CreatingElbState::handle、RedisV7CreatingElbListenerState::handle
func ProcessInitAppBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	return processInitAppBLB(ctx, app, true)
}

func ProcessInitAppBLBNoUpdateCacheCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return processInitAppBLB(ctx, app, false)
}

func processInitAppBLB(ctx context.Context, app *x1model.Application, updateCacheCluster bool) error {
	// 对于私有部署的, 根据env有可能创建normal blb
	if util.UseNormalBlb() {
		if err := createNormalBLB(ctx, app); err != nil {
			return err
		}
		if err := createListener(ctx, app); err != nil {
			return err
		}
	}
	// 0. 若用户是公有云用户，且使用了虚机混布，则将公有云混布池的资源账户记录到数据库中
	env := blbv2.Instance().GetEnv(ctx)
	if !privatecloud.IsPrivateENV() && app.UserId != env.ResourcePrivateUserId && app.ResourceType == "container" {
		if err := saveResourceInfo(ctx, app); err != nil {
			return err
		}
	}

	// 1. 创建BLB并Save
	if err := createAppBLB(ctx, app); err != nil {
		return err
	}
	// // 判断blb是否可用
	// if err := checkAppBLBStatus(ctx, app); err != nil {
	// 	return err
	// }

	// 2. 调用Csmaster接口，将blb信息存入csmaster数据库
	if updateCacheCluster {
		if err := updateCsmasterBLBInfo(ctx, app); err != nil {
			return err
		}
	}

	// 3. 创建BLB ListenPort，Listner以及Backend均为app的port
	if err := createAppListener(ctx, app, x1model.BLBTypeApp); err != nil {
		return err
	}

	// 4. 创建blb ip group
	if err := createAppIPGroup(ctx, app, x1model.BLBTypeApp); err != nil {
		return err
	}

	// 5. 创建ipgroup policy
	if err := createAppIPGroupPolicy(ctx, app, x1model.BLBTypeApp); err != nil {
		return err
	}

	// 6. 创建监听器policy（绑定ipgroup）
	if err := createAppListenerPolicy(ctx, app, x1model.BLBTypeApp); err != nil {
		return err
	}

	// 为特定用户创建mcpack的监听器
	if err := CreateAppBlBComponentForMcpack(ctx, app, x1model.BLBTypeApp); err != nil {
		return err
	}

	return nil
}

// createBLB 创建BLB并Save
func createAppBLB(ctx context.Context, app *x1model.Application) error {
	// 获取待创建的BLB列表
	var newBLBParams []*blbv2.CreateBLBParams
	var newBlbList []*x1model.BLB
	var userId string
	var vpcId string
	var subnetId string
	// user master logicZone
	logicZone, err := getLogicZone(ctx, app)
	if err != nil {
		return err
	}

	// user master physicalZone
	physicalZone, err := getPhysicalZone(ctx, app)
	if err != nil {
		return err
	}

	for _, b := range app.BLBs {
		if len(b.BlbId) != 0 {
			continue
		}
		if len(b.AzoneForCrossAzNearest) > 0 && b.AzoneForCrossAzNearest != x1model.GlobalForCrossAzNearest {
			physicalZone = b.AzoneForCrossAzNearest
			logicZone, err = getLogicZoneByPhysicalZone(ctx, app.UserId, physicalZone)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get logic zone by physical zone failed",
					logit.String("appId", app.AppId),
					logit.String("physicalZone", physicalZone),
					logit.Error("error", err))
				return err
			}
		}

		// 设置创建 blb 的环境参数
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
			vpcId = b.VpcId
			subnetId = b.SubnetId
		} else {
			userId = b.ResourceUserId
			vpcId = b.ResourceVpcId
			subnetId = b.ResourceSubnetId
			// 使用资源池账号(资源账号或混合云管账号) 的 logicZone
			logicZone, err = getLogicZoneByPhysicalZone(ctx, userId, physicalZone)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get logic zone by physical zone failed",
					logit.String("appId", app.AppId),
					logit.String("physicalZone", physicalZone),
					logit.Error("error", err))
				return err
			}
		}

		vpcExchange := &bccresource.ExchangeIDParams{
			ObjectType:  bcc.ExchangeIDObjectTypeVpc,
			InstanceIds: []string{vpcId},
			UserID:      userId,
		}
		vpcMapping, err := bccresource.BccResourceOp().ExchangeProductLongIDToShort(ctx, vpcExchange)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "vpcid long2short fail",
				logit.String("appId", app.AppId),
				logit.String("params", base_utils.Format(vpcExchange)),
			)
			return err
		}
		var vpcShortID string
		for idx := range vpcMapping {
			if vpcMapping[idx].ID == vpcId {
				vpcShortID = vpcMapping[idx].UUID
			}
		}

		subnetExchange := &bccresource.ExchangeIDParams{
			ObjectType:  bcc.ExchangeIDObjectTypeSubnet,
			InstanceIds: []string{subnetId},
			UserID:      userId,
		}
		subnetMapping, err := bccresource.BccResourceOp().ExchangeProductLongIDToShort(ctx, subnetExchange)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "subnetid long2short fail",
				logit.String("appId", app.AppId),
				logit.String("params", base_utils.Format(subnetExchange)),
			)
			return err
		}
		var subnetShortID string
		for idx := range subnetMapping {
			if subnetMapping[idx].ID == subnetId {
				subnetShortID = subnetMapping[idx].UUID
			}
		}

		// 专属 blb
		layer4ClusterExclusive := false
		if b.BgwGroupExclusive == 1 {
			layer4ClusterExclusive = true
		}

		newBLBParams = append(newBLBParams, &blbv2.CreateBLBParams{
			VpcID:                  vpcShortID,
			SubnetID:               subnetShortID,
			Type:                   x1model.BLBTypeApp,
			Layer4MasterAz:         logicZone,
			Name:                   b.Name,
			Layer4ClusterID:        b.BgwGroupId,
			Layer4ClusterExclusive: layer4ClusterExclusive,
		})
		newBlbList = append(newBlbList, b)
	}

	if len(newBLBParams) == 0 {
		return nil
	}

	// 创建BLB
	createBlbParams := &blbv2.CreateBatchBLBParams{
		UserID:    userId,
		Product:   app.Product,
		BLBParams: newBLBParams,
	}
	blbList, err := blbv2.Instance().CreateBatchAppBLB(ctx, createBlbParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create blb fail",
			logit.String("appId", app.AppId),
			logit.String("params", base_utils.Format(createBlbParams)),
		)
		return errors.CreateBlbFail.Wrap(err)
	}

	// 更新数据库
	for k, b := range blbList {
		newBlbList[k].BlbId = b.BLBID
		newBlbList[k].Vip = ""
		newBlbList[k].Ovip = b.Address
		newBlbList[k].Ipv6 = ""
		newBlbList[k].Status = x1model.BLBStatusAvailable
	}
	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	if err := x1model.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

// createListener 创建Listener
func createAppListener(ctx context.Context, app *x1model.Application, blbType string) error {
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, b := range app.BLBs {
		if b.Type != blbType {
			continue
		}
		var userId string
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		createListenerParams := &blbv2.CommonListenerParams{
			UserID:       userId,
			BlbID:        b.BlbId,
			ListenerPort: app.Port,
			// 跨AZ就近访问，使用加权最小连接数
			// 默认还是使用非加权最小连接, 和BLB同学对过一次, 非加权最小连接在工作节点上每3秒同步各个worker记录的最小连接数, 而加权最小连接没有这个逻辑
			Scheduler: func() string {
				if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
					return "LeastConnection"
				}
				return "WeightLeastConn"
			}(),
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processCreateAppListener(ctx, createListenerParams)
			})
		})
		totalCount++
	}
	if totalCount == 0 {
		return nil
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app listeners",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doCreateAppListener", err))
		return err
	}

	return nil
}

// processCreateAppListener will check and create app blb listener
func processCreateAppListener(ctx context.Context, params *blbv2.CommonListenerParams) error {
	// desc listener
	listener, err := blbv2.Instance().DescAppTCPListener(ctx, params)
	if err != nil && !cerrs.ErrNotFound.Is(err) {
		resource.LoggerTask.Warning(ctx, "get app tcp listener failed,", logit.Error("err", err))
		return err
	}
	// update listener scheduler
	var curListener *blbv2.TCPListener
	for _, l := range listener {
		if l.ListenerPort == params.ListenerPort {
			curListener = &l
		}
	}
	if curListener != nil && curListener.Scheduler != params.Scheduler {
		if err = blbv2.Instance().UpdateAppTCPListener(ctx, params); err != nil {
			resource.LoggerTask.Warning(ctx, "update app tcp listener failed,", logit.Error("err", err))
			return err
		}
	}

	// create listener
	if curListener == nil {
		if err = blbv2.Instance().CreateAppTCPListener(ctx, params); err != nil {
			resource.LoggerTask.Warning(ctx, "create app tcp listener failed,", logit.Error("err", err))
			return err
		}
	}
	return nil
}

// ProcessCreateMcpackListenerWithAppBLB create mcpack listener
func ProcessCreateMcpackListenerWithAppBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	var BLBIDs []string
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Type == x1model.BLBTypeApp {
			BLBIDs = append(BLBIDs, b.BlbId)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}

	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}

	totalCount := 0
	for _, blb := range BLBIDs {
		createListenerParams := &blbv2.CommonListenerParams{
			UserID:       userId,
			BlbID:        blb,
			ListenerPort: app.McpackPort,
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processCreateAppListener(ctx, createListenerParams)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app listeners",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doCreateAppListener", err))
		return err
	}

	return nil
}

// createAppIPGroup 创建ip group
func createAppIPGroup(ctx context.Context, app *x1model.Application, blbType string) error {
	var BLBIDs []*x1model.BLB
	var userId string

	for _, b := range app.BLBs {
		// 过滤已创建完成的 ip_group
		if len(b.IPGroupID) != 0 {
			continue
		}

		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	// 创建IPGroup
	BLBIDs2IPGroups := make(map[string]string, 0)
	for _, blb := range BLBIDs {
		b := blb
		createIPGroupParams := &blbv2.CommonBLBParams{
			UserID: userId,
			ElbID:  b.BlbId,
		}
		id, err := blbv2.Instance().CreateAppIPGroup(ctx, createIPGroupParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur create app ip group", logit.Error("err", err))
			return err
		}
		BLBIDs2IPGroups[b.BlbId] = id
	}

	// 更新blb model, 将ipGroup写入数据库
	for _, blb := range BLBIDs {
		blb.IPGroupID = BLBIDs2IPGroups[blb.BlbId]
	}
	// 这里没有严格的进行并发限制，由于这里只是修改blb相关，blb相关的是串行的不会影响到其他的操作
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()

	if err := x1model.BLBsSave(ctx, BLBIDs); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

// createAppIPGroupPolicy 创建ip group policy
func createAppIPGroupPolicy(ctx context.Context, app *x1model.Application, blbType string) error {
	var BLBIDs []*x1model.BLB
	var userID string
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	// 创建IPGroup policy
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, blb := range BLBIDs {
		blb := blb
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return subprocessCreateAppIPGroupPolicy(ctx, userID, blb.BlbId, blb.IPGroupID)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("docreateAppIPGroupPolicy", err))
		return err
	}

	return nil
}

// createAppListenerPolicy 创建blb listener policy
func createAppListenerPolicy(ctx context.Context, app *x1model.Application, blbType string) error {
	var BLBIDs []*x1model.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}

	// 创建监听器 policy
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, blb := range BLBIDs {
		blb := blb
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return subprocessCreateAppPolicys(ctx, userID, blb.BlbId, app.Port, blb.IPGroupID)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("docreateAppIPGroupPolicy", err))
		return err
	}

	return nil
}

func subprocessCreateAppPolicys(ctx context.Context, userID string, blbID string, port int, IPGroupID string) error {
	// check
	listPolicysParams := &blbv2.ListAppPolicysParams{
		UserID: userID,
		BlbID:  blbID,
		Port:   port,
		Type:   "TCP",
	}
	policysList, err := blbv2.Instance().ListAppPolicys(ctx, listPolicysParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur list app policys",
			logit.String("blbID", blbID),
			logit.Int("port", port),
			logit.Error("ListAppPolicys", err))
		return err
	}

	if len(policysList) == 1 {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("blb=%s policy is exist", blbID))
		return nil
	}

	// app policy
	appPolicy := &blbv2.AppPolicyVos{
		IPGroupID: IPGroupID,
		Priority:  100,
		Desc:      "",
		RuleList: []*blbv2.RuleList{
			{
				Key:   "*",
				Value: "*",
			},
		},
	}
	createPolicyParams := &blbv2.CreateAppPolicysParams{
		UserID:       userID,
		BlbID:        blbID,
		ListenerPort: port,
		Type:         "TCP",
		GroupType:    "ip",
		AppPolicyVos: []*blbv2.AppPolicyVos{appPolicy},
	}

	return blbv2.Instance().CreateAppPolicys(ctx, createPolicyParams)
}

func checkAppBLBStatus(ctx context.Context, app *x1model.Application) error {
	var BLBIDs []*x1model.BLB
	var userId string

	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Type == x1model.BLBTypeApp {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	for _, blb := range BLBIDs {
		count := 0
	for1:
		for {
			// 获取应用型BLB详情
			getParams := blbv2.CommonBLBParams{
				UserID: userId,
				ElbID:  blb.BlbId,
			}
			resp, err := blbv2.Instance().GetAppBLBDetail(ctx, &getParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get app blb faield", logit.Error("error", err))
				return err
			}

			switch resp.Status {
			case "avaiable":
				break for1
			case "creating":
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("blb %s status creating", blb.BlbId))
				count++
			default:
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("blb %s status not in creating or avaiable, create failed.", blb.BlbId))
				return errors.CreateBlbFail
			}
			select {
			case <-time.After(5 * time.Second):
				if count > 3 {
					resource.LoggerTask.Warning(ctx, fmt.Sprintf("blb %s create timeout", blb.BlbId))
					return errors.CreateBlbFail
				}
			}
		}
	}
	return nil
}

// CreateAppBlBComponentForMcpack will register app blb component for mcpack protocol
func CreateAppBlBComponentForMcpack(ctx context.Context, app *x1model.Application, blbType string) error {
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}
	if err := createAppMcpackListener(ctx, app, blbType); err != nil {
		return err
	}

	// 创建blb ip group
	if err := createAppMcpackIPGroup(ctx, app, blbType); err != nil {
		return err
	}

	// 创建ipgroup policy
	if err := createAppMcpackIPGroupPolicy(ctx, app, blbType); err != nil {
		return err
	}

	// 创建监听器policy（绑定ipgroup）
	if err := createAppMcpackListenerPolicy(ctx, app, blbType); err != nil {
		return err
	}

	return nil
}

func createAppMcpackListener(ctx context.Context, app *x1model.Application, blbType string) error {
	var BLBIDs []string
	var userID string

	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b.BlbId)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}

	totalCount := 0
	for _, blb := range BLBIDs {
		createListenerParams := &blbv2.CommonListenerParams{
			UserID:       userID,
			BlbID:        blb,
			ListenerPort: app.McpackPort,
			Scheduler:    "LeastConnection",
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return processCreateAppListener(ctx, createListenerParams)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app listeners",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doCreateAppMcpackListener", err))
		return err
	}

	return nil
}

func createAppMcpackIPGroup(ctx context.Context, app *x1model.Application, blbType string) error {
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	var BLBIDs []*x1model.BLB
	var userID string

	for _, b := range app.BLBs {
		// 过滤已创建的 ip_group
		if len(b.McpackIPGroupID) != 0 {
			continue
		}

		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	// 创建IPGroup
	BLBIDs2IPGroups := make(map[string]string, 0)
	for _, blb := range BLBIDs {
		b := blb
		createIPGroupParams := &blbv2.CommonBLBParams{
			UserID: userID,
			ElbID:  b.BlbId,
		}
		id, err := blbv2.Instance().CreateAppIPGroup(ctx, createIPGroupParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur create app ip group", logit.Error("err", err))
			return err
		}
		BLBIDs2IPGroups[b.BlbId] = id
	}

	// 更新blb model, 将ipGroup写入数据库
	for _, blb := range BLBIDs {
		blb.McpackIPGroupID = BLBIDs2IPGroups[blb.BlbId]
	}

	if err := x1model.BLBsSave(ctx, BLBIDs); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

func createAppMcpackIPGroupPolicy(ctx context.Context, app *x1model.Application, blbType string) error {
	var BLBIDs []*x1model.BLB
	var userID string
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}
	// 创建IPGroup policy
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, blb := range BLBIDs {
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return subprocessCreateAppIPGroupPolicy(ctx, userID, blb.BlbId, blb.McpackIPGroupID)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("docreateAppMcpackIPGroupPolicy", err))
		return err
	}

	return nil
}

func subprocessCreateAppIPGroupPolicy(ctx context.Context, userID string, blbID string, IPGroupID string) error {
	// check(没有 ipgroup 详情接口，通过 ipgroup 列表查询)
	listIPGroupParams := &blbv2.CommonBLBParams{
		UserID: userID,
		ElbID:  blbID,
	}
	ipgroupList, err := blbv2.Instance().ListAppIPGroup(ctx, listIPGroupParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur list app ip group",
			logit.String("blbID", blbID),
			logit.Error("ListAppIPGroup", err))
		return err
	}

	var isIPGroupPolicyExist bool
	isIPGroupPolicyExist = false
	for _, ipgroup := range ipgroupList {
		if ipgroup.ID == IPGroupID && len(ipgroup.BackendPolicyList) == 1 {
			isIPGroupPolicyExist = true
		}
	}

	if isIPGroupPolicyExist {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("blb=%s ipgroup=%s ip_group_policy is exist", blbID, IPGroupID))
		return nil
	}

	// create
	createIPGroupPolicyParams := &blbv2.CommonIPGroupParams{
		UserID:    userID,
		BlbID:     blbID,
		IPGroupID: IPGroupID,
		Type:      "TCP",
	}
	return blbv2.Instance().CreateAppIPGroupPolicy(ctx, createIPGroupPolicyParams)
}

func createAppMcpackListenerPolicy(ctx context.Context, app *x1model.Application, blbType string) error {
	var BLBIDs []*x1model.BLB
	var userID string

	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = b.ResourceUserId
		}
		if b.Type == blbType {
			BLBIDs = append(BLBIDs, b)
		}
	}
	if len(BLBIDs) == 0 {
		return nil
	}

	// 创建监听器 policy
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	totalCount := 0
	for _, blb := range BLBIDs {
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return subprocessCreateAppPolicys(ctx, userID, blb.BlbId, app.McpackPort, blb.McpackIPGroupID)
			})
		})
		totalCount++
	}

	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("docreateAppMcpackIPGroupPolicy", err))
		return err
	}

	return nil
}

func createNormalBLB(ctx context.Context, app *x1model.Application) error {
	for _, b := range app.BLBs {
		if b.Type != x1model.BLBTypeNormal {
			continue
		}
		b.Name = fmt.Sprintf("scs-lb-%s", app.AppId)
		params := &blb.CreateBLBParam{
			UserID:  app.UserId,
			Product: "scs",
			BLBParams: []*blb.BLBParam{
				{
					Name:              b.Name,
					VpcId:             b.VpcId,
					SubnetId:          b.SubnetId,
					IpType:            b.IpType,
					BgwGroupId:        b.BgwGroupId,
					BgwGroupExclusive: false,
					BgwGroupMode:      b.BgwGroupMode,
					MasterAZ:          b.MasterAZ,
					SlaveAZ:           b.SlaveAZ,
				},
			},
		}
		blbList, err := blb.Instance().CreateBLB(ctx, params)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "create blb fail",
				logit.String("appId", app.AppId),
				logit.String("params", base_utils.Format(params)),
				logit.Error("err", err))
			return err
		}
		if len(blbList) == 0 {
			resource.LoggerTask.Warning(ctx, "create blb fail, no blb returns",
				logit.String("appId", app.AppId),
				logit.String("params", base_utils.Format(params)))
			return fmt.Errorf("create blb fail, no blb returns")
		}
		b.BlbId = blbList[0].BLBID
		b.Ovip = blbList[0].OVip
		b.Status = x1model.BLBStatusAvailable
		b.Vip = ""
		if err := x1model.BLBsSave(ctx, []*x1model.BLB{b}); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}
	return nil
}
