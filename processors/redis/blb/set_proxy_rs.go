package blb

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	libErrors "icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessSetProxyRs process proxy rs include app blb
func ProcessSetProxyRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := updateProxyBinding(ctx, app); err != nil {
		return err
	}
	return updateBlbIPs(ctx, app)
}

func ProcessSetProxyRsForModify(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := checkProxyAlive(ctx, app); err != nil {
		return err
	}
	return updateProxyBinding(ctx, app)
}

// ProcessSetProxyRsForMcpack process proxy rs include app blb for mcpack
func ProcessSetProxyRsForMcpack(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	// 标准版升级到集群版后，没有 mcpack 端口
	if app.McpackPort == 0 {
		return nil
	}
	return updateMcpackProxyBinding(ctx, app)
}

func checkProxyAlive(ctx context.Context, app *x1model.Application) error {
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model fail", logit.String("appId", app.AppId),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if len(clusterModel.ClientAuth) == 0 {
		return nil
	}
	g := gtask.Group{Concurrent: 20}
	checkTimeout := 30 * time.Second
	checkInterval := 1 * time.Second
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			proxy := proxy
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					c := single_redis.NewClient(proxy.FloatingIP, proxy.Port,
						single_redis.WithPassword(clusterModel.ClientAuth),
						single_redis.WithRetry(0),
					)
					defer c.Close()
					subctx, cancel := context.WithTimeout(ctx, checkTimeout)
					defer cancel()
					for {
						select {
						case <-subctx.Done():
							return fmt.Errorf("check redis proxy(%s:%d) fail: %s", proxy.FloatingIP, proxy.Port, "timeout")
						default:
							if _, err := c.Ping(subctx).Result(); err != nil {
								time.Sleep(checkInterval)
								continue
							}
							return nil
						}
					}
				})
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check redis proxy fail", logit.Error("error", err))
		return err
	}
	return nil
}

func updateProxyBinding(ctx context.Context, app *x1model.Application) error {
	blbs := make([]*x1model.BLB, 0)
	appBlbs := make([]*x1model.BLB, 0)

	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			blbs = append(blbs, blb)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			appBlbs = append(appBlbs, blb)
		}
	}

	if len(blbs) <= 0 && len(appBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	tobindProxys := make([]*x1model.Proxy, 0)
	unbindProxys := make([]*x1model.Proxy, 0)

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToCreate:
				tobindProxys = append(tobindProxys, proxy)
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				unbindProxys = append(unbindProxys, proxy)
			}
		}
	}

	if err := updateProxyRsBindingByBLBList(ctx, app, blbs, tobindProxys, unbindProxys, false); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByBLBList fail", logit.Error("err", err))
		return err
	}

	if err := updateProxyRsForAppBlbs(ctx, appBlbs, app, false); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsForAppBlbs fail", logit.Error("err", err))
		return err
	}
	return nil
}

func updateMcpackProxyBinding(ctx context.Context, app *x1model.Application) error {
	blbs := make([]*x1model.BLB, 0)
	appBlbs := make([]*x1model.BLB, 0)

	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			blbs = append(blbs, blb)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			appBlbs = append(appBlbs, blb)
		}
	}

	if len(blbs) <= 0 && len(appBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	tobindProxys := make([]*x1model.Proxy, 0)
	unbindProxys := make([]*x1model.Proxy, 0)

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToCreate:
				tobindProxys = append(tobindProxys, proxy)
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				unbindProxys = append(unbindProxys, proxy)
			}
		}
	}

	if err := updateProxyRsBindingByBLBList(ctx, app, blbs, tobindProxys, unbindProxys, true); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByBLBList fail", logit.Error("err", err))
		return err
	}

	if err := updateProxyRsForAppBlbs(ctx, appBlbs, app, true); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsForAppBlbs fail", logit.Error("err", err))
		return err
	}
	return nil
}

func updateProxyRsBindingByBLBList(ctx context.Context, app *x1model.Application, blbList []*x1model.BLB,
	bindProxys []*x1model.Proxy, unbindProxys []*x1model.Proxy, isMcpack bool) error {

	resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(base_utils.SelectColumn(bindProxys, "ProxyId"))),
		logit.String("blb", base_utils.Format(blbList)), logit.Bool("isMcpack", isMcpack))
	resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(base_utils.SelectColumn(unbindProxys, "ProxyId"))),
		logit.String("blb", base_utils.Format(blbList)), logit.Bool("isMcpack", isMcpack))

	var userId string
	if len(blbList) == 0 {
		return nil
	}

	blbIds := make([]string, 0)
	for _, b := range blbList {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		blbIds = append(blbIds, b.BlbId)
	}

	// 绑定rss
	if len(bindProxys) > 0 {
		rss := make([]*blb.Rs, 0)
		for _, n := range bindProxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			if isMcpack {
				rss = append(rss, &blb.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   int32(n.McpackPort),
				})
			} else {
				rss = append(rss, &blb.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   int32(n.Port),
				})
			}
		}

		bindReq := &blb.BindRsParam{
			UserID: userId,
			BLBIDs: blbIds,
			Rss:    rss,
		}
		// 里面会做是否已经绑定的判断
		if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("rss", base_utils.Format(rss)))
			return libErrors.BindRsFail.Wrap(err)
		}
	}

	// 解绑rss
	if len(unbindProxys) > 0 {
		uuidList := make([]string, 0)
		for _, n := range unbindProxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			uuidList = append(uuidList, uuid)
		}
		unbindReq := &blb.UnbindRsParam{
			UserID: userId,
			BLBIDs: blbIds,
			UUIDs:  uuidList,
		}
		// 里面会做是否已经解绑的判断
		if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("UUIDs", base_utils.Format(uuidList)))
			return libErrors.UnBindRsFail.Wrap(err)
		}
	}
	return nil
}

func updateProxyRsForAppBlbs(ctx context.Context, blbs []*x1model.BLB, app *x1model.Application, isMcpack bool) error {
	g := gtask.Group{
		Concurrent: 3,
	}
	for _, b := range blbs {
		b := b
		userId := app.UserId
		if len(b.ResourceUserId) > 0 {
			userId = b.ResourceUserId
		}
		curMembers, err := blbv2.Instance().ListAppIPGroupMember(ctx, &blbv2.CommonIPGroupParams{
			BlbID:     b.BlbId,
			IPGroupID: getIPGroupID(b, isMcpack),
			UserID:    userId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get blb members fail",
				logit.Error("error", err), logit.String("blb_id", b.BlbId))
			return err
		}
		resource.LoggerTask.Trace(ctx, "get blb members success",
			logit.String("resp", base_utils.Format(curMembers)), logit.String("blb_id", b.BlbId))
		toBindProxyRs := getToBindProxyRs(curMembers, b, app, isMcpack)
		resource.LoggerTask.Trace(ctx, "to bind proxy rs",
			logit.String("resp", base_utils.Format(toBindProxyRs)), logit.String("blb_id", b.BlbId))
		if len(toBindProxyRs) > 0 {
			g.Go(func() error {
				if err := blbv2.Instance().BindRs(ctx, &blbv2.BindRsParams{
					UserID:  userId,
					BLBID:   b.BlbId,
					IPGroup: getIPGroupID(b, isMcpack),
					Rss:     toBindProxyRs,
				}); err != nil {
					resource.LoggerTask.Warning(ctx, "bind proxy rs fail",
						logit.Error("error", err), logit.String("req", base_utils.Format(toBindProxyRs)),
						logit.String("blb_id", b.BlbId))
					return err
				}
				return nil
			})
		}
		toUpdateProxyRs := getToUpdateProxyRs(curMembers, b, app, isMcpack)
		resource.LoggerTask.Trace(ctx, "to update proxy rs",
			logit.String("resp", base_utils.Format(toUpdateProxyRs)), logit.String("blb_id", b.BlbId))
		if len(toUpdateProxyRs) > 0 {
			g.Go(func() error {
				if err := blbv2.Instance().UpdateElbsRsForRo(ctx, &blbv2.UpdateElbRsForRoParams{
					UserID:     userId,
					ToUpdateRs: toUpdateProxyRs,
					BLBID:      b.BlbId,
					IPGroupID:  getIPGroupID(b, isMcpack),
				}); err != nil {
					resource.LoggerTask.Warning(ctx, "update proxy rs fail",
						logit.Error("error", err), logit.String("req", base_utils.Format(toUpdateProxyRs)),
						logit.String("blb_id", b.BlbId))
					return err
				}
				return nil
			})
		}
		toUnbindRs := getToUnbindProxyRs(curMembers, b, app, isMcpack)
		resource.LoggerTask.Trace(ctx, "to unbind proxy rs",
			logit.String("resp", base_utils.Format(toUnbindRs)), logit.String("blb_id", b.BlbId))
		if len(toUnbindRs) > 0 {
			g.Go(func() error {
				if err := blbv2.Instance().UnbindRs(ctx, &blbv2.UnbindRsParams{
					UserID:     userId,
					BLBID:      b.BlbId,
					IPGroup:    getIPGroupID(b, isMcpack),
					MemberList: toUnbindRs,
				}); err != nil {
					resource.LoggerTask.Warning(ctx, "unbind proxy rs fail",
						logit.Error("error", err), logit.String("req", base_utils.Format(toUnbindRs)),
						logit.String("blb_id", b.BlbId))
					return err
				}
				return nil
			})
		}
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update proxy rs for app blbs fail",
			logit.Error("error", err), logit.String("app_id", app.AppId))
		return err
	}
	return nil
}

func getToBindProxyRs(member []blbv2.Member, b *x1model.BLB, app *x1model.Application, isMcpack bool) []*blbv2.Rs {
	var toBindRs []*blbv2.Rs
	for _, itf := range app.Interfaces {
		for _, p := range itf.Proxys {
			if p.Status != x1model.NodeOrProxyStatusInUse && p.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			foundInMember := false
			for _, m := range member {
				if proxyMatchMember(&m, p, isMcpack) {
					foundInMember = true
					break
				}
			}
			if !foundInMember {
				toBindRs = append(toBindRs, &blbv2.Rs{
					UUID:   getProxyUUID(p, app),
					IP:     p.Ip,
					Weight: getProxyWeight(b, p),
					Port:   getProxyPort(p, isMcpack),
				})
			}
		}
	}
	return toBindRs
}

func getToUpdateProxyRs(member []blbv2.Member, b *x1model.BLB, app *x1model.Application, isMcpack bool) []*blbv2.Rs {
	var toUpdateRs []*blbv2.Rs
	for _, itf := range app.Interfaces {
		for _, p := range itf.Proxys {
			if p.Status != x1model.NodeOrProxyStatusInUse && p.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			for _, m := range member {
				if proxyMatchMember(&m, p, isMcpack) && m.Weight != getProxyWeight(b, p) {
					toUpdateRs = append(toUpdateRs, &blbv2.Rs{
						UUID:   getProxyUUID(p, app),
						IP:     p.Ip,
						Weight: getProxyWeight(b, p),
						Port:   getProxyPort(p, isMcpack),
					})
				}
			}
		}
	}
	return toUpdateRs
}

func getToUnbindProxyRs(member []blbv2.Member, b *x1model.BLB, app *x1model.Application, isMcpack bool) []string {
	var toUnbindRs []string
	for _, itf := range app.Interfaces {
		for _, p := range itf.Proxys {
			if p.Status != x1model.NodeOrProxyStatusToDelete && p.Status != x1model.NodeOrProxyStatusToFakeDelete {
				continue
			}
			for _, m := range member {
				if proxyMatchMember(&m, p, isMcpack) {
					toUnbindRs = append(toUnbindRs, fmt.Sprintf("%s:%d", p.Ip, getProxyPort(p, isMcpack)))
					break
				}
			}
		}
	}
	return toUnbindRs
}

func proxyMatchMember(member *blbv2.Member, p *x1model.Proxy, isMcpack bool) bool {
	if isMcpack {
		return member.IP == p.Ip && member.Port == p.McpackPort
	}
	return member.IP == p.Ip && member.Port == p.Port
}

func getProxyUUID(p *x1model.Proxy, app *x1model.Application) string {
	if app.ResourceType == "container" {
		return p.ContainerId
	}
	return p.ResourceId
}

func getProxyPort(p *x1model.Proxy, isMcpack bool) int {
	if isMcpack {
		return p.McpackPort
	}
	return p.Port
}

func getProxyWeight(b *x1model.BLB, p *x1model.Proxy) int {
	if len(b.AzoneForCrossAzNearest) == 0 ||
		b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest ||
		b.AzoneForCrossAzNearest == p.Azone {
		return 1
	}
	return 0
}

func updateProxyRsBindingByAppBLBList(ctx context.Context, app *x1model.Application, blbList []*x1model.BLB,
	bindProxys []*x1model.Proxy, unbindProxys []*x1model.Proxy, isMcpack bool) error {

	resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(base_utils.SelectColumn(bindProxys, "ProxyId"))),
		logit.String("blb", base_utils.Format(blbList)), logit.Bool("isMcpack", isMcpack))
	resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(base_utils.SelectColumn(unbindProxys, "ProxyId"))),
		logit.String("blb", base_utils.Format(blbList)), logit.Bool("isMcpack", isMcpack))
	var userId string
	if len(blbList) == 0 {
		return nil
	}

	blbIds := make([]string, 0)
	for _, b := range blbList {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		blbIds = append(blbIds, b.BlbId)
	}

	resource.LoggerTask.Notice(ctx, "IAM USER ID", logit.String("userId", userId))
	// 绑定rss
	if len(bindProxys) > 0 {
		rss := make([]*blbv2.Rs, 0)
		for _, n := range bindProxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			if isMcpack {
				rss = append(rss, &blbv2.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   n.McpackPort,
					IP:     n.Ip,
				})
			} else {
				rss = append(rss, &blbv2.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   n.Port,
					IP:     n.Ip,
				})
			}
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbList {
			blb := blb
			bindReq := &blbv2.BindRsParams{
				UserID:  userId,
				BLBID:   blb.BlbId,
				IPGroup: getIPGroupID(blb, isMcpack),
				Rss:     rss,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().BindRs(ctx, bindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2BindRs", err))
			return err
		}
	}

	// 解绑rss
	if len(unbindProxys) > 0 {
		IPList := make([]string, 0)
		for _, n := range unbindProxys {
			rsIpPort := ""
			if isMcpack {
				rsIpPort = fmt.Sprintf("%s:%d", n.Ip, n.McpackPort)
			} else {
				rsIpPort = fmt.Sprintf("%s:%d", n.Ip, n.Port)
			}
			IPList = append(IPList, rsIpPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbList {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userId,
				BLBID:      blb.BlbId,
				IPGroup:    getIPGroupID(blb, isMcpack),
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}

	return nil
}
