/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/18 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file set_rs_readonly.go
 * <AUTHOR>
 * @date 2022/05/18 17:42:18
 * @brief set rs of readonly
 *
 **/

package blb

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessSetReadonlyInstRs process set rs of readonly blb
func ProcessSetReadonlyInstRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 绑定rs
	roNodes := make([]*x1model.RoNode, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.RoGroupID == roGroup.RoGroupShowID {
				roNodes = append(roNodes, node)
			}
		}
	}

	blbIds := make([]string, 0)
	appBlbIds := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeReadOnly {
			blbIds = append(blbIds, b.BlbId)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeAppReadOnly {
			appBlbIds = append(appBlbIds, b)
		}
	}

	if len(blbIds) == 0 && len(appBlbIds) == 0 {
		resource.LoggerTask.Error(ctx, "no blb ids, skip ProcessSetReadonlyInstRs")
		return nil
	}

	if len(blbIds) > 0 {
		if len(roNodes) > 0 {
			rss := make([]*blb.Rs, 0)
			for _, n := range roNodes {
				var uuid string
				if app.ResourceType == "container" {
					uuid = n.ContainerId
				} else {
					uuid = n.ResourceId
				}
				rss = append(rss, &blb.Rs{
					UUID:   uuid,
					Weight: int32(n.RoGroupWeight),
					Port:   int32(n.Port),
				})
			}

			bindReq := &blb.BindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				Rss:    rss,
			}
			// 里面会做是否已经绑定的判断
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("rss", base_utils.Format(rss)))
				return errors.BindRsFail.Wrap(err)
			}
		}
	}
	if len(appBlbIds) > 0 {
		if len(roNodes) > 0 {
			rss := make([]*blbv2.Rs, 0)
			for _, n := range roNodes {
				var uuid string
				if app.ResourceType == "container" {
					uuid = n.ContainerId
				} else {
					uuid = n.ResourceId
				}
				rss = append(rss, &blbv2.Rs{
					UUID:   uuid,
					Weight: n.RoGroupWeight,
					Port:   n.Port,
					IP:     n.Ip,
				})
			}

			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIds {
				blb := blb
				bindReq := &blbv2.BindRsParams{
					UserID:  userId,
					BLBID:   blb.BlbId,
					IPGroup: blb.IPGroupID,
					Rss:     rss,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().BindRs(ctx, bindReq)
					})
				})
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
					logit.String("appId", app.AppId),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2BindRs", err))
				return err
			}
		}
	}
	return nil
}

// ProcessSetReadonlyGroupRs will check all ro group of a cluster
func ProcessSetReadonlyGroupRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 不是标准版直接返回
	if app.Type != x1model.AppTypeStandalone {
		return nil
	}

	// 记录只读组和需要绑定的只读节点
	RoGroupID2InstsNeedBind := make(map[string][]*x1model.RoNode)
	RoGroupID2InstsNeedUnbind := make(map[string][]*x1model.RoNode)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if isRoNodeAvailable(node) {
				RoGroupID2InstsNeedBind[node.RoGroupID] = append(RoGroupID2InstsNeedBind[node.RoGroupID], node)
			} else {
				RoGroupID2InstsNeedUnbind[node.RoGroupID] = append(RoGroupID2InstsNeedUnbind[node.RoGroupID], node)
			}
		}
	}
	// 获取只读组与blb
	RoGroupID2BlbID := make(map[string][]string)
	RoGroupID2AppBlbID := make(map[string][]*x1model.BLB)
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if len(b.RoGroupID) != 0 && b.Type == x1model.BLBTypeReadOnly {
			RoGroupID2BlbID[b.RoGroupID] = append(RoGroupID2BlbID[b.RoGroupID], b.BlbId)
		}
		if len(b.RoGroupID) != 0 && b.Type == x1model.BLBTypeAppReadOnly {
			RoGroupID2AppBlbID[b.RoGroupID] = append(RoGroupID2AppBlbID[b.RoGroupID], b)
		}
	}

	for rogroup, roInst := range RoGroupID2InstsNeedBind {
		blbs := RoGroupID2BlbID[rogroup]
		if len(blbs) == 0 {
			resource.LoggerTask.Debug(ctx, "no blb to bind rs, skip", logit.String("appId", teu.Entity))
			continue
		}
		if err := BindReadonlyRs(ctx, app, roInst, blbs, userId); err != nil {
			resource.LoggerTask.Warning(ctx, "bind ro rs to blb failed", logit.String("appId", teu.Entity))
			return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
		}
	}

	for rogroup, roInst := range RoGroupID2InstsNeedUnbind {
		blbs := RoGroupID2BlbID[rogroup]
		if len(blbs) == 0 {
			resource.LoggerTask.Debug(ctx, "no blb to unbind rs, skip", logit.String("appId", teu.Entity))
			continue
		}
		if err := UnbindReadonlyRs(ctx, app, roInst, blbs, userId); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind ro rs to blb failed", logit.String("appId", teu.Entity))
			return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
		}
	}

	for rogroup, roInst := range RoGroupID2InstsNeedBind {
		blbs := RoGroupID2AppBlbID[rogroup]
		if len(blbs) == 0 {
			resource.LoggerTask.Debug(ctx, "no appblb to bind rs, skip", logit.String("appId", teu.Entity))
			continue
		}
		if err := BindReadonlyAppRs(ctx, app, roInst, blbs, userId); err != nil {
			resource.LoggerTask.Warning(ctx, "bind ro rs to blb failed", logit.String("appId", teu.Entity))
			return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
		}
	}

	for rogroup, roInst := range RoGroupID2InstsNeedUnbind {
		blbs := RoGroupID2AppBlbID[rogroup]
		if len(blbs) == 0 {
			resource.LoggerTask.Debug(ctx, "no appblb to unbind rs, skip", logit.String("appId", teu.Entity))
			continue
		}
		if err := UnbindReadonlyAppRs(ctx, app, roInst, blbs, userId); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind ro rs to appblb failed", logit.String("appId", teu.Entity))
			return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
		}
	}

	return nil
}

// BindReadonlyRs bind readonly inst to group blb
func BindReadonlyRs(ctx context.Context, app *x1model.Application, roInsts []*x1model.RoNode, blbIds []string, userId string) error {
	if len(roInsts) > 0 {
		rss := make([]*blb.Rs, 0)
		for _, n := range roInsts {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			rss = append(rss, &blb.Rs{
				UUID:   uuid,
				Weight: int32(n.RoGroupWeight),
				Port:   int32(n.Port),
			})
		}

		bindReq := &blb.BindRsParam{
			UserID: userId,
			BLBIDs: blbIds,
			Rss:    rss,
		}
		// 里面会做是否已经绑定的判断
		if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("rss", base_utils.Format(rss)))
			return errors.BindRsFail.Wrap(err)
		}
	}
	return nil
}

// UnbindReadonlyRs bind readonly inst to group blb
func UnbindReadonlyRs(ctx context.Context, app *x1model.Application, roInsts []*x1model.RoNode, blbIds []string, userId string) error {
	if len(roInsts) > 0 {
		rss := make([]string, 0)
		for _, n := range roInsts {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			rss = append(rss, uuid)
		}

		unbindReq := &blb.UnbindRsParam{
			UserID: userId,
			BLBIDs: blbIds,
			UUIDs:  rss,
		}
		// 里面会做是否已经绑定的判断
		if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("rss", base_utils.Format(rss)))
			return errors.BindRsFail.Wrap(err)
		}
	}
	return nil
}

// BindReadonlyAppRs bind readonly inst to group blb
func BindReadonlyAppRs(ctx context.Context, app *x1model.Application, roInsts []*x1model.RoNode, blbIds []*x1model.BLB, userId string) error {
	if len(roInsts) > 0 {
		rss := make([]*blbv2.Rs, 0)
		for _, n := range roInsts {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			rss = append(rss, &blbv2.Rs{
				UUID:   uuid,
				Weight: n.RoGroupWeight,
				Port:   n.Port,
				IP:     n.Ip,
			})
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbIds {
			blb := blb
			bindReq := &blbv2.BindRsParams{
				UserID:  userId,
				BLBID:   blb.BlbId,
				IPGroup: blb.IPGroupID,
				Rss:     rss,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().BindRs(ctx, bindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2BindRs", err))
			return err
		}
	}
	return nil
}

// UnbindReadonlyAppRs bind readonly inst to group blb
func UnbindReadonlyAppRs(ctx context.Context, app *x1model.Application, roInsts []*x1model.RoNode, blbIds []*x1model.BLB, userId string) error {
	if len(roInsts) > 0 {
		IPList := make([]string, 0)
		for _, n := range roInsts {
			rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
			IPList = append(IPList, rsIpPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbIds {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userId,
				BLBID:      blb.BlbId,
				IPGroup:    blb.IPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}
	return nil
}

func ProcessDeleteReadonlyGroupRsForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 不是标准版直接返回
	if app.Type != x1model.AppTypeStandalone {
		return nil
	}

	// 记录需要删除的只读节点
	RoGroupID2InstsNeedUnbind := make(map[string][]*x1model.RoNode)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if !isRoNodeAvailable(node) {
				RoGroupID2InstsNeedUnbind[node.RoGroupID] = append(RoGroupID2InstsNeedUnbind[node.RoGroupID], node)
			}
		}
	}
	// 获取只读组与blb
	RoGroupID2BlbID := make(map[string][]string)
	RoGroupID2AppBlbID := make(map[string][]*x1model.BLB)
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if len(b.RoGroupID) != 0 && b.Type == x1model.BLBTypeReadOnly {
			RoGroupID2BlbID[b.RoGroupID] = append(RoGroupID2BlbID[b.RoGroupID], b.BlbId)
		}
		if len(b.RoGroupID) != 0 && b.Type == x1model.BLBTypeAppReadOnly {
			RoGroupID2AppBlbID[b.RoGroupID] = append(RoGroupID2AppBlbID[b.RoGroupID], b)
		}
	}

	for rogroup, roInst := range RoGroupID2InstsNeedUnbind {
		blbs := RoGroupID2BlbID[rogroup]
		if len(blbs) == 0 {
			resource.LoggerTask.Debug(ctx, "no blb to unbind rs, skip", logit.String("appId", teu.Entity))
			continue
		}
		if err := UnbindReadonlyRs(ctx, app, roInst, blbs, userId); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind ro rs to blb failed", logit.String("appId", teu.Entity))
			return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
		}
	}

	for rogroup, roInst := range RoGroupID2InstsNeedUnbind {
		blbs := RoGroupID2AppBlbID[rogroup]
		if len(blbs) == 0 {
			resource.LoggerTask.Debug(ctx, "no appblb to unbind rs, skip", logit.String("appId", teu.Entity))
			continue
		}
		if err := UnbindReadonlyAppRs(ctx, app, roInst, blbs, userId); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind ro rs to appblb failed", logit.String("appId", teu.Entity))
			return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
		}
	}

	return nil
}
