package blb

// import (
// 	"context"

// 	"icode.baidu.com/baidu/gdp/extension/gtask"
// 	"icode.baidu.com/baidu/gdp/logit"
// 	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
// 	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
// 	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
// 	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

// 	"icode.baidu.com/baidu/scs/x1-task/library/resource"
// )

// // ProcessInitAppBLB 初始化appBLB
// // 1. 创建BLB；如果app是ipv6的同时创建ipv6的BLB
// // 2. 调用Csmaster接口，将blb信息存入csmaster数据库
// // 3. 创建BLB Listener，Listener以及Backend均为app的port
// // 相关代码 RedisV7CreatingElbState::handle、RedisV7CreatingElbListenerState::handle
// func ProcessBLBOpenTls(ctx context.Context, teu *workflow.TaskExecUnit) error {
// 	// 获取app信息
// 	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
// 	if err != nil {
// 		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
// 			logit.Error("dbError", err))
// 		return cerrs.ErrDbQueryFail.Wrap(err)
// 	}
// 	if app == nil {
// 		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
// 		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
// 	}

// 	// 判断blb 是否已经ready
// 	cnt := 0
// 	for _, blb := range app.BLBs {
// 		if len(blb.TlsIPGroupID) > 0 {
// 			cnt++
// 		}
// 	}
// 	if cnt == len(app.BLBs) {
// 		resource.LoggerTask.Warning(ctx, "blb tls ipGroup has been created", logit.String("appId", teu.Entity))
// 		return nil
// 	}

// 	// 3. 创建BLB ListenPort，Listner以及Backend均为app的port
// 	if err := createAppTlsListener(ctx, app, x1model.BLBTypeApp); err != nil {
// 		return err
// 	}

// 	// 4. 创建blb ip group
// 	if err := createAppTlsIPGroup(ctx, app, x1model.BLBTypeApp); err != nil {
// 		return err
// 	}

// 	// 5. 创建ipgroup policy
// 	if err := createAppTlsIPGroupPolicy(ctx, app, x1model.BLBTypeApp); err != nil {
// 		return err
// 	}

// 	// 6. 创建监听器policy（绑定ipgroup）
// 	if err := createAppTlsListenerPolicy(ctx, app, x1model.BLBTypeApp); err != nil {
// 		return err
// 	}

// 	return nil
// }

// func createAppTlsListener(ctx context.Context, app *x1model.Application, blbType string) error {
// 	var BLBIDs []string
// 	var userID string

// 	for _, b := range app.BLBs {
// 		if len(b.ResourceUserId) == 0 {
// 			userID = app.UserId
// 		} else {
// 			userID = b.ResourceUserId
// 		}
// 		if b.Type == blbType {
// 			BLBIDs = append(BLBIDs, b.BlbId)
// 		}
// 	}
// 	if len(BLBIDs) == 0 {
// 		return nil
// 	}
// 	g := &gtask.Group{
// 		Concurrent:    2,
// 		AllowSomeFail: false,
// 	}

// 	totalCount := 0
// 	for _, blb := range BLBIDs {
// 		createListenerParams := &blbv2.CommonListenerParams{
// 			UserID:       userID,
// 			BlbID:        blb,
// 			ListenerPort: app.TlsPort,
// 			Scheduler:    "RoundRobin",
// 		}
// 		g.Go(func() error {
// 			return gtask.NoPanic(func() error {
// 				return processCreateAppListener(ctx, createListenerParams)
// 			})
// 		})
// 		totalCount++
// 	}

// 	if succCount, err := g.Wait(); err != nil {
// 		resource.LoggerTask.Warning(ctx, "some errors occur create app listeners",
// 			logit.String("appId", app.AppId),
// 			logit.Int("totalCount", totalCount),
// 			logit.Int("succCount", succCount),
// 			logit.Error("create app tls listener", err))
// 		return err
// 	}

// 	return nil
// }

// func createAppTlsIPGroup(ctx context.Context, app *x1model.Application, blbType string) error {
// 	var BLBIDs []*x1model.BLB
// 	var userID string

// 	for _, b := range app.BLBs {
// 		if len(b.ResourceUserId) == 0 {
// 			userID = app.UserId
// 		} else {
// 			userID = b.ResourceUserId
// 		}
// 		if b.Type == blbType {
// 			BLBIDs = append(BLBIDs, b)
// 		}
// 	}
// 	if len(BLBIDs) == 0 {
// 		return nil
// 	}
// 	// 创建IPGroup
// 	BLBIDs2IPGroups := make(map[string]string, 0)
// 	for _, blb := range BLBIDs {
// 		b := blb
// 		createIPGroupParams := &blbv2.CommonBLBParams{
// 			UserID: userID,
// 			ElbID:  b.BlbId,
// 		}
// 		id, err := blbv2.Instance().CreateAppIPGroup(ctx, createIPGroupParams)
// 		if err != nil {
// 			resource.LoggerTask.Warning(ctx, "some errors occur create app ip group", logit.Error("err", err))
// 			return err
// 		}
// 		BLBIDs2IPGroups[b.BlbId] = id
// 	}

// 	// 更新blb model, 将ipGroup写入数据库
// 	for _, blb := range BLBIDs {
// 		blb.TlsIPGroupID = BLBIDs2IPGroups[blb.BlbId]
// 	}

// 	if err := x1model.BLBsSave(ctx, BLBIDs); err != nil {
// 		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
// 		return cerrs.ErrDbQueryFail.Wrap(err)
// 	}

// 	return nil
// }

// func createAppTlsIPGroupPolicy(ctx context.Context, app *x1model.Application, blbType string) error {
// 	var BLBIDs []*x1model.BLB
// 	var userID string
// 	for _, b := range app.BLBs {
// 		if len(b.ResourceUserId) == 0 {
// 			userID = app.UserId
// 		} else {
// 			userID = b.ResourceUserId
// 		}
// 		if b.Type == blbType {
// 			BLBIDs = append(BLBIDs, b)
// 		}
// 	}
// 	if len(BLBIDs) == 0 {
// 		return nil
// 	}
// 	// 创建IPGroup policy
// 	g := &gtask.Group{
// 		Concurrent:    2,
// 		AllowSomeFail: false,
// 	}
// 	totalCount := 0
// 	for _, blb := range BLBIDs {
// 		b := blb
// 		createIPGroupParams := &blbv2.CommonIPGroupParams{
// 			UserID:    userID,
// 			BlbID:     b.BlbId,
// 			IPGroupID: b.TlsIPGroupID,
// 			Type:      "TCP",
// 		}
// 		g.Go(func() error {
// 			return gtask.NoPanic(func() error {
// 				return blbv2.Instance().CreateAppIPGroupPolicy(ctx, createIPGroupParams)
// 			})
// 		})
// 	}

// 	if succCount, err := g.Wait(); err != nil {
// 		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
// 			logit.String("appId", app.AppId),
// 			logit.Int("totalCount", totalCount),
// 			logit.Int("succCount", succCount),
// 			logit.Error("create app tls IPGroup policy", err))
// 		return err
// 	}

// 	return nil
// }

// func createAppTlsListenerPolicy(ctx context.Context, app *x1model.Application, blbType string) error {
// 	var BLBIDs []*x1model.BLB
// 	var userID string

// 	for _, b := range app.BLBs {
// 		if len(b.ResourceUserId) == 0 {
// 			userID = app.UserId
// 		} else {
// 			userID = b.ResourceUserId
// 		}
// 		if b.Type == blbType {
// 			BLBIDs = append(BLBIDs, b)
// 		}
// 	}
// 	if len(BLBIDs) == 0 {
// 		return nil
// 	}

// 	// 创建监听器 policy
// 	g := &gtask.Group{
// 		Concurrent:    2,
// 		AllowSomeFail: false,
// 	}
// 	totalCount := 0
// 	for _, blb := range BLBIDs {
// 		b := blb
// 		// 构建app policy vos
// 		appPolicy := &blbv2.AppPolicyVos{
// 			IPGroupID: b.TlsIPGroupID,
// 			Priority:  100,
// 			Desc:      "",
// 			RuleList: []*blbv2.RuleList{
// 				{
// 					Key:   "*",
// 					Value: "*",
// 				},
// 			},
// 		}
// 		createPolicyParams := &blbv2.CreateAppPolicysParams{
// 			UserID:       userID,
// 			BlbID:        b.BlbId,
// 			ListenerPort: app.TlsPort,
// 			Type:         "TCP",
// 			GroupType:    "ip",
// 			AppPolicyVos: []*blbv2.AppPolicyVos{appPolicy},
// 		}
// 		g.Go(func() error {
// 			return gtask.NoPanic(func() error {
// 				return blbv2.Instance().CreateAppPolicys(ctx, createPolicyParams)
// 			})
// 		})
// 	}

// 	if succCount, err := g.Wait(); err != nil {
// 		resource.LoggerTask.Warning(ctx, "some errors occur create app ip group policy",
// 			logit.String("appId", app.AppId),
// 			logit.Int("totalCount", totalCount),
// 			logit.Int("succCount", succCount),
// 			logit.Error("create app tls IPGroup policy", err))
// 		return err
// 	}

// 	return nil
// }

// func ProcessBLBCloseTls(ctx context.Context, teu *workflow.TaskExecUnit) error {
// 	// 获取app信息
// 	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
// 	if err != nil {
// 		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
// 			logit.Error("dbError", err))
// 		return cerrs.ErrDbQueryFail.Wrap(err)
// 	}
// 	if app == nil {
// 		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
// 		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
// 	}

// 	// 判断blb 是否已经ready
// 	// blb tls listen_port、ipGroup等不删除,如果用户重复开启则复用
// 	cnt := 0
// 	for _, blb := range app.BLBs {
// 		if len(blb.TlsIPGroupID) > 0 {
// 			cnt++
// 		}
// 	}
// 	if cnt == len(app.BLBs) {
// 		resource.LoggerTask.Warning(ctx, "blb tls ipGroup has been created", logit.String("appId", teu.Entity))
// 		return nil
// 	} else {
// 		resource.LoggerTask.Warning(ctx, "blb tls ipGroup create failed", logit.String("appId", teu.Entity))
// 		return cerrs.ErrNotFound.Errorf("app(%s) blb tls ipGroup create failed", teu.Entity)
// 	}
// }
