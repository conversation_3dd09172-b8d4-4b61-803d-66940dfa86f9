/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/29 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file init_entrance_blb.go
 * <AUTHOR>
 * @date 2022/12/29 19:45:07
 * @brief
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessInitEntranceAppBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 0. 若用户是公有云用户，且使用了虚机混布，则将公有云混布池的资源账户记录到数据库中
	env := blbv2.Instance().GetEnv(ctx)
	if !privatecloud.IsPrivateENV() && app.UserId != env.ResourcePrivateUserId && app.ResourceType == "container" {
		if err := saveResourceInfo(ctx, app); err != nil {
			return err
		}
	}

	// 创建BLB并Save
	if err := createAppBLB(ctx, app); err != nil {
		return err
	}

	// 创建BLB ListenPort，Listener以及Backend均为app的port
	if err := createAppListener(ctx, app, x1model.BLBTypeAppEntrance); err != nil {
		return err
	}

	// 创建blb ip group
	if err := createAppIPGroup(ctx, app, x1model.BLBTypeAppEntrance); err != nil {
		return err
	}

	// 创建ip group policy
	if err := createAppIPGroupPolicy(ctx, app, x1model.BLBTypeAppEntrance); err != nil {
		return err
	}

	// 创建监听器policy（绑定ip group）
	if err := createAppListenerPolicy(ctx, app, x1model.BLBTypeAppEntrance); err != nil {
		return nil
	}

	// 为特定用户创建mc-pack的监听器
	if err := CreateAppBlBComponentForMcpack(ctx, app, x1model.BLBTypeAppEntrance); err != nil {
		return nil
	}

	// 调用cs-master接口，将信息填入entrance字段
	if err := util.UpdateEntranceInfo(ctx, app); err != nil {
		return err
	}

	return nil
}
