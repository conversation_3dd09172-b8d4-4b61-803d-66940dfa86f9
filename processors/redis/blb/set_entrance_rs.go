/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/29 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file set_entrance_rs.go
 * <AUTHOR>
 * @date 2022/12/29 19:19:25
 * @brief
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/utils/ro"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessSetEntranceRs 设置entrance rs
func ProcessSetEntranceRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有entrance节点的blb绑定状态
	if err := updateEntranceRsBinding(ctx, app); err != nil {
		return err
	}

	return nil
}

func isGlobalLeader(app *x1model.Application) bool {
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				return true
			}
		}
	}
	return false
}

func updateEntranceRsBinding(ctx context.Context, app *x1model.Application) error {
	// 获得entrance类型的blb列表
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if b.BlbId == "" {
			continue
		}
		if b.Type == x1model.BLBTypeAppEntrance {
			appBlbList = append(appBlbList, b)
		}
	}

	if len(appBlbList) == 0 {
		return nil
	}

	nodes := make([]*x1model.Node, 0)
	unbindNodes := make([]*x1model.Node, 0)

	// 热活从地域
	if len(app.AppGroupID) != 0 && !isGlobalLeader(app) {
		for _, cluster := range app.Clusters {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusInUse ||
					node.Status == x1model.NodeOrProxyStatusToCreate) {
					nodes = append(nodes, node)
				} else {
					unbindNodes = append(unbindNodes, node)
				}
			}
		}
	}

	if (len(app.AppGroupID) != 0 && isGlobalLeader(app)) || len(app.AppGroupID) == 0 {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				n := ro.ChangeRoNode2Node(node)
				if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusInUse ||
					node.Status == x1model.NodeOrProxyStatusToCreate) {
					nodes = append(nodes, n)
				} else {
					unbindNodes = append(unbindNodes, n)
				}
			}

			// 热活从地域->切主->热活主地域, 需要解绑原有的redis主从节点
			for _, node := range cluster.Nodes {
				unbindNodes = append(unbindNodes, node)
			}
		}
	}

	// 更新APP类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, nodes, unbindNodes); err != nil {
		return err
	}

	return nil
}

// ProcessSetEntranceRs 设置entrance rs
func ProcessDeleteEntranceRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有entrance节点的blb绑定状态
	if err := deleteEntranceRsBinding(ctx, app); err != nil {
		return err
	}

	return nil
}

func deleteEntranceRsBinding(ctx context.Context, app *x1model.Application) error {
	// 获得entrance类型的blb列表
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if b.BlbId == "" {
			continue
		}
		if b.Type == x1model.BLBTypeAppEntrance {
			appBlbList = append(appBlbList, b)
		}
	}

	if len(appBlbList) == 0 {
		return nil
	}

	unbindNodes := make([]*x1model.Node, 0)

	// 热活从地域
	if len(app.AppGroupID) != 0 && !isGlobalLeader(app) {
		for _, cluster := range app.Clusters {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusInUse ||
					node.Status == x1model.NodeOrProxyStatusToCreate) {
					resource.LoggerTask.Notice(ctx, "slave region, do not deal inuse node",
						logit.String("nodeId", node.NodeId), logit.String("ip", node.Ip))
				} else {
					resource.LoggerTask.Notice(ctx, "slave region, only del node",
						logit.String("nodeId", node.NodeId), logit.String("ip", node.Ip))
					unbindNodes = append(unbindNodes, node)
				}
			}
		}
	}

	if (len(app.AppGroupID) != 0 && isGlobalLeader(app)) || len(app.AppGroupID) == 0 {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				n := ro.ChangeRoNode2Node(node)
				if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusInUse ||
					node.Status == x1model.NodeOrProxyStatusToCreate) {
					resource.LoggerTask.Notice(ctx, "master region, do not deal inuse node",
						logit.String("nodeId", node.NodeId), logit.String("ip", node.Ip))
				} else {
					resource.LoggerTask.Notice(ctx, "master region, only del node",
						logit.String("nodeId", node.NodeId), logit.String("ip", node.Ip))
					unbindNodes = append(unbindNodes, n)
				}
			}

			// 热活从地域->切主->热活主地域, 需要解绑原有的redis主从节点
			for _, node := range cluster.Nodes {
				unbindNodes = append(unbindNodes, node)
			}
		}
	}

	// 更新APP类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, nil, unbindNodes); err != nil {
		return err
	}

	return nil
}
