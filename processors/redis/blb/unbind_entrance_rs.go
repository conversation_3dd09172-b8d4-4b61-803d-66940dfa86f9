/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/30 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file unbind_entrance_rs.go
 * <AUTHOR>
 * @date 2022/12/30 15:17:21
 * @brief
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/utils/ro"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessUnbindAllEntranceRs 设置entrance rs
func ProcessUnbindAllEntranceRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有entrance节点的blb绑定状态
	if err := unbindEntranceRsBinding(ctx, app); err != nil {
		return err
	}

	return nil
}

func unbindEntranceRsBinding(ctx context.Context, app *x1model.Application) error {
	// 获得entrance类型的blb列表
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if b.BlbId == "" {
			continue
		}
		if b.Type == x1model.BLBTypeAppEntrance {
			appBlbList = append(appBlbList, b)
		}
	}

	if len(appBlbList) == 0 {
		return nil
	}

	nodes := make([]*x1model.Node, 0)

	// 热活从地域
	if len(app.AppGroupID) != 0 && !isGlobalLeader(app) {
		for _, cluster := range app.Clusters {
			for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
				if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusInUse ||
					node.Status == x1model.NodeOrProxyStatusToCreate) {
					nodes = append(nodes, node)
				}
			}
		}
	}

	if (len(app.AppGroupID) != 0 && isGlobalLeader(app)) || len(app.AppGroupID) == 0 {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				n := ro.ChangeRoNode2Node(node)
				if node.Role == x1model.RoleTypeSlave && (node.Status == x1model.NodeOrProxyStatusInUse ||
					node.Status == x1model.NodeOrProxyStatusToCreate) {
					nodes = append(nodes, n)
				}
			}
		}
	}

	// 更新APP类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, nil, nodes); err != nil {
		return err
	}

	return nil
}
