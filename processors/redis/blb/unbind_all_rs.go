/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/31
 * File: unbind_all_rs.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package blb TODO package function desc
package blb

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessUnbindAllRsStandaloneRs 摘除所有Rs
func ProcessUnbindAllRsStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有节点的blb绑定状态
	if err := unbindAllRs(ctx, app); err != nil {
		return err
	}

	return nil
}

func unbindAllRs(ctx context.Context, app *x1model.Application) error {
	// 获得normal和readonly类型的blb列表
	blbList := make([]*x1model.BLB, 0)
	appblbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}
		if b.Type == x1model.BLBTypeNormal {
			blbList = append(blbList, b)
		}
		if b.Type == x1model.BLBTypeApp {
			appblbList = append(appblbList, b)
		}
	}

	// 获取主从节点
	nodes := make([]*x1model.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			nodes = append(nodes, node)
		}
	}

	// 更新normal类型的blb绑定状态
	if err := updateRsBindingByBLBList(ctx, app, blbList, nil, nodes); err != nil {
		return err
	}

	// 更新app类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appblbList, nil, nodes); err != nil {
		return err
	}

	return nil
}

// ProcessUnbindAllProxys process proxy rs
func ProcessUnbindAllProxys(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	var userId string

	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if utils.CheckSyncGroupField(app) {
		return nil
	}

	blbIds := make([]string, 0)
	appBlbIds := make([]*x1model.BLB, 0)
	for _, blb := range app.BLBs {
		if len(blb.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = blb.ResourceUserId
		}
		if blb.Type == x1model.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbId)
		}
		if blb.Type == x1model.BLBTypeApp {
			appBlbIds = append(appBlbIds, blb)
		}
	}

	toUnbindRsIDList := []string{}
	toUnbindNodeList := []*x1model.Proxy{}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = proxy.ContainerId
			} else {
				uuid = proxy.ResourceId
			}
			toUnbindRsIDList = append(toUnbindRsIDList, uuid)
			toUnbindNodeList = append(toUnbindNodeList, proxy)
		}
	}
	if len(blbIds) > 0 {
		if len(toUnbindRsIDList) > 0 {
			unbindReq := &blb.UnbindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				UUIDs:  toUnbindRsIDList,
			}
			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
				return errors.UnBindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIds) > 0 {
		// 处理ip组
		IPList := make([]string, 0)
		for _, n := range toUnbindNodeList {
			rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
			IPList = append(IPList, rsIpPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range appBlbIds {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userId,
				BLBID:      blb.BlbId,
				IPGroup:    blb.IPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}
	return nil
}

// ProcessUnbindAllReadonlyRs 摘除所有只读Rs
func ProcessUnbindAllReadonlyRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	roGroups, err := resource.CsmasterOpAgent.GetReadonlyGroupsByClusterID(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if len(roGroups) == 0 {
		return nil
	}

	// 更新所有节点的blb绑定状态
	for _, roGroup := range roGroups {
		// 已经删除的只读组不在处理blb
		if roGroup.Status == x1model.RoGroupDeleted || roGroup.Status == x1model.RoGroupCreateFail {
			continue
		}

		if err := unbindReadonlyRs(ctx, app, roGroup); err != nil {
			return err
		}
	}
	return nil
}

// unbindReadonlyRs will unbind rs
func unbindReadonlyRs(ctx context.Context, app *x1model.Application, roGroup *csmaster_model_interface.ReadonlyGroup) error {
	blbIds := make([]string, 0)
	appBlbIds := make([]*x1model.BLB, 0)
	var userId string
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeReadOnly {
			blbIds = append(blbIds, b.BlbId)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeAppReadOnly {
			appBlbIds = append(appBlbIds, b)
		}
	}

	// 获取待解绑节点
	nodes := make([]*x1model.RoNode, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.RoGroupID == roGroup.RoGroupShowID {
				nodes = append(nodes, node)
			}
		}
	}
	if len(blbIds) > 0 {
		// 解绑rss
		if len(nodes) > 0 {
			uuidList := make([]string, 0)
			for _, n := range nodes {
				var uuid string
				if app.ResourceType == "container" {
					uuid = n.ContainerId
				} else {
					uuid = n.ResourceId
				}
				uuidList = append(uuidList, uuid)
			}
			unbindReq := &blb.UnbindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				UUIDs:  uuidList,
			}
			// 里面会做是否已经解绑的判断
			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("UUIDs", base_utils.Format(uuidList)))
				return errors.UnBindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIds) > 0 {
		// 解绑RS 从IP组中删除对应IP
		IPList := make([]string, 0)
		for _, n := range nodes {
			rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
			IPList = append(IPList, rsIpPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range appBlbIds {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userId,
				BLBID:      blb.BlbId,
				IPGroup:    blb.IPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}

	return nil
}

// ProcessUnbindAllProxysMcpackRs process proxy rs
func ProcessUnbindAllProxysMcpackRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	var userId string

	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if utils.CheckSyncGroupField(app) {
		return nil
	}

	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	blbIds := make([]string, 0)
	appBlbIds := make([]*x1model.BLB, 0)
	for _, blb := range app.BLBs {
		if len(blb.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = blb.ResourceUserId
		}
		if blb.Type == x1model.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbId)
		}
		if blb.Type == x1model.BLBTypeApp {
			appBlbIds = append(appBlbIds, blb)
		}
	}

	toUnbindRsIDList := []string{}
	toUnbindNodeList := []*x1model.Proxy{}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = proxy.ContainerId
			} else {
				uuid = proxy.ResourceId
			}
			toUnbindRsIDList = append(toUnbindRsIDList, uuid)
			toUnbindNodeList = append(toUnbindNodeList, proxy)
		}
	}
	// !!!普通型blb直接解绑uuid，正常流程已摘除rs，无需再次操作
	// if len(blbIds) > 0 {
	// 	if len(toUnbindRsIDList) > 0 {
	// 		unbindReq := &blb.UnbindRsParam{
	// 			UserID: userId,
	// 			BLBIDs: blbIds,
	// 			UUIDs:  toUnbindRsIDList,
	// 		}
	// 		if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
	// 			resource.LoggerTask.Warning(ctx, "unbind rs fail",
	// 				logit.String("appId", app.AppId),
	// 				logit.String("blbIds", base_utils.Format(blbIds)),
	// 				logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
	// 			return errors.UnBindRsFail.Wrap(err)
	// 		}
	// 	}
	// }

	if len(appBlbIds) > 0 {
		// 处理ip组
		IPList := make([]string, 0)
		for _, n := range toUnbindNodeList {
			rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
			IPList = append(IPList, rsIpPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range appBlbIds {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userId,
				BLBID:      blb.BlbId,
				IPGroup:    blb.McpackIPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}
	return nil
}

func ProcesUnbindAllProxysForDisableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return procesUnbindAllProxysForDisableCrossAzNearest(ctx, app, app.BLBs)
}

func procesUnbindAllProxysForDisableCrossAzNearest(ctx context.Context, app *x1model.Application, blbs []*x1model.BLB) error {
	needMcpack := util.UseMcpackProtocol(app.UserId)
	IPList := make([]string, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			IPList = append(IPList, fmt.Sprintf("%s:%d", proxy.Ip, proxy.Port))
			if needMcpack {
				IPList = append(IPList, fmt.Sprintf("%s:%d", proxy.Ip, proxy.McpackPort))
			}
		}
	}
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	var userId string
	var totalCount int
	for _, b := range blbs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Type != x1model.BLBTypeApp {
			continue
		}
		if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
			continue
		}
		totalCount++
		unbindReq := &blbv2.UnbindRsParams{
			UserID:     userId,
			BLBID:      b.BlbId,
			IPGroup:    b.IPGroupID,
			MemberList: IPList,
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return blbv2.Instance().UnbindRs(ctx, unbindReq)
			})
		})
	}
	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doblbv2UnBindRs", err))
		return err
	}
	return nil
}

func ProcesUnbindAllProxysMcpackForDisableCrossAzNearest(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}
	IPList := make([]string, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			rsIpPort := fmt.Sprintf("%s:%d", proxy.Ip, proxy.Port)
			IPList = append(IPList, rsIpPort)
		}
	}
	g := &gtask.Group{
		Concurrent:    2,
		AllowSomeFail: false,
	}
	var userId string
	var totalCount int
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Type != x1model.BLBTypeApp {
			continue
		}
		if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
			continue
		}
		totalCount++
		unbindReq := &blbv2.UnbindRsParams{
			UserID:     userId,
			BLBID:      b.BlbId,
			IPGroup:    b.McpackIPGroupID,
			MemberList: IPList,
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return blbv2.Instance().UnbindRs(ctx, unbindReq)
			})
		})
	}
	if succCount, err := g.Wait(); err != nil {
		resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
			logit.String("appId", app.AppId),
			logit.Int("totalCount", totalCount),
			logit.Int("succCount", succCount),
			logit.Error("doblbv2UnBindRs", err))
		return err
	}
	return nil
}
