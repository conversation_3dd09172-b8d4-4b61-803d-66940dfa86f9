package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessUnbindEIP unbind eip
func ProcessUnbindEIP(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 从csmaster里查询是否有EIP绑定
	csmasterCluster, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get csmaster cluster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("queryError", err))
		return errors.QueryCsmasterClusterFail.Wrap(err)
	}

	if len(csmasterCluster.Eip) == 0 {
		resource.LoggerTask.Warning(ctx, "no eip was bind",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId))
		return nil
	}

	// 解绑eip
	unbindParams := &blb.UnbindEipParams{
		UserID: app.UserId,
		Eip:    csmasterCluster.Eip,
	}
	err = blb.Instance().UnbindEip(ctx, unbindParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to unbind eip",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UnbindEipFail.Wrap(err)
	}

	return nil
}
