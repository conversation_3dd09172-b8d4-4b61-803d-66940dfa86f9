/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/18 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file del_readonly_blb.go
 * <AUTHOR>
 * @date 2022/05/18 19:44:41
 * @brief del readonly group blb
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessDeletingAllRoDelBLB 只读组删除blb
func ProcessDeletingAllRoDelBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	roGroups, err := resource.CsmasterOpAgent.GetReadonlyGroupsByClusterID(ctx, param.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if len(roGroups) == 0 {
		return nil
	}

	for _, roGroup := range roGroups {
		// 已经删除的只读组不在处理blb
		if roGroup.Status == x1model.RoGroupDeleted || roGroup.Status == x1model.RoGroupCreateFail {
			continue
		}

		// 1. 删除BLB并Save
		if err := deleteBLBForRo(ctx, app, roGroup); err != nil {
			resource.LoggerTask.Error(ctx, "delete all ro group blb error", logit.Error("error", err))
			return err
		}
	}

	return nil
}
