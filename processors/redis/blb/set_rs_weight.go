/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* set_rs_weight.go - the controller of instance api  */
/*
modification history
--------------------
2023/06/23, by wangbin34
*/

/*
DESCRIPTION
设置 blb 权重
*/
package blb

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessSetProxyRsZeroWeightWhenDelete(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return updateProxyWeight(ctx, app, false)
}

func ProcessSetProxyRsZeroWeightWhenDeleteForMcpack(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}
	return updateProxyWeight(ctx, app, true)
}

func updateProxyWeight(ctx context.Context, app *x1model.Application, isMcpack bool) error {
	blbIDs := make([]string, 0)
	appBlbIDs := make([]*x1model.BLB, 0)
	var userID string

	// (1) get blb id
	for _, blb := range app.BLBs {
		if len(blb.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = blb.ResourceUserId
		}
		if blb.Type == x1model.BLBTypeNormal {
			blbIDs = append(blbIDs, blb.BlbId)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			appBlbIDs = append(appBlbIDs, blb)
		}
	}

	if len(blbIDs) <= 0 && len(appBlbIDs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	// (2) get toSetWeightList
	toUpdateRs := make([]*blb.Rs, 0)
	toUpdateAppRs := make([]*blbv2.Rs, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = proxy.ContainerId
			} else {
				uuid = proxy.ResourceId
			}
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				toUpdateRs = append(toUpdateRs, &blb.Rs{
					UUID:   uuid,
					Weight: 0,
				})

				if isMcpack {
					toUpdateAppRs = append(toUpdateAppRs, &blbv2.Rs{
						UUID:   uuid,
						IP:     proxy.Ip,
						Weight: 0,
						Port:   proxy.McpackPort,
					})
				} else {
					toUpdateAppRs = append(toUpdateAppRs, &blbv2.Rs{
						UUID:   uuid,
						IP:     proxy.Ip,
						Weight: 0,
						Port:   proxy.Port,
					})
				}
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "to set weight rs", logit.String("toRsIdList", base_utils.Format(toUpdateAppRs)))

	// 普通型 BLB
	if len(blbIDs) > 0 {
		if len(toUpdateRs) > 0 {
			for _, blbID := range blbIDs {
				updateElbRsParams := &blb.UpdateElbRsForRoParams{
					UserID:     userID,
					BLbs:       blbID,
					Port:       int32(app.Port),
					ToUpdateRs: toUpdateRs,
				}
				if err := blb.Instance().UpdateElbsRsForRo(ctx, updateElbRsParams); err != nil {
					resource.LoggerTask.Warning(ctx, "blb update rs weight failed")
					return err
				}
			}
		}
	}

	// 应用型 BLB
	if len(appBlbIDs) > 0 {
		if len(toUpdateAppRs) > 0 {
			for _, b := range appBlbIDs {
				updateAppElbRsParams := &blbv2.UpdateElbRsForRoParams{
					UserID:     userID,
					ToUpdateRs: toUpdateAppRs,
					BLBID:      b.BlbId,
					IPGroupID:  b.IPGroupID,
				}

				if err := blbv2.Instance().UpdateElbsRsForRo(ctx, updateAppElbRsParams); err != nil {
					resource.LoggerTask.Warning(ctx, "blb update rs weight failed")
					return err
				}
			}
		}
	}
	return nil
}
