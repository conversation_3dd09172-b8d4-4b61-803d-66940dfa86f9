/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package blb

import (
	"context"
	"errors"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"

	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type BlbDetectReq struct {
	BlbIP         string `json:"blb_ip"`           // blb_ip
	BlbPort       string `json:"blb_port"`         // blb_port
	Engine        string `json:"engine"`           // engine
	BlbAuth       string `json:"blb_auth"`         // blb_auth
	BlbIsReadOnly bool   `json:"blb_is_read_only"` // blb_is_read_only
}

type BlbDetectResp struct {
	TelnetBlb  int    `json:"telnet_blb"`   // telnet_blb
	SetexTest  int    `json:"setex_test"`   // setex_test
	IsReadOnly bool   `json:"is_read_only"` // is_read_only
	ErrMsg     string `json:"err_msg"`      // err_msg
}

func processCheckBlbAvailable(ctx context.Context, app *x1model.Application) error {

	var checkNode *x1model.Node
	// 获取第一个节点验证blb生效
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if len(cluster.Nodes) == 1 {
				resource.LoggerTask.Notice(ctx, "cluster has only one replication, skip check blb",
					logit.String("app", app.AppId))
				return nil
			}
			if node.Role == x1model.RoleTypeMaster {
				continue
			}
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			checkNode = node
		}
		if checkNode != nil {
			break
		}
	}

	if checkNode == nil {
		resource.LoggerTask.Warning(ctx, "no node found to test blb", logit.String("appId", app.AppId))
		return fmt.Errorf("no node found to test blb")
	}

	var checkBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			checkBlb = blb
		}
	}

	if checkBlb == nil {
		resource.LoggerTask.Notice(ctx, "no ipv4 blb found, skip")
		return nil
	}
	// app.Port与cache cluster的port完全一致
	return checkBlbAvailableByXagent(ctx, checkBlb, checkNode, app.Port)
}

func checkBlbAvailableByXagent(ctx context.Context, blb *x1model.BLB, node *x1model.Node, port int) error {
	params := BlbDetectReq{
		BlbIP:         blb.Ovip,
		BlbPort:       cast.ToString(port),
		Engine:        "redis",
		BlbAuth:       "",
		BlbIsReadOnly: true,
	}
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: node.FloatingIP,
			Port: base_utils.ToInt32(node.XagentPort),
		},
		Action:     "blbDetect", // 变更env_vars
		Params:     params,
		TimeoutSec: 30, // 执行超时时间
	}
	resource.LoggerTask.Notice(ctx, "blb detect params",
		logit.String("blb_detect_req", base_utils.Format(req)))
	resp, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "execute blb detect req fail",
			logit.String("blb_ip", blb.Ovip),
			logit.String("blb_port", base_utils.Format(port)),
			logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "execute blb detect req success",
		logit.String("blb_detect_req", base_utils.Format(req)),
		logit.String("blb_detect_resp", base_utils.Format(resp)))

	var blbDetectResp BlbDetectResp
	err = resp.ParseResult(&blbDetectResp)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse blb_detect_resp err",
			logit.String("blb_detect_resp", base_utils.Format(resp)),
			logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "parse blb_detect_resp success",
		logit.String("blbDetectResp", base_utils.Format(blbDetectResp)))

	if blbDetectResp.TelnetBlb != 1 {
		resource.LoggerTask.Notice(ctx, "telnet blb fail",
			logit.String("blbDetectResp", base_utils.Format(blbDetectResp)))
		return errors.New("telnet blb fail")
	}
	resource.LoggerTask.Notice(ctx, "blb is available", logit.String("blb_ip", blb.Ovip))
	return nil
}

// ProcessCheckToExchangeBlbAvailable 通过xagent拨测blb验证连通性
func ProcessCheckToExchangeBlbAvailable(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("error", err))
		return err
	}

	return processCheckBlbAvailable(ctx, app)
}
