/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2021/12/29, by shangshuai02(<EMAIL>), first version
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessInitBLB 初始化BLB
// 1. 创建BLB；如果app是ipv6的同时创建ipv6的BLB
// 2. 调用Csmaster接口，将blb信息存入csmaster数据库
// 3. 创建BLB Listener，Listener以及Backend均为app的port
// 相关代码 RedisV7CreatingElbState::handle、RedisV7CreatingElbListenerState::handle
func ProcessInitBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 1. 创建BLB并Save
	if err := createBLB(ctx, app); err != nil {
		return err
	}

	// 2. 调用Csmaster接口，将blb信息存入csmaster数据库
	if err := updateCsmasterBLBInfo(ctx, app); err != nil {
		return err
	}

	// 3. 创建BLB ListenPort，Listner以及Backend均为app的port
	if err := createListener(ctx, app); err != nil {
		return err
	}

	return nil
}

// createBLB 创建BLB并Save
func createBLB(ctx context.Context, app *x1model.Application) error {
	// 获取待创建的BLB列表
	var newBLBParams []*blb.BLBParam
	var newBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if len(b.BlbId) != 0 {
			continue
		}
		newBLBParams = append(newBLBParams, &blb.BLBParam{
			Name:              b.Name,
			VpcId:             b.VpcId,
			SubnetId:          b.SubnetId,
			IpType:            b.IpType,
			BgwGroupId:        b.BgwGroupId,
			BgwGroupExclusive: b.BgwGroupExclusive != 0,
			BgwGroupMode:      b.BgwGroupMode,
			MasterAZ:          b.MasterAZ,
			SlaveAZ:           b.SlaveAZ,
		})
		newBlbList = append(newBlbList, b)
	}

	if len(newBLBParams) == 0 {
		return nil
	}

	// 创建BLB
	createBlbParams := &blb.CreateBLBParam{
		UserID:    app.UserId,
		Product:   app.Product,
		BLBParams: newBLBParams,
	}
	blbList, err := blb.Instance().CreateBLB(ctx, createBlbParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create blb fail", logit.String("appId", app.AppId))
		return errors.CreateBlbFail.Wrap(err)
	}

	// 更新数据库
	for k, b := range blbList {
		newBlbList[k].BlbId = b.BLBID
		newBlbList[k].Vip = b.Vip
		newBlbList[k].Ovip = b.OVip
		newBlbList[k].Ipv6 = b.IPV6
		newBlbList[k].Status = x1model.BLBStatusAvailable
	}
	if err := x1model.BLBsSave(ctx, newBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}

// createListener 创建Listener
func createListener(ctx context.Context, app *x1model.Application) error {
	var BLBIDs []string
	for _, b := range app.BLBs {
		BLBIDs = append(BLBIDs, b.BlbId)
	}
	if len(BLBIDs) == 0 {
		return nil
	}

	backend_port := getBackendPort(ctx, app)
	createListenerParams := &blb.CreateListenerParam{
		UserID:      app.UserId,
		BLBIDs:      BLBIDs,
		ListenPort:  int32(app.Port),
		BackendPort: int32(backend_port),
	}
	// 里面会判断是否已经创建
	if err := blb.Instance().CreateListener(ctx, createListenerParams); err != nil {
		resource.LoggerTask.Warning(ctx, "create listener fail", logit.String("appId", app.AppId))
		return errors.CreateListenerFail.Wrap(err)
	}

	return nil
}

func ProcessCreateMcpackListener(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	var BLBIDs []string
	for _, b := range app.BLBs {
		BLBIDs = append(BLBIDs, b.BlbId)
	}
	if len(BLBIDs) == 0 {
		return nil
	}

	createListenerParams := &blb.CreateListenerParam{
		UserID:      app.UserId,
		BLBIDs:      BLBIDs,
		ListenPort:  int32(app.McpackPort),
		BackendPort: int32(app.McpackPort),
	}

	if err := blb.Instance().CreateListener(ctx, createListenerParams); err != nil {
		resource.LoggerTask.Warning(ctx, "create listener fail", logit.String("appId", app.AppId))
		return errors.CreateListenerFail.Wrap(err)
	}

	return nil
}
