/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package blb

import (
	"context"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessBindProxyToExchangeApp process proxy rs include app blb
func ProcessBindProxyToExchangeApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return bindProxyToExchangeApp(ctx, app, false, false)
}

func ProcessBindProxyMcpackToExchangeApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	// 标准版升级到集群版后，没有 mcpack 端口
	if app.McpackPort == 0 {
		return nil
	}
	return bindProxyToExchangeApp(ctx, app, true, false)
}

// ProcessUnBindProxyFromOld
func ProcessUnBindProxyFromOld(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return unBindProxyFromOld(ctx, app, false, false)
}

// ProcessRollbackUnBindProxyFromOld
func ProcessRollbackUnBindProxyFromOld(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return unBindProxyFromOld(ctx, app, false, true)
}

func ProcessUnBindProxyMcpackFromOld(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	// 标准版升级到集群版后，没有 mcpack 端口
	if app.McpackPort == 0 {
		return nil
	}
	return unBindProxyFromOld(ctx, app, true, false)
}

func ProcessRollbackUnBindProxyMcpackFromOld(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	// 标准版升级到集群版后，没有 mcpack 端口
	if app.McpackPort == 0 {
		return nil
	}
	return unBindProxyFromOld(ctx, app, true, true)
}

func bindProxyToExchangeApp(ctx context.Context, app *x1model.Application, isMcpack bool, rollback bool) error {
	appBlbIds := make([]*x1model.BLB, 0)

	for _, blb := range app.BLBs {
		if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
			continue
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			appBlbIds = append(appBlbIds, blb)
		}
	}

	if len(appBlbIds) <= 0 {
		resource.LoggerTask.Error(ctx, "no blbs to bind")
		return errors.New("no blbs to bind")
	}

	tobindProxys := make([]*x1model.Proxy, 0)
	unbindProxys := make([]*x1model.Proxy, 0)

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusInUse:
				if !rollback {
					tobindProxys = append(tobindProxys, proxy)
				} else {
					unbindProxys = append(unbindProxys, proxy)
				}
			}
		}
	}

	if err := updateProxyRsBindingByAppBLBList(ctx, app, appBlbIds, tobindProxys, unbindProxys, isMcpack); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByAppBLBList fail", logit.Error("err", err))
		return err
	}
	return nil
}

func unBindProxyFromOld(ctx context.Context, app *x1model.Application, isMcpack bool, rollback bool) error {
	blbIds := make([]*x1model.BLB, 0)
	appBlbIds := make([]*x1model.BLB, 0)

	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			blbIds = append(blbIds, blb)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			appBlbIds = append(appBlbIds, blb)
		}
	}

	if len(appBlbIds) <= 0 && len(blbIds) <= 0 {
		resource.LoggerTask.Error(ctx, "no blbs to bind")
		return errors.New("no blbs to bind")
	}

	tobindProxys := make([]*x1model.Proxy, 0)
	unbindProxys := make([]*x1model.Proxy, 0)

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusInUse:
				if !rollback {
					unbindProxys = append(unbindProxys, proxy)
				} else {
					tobindProxys = append(tobindProxys, proxy)
				}
			}
		}
	}

	if err := updateProxyRsBindingByBLBList(ctx, app, blbIds, tobindProxys, unbindProxys, isMcpack); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByBLBList fail", logit.Error("err", err))
		return err
	}

	if err := updateProxyRsBindingByAppBLBList(ctx, app, appBlbIds, tobindProxys, unbindProxys, isMcpack); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByAppBLBList fail", logit.Error("err", err))
		return err
	}
	return nil
}
