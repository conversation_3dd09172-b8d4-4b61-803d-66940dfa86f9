/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2021/12/29, by shangshuai02(<EMAIL>), first version
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"
	"errors"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	libErrors "icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessSetStandaloneRs 初始化BLB
// 1. 检查App所有分片的主，是否绑定到blb上；如果未绑定，则进行绑定
// 2. 检查App所有分片的从，是否绑定到blb上；如果已绑定，则进行解绑
// 相关代码：BlbExecutorProcessor::process中 cluster_table->version() == VERSION_V7的部分
// 代码中shard_info->op_type() == ShardOpType::EXCHANGE_MASTER是用于节点规格变更时，将绑定转换到新主上
// 这里暂不涉及节点规格变更的相关逻辑
func ProcessSetStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有节点的blb绑定状态
	if err := updateRsBinding(ctx, app, false); err != nil {
		return err
	}

	return nil
}

func updateBlbIPs(ctx context.Context, app *x1model.Application) error {
	// 更新所有节点的blb绑定状态
	if !privatecloud.IsPrivateENV() {
		return nil
	}
	for _, b := range app.BLBs {
		if b.Ovip != "" {
			continue
		}
		if b.Type == x1model.BLBTypeNormal {
			resp, err := blb.Instance().ListELB(ctx, app.UserId, b.BlbId)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get blb ips fail", logit.Error("error", err))
				return err
			}
			b.Ovip = resp.Ovip
			b.Vip = resp.Vip
			csmasterUpdateReq := csmaster.UpdateClusterModelParams{
				Model:  &csmaster.CsmasterCluster{},
				UserID: app.UserId,
				AppID:  app.AppId,
			}
			if b.IpType == x1model.Ipv4 {
				csmasterUpdateReq.Model.ElbId = b.BlbId
				csmasterUpdateReq.Model.ElbPnetip = b.Ovip
			} else if b.IpType == x1model.Ipv6 {
				csmasterUpdateReq.Model.ElbIpv6Id = b.BlbId
				csmasterUpdateReq.Model.ElbIpv6 = b.Ipv6
			} else {
				resource.LoggerTask.Warning(ctx, "blb ip type not found", logit.String("blbId", b.BlbId))
				return fmt.Errorf("blb(%s) ip type not found", b.BlbId)
			}
			if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmasterUpdateReq); err != nil {
				resource.LoggerTask.Warning(ctx, "update csmaster cluster model fail", logit.Error("error", err))
				return err
			}
		}
		if b.Type == x1model.BLBTypeApp {
			resp, err := blbv2.Instance().GetAppBLBDetail(ctx, &blbv2.CommonBLBParams{
				UserID: app.UserId,
				ElbID:  b.BlbId,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get blb details", logit.Error("error", err))
				return err
			}
			csmasterUpdateReq := csmaster.UpdateClusterModelParams{
				Model:  &csmaster.CsmasterCluster{},
				UserID: app.UserId,
				AppID:  app.AppId,
			}
			csmasterUpdateReq.Model.ElbId = resp.BLBID
			csmasterUpdateReq.Model.ElbPnetip = resp.Address
			csmasterUpdateReq.Model.BlbListenerPort = cast.ToInt32(resp.Listener[0].Port)
			if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmasterUpdateReq); err != nil {
				resource.LoggerTask.Warning(ctx, "update csmaster cluster model fail", logit.Error("error", err))
				return err
			}
		}
	}
	if err := x1model.BLBsSave(ctx, app.BLBs); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb ips fail", logit.Error("error", err))
		return err
	}
	return nil
}

func getIPGroupID(blb *x1model.BLB, isMcpack bool) string {
	if isMcpack {
		return blb.McpackIPGroupID
	}
	return blb.IPGroupID
}

func updateRsBinding(ctx context.Context, app *x1model.Application, inSelfHeal bool) error {
	return updateRsBindingForFailover(ctx, app, inSelfHeal, false)
}

func updateRsBindingForFailover(ctx context.Context, app *x1model.Application, inSelfHeal bool, inFailover bool) error {
	// 获得normal和readonly类型的blb列表
	normalBlbList := make([]*x1model.BLB, 0)
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}
		if b.Type == x1model.BLBTypeApp {
			appBlbList = append(appBlbList, b)
		}
		if b.Type == x1model.BLBTypeNormal {
			normalBlbList = append(normalBlbList, b)
		}
	}

	// 获取主从节点
	masterNodes := make([]*x1model.Node, 0)
	slaveNodes := make([]*x1model.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && isNodeAvailable(node) {
				if inFailover && !util.IsNodeSwitchable(node) {
					continue
				}
				masterNodes = append(masterNodes, node)
			} else if node.Role == x1model.RoleTypeSlave && isNodeAvailable(node) {
				if inFailover && !util.IsNodeSwitchable(node) {
					continue
				}
				slaveNodes = append(slaveNodes, node)
			} else if inSelfHeal && node.Role == x1model.RoleTypeMaster &&
				node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				slaveNodes = append(slaveNodes, node)
			}
		}
	}

	// 更新normal类型的blb绑定状态
	if err := updateRsBindingByBLBList(ctx, app, normalBlbList, masterNodes, slaveNodes); err != nil {
		return err
	}

	if err := updateBlbIPs(ctx, app); err != nil {
		return err
	}

	// 更新APP类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, masterNodes, slaveNodes); err != nil {
		return err
	}

	return nil
}

func updateRsBindingByBLBList(ctx context.Context, app *x1model.Application, blbList []*x1model.BLB,
	bindNodes []*x1model.Node, unbindNodes []*x1model.Node) error {
	var userId string

	if len(blbList) == 0 {
		return nil
	}

	blbIds := make([]string, 0)
	for _, b := range blbList {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		blbIds = append(blbIds, b.BlbId)
	}

	// 绑定rss
	if len(bindNodes) > 0 {
		rss := make([]*blb.Rs, 0)
		for _, n := range bindNodes {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			rss = append(rss, &blb.Rs{
				UUID:   uuid,
				Weight: 1,
				Port:   int32(n.Port),
			})
		}

		bindReq := &blb.BindRsParam{
			UserID: userId,
			BLBIDs: blbIds,
			Rss:    rss,
		}
		// 里面会做是否已经绑定的判断
		if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "bind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("rss", base_utils.Format(rss)))
			return libErrors.BindRsFail.Wrap(err)
		}
	}

	// 解绑rss
	if len(unbindNodes) > 0 {
		uuidList := make([]string, 0)
		for _, n := range unbindNodes {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			uuidList = append(uuidList, uuid)
		}
		unbindReq := &blb.UnbindRsParam{
			UserID: userId,
			BLBIDs: blbIds,
			UUIDs:  uuidList,
		}
		// 里面会做是否已经解绑的判断
		if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind rs fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(blbIds)),
				logit.String("UUIDs", base_utils.Format(uuidList)))
			return libErrors.UnBindRsFail.Wrap(err)
		}
	}

	return nil
}

// 处理ip组成员
func updateRsBindingByAppBLBList(ctx context.Context, app *x1model.Application, blbList []*x1model.BLB,
	bindNodes []*x1model.Node, unbindNodes []*x1model.Node) error {
	var userId string

	if len(blbList) == 0 {
		return nil
	}

	blbIds := make([]string, 0)
	for _, b := range blbList {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		blbIds = append(blbIds, b.BlbId)
	}

	// 绑定rss
	if len(bindNodes) > 0 {
		rss := make([]*blbv2.Rs, 0)
		for _, n := range bindNodes {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			rss = append(rss, &blbv2.Rs{
				UUID:   uuid,
				Weight: 1,
				Port:   n.Port,
				IP:     n.Ip,
			})
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbList {
			blb := blb
			bindReq := &blbv2.BindRsParams{
				UserID:  userId,
				BLBID:   blb.BlbId,
				IPGroup: blb.IPGroupID,
				Rss:     rss,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().BindRs(ctx, bindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2BindRs", err))
			return err
		}
	}

	// 解绑rss
	if len(unbindNodes) > 0 {
		IPList := make([]string, 0)
		for _, n := range unbindNodes {
			rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
			IPList = append(IPList, rsIpPort)
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range blbList {
			blb := blb
			unbindReq := &blbv2.UnbindRsParams{
				UserID:     userId,
				BLBID:      blb.BlbId,
				IPGroup:    blb.IPGroupID,
				MemberList: IPList,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().UnbindRs(ctx, unbindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2UnBindRs", err))
			return err
		}
	}

	return nil
}

// ProcessFailoverSetStandaloneRs will process shard failover blb bind rs
func ProcessFailoverSetStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if app.Type == x1model.AppTypeStandalone {
		return updateRsBindingForFailover(ctx, app, false, true)
	}

	return nil
}

// ProcessUpdateSetStandaloneRs will process shard failover blb bind rs
func ProcessUpdateStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if len(app.AppGroupID) == 0 {
		return updateRsBinding(ctx, app, false)
	}

	return updateRsBindRsGlobal(ctx, app)
}

func ProcessUpdateStandaloneRsForSelfHeal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if len(app.AppGroupID) == 0 {
		return updateRsBinding(ctx, app, true)
	}

	return updateRsBindRsGlobal(ctx, app)
}

// updateRsBindRsGlobal will deal with standalone group blb bind rs
//
//	主角色绑定主
//	从角色绑定所有从
func updateRsBindRsGlobal(ctx context.Context, app *x1model.Application) error {
	// 获得normal和readonly类型的blb列表
	normalBlbList := make([]*x1model.BLB, 0)
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 {
			continue
		}
		if b.Type == x1model.BLBTypeApp {
			appBlbList = append(appBlbList, b)
		}
		if b.Type == x1model.BLBTypeNormal {
			normalBlbList = append(normalBlbList, b)
		}
	}

	// 获取主从节点
	masterNodes := make([]*x1model.Node, 0)
	slaveNodes := make([]*x1model.Node, 0)
	notAvailableSlaves := make([]*x1model.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && isNodeAvailable(node) {
				masterNodes = append(masterNodes, node)
			}
			if node.Role == x1model.RoleTypeSlave && isNodeAvailable(node) {
				slaveNodes = append(slaveNodes, node)
			}
			if node.Role == x1model.RoleTypeSlave && !isNodeAvailable(node) {
				notAvailableSlaves = append(notAvailableSlaves, node)
			}
		}
	}
	// 区分主从角色来绑定rs
	cluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		return err
	}
	if cluster.GroupRole == 1 {
		// 更新normal类型的blb绑定状态
		if err := updateRsBindingByBLBList(ctx, app, normalBlbList, masterNodes, slaveNodes); err != nil {
			return err
		}
		// 更新APP类型的blb绑定状态
		if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, masterNodes, slaveNodes); err != nil {
			return err
		}
	}
	if cluster.GroupRole == 2 {
		if len(notAvailableSlaves) > 0 {
			resource.LoggerTask.Trace(ctx, "", logit.String("not available slave to unbind", base_utils.Format(notAvailableSlaves)))
		}
		// 更新normal类型的blb绑定状态
		if err := updateRsBindingByBLBList(ctx, app, normalBlbList, slaveNodes, notAvailableSlaves); err != nil {
			return err
		}
		// 更新APP类型的blb绑定状态
		if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, slaveNodes, notAvailableSlaves); err != nil {
			return err
		}
	}

	return nil
}

// ProcessUpdateStandaloneRsForGlobalModifySpec 标准版热活更新blb绑定
//
//	主角色绑定主
//	从角色绑定所有从
func ProcessUpdateStandaloneRsForGlobalModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	if len(app.AppGroupID) == 0 {
		return nil
	}

	return updateRsBindRsGlobal(ctx, app)
}

func unbindRSFromOld(ctx context.Context, app *x1model.Application, rollback bool) error {
	// 获得normal和readonly类型的blb列表
	normalBlbList := make([]*x1model.BLB, 0)
	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if b.Status != x1model.BLBStatusAvailable {
			continue
		}
		if b.Type == x1model.BLBTypeApp {
			appBlbList = append(appBlbList, b)
		}
		if b.Type == x1model.BLBTypeNormal {
			normalBlbList = append(normalBlbList, b)
		}
	}

	tobindNodes := make([]*x1model.Node, 0)
	unbindNodes := make([]*x1model.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && isNodeAvailable(node) {
				if rollback {
					tobindNodes = append(tobindNodes, node)
				} else {
					unbindNodes = append(unbindNodes, node)
				}
			}
		}
	}

	// 更新normal类型的blb绑定状态
	if err := updateRsBindingByBLBList(ctx, app, normalBlbList, tobindNodes, unbindNodes); err != nil {
		return err
	}

	// 更新APP类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, tobindNodes, unbindNodes); err != nil {
		return err
	}

	return nil
}

func bindRSToExchangeApp(ctx context.Context, app *x1model.Application) error {

	appBlbList := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if b.Status != x1model.BLBStatusAvailable {
			continue
		}
		if b.Type == x1model.BLBTypeAppToExchange {
			appBlbList = append(appBlbList, b)
		}
	}

	if len(appBlbList) == 0 {
		return errors.New("to exchange blb not found")
	}
	// 获取主从节点
	toBindNodes := make([]*x1model.Node, 0)
	unBindNodes := make([]*x1model.Node, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && isNodeAvailable(node) {
				toBindNodes = append(toBindNodes, node)
			} else if node.Role == x1model.RoleTypeSlave && isNodeAvailable(node) {
				unBindNodes = append(unBindNodes, node)
			}
		}
	}

	// 更新APP类型的blb绑定状态
	if err := updateRsBindingByAppBLBList(ctx, app, appBlbList, toBindNodes, unBindNodes); err != nil {
		return err
	}

	return nil
}

// ProcessBindRSToExchangeApp process standalone rs include app blb
func ProcessBindRSToExchangeApp(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return bindRSToExchangeApp(ctx, app)
}

// ProcessUnBindRSFromOld 解绑旧blb后端rs
func ProcessUnBindRSFromOld(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return unbindRSFromOld(ctx, app, false)
}

// ProcessRollbackUnBindRSFromOld 旧blb重新绑上rs
func ProcessRollbackUnBindRSFromOld(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return unbindRSFromOld(ctx, app, true)
}

func ProcessDeleteProxyRsForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return deleteProxyBinding(ctx, app)
}

func deleteProxyBinding(ctx context.Context, app *x1model.Application) error {
	blbs := make([]*x1model.BLB, 0)
	appBlbs := make([]*x1model.BLB, 0)

	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			blbs = append(blbs, blb)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			appBlbs = append(appBlbs, blb)
		}
	}

	if len(blbs) <= 0 && len(appBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	unbindProxys := make([]*x1model.Proxy, 0)

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				unbindProxys = append(unbindProxys, proxy)
			}
		}
	}

	if err := updateProxyRsBindingByBLBList(ctx, app, blbs, nil, unbindProxys, false); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByBLBList fail", logit.Error("err", err))
		return err
	}

	if err := updateProxyRsBindingByAppBLBList(ctx, app, appBlbs, nil, unbindProxys, false); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByAppBLBList fail", logit.Error("err", err))
		return err
	}
	return nil
}

func ProcessDeleteProxyRsMcpackForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	// 满足条件的用户更新mcpack协议的proxy rs
	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	// 标准版升级到集群版后，没有 mcpack 端口
	if app.McpackPort == 0 {
		return nil
	}
	return deleteMcpackProxyBinding(ctx, app)
}

func deleteMcpackProxyBinding(ctx context.Context, app *x1model.Application) error {
	blbs := make([]*x1model.BLB, 0)
	appBlbs := make([]*x1model.BLB, 0)

	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal {
			blbs = append(blbs, blb)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blb(%s) not available", blb.BlbId)
			}
			appBlbs = append(appBlbs, blb)
		}
	}

	if len(blbs) <= 0 && len(appBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	unbindProxys := make([]*x1model.Proxy, 0)

	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				unbindProxys = append(unbindProxys, proxy)
			}
		}
	}

	if err := updateProxyRsBindingByBLBList(ctx, app, blbs, nil, unbindProxys, true); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByBLBList fail", logit.Error("err", err))
		return err
	}

	if err := updateProxyRsBindingByAppBLBList(ctx, app, appBlbs, nil, unbindProxys, true); err != nil {
		resource.LoggerTask.Error(ctx, "updateProxyRsBindingByAppBLBList fail", logit.Error("err", err))
		return err
	}
	return nil
}
