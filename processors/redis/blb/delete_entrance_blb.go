/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/12/29 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file delete_entrance_blb.go
 * <AUTHOR>
 * @date 2022/12/29 20:05:09
 * @brief
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessDeleteEntranceBlB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 1. 删除 entrance BLB并Save
	if err := deleteEntranceBLB(ctx, app); err != nil {
		return err
	}

	return nil
}

func deleteEntranceBLB(ctx context.Context, app *x1model.Application) error {
	var userId string
	var deleteAppBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if b.BlbId == "" || b.Status == x1model.BLBStatusDeleted {
			continue
		}
		if b.ResourceUserId == "" {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}

		if b.Type == x1model.BLBTypeAppEntrance {
			deleteAppBlbList = append(deleteAppBlbList, b)
		}
	}
	if len(deleteAppBlbList) > 0 {
		// 删除app BLB
		for _, b := range deleteAppBlbList {
			deleteBlbParams := &blbv2.CommonBLBParams{
				UserID: userId,
				ElbID:  b.BlbId,
			}
			err := blbv2.Instance().DeleteAppBLB(ctx, deleteBlbParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "delete blb fail",
					logit.String("appId", app.AppId),
					logit.String("blbId", base_utils.Format(b)))
				return errors.DeleteBlbFail.Wrap(err)
			}
		}

		// 更新数据库
		for _, b := range deleteAppBlbList {
			b.Status = x1model.BLBStatusDeleted
		}
		if err := x1model.BLBsSave(ctx, deleteAppBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}
