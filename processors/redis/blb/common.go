package blb

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	zoneComponent "icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// isNodeAvailable
func isNodeAvailable(node *x1model.Node) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse ||
		node.Status == x1model.NodeOrProxyStatusToCreate
}

// isRoNodeAvailable
func isRoNodeAvailable(node *x1model.RoNode) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse ||
		node.Status == x1model.NodeOrProxyStatusToCreate
}

// updateCsmasterBLBInfo 调用Csmaster接口，将blb信息存入csmaster数据库
func updateCsmasterBLBInfo(ctx context.Context, app *x1model.Application) error {
	req := &csmaster.UpdateClusterModelParams{
		Model:  &csdk.CsmasterCluster{},
		UserID: app.UserId,
		AppID:  app.AppId,
	}

	for _, blb := range app.BLBs {
		if blb.Status == x1model.BLBStatusDeleted {
			continue
		}
		if blb.IpType == x1model.Ipv6 {
			req.Model.ElbIpv6Id = blb.BlbId
			req.Model.ElbIpv6 = blb.Ipv6
		} else {
			req.Model.ElbId = blb.BlbId
			req.Model.ElbPnetip = blb.Ovip
		}
	}

	if privatecloud.IsPrivateENV() && privatecloud.GetPrivateEnvType() == privatecloud.DBStackPrefix {
		sp := strings.Split(req.Model.ElbId, "-")
		if len(sp) != 3 {
			resource.LoggerTask.Warning(ctx, "elbId format error", logit.String("elbId", req.Model.ElbId))
			return fmt.Errorf("elbId format error %s", req.Model.ElbId)
		}
		port, err := strconv.Atoi(sp[2])
		if err != nil {
			resource.LoggerTask.Warning(ctx, "elbId format error", logit.String("elbId", req.Model.ElbId))
			return fmt.Errorf("elbId format error %s", req.Model.ElbId)
		}
		req.Model.BlbListenerPort = int32(port)
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update csmaster cluster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UpdateCsmasterClusterModelFail.Wrap(err)
	}

	return nil
}

// 普通blb只支持一个后端端口，因此取某个stand_alone版本的node.port
func getStandAloneBackendPort(ctx context.Context, app *x1model.Application) int32 {
	backend_port := int32(0)

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			backend_port = int32(node.Port)
			return backend_port
		}
	}

	return int32(app.Port)
}

// 普通blb只支持一个后端端口，因此取某个集群版本的proxy.port
func getClusterBackendPort(ctx context.Context, app *x1model.Application) int32 {
	backend_port := int32(0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			backend_port = int32(proxy.Port)
			return backend_port
		}
	}

	return int32(app.Port)
}

func getBackendPort(ctx context.Context, app *x1model.Application) int32 {
	if app.Type == x1model.AppTypeStandalone {
		return getStandAloneBackendPort(ctx, app)
	} else if app.Type == x1model.AppTypeCluster {
		return getClusterBackendPort(ctx, app)
	} else {
		return int32(app.Port)
	}
}

// 获取 LogicZone, 用于创建 blb 时，指定优先的 BLB 四层实例所在主AZ
func getLogicZone(ctx context.Context, app *x1model.Application) (string, error) {
	// 区分主从角色来获取shard master节点
	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return "", err
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node := node
			if len(app.AppGroupID) != 0 && csCluster.GroupRole == 2 {
				if isNodeAvailable(node) {
					return node.LogicZone, nil
				}
			} else {
				if isNodeAvailable(node) && node.Role == x1model.RoleTypeMaster {
					return node.LogicZone, nil
				}
			}
		}
	}

	resource.LoggerTask.Warning(ctx, "resource get LogicZone failed", logit.String("appId", app.AppId))
	return "", cerrs.ErrNotFound.Errorf("resource app(%s) blb LogicZone not found", app.AppId)
}

// 获取 PhysicalZone
func getPhysicalZone(ctx context.Context, app *x1model.Application) (string, error) {
	// 区分主从角色来获取shard master节点
	csCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get csmaster cluster info failed", logit.String("appId", app.AppId))
		return "", err
	}

	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			node := node
			if len(app.AppGroupID) != 0 && csCluster.GroupRole == 2 {
				if isNodeAvailable(node) {
					return node.Azone, nil
				}
			} else {
				if isNodeAvailable(node) && node.Role == x1model.RoleTypeMaster {
					return node.Azone, nil
				}
			}
		}
	}

	resource.LoggerTask.Warning(ctx, "resource get PhysicalZone failed", logit.String("appId", app.AppId))
	return "", cerrs.ErrNotFound.Errorf("resource app(%s) blb PhysicalZone not found", app.AppId)
}

// 查询特定用户的物理 zone 对应的逻辑 zone
func getLogicZoneByPhysicalZone(ctx context.Context, userID string, physicalZone string) (string, error) {
	var logicZone string

	zoneMapperFunc, err := zoneComponent.ZoneOp().GetZoneMap(ctx, userID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return "", err
	}
	logicZone, found := zoneMapperFunc(physicalZone, false)
	if !found {
		return "", cerrs.ErrNotFound.Errorf("LogicZone not found by userID(%s) physicalZone(%s)", userID, physicalZone)
	}
	return logicZone, nil
}

func getPhysicalZoneByLogicZone(ctx context.Context, userID string, logicZone string) (string, error) {
	zoneMapperFunc, err := zoneComponent.ZoneOp().GetZoneMap(ctx, userID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get zone map failed", logit.Error("error", err))
		return "", err
	}
	physicalZone, found := zoneMapperFunc(logicZone, true)
	if !found {
		return "", cerrs.ErrNotFound.Errorf("PhysicalZone not found by userID(%s) logicZone(%s)", userID, logicZone)
	}
	return physicalZone, nil
}
