/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/06/21 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file init_readonly_appblb.go
 * <AUTHOR>
 * @date 2022/06/21 15:56:35
 * @brief init readonly app blb
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessInitRoAppBLB process app blb
func ProcessInitRoAppBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 0. 用户 VPC 是否与资源 VPC 是同一个
	env := blbv2.Instance().GetEnv(ctx)
	if !privatecloud.IsPrivateENV() && app.UserId != env.ResourcePrivateUserId && app.ResourceType == "container" {
		if err := saveResourceInfo(ctx, app); err != nil {
			return err
		}
	}

	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if roGroup.Status == x1model.RoGroupCreating {
		var userId string
		// 1. 创建BLB并Save
		if err := createAppBLB(ctx, app); err != nil {
			return err
		}
		var lb *x1model.BLB
		for _, b := range app.BLBs {
			if b.RoGroupID == roGroup.RoGroupShowID {
				lb = b
				if len(b.ResourceUserId) == 0 {
					userId = app.UserId
				} else {
					userId = b.ResourceUserId
				}
				break
			}
		}

		// // 判断blb是否可用
		// if err := checkAppBLBStatus(ctx, app); err != nil {
		// 	return err
		// }

		// 2. 调用Csmaster接口，将blb信息存入csmaster数据库
		roGroup.BlbID = lb.BlbId
		roGroup.IP = lb.Ovip
		if err := resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
			return err
		}

		// 3. 创建BLB ListenPort，Listner以及Backend均为app的port
		createListenerParams := &blbv2.CommonListenerParams{
			UserID:       userId,
			BlbID:        lb.BlbId,
			ListenerPort: roGroup.ListenerPort,
			Scheduler:    "RoundRobin",
		}
		if err = blbv2.Instance().CreateAppTCPListener(ctx, createListenerParams); err != nil {
			resource.LoggerTask.Warning(ctx, "create app tcp listener failed,", logit.Error("err", err))
			return err
		}

		// 4. 创建blb ip group
		createIPGroupParams := &blbv2.CommonBLBParams{
			UserID: userId,
			ElbID:  lb.BlbId,
		}
		groupID, err := blbv2.Instance().CreateAppIPGroup(ctx, createIPGroupParams)
		if err != nil {
			return nil
		}

		lb.IPGroupID = groupID
		blbList := []*x1model.BLB{lb}
		if err = x1model.BLBsSave(ctx, blbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}

		// 5. 创建ipgroup policy
		createIPGroupPolicyParams := &blbv2.CommonIPGroupParams{
			UserID:    userId,
			BlbID:     lb.BlbId,
			IPGroupID: lb.IPGroupID,
			Type:      "TCP",
		}
		if err = blbv2.Instance().CreateAppIPGroupPolicy(ctx, createIPGroupPolicyParams); err != nil {
			return err
		}

		// 6. 创建监听器policy（绑定ipgroup）
		appPolicy := &blbv2.AppPolicyVos{
			IPGroupID: lb.IPGroupID,
			Priority:  100,
			Desc:      "",
			RuleList: []*blbv2.RuleList{
				{
					Key:   "*",
					Value: "*",
				},
			},
		}
		createPolicyParams := &blbv2.CreateAppPolicysParams{
			UserID:       userId,
			BlbID:        lb.BlbId,
			ListenerPort: roGroup.ListenerPort,
			Type:         "TCP",
			GroupType:    "ip",
			AppPolicyVos: []*blbv2.AppPolicyVos{appPolicy},
		}

		if err := blbv2.Instance().CreateAppPolicys(ctx, createPolicyParams); err != nil {
			return err
		}
	}
	return nil
}
