/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/03/31
 * File: rebind_blb.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package blb TODO package function desc
package blb

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessRebingStandaloneRs 重启，重新挂载Blb
func ProcessRebingStandaloneRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 更新所有节点的blb绑定状态
	if err := updateRsBinding(ctx, app, false); err != nil {
		return err
	}

	return nil
}

// ProcessRebingProxyRs process proxy blb rs include app blb
func ProcessRebingProxyRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if utils.CheckSyncGroupField(app) {
		return nil
	}

	blbIds := make([]string, 0)
	appBlbIDs := make([]*x1model.BLB, 0)
	for _, blb := range app.BLBs {
		if len(blb.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = blb.ResourceUserId
		}
		if blb.Type == x1model.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbId)
		}
		if blb.Type == x1model.BLBTypeApp {
			appBlbIDs = append(appBlbIDs, blb)
		}
	}

	if len(blbIds) <= 0 && len(appBlbIDs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	if len(blbIds) > 0 {
		toBindRsList := []*blb.Rs{}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				switch proxy.Status {
				case x1model.NodeOrProxyStatusToCreate, x1model.NodeOrProxyStatusInUse:
					var uuid string
					if app.ResourceType == "container" {
						uuid = proxy.ContainerId
					} else {
						uuid = proxy.ResourceId
					}
					toBindRsList = append(toBindRsList, &blb.Rs{
						UUID:   uuid,
						Weight: 1,
						Port:   int32(proxy.Port),
					})
				}
			}
		}
		if len(toBindRsList) > 0 {
			bindReq := &blb.BindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				Rss:    toBindRsList,
			}
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("rss", base_utils.Format(toBindRsList)))
				return errors.BindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIDs) > 0 {
		// 处理proxy的应用型BLB的逻辑
		toBindRsList := []*blbv2.Rs{}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				switch proxy.Status {
				case x1model.NodeOrProxyStatusToCreate, x1model.NodeOrProxyStatusInUse:
					var uuid string
					if app.ResourceType == "container" {
						uuid = proxy.ContainerId
					} else {
						uuid = proxy.ResourceId
					}
					toBindRsList = append(toBindRsList, &blbv2.Rs{
						UUID:   uuid,
						Weight: 1,
						Port:   proxy.Port,
						IP:     proxy.Ip,
					})
				}
			}
		}
		if len(toBindRsList) > 0 {
			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIDs {
				blb := blb
				bindReq := &blbv2.BindRsParams{
					UserID:  userId,
					BLBID:   blb.BlbId,
					IPGroup: blb.IPGroupID,
					Rss:     toBindRsList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().BindRs(ctx, bindReq)
					})
				})
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
					logit.String("appId", app.AppId),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2BindRs", err))
				return err
			}
		}
	}

	return nil
}

// ProcessRebingReadonlyRs 重启，重新挂载Blb
func ProcessRebingReadonlyRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}
	roGroups, err := resource.CsmasterOpAgent.GetReadonlyGroupsByClusterID(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if len(roGroups) == 0 {
		return nil
	}

	// 更新所有节点的blb绑定状态
	for _, roGroup := range roGroups {
		// 已经删除的只读组不在处理blb
		if roGroup.Status == x1model.RoGroupDeleted || roGroup.Status == x1model.RoGroupCreateFail {
			continue
		}

		if err := BindReadonlyInstRs(ctx, app, roGroup); err != nil {
			return err
		}
	}
	return nil
}

// BindReadonlyInstRs will unbind rs
func BindReadonlyInstRs(ctx context.Context, app *x1model.Application, roGroup *csmaster_model_interface.ReadonlyGroup) error {
	var userId string

	blbIds := make([]string, 0)
	appBlbIds := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeReadOnly {
			blbIds = append(blbIds, b.BlbId)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeAppReadOnly {
			appBlbIds = append(appBlbIds, b)
		}
	}

	if len(blbIds) == 0 && len(appBlbIds) == 0 {
		return nil
	}

	// 获取待绑节点
	nodes := make([]*x1model.RoNode, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.RoGroupID == roGroup.RoGroupShowID {
				nodes = append(nodes, node)
			}
		}
	}

	if len(blbIds) != 0 {
		if len(nodes) > 0 {
			rss := make([]*blb.Rs, 0)
			for _, n := range nodes {
				var uuid string
				if app.ResourceType == "container" {
					uuid = n.ContainerId
				} else {
					uuid = n.ResourceId
				}

				rss = append(rss, &blb.Rs{
					UUID:   uuid,
					Weight: int32(n.RoGroupWeight),
					Port:   int32(n.Port),
				})
			}

			bindReq := &blb.BindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				Rss:    rss,
			}
			// 里面会做是否已经绑定的判断
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("rss", base_utils.Format(rss)))
				return errors.BindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIds) != 0 {
		// 重新绑定app blbs
		rss := make([]*blbv2.Rs, 0)
		for _, n := range nodes {
			var uuid string
			if app.ResourceType == "container" {
				uuid = n.ContainerId
			} else {
				uuid = n.ResourceId
			}
			rss = append(rss, &blbv2.Rs{
				UUID:   uuid,
				Weight: n.RoGroupWeight,
				Port:   n.Port,
				IP:     n.Ip,
			})
		}

		g := &gtask.Group{
			Concurrent:    2,
			AllowSomeFail: false,
		}
		totalCount := 0
		for _, blb := range appBlbIds {
			blb := blb
			bindReq := &blbv2.BindRsParams{
				UserID:  userId,
				BLBID:   blb.BlbId,
				IPGroup: blb.IPGroupID,
				Rss:     rss,
			}
			g.Go(func() error {
				return gtask.NoPanic(func() error {
					return blbv2.Instance().BindRs(ctx, bindReq)
				})
			})
		}

		if succCount, err := g.Wait(); err != nil {
			resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
				logit.String("appId", app.AppId),
				logit.Int("totalCount", totalCount),
				logit.Int("succCount", succCount),
				logit.Error("doblbv2BindRs", err))
			return err
		}
	}

	return nil
}

// ProcessRebingProxyMcpackRs process proxy blb rs include app blb
func ProcessRebingProxyMcpackRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if utils.CheckSyncGroupField(app) {
		return nil
	}

	if !util.UseMcpackProtocol(app.UserId) {
		return nil
	}

	blbIds := make([]string, 0)
	appBlbIDs := make([]*x1model.BLB, 0)
	for _, blb := range app.BLBs {
		if len(blb.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = blb.ResourceUserId
		}
		if blb.Type == x1model.BLBTypeNormal {
			blbIds = append(blbIds, blb.BlbId)
		}
		if blb.Type == x1model.BLBTypeApp {
			appBlbIDs = append(appBlbIDs, blb)
		}
	}

	if len(blbIds) <= 0 && len(appBlbIDs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	if len(blbIds) > 0 {
		toBindRsList := []*blb.Rs{}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				switch proxy.Status {
				case x1model.NodeOrProxyStatusToCreate, x1model.NodeOrProxyStatusInUse:
					var uuid string
					if app.ResourceType == "container" {
						uuid = proxy.ContainerId
					} else {
						uuid = proxy.ResourceId
					}
					toBindRsList = append(toBindRsList, &blb.Rs{
						UUID:   uuid,
						Weight: 1,
						Port:   int32(proxy.McpackPort),
					})
				}
			}
		}
		if len(toBindRsList) > 0 {
			bindReq := &blb.BindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				Rss:    toBindRsList,
			}
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("rss", base_utils.Format(toBindRsList)))
				return errors.BindRsFail.Wrap(err)
			}
		}
	}

	if len(appBlbIDs) > 0 {
		// 处理proxy的应用型BLB的逻辑
		toBindRsList := []*blbv2.Rs{}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				switch proxy.Status {
				case x1model.NodeOrProxyStatusToCreate, x1model.NodeOrProxyStatusInUse:
					var uuid string
					if app.ResourceType == "container" {
						uuid = proxy.ContainerId
					} else {
						uuid = proxy.ResourceId
					}
					toBindRsList = append(toBindRsList, &blbv2.Rs{
						UUID:   uuid,
						Weight: 1,
						Port:   proxy.McpackPort,
						IP:     proxy.Ip,
					})
				}
			}
		}
		if len(toBindRsList) > 0 {
			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIDs {
				blb := blb
				bindReq := &blbv2.BindRsParams{
					UserID:  userId,
					BLBID:   blb.BlbId,
					IPGroup: blb.McpackIPGroupID,
					Rss:     toBindRsList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().BindRs(ctx, bindReq)
					})
				})
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
					logit.String("appId", app.AppId),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2BindRs", err))
				return err
			}
		}
	}

	return nil
}

func ProcessRebingProxyRsForToDeleteBlbs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	toDeleteBlbIds := []string{}
	if err := json.Unmarshal([]byte(teu.Parameters), &toDeleteBlbIds); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal toDeleteBlbIds fail",
			logit.String("appId", teu.Entity),
			logit.Error("unmarshalError", err))
		return fmt.Errorf("unmarshal toDeleteBlbIds: %w", err)
	}
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	var toDeleteBlbs []*x1model.BLBToDelete
	db, err := x1model.GetDbAgent(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get db agent fail", logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := db.GetAllByCond(ctx, &toDeleteBlbs,
		"app_id = ? AND status = ? AND blb_id IN ?", teu.Entity, x1model.BLBStatusAvailable, toDeleteBlbIds); err != nil {
		resource.LoggerTask.Warning(ctx, "get blb to delete fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if len(toDeleteBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}
	// 更新所有节点的blb绑定状态
	blbs := blbToDeletesToBlbs(toDeleteBlbs)
	if err := updateProxyRsForAppBlbs(ctx, blbs, app, false); err != nil {
		resource.LoggerTask.Warning(ctx, "update proxy rs for app blbs fail",
			logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	if util.UseMcpackProtocol(app.UserId) {
		if err := updateProxyRsForAppBlbs(ctx, blbs, app, true); err != nil {
			resource.LoggerTask.Warning(ctx, "update mcpack rs for app blbs fail",
				logit.String("appId", teu.Entity),
				logit.Error("error", err))
			return err
		}
	}
	for _, b := range toDeleteBlbs {
		b.Status = "rebind"
	}
	if err := db.FullSaveAssociationsSave(ctx, toDeleteBlbs); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb to delete fail",
			logit.String("appId", app.AppId),
			logit.String("blbIds", base_utils.Format(toDeleteBlbIds)),
			logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func ProcessUnbindProxyRsForToDeleteBlbs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	toDeleteBlbIds := []string{}
	if err := json.Unmarshal([]byte(teu.Parameters), &toDeleteBlbIds); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal toDeleteBlbIds fail",
			logit.String("appId", teu.Entity),
			logit.Error("unmarshalError", err))
		return fmt.Errorf("unmarshal toDeleteBlbIds: %w", err)
	}
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	var toDeleteBlbs []*x1model.BLBToDelete
	db, err := x1model.GetDbAgent(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get db agent fail", logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := db.GetAllByCond(ctx, &toDeleteBlbs,
		"app_id = ? AND status = ? AND blb_id IN ?", teu.Entity, "rebind", toDeleteBlbIds); err != nil {
		resource.LoggerTask.Warning(ctx, "get blb to delete fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if len(toDeleteBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to unbind, skip")
		return nil
	}
	blbs := blbToDeletesToBlbs(toDeleteBlbs)
	if err := procesUnbindAllProxysForDisableCrossAzNearest(ctx, app, blbs); err != nil {
		resource.LoggerTask.Warning(ctx, "unbind all proxys for disable cross az nearest fail",
			logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return err
	}
	for _, b := range toDeleteBlbs {
		b.Status = x1model.BLBStatusAvailable
	}
	if err := db.FullSaveAssociationsSave(ctx, toDeleteBlbs); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb to delete fail",
			logit.String("appId", app.AppId),
			logit.String("blbIds", base_utils.Format(toDeleteBlbIds)),
			logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func blbToDeletesToBlbs(blbs []*x1model.BLBToDelete) []*x1model.BLB {
	var blbList []*x1model.BLB
	for _, b := range blbs {
		blbList = append(blbList, &x1model.BLB{
			AppId:                  b.AppId,
			Name:                   b.Name,
			Type:                   b.Type,
			VpcId:                  b.VpcId,
			SubnetId:               b.SubnetId,
			IpType:                 b.IpType,
			BgwGroupId:             b.BgwGroupId,
			BgwGroupExclusive:      b.BgwGroupExclusive,
			BgwGroupMode:           b.BgwGroupMode,
			MasterAZ:               b.MasterAZ,
			SlaveAZ:                b.SlaveAZ,
			BlbId:                  b.BlbId,
			Vip:                    b.Vip,
			Ovip:                   b.Ovip,
			Ipv6:                   b.Ipv6,
			Status:                 b.Status,
			CreateAt:               b.CreateAt,
			UpdateAt:               b.UpdateAt,
			RoGroupID:              b.RoGroupID,
			IPGroupID:              b.IPGroupID,
			ResourceUserId:         b.ResourceUserId,
			ResourceVpcId:          b.ResourceVpcId,
			ResourceSubnetId:       b.ResourceSubnetId,
			ServicePublishEndpoint: b.ServicePublishEndpoint,
			EndpointId:             b.EndpointId,
			EndpointIp:             b.EndpointIp,
			McpackIPGroupID:        b.McpackIPGroupID,
			AzoneForCrossAzNearest: b.AzoneForCrossAzNearest,
		})
	}
	return blbList
}
