/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/18 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file init_readonly_blb.go
 * <AUTHOR>
 * @date 2022/05/18 17:17:06
 * @brief init readonly blb
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessInitRoBLB process blb
func ProcessInitRoBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if roGroup.Status == x1model.RoGroupCreating {
		// 1. 创建BLB并Save
		if err := createBLB(ctx, app); err != nil {
			return err
		}
		var lb *x1model.BLB
		for _, b := range app.BLBs {
			if b.RoGroupID == roGroup.RoGroupShowID {
				lb = b
				break
			}
		}

		// 2. 调用Csmaster接口，将blb信息存入csmaster数据库
		roGroup.BlbID = lb.BlbId
		roGroup.IP = lb.Ovip
		if err := resource.CsmasterOpAgent.UpdateReadonlyGroupModel(ctx, roGroup); err != nil {
			return err
		}

		// 3. 创建BLB ListenPort，Listner以及Backend均为app的port
		backend_port := getBackendPort(ctx, app)
		createListenerParams := &blb.CreateListenerParam{
			UserID:      app.UserId,
			BLBIDs:      []string{lb.BlbId},
			ListenPort:  int32(roGroup.ListenerPort),
			BackendPort: int32(backend_port),
		}
		// 里面会判断是否已经创建
		if err := blb.Instance().CreateListener(ctx, createListenerParams); err != nil {
			resource.LoggerTask.Warning(ctx, "create listener fail", logit.String("appId", app.AppId))
			return errors.CreateListenerFail.Wrap(err)
		}
	}
	return nil
}
