/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
2021/12/29, by shangshuai02(<EMAIL>), first version
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// 用于标准版升级到集群版时使用
func ProcessSetProxyRsForModifyType(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := checkProxyAlive(ctx, app); err != nil {
		return err
	}
	return updateProxyBindingForModifyType(ctx, app)
}

// 用于标准版升级到集群版时使用
func updateProxyBindingForModifyType(ctx context.Context, app *x1model.Application) error {
	// 1. Get blb
	userID, blbIDs, appBlbs := getBlbList(ctx, app)
	if len(blbIDs) <= 0 && len(appBlbs) <= 0 {
		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
		return nil
	}

	// 2. Get tobind rs(toCreate Proxy)
	toBindRsList, toBindNodeList := getToCreateProxyList(ctx, app)
	resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(toBindRsList)))

	// 3. Get unbind rs
	toUnbindRsIDList, toUnbindNodeList := getToDeleteNodeList(ctx, app)
	resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(toUnbindRsIDList)))

	// 4. normal blb bind && unbind
	if len(blbIDs) > 0 {
		if len(toBindRsList) > 0 {
			bindReq := &blb.BindRsParam{
				UserID: userID,
				BLBIDs: blbIDs,
				Rss:    toBindRsList,
			}
			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "bind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIDs", base_utils.Format(blbIDs)),
					logit.String("rss", base_utils.Format(toBindRsList)))
				return errors.BindRsFail.Wrap(err)
			}
		}
		if len(toUnbindRsIDList) > 0 {
			unbindReq := &blb.UnbindRsParam{
				UserID: userID,
				BLBIDs: blbIDs,
				UUIDs:  toUnbindRsIDList,
			}
			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIDs", base_utils.Format(blbIDs)),
					logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
				return errors.UnBindRsFail.Wrap(err)
			}
		}
	}

	// 5. normal blb bind && unbind
	if len(appBlbs) > 0 {
		if len(toBindNodeList) > 0 {
			// bind
			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbs {
				blb := blb
				bindReq := &blbv2.BindRsParams{
					UserID:  userID,
					BLBID:   blb.BlbId,
					IPGroup: blb.IPGroupID,
					Rss:     toBindNodeList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().BindRs(ctx, bindReq)
					})
				})
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
					logit.String("appId", app.AppId),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2BindRs", err))
				return err
			}
		}
		if len(toUnbindNodeList) > 0 {
			// unbind
			IPList := make([]string, 0)
			for _, n := range toUnbindNodeList {
				rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
				IPList = append(IPList, rsIpPort)
			}

			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbs {
				blb := blb
				unbindReq := &blbv2.UnbindRsParams{
					UserID:     userID,
					BLBID:      blb.BlbId,
					IPGroup:    blb.IPGroupID,
					MemberList: IPList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().UnbindRs(ctx, unbindReq)
					})
				})
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
					logit.String("appId", app.AppId),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2UnBindRs", err))
				return err
			}
		}
	}
	return nil
}

// 1. Get blb
func getBlbList(ctx context.Context, app *x1model.Application) (userID string, blbIDs []string, appBlbs []*x1model.BLB) {
	for _, blb := range app.BLBs {
		if len(blb.ResourceUserId) == 0 {
			userID = app.UserId
		} else {
			userID = blb.ResourceUserId
		}
		if blb.Type == x1model.BLBTypeNormal {
			blbIDs = append(blbIDs, blb.BlbId)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				continue
			}
			appBlbs = append(appBlbs, blb)
		}
	}
	return userID, blbIDs, appBlbs
}

// 2. Get toCreate Proxy
func getToCreateProxyList(ctx context.Context, app *x1model.Application) ([]*blb.Rs, []*blbv2.Rs) {
	toBindRsList := []*blb.Rs{}
	toBindNodeList := []*blbv2.Rs{}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			var uuid string
			if app.ResourceType == "container" {
				uuid = proxy.ContainerId
			} else {
				uuid = proxy.ResourceId
			}
			switch proxy.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toBindRsList = append(toBindRsList, &blb.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   int32(proxy.Port),
				})
				toBindNodeList = append(toBindNodeList, &blbv2.Rs{
					UUID:   uuid,
					Weight: 1,
					Port:   proxy.Port,
					IP:     proxy.Ip,
				})
			}
		}
	}
	return toBindRsList, toBindNodeList
}

// 3. Get toDelete Node
func getToDeleteNodeList(ctx context.Context, app *x1model.Application) ([]string, []*x1model.Node) {
	toUnbindRsIDList := []string{}
	toUnbindNodeList := []*x1model.Node{}
	for _, cluster := range app.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId != fmt.Sprintf("%s-0_todelete", app.AppId) {
			continue
		}

		for _, node := range cluster.Nodes {
			var uuid string
			if app.ResourceType == "container" {
				uuid = node.ContainerId
			} else {
				uuid = node.ResourceId
			}
			if node.Role == x1model.RoleTypeMaster {
				toUnbindRsIDList = append(toUnbindRsIDList, uuid)
				toUnbindNodeList = append(toUnbindNodeList, node)
			}
		}
	}
	return toUnbindRsIDList, toUnbindNodeList
}
