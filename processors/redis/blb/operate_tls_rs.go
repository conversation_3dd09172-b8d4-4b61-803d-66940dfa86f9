package blb

// import (
// 	"context"
// 	"fmt"

// 	"icode.baidu.com/baidu/gdp/extension/gtask"
// 	"icode.baidu.com/baidu/gdp/logit"
// 	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
// 	"icode.baidu.com/baidu/scs/x1-base/component/blb"
// 	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
// 	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
// 	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
// 	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
// 	"icode.baidu.com/baidu/scs/x1-task/library/errors"
// 	"icode.baidu.com/baidu/scs/x1-task/library/resource"
// )

// func ProcessBindProxyTlsRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
// 	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
// 	if err != nil {
// 		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
// 			logit.Error("dbError", err))
// 		return cerrs.ErrDbQueryFail.Wrap(err)
// 	}

// 	return bindProxyTlsRs(ctx, app)
// }

// func bindProxyTlsRs(ctx context.Context, app *x1model.Application) error {
// 	blbIds := make([]string, 0)
// 	appBlbIds := make([]*x1model.BLB, 0)
// 	var userId string

// 	for _, blb := range app.BLBs {
// 		if len(blb.ResourceUserId) == 0 {
// 			userId = app.UserId
// 		} else {
// 			userId = blb.ResourceUserId
// 		}
// 		if blb.Type == x1model.BLBTypeNormal {
// 			blbIds = append(blbIds, blb.BlbId)
// 		}
// 		if blb.Type == x1model.BLBTypeApp {
// 			appBlbIds = append(appBlbIds, blb)
// 		}
// 	}

// 	if len(blbIds) <= 0 && len(appBlbIds) <= 0 {
// 		resource.LoggerTask.Notice(ctx, "no blbs to bind, skip")
// 		return nil
// 	}

// 	toBindRsList := []*blb.Rs{}
// 	toBindNodeList := []*blbv2.Rs{}
// 	toUnbindRsIDList := []string{}
// 	toUnbindNodeList := []*x1model.Proxy{}
// 	for _, itf := range app.Interfaces {
// 		for _, proxy := range itf.Proxys {
// 			var uuid string
// 			if app.ResourceType == "container" {
// 				uuid = proxy.ContainerId
// 			} else {
// 				uuid = proxy.ResourceId
// 			}
// 			switch proxy.Status {
// 			/*
// 				1、新集群开启tls:x1model.NodeOrProxyStatusToUpgrade
// 				2、扩容节点:x1model.NodeOrProxyStatusToCreate
// 			*/
// 			case x1model.NodeOrProxyStatusToCreate, x1model.NodeOrProxyStatusTlsOpening:
// 				toBindRsList = append(toBindRsList, &blb.Rs{
// 					UUID:   uuid,
// 					Weight: 1,
// 					Port:   int32(proxy.McpackPort),
// 				})
// 				toBindNodeList = append(toBindNodeList, &blbv2.Rs{
// 					UUID:   uuid,
// 					Weight: 1,
// 					Port:   proxy.TlsPort,
// 					IP:     proxy.Ip,
// 				})
// 			/*
// 				1、缩容或下线节点:x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete
// 			*/
// 			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete, x1model.NodeOrProxyStatusTlsClosing:
// 				toUnbindRsIDList = append(toUnbindRsIDList, uuid)
// 				toUnbindNodeList = append(toUnbindNodeList, proxy)
// 			}
// 		}
// 	}
// 	resource.LoggerTask.Notice(ctx, "to bind rs", logit.String("toBindRsList", base_utils.Format(toBindRsList)))
// 	resource.LoggerTask.Notice(ctx, "to unbind rs", logit.String("toUnbindRsIdList", base_utils.Format(toUnbindRsIDList)))

// 	if len(blbIds) > 0 {
// 		if len(toBindRsList) > 0 {
// 			bindReq := &blb.BindRsParam{
// 				UserID: userId,
// 				BLBIDs: blbIds,
// 				Rss:    toBindRsList,
// 			}
// 			if err := blb.Instance().BindRs(ctx, bindReq); err != nil {
// 				resource.LoggerTask.Warning(ctx, "bind rs fail",
// 					logit.String("appId", app.AppId),
// 					logit.String("blbIds", base_utils.Format(blbIds)),
// 					logit.String("rss", base_utils.Format(toBindRsList)))
// 				return errors.BindRsFail.Wrap(err)
// 			}
// 		}
// 		if len(toUnbindRsIDList) > 0 {
// 			unbindReq := &blb.UnbindRsParam{
// 				UserID: userId,
// 				BLBIDs: blbIds,
// 				UUIDs:  toUnbindRsIDList,
// 			}
// 			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
// 				resource.LoggerTask.Warning(ctx, "unbind rs fail",
// 					logit.String("appId", app.AppId),
// 					logit.String("blbIds", base_utils.Format(blbIds)),
// 					logit.String("UUIDs", base_utils.Format(toUnbindRsIDList)))
// 				return errors.UnBindRsFail.Wrap(err)
// 			}
// 		}
// 	}

// 	if len(appBlbIds) > 0 {
// 		if len(toBindNodeList) > 0 {
// 			// bind
// 			g := &gtask.Group{
// 				Concurrent:    2,
// 				AllowSomeFail: false,
// 			}
// 			totalCount := 0
// 			for _, blb := range appBlbIds {
// 				blb := blb
// 				bindReq := &blbv2.BindRsParams{
// 					UserID:  userId,
// 					BLBID:   blb.BlbId,
// 					IPGroup: blb.TlsIPGroupID,
// 					Rss:     toBindNodeList,
// 				}
// 				g.Go(func() error {
// 					return gtask.NoPanic(func() error {
// 						return blbv2.Instance().BindRs(ctx, bindReq)
// 					})
// 				})
// 			}

// 			if succCount, err := g.Wait(); err != nil {
// 				resource.LoggerTask.Warning(ctx, "some errors occur bind blb rs",
// 					logit.String("appId", app.AppId),
// 					logit.Int("totalCount", totalCount),
// 					logit.Int("succCount", succCount),
// 					logit.Error("doblbv2BindRs", err))
// 				return err
// 			}
// 		}
// 		if len(toUnbindNodeList) > 0 {
// 			// unbind
// 			IPList := make([]string, 0)
// 			for _, n := range toUnbindNodeList {
// 				rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
// 				IPList = append(IPList, rsIpPort)
// 			}

// 			g := &gtask.Group{
// 				Concurrent:    2,
// 				AllowSomeFail: false,
// 			}
// 			totalCount := 0
// 			for _, blb := range appBlbIds {
// 				blb := blb
// 				unbindReq := &blbv2.UnbindRsParams{
// 					UserID:     userId,
// 					BLBID:      blb.BlbId,
// 					IPGroup:    blb.TlsIPGroupID,
// 					MemberList: IPList,
// 				}
// 				g.Go(func() error {
// 					return gtask.NoPanic(func() error {
// 						return blbv2.Instance().UnbindRs(ctx, unbindReq)
// 					})
// 				})
// 			}

// 			if succCount, err := g.Wait(); err != nil {
// 				resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
// 					logit.String("appId", app.AppId),
// 					logit.Int("totalCount", totalCount),
// 					logit.Int("succCount", succCount),
// 					logit.Error("doblbv2UnBindRs", err))
// 				return err
// 			}
// 		}
// 	}
// 	return nil
// }
