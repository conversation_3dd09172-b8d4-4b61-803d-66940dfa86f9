/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/18 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file del_readonly_blb.go
 * <AUTHOR>
 * @date 2022/05/18 19:44:41
 * @brief del readonly group blb
 *
 **/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessCreatingRoErrorDelBLB 只读组删除blb
func ProcessCreatingRoErrorDelBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get ro group error", logit.Error("error", err))
		return err
	}
	// 只有创建只读组并创建blb失败才删除
	if roGroup.Status == x1model.RoGroupCreateFail {
		// 1. 删除BLB并Save
		if err := deleteBLBForRo(ctx, app, roGroup); err != nil {
			return err
		}
	}

	return nil
}

// ProcessDeletingRoDelBLB 只读组删除blb
func ProcessDeletingRoDelBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.DeleteRoGroupParams.ReadonlyGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get ro group error", logit.Error("error", err))
		return err
	}

	// 1. 删除BLB并Save
	if err := deleteBLBForRo(ctx, app, roGroup); err != nil {
		return err
	}

	return nil
}

// deleteBLBForRo 删除BLB并Save
func deleteBLBForRo(ctx context.Context, app *x1model.Application, roGroup *csmaster_model_interface.ReadonlyGroup) error {
	var userId string
	// 获取待删除的BLB列表
	var deleteBLBIDs []string
	var deleteBlbList []*x1model.BLB
	var deleteAppBLBIDs []string
	var deleteAppBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Status == x1model.BLBStatusDeleted {
			continue
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Status != x1model.BLBStatusToCreate && b.Type == x1model.BLBTypeReadOnly {
			deleteBLBIDs = append(deleteBLBIDs, b.BlbId)
			deleteBlbList = append(deleteBlbList, b)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Status == x1model.BLBStatusToCreate && b.Type == x1model.BLBTypeReadOnly {
			deleteBlbList = append(deleteBlbList, b)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Status != x1model.BLBStatusToCreate && b.Type == x1model.BLBTypeAppReadOnly {
			deleteAppBLBIDs = append(deleteBLBIDs, b.BlbId)
			deleteAppBlbList = append(deleteAppBlbList, b)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Status == x1model.BLBStatusToCreate && b.Type == x1model.BLBTypeAppReadOnly {
			deleteAppBlbList = append(deleteAppBlbList, b)
		}
	}

	if len(deleteBlbList) == 0 && len(deleteAppBlbList) == 0 {
		return nil
	}

	if len(deleteBLBIDs) > 0 {
		// 删除BLB
		deleteBlbParams := &blb.DeleteBLBParam{
			UserID: userId,
			BLBIDs: deleteBLBIDs,
		}
		err := blb.Instance().DeleteBLB(ctx, deleteBlbParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "delete blb fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(deleteBLBIDs)))
			return errors.DeleteBlbFail.Wrap(err)
		}

		// 更新数据库
		for _, b := range deleteBlbList {
			b.Status = x1model.BLBStatusDeleted
		}
		if err := x1model.BLBsSave(ctx, deleteBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	if len(deleteAppBLBIDs) > 0 {
		// 删除appBLB
		for _, b := range deleteAppBLBIDs {
			deleteBlbParams := &blbv2.CommonBLBParams{
				UserID: userId,
				ElbID:  b,
			}
			err := blbv2.Instance().DeleteAppBLB(ctx, deleteBlbParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "delete blb fail",
					logit.String("appId", app.AppId),
					logit.String("blbId", base_utils.Format(b)))
				return errors.DeleteBlbFail.Wrap(err)
			}
		}

		// 更新数据库
		for _, b := range deleteAppBlbList {
			b.Status = x1model.BLBStatusDeleted
		}
		if err := x1model.BLBsSave(ctx, deleteAppBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}
