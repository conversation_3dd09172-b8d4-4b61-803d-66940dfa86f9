// Copyright(C) 2025 Baidu Inc. All Rights Reserved.
// Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
// Date: 2025/02/05

package blb

import (
	"context"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
)

// ShiftLbForStandaloneGroupModifySpec 热活标准版变配优化2025
// wiki：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/p4L1DJjqguDBso
// 主地域修改只读组、统一入口LB
// 从地域修改普通、只读组、统一入口BLB
func ShiftLbForStandaloneGroupModifySpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return fmt.Errorf("app(%s) not found", teu.Entity)
	}
	// 非热活直接返回
	if len(app.AppGroupID) == 0 {
		return nil
	}
	// 非标准版直接返回
	if app.Type != x1model.AppTypeStandalone {
		return nil
	}
	if len(app.Clusters) != 1 {
		resource.LoggerTask.Warning(ctx, "app type is not standalone or clusters count is not 1",
			logit.String("appId", teu.Entity), logit.String("appType", app.Type))
		return errors.New("app type is not standalone or clusters count is not 1")
	}

	// 热活从地域修改普通BLB
	if !metaserver.IsGlobalLeader(ctx, app) {
		// 从：修改普通BLB
		if err := updateRsBindRsGlobal(ctx, app); err != nil {
			resource.LoggerTask.Error(ctx, "updateRsBindRsGlobal fail", logit.Error("error", err))
			return err
		}
	}
	// 主、从：修改只读组BLB
	if err := ProcessSetReadonlyGroupRs(ctx, teu); err != nil {
		resource.LoggerTask.Error(ctx, "ProcessSetReadonlyGroupRs fail", logit.Error("error", err))
		return err
	}

	// 主、从：修改统一入口BLB
	if err := updateEntranceRsBinding(ctx, app); err != nil {
		resource.LoggerTask.Error(ctx, "updateEntranceRsBinding fail", logit.Error("error", err))
		return err
	}
	return nil
}
