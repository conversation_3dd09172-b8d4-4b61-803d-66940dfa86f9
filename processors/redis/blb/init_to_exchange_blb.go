/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// ProcessInitToExchangeBLB 初始化toexchange的blb
func ProcessInitToExchangeBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 对于私有部署的, 根据env有可能创建normal blb
	if util.UseNormalBlb() {
		if err := createNormalBLB(ctx, app); err != nil {
			return err
		}
		if err := createListener(ctx, app); err != nil {
			return err
		}
	}
	// 0. 若用户是公有云用户，且使用了虚机混布，则将公有云混布池的资源账户记录到数据库中
	env := blbv2.Instance().GetEnv(ctx)
	if !privatecloud.IsPrivateENV() && app.UserId != env.ResourcePrivateUserId && app.ResourceType == "container" {
		// 只会修改未初始化blb的元信息
		if err := saveResourceInfo(ctx, app); err != nil {
			return err
		}
	}

	// 创建BLB并Save
	if err := createAppBLB(ctx, app); err != nil {
		return err
	}

	// 创建BLB ListenPort，Listner以及Backend均为app的port
	if err := createAppListener(ctx, app, x1model.BLBTypeAppToExchange); err != nil {
		return err
	}

	// 创建blb ip group
	if err := createAppIPGroup(ctx, app, x1model.BLBTypeAppToExchange); err != nil {
		return err
	}

	// 创建ipgroup policy
	if err := createAppIPGroupPolicy(ctx, app, x1model.BLBTypeAppToExchange); err != nil {
		return err
	}

	// 创建监听器policy（绑定ipgroup）
	if err := createAppListenerPolicy(ctx, app, x1model.BLBTypeAppToExchange); err != nil {
		return err
	}

	// 为特定用户创建mcpack的监听器
	if err := CreateAppBlBComponentForMcpack(ctx, app, x1model.BLBTypeAppToExchange); err != nil {
		return err
	}

	return nil
}
