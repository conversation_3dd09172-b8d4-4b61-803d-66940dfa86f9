/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2022/07/06, by wangbin34
*/

/*
DESCRIPTION
将后端资源信息录入到数据库中
*/

package blb

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// 记录资源层 vpc 等信息(用户 vpc 和 资源层 vpc 不在同一 vpc 时使用)
func saveResourceInfo(ctx context.Context, app *x1model.Application) error {
	var curBlbList []*x1model.BLB
	env := blbv2.Instance().GetEnv(ctx)

	azone, err := getPhysicalZone(ctx, app)
	if err != nil {
		return err
	}

	ResourceUserId := ""
	ResourceVpcId := ""
	ResourceSubnetId := ""
	if blbv2.Instance().IsUsePrivateResource(ctx, app.UserId) {
		ResourceUserId = env.ResourcePrivateUserId
		ResourceVpcId = env.ResourcePrivateVpcId
		subnetId, found := blbv2.Instance().GetResourcePrivateSubnet(ctx, azone)
		if !found {
			resource.LoggerTask.Warning(ctx, "resource_private get subnet failed",
				logit.String("appId", app.AppId),
				logit.String("azone", azone),
			)
			return cerrs.ErrNotFound.Errorf("resource_private app(%s) subnet not found for azone(%s)", app.AppId, azone)
		}
		ResourceSubnetId = subnetId
	} else {
		ResourceUserId = env.ResourceCloudUserId
		ResourceVpcId = env.ResourceCloudVpcId
		subnetId, found := blbv2.Instance().GetResourceCloudSubnet(ctx, azone)
		if !found {
			resource.LoggerTask.Warning(ctx, "resource_cloud get subnet failed",
				logit.String("appId", app.AppId),
				logit.String("azone", azone),
			)
			return cerrs.ErrNotFound.Errorf("resource_cloud app(%s) subnet not found for azone(%s)", app.AppId, azone)
		}
		ResourceSubnetId = subnetId
	}

	for _, b := range app.BLBs {
		if len(b.BlbId) != 0 {
			continue
		}
		b.ResourceUserId = ResourceUserId
		b.ResourceVpcId = ResourceVpcId
		b.ResourceSubnetId = ResourceSubnetId
		curBlbList = append(curBlbList, b)
	}

	if len(curBlbList) == 0 {
		return nil
	}

	if err := x1model.BLBsSave(ctx, curBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	return nil
}
