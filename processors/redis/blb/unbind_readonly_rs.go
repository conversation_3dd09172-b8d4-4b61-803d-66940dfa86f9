/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/05/17 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file unbind_readonly_rs.go
 * <AUTHOR>
 * @date 2022/05/17 18:55:22
 * @brief unbind readonly rs
 *
 **/

package blb

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// clientKillErrResult definiton
type clientKillErrResult struct {
	NodeID string `form:"nodeId"`
	Msg    string `form:"msg"`
}

// ProcessReadonlyInstanceUnbindRs unbind readonly instances rs of blb
func ProcessReadonlyInstanceUnbindRs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var userId string

	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	// 获取用户
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
	}

	// 获取只读组
	roGroupID := param.DeleteRoInstParams.ReadonlyGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 更新所有节点的blb绑定状态
	if err := unbindReadonlyInstanceRs(ctx, app, roGroup); err != nil {
		return err
	}

	// 重新负载均衡
	if err := rebalancedBLB(ctx, userId, app, roGroup); err != nil {
		return err
	}

	return nil
}

// rebalancedBLB will balance blb
func rebalancedBLB(ctx context.Context, userId string, app *x1model.Application, roGroup *csmaster_model_interface.ReadonlyGroup) error {
	if roGroup.IsBalanceReload == x1model.RebalanceOpen {
		toUpdateRs := make([]*blb.Rs, 0)
		toUpdateAppRs := make([]*blbv2.Rs, 0)
		blbIds := make([]string, 0)
		appBlbIds := make([]*x1model.BLB, 0)
		for _, b := range app.BLBs {
			if len(b.ResourceUserId) == 0 {
				userId = app.UserId
			} else {
				userId = b.ResourceUserId
			}
			if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeReadOnly {
				blbIds = append(blbIds, b.BlbId)
			}
			if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeAppReadOnly {
				appBlbIds = append(appBlbIds, b)
			}
		}

		for _, cluster := range app.Clusters {
			for _, node := range cluster.RoNodes {
				if node.RoGroupID == roGroup.RoGroupShowID && node.RoGroupStatus != x1model.RoInstDeleting {
					var uuid string
					if app.ResourceType == "container" {
						uuid = node.ContainerId
					} else {
						uuid = node.ResourceId
					}
					toUpdateRs = append(toUpdateRs, &blb.Rs{
						UUID:   uuid,
						Weight: int32(node.RoGroupWeight),
					})

					toUpdateAppRs = append(toUpdateAppRs, &blbv2.Rs{
						UUID:   uuid,
						IP:     node.Ip,
						Weight: node.RoGroupWeight,
						Port:   node.Port,
					})
				}
			}
		}
		if len(blbIds) > 0 {
			updateElbRsParams := &blb.UpdateElbRsForRoParams{
				UserID:     userId,
				BLbs:       roGroup.BlbID,
				Port:       int32(app.Port),
				ToUpdateRs: toUpdateRs,
			}
			if err := blb.Instance().UpdateElbsRsForRo(ctx, updateElbRsParams); err != nil {
				resource.LoggerTask.Warning(ctx, "blb update rs weight failed")
				return err
			}
		}

		if len(appBlbIds) > 0 {
			for _, b := range appBlbIds {
				updateAppElbRsParams := &blbv2.UpdateElbRsForRoParams{
					UserID:     userId,
					ToUpdateRs: toUpdateAppRs,
					BLBID:      b.BlbId,
					IPGroupID:  b.IPGroupID,
				}

				if err := blbv2.Instance().UpdateElbsRsForRo(ctx, updateAppElbRsParams); err != nil {
					resource.LoggerTask.Warning(ctx, "blb update rs weight failed")
					return err
				}
			}
		}

		// 获取acl信息
		acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
		if err != nil && !x1model.IsNotFound(err) {
			resource.LoggerTask.Warning(ctx, "get acl fail", logit.Error("err", err))
			return err
		}
		var password string
		if acl != nil && len(acl.Password) != 0 {
			password, _ = crypto_utils.DecryptKey(acl.Password)
		}
		// 执行client kill type normal ！！！ 仅记录错误，不返回错误
		errMsgMap := make(map[string]*clientKillErrResult)
		g := gtask.Group{}
		for _, n := range roGroup.CacheInstances {
			n := n
			if n.RoGroupStatus == x1model.RoInstDeleting {
				continue
			}
			errMsg := errMsgMap[n.Uuid]
			g.Go(func() error {
				_ = gtask.NoPanic(func() error {
					cli := single_redis.NewClient(n.FloatingIp, n.Port, single_redis.WithTimeout(&single_redis.ConfigTimeout{
						Connect: 500 * time.Millisecond,
						Read:    1000 * time.Millisecond,
						Write:   1000 * time.Millisecond,
					}), single_redis.WithPassword(password),
						single_redis.WithRetry(2))
					defer cli.Close()

					args := []string{
						"Type",
						"normal",
					}
					if err := cli.ClientKillByFilter(ctx, args...).Err(); err != nil {
						errMsg.Msg = err.Error()
					}
					return nil
				})
				return nil
			})
		}
		_, _ = g.Wait()
		ret := []*clientKillErrResult{}
		for _, errMsg := range errMsgMap {
			if len(errMsg.Msg) != 0 {
				ret = append(ret, errMsg)
			}
		}
		if len(ret) != 0 {
			resource.LoggerTask.Error(ctx, "some instance execute redis command failed",
				logit.String("errRets", base_utils.Format(ret)))
		}
	}
	return nil
}

// unbindReadonlyInstanceRs will unbind rs
func unbindReadonlyInstanceRs(ctx context.Context, app *x1model.Application, roGroup *csmaster_model_interface.ReadonlyGroup) error {
	var userId string

	blbIds := make([]string, 0)
	appBlbIds := make([]*x1model.BLB, 0)
	for _, b := range app.BLBs {
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeReadOnly {
			blbIds = append(blbIds, b.BlbId)
		}
		if b.RoGroupID == roGroup.RoGroupShowID && b.Type == x1model.BLBTypeAppReadOnly {
			appBlbIds = append(appBlbIds, b)
		}
	}

	if len(blbIds) == 0 && len(appBlbIds) == 0 {
		resource.LoggerTask.Debug(ctx, "no blb to unbind, skip")
		return nil
	}

	// 获取待删除节点
	nodes := make([]*x1model.RoNode, 0)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			if node.RoGroupStatus == x1model.RoInstDeleting {
				nodes = append(nodes, node)
			}
		}
	}
	if len(blbIds) > 0 {
		// 解绑rss
		if len(nodes) > 0 {
			uuidList := make([]string, 0)
			for _, n := range nodes {
				var uuid string
				if app.ResourceType == "container" {
					uuid = n.ContainerId
				} else {
					uuid = n.ResourceId
				}
				uuidList = append(uuidList, uuid)
			}
			unbindReq := &blb.UnbindRsParam{
				UserID: userId,
				BLBIDs: blbIds,
				UUIDs:  uuidList,
			}
			// 里面会做是否已经解绑的判断
			if err := blb.Instance().UnbindRs(ctx, unbindReq); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind rs fail",
					logit.String("appId", app.AppId),
					logit.String("blbIds", base_utils.Format(blbIds)),
					logit.String("UUIDs", base_utils.Format(uuidList)))
				return errors.UnBindRsFail.Wrap(err)
			}
		}
	}

	// 解绑APP rs
	if len(appBlbIds) > 0 {
		// 解绑rss
		if len(nodes) > 0 {
			IPList := make([]string, 0)
			for _, n := range nodes {
				rsIpPort := fmt.Sprintf("%s:%d", n.Ip, n.Port)
				IPList = append(IPList, rsIpPort)
			}

			g := &gtask.Group{
				Concurrent:    2,
				AllowSomeFail: false,
			}
			totalCount := 0
			for _, blb := range appBlbIds {
				blb := blb
				unbindReq := &blbv2.UnbindRsParams{
					UserID:     userId,
					BLBID:      blb.BlbId,
					IPGroup:    blb.IPGroupID,
					MemberList: IPList,
				}
				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return blbv2.Instance().UnbindRs(ctx, unbindReq)
					})
				})
			}

			if succCount, err := g.Wait(); err != nil {
				resource.LoggerTask.Warning(ctx, "some errors occur unbind blb rs",
					logit.String("appId", app.AppId),
					logit.Int("totalCount", totalCount),
					logit.Int("succCount", succCount),
					logit.Error("doblbv2UnBindRs", err))
				return err
			}
		}
	}

	return nil
}
