/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
初始化负载均衡
*/

package blb

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	blbv2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessDelBLB 删除BLB
// 1. 从interface表中找到所有BLB
// 2. 发送请求进行删除
// 相关代码 ElbComponents::delete_elb
// 不涉及解绑eip等，这里默认已经操作过
func ProcessDelBLB(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("app(%s) not found", teu.Entity)
	}

	// 1. 删除BLB并Save
	if err := deleteBLB(ctx, app); err != nil {
		return err
	}

	return nil
}

// deleteBLB 删除BLB并Save
func deleteBLB(ctx context.Context, app *x1model.Application) error {
	// 获取待删除的BLB列表
	var deleteBLBIDs []string
	var deleteBlbList []*x1model.BLB
	var deleteAppBlbIDs []string
	var deleteAppBlbList []*x1model.BLB
	var userId string

	for _, b := range app.BLBs {
		if len(b.BlbId) == 0 || b.Status == x1model.BLBStatusDeleted {
			continue
		}
		if len(b.ResourceUserId) == 0 {
			userId = app.UserId
		} else {
			userId = b.ResourceUserId
		}
		if b.Type == x1model.BLBTypeNormal {
			deleteBLBIDs = append(deleteBLBIDs, b.BlbId)
			deleteBlbList = append(deleteBlbList, b)
		}

		if b.Type == x1model.BLBTypeApp {
			deleteAppBlbIDs = append(deleteAppBlbIDs, b.BlbId)
			deleteAppBlbList = append(deleteAppBlbList, b)
		}
	}

	if len(deleteBLBIDs) == 0 && len(deleteAppBlbIDs) == 0 {
		return nil
	}

	if len(deleteBLBIDs) > 0 {
		// 删除BLB
		deleteBlbParams := &blb.DeleteBLBParam{
			UserID: userId,
			BLBIDs: deleteBLBIDs,
		}
		err := blb.Instance().DeleteBLB(ctx, deleteBlbParams)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "delete blb fail",
				logit.String("appId", app.AppId),
				logit.String("blbIds", base_utils.Format(deleteBLBIDs)))
			// blb sdk may return 404, old blb may delete by csmaster
			// return errors.DeleteBlbFail.Wrap(err)
		}

		// 更新数据库
		for _, b := range deleteBlbList {
			b.Status = x1model.BLBStatusDeleted
		}
		if err := x1model.BLBsSave(ctx, deleteBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	if len(deleteAppBlbIDs) > 0 {
		// 删除app BLB
		for _, b := range deleteAppBlbIDs {
			deleteBlbParams := &blbv2.CommonBLBParams{
				UserID: userId,
				ElbID:  b,
			}
			err := blbv2.Instance().DeleteAppBLB(ctx, deleteBlbParams)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "delete blb fail",
					logit.String("appId", app.AppId),
					logit.String("blbId", base_utils.Format(b)))
				return errors.DeleteBlbFail.Wrap(err)
			}
		}

		// 更新数据库
		for _, b := range deleteAppBlbList {
			b.Status = x1model.BLBStatusDeleted
		}
		if err := x1model.BLBsSave(ctx, deleteAppBlbList); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb fail", logit.String("appId", app.AppId))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}

	return nil
}

func ProcessFakeDeleteBlbsForDisableCrossAz(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	var toDeleteBlbList []*x1model.BLB
	for _, b := range app.BLBs {
		if b.Type != x1model.BLBTypeApp {
			continue
		}
		if b.AzoneForCrossAzNearest != "" && b.AzoneForCrossAzNearest != x1model.GlobalForCrossAzNearest {
			toDeleteBlbList = append(toDeleteBlbList, b)
		}
	}

	if err := x1model.BlbDelete(ctx, toDeleteBlbList); err != nil {
		resource.LoggerTask.Warning(ctx, "delete blb fail",
			logit.String("appId", app.AppId),
			logit.String("blbIds", base_utils.Format(toDeleteBlbList)))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func ProcessPurgeToDeleteBLBs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	var toDeleteBlbIds []string
	if err := json.Unmarshal([]byte(teu.Parameters), &toDeleteBlbIds); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal parameters fail", logit.String("parameters", teu.Parameters),
			logit.Error("error", err))
		return fmt.Errorf("unmarshal parameters fail, err: %w", err)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	var blbsToDelete []*x1model.BLBToDelete
	db, err := x1model.GetDbAgent(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get db agent fail", logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	if err := db.GetAllByCond(ctx, &blbsToDelete,
		"app_id = ? AND status = ? AND blb_id IN ?", teu.Entity, x1model.BLBStatusAvailable, toDeleteBlbIds); err != nil {
		resource.LoggerTask.Warning(ctx, "get blb to delete fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	for _, b := range blbsToDelete {
		userId := app.UserId
		if len(b.ResourceUserId) > 0 {
			userId = b.ResourceUserId
		}
		params := &blb.DeleteBLBParam{
			UserID: userId,
			BLBIDs: []string{b.BlbId},
		}
		if err := blb.Instance().DeleteBLB(ctx, params); err != nil {
			resource.LoggerTask.Warning(ctx, "delete blb fail",
				logit.String("appId", app.AppId),
				logit.String("blbId", b.BlbId),
				logit.Error("error", err))
			return errors.DeleteBlbFail.Wrap(err)
		}
		b.Status = x1model.BLBStatusDeleted
		b.DeleteAt = time.Now()
		if err := db.FullSaveAssociationsSave(ctx, []*x1model.BLBToDelete{b}); err != nil {
			resource.LoggerTask.Warning(ctx, "save blb to delete fail",
				logit.String("appId", app.AppId),
				logit.String("blbId", b.BlbId),
				logit.Error("error", err))
			return cerrs.ErrDbQueryFail.Wrap(err)
		}
	}
	return nil
}
