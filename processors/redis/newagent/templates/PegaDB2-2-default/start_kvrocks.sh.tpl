#!/bin/bash
PEGA_DATA="{{.pegadb_data_path}}/kvrocks_{{.port}}"

[[ -d ${PEGA_DATA}/data ]] || mkdir -p ${PEGA_DATA}/data
[[ -d ${PEGA_DATA}/log ]] || mkdir -p ${PEGA_DATA}/log
[[ -d ${PEGA_DATA}/var/run ]] || mkdir -p ${PEGA_DATA}/var/run

if [ x"{{.resource_type}}" == x"container" ];then
   echo "start kvrocks by cmd: {{.kvrocks_bin_path}} {{.kvrocks_conf_path}}"
   {{.kvrocks_bin_path}} {{.kvrocks_conf_path}}
else
   echo "start kvrocks by cmd: {{.kvrocks_bin_path}} {{.kvrocks_conf_path}}"
   {{.kvrocks_bin_path}} {{.kvrocks_conf_path}}
   worker_conf_cnt=$(grep -E '^workers' {{.kvrocks_conf_path}} | awk '{print $2}')
   for i in `seq 1 30`; do
     sleep 1
     pid=$(ps -ef | grep {{.kvrocks_bin_path}} | grep -v grep | awk '{print $2}')
     worker_real_cnt=$(ps -T -p $pid | grep worker | wc -l)
     if [ $worker_real_cnt -eq $worker_conf_cnt ]; then
       echo "kvrocks started successfully with $worker_real_cnt workers"
       ps -T -p $pid | grep worker | awk '{print $2}' | while read tid; do renice -20 -p $tid; done
       exit 0
     else
       echo "Waiting for kvrocks to start, current worker count: $worker_real_cnt, expected: $worker_conf_cnt"
     fi
   done
   echo "kvrocks failed to start with $worker_real_cnt workers, expected: $worker_conf_cnt"
   exit 1
fi