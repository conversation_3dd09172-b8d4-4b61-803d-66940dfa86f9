bind 0.0.0.0
port {{.port}}
timeout 0
workers {{.workers}}
repl-workers 1
daemonize yes
maxclients 60000

db-name r_cluster_id.r_shard_id.db
{{.databases}}

dir {{.pegadb_data_path}}/kvrocks_{{.port}}/data
log-dir {{.pegadb_data_path}}/kvrocks_{{.port}}/log
pidfile {{.pegadb_data_path}}/kvrocks_{{.port}}/var/run/kvrocks.pid

slave-read-only yes
slave-priority 100
tcp-backlog 511
master-use-repl-port no
use-rsid-psync {{.use_rsid_psync}}

slave-serve-stale-data yes
max-replication-mb {{.max_replication_mb}}
max-io-mb 500
max-expire-delete-io-mb 5
max-db-size {{.max_db_size}}
max-backup-to-keep 1
max-backup-keep-hours 168

cluster-enabled yes
use-op-header {{.use_op_header}}
crdt-enabled no
crdt-conflict-us 0
crdt-deleted-survive-sec 60

slowlog-log-slower-than {{.pegadb_slowlog_log_slower_than}}
slowlog-max-len 128
supervised no

profiling-sample-ratio 0
profiling-sample-record-max-len 256
profiling-sample-record-threshold-ms 100

compaction-checker-range {{.compaction_checker_range}}

migrate-speed 4096
migrate-pipeline-size 16
migrate-sequence-gap 10000
migrate-response-timeout 3
migrate-batch-rate-limit-mb 4
semi-sync-enabled no
semi-sync-wait-for-replica-count 1
semi-sync-timeout 1000

rocksdb.metadata_block_cache_size {{.metadata_block_cache_size}}
rocksdb.subkey_block_cache_size {{.subkey_block_cache_size}}
rocksdb.share_metadata_and_subkey_block_cache yes
rocksdb.max_open_files 8096


rocksdb.write_buffer_size {{.write_buffer_size}}
rocksdb.target_file_size_base 64
rocksdb.max_write_buffer_number {{.max_write_buffer_number}}
rocksdb.min_write_buffer_number_to_merge 1
rocksdb.max_background_compactions 4
rocksdb.max_background_flushes 4
rocksdb.max_sub_compactions 2
rocksdb.wal_ttl_seconds 7200
rocksdb.wal_size_limit_mb {{.wal_size_limit_mb}}
rocksdb.wal_check_interval_seconds 30
rocksdb.max_total_wal_size 512
rocksdb.max_manifest_file_size 64
rocksdb.max_log_file_size 256
rocksdb.keep_log_file_num 12
rocksdb.block_size 4096
rocksdb.cache_index_and_filter_blocks yes
rocksdb.compression lz4
rocksdb.compaction_readahead_size 2097152

rocksdb.delayed_write_rate 8388608
rocksdb.enable_pipelined_write yes
rocksdb.level0_slowdown_writes_trigger 20
rocksdb.stats_dump_period_sec 0
rocksdb.disable_auto_compactions no
rocksdb.enable_blob_files no
rocksdb.min_blob_size 4096
rocksdb.blob_file_size 134217728
rocksdb.enable_blob_garbage_collection yes
rocksdb.blob_garbage_collection_age_cutoff 25
rocksdb.blob_compaction_readahead_size 262144
rocksdb.max_bytes_for_level_base 268435456
rocksdb.max_compaction_bytes 536870912
rocksdb.bytes_per_sync 1048576
rocksdb.wal_bytes_per_sync 1048576
rocksdb.strict_bytes_per_sync no
rocksdb.rate_limit_auto_tuned yes
rocksdb.use_prefix_extractor no
slave-empty-db-before-fullsync yes

redis-id {{.instance_id}}
shard-id {{.shard_id}}
cluster-id {{.cluster_id}}
metaserverhost {{.meta_master_ip}}
metaserverport {{.meta_master_port}}

{{.user_conf_records}}

{{.rename_commands}}