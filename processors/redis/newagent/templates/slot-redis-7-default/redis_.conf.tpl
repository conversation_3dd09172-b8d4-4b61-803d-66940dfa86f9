protected-mode no
port {{.port}}
tcp-backlog 511
timeout 0
tcp-keepalive 300
daemonize yes
supervised no
pidfile /var/run/redis_{{.port}}.pid
loglevel notice
logfile {{.log_file}}
databases 256
always-show-logo yes
{{.save_mode}}
stop-writes-on-bgsave-error no
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
rdb-del-sync-files no
dir {{.dir}}
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-diskless-load disabled
repl-ping-slave-period 3
repl-timeout {{.repl_timeout}}
repl-disable-tcp-nodelay no
repl-backlog-size {{.repl_backlog_size}}
replica-priority 100
acllog-max-len 128
aclfile {{.acl_file}}
maxclients 60000
maxmemory {{.maxmemory}}
maxmemory-policy {{.max_memory_policy}}
lazyfree-lazy-eviction no
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes
lazyfree-lazy-user-del yes
io-threads {{.io_threads}}
io-threads-do-reads {{.io_threads_do_reads}}
appendonly {{.appendonly}}
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite yes
auto-aof-rewrite-percentage {{.auto_aof_rewrite_percentage}}
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble no
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 10
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica {{.client_output_buffer_for_replica}}
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
activedefrag no
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 30
active-defrag-cycle-min 1
active-defrag-cycle-max 25
jemalloc-bg-thread {{.jemalloc_bg_thread}}
activedefrag {{.activedefrag}}
active-defrag-ignore-bytes {{.active_defrag_ignore_bytes}}
active-defrag-threshold-lower {{.active_defrag_threshold_lower}}
active-defrag-threshold-upper {{.active_defrag_threshold_upper}}
active-defrag-cycle-min {{.active_defrag_cycle_min}}
active-defrag-cycle-max {{.active_defrag_cycle_max}}
aof-shift-size 500MB
aof-shift-retain-size {{.aof_shift_retain_size}}
aof-async yes
aof-shift {{.aof_shift}}
aof-repl no
aof-repl-thread-max 0
aof-repl-send-interval 0
aof-repl-send-buffer-size 16384
oom-score-adj yes
oom-score-adj-values 0 200 800
use-op-header {{.use_op_header}}
shard_id {{.shard_id}}
g_shard_id {{.g_shard_id}}
{{.requirepass}}
{{.masterauth}}
{{.server_cpulist}}
{{.bio_cpulist}}
{{.aof_rewrite_cpulist}}
{{.bgsave_cpulist}}
{{.user_conf_records}}
{{.load_modules}}
{{.rename_commands}}