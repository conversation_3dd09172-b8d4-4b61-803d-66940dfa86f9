self_cluster={{.cluster_show_id}}
src_redis_ip={{.host}}
src_redis_port={{.port}}
storage_bns={{.ma_meta_ip}}:{{.ma_meta_port}}
storage_pass={{.ma_meta_auth}}
redis_inst_id={{.redis_id}}
data_dir={{.data_dir_prefix}}/{{.server_name}}_{{.port}}{{.data_dir_suffix}}
log_file={{.log_dir}}/sync.log
log_level=INFO
filter_keys=DCS_CACHE_,CACHE_,BDRP_STATICS_ONLINE_QA_TEST
filter_commands=flush,slot,migrate,select,{{.rename_str}}{{.user_filter_cmds}}
retain_day=3
max_delay_time=100
max_pipe_num=200
operate_timeout=2
operate_timeout=2
second_exec_enable={{.support_second_exec}}
second_exec_delay_ms=5000
second_exec_cmds=del
log_dir={{.log_dir}}
log_split_type=date
dst_proxy_pass={{.dst_proxy_auth}}
server_type={{.server_type}}