#!/usr/bin/env python
# -*- coding: utf-8 -*-
################################################################################
#
# Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
# 对比两个数据库表结构不同并生成差异化的sql语句

作者: cuiyi01(<EMAIL>)
日期: May 23, 2019 at 2:04:52 PM
"""
import json
import os
import requests
import sys
import time


def load_conf_tpls():
    """load conf tpls
    """
    ret = dict()
    # 遍历当前目录下所有目录
    for path in os.listdir(os.getcwd()):
        if not os.path.isdir(path):
            continue

        ret[path] = {
            "tpl_id": path,
            "pkg_name": "-".join(path.split("-")[:-2]),
            "major_version": path.split("-")[-2],
            "items": [],
        }
        # 遍历目录下所有文件
        for file_name in os.listdir(path):
            if not os.path.isfile(os.path.join(path, file_name)):
                continue
            # 读取文件内容
            with open(os.path.join(path, file_name), "r") as f:
                ret[path]["items"].append({
                    "deploy_path": ".".join(file_name.split(".")[:-1]),
                    "content": f.read(),
                })
    return ret


def load_x1_api_endpoints():
    """load x1 endpoints
    """
    with open("x1-api-endpoints.json", "r") as f:
        return json.loads(f.read())


def update_one_region_conf_tpls(conf_tpls, x1_api_endpoints):
    """update one region conf tpls
    """
    for x1_api_ep in x1_api_endpoints:
        url = "http://%s:%s/v1/scs/addOrUpdateConfTpl" % (x1_api_ep["ip"], x1_api_ep["port"])
        for conf_tpl in conf_tpls:
            data = {
                "pkgName": conf_tpl["pkg_name"],
                "majorVersion": conf_tpl["major_version"],
                "tplId": conf_tpl["tpl_id"],
                "desc": "conf tpl for %s of version %s" % (conf_tpl["pkg_name"], conf_tpl["major_version"]),
                "confTplItems": [{"deployPath": item["deploy_path"], "content": item["content"]}
                                 for item in conf_tpl["items"]],
            }
            headers = {
                "Content-Type": "application/json",
                "scs-mis-token": "2a5d355806e24876ab025d14529d26e5",
                "scs-mis-userid": "a11602e1c4c24e59865b9bb9209ff16d",
                "scs-mis-user": "cuiyi01",
                "x-bce-request-id": "update_conf_tpls-%s-%d" % (conf_tpl["tpl_id"], time.time()),
            }
            r = requests.post(url, data=json.dumps(data), headers=headers)
            print("status_code: %d, content: %s" % (r.status_code, r.content))


def update_conf_tpls():
    """update conf tpls
    """
    conf_tpls = load_conf_tpls()
    x1_api_endpoints = load_x1_api_endpoints()
    update_one_region_conf_tpls(conf_tpls.values(), [x for x in x1_api_endpoints if x["region"] in sys.argv[1:]])


if __name__ == "__main__":
    update_conf_tpls()
