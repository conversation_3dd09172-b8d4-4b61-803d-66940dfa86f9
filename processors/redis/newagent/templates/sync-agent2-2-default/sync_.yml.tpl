cluster-name: {{.cluster_show_id}}
unix-socket: /root/agent/bin/sync_agent.sock
storage-addr: {{.ma_meta_ip}}:{{.ma_meta_port}}
storage-password: {{.ma_meta_auth}}
pull-info-interval: 1s
push-info-interval: 1s
update-info-interval: 3s

log:
    level: info
    format: json
    filename: {{.log_dir}}/syncagent.log
    max-size: 256
    max-days: 3
    max-backups: 10

reader:
    source-addr: {{.host}}:{{.port}}
    source-password:
    data-dir: {{.data_dir_prefix}}/{{.server_name}}_{{.port}}{{.data_dir_suffix}}
    type : {{.server_type}}

filter:
    key-prefixes: [{{.key_prefixes}}]
    keys:
    commands: [flush,slot,migrate,select,{{.rename_str}}{{.user_filter_cmds}}]

writer:
    pipeline: {{.pipeline}}
    timeout: {{.timeout}}s
    channel-num: {{.channel_num}}
    channel-size: {{.channel_size}}
    connections-num-in-channel: 2{{.discard_rules}}