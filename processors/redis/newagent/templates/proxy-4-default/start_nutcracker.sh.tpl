echo 'start nutcracker with cmd: {{.supervise_path}} -p {{.supervise_status_dir}} -f "{{.nutcracker_path}} -c {{.nutcracker_conf}} -o {{.nutcracker_log}}"'
mkdir -p "{{.nutcracker_log_dir}}"
mkdir -p "{{.agent_log_dir}}"
ln -s "{{.nutcracker_log}}" "{{.agent_log}}"
if [ x"{{.resource_type}}" == x"container" ];then
   {{.supervise_path}} -p {{.supervise_status_dir}} -f "{{.nutcracker_path}} -c {{.nutcracker_conf}} -o {{.nutcracker_log}}"
else
   nice -n -20 {{.supervise_path}} -p {{.supervise_status_dir}} -f "{{.nutcracker_path}} -c {{.nutcracker_conf}} -o {{.nutcracker_log}}"
fi