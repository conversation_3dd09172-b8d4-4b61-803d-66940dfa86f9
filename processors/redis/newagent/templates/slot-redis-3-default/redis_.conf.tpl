protected-mode no
port {{.port}}
tcp-backlog 511
timeout 0
tcp-keepalive 300
daemonize yes
supervised no
pidfile /var/run/redis_{{.port}}.pid
loglevel notice
logfile {{.log_file}}
databases 16
{{.save_mode}}
stop-writes-on-bgsave-error no
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir {{.dir}}
slave-serve-stale-data yes
slave-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-timeout {{.repl_timeout}}
repl-disable-tcp-nodelay no
repl-backlog-size {{.repl_backlog_size}}
repl-backlog-ttl 3600
slave-priority 100
maxclients 60000
maxmemory {{.maxmemory}}
maxmemory-policy {{.max_memory_policy}}
appendonly {{.appendonly}}
appendfilename appendonly.aof
appendfsync everysec
no-appendfsync-on-rewrite yes
auto-aof-rewrite-percentage {{.auto_aof_rewrite_percentage}}
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 0
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit slave {{.client_output_buffer_for_replica}}
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
use-op-header {{.use_op_header}}
aof-shift-size {{.aof_shift_size}}

aof-rewrite-incremental-fsync yes

add-meta-serv no
rfsync-with-mem yes
use-redis-ping yes
querybuf-limit-to-maxmemory no
repl_ack_timer_timeout 1000
add-slot-info yes
rdb-save-sleep-us 0

metaserverof {{.meta_master_ip}} {{.meta_master_port}}
shard_id {{.shard_id}}
g_shard_id {{.g_shard_id}}
cluster_id {{.cluster_id}}
instance_id {{.instance_id}}

use-hash-tag {{.use_hash_tag}}
db-slot-check {{.db_slot_check}}

{{.user_conf_records}}
{{.rename_commands}}
