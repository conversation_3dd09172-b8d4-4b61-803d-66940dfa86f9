daemonize yes
pidfile /var/run/redis_{{.port}}.pid
port {{.port}}
timeout 3600
tcp-keepalive 0
loglevel notice
logfile {{.log_file}}
databases 256
{{.save_mode}}
stop-writes-on-bgsave-error no
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir {{.dir}}
slave-serve-stale-data yes
slave-read-only yes
repl-timeout {{.repl_timeout}}
repl-disable-tcp-nodelay no
repl-backlog-size {{.repl_backlog_size}}
slave-priority 100
maxclients 60000
maxmemory {{.maxmemory}}
maxmemory-policy {{.max_memory_policy}}
appendonly {{.appendonly}}
appendfilename appendonly.aof
appendfsync everysec
no-appendfsync-on-rewrite yes
auto-aof-rewrite-percentage {{.auto_aof_rewrite_percentage}}
auto-aof-rewrite-min-size 64mb
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-entries 512
list-max-ziplist-value 64
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit slave {{.client_output_buffer_for_replica}}
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
aof-rewrite-incremental-fsync yes
protected-mode no
{{.requirepass}}
{{.masterauth}}
{{.user_conf_records}}
{{.rename_commands}}