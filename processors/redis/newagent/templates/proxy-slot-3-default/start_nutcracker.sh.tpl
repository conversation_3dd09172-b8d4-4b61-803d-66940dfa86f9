echo 'start nutcracker with cmd: {{.supervise_path}} -p {{.supervise_status_dir}} -f "{{.nutcracker_path}} -c {{.nutcracker_conf}} -o {{.nutcracker_log}} -S {{.meta_port}} -A {{.meta_ip}} -w {{.whitelist_prefix}} -u {{.user_conf_path}} -m {{.proxy_mbuf_size}} -P {{.module_conf}} -s {{.stat_port}}"'
mkdir -p "{{.nutcracker_log_dir}}"
mkdir -p "{{.agent_log_dir}}"
ln -s "{{.nutcracker_log}}" "{{.agent_log}}"
if [ x"{{.resource_type}}" == x"container" ];then
    {{.supervise_path}} -p {{.supervise_status_dir}} -f "{{.nutcracker_path}} -c {{.nutcracker_conf}} -o {{.nutcracker_log}} -S {{.meta_port}} -A {{.meta_ip}} -w {{.whitelist_prefix}} -u {{.user_conf_path}} -m {{.proxy_mbuf_size}} -P {{.module_conf}} -s {{.stat_port}}"
else
   nice -n -20 {{.supervise_path}} -p {{.supervise_status_dir}} -f "{{.nutcracker_path}} -c {{.nutcracker_conf}} -o {{.nutcracker_log}} -S {{.meta_port}} -A {{.meta_ip}} -w {{.whitelist_prefix}} -u {{.user_conf_path}} -m {{.proxy_mbuf_size}} -P {{.module_conf}} -s {{.stat_port}}"
fi