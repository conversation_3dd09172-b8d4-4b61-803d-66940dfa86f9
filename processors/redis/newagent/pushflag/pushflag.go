package pushflag

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

type XagentRequest struct {
	TargetPushFlag bool `json:"target_push_flag"`
}

func ProcessUpdatePushFlagAllTrue(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processUpdatePushFlag(ctx, teu,
		map[string]bool{"*": true, x1model.NodeOrProxyStatusToCreate: true}, false)
}

func ProcessUpdatePushFlagForReplaceNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processUpdatePushFlag(ctx, teu, map[string]bool{"*": true, x1model.NodeOrProxyStatusToCreate: true,
		x1model.NodeOrProxyStatusToDelete: false, x1model.NodeOrProxyStatusToFakeDelete: false}, true)
}

func ProcessRollbackUpdatePushFlagForReplaceNodes(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 回滚不考虑ToCreate节点Flag下发，依赖BCC释放
	return processUpdatePushFlag(ctx, teu, map[string]bool{"*": true,
		x1model.NodeOrProxyStatusToDelete: true, x1model.NodeOrProxyStatusToFakeDelete: true}, false)
}

func processUpdatePushFlag(ctx context.Context, teu *workflow.TaskExecUnit,
	statusPushFlagMap map[string]bool, allowFail bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app error", logit.Error("err", err))
		return err
	}
	if app.UseNewAgent != "yes" {
		resource.LoggerTask.Notice(ctx, "skip push flag for non-newagent", logit.String("appId", app.AppId))
		return nil
	}
	// ToDelete节点虽然也会释放BCC,下发false失败也可以忽略
	// 但是回滚时，ToDelete节点下发true必须成功
	reqList := make([]*xagent.AsyncRequest, 0)
	toFakeDeleteReqList := make([]*xagent.AsyncRequest, 0)

	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if _, found := statusPushFlagMap[node.Status]; !found {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: node.FloatingIP,
					Port: int32(node.XagentPort),
				},
				Action: "update_push_flag",
				Params: &XagentRequest{
					TargetPushFlag: getPushFlag(node.Status, statusPushFlagMap),
				},
			}
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toFakeDeleteReqList = append(toFakeDeleteReqList, req)
			} else {
				reqList = append(reqList, req)
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if _, found := statusPushFlagMap[proxy.Status]; !found {
				continue
			}
			req := &xagent.AsyncRequest{
				Addr: &xagent.Addr{
					Host: proxy.FloatingIP,
					Port: int32(proxy.XagentPort),
				},
				Action: "update_push_flag",
				Params: &XagentRequest{
					TargetPushFlag: getPushFlag(proxy.Status, statusPushFlagMap),
				},
			}
			if proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				toFakeDeleteReqList = append(toFakeDeleteReqList, req)
			} else {
				reqList = append(reqList, req)
			}
		}
	}
	g := gtask.Group{Concurrent: 25}
	for _, req := range reqList {
		req := req
		g.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update push flag fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = g.Wait()
	if err != nil {
		return err
	}

	// 宕机场景下允许设置失败
	gg := gtask.Group{Concurrent: 25, AllowSomeFail: allowFail}
	for _, req := range toFakeDeleteReqList {
		req := req
		gg.Go(func() error {
			_, err := xagent.Instance().DoAsync(ctx, req).Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "update push flag fail",
					logit.String("addr", fmt.Sprintf("%s:%d", req.Addr.Host, req.Addr.Port)),
					logit.Error("err", err))
			}
			return err
		})
	}
	_, err = gg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "to fake delete node update push flag fail, ignore...",
			logit.Error("err", err))
		return nil
	}
	return nil
}

func getPushFlag(status string, statusPushFlagMap map[string]bool) bool {
	if _, found := statusPushFlagMap[status]; found {
		return statusPushFlagMap[status]
	}
	if statusPushFlagMap["*"] {
		return true
	}
	return false
}
