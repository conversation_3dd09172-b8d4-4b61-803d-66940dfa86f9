package migrate

import (
	"testing"
)

func Test_parseRedisConfig(t *testing.T) {
	rawConf := `protected-mode no
port 6671
tcp-backlog 511
timeout 0
tcp-keepalive 300
daemonize yes
supervised no
pidfile /var/run/redis_6671.pid
loglevel notice
logfile /root/agent/log/redis_6671.log
databases 256
always-show-logo yes
save ""
stop-writes-on-bgsave-error no
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
rdb-del-sync-files no
dir /mnt/data/redis_6671
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-diskless-load disabled
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 64mb
replica-priority 100
acllog-max-len 128
aclfile /root/agent/conf/user.acl
maxclients 60000
maxmemory 1073741824
maxmemory-policy volatile-ttl
lazyfree-lazy-eviction no
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes
lazyfree-lazy-user-del yes
io-threads 1
io-threads-do-reads no
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite yes
auto-aof-rewrite-percentage 200
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble no
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 10
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 128mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
activedefrag no
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 30
active-defrag-cycle-min 1
active-defrag-cycle-max 25
jemalloc-bg-thread no
oom-score-adj yes
oom-score-adj-values 0 200 800
bio_cpulist 2-6
aof_rewrite_cpulist 2-6
bgsave_cpulist 2-6
loadmodule /root/agent/bin/modules/libcascad.so
rename-command LASTSAVE aae420ac56ef116058218c11d8b35b30LASTSAVE
rename-command BGSAVE aae420ac56ef116058218c11d8b35b30BGSAVE
rename-command BGREWRITEAOF aae420ac56ef116058218c11d8b35b30BGREWRITEAOF
rename-command FLUSHALL aae420ac56ef116058218c11d8b35b30FLUSHALL
rename-command SHUTDOWN aae420ac56ef116058218c11d8b35b30SHUTDOWN
rename-command MIGRATE aae420ac56ef116058218c11d8b35b30MIGRATE
rename-command DEBUG aae420ac56ef116058218c11d8b35b30DEBUG
rename-command SAVE aae420ac56ef116058218c11d8b35b30SAVE
rename-command SYNC aae420ac56ef116058218c11d8b35b30SYNC
rename-command FLUSHDB aae420ac56ef116058218c11d8b35b30FLUSHDB`
	config := parseRedisConfig(rawConf)
	t.Log(config)
}

func Test_parseProxyConfig(t *testing.T) {
	rawConf := `46433:
  listen: 0.0.0.0:6379
  hash: crc16
  hash_tag: "{}"
  distribution: slot
  preconnect: true
  auto_eject_hosts: false
  redis: true
  timeout: 2000
  backlog: 512
  client_connections: 60000
  server_connections: 36
  server_connections_load_balance: true
  server_retry_timeout: 2000
  server_failure_limit: 2
  pipeline_limit_threshold: 100
  client_idle_timeout: 4000
  client_keepalive: 300
  flow_control: false
  print_access_log_control: false
  mtl_as_wl: false
  is_scale_dump: false
  max_dump_len: 128
  mtl_match_prefix_control: false
  quota: 100000
  qpsquota: 200000
  rqpsquota: 200000
  wqpsquota: 200000
  temp_quota_threshold: 10
  slowlog_log_slower_than: 300
  print_interval_every_cmds: 0
  stale_slave_readable: true
  slave_force_readable: false
  all_region_readable: false
  hotkey_observe_enable: false
  hotkey_local_cache_enable: false
  hotkey_ttl_ms: 1000
  hotkey_max_num: 64
  hotkey_lfu_capacity: 10240
  hotkey_lfu_memuse_limit_mb: 64
  hotkey_collect_interval_ms: 3000
  eject_slow_server: true
  mcpack_port: 6380
  metaserver_timeout: 120000
  metaserver_ping_interval: 1000
  log_discard_level: 4
  log_level: 4
  log_max_total_size: 1000
  log_rotate: "*:-1:-1"
  log_max_memory: 256
  client_auth: test_zlf
  mcpack_msg_len: 67108864
  meta_use_dns: 1
  proxy_id: 46433
  proxy_ins_id: 278692
  rename_prefix: aae420ac56ef116058218c11d8b35b30
  support_multi_db: true
  db_count: 256
  ignore_select_when_multi_db_disabled: true
  need_reserved_conn: false
  max_reserved_connections: 0
  support_scan: true
  servers:
   - bj_uoxwlfhajvxv_0
     10.178.11.138:8080:1
     10.178.11.138:8080:1
     10.179.1.226:8080:1`
	config := parseProxyConfig(rawConf)
	t.Log(config)
}
