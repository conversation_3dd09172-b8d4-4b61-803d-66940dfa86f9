package migrate

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"strings"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/guardian"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/getvars"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/render"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	RenameConfigGetCmd     = "aae420ac56ef116058218c11d8b35b30config"
	ProxyConfigCMD         = "proxyconfig"
	XAgentMinVersion       = "1.0.75.1"
	AgentMinVersion        = "4.5.152.1"
	CsAgentMinVersion      = "3.6.33.1"
	MonitorAgentMinVersion = "1.0.58.1"
)

type DiffRuntimeConfAndNewConfParams struct {
	BatchID      string
	App          *x1model.Application
	ClusterModel *csmaster.CsmasterCluster
	Entity       string
	Type         string
}

func ProcessPreCheckForMigration(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if err := checkFlags(ctx, app); err != nil {
		return err
	}
	if err := checkStatus(ctx, clusterModel); err != nil {
		return err
	}
	// if err := checkInspection(ctx, app); err != nil {
	//	return err
	// }
	if err := checkAgentVersion(ctx, app); err != nil {
		return err
	}
	if app.Type == x1model.AppTypeCluster && clusterModel.ClientAuth != "" {
		if err := checkProxyAcls(ctx, clusterModel); err != nil {
			return err
		}
	}
	// if err := checkAppConf(ctx, app, clusterModel, req.BatchID); err != nil {
	//	return err
	// }
	if err := checkAgents(ctx, app, clusterModel); err != nil {
		return err
	}
	return nil
}

func checkAgents(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster) error {
	var params []*deploy.DeployNodeOfAllTypeParams
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			deployParams, err := getDeployParams(ctx, &DiffRuntimeConfAndNewConfParams{
				App:          app,
				ClusterModel: clusterModel,
				Entity:       node.NodeId,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get deploy params failed", logit.Error("error", err))
				return err
			}
			params = append(params, deployParams)
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			deployParams, err := getDeployParams(ctx, &DiffRuntimeConfAndNewConfParams{
				App:          app,
				ClusterModel: clusterModel,
				Entity:       proxy.ProxyId,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get deploy params failed", logit.Error("error", err))
				return err
			}
			params = append(params, deployParams)
		}
	}
	g := gtask.Group{Concurrent: 50}
	for _, deployParams := range params {
		deployParams := deployParams
		g.Go(func() error {
			return sendCheckAgents(ctx, deployParams)
		})
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check agents failed", logit.Error("error", err))
		return err
	}
	return nil
}

func sendCheckAgents(ctx context.Context, deployParams *deploy.DeployNodeOfAllTypeParams) error {
	request := &MigrateToNewAgentXagentRequest{
		Meta: &xagent.Meta{
			Engine:        deployParams.EngineType,
			EngineVersion: deployParams.KernelMajorVersion,
			Basedir:       deployParams.BaseDir,
			Port:          int32(deployParams.NodePort),
		},
	}
	_, err := xagent.Instance().DoAsync(ctx, &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: deployParams.XagentHost,
			Port: int32(deployParams.XagentPort),
		},
		Action:     "migrate_to_newagent_precheck",
		Params:     request,
		TimeoutSec: MigrateToNewAgentTimeoutSec,
	}).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to migrate to new agent", logit.Error("err", err))
		return err
	}
	return nil
}

func checkProxyAcls(ctx context.Context, clusterModel *csmaster.CsmasterCluster) error {
	var clusterAcls []*csmaster_model_interface.ClusterAclUser
	if err := resource.CsmasterModel.GetAllByCond(ctx, &clusterAcls, "cluster_id = ? AND update_status != ?",
		clusterModel.Id, csmaster_model_interface.ACLUSER_DELETED); err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster acl failed", logit.Error("err", err))
		return err
	}
	for _, clusterAcl := range clusterAcls {
		if clusterAcl.UpdateStatus != csmaster_model_interface.ACLUSER_UPDATE_SUCCESS {
			resource.LoggerTask.Warning(ctx, "cluster acl not update success", logit.String("clusterAcl", base_utils.Format(clusterAcl)))
			return errors.New("cluster acl not update success")
		}
	}
	return nil
}

func checkFlags(ctx context.Context, app *x1model.Application) error {
	clusterDbModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get cluster model", logit.Error("err", err))
		return err
	}
	if clusterDbModel.UseXmaster == csmaster.CsmasterSetHAServicerUseCsmaster {
		resource.LoggerTask.Warning(ctx, "ha servicer is csmaster", logit.String("appId", app.AppId))
		return errors.New("ha servicer is csmaster")
	}
	if clusterDbModel.UseNewAgent == "yes" {
		resource.LoggerTask.Warning(ctx, "use new agent is true", logit.String("appId", app.AppId))
		return errors.New("use new agent is true")
	}
	if app.UseNewPackage != 1 {
		resource.LoggerTask.Warning(ctx, "use new package is false", logit.String("appId", app.AppId))
		return errors.New("use new package is false")
	}
	if app.UseNewAgent == "yes" {
		resource.LoggerTask.Warning(ctx, "use new agent is true", logit.String("appId", app.AppId))
		return errors.New("use new agent is true")
	}
	return nil
}

func checkStatus(ctx context.Context, clusterModel *csmaster.CsmasterCluster) error {
	if clusterModel.Status != csmaster_model_interface.CACHE_CLUSTER_RUNNING {
		resource.LoggerTask.Warning(ctx, "cluster status not running", logit.Int("status", int(clusterModel.Status)))
		return fmt.Errorf("cluster status not running:%d", clusterModel.Status)
	}
	return nil
}

func checkInspection(ctx context.Context, app *x1model.Application) error {
	resp, err := guardian.NewDefaultGuardianSDK().TopoCheck(ctx, &guardian.TopoCheckRequest{AppID: app.AppId})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to execute inspection", logit.Error("err", err))
		return err
	}
	if resp.Topo != nil {
		resource.LoggerTask.Warning(ctx, "inspection fail", logit.String("topo", base_utils.Format(resp.Topo)))
		return fmt.Errorf("inspection fail:%s", base_utils.Format(resp.Topo))
	}
	return nil
}

func checkAgentVersion(ctx context.Context, app *x1model.Application) error {
	pkgs, err := x1model.GetCurrentPackageRecord(ctx, app.AppId, []string{"xagent", "monitor-agent"}, x1model.PkgRecordStatusInuse)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get current package record failed", logit.Error("error", err))
		return err
	}
	for _, pkg := range pkgs {
		if pkg.Name == "xagent" && !strings.Contains(pkg.FullVersion, "mis") {
			if base_utils.CompareVersion(pkg.FullVersion, XAgentMinVersion) < 0 {
				resource.LoggerTask.Warning(ctx, "xagent version not match", logit.String("version", pkg.FullVersion))
				return fmt.Errorf("xagent version not match:%s", pkg.FullVersion)
			}
		}
		if pkg.Name == "monitor-agent" && !strings.Contains(pkg.FullVersion, "mis") {
			if base_utils.CompareVersion(pkg.FullVersion, MonitorAgentMinVersion) < 0 {
				resource.LoggerTask.Warning(ctx, "monitor-agent version not match", logit.String("version", pkg.FullVersion))
				return fmt.Errorf("monitor-agent version not match:%s", pkg.FullVersion)
			}
		}
	}
	return nil
}

func checkAppConf(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, batchID string) error {
	subg := gtask.Group{
		Concurrent:    10,
		AllowSomeFail: true,
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			subg.Go(func() error {
				params := &DiffRuntimeConfAndNewConfParams{
					BatchID:      batchID,
					App:          app,
					ClusterModel: clusterModel,
					Entity:       node.NodeId,
					Type:         "check",
				}
				return DiffRuntimeConfAndNewConf(ctx, params)
			})
		}
	}
	for _, itf := range app.Interfaces {
		itf := itf
		for _, proxy := range itf.Proxys {
			proxy := proxy
			subg.Go(func() error {
				params := &DiffRuntimeConfAndNewConfParams{
					BatchID:      batchID,
					App:          app,
					ClusterModel: clusterModel,
					Entity:       proxy.ProxyId,
					Type:         "check",
				}
				return DiffRuntimeConfAndNewConf(ctx, params)
			})
		}
	}
	_, err := subg.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "subg wait failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCheckAppsConf(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var preCheckReq PreCheckRequest
	if err := json.Unmarshal([]byte(teu.Parameters), &preCheckReq); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal parameters failed", logit.Error("error", err))
		return err
	}
	g := gtask.Group{
		Concurrent:    10,
		AllowSomeFail: true,
	}
	for _, appID := range preCheckReq.AppIDs {
		appID := appID
		g.Go(func() error {
			app, err := x1model.ApplicationGetByAppId(ctx, appID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("error", err))
				return err
			}
			clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, appID)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
				return err
			}
			subg := gtask.Group{
				Concurrent:    10,
				AllowSomeFail: true,
			}
			for _, cluster := range app.Clusters {
				cluster := cluster
				for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
					node := node
					subg.Go(func() error {
						params := &DiffRuntimeConfAndNewConfParams{
							BatchID:      preCheckReq.BatchID,
							App:          app,
							ClusterModel: clusterModel,
							Entity:       node.NodeId,
							Type:         "diff",
						}
						return DiffRuntimeConfAndNewConf(ctx, params)
					})
				}
			}
			for _, itf := range app.Interfaces {
				itf := itf
				for _, proxy := range itf.Proxys {
					proxy := proxy
					subg.Go(func() error {
						params := &DiffRuntimeConfAndNewConfParams{
							BatchID:      preCheckReq.BatchID,
							App:          app,
							ClusterModel: clusterModel,
							Entity:       proxy.ProxyId,
							Type:         "diff",
						}
						return DiffRuntimeConfAndNewConf(ctx, params)
					})
				}
			}
			_, err = subg.Wait()
			if err != nil {
				resource.LoggerTask.Warning(ctx, "subg wait failed", logit.Error("error", err))
				return err
			}
			return nil
		})
	}
	_, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "g wait failed", logit.Error("error", err))
		return err
	}
	return nil
}

func DiffRuntimeConfAndNewConf(ctx context.Context, params *DiffRuntimeConfAndNewConfParams) error {
	deployParams, err := getDeployParams(ctx, params)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deploy params failed", logit.Error("error", err))
		return err
	}
	deployParams.TaskID = "warning-no-expect-update"
	deployParams.NoUpdate = true
	toDeployPkgsRet, err := deploy.GetAndUpdateToDeployPkgs(ctx, deployParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get to deploy pkgs failed", logit.Error("error", err))
		return err
	}
	getVarsParams, err := getvars.GetParamters(ctx, params.App.AppId, params.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get vars", logit.Error("err", err))
		return err
	}
	confMap, err := getConfMap(ctx, toDeployPkgsRet, getVarsParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get conf map", logit.Error("err", err))
		return err
	}
	runtimeConfMap, err := getRuntimeConf(ctx, params.App, params.ClusterModel, params.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get runtime conf map", logit.Error("err", err))
		return err
	}
	x1Resource, err := x1model.GetDbAgent(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get db agent", logit.Error("err", err))
		return err
	}
	if params.Type == "diff" {
		var records []*CheckRecord
		for rKey, rVal := range runtimeConfMap {
			if nVal, ok := confMap[rKey]; ok {
				records = append(records, &CheckRecord{
					AppID:       params.App.AppId,
					BatchID:     params.BatchID,
					ConfName:    rKey,
					Entity:      params.Entity,
					BeforeVal:   rVal,
					AfterVal:    nVal,
					ManualCheck: "",
				})
			} else {
				records = append(records, &CheckRecord{
					AppID:       params.App.AppId,
					BatchID:     params.BatchID,
					ConfName:    rKey,
					Entity:      params.Entity,
					BeforeVal:   rVal,
					AfterVal:    "<nil>",
					ManualCheck: "",
				})
			}
		}
		for nKey, nVal := range confMap {
			if _, ok := runtimeConfMap[nKey]; !ok {
				records = append(records, &CheckRecord{
					AppID:       params.App.AppId,
					BatchID:     params.BatchID,
					ConfName:    nKey,
					Entity:      params.Entity,
					BeforeVal:   "<nil>",
					AfterVal:    nVal,
					ManualCheck: "",
				})
			}
		}
		if err := x1Resource.FullSaveAssociationsSave(ctx, records); err != nil {
			resource.LoggerTask.Warning(ctx, "fail to save check records", logit.Error("err", err))
			return err
		}
	} else {
		var records []*CheckRecord
		if err := x1Resource.GetAllByCond(ctx, &records, "app_id = ? AND batch_id = ? AND entity = ?", params.App.AppId, params.BatchID, params.Entity, "yes"); err != nil {
			resource.LoggerTask.Warning(ctx, "fail to get check records", logit.Error("err", err))
			return err
		}
		if len(records) == 0 {
			resource.LoggerTask.Warning(ctx, "check records is empty")
			return errors.New("check records is empty")
		}
		recordMap := make(map[string]*CheckRecord)
		for _, record := range records {
			recordMap[record.ConfName] = record
		}
		for rKey, rVal := range runtimeConfMap {
			nVal, ok := confMap[rKey]
			if !ok || nVal != rVal {
				if recordMap[rKey] == nil || recordMap[rKey].ManualCheck != "ignore" {
					resource.LoggerTask.Warning(ctx, "conf not pass manual check", logit.String("confName", rKey))
					return fmt.Errorf("conf not pass check:%s", rKey)
				}
			}
		}
		for nKey := range confMap {
			if _, ok := runtimeConfMap[nKey]; !ok {
				if recordMap[nKey] == nil || recordMap[nKey].ManualCheck != "ignore" {
					resource.LoggerTask.Warning(ctx, "conf not pass manual check", logit.String("confName", nKey))
					return fmt.Errorf("conf not pass check:%s", nKey)
				}
			}
		}
	}
	return nil
}

func getConfMap(ctx context.Context, toDeployPkgsRet *deploy.ToDeployPkgsResp, getVarsParams *getvars.Parameter) (map[string]string, error) {
	confMap := make(map[string]string)
	for _, pkg := range toDeployPkgsRet.ToDeployPkgInfos {
		if pkg.Name == "redis" || pkg.Name == "slot-redis" || pkg.Name == "PegaDB2" {
			renderedConfs, err := render.GetRenderdConf(ctx, getVarsParams, pkg.TplID, pkg.FullVersion)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "fail to get rendered conf", logit.Error("err", err))
				return nil, err
			}
			resource.LoggerTask.Trace(ctx, "get redis rendered conf", logit.String("pkgName", pkg.Name),
				logit.String("renderedConfs", base_utils.Format(renderedConfs)))
			for _, renderedConf := range renderedConfs {
				resource.LoggerTask.Trace(ctx, "redis render conf to map check",
					logit.String("confPath", renderedConf.ConfPath))
				if renderedConf.ConfPath == "redis_.conf" || renderedConf.ConfPath == "kvrocks_.conf" {
					confMap = parseRedisConfig(renderedConf.ConfContent)
					break
				}
			}
			break
		}
		if pkg.Name == "proxy-slot" || pkg.Name == "nutcraker" || pkg.Name == "proxy" {
			renderedConfs, err := render.GetRenderdConf(ctx, getVarsParams, pkg.TplID, pkg.FullVersion)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "fail to get rendered conf", logit.Error("err", err))
				return nil, err
			}
			resource.LoggerTask.Trace(ctx, "get proxy rendered conf", logit.String("pkgName", pkg.Name),
				logit.String("renderedConfs", base_utils.Format(renderedConfs)))
			for _, renderedConf := range renderedConfs {
				if renderedConf.ConfPath == "nutcracker_.yml" {
					confMap = parseProxyConfig(renderedConf.ConfContent)
					break
				}
			}
			break
		}
	}
	if len(confMap) == 0 {
		resource.LoggerTask.Warning(ctx, "fail to get rendered conf")
		return nil, errors.New("fail to get rendered conf")
	}
	return confMap, nil
}

func parseRedisConfig(rawConfig string) map[string]string {
	config := make(map[string]string)
	lines := strings.Split(rawConfig, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue // 跳过空行和注释行
		}
		parts := strings.SplitN(line, " ", 2)
		if len(parts) == 2 && parts[0] != "loadmodule" && parts[0] != "rename-command" {
			config[parts[0]] = strings.TrimSpace(parts[1])
		}
	}
	return config
}

func parseProxyConfig(rawConfig string) map[string]string {
	config := make(map[string]string)
	lines := strings.Split(rawConfig, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue // 跳过空行和注释行
		}
		parts := strings.SplitN(line, ":", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			val := strings.TrimSpace(parts[1])
			if key == "" {
				continue
			}
			if key == "servers" {
				break
			}
			config[key] = val
		}
	}
	return config
}

func getDeployParams(ctx context.Context, params *DiffRuntimeConfAndNewConfParams) (*deploy.DeployNodeOfAllTypeParams, error) {
	for _, cluster := range params.App.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.NodeId == params.Entity {
				return deploy.GetNodeDeployParams(params.App, cluster, node, "", ""), nil
			}
		}
	}
	for _, itf := range params.App.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.ProxyId == params.Entity {
				return deploy.GetProxyDeployParams(params.App, itf, proxy), nil
			}
		}
	}
	return nil, fmt.Errorf("entity %s not found", params.Entity)
}

func GetRuntimeConf(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, entity string) (map[string]string, error) {
	return getRuntimeConf(ctx, app, clusterModel, entity)
}

func getRuntimeConf(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, entity string) (map[string]string, error) {
	redisCli, isRedis, err := getConnection(ctx, app, clusterModel, entity)
	defer redisCli.Close()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get connection failed", logit.Error("error", err))
		return nil, err
	}
	configName := "*"
	var getRet []any
	if isRedis {
		getRet, err = redisCli.Do(ctx, RenameConfigGetCmd, "get", configName).Slice()
		if err != nil {
			getRet, err = redisCli.ConfigGet(ctx, configName).Result()
			if err != nil {
				resource.LoggerTask.Error(ctx, "config get fail", logit.String("entity", entity),
					logit.Error("error", err))
				return nil, fmt.Errorf("config get fail:%w", err)
			}
		}
	} else {
		getRet, err = redisCli.Do(ctx, ProxyConfigCMD, "get", configName).Slice()
		if err != nil {
			resource.LoggerTask.Error(ctx, "proxy config get fail", logit.String("entity", entity),
				logit.Error("error", err))
			return nil, fmt.Errorf("proxy config get fail:%w", err)
		}
	}
	if len(getRet) == 0 {
		resource.LoggerTask.Warning(ctx, "config get empty", logit.String("entity", entity))
		return nil, fmt.Errorf("config get fail:%w", err)
	}

	if len(getRet) < 2 || len(getRet)%2 != 0 {
		resource.LoggerTask.Warning(ctx, "ret fmt not valid", logit.String("entity", entity),
			logit.String("raw ret", base_utils.Format(getRet)))
		return nil, fmt.Errorf("ret fmt not valid,raw ret:%s", base_utils.Format(getRet))
	}
	keyIndex := 0
	valIndex := 1
	confMap := make(map[string]string)
	for valIndex < len(getRet) {
		confMap[cast.ToString(getRet[keyIndex])] = cast.ToString(getRet[valIndex])
		keyIndex += 2
		valIndex += 2
	}
	return confMap, nil
}

func getConnection(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster,
	entity string) (*single_redis.SingleClient, bool, error) {
	for _, cluster := range app.Clusters {
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.NodeId == entity {
				return single_redis.NewClient(node.FloatingIP, node.Port, single_redis.WithPassword(clusterModel.RedisAuth)), true, nil
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.ProxyId == entity {
				return single_redis.NewClient(proxy.FloatingIP, proxy.Port, single_redis.WithPassword(clusterModel.ClientAuth)), false, nil
			}
		}
	}
	return nil, false, fmt.Errorf("entity %s not found", entity)
}
