package migrate

import (
	"context"
	"errors"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/getvars"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/render"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

const (
	MigrateToNewAgentActionName           = "migrate_to_newagent"
	MigrateRollbackActionName             = "migrate_to_newagent_rollback"
	MigrateCheckPushBcmActionName         = "migrate_check_push_bcm"
	MigrateCheckRollbackPushBcmActionName = "migrate_check_rollback_push_bcm"
	MigrateToNewAgentTimeoutSec           = 180
)

type MigrateToNewAgentXagentRequest struct {
	Meta          *xagent.Meta           `json:"meta"`
	PkgsToInstall []*deploy.PkgToInstall `json:"pkgs_to_install"`
	AgentRecovers *getvars.AgentRecovers `json:"agent_recovers"`
}

func ProcessMigrateToNewAgent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processMigrateToNewAgent(ctx, teu, false); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	return nil
}

func ProcessRollbackMigrateToNewAgent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processMigrateToNewAgent(ctx, teu, true); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	return nil
}

func ProcessCheckPushBcm(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processCheckPushBcm(ctx, teu, false); err != nil {
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	return nil
}

func ProcessCheckRollbackPushBcm(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processCheckPushBcm(ctx, teu, true)
}

func processCheckPushBcm(ctx context.Context, teu *workflow.TaskExecUnit, isRollback bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("err", err))
		return err
	}
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("err", err))
		return err
	}
	g := &gtask.Group{
		Concurrent:    50,
		AllowSomeFail: true,
	}
	time.Sleep(75 * time.Second)
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			g.Go(func() error {
				return checkPushBcm(ctx, app, clusterModel, node.NodeId, func() string {
					if isRollback {
						return MigrateCheckRollbackPushBcmActionName
					}
					return MigrateCheckPushBcmActionName
				}())
			})
		}
	}
	for _, itf := range app.Interfaces {
		itf := itf
		for _, proxy := range itf.Proxys {
			proxy := proxy
			g.Go(func() error {
				return checkPushBcm(ctx, app, clusterModel, proxy.ProxyId, func() string {
					if isRollback {
						return MigrateCheckRollbackPushBcmActionName
					}
					return MigrateCheckPushBcmActionName
				}())
			})
		}
	}
	count, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check push bcm failed", logit.Error("err", err), logit.Int("count", count))
		return err
	}
	return nil
}

func processMigrateToNewAgent(ctx context.Context, teu *workflow.TaskExecUnit, isRollback bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app failed", logit.Error("err", err))
		return err
	}
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("err", err))
		return err
	}
	if err := checkStatus(ctx, clusterModel); err != nil {
		resource.LoggerTask.Warning(ctx, "check status failed", logit.Error("err", err))
		return err
	}
	req := &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster_model_interface.CACHE_CLUSTER_MODIFY_CONFIGURED,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if isRollback {
		req.Model.UseNewAgent = ""
		req.RequiredFields = []string{"use_new_agent"}
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("err", err))
		return err
	}
	if !isRollback && app.Type == x1model.AppTypeCluster && clusterModel.ClientAuth != "" {
		if err := updateProxyDefaultAuth(ctx, app, clusterModel); err != nil {
			resource.LoggerTask.Warning(ctx, "update client auth failed", logit.Error("err", err))
			return err
		}
		if err := updateProxyAcls(ctx, app, clusterModel); err != nil {
			resource.LoggerTask.Warning(ctx, "update proxy acls failed", logit.Error("err", err))
			return err
		}
	}
	g := &gtask.Group{
		Concurrent:    getConcurrentNum(clusterModel),
		AllowSomeFail: false,
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			if node.Role == x1model.RoleTypeMaster {
				continue
			}
			g.Go(func() error {
				return migrateToNewAgent(ctx, app, clusterModel, node.NodeId, isRollback)
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "migrate slave nodes to new agent failed", logit.Error("err", err))
		return err
	}
	g = &gtask.Group{
		Concurrent:    getConcurrentNum(clusterModel),
		AllowSomeFail: false,
	}
	for _, cluster := range app.Clusters {
		cluster := cluster
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			node := node
			if node.Role == x1model.RoleTypeSlave {
				continue
			}
			g.Go(func() error {
				return migrateToNewAgent(ctx, app, clusterModel, node.NodeId, isRollback)
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "migrate master nodes to new agent failed", logit.Error("err", err))
		return err
	}
	g = &gtask.Group{
		Concurrent:    getConcurrentNum(clusterModel),
		AllowSomeFail: false,
	}
	for _, itf := range app.Interfaces {
		itf := itf
		for _, proxy := range itf.Proxys {
			proxy := proxy
			g.Go(func() error {
				return migrateToNewAgent(ctx, app, clusterModel, proxy.ProxyId, isRollback)
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "migrate proxy to new agent failed", logit.Error("err", err))
		return err
	}
	if err := updateUseNewAgent(ctx, app, clusterModel, isRollback); err != nil {
		resource.LoggerTask.Warning(ctx, "update use new agent failed", logit.Error("err", err))
		return err
	}
	return nil
}

func getConcurrentNum(clusterModel *csmaster.CsmasterCluster) int {
	n := clusterModel.InstanceNum / 2
	if n > 10 {
		n = 10
	}
	if n < 1 {
		n = 1
	}
	return int(n)
}

func updateUseNewAgent(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, isRollback bool) error {
	req := &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status:      csmaster_model_interface.CACHE_CLUSTER_RUNNING,
			UseNewAgent: "yes",
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}
	if isRollback {
		req.Model.UseNewAgent = ""
		req.RequiredFields = []string{"use_new_agent"}
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("err", err))
		return err
	}
	app.UseNewAgent = "yes"
	if isRollback {
		app.UseNewAgent = "no"
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "save app failed", logit.Error("err", err))
		return err
	}
	return nil
}

func migrateToNewAgent(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, entity string, isRollback bool) error {
	if isRollback {
		return migrateToNewAgentRollback(ctx, app, clusterModel, entity)
	}
	deployParams, err := getDeployParams(ctx, &DiffRuntimeConfAndNewConfParams{
		App:          app,
		ClusterModel: clusterModel,
		Entity:       entity,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deploy params failed", logit.Error("err", err))
		return err
	}
	deployParams.TaskID = "warning-no-expect-update"
	deployParams.NoUpdate = true
	toDeployPkgsRet, err := deploy.GetAndUpdateToDeployPkgs(ctx, deployParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get to deploy pkgs", logit.Error("err", err))
		return err
	}
	getVarsParams, err := getvars.GetParamters(ctx, app.AppId, entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get vars", logit.Error("err", err))
		return err
	}
	var pkgToInstall []*deploy.PkgToInstall
	for _, pkg := range toDeployPkgsRet.ToDeployPkgInfos {
		if conf.IsCorePkg(pkg.Name) {
			renderedConfs, err := render.GetRenderdConf(ctx, getVarsParams, pkg.TplID, pkg.FullVersion)
			if err != nil {
				resource.LoggerTask.Warning(ctx, "fail to get rendered conf", logit.Error("err", err))
				return err
			}
			pkgToInstall = append(pkgToInstall, &deploy.PkgToInstall{
				Name:          pkg.Name,
				DeployPath:    deployParams.BaseDir,
				NoNeedExecute: true,
				RenderedConfs: renderedConfs,
			})
		}
	}
	agentRecovers, err := getvars.GetAgentRecover(ctx, getVarsParams)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to get agent recover", logit.Error("err", err))
		return err
	}
	request := &MigrateToNewAgentXagentRequest{
		Meta: &xagent.Meta{
			Engine:        deployParams.EngineType,
			EngineVersion: deployParams.KernelMajorVersion,
			Basedir:       deployParams.BaseDir,
			Port:          int32(deployParams.NodePort),
		},
		PkgsToInstall: pkgToInstall,
		AgentRecovers: agentRecovers,
	}
	_, err = xagent.Instance().DoAsync(ctx, &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: deployParams.XagentHost,
			Port: int32(deployParams.XagentPort),
		},
		Action:     MigrateToNewAgentActionName,
		Params:     request,
		TimeoutSec: MigrateToNewAgentTimeoutSec,
	}).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to migrate to new agent", logit.Error("err", err))
		return err
	}
	if err := checkAlive(ctx, app, clusterModel, entity); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to check alive", logit.Error("err", err))
		return err
	}
	return nil
}

func migrateToNewAgentRollback(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, entity string) error {
	deployParams, err := getDeployParams(ctx, &DiffRuntimeConfAndNewConfParams{
		App:          app,
		ClusterModel: clusterModel,
		Entity:       entity,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deploy params failed", logit.Error("err", err))
		return err
	}
	request := &MigrateToNewAgentXagentRequest{
		Meta: &xagent.Meta{
			Engine:        deployParams.EngineType,
			EngineVersion: deployParams.KernelMajorVersion,
			Basedir:       deployParams.BaseDir,
			Port:          int32(deployParams.NodePort),
		},
	}
	_, err = xagent.Instance().DoAsync(ctx, &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: deployParams.XagentHost,
			Port: int32(deployParams.XagentPort),
		},
		Action:     MigrateRollbackActionName,
		Params:     request,
		TimeoutSec: MigrateToNewAgentTimeoutSec,
	}).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to migrate to new agent", logit.Error("err", err))
		return err
	}
	if err := checkAlive(ctx, app, clusterModel, entity); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to check alive", logit.Error("err", err))
		return err
	}
	return nil
}

func checkPushBcm(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, entity, action string) error {
	deployParams, err := getDeployParams(ctx, &DiffRuntimeConfAndNewConfParams{
		App:          app,
		ClusterModel: clusterModel,
		Entity:       entity,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deploy params failed", logit.Error("err", err))
		return err
	}
	request := &MigrateToNewAgentXagentRequest{
		Meta: &xagent.Meta{
			Engine:        deployParams.EngineType,
			EngineVersion: deployParams.KernelMajorVersion,
			Basedir:       deployParams.BaseDir,
			Port:          int32(deployParams.NodePort),
		},
	}
	_, err = xagent.Instance().DoAsync(ctx, &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: deployParams.XagentHost,
			Port: int32(deployParams.XagentPort),
		},
		Action:     action,
		Params:     request,
		TimeoutSec: MigrateToNewAgentTimeoutSec,
	}).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "fail to migrate to new agent", logit.Error("err", err))
		return err
	}
	return nil
}

func updateProxyDefaultAuth(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster) error {
	proxyAcls, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name = ? AND status != ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy acl failed", logit.Error("err", err))
		return err
	}
	pw, err := crypto_utils.EncryptKey(clusterModel.ClientAuth)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "encrypt client auth failed", logit.Error("err", err))
		return err
	}
	if len(proxyAcls) == 0 {
		proxyAcls = append(proxyAcls, &x1model.ProxyAcl{
			AppID:          app.AppId,
			AccountName:    x1model.DefaultACLUser,
			CreateAt:       time.Now(),
			UpdateAt:       time.Now(),
			Version:        0,
			Engine:         "proxy",
			Password:       pw,
			AllowedCmds:    "@all",
			AllowedSubCmds: "",
			KeyPatterns:    "*",
			Properties:     "",
			Status:         x1model.ACLStatusInUse,
		})
	} else {
		proxyAcls[0].Password = pw
		proxyAcls[0].UpdateAt = time.Now()
	}
	if err := x1model.ProxyAclSave(ctx, proxyAcls); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy acl failed", logit.Error("err", err))
		return err
	}
	return nil
}

func checkAlive(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster, entity string) error {
	redisCli, _, err := getConnection(ctx, app, clusterModel, entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get connection failed", logit.Error("err", err))
	}
	defer redisCli.Close()
	for i := 0; i < 3; i++ {
		if err := redisCli.Ping(ctx).Err(); err != nil {
			resource.LoggerTask.Warning(ctx, "ping failed", logit.Error("err", err))
			return err
		}
		time.Sleep(time.Second)
	}
	return nil
}

func updateProxyAcls(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster) error {
	proxyAcls, err := x1model.ProxyAclGetAllByCond(ctx, "app_id = ? AND account_name != ? AND status != ?",
		app.AppId, x1model.DefaultACLUser, x1model.ACLStatusDeleted)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get proxy acl failed", logit.Error("err", err))
		return err
	}
	var clusterAcls []*csmaster_model_interface.ClusterAclUser
	if err := resource.CsmasterModel.GetAllByCond(ctx, &clusterAcls, "cluster_id = ? AND update_status != ?",
		clusterModel.Id, csmaster_model_interface.ACLUSER_DELETED); err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster acl failed", logit.Error("err", err))
		return err
	}
	for _, clusterAcl := range clusterAcls {
		if clusterAcl.UpdateStatus != csmaster_model_interface.ACLUSER_UPDATE_SUCCESS {
			resource.LoggerTask.Warning(ctx, "cluster acl not update success", logit.String("clusterAcl", base_utils.Format(clusterAcl)))
			return errors.New("cluster acl not update success")
		}
		found := false
		pw, err := crypto_utils.EncryptKey(clusterAcl.Password)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "encrypt password failed", logit.Error("err", err))
			return err
		}
		for _, proxyAcl := range proxyAcls {
			if proxyAcl.AccountName == clusterAcl.UserName {
				proxyAcl.Password = pw
				proxyAcl.UpdateAt = time.Now()
				found = true
			}
		}
		if !found {
			proxyAcls = append(proxyAcls, &x1model.ProxyAcl{
				AppID:          app.AppId,
				AccountName:    clusterAcl.UserName,
				CreateAt:       time.Now(),
				UpdateAt:       time.Now(),
				Version:        0,
				Engine:         "proxy",
				Password:       pw,
				AllowedCmds:    clusterAcl.AllowedCommands,
				AllowedSubCmds: clusterAcl.AllowedSubCommands,
				KeyPatterns:    clusterAcl.KeyPatterns,
				Properties:     "",
				Status:         x1model.ACLStatusInUse,
			})
		}
	}
	if err := x1model.ProxyAclSave(ctx, proxyAcls); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy acl failed", logit.Error("err", err))
		return err
	}
	return nil
}
