package migrate

type PreCheckRequest struct {
	AppIDs  []string
	BatchID string
}

type MigrateRequest struct {
	AppID   string
	BatchID string
}

type CheckRecord struct {
	ID          int64  `json:"id" gorm:"column:id"`                     // bigint(20), 主键, BTREE
	AppID       string `json:"app_id" gorm:"column:app_id"`             // varchar(64), BTREE
	BatchID     string `json:"batch_id" gorm:"column:batch_id"`         // varchar(64), BTREE, 批次id, 顺便生成一个批次的编号
	ConfName    string `json:"conf_name" gorm:"column:conf_name"`       // varchar(128)
	Entity      string `json:"entity" gorm:"column:entity"`             // varchar(64)
	BeforeVal   string `json:"before_val" gorm:"column:before_val"`     // varchar(2048)
	AfterVal    string `json:"after_val" gorm:"column:after_val"`       // varchar(2048)
	ManualCheck string `json:"manual_check" gorm:"column:manual_check"` // varchar(16), yes:人工确认通过, no:人工确认不通过
}

func (m CheckRecord) TableName() string {
	return "check_record"
}
