package debugserver

import (
	"context"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"

	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/getvars"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/render"
)

func StartDebugServer() error {
	logF, err := os.Create("log/debugserver.log")
	logWF, err := os.Create("log/debugserver_wf.log")
	if err != nil {
		return err
	}
	gin.DefaultWriter = logF
	gin.DefaultErrorWriter = logWF
	r := gin.Default()
	r.GET("/getVars", getVars)
	r.GET("/getRenders", getRenders)
	r.GET("/getAgentRecovers", getAgentRecovers)
	return r.Run(":8974")
}

func getVars(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	appId := c.Query("appId")
	entity := c.Query("entity")
	syncAgent := c.Query("syncAgent")
	if len(appId) == 0 {
		c.JSON(400, gin.H{
			"message": "appId is empty",
		})
	}
	if len(entity) == 0 {
		c.JSON(400, gin.H{
			"message": "entity is empty",
		})
	}
	params, err := getvars.GetParamters(ctx, appId, entity)
	if err != nil {
		c.JSON(500, gin.H{
			"message": err.Error(),
		})
	}
	switch params.EngineType {
	case x1model.EngineRedis:
		if syncAgent == "yes" {
			vars, err := getvars.GetSyncAgentVars(ctx, params)
			if err != nil {
				c.JSON(500, gin.H{
					"message": err.Error(),
				})
			}
			c.JSON(200, gin.H{
				"vars": vars,
			})
		} else {
			vars, err := getvars.GetRedisVar(ctx, params)
			if err != nil {
				c.JSON(500, gin.H{
					"message": err.Error(),
				})
			}
			c.JSON(200, gin.H{
				"vars": vars,
			})
		}

	case x1model.EngineBDRPProxy:
		vars, err := getvars.GetProxyVars(ctx, params)
		if err != nil {
			c.JSON(500, gin.H{
				"message": err.Error(),
			})
		}
		c.JSON(200, gin.H{
			"vars": vars,
		})
	case x1model.EnginePegaDB:
		if syncAgent == "yes" {
			vars, err := getvars.GetSyncAgentVars(ctx, params)
			if err != nil {
				c.JSON(500, gin.H{
					"message": err.Error(),
				})
			}
			c.JSON(200, gin.H{
				"vars": vars,
			})
		} else {
			vars, err := getvars.GetPegaVars(ctx, params)
			if err != nil {
				c.JSON(500, gin.H{
					"message": err.Error(),
				})
			}
			c.JSON(200, gin.H{
				"vars": vars,
			})
		}
	default:
		c.JSON(500, gin.H{
			"message": "unknown engine type",
		})
	}
}

func getRenders(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	appId := c.Query("appId")
	entity := c.Query("entity")
	tplID := c.Query("tplId")
	packageVersion := c.Query("packageVersion")
	if len(appId) == 0 {
		c.JSON(400, gin.H{
			"message": "appId is empty",
		})
	}
	if len(entity) == 0 {
		c.JSON(400, gin.H{
			"message": "entity is empty",
		})
	}
	params, err := getvars.GetParamters(ctx, appId, entity)
	if err != nil {
		c.JSON(500, gin.H{
			"message": err.Error(),
		})
	}
	renders, err := render.GetRenderdConf(ctx, params, tplID, packageVersion)
	if err != nil {
		c.JSON(500, gin.H{
			"message": err.Error(),
		})
	}
	c.JSON(200, gin.H{
		"renders": renders,
	})
}

func getAgentRecovers(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	appId := c.Query("appId")
	entity := c.Query("entity")
	if len(appId) == 0 {
		c.JSON(400, gin.H{
			"message": "appId is empty",
		})
	}
	if len(entity) == 0 {
		c.JSON(400, gin.H{
			"message": "entity is empty",
		})
	}
	params, err := getvars.GetParamters(ctx, appId, entity)
	if err != nil {
		c.JSON(500, gin.H{
			"message": err.Error(),
		})
	}
	agentRecovers, err := getvars.GetAgentRecover(ctx, params)
	if err != nil {
		c.JSON(500, gin.H{
			"message": err.Error(),
		})
	}
	c.JSON(200, gin.H{
		"agentRecovers": agentRecovers,
	})
}
