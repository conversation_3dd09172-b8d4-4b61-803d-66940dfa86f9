package render

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"text/template"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/sync-agent2/pkg/config"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/newagent/getvars"
)

type RenderedConfig struct {
	ConfPath    string `json:"conf_path"`
	ConfContent string `json:"conf_content"`
}

var RedisConfigHasSubKey = []string{"rename-command", "loadmodule", "client-output-buffer-limit"}

var TplContentMap = make(map[string]string)
var TplContentMutex = &sync.Mutex{}

func removeDuplicate(content, confPath string) string {
	lines := strings.Split(content, "\n")
	newLines := []string{}
	done := make(map[string]bool)

	for i, line := range lines {
		if strings.HasPrefix(strings.TrimSpace(line), "#") {
			continue
		}
		if strings.HasPrefix(strings.TrimSpace(line), "servers:") && confPath == "nutcracker_.yml" {
			for j := i; j < len(lines); j++ {
				newLines = append(newLines, lines[j])
			}
			break
		}
		key := getKey(line, confPath)
		if len(key) == 0 {
			continue
		}
		if _, has := done[key]; has {
			continue
		}
		newLine := line
		for j := i + 1; j < len(lines); j++ {
			if strings.HasPrefix(strings.TrimSpace(lines[j]), "#") {
				continue
			}
			if key == getKey(lines[j], confPath) {
				newLine = lines[j]
			}
		}
		newLines = append(newLines, newLine)
		done[key] = true
	}
	return strings.Join(newLines, "\n") + "\n"
}

func getKey(line, confPath string) string {
	if confPath == "redis_.conf" || confPath == "kvrocks_.conf" {
		sp := strings.Split(line, " ")
		if len(sp) > 0 {
			if in, _ := base_utils.InArray(sp[0], RedisConfigHasSubKey); in && len(sp) > 1 {
				return sp[0] + " " + sp[1]
			}
			return sp[0]
		}
		return ""
	}
	if confPath == "nutcracker_.yml" {
		sp := strings.Split(line, ":")
		if len(sp) > 0 {
			return sp[0]
		}
		return ""
	}
	return ""
}

func GetTplContent(ctx context.Context, tplID, deployPath string) (string, error) {
	TplContentMutex.Lock()
	defer TplContentMutex.Unlock()
	key := tplID + "-" + deployPath
	if content, has := TplContentMap[key]; has {
		return content, nil
	}
	filePath := filepath.Join(env.RootDir(), "templates", tplID, deployPath+".tpl")
	file, err := os.Open(filePath)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "open tpl file fail", logit.String("filePath", filePath), logit.Error("error", err))
		return "", err
	}
	content, err := io.ReadAll(file)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "read tpl file fail", logit.String("filePath", filePath), logit.Error("error", err))
		return "", err
	}
	TplContentMap[key] = string(content)
	return string(content), nil
}

func GetRenderdConf(ctx context.Context, params *getvars.Parameter, tplID, pkgVersion string) ([]*RenderedConfig, error) {
	confTpls, err := x1model.ConfTplGetAllByCond(ctx, "tpl_id = ?", tplID)
	if err != nil {
		return nil, err
	}
	if len(confTpls) == 0 {
		return nil, fmt.Errorf("tpl_id(%s) not found", tplID)
	}
	params.PackageVersion = pkgVersion
	var renderedConfs []*RenderedConfig
	for _, confTplItem := range confTpls[0].Items {
		tplContent, err := GetTplContent(ctx, tplID, confTplItem.DeployPath)
		if err != nil {
			return nil, err
		}
		t, err := template.New(tplID + "-" + confTplItem.DeployPath).Parse(tplContent)
		if err != nil {
			return nil, err
		}
		buffer := bytes.Buffer{}
		var vars map[string]string
		switch confTpls[0].PkgType {
		case "redis-3", "slot-redis-3", "redis-5", "slot-redis-4", "slot-redis-6", "slot-redis-7":
			vars, err = getvars.GetRedisVar(ctx, params)
			if err != nil {
				return nil, err
			}
		case "proxy-slot-3", "proxy-4":
			vars, err = getvars.GetProxyVars(ctx, params)
			if err != nil {
				return nil, err
			}
		case "PegaDB2-2", "PegaDB2-3":
			vars, err = getvars.GetPegaVars(ctx, params)
			if err != nil {
				return nil, err
			}
		case "sync-agent-1", "sync-agent2-2":
			vars, err = getvars.GetSyncAgentVars(ctx, params)
			if err != nil {
				return nil, err
			}
		default:
			return nil, fmt.Errorf("unknown package name: %s", confTpls[0].PkgType)
		}
		if err := t.Execute(&buffer, vars); err != nil {
			return nil, err
		}
		rawConf := buffer.String()
		resource.LoggerTask.Trace(ctx, "trace rendered conf",
			logit.String("conf_path", confTplItem.DeployPath), logit.String("conf_content", rawConf))
		if confTplItem.DeployPath == "redis_.conf" || confTplItem.DeployPath == "nutcracker_.yml" || confTplItem.DeployPath == "kvrocks_.conf" {
			rawConf = removeDuplicate(rawConf, confTplItem.DeployPath)
			resource.LoggerTask.Trace(ctx, "trace rendered conf after remove duplicate",
				logit.String("conf_path", confTplItem.DeployPath), logit.String("conf_content", rawConf))
		}
		if confTplItem.DeployPath == "sync_.yml" {
			if err := config.ValidateYamlConf(rawConf); err != nil {
				return nil, fmt.Errorf("validate sync_agent yaml conf failed: %v", err)
			}
		}
		renderedConfs = append(renderedConfs, &RenderedConfig{
			ConfPath:    confTplItem.DeployPath,
			ConfContent: rawConf,
		})
	}
	return renderedConfs, nil
}
