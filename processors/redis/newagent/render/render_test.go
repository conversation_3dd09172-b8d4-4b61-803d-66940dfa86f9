package render

import "testing"

func Test_removeDuplicate(t *testing.T) {
	nut_conf := `server_failure_limit: 2
  stale_slave_readable: true
  eject_slow_server: true
  slowlog_log_slower_than: 300
  meta_use_dns: 1
  support_multi_db: true
  server_connections: 16
  db_count: 256
  need_reserved_conn: false
  server_connections_load_balance: false
  ignore_select_when_multi_db_disabled: true
  qpsquota: 200000
  client_keepalive: 300
  client_idle_timeout: 4000
  proxy_id: 200004656
  proxy_ins_id: 25575
  mcpack_port: 8384
  mcpack_msg_len: 67108864
  rename_prefix: aae420ac56ef116058218c11d8b35b30
  support_scan: true
  eject_slow_server: true
  servers:
   - bj_jefqmyovqwwu_0
     172.17.33.159:6614:1
`
	t.Run("nutcracker_.conf", func(t *testing.T) {
		got := removeDuplicate(nut_conf, "nutcracker_.yml")
		t.Log(got)
	})

	redis_conf := `stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 512mb 256mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
activedefrag no
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 30
active-defrag-cycle-min 1
active-defrag-cycle-max 25
# requirepass nopass
# masterauth nopass
server_cpulist 1
bio_cpulist 0
aof_rewrite_cpulist 0
bgsave_cpulist 0
hz 100

rename-command SYNC aae420ac56ef116058218c11d8b35b30SYNC
rename-command BGSAVE aae420ac56ef116058218c11d8b35b30BGSAVE
rename-command LASTSAVE aae420ac56ef116058218c11d8b35b30LASTSAVE
rename-command SAVE aae420ac56ef116058218c11d8b35b30SAVE
rename-command DEBUG aae420ac56ef116058218c11d8b35b30DEBUG
rename-command SHUTDOWN aae420ac56ef116058218c11d8b35b30SHUTDOWN
rename-command MIGRATE aae420ac56ef116058218c11d8b35b30MIGRATE
`
	t.Run("redis_.conf", func(t *testing.T) {
		got := removeDuplicate(redis_conf, "redis_.conf")
		t.Log(got)
	})
}
