option cc_generic_services = true;
option py_generic_services = true;

package bccs.csmaster;

message Id{    /*agent持久化该项*/
  optional string uuid = 1;
  optional string user_id = 2;
  optional int64 cluster_id = 3;
  optional string node_id = 4;
  optional string show_id = 5;
  optional string hash_name = 6;
}

message MasterSlavePair {
  optional string master = 1;
  optional string slave = 2;
}

message Latency {
  optional double max_latency = 1;
  optional double avg_latency = 2;
  optional double min_latency = 3;
}

message Auth {
  optional string client_auth = 1;
  optional string meta_auth = 2;
  optional string redis_auth = 3;
  optional int32 auth_sign = 4;
}

message RestoreBackupRequest {
  optional int64 backup_id = 1;
  optional string server_ak = 2;
  optional string server_sk = 3;
  optional string bos_host = 4;
  optional string bucket_name = 5;
  optional string object_key = 6;
  optional string download_ak = 7;
  optional string download_sk = 8;
}

message RestoreBackupResponse {
  optional int64 restore_backup_id = 1;
  optional int32 status = 2;
  optional string object_key = 3;
  optional int64 cluster_id = 4;
  optional string shard_name = 5;
}

message RestoreStartRequest {
  optional int64 restore_id = 1;
  optional string restore_time = 2;
  optional string object_key = 3; //区分是本地按时间恢复还是克隆实例的恢复
  optional string server_ak = 4;
  optional string server_sk = 5;
  optional string bos_host = 6;
  optional string bucket_name = 7;
}

message RestoreStartResponse {
  optional int64 restore_id = 1;
  optional int32 status = 2;
}

message RestoreExecuteRequest {
  optional int64 restore_exec_id = 1;
}

message RestoreExecuteResponse {
  optional int64 restore_exec_id = 1;
  optional int32 status = 2;
}

message HeartBeatRequest {
  optional int32 status = 1; /* 0: creating, 1: running */
  optional Id id = 2;
  optional string hostname = 3;
  optional int32 ack = 4;
  optional int32 ack_wl = 5;  /* set white ip list result -- 0:fail, 1:ok */
  repeated MasterSlavePair pair = 6;
  optional string version = 7; /* version of scs agent */
  optional int64 redisMemUsed = 8; /* memory usage of redis instance */
  optional int32 data_process_status = 9; // 0:init, 1:operating, 2:success, 3:fail
  optional string data_process_error = 10; //导数据的错误信息
  optional string transaction_id = 11;
  optional int64 used_mem = 12;
  optional Latency latency = 13;
  optional int64 qps = 14;
  optional BackupResponse backup_response = 15;
  optional int32 auth_sign = 16;
  optional int32 whitelist_version = 17;
  optional int32 conf_version = 18;
  optional RecoverResponse recover_response = 19;
  optional RestartResponse restart_response = 20;
  optional UpdateResponse update_response = 21;
  optional int32 master_linkup = 22;
  repeated AclResponse acl_response = 23;
  optional int32 acluser_version = 24;
  optional int32 cur_mnt_size = 25;    // in gb
  optional int64 cur_max_memory = 26;  // in bytes
  optional RestoreBackupResponse restore_backup_response = 27;
  optional RestoreStartResponse restore_start_response = 28;
  optional RestoreExecuteResponse restore_execute_response = 29;
}

message HeartBeatResponse {
  optional int32 action = 1; /* 0:create, 1:normal */
  optional int32 type = 2; /*0:mc, 1:redis, 2:proxy, 3:sentinel, 4:hsdb 5:pegadb 6:couchbase*/
  optional RedisConf redis_conf = 3;
  optional SentinelConf sentinel_conf = 4;
  optional ProxyConf proxy_conf = 5;
  optional MemcachedConf mc_conf = 6;
  optional Id id = 7;
  optional DataProcessParam data_process_param = 8; //执行bos tool的一些参数
  optional BackupRequest backup_request = 9;
  optional string kernel_version = 10;
  optional Auth auth = 11;
  repeated ConfItem conf_change_list = 12;
  optional int32 whitelist_version = 13;
  optional int32 conf_version = 14;
  optional string elb_ip = 15;
  optional int32 elb_port = 16;
  optional RecoverRequest recover_request = 17;
  optional RestartRequest restart_request = 18;
  optional UpdateRequest update_request = 19;
  optional MaChannelInfo ma_channel_info = 20;
  optional BcmInfo bcm_info = 21;
  repeated AclRequest acl_request = 22;
  optional int32 acluser_version = 23;
  optional string domain = 24;
  optional int32 target_mnt_size = 25;  // in gb
  optional int64 target_max_memory = 26;  // in bytes
  optional RestoreStartRequest restore_start_request = 27;
  optional RestoreExecuteRequest restore_execute_request = 28;
  optional int32 enable_restore = 29;
  optional RestoreBackupRequest restore_backup_request = 30;
  optional int32 enable_slow_log = 31;
  optional int32 enable_hotkey = 32;
  optional LogRequest log_request = 33;
  optional CouchbaseConf cb_conf = 34;
  optional string resource_type = 35;
  optional int32 heatbeat_interval = 36;
}

message LogRequest {
  optional string download_ak = 1;
  optional string download_sk = 2;
  optional string log_bucket = 3;
}

message AclRequest {
  optional string user_name = 1;
  optional int32 acl_action = 2;
  optional string password = 3;
  optional string allowed_commands = 4;
  optional string allowed_sub_commands = 5;
  optional string key_patterns = 6;
  optional string transaction_id = 7;
}

message AclResponse {
  optional int32 acl_action = 1;
  optional string user_name = 2;
  optional int32 status = 3;
  optional int32 transaction_id = 4;
}

message BcmItemPushCycle {
  optional string item = 1;
  optional int32 cycle = 2;
}

message BcmInfo {
  optional string bcm_ak = 1;
  optional string bcm_sk = 2;
  repeated BcmItemPushCycle bcm_item_push_cycle = 3;
  optional int32 default_cycle = 4;
  optional string bcm_host = 5;
  optional int32 bcm_port = 6;
  optional int32 refactoring_v1 = 7;
}

message DataProcessParam {
  optional int32 data_process_action = 1;  //0：bos_action_init 1:导入bos数据 2：导入redis数据 3:删除数据
  optional string ak = 2;
  optional string sk = 3;
  optional string bucket_name = 4;
  optional string object_time = 5;  //aof 保存为cluster_id/object_time/hash_name/uuid.time.tar.gz
  optional int32 time_out = 6;    //备份任务超时时间
  optional string data_file_type = 7;   //aof 或rdb     //yes
  optional string domain = 8;                 //yes
  repeated string wget_url = 9; //导入数据的bos地址，aof可以多个  //yes
  optional int32 time_to_remove = 10; //可选,单位为天,默认7天
  optional string bos_host = 11;
  optional string hash_name = 12;
  optional string uuid = 13;
  optional int32 cluster_port = 14;    //yes
  optional string transaction_id = 15;
  optional int64 cluster_id = 16;
  optional int32 db_index = 17;    //0-255
}

message BackupResponse {
  optional int64 backup_id = 1;
  optional int32 status = 2;    //0: 未开始；1:备份中；2:备份完成；3:备份失败
  optional int64 object_size = 3;
  optional string url = 4;
}

message BackupRequest {
  optional int64 backup_id = 1;    //为0说明中控模块没有在备份状态，如果此时在备份中，应当取消。
  optional string server_ak = 2;
  optional string server_sk = 3;
  optional string bos_host = 4;
  optional string bucket_name = 5;
  optional string object_key = 6;
  optional string download_ak = 7;
  optional string download_sk = 8;
}

message RecoverRequest {
  optional int64 recover_id = 1;
  optional string server_ak = 2;
  optional string server_sk = 3;
  optional string bos_host = 4;
  optional string bucket_name = 5;
  optional string object_key = 6;
}

message RecoverResponse {
  optional int64 recover_id = 1;
  optional int32 status = 2;
}

message RestartRequest {
  optional int64 restart_id = 1;
}

message RestartResponse {
  optional int64 restart_id = 1;
  optional int32 status = 2;
}

message UpdateResponse {
  optional string image_status = 1;
  optional string image_version = 2;
  optional string inst_version = 3;
  optional string agent_version = 4;
}

message UpdateRequest {
  optional string expect_version = 1;
  optional string agent_version = 2;
  optional string inst_version = 3;
}

message MemcachedConf {
  optional int32 port =1;
  optional int32 mem = 2; /*MB*/ // 按套餐配置,注意用户的指和套餐的值
}

message CouchbaseConf {
  optional string user = 1;
  optional string passwd = 2;
  optional int32 port = 3;
  optional int32 isRs = 4; //is blb rs
}

message RedisConf  {
  optional int32 port =1;
  optional int32 master_slave = 2; /*0:master, 1:slave*/
  optional string master_ip = 3;
  optional int32 master_port = 4;
  optional int32 persist_type =5;
  optional string meta_master_ip = 6; /* master ip of metaservers */
  optional int32 meta_master_port = 7; /* port of master metaserver */
  optional int32 redis_instance_id = 8; /* instance id of redis server */
  optional int32 redis_shard_id = 9; /* shard id of redis server */
  optional int32 redis_cluster_id = 10; /* cluster id of redis server */
  optional int64 master_max_memory = 11; /* max memory of redis */
  optional string master_max_memory_policy = 12; /* max memory policy of redis */
  optional int32 redis_version = 13; /* 0:not slot version, 1:slot version  2:slot version single shard with no proxy */
  repeated WhiteIpList white_ip_list = 14; /* whitelist of redis for single shard */
  optional int32 compaction_speed = 15; /*for pegadb -- restricting speed for compacting .sst files*/
  optional int32 max_write_buffer_number = 16; /*for pegadb -- lru cache size for seeking*/
  optional int32 write_buffer_size = 17; /*for pegadb -- buffer size for writing*/
  repeated string disabled_command = 18;/*for redis rename*/
  optional int64 memory_flavor = 19; /* memory flavor of redis */
  optional int64 g_shard_id = 20; /* g_shard_id of redis */
  optional RedisConstantConf constant_conf = 21;
  optional int32 cpu_num = 22;
  optional int32 metadata_block_cache_size = 23;
  optional int32 subkey_block_cache_size = 24;
  optional int64 disk_flavor = 25;
  optional string node_show_id = 26;
  optional int32 instance_type = 27;
  optional int32 is_readonly = 28;
}

message  ShardInfo  {
  optional string server_name = 1;
  optional string master_ip = 2;
  optional int32 master_port = 3;
}

message  SentinelConf  {
  optional int32 port =1;
  optional string pool_name = 2;
  repeated ShardInfo shard_info = 3;
}

message NodeInfo {
  optional string server_name = 1; /*server1, server2*/
  optional string node = 2;   /*ip:port:weight*/
  optional string uuid = 3; //
  optional string node_id = 4;
}

message ProxyConf {
  optional string pool_name = 1;
  optional int32 port = 2;
  optional int32 engine = 3;  /*0:mc, 1:redis*/
  repeated NodeInfo node_info = 4;
  optional int32 instance_qps = 5;
  optional string sentinel_ip = 6;
  optional int32 sentinel_port = 7;
  repeated WhiteIpList white_ip_list = 8;
  optional int32 appid = 9; /* pool name is appid which is used in metaserver */
  optional string proxy_meta_master_ip = 10;
  optional int32 proxy_meta_master_port = 11;
  optional int32 proxy_id = 12; /* proxy id used by metaserver */
  optional int32 proxy_instance_id = 13; /* proxy instance id used by metaserver */
  optional int32 proxy_version = 14; /* 0:not slot version, 1:slot version */
  optional int32 ip_tag = 15; /* 1:ipv4, 2:ipv6, 3:ipv4 and ipv6 */
  optional int32 stat_port = 16;
  optional int32 mbuf_size = 17;
  optional string node_show_id = 18;
  optional int32 instance_type = 19;
  optional int32 mcpack_port = 20;
  optional string backend_passwd = 21;
  optional int32 store_type = 22;
  optional int32 conn_num = 23;
  optional string cluster_entry = 24;
}

message WhiteIpList {
  optional string ip = 1;
  optional string mode = 2;  /* auth=r/w/rw  */
}

message ConfItem {
  optional string conf_name = 1;
  optional int32 conf_module = 2;  /* 1:redis 2:proxy  */
  optional string conf_value = 3;
}

message MaChannelInfo {
  optional string ma_meta_ip = 1;
  optional string ma_meta_auth = 2;
  optional int32  ma_meta_port = 3;
  optional string support_multi_active = 4;
  optional string user_filter_cmds = 5;
  optional string support_second_exec = 6;
  optional string proxy_auth = 7;
}

message RedisConstantConf {
  optional string use_hash_tag = 1;
  optional string db_slot_check = 2;
  optional string database_enable_on_loading = 3;
}

//nas heart beat
message NasId {
  optional string uuid = 1;
  optional string user_id = 2;
  optional string share_server_show_id = 3;
}

message NasHeartBeatRequest {
  optional NasId id = 1;
  optional string hostname = 2;
  optional string transaction_status = 3;
  optional string transaction_id = 4;
  optional string proc_status = 5;
}

message NasHeartBeatResponse {
  optional string action = 1;
  optional NasId id = 2;
  optional string transaction_id = 3;
  optional string errmsg = 4;
  optional NasAccessConf access_conf = 5;
}

message NasAccessConf {
  repeated NfsAccessConf nfs_conf = 1;
  repeated SambaAccessConf samba_conf = 2;
}

message NfsAccessConf {
  optional string host = 1;
  optional string mode = 2;
  optional string squash = 3;
  optional string sync_mode = 4;
  optional string directory = 5;
  optional string endpoint = 6;
  optional string bucket = 7;
  optional string ak = 8;
  optional string sk = 9;
  optional string device = 10;
  optional string session_token = 11;
}

message SambaAccessConf {
  optional string user = 1;
  optional string password = 2;
  optional string directory = 3;
  optional string endpoint = 4;
  optional string bucket = 5;
  optional string ak = 6;
  optional string sk = 7;
  optional string device = 8;
  optional string session_token = 9;
}

service HeartBeatService {
  rpc HeartBeat(HeartBeatRequest) returns (HeartBeatResponse);
  rpc nas_heart_beat(NasHeartBeatRequest) returns (NasHeartBeatResponse);
}