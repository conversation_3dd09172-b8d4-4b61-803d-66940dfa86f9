package getvars

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func GetProxyVars(ctx context.Context, param *Parameter) (map[string]string, error) {
	proxyVars := make(map[string]string)
	if param.App.Clusters[0].Engine == "redis" || param.App.Clusters[0].Engine == "pegadb" {
		proxyVars["hash"] = "crc16"
		proxyVars["distribution"] = "slot"
		proxyVars["engine"] = "true"
	} else {
		proxyVars["hash"] = "fnv1a_64"
		proxyVars["distribution"] = "ketama"
		proxyVars["engine"] = "false"
	}
	proxyVars["listen"] = fmt.Sprintf("0.0.0.0:%d", param.Proxy.Port)
	if strings.Contains(param.App.IpType, "ipv6") {
		proxyVars["listen"] = fmt.Sprintf(":::%d", param.Proxy.Port)
	}
	proxyVars["server_connections"] = strconv.Itoa(param.GetProxyServerConnectionCount())
	proxyVars["server_connections_load_balance"] = "false"
	proxyVars["rename_prefix"] = "  rename_prefix: aae420ac56ef116058218c11d8b35b30\n"
	if param.App.Clusters[0].Engine == "pegadb" {
		proxyVars["server_connections_load_balance"] = "true"
		proxyVars["rename_prefix"] = ""
	}
	proxyVars["mcpack_port"] = strconv.Itoa(param.Proxy.McpackPort)
	proxyVars["proxy_ins_id"] = strconv.Itoa(param.Proxy.ProxyShortID)
	proxyVars["proxy_id"] = strconv.Itoa(param.App.AppShortID)
	if len(param.App.AppGroupID) != 0 {
		proxyVars["proxy_ins_id"] = strconv.Itoa(param.Proxy.GlobalSeqID)
		proxyVars["proxy_id"] = strconv.Itoa(param.App.AppGroupSeqID)
	}
	proxyVars["pool_name"] = proxyVars["proxy_id"]
	proxyVars["client_auth"] = ""
	// 这里设置默认值，如果OP修改了该配置，会在FillProxyUserConfs中覆盖
	proxyVars["qpsquota"] = "200000"
	if len(param.GetProxyPassword()) != 0 {
		proxyVars["client_auth"] = fmt.Sprintf("  client_auth: \"%s\"\n", param.GetProxyPassword())
	}
	proxyVars["cluster_entry"] = param.GetClusterEntry()
	proxyVars["resource_type"] = param.CsmasterModel.Resource_type
	if err := FillProxyStartCmd(ctx, param, proxyVars); err != nil {
		return nil, err
	}
	if err := FillProxyServers(ctx, param, proxyVars); err != nil {
		return nil, err
	}
	if err := FillProxyAuth(ctx, param, proxyVars); err != nil {
		return nil, err
	}
	if err := FillProxyTlsConfigs(ctx, param, proxyVars); err != nil {
		return nil, err
	}
	if err := FillProxyUserConfs(ctx, param, proxyVars); err != nil {
		return nil, err
	}
	if err := FillProxyWhiteIps(ctx, param, proxyVars); err != nil {
		return nil, err
	}
	return proxyVars, nil
}

func FillProxyStartCmd(ctx context.Context, param *Parameter, proxyVars map[string]string) error {
	proxyVars["supervise_path"] = fmt.Sprintf("%s/supervise.centos", param.Proxy.Basedir)
	proxyVars["supervise_status_dir"] = fmt.Sprintf("%s/status/nutcracker", param.Proxy.Basedir)
	proxyVars["nutcracker_path"] = fmt.Sprintf("%s/agent/bin/nutcracker", param.Proxy.Basedir)
	proxyVars["nutcracker_conf"] = fmt.Sprintf("%s/agent/conf/nutcracker_.yml", param.Proxy.Basedir)
	proxyVars["nutcracker_log_dir"] = fmt.Sprintf("/mnt/log")
	proxyVars["nutcracker_log"] = fmt.Sprintf("/mnt/log/nutcracker.log")
	proxyVars["agent_log_dir"] = fmt.Sprintf("%s/agent/log", param.Proxy.Basedir)
	proxyVars["agent_log"] = fmt.Sprintf("%s/agent/log/nutcracker.log", param.Proxy.Basedir)
	proxyVars["meta_port"] = strconv.Itoa(param.MetaServer.Port)
	proxyVars["meta_ip"] = param.MetaServer.Ip
	proxyVars["whitelist_prefix"] = fmt.Sprintf("%s/agent/conf/whitelist", param.Proxy.Basedir)
	proxyVars["user_conf_path"] = fmt.Sprintf("%s/agent/conf/user.acl", param.Proxy.Basedir)
	// 这里设置默认值，如果OP修改了该配置，会在FillProxyUserConfs中覆盖
	proxyVars["proxy_mbuf_size"] = "4096"
	proxyVars["module_conf"] = fmt.Sprintf("%s/agent/conf/module.yml", param.Proxy.Basedir)
	proxyVars["stat_port"] = strconv.Itoa(param.Proxy.StatPort)
	return nil
}

func FillProxyServers(ctx context.Context, param *Parameter, proxyVars map[string]string) error {
	servers := make([]string, 0)
	for _, s := range param.GetProxyNodeInfos() {
		servers = append(servers, fmt.Sprintf("   - %s\n", s.ServerName))
		servers = append(servers, fmt.Sprintf("     %s\n", s.Node))
	}
	proxyVars["servers"] = strings.Join(servers, "")
	return nil
}

func FillProxyAuth(ctx context.Context, param *Parameter, proxyVars map[string]string) error {
	var err error
	if len(param.GetProxyPassword()) != 0 {
		proxyVars["client_auth"] = fmt.Sprintf("  client_auth: \"%s\"\n", param.GetProxyPassword())
	}
	userAclStrs := make([]string, 0)
	hasNoPasswordDefault := false
	for _, proxyAcl := range param.ProxyAcls {
		if proxyAcl.Status != x1model.ACLStatusInUse {
			continue
		}
		var password string
		if len(proxyAcl.Password) != 0 {
			password, err = crypto_utils.DecryptKey(proxyAcl.Password)
			if err != nil {
				return err
			}
		}
		// 除了default密码, 其他密码不能为空
		if len(password) == 0 && proxyAcl.AccountName != "default" {
			resource.LoggerTask.Warning(ctx, "no pass non default account is not allowed", logit.String("account", proxyAcl.AccountName))
			return fmt.Errorf("no pass non default account is not allowed, account: %s", proxyAcl.AccountName)
		}
		// 如果密码为空且账号是default
		if len(password) == 0 && proxyAcl.AccountName == "default" {
			hasNoPasswordDefault = true
			userAclStrs = append(userAclStrs, "user default on nopass +@all ~*")
			continue
		}
		AllowCmdsStr := ""
		if proxyAcl.AllowedCmds == "" {
			AllowCmdsStr = "+@all"
		} else {
			AllowCmds := strings.Split(proxyAcl.AllowedCmds, ";")
			for _, cmd := range AllowCmds {
				if len(cmd) != 0 {
					AllowCmdsStr += " "
				}
				AllowCmdsStr += "+" + cmd
			}
		}
		userAclStrs = append(userAclStrs, fmt.Sprintf("user %s on >%s %s ~*", proxyAcl.AccountName, password, AllowCmdsStr))
	}
	if len(userAclStrs) > 1 && hasNoPasswordDefault {
		resource.LoggerTask.Warning(ctx, "default user with no password is not allowed when there are other users with password")
		return fmt.Errorf("default user with no password is not allowed when there are other users with password")
	}
	if len(userAclStrs) == 0 {
		resource.LoggerTask.Notice(ctx, "no proxy acls found, add default user")
		userAclStrs = append(userAclStrs, "user default on nopass +@all ~*")
	}
	proxyVars["user_acls"] = strings.Join(userAclStrs, "\n")
	return nil
}

func FillProxyUserConfs(ctx context.Context, param *Parameter, proxyVars map[string]string) error {
	var userConfRecords []string
	proxyVars["proxy_user_confs"] = ""
	confItem, err := util.GetUpdateConfigList(ctx, &util.GetUpdateConfigParams{
		Engine:             param.EngineType,
		ConfDefs:           param.ConfDefs,
		ConfRecords:        param.ConfRecords,
		UpdateConfigParams: nil,
		NeedFilter:         false,
	})
	resource.LoggerTask.Trace(ctx, "get confItems for proxy succ",
		logit.String("engine", param.EngineType),
		logit.String("entity", param.Entity),
		logit.String("confItems", base_utils.Format(confItem)),
		logit.String("confRecords", base_utils.Format(param.ConfRecords)))
	if err != nil {
		return err
	}
	for _, item := range confItem {
		varName := strings.ReplaceAll(item.Name, "-", "_")
		if _, has := proxyVars[varName]; has {
			proxyVars[varName] = item.Value
			if item.Value == "" {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("proxy conf %s is empty, will cause proxy crash", item.Name))
				return fmt.Errorf("proxy conf %s is empty, will cause proxy crash", item.Name)
			}
		} else {
			if item.Value != "" {
				userConfRecords = append(userConfRecords, fmt.Sprintf("  %s: %s", item.Name, item.Value))
			}
		}
	}
	if len(userConfRecords) > 0 {
		proxyVars["proxy_user_confs"] = strings.Join(userConfRecords, "\n") + "\n"
	}
	return nil
}

func FillProxyWhiteIps(ctx context.Context, param *Parameter, proxyVars map[string]string) error {
	proxyVars["whitelist_ip"] = ""
	proxyVars["whitelist_cidr"] = ""
	for _, whiteIP := range param.ProxyWhiteIps {
		if strings.Contains(whiteIP.IP, "/") {
			proxyVars["whitelist_cidr"] += fmt.Sprintf("%s %s\n", whiteIP.IP, whiteIP.Mode)
		} else {
			if !isValidIP(whiteIP.IP) {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("invalid whitelist ip: %s", whiteIP.IP))
				continue
			}
			proxyVars["whitelist_ip"] += fmt.Sprintf("%s %s\n", whiteIP.IP, whiteIP.Mode)
		}
	}
	return nil
}

func FillProxyTlsConfigs(ctx context.Context, param *Parameter, proxyVars map[string]string) error {
	// 获取集群tls信息
	proxyVars["tls_key_file"] = ""
	proxyVars["tls_cert_file"] = ""
	if param.TlsEnabled {
		proxyVars["tls_key_file"] = "  tls_key_file: \"/root/agent/conf/tls.key\"\n"
		proxyVars["tls_cert_file"] = "  tls_cert_file: \"/root/agent/conf/tls.crt\"\n"
		proxyVars["tls_key"] = param.TlsKey
		proxyVars["tls_crt"] = param.TlsCert
	}
	return nil
}

func isValidIP(ip string) bool {
	if ip == "*.*.*.*" {
		return true
	}
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}
	return true
}
