package getvars

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func GetPegaVars(ctx context.Context, param *Parameter) (map[string]string, error) {
	r := make(map[string]string)
	if err := FillPegaBaseInfo(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillPegaConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillUserConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillRedisRenameCommands(ctx, param, r); err != nil {
		return nil, err
	}
	return r, nil
}

func FillPegaBaseInfo(ctx context.Context, param *Parameter, pegaVars map[string]string) error {
	pegaVars["port"] = strconv.Itoa(param.Node.Port)
	pegaVars["pegadb_data_path"] = "/mnt"
	pegaVars["meta_master_ip"] = param.MetaServer.Ip
	pegaVars["meta_master_port"] = strconv.Itoa(param.MetaServer.Port)
	pegaVars["instance_id"] = strconv.FormatInt(param.CsmasterInstanceCurrent.Id, 10)
	pegaVars["shard_id"] = strconv.Itoa(param.CsmasterInstanceCurrent.ShardId)
	pegaVars["cluster_id"] = strconv.FormatInt(param.CsmasterModel.Id, 10)
	if len(param.App.AppGroupID) != 0 {
		pegaVars["instance_id"] = strconv.Itoa(param.Node.GlobalSeqID)
		pegaVars["shard_id"] = strconv.Itoa(param.Cluster.GlobalSeqID)
		pegaVars["cluster_id"] = strconv.Itoa(param.App.AppGroupSeqID)
	}
	pegaVars["g_shard_id"] = strconv.Itoa(param.GetGShardID())
	// Pega不同于Redis，Redis有单独的g_shard_id配置项，而Pega只有shard_id一个配置项，与内核同学（caipengbo）沟通后确认，
	// pega多活场景下，需要把shard_id覆盖成g_shard_id
	if param.App.SyncGroupID != "" {
		resource.LoggerTask.Trace(ctx, "this is a sync group instance,change shard_id to g_shard_id",
			logit.String("g_shard_id", pegaVars["g_shard_id"]),
			logit.String("shard_id", pegaVars["shard_id"]))
		pegaVars["shard_id"] = pegaVars["g_shard_id"]
	}
	pegaVars["kvrocks_bin_path"] = fmt.Sprintf("%s/agent/bin/kvrocks", param.Node.Basedir)
	pegaVars["kvrocks_conf_path"] = fmt.Sprintf("%s/agent/conf/kvrocks_.conf", param.Node.Basedir)

	pegaVars["resource_type"] = param.CsmasterModel.Resource_type
	return nil
}

func FillPegaConf(ctx context.Context, param *Parameter, pegaVars map[string]string) error {
	pegaVars["max_write_buffer_number"] = strconv.Itoa(param.GetPegaMaxWriteBufferNumber())
	pegaVars["write_buffer_size"] = strconv.Itoa(param.GetPegaWriteBufferSize())
	pegaVars["metadata_block_cache_size"] = strconv.Itoa(param.GetPegaMetadataBlockCacheSize())
	pegaVars["subkey_block_cache_size"] = pegaVars["metadata_block_cache_size"]
	pegaVars["max_db_size"] = strconv.Itoa(param.Cluster.AvailableVolume)
	pegaVars["workers"] = strconv.Itoa(param.GetPegaWorkers())
	pegaVars["wal_size_limit_mb"] = strconv.Itoa(param.GetPegaWalSizeLimitMB())
	pegaVars["max_replication_mb"] = strconv.Itoa(param.GetPegaMaxReplicationMB())
	pegaVars["use_rsid_psync"] = param.GetConfValue("use-rsid-psync", "redis", "no")
	pegaVars["pegadb_slowlog_log_slower_than"] = param.GetConfValue("slowlog-log-slower-than", "redis", "200000")
	pegaVars["use_op_header"] = param.GetPegaUseOpHeader()
	return nil
}

func FillUserConf(ctx context.Context, param *Parameter, pegaVars map[string]string) error {
	pegaVars["databases"] = ""
	if param.GetConfValue("pega_databases_256", "redis", "") == "yes" {
		pegaVars["databases"] = fmt.Sprintf("databases 256")
	}
	pegaVars["compaction_checker_range"] = "\"\""
	var userConfRecords []string
	confItem, err := util.GetUpdateConfigList(ctx, &util.GetUpdateConfigParams{
		Engine:             param.EngineType,
		EngineVersion:      param.Cluster.EngineVersion,
		Role:               param.Node.Role,
		ConfDefs:           param.ConfDefs,
		ConfRecords:        param.ConfRecords,
		UpdateConfigParams: nil,
		NeedFilter:         false,
	})
	resource.LoggerTask.Trace(ctx, "get confItems for redis succ",
		logit.String("engine", param.EngineType),
		logit.String("engineVersion", param.Cluster.EngineVersion),
		logit.String("entity", param.Entity),
		logit.String("confItems", base_utils.Format(confItem)),
		logit.String("confDefs", base_utils.Format(param.ConfDefs)),
		logit.String("confRecords", base_utils.Format(param.ConfRecords)))
	if err != nil {
		return err
	}
	for _, item := range confItem {
		item := item
		if item.Name == "disable_commands_pega" {
			resource.LoggerTask.Trace(ctx, "disable_comamnd_pega no need to add raw conf into conf file,skip")
			continue
		}

		varName := strings.ReplaceAll(item.Name, "-", "_")
		if _, has := pegaVars[varName]; has {
			pegaVars[varName] = func() string {
				if item.Value == "" {
					return "\"\""
				}
				return item.Value
			}()
		} else {
			userConfRecords = append(userConfRecords, fmt.Sprintf("%s %s", item.Name, func() string {
				if item.Value == "" {
					return "\"\""
				}
				return item.Value
			}()))
		}
	}
	if len(userConfRecords) > 0 {
		pegaVars["user_conf_records"] = strings.Join(userConfRecords, "\n") + "\n"
	}
	return nil
}
