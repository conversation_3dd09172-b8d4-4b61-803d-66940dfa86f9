package getvars

import (
	"context"
	"fmt"
	"strconv"
	"strings"
)

func GetSyncAgentVars(ctx context.Context, param *Parameter) (map[string]string, error) {
	r := make(map[string]string)
	if err := FillSyncAgentBaseConf(ctx, param, r); err != nil {
		return nil, err
	}
	return r, nil
}

func FillSyncAgentBaseConf(ctx context.Context, param *Parameter, syncAgentVars map[string]string) error {
	syncAgentVars["support_multi_active"] = param.GetConfValue("support_multi_active", "redis", "no")
	syncAgentVars["cluster_show_id"] = param.App.AppId
	syncAgentVars["host"] = "127.0.0.1"
	syncAgentVars["port"] = strconv.Itoa(param.Node.Port)
	syncAgentVars["ma_meta_ip"] = param.GetDataserverIP()
	syncAgentVars["ma_meta_port"] = strconv.Itoa(param.GetDataserverPort())
	syncAgentVars["ma_meta_auth"] = param.GetDataserverPassword()
	syncAgentVars["redis_id"] = strconv.Itoa(param.GetGShardID())
	syncAgentVars["data_dir_prefix"] = param.GetSyncAgentDataPathPrefix()
	syncAgentVars["data_dir_suffix"] = param.GetSyncAgentDataPathSuffix()
	syncAgentVars["log_dir"] = param.GetSyncAgentLogDir()
	syncAgentVars["rename_str"] = param.GetSyncAgentRenameConf()
	syncAgentVars["dst_proxy_auth"] = param.GetProxyPassword()
	syncAgentVars["server_name"] = param.GetSyncAgentServerName()
	syncAgentVars["server_type"] = param.GetSyncAgentServerType()
	syncAgentVars["user_filter_cmds"] = param.GetSyncAgentUserFilterCmds()
	syncAgentVars["support_second_exec"] = param.GetSyncAgentSupportSecondExec()
	syncAgentVars["supervise_path"] = param.Node.Basedir + "/supervise.centos"
	syncAgentVars["supervise_status_dir"] = param.Node.Basedir + "/status/sync_agent"
	syncAgentVars["sync_agent_path"] = param.Node.Basedir + "/agent/bin/sync-agent"
	syncAgentVars["sync_agent_conf"] = param.Node.Basedir + "/agent/conf/sync_.conf"
	if param.App.SyncAgentBigver == "2.0" {
		// 新版sync-agent配置文件是yml格式
		syncAgentVars["sync_agent_conf"] = param.Node.Basedir + "/agent/conf/sync_.yml"
		if err := FillSyncAgentUserConf(ctx, param, syncAgentVars); err != nil {
			return err
		}
	}
	return nil
}

func FillSyncAgentUserConf(ctx context.Context, param *Parameter, syncAgentVars map[string]string) error {
	syncAgentVars["pipeline"] = param.GetMetaConfValue("syncagent-pipeline", "128")
	syncAgentVars["timeout"] = param.GetMetaConfValue("syncagent-timeout", "3")
	syncAgentVars["channel_num"] = param.GetMetaConfValue("syncagent-channel-num", "7")
	syncAgentVars["channel_size"] = param.GetMetaConfValue("syncagent-channel-size", "1024")
	syncAgentVars["key_prefixes"] = param.GetMetaConfValue("syncagent-key-prefixes", "DCS_CACHE_,CACHE_,BDRP_STATICS_ONLINE_QA_TEST,BDRP_SYNC_")
	var discardRules string
	discardRulesConf := param.GetMetaConfValue("syncagent-discard-rules", "")
	if discardRulesConf != "" {
		for _, rule := range strings.Split(discardRulesConf, ";") {
			ruleKey := strings.Split(rule, "=")[0]
			ruleVal := strings.Split(rule, "=")[1]
			if discardRules == "" {
				discardRules = "\n    discard-rules:"
			}
			discardRules += "\n          - keys-pattern:"
			discardRules += fmt.Sprintf("\n            command: \"%s\"", ruleKey)
			discardRules += fmt.Sprintf("\n            error-message: \"%s\"", ruleVal)
		}
	}
	return nil
}
