package getvars

import (
	"context"
	"strconv"

	"icode.baidu.com/baidu/scs/x1-base/component/opmonitor"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
)

func GetRedisConf(ctx context.Context, param *Parameter) (*RedisConf, error) {
	disabledCommands, err := param.GetDisabledCommands(ctx)
	if err != nil {
		return nil, err
	}
	r := &RedisConf{
		CPUNum:                  param.GetWorkingCpuNum(),
		DatabaseEnableOnLoading: param.GetDatabaseEnableOnLoading(),
		DbSlotCheck:             param.GetDbSlotCheck(),
		DisabledCommand:         disabledCommands,
		DiskFlavor:              param.GetPegaDiskFlavor(),
		GShardID:                param.GetGShardID(),
		InstanceType:            param.GetInstanceType(),
		IsReadonly:              param.CsmasterInstanceCurrent.IsReadonly,
		MaMetaAuth:              param.GetDataserverPassword(),
		MaMetaIP:                param.GetDataserverIP(),
		MaMetaPort:              param.GetDataserverPort(),
		MasterIP:                param.GetMasterIp(),
		MasterMaxMemory:         param.GetMaxMemory(),
		MasterMaxMemoryPolicy:   param.GetMaxMemoryPolicy(),
		MasterPort:              param.GetMasterPort(),
		MasterSlave:             param.GetMasterSlaveFlag(),
		MaxWriteBufferNumber:    param.GetPegaMaxWriteBufferNumber(), // 仅PegaDB使用
		MetaMasterIP:            param.MetaServer.Ip,
		MetaMasterPort:          param.MetaServer.Port,
		MetadataBlockCacheSize:  param.GetPegaMetadataBlockCacheSize(),
		NodeShowID:              param.Node.NodeFixID,
		PersistType:             int(param.CsmasterModel.Persistence.Int32),
		Port:                    param.Node.Port,
		ProxyAuth:               param.GetProxyPassword(),
		RedisClusterID:          param.App.AppShortID,
		RedisInstanceID:         param.Node.NodeShortID,
		RedisShardID:            param.Cluster.ClusterShortID,
		RedisVersion:            param.GetRedisVersion(),
		ShowID:                  param.App.AppId,
		SubkeyBlockCacheSize:    param.GetPegaMetadataBlockCacheSize(),
		SupportMultiActive:      param.GetConfValue("support_multi_active", "redis", ConfDefDefault),
		SupportSecondExec:       param.GetSyncAgentSupportSecondExec(),
		UseHashTag:              param.GetConfValue("hashtag_enable", "redis", ConfDefDefault),
		UserFilterCmds:          param.GetConfValue("user_filter_cmds", "redis", ConfDefDefault),
		WriteBufferSize:         param.GetPegaWriteBufferSize(), // 仅PegaDB使用
	}
	return r, nil
}

func GetProxyRecoverConf(ctx context.Context, param *Parameter) (*ProxyConf, error) {
	r := &ProxyConf{
		AppID:               param.App.AppShortID,
		ClusterEntry:        param.GetClusterEntry(),
		ConnNum:             param.GetProxyServerConnectionCount(),
		DisabledCommand:     []string{},
		Engine:              1,
		InstanceType:        0,
		IPtag:               param.GetIpTags(),
		MbufSize:            param.GetMBufSize(),
		McpackPort:          param.Proxy.McpackPort,
		NodeInfo:            param.GetProxyNodeInfos(),
		NodeShowID:          param.Proxy.NodeFixID,
		PoolName:            param.CsmasterModel.PoolName,
		Port:                param.Proxy.Port,
		ProxyID:             param.App.AppShortID,
		ProxyInstanceID:     param.Proxy.ProxyShortID,
		ProxyMetaMasterIP:   param.MetaServer.Ip,
		ProxyMetaMasterPort: param.MetaServer.Port,
		ProxyVersion:        1,
		QPS:                 200000,
		SentinelIP:          "",
		SentinelPort:        0,
		StatPort:            param.Proxy.StatPort,
		StoreType:           param.CsmasterModel.StoreType,
		WhiteIPList:         param.ProxyWhiteIps,
	}
	return r, nil
}

func GetAuthConf(ctx context.Context, param *Parameter) (*Auth, error) {
	redisPw, err := param.GetRedisDefaultPassword()
	if err != nil {
		return nil, err
	}
	r := &Auth{
		ClientAuth: param.GetProxyPassword(),
		RedisAuth:  redisPw,
		MetaAuth:   "",
		AuthSign:   0,
	}
	return r, nil
}

func GetInstanceMetadata(ctx context.Context, param *Parameter) (*InstanceMetadata, error) {
	r := &InstanceMetadata{
		Port: func() int {
			if param.Node != nil {
				return param.Node.Port
			} else if param.Proxy != nil {
				return param.Proxy.Port
			}
			return 0
		}(),
		Type: func() string {
			if param.EngineType == x1model.EngineRedis || param.EngineType == x1model.EnginePegaDB {
				if param.Node.Role == "master" {
					return "redis-master"
				}
				return "redis-slave"
			} else if param.EngineType == x1model.EngineBDRPProxy {
				return "proxy"
			}
			return ""
		}(),
		ProcName: func() string {
			switch param.EngineType {
			case x1model.EngineRedis:
				return "redis-server"
			case x1model.EnginePegaDB:
				return "kvrocks"
			case x1model.EngineBDRPProxy:
				return "nutcracker"
			default:
				return ""
			}
		}(),
		Engine:        1,
		KernelVersion: param.CsmasterModel.KernelVersion,
		ResourceType:  param.CsmasterModel.Resource_type,
	}
	return r, nil
}

func GetBcmConf(ctx context.Context, param *Parameter) (*BcmConf, error) {
	bcmPushCycle := 60
	if in, _ := base_utils.InArray(param.App.BcmCycle, []int{5, 10, 30, 60}); in {
		bcmPushCycle = param.App.BcmCycle
	}
	r := &BcmConf{
		Cycle:         bcmPushCycle,
		RefactoringV1: 1, // always 1
	}
	return r, nil
}

func GetMonitorConf(ctx context.Context, param *Parameter) (*Monitor, error) {
	opMonitor := opmonitor.Instance()
	var noahBns string
	var err error
	if param.CsmasterInstanceCurrent.CacheInstanceType == 0 {
		noahBns = opMonitor.GenerateInterfaceBnsInstanceName(param.CsmasterModel.Id, param.Proxy.ProxyShortID)
	} else {
		noahBns, err = opMonitor.GenerateBackendBnsInstanceName(param.CsmasterModel.Id,
			param.Node.NodeShortID, param.Cluster.Engine)
		if err != nil {
			return nil, err
		}
	}
	keepFilesCount, err := strconv.Atoi(param.GetConfValue("consistency_check_keep_files", "redis", "3"))
	if err != nil {
		keepFilesCount = 3
	}
	r := &Monitor{
		HashName:     param.CsmasterInstanceCurrent.HashName,
		HashID:       param.CsmasterInstanceCurrent.HashId,
		Version:      param.CsmasterModel.Version,
		StoreType:    param.CsmasterModel.StoreType,
		NoahBns:      noahBns,
		NoahEndpoint: opMonitor.GetEndPoint(),
		ProxyInstanceID: func() int {
			if param.CsmasterInstanceCurrent.CacheInstanceType == 0 {
				return int(param.CsmasterInstanceCurrent.Id)
			}
			return 0
		}(),
		IsPrivate:                 privatecloud.IsPrivateENV(),
		PrivateType:               privatecloud.GetPrivateEnvType(),
		FixIP:                     param.CsmasterInstanceCurrent.FixIp,
		ConsistencyCheckAllowRun:  param.GetConfValue("consistency_check_allow_run", "redis", "no"),
		ConsistencyCheckKeepFiles: keepFilesCount,
	}
	return r, nil
}

func GetAgentRecover(ctx context.Context, param *Parameter) (*AgentRecovers, error) {
	r := &AgentRecovers{}
	var err error
	if param.EngineType == x1model.EngineRedis || param.EngineType == x1model.EnginePegaDB {
		redisConf, err := GetRedisConf(ctx, param)
		if err != nil {
			return nil, err
		}
		r.RedisConf = redisConf
	}
	if param.EngineType == x1model.EngineBDRPProxy {
		proxyConf, err := GetProxyRecoverConf(ctx, param)
		if err != nil {
			return nil, err
		}
		r.ProxyConf = proxyConf
	}
	r.Auth, err = GetAuthConf(ctx, param)
	if err != nil {
		return nil, err
	}
	r.EnableHotkey = &EnableHotkey{
		EnableHotkey: param.CsmasterModel.EnableHotkey,
	}
	r.EnableSlowLog = &EnableSlowLog{
		EnableSlowLogFlag: param.CsmasterModel.EnableSlowLog,
	}
	r.ID = &ID{
		UUID:      param.CsmasterInstanceCurrent.Uuid,
		UserID:    param.App.UserId,
		ClusterID: int(param.CsmasterModel.Id),
		NodeID:    param.CsmasterInstanceCurrent.HashId,
	}
	r.InstanceMetadata, err = GetInstanceMetadata(ctx, param)
	if err != nil {
		return nil, err
	}
	r.BcmConf, err = GetBcmConf(ctx, param)
	if err != nil {
		return nil, err
	}
	r.Blb = &Blb{
		ElbIP:   param.CsmasterModel.ElbPnetip.String,
		ElbPort: int(param.CsmasterModel.BlbListenerPort.Int32),
	}
	r.Monitor, err = GetMonitorConf(ctx, param)
	if err != nil {
		return nil, err
	}
	return r, nil
}
