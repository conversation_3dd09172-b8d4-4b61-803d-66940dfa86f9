package getvars

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func GetRedisVar(ctx context.Context, param *Parameter) (map[string]string, error) {
	r := make(map[string]string)
	if err := FillRedisBaseConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillReplConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillPersistenceConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillSpecConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillRedisAuth(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillRedisModulesConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillActiveDefragConf(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillRedisRenameCommands(ctx, param, r); err != nil {
		return nil, err
	}
	if err := FillRedisUserConf(ctx, param, r); err != nil {
		return nil, err
	}
	return r, nil
}

func FillRedisBaseConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	redisVars["port"] = strconv.Itoa(param.Node.Port)
	// redisVars["deploy_path"] = param.Node.Basedir + "/redis"
	// redisVars["data_path"] = "/mnt"
	redisVars["dir"] = fmt.Sprintf("/mnt/data/redis_%d", param.Node.Port)
	redisVars["acl_file"] = param.Node.Basedir + "/agent/conf/user.acl"
	redisVars["log_file"] = param.GetRedisLogFile()
	redisVars["log_dir"] = param.GetRedisLogDir()
	redisVars["g_shard_id"] = strconv.Itoa(param.GetGShardID())
	redisVars["shard_id"] = strconv.Itoa(param.GetShardID())
	redisVars["cluster_id"] = strconv.FormatInt(param.CsmasterModel.Id, 10)
	redisVars["instance_id"] = strconv.FormatInt(param.CsmasterInstanceCurrent.Id, 10)
	if len(param.App.AppGroupID) != 0 {
		redisVars["shard_id"] = strconv.Itoa(param.Cluster.GlobalSeqID)
		redisVars["cluster_id"] = strconv.Itoa(param.App.AppGroupSeqID)
		redisVars["instance_id"] = strconv.Itoa(param.Node.GlobalSeqID)
	}
	redisVars["meta_master_ip"] = param.MetaServer.Ip
	redisVars["meta_master_port"] = strconv.Itoa(param.MetaServer.Port)
	redisVars["redis_bin_path"] = fmt.Sprintf("%s/agent/bin/redis-server", param.Node.Basedir)
	redisVars["redis_conf_path"] = fmt.Sprintf("%s/agent/conf/redis_.conf", param.Node.Basedir)

	redisVars["resource_type"] = param.CsmasterModel.Resource_type

	return nil
}

func FillReplConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	// Redis slave与主节点的探活超时时间从9s改成15s, 因为DTS的探活间隔为10s
	// redisVars["repl_timeout"] = "9"
	redisVars["repl_timeout"] = "15"
	if strings.HasPrefix(param.CsmasterModel.KernelVersion, "3") {
		redisVars["repl_timeout"] = "60"
	}
	if param.Cluster.AvailableVolume <= 2 {
		redisVars["repl_backlog_size"] = "64mb"
		redisVars["client_output_buffer_for_replica"] = "256mb 128mb 60"
	} else if param.Cluster.AvailableVolume <= 4 {
		redisVars["repl_backlog_size"] = "64mb"
		redisVars["client_output_buffer_for_replica"] = "512mb 256mb 60"
	} else if param.Cluster.AvailableVolume <= 8 {
		redisVars["repl_backlog_size"] = "128mb"
		redisVars["client_output_buffer_for_replica"] = "1024mb 512mb 600"
	} else if param.Cluster.AvailableVolume <= 32 {
		redisVars["repl_backlog_size"] = "256mb"
		redisVars["client_output_buffer_for_replica"] = "2048mb 1024mb 600"
	} else {
		redisVars["repl_backlog_size"] = "512mb"
		redisVars["client_output_buffer_for_replica"] = "4096mb 2048mb 600"
	}
	return nil
}

func FillPersistenceConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	redisVars["save_mode"] = "save \"\""
	redisVars["appendonly"] = param.GetRedisAppendOnly()
	redisVars["appendfsync"] = param.GetRedisAppendfsync()
	redisVars["auto_aof_rewrite_percentage"] = param.GetRedisAutoAofRewritePercentage()
	redisVars["use_op_header"] = param.GetRedisUseOpHeader()
	redisVars["aof_shift"] = param.GetRedisAofShift()
	redisVars["aof_shift_size"] = param.GetRedisAofShiftSize()
	redisVars["aof_retain_size"] = param.GetRedisAofRetainSize()
	redisVars["aof_retain_file_count"] = strconv.FormatInt(param.Cluster.DiskSize, 10)
	redisVars["aof_shift_retain_size"] = strconv.FormatInt(param.Cluster.DiskSize*1024*1024*1024/2, 10)
	redisVars["increment_save_maxmemory"] = strconv.Itoa(param.GetMaxMemory() / 10)
	return nil
}

func FillActiveDefragConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	redisVars["activedefrag"] = "no"
	redisVars["active_defrag_ignore_bytes"] = "100mb"
	redisVars["active_defrag_threshold_lower"] = "10"
	redisVars["active_defrag_threshold_upper"] = "30"
	redisVars["active_defrag_cycle_min"] = "1"
	redisVars["active_defrag_cycle_max"] = "25"
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "6.0" {
		redisVars["activedefrag"] = "yes"
		redisVars["active_defrag_cycle_min"] = "25"
		redisVars["active_defrag_cycle_max"] = "75"
	}
	return nil
}

func FillSpecConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	redisVars["maxmemory"] = strconv.Itoa(param.GetMaxMemory())
	redisVars["io_threads"] = strconv.Itoa(param.GetWorkingCpuNum())
	redisVars["jemalloc_bg_thread"] = "no"
	if (strings.HasPrefix(param.CsmasterModel.KernelVersion, "6") ||
		strings.HasPrefix(param.CsmasterModel.KernelVersion, "7")) && param.GetWorkingCpuNum() > 2 {
		redisVars["io_threads_do_reads"] = "yes"
	} else {
		redisVars["io_threads_do_reads"] = "no"
	}
	if param.App.ResourceType == "container" {
		redisVars["server_cpulist"] = ""
		redisVars["bio_cpulist"] = "bio_cpulist 2-6"
		redisVars["aof_rewrite_cpulist"] = "aof_rewrite_cpulist 2-6"
		redisVars["bgsave_cpulist"] = "bgsave_cpulist 2-6"
	} else {
		redisVars["bio_cpulist"] = "bio_cpulist 0"
		redisVars["aof_rewrite_cpulist"] = "aof_rewrite_cpulist 0"
		redisVars["bgsave_cpulist"] = "bgsave_cpulist 0"
		if param.GetWorkingCpuNum() == 1 {
			redisVars["server_cpulist"] = "server_cpulist 1"
		} else if param.GetWorkingCpuNum() > 1 {
			redisVars["server_cpulist"] = "server_cpulist 1-" + strconv.Itoa(param.GetWorkingCpuNum())
		} else {
			redisVars["server_cpulist"] = "server_cpulist 0"
		}
	}
	return nil
}

func FillRedisAuth(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	defaultAcl := "user default on nopass +@all &* ~*"
	redisVars["requirepass"] = "# requirepass nopass"
	redisVars["masterauth"] = "# masterauth nopass"
	// 集群版不设置密码
	if param.App.Type == x1model.AppTypeCluster {
		redisVars["user_acls"] = defaultAcl
		return nil
	}
	pw, err := param.GetRedisDefaultPassword()
	if err != nil {
		return err
	}
	if len(pw) != 0 {
		redisVars["requirepass"] = fmt.Sprintf("requirepass %s", pw)
		redisVars["masterauth"] = fmt.Sprintf("masterauth %s", pw)
	}
	userAclStrs := make([]string, 0)
	for _, redisAcl := range param.RedisAcls {
		if redisAcl.Status != x1model.ACLStatusInUse {
			continue
		}
		var password string
		if len(redisAcl.Password) != 0 {
			password, err = crypto_utils.DecryptKey(redisAcl.Password)
			if err != nil {
				return err
			}
		}
		if len(password) == 0 && redisAcl.AccountName == "default" {
			password = "nopass"
		}
		// 不支持除了default账号以外的账号不设置密码
		if len(password) == 0 {
			continue
		}
		if password != "nopass" {
			password = fmt.Sprintf(">%s", password)
		}
		AllowCmdsStr := ""
		if redisAcl.AllowedCmds == "" {
			AllowCmdsStr = "+@all"
		} else {
			AllowCmds := strings.Split(redisAcl.AllowedCmds, ";")
			for _, cmd := range AllowCmds {
				if cmd == "" {
					continue
				}
				if len(AllowCmdsStr) != 0 {
					AllowCmdsStr += " "
				}
				AllowCmdsStr += "+" + cmd
			}
		}
		userAclStrs = append(userAclStrs, fmt.Sprintf("user %s on %s %s &* ~*", redisAcl.AccountName, password, AllowCmdsStr))
	}
	if len(userAclStrs) == 0 {
		userAclStrs = append(userAclStrs, defaultAcl)
	}
	redisVars["user_acls"] = strings.Join(userAclStrs, "\n") + "\n"
	return nil
}

func FillRedisUserConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	redisVars["max_memory_policy"] = "volatile-ttl"
	redisVars["database_enable_on_loading"] = param.GetDatabaseEnableOnLoading()
	redisVars["use_hash_tag"] = "no"
	redisVars["db_slot_check"] = "no"
	redisVars["user_conf_records"] = ""
	var userConfRecords []string
	confItem, err := util.GetUpdateConfigList(ctx, &util.GetUpdateConfigParams{
		Engine:             param.EngineType,
		EngineVersion:      param.Cluster.EngineVersion,
		Role:               param.Node.Role,
		ConfDefs:           param.ConfDefs,
		ConfRecords:        param.ConfRecords,
		UpdateConfigParams: nil,
		NeedFilter:         false,
	})
	resource.LoggerTask.Trace(ctx, "get confItems for redis succ",
		logit.String("engine", param.EngineType),
		logit.String("engineVersion", param.Cluster.EngineVersion),
		logit.String("entity", param.Entity),
		logit.String("confItems", base_utils.Format(confItem)),
		logit.String("confDefs", base_utils.Format(param.ConfDefs)),
		logit.String("confRecords", base_utils.Format(param.ConfRecords)))
	if err != nil {
		return err
	}
	for _, item := range confItem {
		item := item
		varName := strings.ReplaceAll(item.Name, "-", "_")
		if _, has := redisVars[varName]; has {
			redisVars[varName] = func() string {
				if item.Value == "" {
					return "\"\""
				}
				return item.Value
			}()
		} else {
			userConfRecords = append(userConfRecords, fmt.Sprintf("%s %s", item.Name, func() string {
				if item.Value == "" {
					return "\"\""
				}
				return item.Value
			}()))
		}
	}
	if len(userConfRecords) > 0 {
		redisVars["user_conf_records"] = strings.Join(userConfRecords, "\n") + "\n"
	}
	return nil
}

func FillRedisRenameCommands(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	renameCmds := make([]string, 0)
	for _, renameCmd := range param.RenamedCommands {
		if renameCmd == "" {
			continue
		}
		if param.App.Type == x1model.AppTypeStandalone && strings.ToLower(renameCmd) == "config" {
			continue
		}
		if strings.Contains(renameCmd, "|") {
			for _, spName := range strings.Split(renameCmd, "|") {
				if len(spName) >= 0 {
					renameCmds = append(renameCmds, fmt.Sprintf("rename-command %s %s",
						spName, "aae420ac56ef116058218c11d8b35b30"+spName))
				}
			}
			continue
		}
		renameCmds = append(renameCmds, fmt.Sprintf("rename-command %s %s",
			renameCmd, "aae420ac56ef116058218c11d8b35b30"+renameCmd))
	}
	redisVars["rename_commands"] = strings.Join(renameCmds, "\n") + "\n"
	return nil
}

func FillRedisModulesConf(ctx context.Context, param *Parameter, redisVars map[string]string) error {
	modulesStr := param.GetConfValue("load-modules", "redis", "libcascad")
	modules := strings.Split(modulesStr, ",")
	ret := make([]string, 0)
	for _, module := range modules {
		ret = append(ret, fmt.Sprintf("loadmodule %s/agent/bin/modules/%s.so", param.Node.Basedir, module))
	}
	redisVars["load_modules"] = strings.Join(ret, "\n") + "\n"
	return nil
}
