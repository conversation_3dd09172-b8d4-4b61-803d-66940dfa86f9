package getvars

// EnableSlowLog
// configure.py init_enable_slowlog_file
type EnableSlowLog struct {
	EnableSlowLogFlag int `json:"enable_slow_log_flag"`
}

// EnableHotkey
// configure.py init_enable_hotkey_file
type EnableHotkey struct {
	EnableHotkey int `json:"enable_hotkey"`
}

// Auth
// change_auth.py set_auth_conf recover/auth.txt
type Auth struct {
	ClientAuth string `json:"client_auth"`
	RedisAuth  string `json:"redis_auth"`
	MetaAuth   string `json:"meta_auth"`
	AuthSign   int    `json:"auth_sign"`
}

type ID struct {
	UUID      string `json:"uuid"`
	UserID    string `json:"user_id"`
	ClusterID int    `json:"cluster_id"`
	NodeID    string `json:"node_id"`
}

// RedisConf
// recover/redis_conf.txt
type RedisConf struct {
	CPUNum                  int      `json:"cpu_num"`
	DatabaseEnableOnLoading string   `json:"database_enable_on_loading"`
	DbSlotCheck             string   `json:"db_slot_check"`
	DisabledCommand         []string `json:"disabled_command"`
	DiskFlavor              int      `json:"disk_flavor"`
	GShardID                int      `json:"g_shard_id"`
	InstanceType            int      `json:"instance_type"`
	IsReadonly              int      `json:"is_readonly"`
	MaMetaAuth              string   `json:"ma_meta_auth"`
	MaMetaIP                string   `json:"ma_meta_ip"`
	MaMetaPort              int      `json:"ma_meta_port"`
	MasterIP                string   `json:"master_ip"`
	MasterMaxMemory         int      `json:"master_max_memory"`
	MasterMaxMemoryPolicy   string   `json:"master_max_memory_policy"`
	MasterPort              int      `json:"master_port"`
	MasterSlave             int      `json:"master_slave"`
	MaxWriteBufferNumber    int      `json:"max_write_buffer_number"`
	MetaMasterIP            string   `json:"meta_master_ip"`
	MetaMasterPort          int      `json:"meta_master_port"`
	MetadataBlockCacheSize  int      `json:"metadata_block_cache_size"`
	NodeShowID              string   `json:"node_show_id"`
	PersistType             int      `json:"persist_type"`
	Port                    int      `json:"port"`
	ProxyAuth               string   `json:"proxy_auth"`
	RedisClusterID          int      `json:"redis_cluster_id"`
	RedisInstanceID         int      `json:"redis_instance_id"`
	RedisShardID            int      `json:"redis_shard_id"`
	RedisVersion            int      `json:"redis_version"`
	ShowID                  string   `json:"show_id"`
	SubkeyBlockCacheSize    int      `json:"subkey_block_cache_size"`
	SupportMultiActive      string   `json:"support_multi_active"`
	SupportSecondExec       string   `json:"support_second_exec"`
	UseHashTag              string   `json:"use_hash_tag"`
	UserFilterCmds          string   `json:"user_filter_cmds"`
	WriteBufferSize         int      `json:"write_buffer_size"`
}

type NodeInfo struct {
	Node       string `json:"node"`
	NodeID     string `json:"node_id"`
	ServerName string `json:"server_name"`
	UUID       string `json:"uuid"`
}

// ProxyConf
// recover/proxy_conf.txt
type ProxyConf struct {
	AppID               int             `json:"appid"`
	ClusterEntry        string          `json:"cluster_entry"`
	ConnNum             int             `json:"conn_num"`
	DisabledCommand     []string        `json:"disabled_command"`
	Engine              int             `json:"engine"`
	InstanceType        int             `json:"instance_type"`
	IPtag               int             `json:"ip_tag"`
	MbufSize            int             `json:"mbuf_size"`
	McpackPort          int             `json:"mcpack_port"`
	NodeInfo            []*NodeInfo     `json:"node_info"`
	NodeShowID          string          `json:"node_show_id"`
	PoolName            string          `json:"pool_name"`
	Port                int             `json:"port"`
	ProxyID             int             `json:"proxy_id"`
	ProxyInstanceID     int             `json:"proxy_instance_id"`
	ProxyMetaMasterIP   string          `json:"proxy_meta_master_ip"`
	ProxyMetaMasterPort int             `json:"proxy_meta_master_port"`
	ProxyVersion        int             `json:"proxy_version"`
	QPS                 int             `json:"qps"`
	SentinelIP          string          `json:"sentinel_ip"`
	SentinelPort        int             `json:"sentinel_port"`
	StatPort            int             `json:"stat_port"`
	StoreType           int             `json:"store_type"`
	WhiteIPList         []*ProxyWhiteIp `json:"white_ip_list"`
}

type InstanceMetadata struct {
	Port          int    `json:"port"`
	Type          string `json:"type"`
	ProcName      string `json:"proc_name"`
	Engine        int    `json:"engine"`
	KernelVersion string `json:"kernel_version"`
	ResourceType  string `json:"resource_type"`
}

type BcmConf struct {
	Cycle         int `json:"cycle"`
	RefactoringV1 int `json:"refactoring_v1"`
}

type Blb struct {
	ElbIP   string `json:"elb_ip"`
	ElbPort int    `json:"elb_port"`
}

type Monitor struct {
	HashName                  string `json:"hash_name"`
	HashID                    string `json:"hash_id"`
	Version                   int    `json:"version"`
	StoreType                 int    `json:"store_type"`
	NoahBns                   string `json:"noah_bns"`
	NoahEndpoint              string `json:"noah_endpoint"`
	AllowPush                 bool   `json:"forbid_push"`
	ProxyInstanceID           int    `json:"proxy_instance_id"`
	IsPrivate                 bool   `json:"is_private"`
	PrivateType               string `json:"private_type"`
	FixIP                     string `json:"fix_ip"`
	ConsistencyCheckKeepFiles int    `json:"consistency_check_keep_files"`
	ConsistencyCheckAllowRun  string `json:"consistency_check_allow_run"`
}

type AgentRecovers struct {
	EnableSlowLog    *EnableSlowLog    `json:"enable_slow_log"`
	EnableHotkey     *EnableHotkey     `json:"hotkey"`
	Auth             *Auth             `json:"auth"`
	ID               *ID               `json:"id"`
	RedisConf        *RedisConf        `json:"redis_conf,omitempty"`
	ProxyConf        *ProxyConf        `json:"proxy_conf,omitempty"`
	InstanceMetadata *InstanceMetadata `json:"instance_metadata"`
	BcmConf          *BcmConf          `json:"bcm_conf"`
	Blb              *Blb              `json:"blb"`
	Monitor          *Monitor          `json:"monitor"`
}
