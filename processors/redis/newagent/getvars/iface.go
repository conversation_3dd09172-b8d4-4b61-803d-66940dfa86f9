package getvars

import "context"

type Vars struct {
	NodeType  string            `json:"node_type"` // 旧心跳中的.type memcached|redis|proxy|pegadb
	RedisVars map[string]string `json:"redis_vars"`
	ProxyVars map[string]string `json:"proxy_vars"`
}

// IDInfo 对应旧心跳中.Id

type ConfMetaItem struct {
	Name        string
	Tags        []*ConfMetaTag
	Type        string // single_select|number|multi_select
	Desc        string
	UserVisible bool
}

type ConfMetaTag struct {
	Key        string
	Tag        string
	DefaultVal any
	ValRange   string
	NeedReboot bool
}

type GetVarsIface interface {
	GetVars(ctx context.Context, appID string) (map[string]*Vars, error)
}
