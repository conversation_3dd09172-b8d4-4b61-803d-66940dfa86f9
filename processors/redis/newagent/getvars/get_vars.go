package getvars

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/gmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/neutron/security_group"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

const (
	ConfDefDefault = "#conf-def-default"
)

type Parameter struct {
	Entity                  string
	EntityType              string
	EngineType              string
	App                     *x1model.Application
	Cluster                 *x1model.Cluster
	Itf                     *x1model.Interface
	Node                    *x1model.Node
	Proxy                   *x1model.Proxy
	CsmasterModel           *csmaster_model_interface.CacheCluster
	CsmasterInstances       []*csmaster_model_interface.CacheInstance
	CsmasterInstanceCurrent *csmaster_model_interface.CacheInstance
	RedisAcls               []*x1model.RedisAcl
	ProxyAcls               []*x1model.ProxyAcl
	ProxyWhiteIps           []*ProxyWhiteIp
	ConfRecords             []*csmaster_model_interface.ConfRecordList
	ConfDefs                []*csmaster_model_interface.UserConfList
	MetaServer              *MetserverInfo
	GlobalRedisForAll       []*global_model.AppGroupRedis
	GlobalRedisForCurShard  []*global_model.AppGroupRedis
	PackageVersion          string
	RenamedCommands         []string
	TlsEnabled              bool
	TlsKey                  string
	TlsCert                 string
}

type ProxyWhiteIp struct {
	IP   string `json:"ip"`
	Mode string `json:"mode"`
}

type MetserverInfo struct {
	Ip   string
	Port int
	Auth string
}

func (param *Parameter) GetConfValue(key string, kernelType string, df string) string {
	confModule := 1 // redis
	if kernelType == "proxy" {
		confModule = 2
	}
	for _, record := range param.ConfRecords {
		if record.ConfName == key && (record.ConfModule == confModule || record.ConfModule == 3) {
			return record.Value
		}
	}
	if df == ConfDefDefault {
		for _, confDef := range param.ConfDefs {
			if confDef.ConfName == key && (confDef.ConfModule == confModule || confDef.ConfModule == 3) {
				return confDef.ConfDefault
			}
		}
		return ""
	}
	return df
}

func (param *Parameter) GetMetaConfValue(key string, df string) string {
	for _, record := range param.ConfRecords {
		if record.ConfName == key && record.ConfModule == 4 {
			return record.Value
		}
	}
	if df == ConfDefDefault {
		for _, confDef := range param.ConfDefs {
			if confDef.ConfName == key && confDef.ConfModule == 4 {
				return confDef.ConfDefault
			}
		}
		return ""
	}
	return df
}

func (param *Parameter) GetConfDef(key string, kernelType string) *csmaster_model_interface.UserConfList {
	confModule := 1 // redis
	if kernelType == "proxy" {
		confModule = 2
	}
	for _, confDef := range param.ConfDefs {
		if confDef.ConfName == key && confModule == confDef.ConfModule {
			return confDef
		}
	}
	return nil
}

func Instance() GetVarsIface {
	panic("implement me")
}

func (param *Parameter) GetDbSlotCheck() string {
	r := param.GetConfValue("redis-db-slot-check", "redis", ConfDefDefault)
	if len(r) == 0 {
		return "no"
	}
	return r
}

func (param *Parameter) GetDatabaseEnableOnLoading() string {
	r := param.GetConfValue("database-enable-on-loading", "redis", "yes")
	if len(r) == 0 {
		return "yes"
	}
	return r
}

func (param *Parameter) GetDisabledCommands(ctx context.Context) ([]string, error) {
	r, err := util.GetDisableCommands(ctx, param.App.AppId)
	if err != nil {
		return nil, err
	}
	sp := strings.Split(r, ",")
	if param.CsmasterModel.StoreType == 0 &&
		(strings.HasPrefix(param.CsmasterModel.KernelVersion, "6") ||
			strings.HasPrefix(param.CsmasterModel.KernelVersion, "7")) {
		sp = append(sp, "ACL")
	}
	return sp, nil
}

func (param *Parameter) GetGShardID() int {
	gShardID := conf.ScsMainConf.IDCCODE
	gShardID = gShardID << 32
	gShardID += int64(param.Cluster.ClusterShortID) & 0xFFFFFFFF
	return int(gShardID)
}

func (param *Parameter) GetShardID() int {
	if param.GetConfValue("g_shard_id_as_shard_id", "redis", "") == "yes" {
		return param.GetGShardID()
	}
	return param.CsmasterInstanceCurrent.ShardId
}

func (param *Parameter) GetPegaDiskFlavor() int {
	if param.App.Clusters[0].Engine != x1model.EnginePegaDB {
		return 0
	}
	return param.Cluster.AvailableVolume
}

func (param *Parameter) GetInstanceType() int {
	if param.Node != nil {
		if param.Node.Role == "master" {
			return 3
		}
		return 2
	}
	return 0
}

func (param *Parameter) getWorkingCpuNum() int {
	if param.App.Clusters[0].Engine == x1model.EnginePegaDB {
		return param.App.Clusters[0].Cpu
	}
	if strings.HasPrefix(param.CsmasterModel.KernelVersion, "6") || strings.HasPrefix(param.CsmasterModel.KernelVersion, "7") {
		cluster := param.App.Clusters[0]
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", param.App.AppId) {
			cluster = param.App.Clusters[1]
		}
		if param.App.ResourceType == "container" {
			return cluster.Cpu
		}
		return cluster.Cpu - 1
	}
	return 1
}

func (param *Parameter) GetWorkingCpuNum() int {
	workingCpuNum := param.getWorkingCpuNum()
	if workingCpuNum <= 0 {
		return 1
	}
	return workingCpuNum
}

func (param *Parameter) GetMasterIp() string {
	if len(param.App.AppGroupID) != 0 {
		for _, globalNode := range param.GlobalRedisForCurShard {
			if globalNode.Role == "master" {
				return globalNode.Ip
			}
		}
	}
	for _, node := range param.Cluster.Nodes {
		if node.Role == "master" && (node.Status == "inuse" || node.Status == param.Node.Status) {
			return node.Ip
		}
	}
	return ""
}

func (param *Parameter) GetMasterPort() int {
	if len(param.App.AppGroupID) != 0 {
		for _, globalNode := range param.GlobalRedisForCurShard {
			if globalNode.Role == "master" {
				return globalNode.Port
			}
		}
	}
	for _, node := range param.Cluster.Nodes {
		if node.Role == "master" && (node.Status == "inuse" || node.Status == param.Node.Status) {
			return node.Port
		}
	}
	return 0
}

func (param *Parameter) GetMaxMemory() int {
	if param.App.Clusters[0].Engine == x1model.EnginePegaDB {
		return 0
	}

	// build_meta 时，Spec 是空值，DestSpec 为 nodeType 值, callback 时 Spec 才会有值，所以这里不能使用 Spec
	if param.App.Clusters[0].DestSpec == "cache.n1.nano" {
		return 0.25 * 1024 * 1024 * 1024
	}
	return param.Cluster.AvailableVolume * 1024 * 1024 * 1024
}

func (param *Parameter) GetMaxMemoryPolicy() string {
	return param.GetConfValue("maxmemory-policy", "redis", ConfDefDefault)
}

func (param *Parameter) GetRedisDefaultPassword() (string, error) {
	for _, redisAcl := range param.RedisAcls {
		if redisAcl.AccountName == "default" && len(redisAcl.Password) != 0 && redisAcl.Status == "inuse" {
			password, err := crypto_utils.DecryptKey(redisAcl.Password)
			if err != nil {
				return "", err
			}
			return password, nil
		}
	}
	return "", nil
}

func (param *Parameter) GetPegaMetadataBlockCacheSize() int {
	pegaMem := 1024
	if param.Cluster.MemSize <= 4 {
		pegaMem = 512
	}
	reservedMem := 512
	if param.Cluster.MemSize <= 4 {
		reservedMem = 256
	}
	ret := (param.Cluster.MemSize*1024/2 - pegaMem - reservedMem) / 2
	if ret < 0 {
		ret = 128
	}
	return ret
}

func (param *Parameter) GetProxyPassword() string {
	for _, proxyAcl := range param.ProxyAcls {
		if proxyAcl.AccountName == "default" && len(proxyAcl.Password) != 0 && proxyAcl.Status == "inuse" {
			password, err := crypto_utils.DecryptKey(proxyAcl.Password)
			if err != nil {
				return ""
			}
			return password
		}
	}
	return ""
}

func (param *Parameter) GetRedisVersion() int {
	if param.App.Type == x1model.AppTypeCluster {
		return 1
	}
	if param.App.Type == x1model.AppTypeStandalone {
		return 3
	}
	return 0
}

func (param *Parameter) GetMasterSlaveFlag() int {
	if param.Node.Role == "master" {
		return 0
	}
	return 1
}

func (param *Parameter) GetClusterEntry() string {
	azResult := fmt.Sprintf("%s:%d", param.getClusterEntryIP(), param.CsmasterModel.BlbListenerPort.Int32)
	for _, b := range param.App.BLBs {
		if b.Status != x1model.BLBStatusAvailable {
			continue
		}
		if b.Type != x1model.BLBTypeApp {
			continue
		}
		if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
			return fmt.Sprintf("%s:%d", param.getClusterEntryIP(), param.CsmasterModel.BlbListenerPort.Int32)
		}
		if b.AzoneForCrossAzNearest == param.Proxy.Azone {
			if b.EndpointIp != "" {
				azResult = fmt.Sprintf("%s:%d", b.EndpointIp, param.CsmasterModel.BlbListenerPort.Int32)
			} else {
				azResult = fmt.Sprintf("%s:%d", b.Ovip, param.CsmasterModel.BlbListenerPort.Int32)
			}
		}
	}
	return azResult
}

func (param *Parameter) getClusterEntryIP() string {
	if privatecloud.IsPrivateENV() && param.CsmasterModel.ElbPnetip.String == "" {
		return "127.0.0.1"
	}
	ip := param.CsmasterModel.ElbPnetip.String
	if len(param.CsmasterModel.EndpointIp) != 0 {
		ip = param.CsmasterModel.EndpointIp
	}
	return ip
}

func (param *Parameter) GetPegaWorkers() int {
	return 3 * param.GetWorkingCpuNum()
}

func (param *Parameter) GetProxyServerConnectionCount() int {
	if param.App.Clusters[0].Engine != x1model.EnginePegaDB {
		return 16
	}
	connBase := 12
	shardCount := len(param.App.Clusters)
	r := connBase * (param.GetPegaWorkers() / param.GetWorkingCpuNum()) / shardCount
	if r <= 0 {
		r = 1
	}
	return r
}

func (param *Parameter) GetIpTags() int {
	r := 1
	if strings.Contains(param.App.IpType, "ipv6") {
		r = 3
	}
	return r
}

func (param *Parameter) GetMBufSize() int {
	minMbufSize := 512
	maxMbufSize := 16777216
	defaultMbufSize := 4096
	mbufSizeStr := param.GetConfValue("proxy_mbuf_size", "redis", ConfDefDefault)
	mbufSize, err := strconv.Atoi(mbufSizeStr)
	if err != nil || mbufSize < minMbufSize || mbufSize > maxMbufSize {
		return defaultMbufSize
	}
	return mbufSize
}

// GetProxyNodeInfos 获取代理节点信息列表，包括每个节点的IP、端口和资源ID等信息
// 参数：无
// 返回值：[]*NodeInfo，代理节点信息列表，每个元素都是一个指向NodeInfo结构体的指针
func (param *Parameter) GetProxyNodeInfos() []*NodeInfo {
	var nodeInfos []*NodeInfo
	isAppGroup := len(param.App.AppGroupID) != 0
	for _, cluster := range param.App.Clusters {
		// for modify type standalone to cluster
		if cluster.ClusterId == fmt.Sprintf("%s-0_todelete", param.App.AppId) {
			continue
		}
		for _, node := range cluster.Nodes {
			if node.Role == "master" || isAppGroup {
				nodeInfos = append(nodeInfos, &NodeInfo{
					Node: fmt.Sprintf("%s:%d:1", node.Ip, node.Port),
				})
				for _, cmInst := range param.CsmasterInstances {
					if cmInst.Uuid == node.ResourceId {
						nodeInfos[len(nodeInfos)-1].NodeID = cmInst.HashName
						nodeInfos[len(nodeInfos)-1].ServerName = cmInst.HashName
						nodeInfos[len(nodeInfos)-1].UUID = cmInst.HashId
						break
					}
				}
				break
			}
		}
	}
	return nodeInfos
}

func (param *Parameter) GetRedisLogFile() string {
	return fmt.Sprintf(param.GetRedisLogDir()+"/redis_%d.log", param.Node.Port)
}

func (param *Parameter) GetRedisLogDir() string {
	return param.Node.Basedir + "/agent/log"
}

func (param *Parameter) GetPegaMaxWriteBufferNumber() int {
	return 4
}

func (param *Parameter) GetPegaWriteBufferSize() int {
	return 64
}

func (param *Parameter) GetPegaWalSizeLimitMB() int {
	if param.Cluster.AvailableVolume > 200 {
		return 20480
	}
	return 10240
}

func (param *Parameter) GetPegaMaxReplicationMB() int {
	var maxReplicationMB int = 500
	var cpuNum int = param.GetWorkingCpuNum()
	if cpuNum <= 4 {
		maxReplicationMB = 187 / 2
	} else if cpuNum <= 8 {
		maxReplicationMB = 375 / 2
	} else if cpuNum <= 16 {
		maxReplicationMB = 750 / 2
	} else if cpuNum <= 24 {
		maxReplicationMB = 1000 / 2
	} else if cpuNum <= 32 {
		maxReplicationMB = 1500 / 2
	} else if cpuNum <= 48 {
		maxReplicationMB = 2000 / 2
	} else if cpuNum <= 64 {
		maxReplicationMB = 2750 / 2
	} else if cpuNum <= 112 {
		maxReplicationMB = 4375 / 2
	}
	return maxReplicationMB
}

func (param *Parameter) GetSyncAgentDataPathPrefix() string {
	switch param.App.Clusters[0].Engine {
	case x1model.EnginePegaDB:
		return "/mnt"
	case x1model.EngineRedis:
		return "/mnt/data"
	default:
		return "/mnt"
	}
}

func (param *Parameter) GetSyncAgentDataPathSuffix() string {
	switch param.App.Clusters[0].Engine {
	case x1model.EnginePegaDB:
		return "/data"
	case x1model.EngineRedis:
		return ""
	default:
		return ""
	}
}

func (param *Parameter) GetSyncAgentLogDir() string {
	return param.Node.Basedir + "/agent/log"
}

func (param *Parameter) GetSyncAgentRenameConf() string {
	return "aae420ac56ef116058218c11d8b35b30"
}

func (param *Parameter) GetSyncAgentSupportSecondExec() string {
	return param.GetConfValue("support_second_exec", "redis", ConfDefDefault)
}

func (param *Parameter) GetSyncAgentServerName() string {
	switch param.App.Clusters[0].Engine {
	case x1model.EnginePegaDB:
		return "kvrocks"
	case x1model.EngineRedis:
		return "redis"
	default:
		return ""
	}
}

func (param *Parameter) GetSyncAgentUserFilterCmds() string {
	userFilterCmds := param.GetConfValue("user_filter_cmds", "redis", ConfDefDefault)
	if len(userFilterCmds) > 0 {
		return "," + userFilterCmds
	}
	return ""
}

func (param *Parameter) GetSyncAgentServerType() string {
	switch param.App.Clusters[0].Engine {
	case x1model.EnginePegaDB:
		return "PegaDB2.0"
	case x1model.EngineRedis:
		return "redis"
	default:
		return ""
	}
}

func (param *Parameter) GetRedisAppendfsync() string {
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "4.0" {
		return "no"
	}
	return "everysec"
}

func (param *Parameter) GetRedisAutoAofRewritePercentage() string {
	r := "50"
	if param.Cluster != nil {
		if param.Cluster.AvailableVolume <= 2 {
			r = "200"
		} else if param.Cluster.AvailableVolume <= 4 {
			r = "100"
		}
	}
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "4.0" {
		r = "0"
	}
	if param.GetConfValue("support_multi_active", "redis", ConfDefDefault) == "yes" {
		r = "0"
	}
	return r
}

func (param *Parameter) GetRedisUseOpHeader() string {
	r := "no"
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "4.0" {
		r = "yes"
	}
	if param.GetConfValue("support_multi_active", "redis", ConfDefDefault) == "yes" {
		r = "yes"
	}
	return r
}

func (param *Parameter) GetRedisAofShift() string {
	r := "no"
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "4.0" {
		r = "yes"
	}
	if param.GetConfValue("support_multi_active", "redis", ConfDefDefault) == "yes" {
		r = "yes"
	}
	return r
}

func (param *Parameter) GetRedisAofShiftSize() string {
	r := "0"
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "4.0" {
		r = "512MB"
	}
	if param.GetConfValue("support_multi_active", "redis", ConfDefDefault) == "yes" {
		r = "512MB"
	}
	return r
}

func (param *Parameter) GetRedisAofRetainSize() string {
	r := "0"
	if param.App.Type == x1model.AppTypeCluster && param.App.Clusters[0].EngineVersion == "4.0" {
		r = "30"
	}
	return r
}

func (param *Parameter) GetRedisAppendOnly() string {
	r := "no"
	if param.GetConfValue("appendonly", "redis", ConfDefDefault) == "yes" {
		r = "yes"
	}
	if param.GetConfValue("support_multi_active", "redis", ConfDefDefault) == "yes" {
		r = "yes"
	}
	return r
}

func (param *Parameter) GetDataserverPassword() string {
	if param.App.DataserverType == "bdrp" {
		return conf.ScsMainConf.BDRPDataServerPassword
	}
	return conf.ScsMainConf.DataServerPassword
}

func (param *Parameter) GetDataserverIP() string {
	if param.App.DataserverType == "bdrp" {
		if conf.ScsMainConf.BDRPDataServerEntranceHostTrans != "" {
			return conf.ScsMainConf.BDRPDataServerEntranceHostTrans
		}
		return conf.ScsMainConf.BDRPDataServerHost
	}

	if conf.ScsMainConf.DataServerEntranceHostTrans != "" {
		return conf.ScsMainConf.DataServerEntranceHostTrans
	}
	return conf.ScsMainConf.DataServerHost
}

func (param *Parameter) GetDataserverPort() int {
	if param.App.DataserverType == "bdrp" {
		if conf.ScsMainConf.BDRPDataServerEntrancePortTrans != 0 {
			return conf.ScsMainConf.BDRPDataServerEntrancePortTrans
		}
		return conf.ScsMainConf.BDRPDataserverPort
	}

	if conf.ScsMainConf.DataServerEntrancePortTrans != 0 {
		return conf.ScsMainConf.DataServerEntrancePortTrans
	}
	return conf.ScsMainConf.DataserverPort
}

func GetParamters(ctx context.Context, appId string, entity string) (*Parameter, error) {
	param, err := GetParameterWithAppInfo(ctx, appId)
	if err != nil {
		return nil, err
	}
	if err := FillParameterEntityInfo(ctx, param, entity); err != nil {
		return nil, err
	}
	return param, nil
}

func GetParameterWithAppInfo(ctx context.Context, appId string) (*Parameter, error) {
	var err error
	param := &Parameter{}
	param.App, err = x1model.ApplicationGetByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load application from db failed", logit.Error("error", err))
		return nil, err
	}
	param.CsmasterModel, err = resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load csmaster cluster from db failed", logit.Error("error", err))
		return nil, err
	}
	param.CsmasterInstances, err = resource.CsmasterOpAgent.GetInstancesByClusterID(ctx, param.CsmasterModel.Id)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load csmaster instances from db failed", logit.Error("error", err))
		return nil, err
	}
	param.RedisAcls, err = x1model.RedisAclAllByCond(ctx, "app_id = ?", param.App.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load redis acls from db failed", logit.Error("error", err))
		return nil, err
	}
	param.ProxyAcls, err = x1model.ProxyAclGetAllByCond(ctx, "app_id = ?", param.App.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load proxy acls from db failed", logit.Error("error", err))
		return nil, err
	}
	param.ConfRecords, err = resource.CsmasterOpAgent.GetConfigByAppShortID(ctx, int(param.CsmasterModel.Id))
	if err != nil {
		resource.LoggerTask.Warning(ctx, "load csmaster config from db failed", logit.Error("error", err))
		return nil, err
	}
	if err := resource.CsmasterModel.GetAllByCond(ctx, &param.ConfDefs, "1 = 1"); err != nil {
		resource.LoggerTask.Warning(ctx, "load csmaster config def from db failed", logit.Error("error", err))
		return nil, err
	}
	param.MetaServer = &MetserverInfo{
		Ip:   "127.0.0.1",
		Port: 1025,
		Auth: "",
	}
	if param.App.Type == x1model.AppTypeCluster {
		metaClusterId := param.App.LocalMetaserver
		if len(param.App.AppGroupID) != 0 {
			metaClusterId = param.App.GlobalMetaserver
		}
		metaCluster, err := x1model.MetaClusterGetByIMetaClusterId(ctx, metaClusterId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "load meta cluster from db failed", logit.Error("error", err))
			return nil, err
		}
		metaClusterEntranceSplit := strings.Split(metaCluster.Entrance, ":")
		if len(metaClusterEntranceSplit) != 2 {
			resource.LoggerTask.Warning(ctx, "invalid meta cluster entrance")
			return nil, fmt.Errorf("invalid meta cluster entrance %s", metaCluster.Entrance)
		}
		param.MetaServer = &MetserverInfo{
			Ip:   metaClusterEntranceSplit[0],
			Port: cast.ToInt(metaClusterEntranceSplit[1]),
			Auth: metaCluster.Password,
		}
		// 获取集群tls信息
		redisTLSInfos, err := x1model.RedisTlsGetByAppId(ctx, appId)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get redis tls conf error", logit.Error("error", err))
			return nil, err
		}
		// 查询当前属于tls 开启流程中还是关闭流程中
		// true:触发开启流程   false:触发关闭流程
		for _, redisTLSInfo := range redisTLSInfos {
			if redisTLSInfo.Status == x1model.TlsStatusInUse {
				param.TlsEnabled = true
				param.TlsKey = redisTLSInfo.CertKeyPem
				param.TlsCert = redisTLSInfo.CertPem
			}
		}
	}

	if len(param.App.AppGroupID) != 0 {
		param.GlobalRedisForAll, err = gmaster.GlobalMasterOp().GetNodes(ctx, &gmaster.GetNodesParams{
			AppGroupID: param.App.AppGroupID,
			UserID:     param.App.UserId,
		})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "load global redis from global-api failed", logit.Error("error", err))
			return nil, err
		}
	}
	renamedCommands, err := param.GetDisabledCommands(ctx)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get renamed commands failed", logit.Error("error", err))
		return nil, err
	}
	param.RenamedCommands = renamedCommands
	// BCCS_CSMASTER_ERROR_NO RedisV5TopologyController::fill_cluster_new_whitelist(
	//        HeartBeatRequest* request, HeartBeatResponse* response) {
	//    int64_t cluster_id = request->id().cluster_id();
	//
	//    HashMap<int64_t, WhiteIpListModel, 1024>* ip_list_map
	//        = _state_context->white_ip_list_manager()->mutable_id_index_ip_list();
	//    HashMap<int64_t, BaeWhiteIpListModel, 1024>* bae_ip_list_map
	//        = _state_context->white_ip_list_manager()->mutable_id_index_bae_ip_list();
	//    ScopedRLock ip_list_lock(ip_list_map->lock(cluster_id));
	//    ScopedRLock bae_ip_list_lock(bae_ip_list_map->lock(cluster_id));
	//    WhiteIpListModel* white_ip_model = ip_list_map->find(cluster_id);
	//    if (white_ip_model == NULL) {
	//        BCCS_CSMASTER_WARNING("white ip list not in cluster:%ld", cluster_id);
	//        return BCCS_CSMASTER_ERROR_CLUSTER_NOT_EXIST;
	//    }
	//    BaeWhiteIpListModel* bae_white_ip_model = bae_ip_list_map->find(cluster_id);
	//    if (bae_white_ip_model == NULL) {
	//        BCCS_CSMASTER_WARNING("bae white ip list not in cluster:%ld", cluster_id);
	//        return BCCS_CSMASTER_ERROR_CLUSTER_NOT_EXIST;
	//    }
	//
	//    std::string ip_list;
	//    ClusterWhiteListTable* white_ip_table = white_ip_model->mutable_ip_list_table();
	//    for (int i = 0; i < white_ip_table->ip_groups_size(); ++i) {
	//        WhiteIpList* white_ip_list = response->mutable_proxy_conf()->add_white_ip_list();
	//        white_ip_list->set_ip(white_ip_table->ip_groups(i).floating_ip());
	//        white_ip_list->set_mode("rwx");
	//        ip_list.append(white_ip_table->ip_groups(i).floating_ip());
	//        if (i != (white_ip_table->ip_groups_size() - 1)) {
	//            ip_list.append(" | ");
	//        }
	//    }
	//    BaeClusterWhiteListTable* bae_white_ip_table = bae_white_ip_model->mutable_ip_list_table();
	//    for (int i = 0; i < bae_white_ip_table->ip_groups_size(); ++i) {
	//        WhiteIpList* white_ip_list = response->mutable_proxy_conf()->add_white_ip_list();
	//        white_ip_list->set_ip(bae_white_ip_table->ip_groups(i).floating_ip());
	//        white_ip_list->set_mode("rwx");
	//        ip_list.append(bae_white_ip_table->ip_groups(i).floating_ip());
	//        if (i != (bae_white_ip_table->ip_groups_size() - 1)) {
	//            ip_list.append(" | ");
	//        }
	//    }
	//
	//    std::vector<std::string>* default_ip_list = white_ip_model->default_ip_list();
	//    for (size_t i = 0; i < default_ip_list->size(); ++i) {
	//        WhiteIpList* white_ip_list = response->mutable_proxy_conf()->add_white_ip_list();
	//        white_ip_list->set_ip(default_ip_list->at(i));
	//        white_ip_list->set_mode("rwx");
	//        ip_list.append(default_ip_list->at(i));
	//        if (i != (default_ip_list->size() - 1)) {
	//            ip_list.append(" | ");
	//        }
	//    }
	//
	//    BCCS_CSMASTER_DEBUG(
	//            "[Success] [cluster_id:%ld] push"
	//            " white ip list : %s",
	//            cluster_id,
	//            ip_list.c_str());
	//    return BCCS_CSMASTER_SUCCESS;
	// }
	if param.App.Type == x1model.AppTypeCluster {
		var whitleIps []*csmaster_model_interface.ClusterWhiteList
		if err := resource.CsmasterModel.GetAllByCond(ctx, &whitleIps, "cluster_id = ?", param.CsmasterModel.Id); err != nil {
			return nil, err
		}
		allowAll := false
		for _, whiteIp := range whitleIps {
			if whiteIp.FloatingIp == "*.*.*.*" {
				param.ProxyWhiteIps = append(param.ProxyWhiteIps, &ProxyWhiteIp{
					IP:   whiteIp.FloatingIp,
					Mode: "rwx",
				})
				allowAll = true
				break
			}
		}
		if !allowAll {
			param.ProxyWhiteIps = append(param.ProxyWhiteIps, &ProxyWhiteIp{
				IP:   "127.0.0.1",
				Mode: "rwx",
			})
			for _, whiteIp := range whitleIps {
				param.ProxyWhiteIps = append(param.ProxyWhiteIps, &ProxyWhiteIp{
					IP:   whiteIp.FloatingIp,
					Mode: "rwx",
				})
			}
			for _, whiteIp := range security_group.Instance().GetDefaultIps(ctx) {
				param.ProxyWhiteIps = append(param.ProxyWhiteIps, &ProxyWhiteIp{
					IP:   whiteIp,
					Mode: "rwx",
				})
			}
			for _, cluster := range param.App.Clusters {
				for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
					param.ProxyWhiteIps = append(param.ProxyWhiteIps, &ProxyWhiteIp{
						IP:   node.Ip,
						Mode: "rwx",
					})
				}
			}
			for _, itf := range param.App.Interfaces {
				for _, proxy := range itf.Proxys {
					param.ProxyWhiteIps = append(param.ProxyWhiteIps, &ProxyWhiteIp{
						IP:   proxy.Ip,
						Mode: "rwx",
					})
				}
			}
		}
	}
	return param, nil
}

func FillParameterEntityInfo(ctx context.Context, param *Parameter, entity string) error {
	var err error
	var resourceID string
cloop:
	for _, cluster := range param.App.Clusters {
		for _, node := range cluster.Nodes {
			if node.NodeId == entity {
				param.Cluster = cluster
				param.Node = node
				param.Entity = entity
				param.EntityType = "node"
				param.EngineType = cluster.Engine
				resourceID = node.ResourceId
				break cloop
			}
		}
		for _, roNode := range cluster.RoNodes {
			if roNode.NodeId == entity {
				param.Cluster = cluster
				param.Node = util.ChangeRoNode2Node(roNode)
				param.Entity = entity
				param.EntityType = "ro_node"
				param.EngineType = cluster.Engine
				resourceID = roNode.ResourceId
				break cloop
			}
		}
	}
	if param.Node == nil {
	iloop:
		for _, itf := range param.App.Interfaces {
			for _, proxy := range itf.Proxys {
				if proxy.ProxyId == entity {
					param.Itf = itf
					param.Proxy = proxy
					param.Entity = entity
					param.EntityType = "proxy"
					param.EngineType = itf.Engine
					resourceID = proxy.ResourceId
					break iloop
				}
			}
		}
	}
	if len(resourceID) == 0 {
		resource.LoggerTask.Warning(ctx, "entity not found", logit.String("entity", entity))
		return fmt.Errorf("entity %s not found", entity)
	}
	for _, instance := range param.CsmasterInstances {
		if resourceID == instance.Uuid {
			param.CsmasterInstanceCurrent = instance
			break
		}
	}
	if param.CsmasterInstanceCurrent == nil {
		resource.LoggerTask.Warning(ctx, "resource not found in cache_instance", logit.String("resource_id", resourceID))
		return fmt.Errorf("resource %s not found in cache_instance", resourceID)
	}
	if len(param.App.AppGroupID) != 0 {
		if param.Cluster != nil {
			param.GlobalRedisForCurShard, err = gmaster.GlobalMasterOp().GetNodes(ctx, &gmaster.GetNodesParams{
				AppGroupID:    param.App.AppGroupID,
				UserID:        param.App.UserId,
				ShardGlobalID: param.Cluster.GlobalID,
			})
			if err != nil {
				resource.LoggerTask.Warning(ctx, "load global redis from global-api failed", logit.Error("error", err))
				return err
			}
		}
	}
	return nil
}

func (param *Parameter) GetPegaUseOpHeader() string {
	r := "no"
	if param.GetConfValue("support_multi_active", "redis", ConfDefDefault) == "yes" {
		r = "yes"
	}
	return r
}
