/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/14, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
检查子网ip是否充足，是否需要使用备用子网
*/

package checksubnets

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"icode.baidu.com/baidu/gdp/logit"
	subnet "icode.baidu.com/baidu/scs/x1-base/component/neutronV2/subnet"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// CheckEnoughIPs 检查子网ip是否充足，是否需要使用备用子网
// (1) 获取子网剩余ips，OpenStackSDK::get_subnet_total_ip_num - OpenStackSDK::get_subnet_used_ip_num 如果没有可用子网，则报错退出
// (2) 检查所有需要新创建的node、proxy；subnet字段填入ip数量充足的子网, 优先级低
// 参考代码位于FillSubnetIdProcessor::process
func CheckEnoughIPs(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err // todo 细化err
	}

	if skipCheckSubnetIPs(ctx, app) {
		resource.LoggerTask.Notice(ctx, "skip check subnet ips", logit.String("appId", teu.Entity))
		return nil
	}

	if app.ResourceType == "container" {
		return nil
	}

	iamUserId := app.UserId
	replicas := param.Replicas
	// 变更可用区场景,destReplicas记录目标子网信息
	if len(param.DestReplicas) != 0 {
		replicas = param.DestReplicas
	}

	if len(replicas) == 0 {
		err = json.Unmarshal([]byte(app.Replicas), &replicas)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "unmarshal replicas fail", logit.String("app.Replicas", app.Replicas),
				logit.Error("error", err))
			return err // todo 细化err
		}
	}
	subnetList := make([]string, 0)
	mapLogicZone2Subnet := make(map[string][]string) // 逻辑可用区=>subnetIds ，用于寻找同Az下其他subnetId
	for _, v := range replicas {
		for _, subnetId := range v.SubnetIDs {
			subnetList = append(subnetList, subnetId)
			mapLogicZone2Subnet[v.Zone] = append(mapLogicZone2Subnet[v.Zone], subnetId)
		}
	}
	subnetCompent := subnet.Instance()
	mapSubnetId2AvailableIpNum, err := subnetCompent.GetAvailableIPCount(ctx, &subnet.GetAvailableIPCountParams{
		UserID: iamUserId,
		Subnet: subnetList,
	}) // subnetId=>可用Ip数量

	if err != nil {
		resource.LoggerTask.Warning(ctx, "get available ip error", logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return err // todo 细化err
	}

	resource.LoggerTask.Notice(ctx, "get available ip suc", logit.String("mapSubnetId2IpNums", base_utils.Format(mapSubnetId2AvailableIpNum)))

	for _, cluster := range app.Clusters {
		if err := checkClusterIpEnough(ctx, cluster, mapLogicZone2Subnet, mapSubnetId2AvailableIpNum); err != nil {
			return err
		}
	}

	for _, itf := range app.Interfaces {
		if err := checkInterfaceIpEnough(ctx, itf, mapLogicZone2Subnet, mapSubnetId2AvailableIpNum); err != nil {
			return err
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return err // todo 细化err
	}
	return nil
}

func checkClusterIpEnough(ctx context.Context, cluster *x1model.Cluster, mapLogicZone2Subnet map[string][]string, mapSubnetId2AvailableIpNum map[string]int64) error {
	mapAz2DefaultSubnetid := make(map[string]string)      // 当前cluster下 逻辑az=>subnet , 用于逐个Az遍历判断是否ip够用
	mapSubnetId2Nodes := make(map[string][]*x1model.Node) // 当前cluster下 subnet=>node , 用于统计需要多少ip、维护node状态
	for _, node := range cluster.Nodes {
		// 非待检查状态节点不处理
		if node.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}
		if len(mapAz2DefaultSubnetid[node.LogicZone]) == 0 {
			mapAz2DefaultSubnetid[node.LogicZone] = node.SubnetId
		} else {
			// 在一个请求内，不允许一个分片的新节点使用不通的子网
			if mapAz2DefaultSubnetid[node.LogicZone] != node.SubnetId {
				resource.LoggerTask.Warning(ctx, "multi subnets in cluster new nodes", logit.String("cluster_id", cluster.ClusterId))
				return errors.Errorf("multi subnets in cluster %s new nodes", cluster.ClusterId)
			}
		}
		mapSubnetId2Nodes[node.SubnetId] = append(mapSubnetId2Nodes[node.SubnetId], node)
	}
	for az, defaultSubnetid := range mapAz2DefaultSubnetid {
		needIpNum := cast.ToInt64(len(mapSubnetId2Nodes[defaultSubnetid]))
		// 够用
		if needIpNum <= mapSubnetId2AvailableIpNum[defaultSubnetid] {
			mapSubnetId2AvailableIpNum[defaultSubnetid] = mapSubnetId2AvailableIpNum[defaultSubnetid] - needIpNum
			needIpNum = 0
			continue
		}
		// 不够用，尝试同Az下其他subnetId
		resource.LoggerTask.Notice(ctx, "not enough ips in default subnet", logit.String("az", az),
			logit.String("default_subnet_id", defaultSubnetid), logit.String("need_ip_num", base_utils.Format(needIpNum)))
		for _, availSubnetId := range mapLogicZone2Subnet[az] {
			// 换了个新的够用
			if needIpNum <= mapSubnetId2AvailableIpNum[availSubnetId] {
				resource.LoggerTask.Notice(ctx, "find an new subnet", logit.String("default_subnet_id", defaultSubnetid),
					logit.String("new_subnet_id", availSubnetId))
				mapSubnetId2AvailableIpNum[availSubnetId] = mapSubnetId2AvailableIpNum[availSubnetId] - needIpNum
				needIpNum = 0
				// 把default subnetip换成新的
				for _, node := range mapSubnetId2Nodes[defaultSubnetid] {
					node.SubnetId = availSubnetId
				}
				break
			}
		}
		// 够用或有新的可用时，已置0，大于0即为不够用且无新的可用
		if needIpNum > 0 {
			resource.LoggerTask.Warning(ctx, "not enough ips")
			return errors.Errorf("not enough ips")
		}
	}
	return nil
}

func checkInterfaceIpEnough(ctx context.Context, itf *x1model.Interface, mapLogicZone2Subnet map[string][]string, mapSubnetId2AvailableIpNum map[string]int64) error {
	mapAz2DefaultSubnetid := make(map[string]string)        // 当前cluster下 逻辑az=>subnet , 用于逐个Az遍历判断是否ip够用
	mapSubnetId2Proxys := make(map[string][]*x1model.Proxy) // 当前cluster下 subnet=>node , 用于统计需要多少ip、维护node状态
	for _, proxy := range itf.Proxys {
		// 非待检查状态节点不处理
		if proxy.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}
		if len(mapAz2DefaultSubnetid[proxy.LogicZone]) == 0 {
			mapAz2DefaultSubnetid[proxy.LogicZone] = proxy.SubnetId
		} else {
			// 在一个请求内，不允许一个分片的新节点使用不通的子网
			if mapAz2DefaultSubnetid[proxy.LogicZone] != proxy.SubnetId {
				resource.LoggerTask.Warning(ctx, "multi subnets in interface new proxys", logit.String("interface_id", itf.InterfaceId))
				return errors.Errorf("multi subnets in interface %s new nodes", itf.InterfaceId)
			}
		}
		mapSubnetId2Proxys[proxy.SubnetId] = append(mapSubnetId2Proxys[proxy.SubnetId], proxy)
	}
	for az, defaultSubnetid := range mapAz2DefaultSubnetid {
		needIpNum := cast.ToInt64(len(mapSubnetId2Proxys[defaultSubnetid]))
		// 够用
		if needIpNum <= mapSubnetId2AvailableIpNum[defaultSubnetid] {
			mapSubnetId2AvailableIpNum[defaultSubnetid] = mapSubnetId2AvailableIpNum[defaultSubnetid] - needIpNum
			needIpNum = 0
			continue
		}
		// 不够用，尝试同Az下其他subnetId
		resource.LoggerTask.Notice(ctx, "not enough ips in default subnet", logit.String("az", az),
			logit.String("default_subnet_id", defaultSubnetid), logit.String("need_ip_num", base_utils.Format(needIpNum)))
		for _, availSubnetId := range mapLogicZone2Subnet[az] {
			// 换了个新的够用
			if needIpNum <= mapSubnetId2AvailableIpNum[availSubnetId] {
				resource.LoggerTask.Notice(ctx, "find an new subnet", logit.String("default_subnet_id", defaultSubnetid),
					logit.String("new_subnet_id", availSubnetId))
				mapSubnetId2AvailableIpNum[availSubnetId] = mapSubnetId2AvailableIpNum[availSubnetId] - needIpNum
				needIpNum = 0
				// 把default subnetip换成新的
				for _, proxy := range mapSubnetId2Proxys[defaultSubnetid] {
					proxy.SubnetId = availSubnetId
				}
				break
			}
		}
		// 够用或有新的可用时，已置0，大于0即为不够用且无新的可用
		if needIpNum > 0 {
			resource.LoggerTask.Warning(ctx, "not enough ips")
			return errors.Errorf("not enough ips")
		}
	}
	return nil
}

// CheckEnoughIPsRorRo 校验创建只读实例是否存在有足够子网
func CheckEnoughIPsRorRo(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err // todo 细化err
	}

	if app.ResourceType == "container" {
		return nil
	}

	iamUserID := app.UserId

	// 获取创建只读实例的参数
	subnetMap := make(map[string]struct{}, 0)
	subnetList := make([]string, 0)
	mapLogicZone2Subnet := make(map[string][]string) // 逻辑可用区=>subnetIds ，用于寻找同Az下其他subnetId
	for _, v := range param.CreateRoInstParams.RoInstances {
		if _, exist := subnetMap[v.SubnetID]; !exist {
			subnetMap[v.SubnetID] = struct{}{}
			subnetList = append(subnetList, v.SubnetID)
			mapLogicZone2Subnet[v.AvailabilityZone] = append(mapLogicZone2Subnet[v.AvailabilityZone], v.SubnetID)
		}
	}
	subnetCompent := subnet.Instance()
	mapSubnetID2AvailableIPNum, err := subnetCompent.GetAvailableIPCount(ctx, &subnet.GetAvailableIPCountParams{
		UserID: iamUserID,
		Subnet: subnetList,
	}) // subnetId=>可用Ip数量

	if err != nil {
		resource.LoggerTask.Warning(ctx, "get available ip error", logit.String("appId", teu.Entity),
			logit.Error("error", err))
		return err // todo 细化err
	}

	resource.LoggerTask.Notice(ctx, "get available ip suc", logit.String("mapSubnetId2IpNums", base_utils.Format(mapSubnetID2AvailableIPNum)))

	for _, cluster := range app.Clusters {
		mapAz2DefaultSubnetid := make(map[string]string)      // 当前cluster下 逻辑az=>subnet , 用于逐个Az遍历判断是否ip够用
		mapSubnetID2Nodes := make(map[string][]*x1model.Node) // 当前cluster下 subnet=>node , 用于统计需要多少ip、维护node状态
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			// 非待检查状态节点不处理
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if len(mapAz2DefaultSubnetid[node.LogicZone]) == 0 {
				mapAz2DefaultSubnetid[node.LogicZone] = node.SubnetId
			} else {
				// 不允许同一个Az下多个subnetid
				if mapAz2DefaultSubnetid[node.LogicZone] != node.SubnetId {
					bytesCluster, err := json.Marshal(cluster)
					if err != nil {
						resource.LoggerTask.Warning(ctx, "Cluster Has Mulit Subnet In One Az,And Cluster Cant Marshal", logit.String("appId", teu.Entity),
							logit.Error("error", err))
						return errors.Errorf("Cluster Has Mulit Subnet In One Az,And Cluster Cant Marshal")
					}
					resource.LoggerTask.Warning(ctx, "Cluster Has Mulit Subnet In One Az", logit.String("appId", teu.Entity),
						logit.String("Cluster Info", string(bytesCluster)))
					return errors.Errorf("Cluster Has Mulit Subnet In One Az,Cluster Info: %s", string(bytesCluster))
				}
			}
			mapSubnetID2Nodes[node.SubnetId] = append(mapSubnetID2Nodes[node.SubnetId], node)
		}
		// 记录所有的只读实例
		roNodes := make([]*x1model.RoNode, 0)
		for _, roNode := range cluster.RoNodes {
			roNodes = append(roNodes, roNode)
		}

		for az, defaultSubnetid := range mapAz2DefaultSubnetid {
			needIPNum := cast.ToInt64(len(mapSubnetID2Nodes[defaultSubnetid]))
			// 够用
			if needIPNum <= mapSubnetID2AvailableIPNum[defaultSubnetid] {
				mapSubnetID2AvailableIPNum[defaultSubnetid] = mapSubnetID2AvailableIPNum[defaultSubnetid] - needIPNum
				needIPNum = 0
				continue
			}
			// 不够用，尝试同Az下其他subnetId
			resource.LoggerTask.Notice(ctx, "not have enough ips,try in same Az", logit.String("az", az),
				logit.String("defaultSubnetid", defaultSubnetid), logit.String("need ip num", base_utils.Format(needIPNum)))
			for _, availSubnetID := range mapLogicZone2Subnet[az] {
				// 换了个新的够用
				if needIPNum <= mapSubnetID2AvailableIPNum[availSubnetID] {
					resource.LoggerTask.Notice(ctx, "find an new subnet", logit.String("default subnet id", defaultSubnetid),
						logit.String("avail subnet id", availSubnetID))
					mapSubnetID2AvailableIPNum[availSubnetID] = mapSubnetID2AvailableIPNum[availSubnetID] - needIPNum
					needIPNum = 0
					// 把default subnetip换成新的
					for _, node := range mapSubnetID2Nodes[defaultSubnetid] {
						for _, roNode := range roNodes {
							if node.ResourceId == roNode.ResourceId {
								node.SubnetId = availSubnetID
							}
						}
						node.SubnetId = availSubnetID
					}
					break
				}
			}
			// 够用或有新的可用时，已置0，大于0即为不够用且无新的可用
			if needIPNum > 0 {
				bytesCluster, err := json.Marshal(cluster)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "Not Have Enough Ips ,And Cluster Cant Marshal", logit.String("appId", teu.Entity),
						logit.Error("error", err))
					return errors.Errorf("Not Have Enough Ips ,And Cluster Cant Marshal")
				}
				resource.LoggerTask.Warning(ctx, "Not Have Enough Ips", logit.String("appId", teu.Entity),
					logit.String("Cluster Info", string(bytesCluster)))
				return errors.Errorf("Not Have Enough Ips,Cluster Info: %s", string(bytesCluster))
			}
		}
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err // todo 细化err
	}
	return nil
}

func skipCheckSubnetIPs(ctx context.Context, app *x1model.Application) bool {
	// 边缘地域跳过检查
	if common.IsEdgeRegion() {
		return true
	}
	return false
}
