/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
解析parameter中的NodeType，将具体规格填入Cluster表/Interface表
*/

package specification

import (
	"context"
	"fmt"
	"math"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessFillSpec 解析parameter中的NodeType，将具体规格填入Cluster表/Interface表
// 1. 从x1-base的spec-conf中获取NodeType的具体规格，包括cpu、mem、root/data disk、instanceType
// 2. 将规格填入到cluster表以及interface表中（表中有规格相关的字段，供后续创建资源使用)；
// 参考代码在 OriginalReqToGeneralReqProcessor::fill_flavor_info
func ProcessFillSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get parameters error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			// cluster.Spec = cluster.DestSpec
			param := &specification.GetSpecificationParams{
				UserID:    app.UserId,
				Name:      cluster.DestSpec,
				Engine:    cluster.Engine,
				StoreType: cluster.StoreType,
				AppType:   app.Type,
			}
			spec, err := specification.GetSpecification(ctx, param)
			if err != nil {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
				return err
			}
			cluster.AvailableVolume = spec.AvailableVolume
			cluster.Cpu = spec.CPUCount
			cluster.MemSize = spec.MemoryCapacityInGB
			if app.ResourceType == "container" {
				if cluster.Cpu == 2 {
					cluster.Cpu = 1
				}
				if cluster.Engine != x1model.EnginePegaDB {
					cluster.MemSize = spec.AvailableVolume
				}
			}
			cluster.SysDiskSize = spec.RootDiskCapacityInGB
			cluster.DiskSize = int64(spec.DataDiskCapacityInGB)
		}
		for _, fspec := range params.ForceSpecs {
			if fspec.Engine == cluster.Engine {
				if fspec.Cpu > 0 {
					cluster.Cpu = fspec.Cpu
				}
				if fspec.Memory > 0 {
					cluster.MemSize = fspec.Memory
				}
				if fspec.SysDisk > 0 {
					cluster.SysDiskSize = fspec.SysDisk
				}
				// pega本地盘跳过fpesc覆盖数据盘大小逻辑
				if fspec.DataDisk > 0 && cluster.StoreType != x1model.StoreTypeLOCALDISK {
					cluster.DiskSize = int64(fspec.DataDisk)
				}
				if fspec.Volume > 0 {
					cluster.AvailableVolume = fspec.Volume
				}
				break
			}
		}

		// pega磁盘预留调整背景:https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/CyUESrdXXe/rUCEm_hUg_h14N
		// 获取集群配置信息信息
		// pegadb本地盘跳过cds预留逻辑
		if cluster.Engine == "pegadb" && cluster.StoreType != x1model.StoreTypeLOCALDISK {
			cdsQuotaInfos, err := x1model.CdsQuotaGetByAppId(ctx, app.AppId)
			if err != nil {
				resource.LoggerTask.Error(ctx, "get cds quota error", logit.Error("error", err))
				return err
			}
			if len(cdsQuotaInfos) == 0 {
				resource.LoggerTask.Notice(ctx, app.AppId+" is old app, no need to change cds quota")
			} else if len(cdsQuotaInfos) > 1 {
				msg := fmt.Sprintf("more than one cds quota record for app %+v", app.AppId)
				resource.LoggerTask.Error(ctx, msg)
				return errors.Errorf(msg)
			} else {
				cdsQuotaInfo := cdsQuotaInfos[0]
				// 计算并更新 cluster.DiskSize
				reservePercentage := cdsQuotaInfo.ReservePercentage
				reserveMaxQuota := cdsQuotaInfo.ReserveMaxQuota

				adjustAvailableVolume := math.Ceil(cast.ToFloat64(cluster.AvailableVolume) * cast.ToFloat64(1+cast.ToFloat64(reservePercentage)/100))
				if reserveMaxQuota == -1 {
					// 外部用户,不校验最大预留量
					// 圆整给 cluster.DiskSize 赋值
					roundAdjustAvailableVolume := int64(0)
					if cast.ToInt64(adjustAvailableVolume)%10 == 0 {
						roundAdjustAvailableVolume = cast.ToInt64(adjustAvailableVolume)
					} else {
						roundAdjustAvailableVolume = (cast.ToInt64(adjustAvailableVolume) + 10) / 10 * 10
					}
					cluster.DiskSize = cast.ToInt64(roundAdjustAvailableVolume) + 20 // 20G为固定预留
				} else {
					if adjustAvailableVolume-cast.ToFloat64(cluster.AvailableVolume) > cast.ToFloat64(reserveMaxQuota) {
						cluster.DiskSize = cast.ToInt64(cluster.AvailableVolume) + cast.ToInt64(reserveMaxQuota) + 20 // 20G为固定预留
					} else {
						// 圆整给 cluster.DiskSize 赋值
						roundAdjustAvailableVolume := int64(0)
						if cast.ToInt64(adjustAvailableVolume)%10 == 0 {
							roundAdjustAvailableVolume = cast.ToInt64(adjustAvailableVolume)
						} else {
							roundAdjustAvailableVolume = (cast.ToInt64(adjustAvailableVolume) + 10) / 10 * 10
						}
						cluster.DiskSize = cast.ToInt64(roundAdjustAvailableVolume) + 20 // 20G为固定预留
					}
				}
			}
		}
	}

	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			// itf.Spec = itf.DestSpec
			param := &specification.GetSpecificationParams{
				UserID:    app.UserId,
				Name:      itf.DestSpec,
				Engine:    itf.Engine,
				StoreType: itf.StoreType,
				AppType:   app.Type,
			}
			spec, err := specification.GetSpecification(ctx, param)
			if err != nil {
				resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
				return err
			}
			itf.AvailableVolume = spec.AvailableVolume
			itf.Cpu = spec.CPUCount
			itf.MemSize = spec.MemoryCapacityInGB
			itf.SysDiskSize = spec.RootDiskCapacityInGB
			itf.DiskSize = int64(spec.DataDiskCapacityInGB)
		}
		for _, fspec := range params.ForceSpecs {
			if fspec.Engine == itf.Engine {
				if fspec.Cpu > 0 {
					itf.Cpu = fspec.Cpu
				}
				if fspec.Memory > 0 {
					itf.MemSize = fspec.Memory
				}
				if fspec.SysDisk > 0 {
					itf.SysDiskSize = fspec.SysDisk
				}
				if fspec.DataDisk > 0 {
					itf.DiskSize = int64(fspec.DataDisk)
				}
				if fspec.Volume > 0 {
					itf.AvailableVolume = fspec.Volume
				}
				break
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCommitSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			cluster.Spec = cluster.DestSpec
		}
		cluster.ActualCpu = cluster.Cpu
		cluster.ActualMemSize = cluster.MemSize
		cluster.ActualDiskSize = int(cluster.DiskSize)
	}
	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			itf.Spec = itf.DestSpec
		}
		itf.ActualCpu = itf.Cpu
		itf.ActualMemSize = itf.MemSize
		itf.ActualDiskSize = int(itf.DiskSize)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessRollbackSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	for _, cluster := range app.Clusters {
		if cluster.DestSpec != cluster.Spec {
			cluster.DestSpec = cluster.Spec
		}
		cluster.Cpu = cluster.ActualCpu
		cluster.MemSize = cluster.ActualMemSize
		cluster.DiskSize = int64(cluster.ActualDiskSize)
	}
	for _, itf := range app.Interfaces {
		if itf.DestSpec != itf.Spec {
			itf.DestSpec = itf.Spec
		}
		itf.Cpu = itf.ActualCpu
		itf.MemSize = itf.ActualMemSize
		itf.DiskSize = int64(itf.ActualDiskSize)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app error", logit.Error("error", err))
		return err
	}
	return nil
}
