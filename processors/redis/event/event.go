package event

import (
	"context"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

const (
	modifyingEvent         = "modifying_cluster"
	switchingEvent         = "switching_cluster"
	replacingEvent         = "replacing_cluster"
	restartingEvent        = "restarting_cluster"
	modifyingAZEvent       = "modifying_cluster_az"
	modifyingEntranceEvent = "modifying_cluster_entrance"
	selfHealingEvent       = "self_healing_cluster"
)

const (
	successEvent = "success"
	failedEvent  = "failed"
)

const (
	FailoverSuccessEvent = "success"
	FailoverFailedEvent  = "failed"
)

func ProcessAddRestartingStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// // app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, restartingEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+restartingEvent+" event start failed", logit.Error("error", err))
	}

	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+restartingEvent+" event start success")
	return nil
}

func ProcessAddRestartingSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, restartingEvent, successEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+restartingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+restartingEvent+" event end success")
	return nil
}

func ProcessAddRestartingFailedEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, restartingEvent, failedEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+restartingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+restartingEvent+" event end success")
	return nil
}

func ProcessAddModifyingStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// // app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, modifyingEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingEvent+" event start failed", logit.Error("error", err))
	}

	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingEvent+" event start success")
	return nil
}

func ProcessAddModifyingSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, modifyingEvent, successEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingEvent+" event end success")
	return nil
}

func ProcessAddModifyingFailedEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, modifyingEvent, failedEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingEvent+" event end success")
	return nil
}

func GetEventForSwitchingStart(ctx context.Context, teu *workflow.TaskExecUnit) string {
	event := switchingEvent
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return ""
	}
	switch param.ManualFailoverReason {
	case "iaas_fault":
		event = replacingEvent
	}
	return event
}

func ProcessAddSwitchingStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	event := GetEventForSwitchingStart(ctx, teu)
	if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, event); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+event+" event start failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+event+" event start success")
	return nil
}

func ProcessAddSwitchingSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	event := GetEventForSwitchingStart(ctx, teu)
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, event, successEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+event+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+event+" event end success")
	return nil
}

func ProcessAddSwitchingFailedEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	event := GetEventForSwitchingStart(ctx, teu)
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, event, failedEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+event+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+event+" event end success")
	return nil
}

func ProcessAddReplacingStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, replacingEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event start success")
	return nil
}

func ProcessAddReplacingSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, replacingEvent, successEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end success")
	return nil
}

func ProcessAddReplacingFailedEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, replacingEvent, failedEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+replacingEvent+" event end success")
	return nil
}

func ProcessAddModifyingAZStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	//if teu == nil {
	//	resource.LoggerTask.Warning(ctx, "teu is nilptr")
	//	return errors.Errorf("teu is nilptr")
	//}
	//
	//app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	//if err != nil {
	//	resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	//}
	//
	//// // app.AppId  ==> cluster_show_id
	//if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, modifyingAZEvent); err != nil {
	//	resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingAZEvent+" event start failed", logit.Error("error", err))
	//}
	//
	//resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingAZEvent+" event start success")
	return nil
}

func ProcessAddModifyingAZSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	//if teu == nil {
	//	resource.LoggerTask.Warning(ctx, "teu is nilptr")
	//	return errors.Errorf("teu is nilptr")
	//}
	//
	//app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	//if err != nil {
	//	resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	//}
	//
	//// app.AppId  ==> cluster_show_id
	//if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, modifyingAZEvent, successEvent); err != nil {
	//	resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingAZEvent+" event end failed", logit.Error("error", err))
	//}
	//resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingAZEvent+" event end success")
	return nil
}

func ProcessAddModifyingAZFailedEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return nil
	//if teu == nil {
	//	resource.LoggerTask.Warning(ctx, "teu is nilptr")
	//	return errors.Errorf("teu is nilptr")
	//}
	//
	//app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	//if err != nil {
	//	resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	//}
	//
	//// app.AppId  ==> cluster_show_id
	//if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, modifyingAZEvent, failedEvent); err != nil {
	//	resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingAZEvent+" event end failed", logit.Error("error", err))
	//}
	//resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingAZEvent+" event end success")
	//return nil
}

func ProcessAddModifyingEntranceStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	//if teu == nil {
	//	resource.LoggerTask.Warning(ctx, "teu is nilptr")
	//	return errors.Errorf("teu is nilptr")
	//}
	//
	//app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	//if err != nil {
	//	resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	//}
	//
	//// // app.AppId  ==> cluster_show_id
	//if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, modifyingEntranceEvent); err != nil {
	//	resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingEntranceEvent+" event start failed", logit.Error("error", err))
	//}
	//
	//resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingEntranceEvent+" event start success")
	return nil
}

func ProcessAddModifyingEntranceSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	//if teu == nil {
	//	resource.LoggerTask.Warning(ctx, "teu is nilptr")
	//	return errors.Errorf("teu is nilptr")
	//}
	//
	//app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	//if err != nil {
	//	resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	//}
	//
	//// app.AppId  ==> cluster_show_id
	//if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, modifyingEntranceEvent, successEvent); err != nil {
	//	resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingEntranceEvent+" event end failed", logit.Error("error", err))
	//}
	//resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingEntranceEvent+" event end success")
	return nil
}

func ProcessAddModifyingEntranceFailedEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	//if teu == nil {
	//	resource.LoggerTask.Warning(ctx, "teu is nilptr")
	//	return errors.Errorf("teu is nilptr")
	//}
	//
	//app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	//if err != nil {
	//	resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
	//}
	//
	//// app.AppId  ==> cluster_show_id
	//if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, modifyingEntranceEvent, failedEvent); err != nil {
	//	resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+modifyingEntranceEvent+" event end failed", logit.Error("error", err))
	//}
	//resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+modifyingEntranceEvent+" event end success")
	return nil
}

func ProcessSelfHealingStartEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return errors.New("get app model failed")
	}

	//代理节点不推送事件
	if app.Type == x1model.AppTypeCluster && len(param.SelfHealFromCsmaster.NodeShortIDs) == 0 {
		resource.LoggerTask.Notice(ctx, "proxy no need to notify")
		return nil
	}

	// // app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventStart(ctx, app.AppId, selfHealingEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+selfHealingEvent+" event start failed", logit.Error("error", err))
	}

	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+selfHealingEvent+" event start success")
	return nil
}

func ProcessSelfHealingSuccessEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return errors.New("get app model failed")
	}

	if app.Type == x1model.AppTypeCluster && len(param.SelfHealFromCsmaster.NodeShortIDs) == 0 {
		resource.LoggerTask.Notice(ctx, "proxy no need to notify")
		return nil
	}

	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, selfHealingEvent, successEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+selfHealingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+selfHealingEvent+" event end success")
	return nil
}

func ProcessSelfHealingFailEndEvent(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return errors.New("get app model failed")
	}

	if app.Type == x1model.AppTypeCluster && len(param.SelfHealFromCsmaster.NodeShortIDs) == 0 {
		resource.LoggerTask.Notice(ctx, "proxy no need to notify")
		return nil
	}
	// app.AppId  ==> cluster_show_id
	if err := resource.CsmasterOpAgent.AddEventEnd(ctx, app.AppId, selfHealingEvent, failedEvent); err != nil {
		resource.LoggerTask.Error(ctx, "AppId:"+app.AppId+" add "+selfHealingEvent+" event end failed", logit.Error("error", err))
	}
	resource.LoggerTask.Notice(ctx, "AppId:"+app.AppId+" add "+selfHealingEvent+" event end success")
	return nil
}
