/*
 * Copyright(C) 2022 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2022/01/24
 * File: slaves.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package upgrade TODO package function desc
package upgrade

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/deploy"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/acl"
	pDeploy "icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/readonlygroup"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	upgradeTimeoutSec = 500
	pingTimeoutSec    = 300
)

func GetVersion(app *x1model.Application) string {
	_, version := util.GetImageIdAndVersion(app.ImageID)
	return version
}

// IsBigUpgrade check big upgrade or not
func IsBigUpgrade(ctx context.Context, oldVer string, newVer string) (bool, error) {
	oldVers := strings.Split(oldVer, ".")
	newVers := strings.Split(newVer, ".")
	if len(oldVers) == 0 || len(newVers) == 0 {
		resource.LoggerTask.Warning(ctx, "old version or new version is int wrong format",
			logit.String("oldver", oldVer), logit.String("new ver", newVer))
		return false, errors.Errorf("wrong version,old version:%s, new version:%s", oldVer, newVer)
	}
	if len(newVer) == 0 {
		// 不传新版本是小更新
		return false, nil
	}

	if cast.ToInt(oldVers[0]) > cast.ToInt(newVers[0]) {
		resource.LoggerTask.Warning(ctx, "old version is newer than new version",
			logit.String("oldver", oldVer), logit.String("new ver", newVer))
		return false, errors.Errorf("invalid upgrade ,old version:%s, new version:%s", oldVer, newVer)
	} else if cast.ToInt(oldVers[0]) == cast.ToInt(newVers[0]) {
		// 小更新
		resource.LoggerTask.Notice(ctx, "is small upgrade",
			logit.String("oldver", oldVer), logit.String("new ver", newVer))
		return false, nil
	} else {
		resource.LoggerTask.Notice(ctx, "is big upgrade",
			logit.String("oldver", oldVer), logit.String("new ver", newVer))
		// 大更新
		return true, nil
	}
}

func ProcessUpgradeSlaves(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	deployClient := deploy.NewDefaultClient()

	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return err
	}
	isBigUp := false
	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if masterNode == nil {
					masterNode = node
				} else {
					resource.LoggerTask.Warning(ctx, "more than one master", logit.String("master1", base_utils.Format(masterNode)),
						logit.String("master2", base_utils.Format(node)), logit.String("appid", app.AppId), logit.String("clusterId", cluster.ClusterId))
					return errors.Errorf("more than one master")
				}
			}
		}
		if masterNode == nil {
			resource.LoggerTask.Warning(ctx, "no master in this cluster", logit.String("appid", app.AppId), logit.String("clusterId", cluster.ClusterId))
			return errors.Errorf("no master in this cluster")
		}

		for _, node := range cluster.Nodes {
			if (node.Role == x1model.RoleTypeSlave && !util.IsSingleReplica(ctx, cluster)) &&
				(node.Status == x1model.NodeOrProxyStatusToUpgrade || node.Status == x1model.NodeOrPorxyStatusRestarted) {
				oldVersion := node.EngineVersion
				// 已经是新版了
				if oldVersion == param.TargetKernelVersion {
					resource.LoggerTask.Warning(ctx, "no need to upgrade", logit.String("old ver", oldVersion),
						logit.String("new version", param.TargetKernelVersion))
				}
				// 只有Redis支持大版本升级,判断是不是大更新
				if cluster.Engine == x1model.EngineRedis {
					isBigUp, err = IsBigUpgrade(ctx, oldVersion, param.TargetKernelVersion)
					if err != nil {
						resource.LoggerTask.Warning(ctx, "invalid version", logit.String("errors", err.Error()))
						return err
					}
				}
				if len(param.TargetKernelVersion) != 0 && param.TargetKernelVersion != oldVersion {
					node.EngineVersion = param.TargetKernelVersion
					cluster.EngineVersion = param.TargetKernelVersion
				}
				if err := processUpgradeOneSlave(ctx, app, cluster, node, masterNode, deployClient,
					false, cmdList, password, teu.TaskID); err != nil {
					return err
				}
			}
		}
	}

	if isBigUp {
		updateReq := csmaster.UpdateClusterModelParams{
			Model: &csmaster.CsmasterCluster{
				KernelVersion: param.TargetKernelVersion,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &updateReq); err != nil {
			resource.LoggerTask.Warning(ctx, "big upgrade cb cs-master fail", logit.String("req", base_utils.Format(updateReq)),
				logit.String("err", err.Error()))
			return err
		}
		resource.LoggerTask.Notice(ctx, "big upgrade cb cs-master suc", logit.String("req", base_utils.Format(updateReq)))
	}

	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	return nil
}

// processUpgradeOneSlave will upgrade one slave
func processUpgradeOneSlave(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster,
	node, masterNode *x1model.Node, deployClient deploy.Service, inGroup bool, cmdList, password, taskID string) error {
	if node.Status == x1model.NodeOrProxyStatusToUpgrade {
		var err error
		if app.UseNewPackage == 1 {
			err = ExecuteUpgradeNew(ctx, app, cluster, node, cmdList, password, taskID, false)
		} else {
			err = ExecuteUpgrade(ctx, app, cluster, node, deployClient)
		}
		if err != nil {
			resource.LoggerTask.Warning(ctx, "upgrade node fail", logit.String("nodeId", node.NodeFixID), logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Notice(ctx, "node restart suc", logit.String("appId", app.AppId), logit.String("clusterId", app.AppId),
			logit.String("nodeId", node.NodeId))
		node.Status = x1model.NodeOrPorxyStatusRestarted
		if err := x1model.NodesSave(ctx, []*x1model.Node{node}); err != nil {
			if err != nil {
				resource.LoggerTask.Warning(ctx, "save  ro node fail", logit.String("nodeId", node.NodeFixID),
					logit.Error("dbError", err))
				return err
			}
		}
	}

	// 当前old agent还有设置topo的逻辑，需要等待5s，避免太快，agent设置topo覆盖了task设置的topo
	time.Sleep(5 * time.Second)
	if err := SetSlaveOfForStandalone(ctx, app, node, masterNode, password, !inGroup); err != nil {
		resource.LoggerTask.Warning(ctx, "set slave of fail", logit.String("nodeId", node.NodeFixID),
			logit.Error("error", err))
		return err
	}

	// check one redis sync
	if err := util.CheckOneRedisSync(ctx, app, node); err != nil {
		resource.LoggerTask.Warning(ctx, "check redis inst sync failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	node.Status = x1model.NodeOrProxyStatusInUse
	if err := x1model.NodesSave(ctx, []*x1model.Node{node}); err != nil {
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save  ro node fail", logit.String("nodeId", node.NodeFixID),
				logit.Error("dbError", err))
			return err
		}
	}
	return nil
}

func ExecuteUpgrade(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node, deployClient deploy.Service) error {
	xAgentAddr := xagent.Addr{
		Host: node.FloatingIP,
		Port: cast.ToInt32(node.XagentPort),
	}

	deployConfRsp, err := deployClient.GetXcacheDeployConf(ctx, &deploy.GetXcacheDeployConfRequest{
		Conf: &deploy.XcacheConf{
			PackageTag: "xcache",
			Version:    GetVersion(app),
			WorkDir:    node.Basedir,
			PORT:       int32(node.Port),
			ServerId:   node.NodeId,
			MaxSpace:   cluster.DiskSize,
		},
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get scs cache conf fail", logit.String("nodeId", node.NodeId))
		return err
	}

	restartReq := xagent.AsyncRequest{
		Addr:   &xAgentAddr,
		Action: "upgrade",
		Params: &ScsDeployConf{
			PackageName: deployConfRsp.Package.PackageName,
			PackageUrl:  deployConfRsp.Package.PackageUri,
			XcacheConf:  deployConfRsp.Conf,
		},
		TimeoutSec: upgradeTimeoutSec,
	}

	asyncTask := xagent.Instance().DoAsync(ctx, &restartReq)
	_, err = asyncTask.Wait()
	if err != nil {
		if strings.Contains(err.Error(), "Redis is loading the dataset in memory") {
			resource.LoggerTask.Notice(ctx, "node restart ok and loading", logit.String("appId", app.AppId),
				logit.Error("err", err), logit.String("clusterId", app.AppId),
				logit.String("nodeId", node.NodeId))
		} else {
			resource.LoggerTask.Warning(ctx, "call x-agent restart fail", logit.String("appId", app.AppId),
				logit.Error("err", err), logit.String("clusterId", app.AppId),
				logit.String("nodeId", node.NodeId), logit.Error("err", err))
			return err
		}
	}
	return nil
}

func ExecuteUpgradeNew(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node,
	cmdList string, password string, taskID string, forceRestart bool) error {
	params := pDeploy.GetNodeDeployParams(app, cluster, node, cmdList, password)
	params.IsUpgrade = true
	params.TaskID = taskID
	params.ForceRestart = forceRestart
	return pDeploy.DeployNodeOfAllTypeNew(ctx, params)
}

// ExecuteRestartNew 使用原版本进行重启，实现重启的版本固定
// 知识库：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/oWJj7uxF4H/iih_cYvm_iU6jJ
func ExecuteRestartNew(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, node *x1model.Node,
	cmdList string, password string, taskID string, forceRestart bool) error {
	params := pDeploy.GetNodeDeployParams(app, cluster, node, cmdList, password)
	params.IsUpgrade = true
	params.TaskID = taskID
	params.ForceRestart = forceRestart
	params.SamePkgVersionServerID = node.NodeId // 与ExecuteUpgradeNew唯一区别
	return pDeploy.DeployNodeOfAllTypeNew(ctx, params)
}

// ProcessUpgradeReadonlyInstances upgrade readonly instances slave
func ProcessUpgradeReadonlyInstances(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nil ptr")
		return err
	}

	if len(app.AppGroupID) != 0 {
		return nil
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	if err := processUpgradeReadOnlyInstances(ctx, teu, app, param); err != nil {
		return err
	}
	return nil
}

// processUpgradeReadOnlyInstances will only restart/upgrade readonly instances one by one
func processUpgradeReadOnlyInstances(ctx context.Context, teu *workflow.TaskExecUnit,
	app *x1model.Application, param *iface.Parameters) error {
	var err error
	deployClient := deploy.NewDefaultClient()
	isBigUp := false

	cmdList, err := util.GetDisableCommands(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get disable commands failed")
		return err
	}
	password, err := util.GetRedisCryptedPassword(ctx, app)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get redis password failed")
		return err
	}
	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node = nil
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster {
				if masterNode == nil {
					masterNode = node
				} else {
					resource.LoggerTask.Warning(ctx, "more than one master", logit.String("master1", base_utils.Format(masterNode)),
						logit.String("master2", base_utils.Format(node)), logit.String("appid", app.AppId), logit.String("clusterId", teu.Entity))
					return errors.Errorf("more than one master")
				}
			}
		}

		for _, node := range cluster.RoNodes {
			if node.Role == x1model.RoleTypeSlave &&
				(node.Status == x1model.NodeOrProxyStatusToUpgrade || node.Status == x1model.NodeOrPorxyStatusRestarted) {
				oldVersion := node.EngineVersion
				// 已经是新版了
				if oldVersion == param.TargetKernelVersion {
					resource.LoggerTask.Warning(ctx, "no need to upgrade", logit.String("old ver", oldVersion),
						logit.String("new version", param.TargetKernelVersion))
				}
				// 判断是不是大更新
				isBigUp, err = IsBigUpgrade(ctx, oldVersion, param.TargetKernelVersion)
				if err != nil {
					resource.LoggerTask.Warning(ctx, "invalid version", logit.String("errors", err.Error()))
					return err
				}

				if len(param.TargetKernelVersion) != 0 && param.TargetKernelVersion != oldVersion {
					node.EngineVersion = param.TargetKernelVersion
					cluster.EngineVersion = param.TargetKernelVersion
				}
				if err := processUpgradeOneReadonlyInstance(ctx, app, cluster, node, masterNode, deployClient,
					false, cmdList, password, teu.TaskID); err != nil {
					return err
				}
			}
		}
	}

	if isBigUp {
		updateReq := csmaster.UpdateClusterModelParams{
			Model: &csmaster.CsmasterCluster{
				KernelVersion: param.TargetKernelVersion,
			},
			UserID: app.UserId,
			AppID:  app.AppId,
		}
		if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &updateReq); err != nil {
			resource.LoggerTask.Warning(ctx, "big upgrade cb cs-master fail", logit.String("req", base_utils.Format(updateReq)),
				logit.String("err", err.Error()))
			return err
		}
		resource.LoggerTask.Notice(ctx, "big upgrade cb cs-master suc", logit.String("req", base_utils.Format(updateReq)))
	}

	// 保存节点状态
	err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	return nil
}

// processUpgradeOneReadonlyInstance will upgrade one readonly instance
// 获取只读组id, 查询对应blb, list ip group member, 存在则删除， 不存在则进行下一步
// 重启只读实例+ slave of
// 重启完检查同步
// 获取只读组id, 查询对应blb, list ip group member, 存在则返回成功，不存在则进行添加
// 完成单个只读实例的重启完整流程
func processUpgradeOneReadonlyInstance(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster,
	node *x1model.RoNode, masterNode *x1model.Node, deployClient deploy.Service, inGroup bool, cmdList string, password, taskID string) error {
	// unbind rs
	var blb *x1model.BLB = nil
	for _, b := range app.BLBs {
		if b.RoGroupID == node.RoGroupID {
			blb = b
			break
		}
	}
	if blb == nil {
		resource.LoggerTask.Warning(ctx, "blb not found", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId),
			logit.String("nodeId", node.NodeId))
		return errors.Errorf("readonly inst not found blb")
	}

	if err := readonlygroup.UnbindRoInstRs(ctx, app, blb, node); err != nil {
		resource.LoggerTask.Warning(ctx, "unbind ro inst rs failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	// 重启节点
	if node.Status == x1model.NodeOrProxyStatusToUpgrade {
		var err error
		if app.UseNewPackage == 1 {
			err = ExecuteUpgradeNew(ctx, app, cluster, util.ChangeRoNode2Node(node), cmdList, password, taskID, false)
		} else {
			err = ExecuteUpgrade(ctx, app, cluster, util.ChangeRoNode2Node(node), deployClient)
		}
		if err != nil {
			resource.LoggerTask.Warning(ctx, "node upgrade fail",
				logit.Error("err", err), logit.String("nodeId", node.NodeId))
			return err
		}

		resource.LoggerTask.Notice(ctx, "node upgrade suc", logit.String("appId", app.AppId),
			logit.Error("err", err), logit.String("clusterId", app.AppId),
			logit.String("nodeId", node.NodeId))

		// 更新节点状态为已重启
		node.Status = x1model.NodeOrPorxyStatusRestarted
		if err := x1model.RoNodesSave(ctx, []*x1model.RoNode{node}); err != nil {
			if err != nil {
				resource.LoggerTask.Warning(ctx, "save ro node fail", logit.String("nodeId", node.NodeFixID),
					logit.Error("dbError", err))
				return err
			}
		}
	}

	if inGroup {
		// 获取可用的master节点
		master, err := util.GetShardMasterNode(ctx, app, cluster)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "find readonly master fail", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
				logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)), logit.Error("Error", err))
			return err
		}
		masterNode = master
	}

	// 当前old agent还有设置topo的逻辑，需要等待5s，避免太快，agent设置topo覆盖了task设置的topo
	time.Sleep(5 * time.Second)
	if err := SetSlaveOfForStandalone(ctx, app, util.ChangeRoNode2Node(node), masterNode, password, true); err != nil {
		resource.LoggerTask.Warning(ctx, "set slave of fail", logit.String("nodeId", node.NodeFixID),
			logit.Error("error", err))
		return err
	}

	// check sync
	if err := readonlygroup.CheckRoInstSync(ctx, app, node); err != nil {
		resource.LoggerTask.Warning(ctx, "check ro inst sync failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	// bind rs
	if err := readonlygroup.BindRoInstRs(ctx, app, blb, node); err != nil {
		resource.LoggerTask.Warning(ctx, "rebind ro inst rs failed", logit.String("appId", app.AppId),
			logit.String("clusterId", app.AppId), logit.Error("err", err),
			logit.String("nodeId", node.NodeId))
		return err
	}

	node.Status = x1model.NodeOrProxyStatusInUse
	if err := x1model.RoNodesSave(ctx, []*x1model.RoNode{node}); err != nil {
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save ro node fail", logit.String("nodeId", node.NodeFixID),
				logit.Error("dbError", err))
			return err
		}
	}
	return nil
}

func ProcessUpgradeProxies(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return err
	}
	isInDel, err := util.IsInCsmasterDelProcess(ctx, app.AppId, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get status fail in cm db")
		return errors.Errorf("get status fail in cm db")
	}
	if isInDel {
		app.Status = x1model.AppStatusDeleted
		err = x1model.ApplicationsSave(ctx, []*x1model.Application{app})
		if err != nil {
			resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
			return err
		}
		return nil
	}
	deployClient := deploy.NewDefaultClient()
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToUpgrade {
				if app.UseNewPackage == 1 {
					if err := processUpgradeOneProxyNew(ctx, app, itf, proxy, teu.TaskID); err != nil {
						return err
					}
				} else {
					if err := processRestartProxies(ctx, app, []*x1model.Proxy{proxy}, deployClient); err != nil {
						return err
					}
				}
			}
		}
	}
	return nil
}

func needGlobalProxyInfo(app *x1model.Application) bool {
	if len(app.AppGroupID) == 0 {
		return false
	}
	if app.Status == x1model.AppStatusFollowerQuit ||
		app.Status == x1model.AppStatusDeleteQuit {
		return false
	}
	return true
}

func processRestartProxies(ctx context.Context, app *x1model.Application, proxys []*x1model.Proxy, deployClient deploy.Service) error {
	asyncTasks := make(map[string]*xagent.TaskContext, 0)
	mapTaskIdToProxy := make(map[string]*x1model.Proxy, 0)
	for _, proxy := range proxys {
		deployConfRsp, err := deployClient.GetXcacheDeployConf(ctx, &deploy.GetXcacheDeployConfRequest{
			Conf: &deploy.XcacheConf{
				PackageTag:     "xcache",
				Version:        "",
				WorkDir:        proxy.Basedir,
				PORT:           int32(proxy.Port),
				ServerId:       proxy.ProxyId,
				MaxSpace:       20,
				GlobalSeqId:    cast.ToInt64(proxy.GlobalSeqID),
				AppGlobalSeqId: cast.ToInt64(app.AppGroupSeqID),
			},
		})
		if !needGlobalProxyInfo(app) {
			deployConfRsp.Conf.AppGlobalSeqId = 0
			deployConfRsp.Conf.GlobalSeqId = 0
		}
		if err != nil {
			resource.LoggerTask.Warning(ctx, "get scs scache conf fail", logit.String("proxyId", proxy.ProxyId), logit.Error("error", err))
			return err
		}
		xagentAddr := xagent.Addr{
			Host: proxy.FloatingIP,
			Port: cast.ToInt32(proxy.XagentPort),
		}
		restartReq := xagent.AsyncRequest{
			Addr:   &xagentAddr,
			Action: "upgrade",
			Params: &ScsDeployConf{
				PackageName: deployConfRsp.Package.PackageName,
				PackageUrl:  deployConfRsp.Package.PackageUri,
				XcacheConf:  deployConfRsp.Conf,
			},
			TimeoutSec: upgradeTimeoutSec,
		}
		asyncCtx := xagent.Instance().DoAsync(ctx, &restartReq)
		taskId := proxy.InterfaceId + ":" + proxy.ProxyId
		asyncTasks[taskId] = asyncCtx
		mapTaskIdToProxy[taskId] = proxy
	}

	var completeProxys []*x1model.Proxy
	for taskId, task := range asyncTasks {
		_, err := task.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "upgrade proxy fail", logit.String("proxyId", mapTaskIdToProxy[taskId].ProxyId), logit.Error("error", err))
			continue
		}
		resource.LoggerTask.Notice(ctx, "upgrade proxy success", logit.String("proxyId", mapTaskIdToProxy[taskId].ProxyId))
		mapTaskIdToProxy[taskId].Status = x1model.NodeOrProxyStatusInUse
		completeProxys = append(completeProxys, mapTaskIdToProxy[taskId])
	}
	if err := x1model.ProxysSave(ctx, completeProxys); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy status fail", logit.Error("error", err))
		return err
	}
	if len(completeProxys) != len(asyncTasks) {
		resource.LoggerTask.Warning(ctx, "upgrade proxy fail", logit.Int("success", len(completeProxys)), logit.Int("total", len(asyncTasks)))
		return fmt.Errorf("upgrade proxy fail,success:%d,total:%d", len(completeProxys), len(asyncTasks))
	}

	for _, proxy := range proxys {
		if err := util.PingTest(ctx, proxy.FloatingIP, proxy.Port, pingTimeoutSec, nil); err != nil && !strings.Contains(err.Error(), "NOAUTH") {
			resource.LoggerTask.Warning(ctx, "ping proxy failed",
				logit.String("proxyId", proxy.ProxyId),
				logit.String("floatingIp", proxy.FloatingIP),
				logit.Int("port", proxy.Port),
				logit.Error("err", err),
			)
			return err
		}
	}
	return nil
}

func processUpgradeOneProxyNew(ctx context.Context, app *x1model.Application, itf *x1model.Interface, proxy *x1model.Proxy, taskID string) error {
	params := pDeploy.GetProxyDeployParams(app, itf, proxy)
	params.TaskID = taskID
	params.IsUpgrade = true
	if err := pDeploy.DeployNodeOfAllTypeNew(ctx, params); err != nil {
		resource.LoggerTask.Warning(ctx, "deploy proxy fail", logit.String("proxyId", proxy.ProxyId), logit.Error("error", err))
		return err
	}
	proxy.Status = x1model.NodeOrProxyStatusInUse
	if err := x1model.ProxysSave(ctx, []*x1model.Proxy{proxy}); err != nil {
		resource.LoggerTask.Warning(ctx, "save proxy status fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessUpgradeProxiesNew(ctx context.Context, app *x1model.Application, itfMap map[string]*x1model.Interface,
	proxies []*x1model.Proxy, taskID string) error {
	g := gtask.Group{Concurrent: 20}
	for _, proxy := range proxies {
		proxy := proxy
		itf := itfMap[proxy.InterfaceId]
		g.Go(func() error {
			return processUpgradeOneProxyNew(ctx, app, itf, proxy, taskID)
		})
	}
	_, err := g.Wait()
	return err
}

func SetSlaveOfForStandalone(ctx context.Context, app *x1model.Application, node *x1model.Node, masterNode *x1model.Node,
	password string, setSlaveofFlag bool) error {
	// 设置slave of(选择先设置slave of, 后设置acl，则不用处理password)
	// 如果重构后agent，由于部署时已经设置密码，这里需要传密码
	if setSlaveofFlag {
		var slavePass string
		if app.UseNewAgent == "yes" && password != "" {
			resource.LoggerTask.Trace(ctx, "send slaveof command with password", logit.String("password", password))
			slavePass, _ = crypto_utils.DecryptKey(password)
		}
		err := util.SetSlaveOf(ctx, node.FloatingIP, node.Port, slavePass, masterNode.Ip, cast.ToString(masterNode.Port))
		if err != nil {
			resource.LoggerTask.Warning(ctx, "send slave of master fail", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
				logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)), logit.Error("Error", err))
			return err
		}

		resource.LoggerTask.Notice(ctx, "send slave of master suc", logit.String("master info ", fmt.Sprintf("%s:%d", masterNode.Ip, masterNode.Port)),
			logit.String("slave info ", fmt.Sprintf("%s:%d", node.FloatingIP, node.Port)))
	}
	// 标准版设置acl
	if app.Type == x1model.AppTypeStandalone && app.UseNewAgent != "yes" {
		if err := acl.SetStandaloneACL(ctx, app, node); err != nil {
			resource.LoggerTask.Warning(ctx, "set standalone acl failed", logit.String("appId", app.AppId),
				logit.Error("err", err), logit.String("clusterId", app.AppId),
				logit.String("nodeId", node.NodeId), logit.Error("err", err))
			return err
		}
	}
	return nil
}
