package upgrade

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	FullVersionNewest = "newest"
)

type UpgradePackagesParams struct {
	Packages []*PackageInfo `json:"packages"`
	ServerID string
}

type PackageInfo struct {
	PackageName string `json:"package_name"`
	FullVersion string `json:"full_version"`
}

var AllowUpgradePackages = []string{
	"xagent", "agent", "monitor-agent", "csagent", "cron", "opbin",
}

func ProcessUpgradePackages(ctx context.Context, teu *workflow.TaskExecUnit) error {
	var params UpgradePackagesParams
	if err := json.Unmarshal([]byte(teu.Parameters), &params); err != nil {
		resource.LoggerTask.Warning(ctx, "unmarshal params fail", logit.Error("err", err))
		return err
	}
	if len(params.Packages) == 0 {
		resource.LoggerTask.Warning(ctx, "packages is empty")
		return nil
	}
	for _, pkg := range params.Packages {
		if in, _ := base_utils.InArray(pkg.PackageName, AllowUpgradePackages); !in {
			resource.LoggerTask.Warning(ctx, "package not allow upgrade", logit.String("package", pkg.PackageName))
			return fmt.Errorf("package %s not allow upgrade", pkg)
		}
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app info fail", logit.Error("err", err))
		return err
	}
	if app.UseNewPackage == 0 {
		resource.LoggerTask.Warning(ctx, "app not use new package")
		return errors.New("app not use new package")
	}

	// 允许部分失败
	g := gtask.Group{Concurrent: 10, AllowSomeFail: true}
	for cidx, cluster := range app.Clusters {
		cidx := cidx
		cluster := cluster
		for nidx, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			nidx := nidx
			node := node
			dParams := deploy.GetNodeDeployParams(app, cluster, node, "", "")
			dParams.TaskID = teu.TaskID
			g.Go(func() error {
				return updatePackageOne(ctx, app, params.Packages, dParams, cidx == 0 && nidx == 0)
			})
		}
	}
	for _, itf := range app.Interfaces {
		itf := itf
		for _, proxy := range itf.Proxys {
			proxy := proxy
			dParams := deploy.GetProxyDeployParams(app, itf, proxy)
			dParams.TaskID = teu.TaskID
			g.Go(func() error {
				return updatePackageOne(ctx, app, params.Packages, dParams, false)
			})
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "update package fail", logit.Error("err", err))
		return err
	}
	return nil
}

func getToUpdatePkgs(ctx context.Context, app *x1model.Application, pkgInfos []*PackageInfo,
	serverID string, taskId string, updateApp bool) ([]string, []*x1model.Package, error) {
	var pkgs []*x1model.Package
	var pkgNames []string
	var newestPkgNames []string
	var pkgIds []string
	for _, pkgInfo := range pkgInfos {
		pkgNames = append(pkgNames, pkgInfo.PackageName)
		if pkgInfo.FullVersion == FullVersionNewest {
			newestPkgNames = append(newestPkgNames, pkgInfo.PackageName)
		} else {
			pkgIds = append(pkgIds, fmt.Sprintf("%s-%s", pkgInfo.PackageName, pkgInfo.FullVersion))
		}
	}
	resource.LoggerTask.Trace(ctx, "pkg infos", logit.String("pkgNames", base_utils.Format(pkgNames)),
		logit.String("pkgIds", base_utils.Format(pkgIds)), logit.String("newestPkgNames", base_utils.Format(newestPkgNames)))
	if len(newestPkgNames) > 0 {
		newestPkgs, err := x1model.GetLatestPackages(ctx, newestPkgNames, map[string]string{})
		if err != nil {
			return nil, nil, err
		}
		newestPkgs = util.MergeGreyBoxPkgs(ctx, &util.ParmasGetGreyBoxFilter{
			ServerID: serverID,
			AppID:    app.AppId,
			UserID:   app.UserId,
			VpcID:    app.VpcId,
		}, newestPkgs)
		pkgs = append(pkgs, newestPkgs...)
	}
	if len(pkgIds) > 0 {
		x1Resource, err := x1model.GetDbAgent(ctx)
		if err != nil {
			return nil, nil, err
		}
		var fvPkgs []*x1model.Package
		if err := x1Resource.GetAllByCond(ctx, &fvPkgs, "package_id IN ?", pkgIds); err != nil {
			resource.LoggerTask.Warning(ctx, "get package info fail", logit.Error("err", err))
			return nil, nil, err
		}
		pkgs = append(pkgs, fvPkgs...)
	}
	resource.LoggerTask.Trace(ctx, "pkgs to update", logit.String("pkgs", base_utils.Format(pkgs)))
	if len(pkgs) != len(pkgNames) {
		resource.LoggerTask.Warning(ctx, "get package info fail", logit.String("pkgs", base_utils.Format(pkgs)),
			logit.String("pkgNames", base_utils.Format(pkgNames)))
		return nil, nil, errors.New("get package info fail")
	}
	if err := x1model.UpgradeEntityPackegeRecord(ctx, serverID, pkgNames, pkgs, "task-"+taskId); err != nil {
		return nil, nil, err
	}
	if updateApp {
		if err := x1model.UpgradeEntityPackegeRecord(ctx, app.AppId, pkgNames, pkgs, "task-"+taskId); err != nil {
			return nil, nil, err
		}
	}
	return pkgNames, pkgs, nil
}

func updatePackageOne(ctx context.Context, app *x1model.Application, pkgInfos []*PackageInfo,
	deployParams *deploy.DeployNodeOfAllTypeParams, updateApp bool) error {
	pkgNames, pkgs, err := getToUpdatePkgs(ctx, app, pkgInfos, deployParams.ServerID, deployParams.TaskID, updateApp)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get to update pkgs fail", logit.Error("err", err))
		return err
	}
	xagentReq, err := deploy.GetXagentRequest(ctx, deployParams, pkgs)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get xagent request failed", logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "package manager xagent req", logit.String("req", base_utils.Format(xagentReq)))
	aCtx := xagent.Instance().DoAsync(ctx, xagentReq)
	resp, err := aCtx.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy node failed", logit.Error("err", err),
			logit.String("resp", base_utils.Format(resp)), logit.String("xagentreq", base_utils.Format(xagentReq)))
		return err
	}
	if err := x1model.SaveDeployTime(ctx, deployParams.ServerID, pkgNames, pkgs); err != nil {
		return err
	}
	if updateApp {
		if err := x1model.SaveDeployTime(ctx, app.AppId, pkgNames, pkgs); err != nil {
			return err
		}
	}
	return nil
}
