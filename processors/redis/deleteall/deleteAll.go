package deleteall

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type TaskParams struct {
	AppID string `json:"app_id,omitempty"`
}

func ProcessDeleteAll(ctx context.Context, teu *workflow.TaskExecUnit) error {
	clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model by app id failed", logit.Error("error", err))
		return err
	}
	if clusterModel.Status != csmaster_model_interface.CACHE_CLUSTER_DELETING {
		resource.LoggerTask.Warning(ctx, "cluster status not match", logit.Error("error", err))
		return nil
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	if err := deletePendingTasks(ctx, teu.Entity); err != nil {
		resource.LoggerTask.Warning(ctx, "delete pending tasks failed", logit.Error("error", err))
		return err
	}
	taskParams := &TaskParams{
		AppID: teu.Entity,
	}
	subTaskParam := &iface.CreateTaskParams{
		WorkFlow:   "scs-delete-all-instance-app",
		Schedule:   time.Now(),
		Timeout:    7 * 24 * time.Hour,
		Mutex:      "n_" + teu.Entity,
		Entity:     teu.Entity,
		Parameters: taskParams,
	}
	if err := resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{subTaskParam}); err != nil {
		resource.LoggerTask.Warning(ctx, "create sub tasks failed", logit.Error("error", err))
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	subTaskParam = &iface.CreateTaskParams{
		WorkFlow:   "scs-delete-all-ro-group-app",
		Schedule:   time.Now(),
		Timeout:    7 * 24 * time.Hour,
		Mutex:      "n_" + teu.Entity,
		Entity:     teu.Entity,
		Parameters: taskParams,
	}
	if app.Type == x1model.AppTypeStandalone {
		if err := resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*iface.CreateTaskParams{subTaskParam}); err != nil {
			resource.LoggerTask.Warning(ctx, "create sub tasks failed", logit.Error("error", err))
			return cerrs.ErrorTaskManual.Wrap(err)
		}
	}
	clusterModel.Status = csmaster_model_interface.CACHE_CLUSTER_DELETED
	if err := resource.CsmasterModel.FullSaveAssociationsSave(ctx, []*csmaster_model_interface.CacheCluster{clusterModel}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return cerrs.ErrorTaskManual.Wrap(err)
	}
	return nil
}

func deletePendingTasks(ctx context.Context, appID string) error {
	tasks, err := resource.TaskOperator.RetrieveTasks(ctx, "entity = ? and status not in ?", appID, []string{iface.TaskStatusSuccess, iface.TaskStatusError})
	if err != nil {
		resource.LoggerTask.Error(ctx, "retrieve tasks failed", logit.Error("error", err))
		return err
	}
	for _, t := range tasks {
		if t.WorkFlow == "scs-delete-cluster-app" || t.WorkFlow == "scs-delete-standalone-app" {
			continue
		}
		t.Status = "entity_deleted"
	}
	if err := resource.TaskOperator.UpdateTasks(ctx, tasks); err != nil {
		resource.LoggerTask.Error(ctx, "update tasks failed", logit.Error("error", err))
		return err
	}
	return nil
}
