package dns

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dns"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func ProcessAppDomainAddRecordsWithAz(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	for _, b := range app.BLBs {
		if b.Type != x1model.BLBTypeApp && b.Type != x1model.BLBTypeNormal {
			continue
		}
		if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
			continue
		}
		enableIpv6 := app.IpType == x1model.Ipv6
		bindIP := b.Ovip
		bindIPV6 := b.Ipv6
		if len(b.EndpointIp) != 0 {
			bindIP = b.EndpointIp
			enableIpv6 = false
			bindIPV6 = ""
		}
		createDomainParam := dns.CreateDomainParam{
			UserID:     app.UserId,
			VpcID:      app.VpcId,
			BindIp:     bindIP,
			Domain:     app.Domain,
			EnableIpv6: enableIpv6,
			BindIpIpv6: bindIPV6,
		}
		if err := dns.Instance().CreateDomain(ctx, &createDomainParam); err != nil {
			resource.LoggerTask.Warning(ctx, "create domain fail", logit.String("appId", app.AppId),
				logit.Error("ComponentError", err))
			return err
		}
	}
	return nil
}

func ProcessAppDomainDeleteRecordsWithAz(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	for _, b := range app.BLBs {
		if b.Type != x1model.BLBTypeApp {
			continue
		}
		if b.AzoneForCrossAzNearest == "" || b.AzoneForCrossAzNearest == x1model.GlobalForCrossAzNearest {
			continue
		}
		enableIpv6 := app.IpType == x1model.Ipv6
		bindIP := b.Ovip
		bindIPV6 := b.Ipv6
		if len(b.EndpointIp) != 0 {
			bindIP = b.EndpointIp
			enableIpv6 = false
			bindIPV6 = ""
		}
		deleteDomainParam := dns.DeleteDomainParam{
			UserID:     app.UserId,
			VpcID:      app.VpcId,
			BindIp:     bindIP,
			Domain:     app.Domain,
			EnableIpv6: enableIpv6,
			BindIpIpv6: bindIPV6,
		}
		if err := dns.Instance().DeleteDomain(ctx, &deleteDomainParam); err != nil {
			resource.LoggerTask.Warning(ctx, "delete domain fail", logit.String("appId", app.AppId),
				logit.Error("ComponentError", err))
			return err
		}
	}
	return nil
}
