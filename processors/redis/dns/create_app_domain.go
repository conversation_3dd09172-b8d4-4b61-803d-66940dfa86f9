/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
绑定app的blb到域名
*/

package dns

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/dns"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/common"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessCreateAppDomain 创建app的域名
// 1. 从interface表获取app的blb ip、ipv6 blb ip（如果app是ipv6的）
// 2. 将blb ip绑定domain
// 相关代码 DnsExecutorProcessor::bind_cluster_domain
func ProcessCreateAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("CreateAppDomain fail : teu is nilptr")
	}

	if privatecloud.IsPrivateENV() {
		resource.LoggerTask.Notice(ctx, "private env do not create domain")
		return nil
	}

	if common.IsEdgeRegion() {
		resource.LoggerTask.Notice(ctx, "edge region do not create domain")
		return nil
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	var blb *x1model.BLB
	for _, b := range app.BLBs {
		if b.Type == x1model.BLBTypeApp || b.Type == x1model.BLBTypeNormal {
			blb = b
		}
	}
	if blb == nil {
		return errors.New("no blb add to domain")
	}
	return createAppDomain(ctx, app, blb)
}

func createAppDomain(ctx context.Context, app *x1model.Application, blb *x1model.BLB) error {

	enableIpv6 := false
	if app.IpType == x1model.Ipv6 {
		enableIpv6 = true
	}

	bindIP := ""
	bindIPV6 := ""
	if len(blb.EndpointIp) != 0 {
		bindIP = blb.EndpointIp
		enableIpv6 = false
	} else {
		if blb.IpType == x1model.Ipv4 {
			bindIP = blb.Ovip
		}
		if blb.IpType == x1model.Ipv6 {
			bindIPV6 = blb.Ipv6
		}
	}

	createDomainParam := dns.CreateDomainParam{
		UserID:     app.UserId,
		VpcID:      app.VpcId,
		BindIp:     bindIP,
		Domain:     app.Domain,
		EnableIpv6: enableIpv6,
		BindIpIpv6: bindIPV6,
	}
	err := dns.Instance().CreateDomain(ctx, &createDomainParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create domain fail ", logit.String("appId", app.AppId),
			logit.Error("ComponentError", err))
		return err
	}

	// 让域名在公网环境可以解析
	err = CreateAppInetDomain(ctx, app.Domain, bindIP, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create inet domain fail ", logit.String("appId", app.AppId),
			logit.Error("ComponentError", err))
		return err
	}

	// todo actionrecordmodel ?
	return nil
}

// ProcessCreateRoGroupDomain process domain
func ProcessCreateRoGroupDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("DeleteAppDomain fail : teu is nilptr")
	}

	if privatecloud.IsPrivateENV() {
		resource.LoggerTask.Notice(ctx, "private env do not create domain")
		return nil
	}

	if common.IsEdgeRegion() {
		resource.LoggerTask.Notice(ctx, "edge region do not create domain")
		return nil
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	enableIpv6 := false
	if app.IpType == x1model.Ipv6 {
		enableIpv6 = true
	}

	if len(app.BLBs) == 0 {
		resource.LoggerTask.Warning(ctx, "no blb", logit.String("appId", teu.Entity))
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.CreateRoInstParams.RoGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// 只读组处于更新中状态，不再创建域名
	if roGroup.Status == x1model.RoGroupUpdating {
		return nil
	}

	bindIP := ""
	bindIPV6 := ""
	for _, blb := range app.BLBs {
		if blb.RoGroupID == roGroupID {
			if len(blb.EndpointIp) != 0 {
				bindIP = blb.EndpointIp
				enableIpv6 = false
				continue
			}
			if blb.IpType == x1model.Ipv4 {
				bindIP = blb.Ovip
			}
			if blb.IpType == x1model.Ipv6 {
				bindIPV6 = blb.Ipv6
			}
		}
	}

	createDomainParam := dns.CreateDomainParam{
		UserID:     app.UserId,
		VpcID:      app.VpcId,
		BindIp:     bindIP,
		Domain:     roGroup.Domain,
		EnableIpv6: enableIpv6,
		BindIpIpv6: bindIPV6,
	}
	err = dns.Instance().CreateDomain(ctx, &createDomainParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	// 让域名在公网环境可以解析
	err = CreateAppInetDomain(ctx, roGroup.Domain, bindIP, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create inet domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	return nil
}

func CreateAppInetDomain(ctx context.Context, domain string, bindIP string, userID string) (err error) {
	parts := strings.Split(domain, ".")
	if len(parts) <= 3 {
		resource.LoggerTask.Warning(ctx, "create inet domain faield invalid domain",
			logit.String("app.Domain", domain))
		return err
	}

	parts = parts[:len(parts)-3]
	name := strings.Join(parts, ".")

	createInetDomainParam := &dns.CreateInetDomainParam{
		Domain: name,
		Eip:    bindIP,
		UserID: userID,
	}
	if err := dns.Instance().CreateInetDomain(ctx, createInetDomainParam); err != nil {
		resource.LoggerTask.Warning(ctx, "create inet domain failed",
			logit.String("app.Domain", domain), logit.Error("ComponentError", err))
		return err
	}

	return nil
}

func ProcessAddToExchangeBlbToDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	var blb *x1model.BLB
	for _, b := range app.BLBs {
		if b.BlbId == "" || b.Status != x1model.BLBStatusAvailable {
			continue
		}
		if b.Type == x1model.BLBTypeAppToExchange {
			blb = b
		}
	}
	if blb == nil {
		return errors.New("no blb add to domain")
	}
	return createAppDomain(ctx, app, blb)
}
