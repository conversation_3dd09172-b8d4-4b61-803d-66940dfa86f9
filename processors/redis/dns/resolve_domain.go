/* Copyright 2024 Baidu Inc. All Rights Reserved. */
package dns

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/logit"
	blb_v2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

type resolveDomainParam struct {
	Domain string `json:"domain"`
}

type resolveDomainResp struct {
	IPs []string `json:"ips"`
}

func resolveDomainByXagent(ctx context.Context, domain, xagentIP string, xagentPort int) (*resolveDomainResp, error) {

	params := &resolveDomainParam{
		Domain: domain,
	}
	req := &xagent.AsyncRequest{
		Addr: &xagent.Addr{
			Host: xagentIP,
			Port: base_utils.ToInt32(xagentPort),
		},
		Action:     "dns_resolve",
		Params:     params,
		TimeoutSec: 30,
	}
	resource.LoggerTask.Notice(ctx, "dns resolve params",
		logit.String("dns_resolve_req", base_utils.Format(req)))
	resp, err := xagent.Instance().DoAsync(ctx, req).Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "dns resolve fail",
			logit.String("domain", domain),
			logit.String("xagentIp", xagentIP),
			logit.Int("xagentPort", xagentPort),
			logit.Error("err", err))
		return nil, err
	}
	resource.LoggerTask.Trace(ctx, "dns resolve success",
		logit.String("dns_resolve_req", base_utils.Format(req)),
		logit.String("dns_resolve_resp", base_utils.Format(resp)),
		logit.String("domain", domain),
		logit.String("xagentIp", xagentIP),
		logit.Int("xagentPort", xagentPort))

	var rsp *resolveDomainResp
	err = resp.ParseResult(&rsp)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "parse dns_resolve_resp err",
			logit.String("dns_resolve_resp", base_utils.Format(resp)),
			logit.Error("err", err))
		return nil, err
	}
	resource.LoggerTask.Trace(ctx, "parse dns_resolve_resp success",
		logit.String("dns_resolve_resp", base_utils.Format(rsp)))
	return rsp, nil
}

func processCheckToExchangeBlbInDomain(ctx context.Context, app *x1model.Application) error {

	expectIPs := make([]string, 0)
	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeNormal {
			expectIPs = append(expectIPs, blb.Ovip)
		}
		if blb.Type == x1model.BLBTypeApp {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			if blb.EndpointId == "" {
				expectIPs = append(expectIPs, blb.Ovip)
			} else {
				expectIPs = append(expectIPs, blb.EndpointIp)
			}
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			if blb.EndpointId == "" {
				expectIPs = append(expectIPs, blb.Ovip)
			} else {
				expectIPs = append(expectIPs, blb.EndpointIp)
			}
		}
	}

	if len(expectIPs) == 0 {
		resource.LoggerTask.Notice(ctx, "no ipv4 ip found, skip")
		return nil
	}

	var checkNode *x1model.Node
	// 获取第一个节点验证域名生效
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && len(cluster.Nodes) > 1 {
				continue
			}
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			checkNode = node
		}
		if checkNode != nil {
			break
		}
	}

	if checkNode == nil {
		resource.LoggerTask.Warning(ctx, "no node found", logit.String("appId", app.AppId))
		return fmt.Errorf("no node found")
	}

	rsp, err := resolveDomainByXagent(ctx, app.Domain, checkNode.FloatingIP, checkNode.XagentPort)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resolve domain fail", logit.String("domain", app.Domain),
			logit.Error("err", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, "resolve domain success",
		logit.String("ips", base_utils.Format(rsp.IPs)),
		logit.String("expectIPs", base_utils.Format(expectIPs)))
	for _, ip := range expectIPs {
		in, err := base_utils.InArray(ip, rsp.IPs)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "ip not in ips",
				logit.String("ip", ip), logit.String("ips", base_utils.Format(rsp.IPs)),
				logit.Error("err", err))
			return err
		}
		if !in {
			resource.LoggerTask.Warning(ctx, "ip not in ips",
				logit.String("ip", ip), logit.String("ips", base_utils.Format(rsp.IPs)))
			return fmt.Errorf("ip(%s) not in ips(%s)", ip, base_utils.Format(rsp.IPs))
		}
	}
	return nil
}

// ProcessCheckToExchangeBlbInDomain 检查toexchange的blb是否在域名下
func ProcessCheckToExchangeBlbInDomain(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	env := blb_v2.Instance().GetEnv(ctx)
	if app.ResourceType == "container" && app.UserId != env.ResourcePrivateUserId {
		//跳过等待,缩短生效判断时间
		//time.Sleep(10 * time.Second)
		resource.LoggerTask.Notice(ctx, "cannot check blb in domain for container, skip")
		return nil
	}
	return processCheckToExchangeBlbInDomain(ctx, app)

}

func processOnlyToExchangeBlbInDomain(ctx context.Context, app *x1model.Application) error {

	expectIPs := make([]string, 0)

	for _, blb := range app.BLBs {
		if blb.IpType != x1model.Ipv4 {
			continue
		}
		if blb.Type == x1model.BLBTypeAppToExchange {
			if blb.BlbId == "" || blb.Status != x1model.BLBStatusAvailable {
				resource.LoggerTask.Warning(ctx, "blb not available", logit.String("blbId", blb.BlbId))
				return fmt.Errorf("blbid(%s) not available", base_utils.Format(blb.Id))
			}
			if blb.EndpointId == "" {
				expectIPs = append(expectIPs, blb.Ovip)
			} else {
				expectIPs = append(expectIPs, blb.EndpointIp)
			}
		}
	}

	if len(expectIPs) == 0 {
		resource.LoggerTask.Notice(ctx, "no ipv4 ip found, skip")
		return nil
	}

	var checkNode *x1model.Node
	// 获取第一个节点验证域名生效
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeMaster && len(cluster.Nodes) > 1 {
				continue
			}
			if node.Status != x1model.NodeOrProxyStatusInUse {
				continue
			}
			checkNode = node
		}
		if checkNode != nil {
			break
		}
	}

	if checkNode == nil {
		resource.LoggerTask.Warning(ctx, "no node found", logit.String("appId", app.AppId))
		return fmt.Errorf("no node found")
	}

	rsp, err := resolveDomainByXagent(ctx, app.Domain, checkNode.FloatingIP, checkNode.XagentPort)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "resolve domain fail", logit.String("domain", app.Domain),
			logit.Error("err", err))
		return err
	}

	resource.LoggerTask.Notice(ctx, "resolve domain success",
		logit.String("ips", base_utils.Format(rsp.IPs)),
		logit.String("expectIPs", base_utils.Format(expectIPs)))
	for _, ip := range expectIPs {
		in, err := base_utils.InArray(ip, rsp.IPs)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "ip not in ips",
				logit.String("ip", ip), logit.String("ips", base_utils.Format(rsp.IPs)),
				logit.Error("err", err))
			return err
		}
		if !in {
			resource.LoggerTask.Warning(ctx, "ip not in ips",
				logit.String("ip", ip), logit.String("ips", base_utils.Format(rsp.IPs)))
			return fmt.Errorf("ip(%s) not in ips(%s)", ip, base_utils.Format(rsp.IPs))
		}
	}
	return nil
}

// ProcessCheckOnlyToExchangeBlbInDomain 检测域名下只有appToExchange的blb
func ProcessCheckOnlyToExchangeBlbInDomain(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	env := blb_v2.Instance().GetEnv(ctx)
	if app.ResourceType == "container" && app.UserId != env.ResourcePrivateUserId {
		//跳过等待,缩短生效判断时间
		//time.Sleep(10 * time.Second)
		resource.LoggerTask.Notice(ctx, "cannot check blb in domain for container, skip")
		return nil
	}
	return processOnlyToExchangeBlbInDomain(ctx, app)
}
