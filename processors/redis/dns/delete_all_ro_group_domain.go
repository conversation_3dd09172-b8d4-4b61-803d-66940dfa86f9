package dns

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	"icode.baidu.com/baidu/scs/x1-base/component/dns"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessDeleteAllRoGroupDomain 删除只读组内网域名和公网域名
func ProcessDeleteAllRoGroupDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("DeleteAppDomain fail : teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	enableIpv6 := false
	if app.IpType == x1model.Ipv6 {
		enableIpv6 = true
	}

	if len(app.BLBs) == 0 {
		resource.LoggerTask.Warning(ctx, "no blb", logit.String("appId", teu.Entity))
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	roGroups, err := resource.CsmasterOpAgent.GetReadonlyGroupsByClusterID(ctx, param.AppID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if len(roGroups) == 0 {
		return nil
	}

	for _, roGroup := range roGroups {
		bindIP := ""
		bindIPV6 := ""
		for _, blb := range app.BLBs {
			if blb.RoGroupID == roGroup.RoGroupShowID {
				if len(blb.EndpointIp) != 0 {
					bindIP = blb.EndpointIp
					enableIpv6 = false
					continue
				}
				if blb.IpType == x1model.Ipv4 {
					bindIP = blb.Ovip
				}
				if blb.IpType == x1model.Ipv6 {
					bindIPV6 = blb.Ipv6
				}
			}
		}

		// 删除域名在公网环境的解析
		err = DeleteAppInetDomain(ctx, roGroup.Domain, bindIP, app.UserId)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "delete inet domain fail ", logit.String("appId", teu.Entity),
				logit.Error("ComponentError", err))
			return err
		}

		deleteDomainParam := dns.DeleteDomainParam{
			UserID:     app.UserId,
			VpcID:      app.VpcId,
			BindIp:     bindIP,
			Domain:     roGroup.Domain,
			EnableIpv6: enableIpv6,
			BindIpIpv6: bindIPV6,
		}
		err = dns.Instance().DeleteDomain(ctx, &deleteDomainParam)
		if err != nil {
			resource.LoggerTask.Warning(ctx, "delete domain fail ", logit.String("appId", teu.Entity),
				logit.Error("ComponentError", err))
			return err
		}

		// 删除只读组公网域名&&解绑eip
		if len(roGroup.PublicDomain) != 0 && len(roGroup.Eip) != 0 {
			unbindEipParams := &blb.UnbindEipParams{
				UserID: app.UserId,
				Eip:    roGroup.Eip,
			}

			if err := blb.Instance().UnbindEip(ctx, unbindEipParams); err != nil {
				resource.LoggerTask.Warning(ctx, "unbind eip fail", logit.String("unbindEipParam", base_utils.Format(unbindEipParams)))
				return err
			}

			// 需要处理domain
			publicDomain := strings.Split(roGroup.PublicDomain, ".")
			domain := strings.Join(publicDomain[:len(publicDomain)-2], ".")
			deleteInetDomainParam := &dns.DeleteInetDomainParam{
				Domain: domain,
				Eip:    roGroup.Eip,
				UserID: app.UserId,
			}
			if err := dns.Instance().DeleteInetDomain(ctx, deleteInetDomainParam); err != nil {
				resource.LoggerTask.Warning(ctx, "delete public domain fail", logit.String("domain", domain))
				return err
			}
		}
	}

	return nil
}
