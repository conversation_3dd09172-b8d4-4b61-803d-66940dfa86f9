/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/*
modification history
--------------------
2022/09/08, by <PERSON><PERSON><PERSON>(wang<PERSON><EMAIL>), create
*/

/*
DESCRIPTION
更新数据库中的域名记录
*/

package dns

import (
	"context"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	csdk "icode.baidu.com/baidu/scs/x1-base/sdk/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/errors"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessCreateAppDomain 创建app的域名
// 交换 application 中的 domain 记录
// 更新 Csmaster
func ProcessSwitchAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	// (1) 获取
	param, err := iface.GetSwitchServiceParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	oldApp, err := x1model.ApplicationGetByAppId(ctx, param.OldAppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", param.OldAppId), logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	newApp, err := x1model.ApplicationGetByAppId(ctx, param.NewAppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", param.NewAppId), logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// (2) 交换 application 中的 domain 记录
	err = saveDb(ctx, oldApp, newApp)
	if err != nil {
		return err
	}

	// (3) 更新 Csmaster
	err = updateCsmasterDomain(ctx, oldApp)
	if err != nil {
		return err
	}
	err = updateCsmasterDomain(ctx, newApp)
	if err != nil {
		return err
	}
	return nil
}

func saveDb(ctx context.Context, oldApp *x1model.Application, newApp *x1model.Application) error {
	var changedAppList []*x1model.Application
	var tmpDomain string

	tmpDomain = oldApp.Domain
	oldApp.Domain = newApp.Domain
	newApp.Domain = tmpDomain

	changedAppList = append(changedAppList, oldApp)
	changedAppList = append(changedAppList, newApp)

	// 更新数据库
	if err := x1model.ApplicationsSave(ctx, changedAppList); err != nil {
		resource.LoggerTask.Warning(ctx, "save app fail", logit.Error("error", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}
	return nil
}

func updateCsmasterDomain(ctx context.Context, app *x1model.Application) error {
	req := &csmaster.UpdateClusterModelParams{
		Model:  &csdk.CsmasterCluster{},
		UserID: app.UserId,
		AppID:  app.AppId,
	}

	req.Model.Domain = app.Domain

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, req); err != nil {
		resource.LoggerTask.Warning(ctx, "fail to update csmaster cluster model",
			logit.String("userId", app.UserId), logit.String("appId", app.AppId),
			logit.Error("opError", err))
		return errors.UpdateCsmasterClusterModelFail.Wrap(err)
	}
	return nil
}
