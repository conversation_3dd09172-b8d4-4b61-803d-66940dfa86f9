package dns

import (
	"context"
	"strings"

	blb_v2 "icode.baidu.com/baidu/scs/x1-base/component/blb_v2"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/blb"
	"icode.baidu.com/baidu/scs/x1-base/component/dns"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessDeleteAppDomain 删除app域名
func ProcessDeleteAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("DeleteAppDomain fail : teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}
	var toDeleteBlb *x1model.BLB
	for _, blb := range app.BLBs {
		if blb.Type == x1model.BLBTypeNormal || blb.Type == x1model.BLBTypeApp {
			toDeleteBlb = blb
		}
	}
	if toDeleteBlb == nil {
		resource.LoggerTask.Warning(ctx, "no blb to delete", logit.String("appId", teu.Entity))
		return errors.New("no blb to delete")
	}

	// todo actionrecordmodel ?
	return DeleteBlbFromAppDomain(ctx, app, toDeleteBlb)
}

// ProcessDeleteRoGroupDomain 删除只读组内网域名和公网域名
func ProcessDeleteRoGroupDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("DeleteAppDomain fail : teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	enableIpv6 := false
	if app.IpType == x1model.Ipv6 {
		enableIpv6 = true
	}

	if len(app.BLBs) == 0 {
		resource.LoggerTask.Warning(ctx, "no blb", logit.String("appId", teu.Entity))
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 获取只读组
	roGroupID := param.DeleteRoGroupParams.ReadonlyGroupID
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	bindIP := ""
	bindIPV6 := ""
	for _, blb := range app.BLBs {
		if blb.RoGroupID == roGroupID {
			if len(blb.EndpointIp) != 0 {
				bindIP = blb.EndpointIp
				enableIpv6 = false
				continue
			}
			if blb.IpType == x1model.Ipv4 {
				bindIP = blb.Ovip
			}
			if blb.IpType == x1model.Ipv6 {
				bindIPV6 = blb.Ipv6
			}
		}
	}

	// 删除域名在公网环境的解析
	err = DeleteAppInetDomain(ctx, app.Domain, bindIP, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete inet domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	deleteDomainParam := dns.DeleteDomainParam{
		UserID:     app.UserId,
		VpcID:      app.VpcId,
		BindIp:     bindIP,
		Domain:     roGroup.Domain,
		EnableIpv6: enableIpv6,
		BindIpIpv6: bindIPV6,
	}
	err = dns.Instance().DeleteDomain(ctx, &deleteDomainParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete domain fail ", logit.String("appId", teu.Entity),
			logit.Error("ComponentError", err))
		return err
	}

	// 删除只读组公网域名&&解绑eip
	if len(roGroup.PublicDomain) != 0 && len(roGroup.Eip) != 0 {
		unbindEipParams := &blb.UnbindEipParams{
			UserID: app.UserId,
			Eip:    roGroup.Eip,
		}

		if err := blb.Instance().UnbindEip(ctx, unbindEipParams); err != nil {
			resource.LoggerTask.Warning(ctx, "unbind eip fail", logit.String("unbindEipParam", base_utils.Format(unbindEipParams)))
			return err
		}

		// 需要处理domain
		publicDomain := strings.Split(roGroup.PublicDomain, ".")
		domain := strings.Join(publicDomain[:len(publicDomain)-2], ".")
		deleteInetDomainParam := &dns.DeleteInetDomainParam{
			Domain: domain,
			Eip:    roGroup.Eip,
			UserID: app.UserId,
		}
		if err := dns.Instance().DeleteInetDomain(ctx, deleteInetDomainParam); err != nil {
			resource.LoggerTask.Warning(ctx, "delete public domain fail", logit.String("domain", domain))
			return err
		}
	}

	return nil
}

func DeleteAppInetDomain(ctx context.Context, domain string, bindIP string, userID string) (err error) {
	parts := strings.Split(domain, ".")
	if len(parts) <= 3 {
		resource.LoggerTask.Warning(ctx, "delete inet domain faield invalid domain",
			logit.String("app.Domain", domain))
		return nil
	}
	parts = parts[:len(parts)-3]
	name := strings.Join(parts, ".")

	deleteInetDomainParam := &dns.DeleteInetDomainParam{
		Domain: name,
		Eip:    bindIP,
		UserID: userID,
	}
	if err := dns.Instance().DeleteInetDomain(ctx, deleteInetDomainParam); err != nil {
		resource.LoggerTask.Warning(ctx, "delete inet domain failed",
			logit.String("app.Domain", domain), logit.Error("ComponentError", err))
		return nil
	}

	return nil
}

func DeleteBlbFromAppDomain(ctx context.Context, app *x1model.Application, blb *x1model.BLB) (err error) {

	bindIP := ""
	bindIPV6 := ""
	enableIpv6 := false
	if app.IpType == x1model.Ipv6 && len(blb.EndpointIp) == 0 {
		enableIpv6 = true
	}

	if len(blb.EndpointIp) != 0 {
		bindIP = blb.EndpointIp
	} else {
		if blb.IpType == x1model.Ipv4 {
			bindIP = blb.Ovip
		}
		if blb.IpType == x1model.Ipv6 {
			bindIPV6 = blb.Ipv6
		}
	}

	if bindIP == "" && bindIPV6 == "" {
		resource.LoggerTask.Warning(ctx, "no blb to delete", logit.String("appId", blb.AppId))
		return errors.New("no blb to delete")
	}
	// 删除域名在公网环境的解析
	err = DeleteAppInetDomain(ctx, app.Domain, bindIP, app.UserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete inet domain fail ", logit.String("appId", app.AppId),
			logit.Error("ComponentError", err))
		return err
	}

	deleteDomainParam := dns.DeleteDomainParam{
		UserID:     app.UserId,
		VpcID:      app.VpcId,
		BindIp:     bindIP,
		Domain:     app.Domain,
		EnableIpv6: enableIpv6,
		BindIpIpv6: bindIPV6,
	}
	err = dns.Instance().DeleteDomain(ctx, &deleteDomainParam)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "delete domain fail ", logit.String("appId", app.AppId),
			logit.Error("ComponentError", err))
		return err
	}
	return nil
}

// ProcessDeleteOldBlbFromAppDomain 从域名中删除旧blb
func ProcessDeleteOldBlbFromAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	var toDeleteBlb *x1model.BLB
	for _, b := range app.BLBs {
		if b.Type == x1model.BLBTypeNormal || b.Type == x1model.BLBTypeApp {
			toDeleteBlb = b
		}
	}
	if toDeleteBlb == nil {
		resource.LoggerTask.Warning(ctx, "no blb to delete", logit.String("appId", teu.Entity))
		return errors.New("no blb to delete")
	}
	return DeleteBlbFromAppDomain(ctx, app, toDeleteBlb)
}

// ProcessDeleteToExchangeBlbFromAppDomain 从域名中删除toexchange的blb
func ProcessDeleteToExchangeBlbFromAppDomain(ctx context.Context, teu *workflow.TaskExecUnit) (err error) {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return err
	}

	var toDeleteBlb *x1model.BLB
	for _, b := range app.BLBs {
		if b.Type == x1model.BLBTypeAppToExchange {
			toDeleteBlb = b
		}
	}
	if toDeleteBlb == nil {
		resource.LoggerTask.Warning(ctx, "no blb to delete", logit.String("appId", teu.Entity))
		return errors.New("no blb to delete")
	}
	env := blb_v2.Instance().GetEnv(ctx)
	if app.UserId != env.ResourcePrivateUserId &&
		app.ResourceType == "container" && toDeleteBlb.EndpointId == "" {
		//还未初始化
		resource.LoggerTask.Warning(ctx, "no blb need to delete from domain",
			logit.String("blb", base_utils.Format(toDeleteBlb)))
		return nil
	}
	if toDeleteBlb.BlbId == "" || toDeleteBlb.Status != x1model.BLBStatusAvailable {
		//还未初始化
		resource.LoggerTask.Warning(ctx, "no blb need to delete from domain",
			logit.String("blb", base_utils.Format(toDeleteBlb)))
		return nil
	}
	return DeleteBlbFromAppDomain(ctx, app, toDeleteBlb)
}
