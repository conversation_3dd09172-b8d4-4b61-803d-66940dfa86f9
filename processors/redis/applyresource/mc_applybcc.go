/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/25
 * File: mc_applybcc.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package applyresource TODO package function desc
package applyresource

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
)

// ProcessApplyMcBccResources 申请bcc资源
// 1. 遍历所有cluster、interface中需要新建的node、proxy; 生成bcc创建请求(对应CreateBccResourceParams)
// 2. 调用 bccresource.CreateBccResources 发送创建请求
// 3. 调用 bccresource.ShowBccResources 查询资源
// 4. 将资源信息写入到X1数据库中
// 5. 将对应的node、proxy写入到csmaster cache_instance表中
func ProcessApplyMcBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		return fmt.Errorf("parse param fail,err:%w", err)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	// 获取集群部署集策略
	PAASDeploySetStrategies, err := getPAASDeploySetStrategies(ctx, app.UserId, app.DeploySetIds)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deployset detail failed", logit.String("deploySetIDs", app.DeploySetIds), logit.Error("error", err))

	}
	aZoneReqMap, aZoneOrderMap := generateMcApplyBccRequest(app, PAASDeploySetStrategies)
	err = applyMcBccResource(ctx, teu, aZoneReqMap, app, aZoneOrderMap, param, err)
	if err != nil {
		return err
	}
	if err := queryResource(ctx, teu, aZoneOrderMap, app); err != nil {
		return fmt.Errorf("query bcc resources failed,err:%w", err)
	}
	return nil
}

func generateMcApplyBccRequest(app *x1model.Application, PAASDeploySetStrategies []bccresource.PAASDeploySetStrategy) (
	map[string]*bccresource.CreateBccResourceParams, map[string]string) {
	aZoneReqMap := make(map[string]*bccresource.CreateBccResourceParams)
	aZoneOrderMap := make(map[string]string)
	fillNodesToRequestsMap(aZoneReqMap, app, PAASDeploySetStrategies)
	fillProxysToRequestsMap(aZoneReqMap, app, PAASDeploySetStrategies)
	return aZoneReqMap, aZoneOrderMap
}

func fillNodesToRequestsMap(aZoneReqMap map[string]*bccresource.CreateBccResourceParams, app *x1model.Application,
	PAASDeploySetStrategies []bccresource.PAASDeploySetStrategy) {
	internalSecurityGroupId := app.SecurityGroupId
	if len(app.InternalSecurityGroupId) != 0 {
		internalSecurityGroupId = app.InternalSecurityGroupId
	}
	for _, cluster := range app.Clusters {
		clusterItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}
		for _, node := range cluster.Nodes {
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[node.Azone]; !found {
				aZoneReqMap[node.Azone] = &bccresource.CreateBccResourceParams{
					AppID:                   app.AppId,
					UserID:                  app.UserId,
					Product:                 app.Product,
					ImageID:                 getImageId(app),
					VpcID:                   app.VpcId,
					PAASDeploySetStrategies: PAASDeploySetStrategies,
					LogicalZone:             node.LogicZone,
					Azone:                   node.Azone,
					StoreType:               map[string]string{cluster.Engine: cluster.StoreType},
				}
			} else {
				if aZoneReqMap[node.Azone].StoreType == nil {
					aZoneReqMap[node.Azone].StoreType = make(map[string]string)
				}
				aZoneReqMap[node.Azone].StoreType[cluster.Engine] = cluster.StoreType
			}
			if _, found := clusterItemMap[node.Azone]; !found {
				aZoneReqMap[node.Azone].Items = append(aZoneReqMap[node.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      cluster.AvailableVolume,
						CPUCount:             cluster.Cpu,
						MemoryCapacityInGB:   cluster.MemSize,
						RootDiskCapacityInGB: cluster.SysDiskSize,
						DataDiskCapacityInGB: int(cluster.DiskSize),
					},
					Subnet:          node.SubnetId,
					Engine:          cluster.Engine,
					Count:           1,
					DeploySetID:     getClusterDeploySetId(app.AppId, app.AppName, cluster.ClusterId),
					SecurityGroupID: internalSecurityGroupId,
					EntityIDs:       []string{node.NodeId},
				})
				clusterItemMap[node.Azone] = &(aZoneReqMap[node.Azone].Items[len(aZoneReqMap[node.Azone].Items)-1])
			} else {
				clusterItemMap[node.Azone].Count++
				clusterItemMap[node.Azone].EntityIDs = append(clusterItemMap[node.Azone].EntityIDs, node.NodeId)
			}
		}
	}
}

func fillProxysToRequestsMap(aZoneReqMap map[string]*bccresource.CreateBccResourceParams, app *x1model.Application,
	PAASDeploySetStrategies []bccresource.PAASDeploySetStrategy) {
	for _, itf := range app.Interfaces {
		itfItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}
		for _, proxy := range itf.Proxys {
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[proxy.Azone]; !found {
				aZoneReqMap[proxy.Azone] = &bccresource.CreateBccResourceParams{
					AppID:                   app.AppId,
					UserID:                  app.UserId,
					Product:                 app.Product,
					ImageID:                 getImageId(app),
					VpcID:                   app.VpcId,
					PAASDeploySetStrategies: PAASDeploySetStrategies,
					LogicalZone:             proxy.LogicZone,
					Azone:                   proxy.Azone,
					StoreType:               map[string]string{itf.Engine: itf.StoreType},
				}
			} else {
				if aZoneReqMap[proxy.Azone].StoreType == nil {
					aZoneReqMap[proxy.Azone].StoreType = make(map[string]string)
				}
				aZoneReqMap[proxy.Azone].StoreType[itf.Engine] = itf.StoreType
			}
			if _, found := itfItemMap[proxy.Azone]; !found {
				aZoneReqMap[proxy.Azone].Items = append(aZoneReqMap[proxy.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      itf.AvailableVolume,
						CPUCount:             itf.Cpu,
						MemoryCapacityInGB:   itf.MemSize,
						RootDiskCapacityInGB: itf.SysDiskSize,
						DataDiskCapacityInGB: int(itf.DiskSize),
					},
					Subnet:          proxy.SubnetId,
					Engine:          itf.Engine,
					Count:           1,
					DeploySetID:     getClusterItfDeploySetId(app.AppId, app.AppName, itf.InterfaceId),
					SecurityGroupID: app.SecurityGroupId,
					EntityIDs:       []string{proxy.ProxyId},
				})
				itfItemMap[proxy.Azone] = &(aZoneReqMap[proxy.Azone].Items[len(aZoneReqMap[proxy.Azone].Items)-1])
			} else {
				itfItemMap[proxy.Azone].Count++
				itfItemMap[proxy.Azone].EntityIDs = append(itfItemMap[proxy.Azone].EntityIDs, proxy.ProxyId)
			}
		}
	}
}

func applyMcBccResource(ctx context.Context, teu *workflow.TaskExecUnit, aZoneReqMap map[string]*bccresource.CreateBccResourceParams,
	app *x1model.Application, aZoneOrderMap map[string]string, param *iface.Parameters, err error) error {
	var reqErr error
	var errReq *bccresource.CreateBccResourceParams
	for aZone, request := range aZoneReqMap {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("zone %s request is %+v", aZone, *request))
		var orderId string
		var err error
		orderId, err = loadSucceededOrder(ctx, app.AppId, teu.TaskID, aZone)
		if err == nil && len(orderId) != 0 {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("zone %s has succeeded order %s", aZone, orderId))
			aZoneOrderMap[aZone] = orderId
			continue
		}
		request.Retry = getRetry(teu.TaskBatchID)
		request.TaskID = teu.TaskID
		orderId, err = bccresource.BccResourceOp().CreateBccResources(ctx, request)
		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("apply for bcc resources of azone %s failed", aZone), logit.Error("error", err))
			reqErr = err
			errReq = request
			break
		}
		if err := saveSucceededOrder(ctx, app.AppId, teu.TaskID, aZone, orderId); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("save bcc resource order id of azone %s failed", aZone), logit.Error("error", err))
		}
		aZoneOrderMap[aZone] = orderId
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("azone orderid map is %+v", aZoneOrderMap))
	if err := saveReousrceOrderIds(ctx, aZoneOrderMap, app); err != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("save resources orders into db failed, order: %+v", aZoneOrderMap), logit.Error("error", err))
		return err
	}
	if reqErr != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("send bcc resources request failed, request: %+v", errReq),
			logit.Error("error", err), logit.Int("error_code", cerrs.Code(reqErr)))
		// 对于scs-unit-migrate任务，如果创建资源失败，直接返回错误，不会进入manual
		if strings.Contains(getStep(teu.TaskBatchID), "scs-unit-migrate") {
			return fmt.Errorf(reqErr.Error())
		}
		return reqErr
	}
	return nil
}
