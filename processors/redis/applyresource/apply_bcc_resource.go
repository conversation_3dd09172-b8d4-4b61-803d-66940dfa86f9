/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
申请资源
*/

package applyresource

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/bccresource"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/component/x1resource"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/privatecloud"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func getClusterDeploySetId(appId string, appName string, clusterId string) string {
	clusterIdChunks := strings.Split(clusterId, appId)
	if len(clusterIdChunks) != 2 {
		return clusterId
	}
	return appId + "-" + strings.ReplaceAll(appName, "-", "_") + strings.ReplaceAll(clusterIdChunks[1], "-", "_")
}

func getClusterItfDeploySetId(appId string, appName string, interface_id string) string {
	itfIdChunks := strings.Split(interface_id, appId)
	if len(itfIdChunks) != 2 {
		return interface_id
	}
	return appId + "-" + strings.ReplaceAll(appName, "-", "_") + strings.ReplaceAll(itfIdChunks[1], "-", "_")
}

func getPAASDeploySetStrategies(ctx context.Context, userID string, appDeploySetIDs string) ([]bccresource.PAASDeploySetStrategy, error) {
	ret := []bccresource.PAASDeploySetStrategy{}
	for _, deploySetID := range strings.Split(appDeploySetIDs, ",") {
		if len(deploySetID) != 0 {
			dset, err := x1model.DeploySetGet(ctx, userID, deploySetID)
			if err != nil {
				return nil, err
			}

			ret = append(ret, bccresource.PAASDeploySetStrategy{
				Concurrency: dset.Concurrency,
				ID:          "scs-" + deploySetID,
				Policy:      dset.Strategy,
				Quota:       300000,
			})
		}
	}
	return ret, nil

}

func skipMergeMap() bool {
	if privatecloud.IsPrivateENV() {
		switch privatecloud.GetPrivateEnvType() {
		case privatecloud.EnvLiTest, privatecloud.EnvLiProd:
			return true
		}
	}
	return false
}

func fillClusterToRequestsMap(ctx context.Context, aZoneReqMap map[string]*bccresource.CreateBccResourceParams,
	app *x1model.Application, isFailover bool, PAASDeploySetStrategies []bccresource.PAASDeploySetStrategy) error {
	// (1) 标准版 Redis 使用 SecurityGroupId, 集群版 Redis 使用 InternalSecurityGroupId
	// (2) 部分从 csmaster 迁移到 x1 的集群版 Redis 使用的 SecurityGroupId
	internalSecurityGroupId := app.SecurityGroupId
	if len(app.InternalSecurityGroupId) != 0 && app.Type == x1model.AppTypeCluster {
		internalSecurityGroupId = app.InternalSecurityGroupId
	}
	for _, cluster := range app.Clusters {
		param := &specification.GetSpecificationParams{
			UserID:    app.UserId,
			Name:      cluster.DestSpec,
			Engine:    cluster.Engine,
			StoreType: cluster.StoreType,
			AppType:   app.Type,
		}
		spec, err := specification.GetSpecification(ctx, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
			return err
		}
		clusterItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}
		clusterToFakeDeleteInstanceMap := map[string][]string{}
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				clusterToFakeDeleteInstanceMap[node.Azone] = append(clusterToFakeDeleteInstanceMap[node.Azone], node.ResourceId)
			}
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[node.Azone]; !found {
				aZoneReqMap[node.Azone] = &bccresource.CreateBccResourceParams{
					AppID:                   app.AppId,
					UserID:                  app.UserId,
					Product:                 app.Product,
					ImageID:                 getImageId(app),
					VpcID:                   app.VpcId,
					PAASDeploySetStrategies: PAASDeploySetStrategies,
					LogicalZone:             node.LogicZone,
					Azone:                   node.Azone,
					StoreType:               map[string]string{cluster.Engine: cluster.StoreType},
				}
			} else {
				if aZoneReqMap[node.Azone].StoreType == nil {
					aZoneReqMap[node.Azone].StoreType = make(map[string]string)
				}
				aZoneReqMap[node.Azone].StoreType[cluster.Engine] = cluster.StoreType
			}
			if _, found := clusterItemMap[node.Azone]; !found {
				aZoneReqMap[node.Azone].Items = append(aZoneReqMap[node.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      cluster.AvailableVolume,
						CPUCount:             cluster.Cpu,
						MemoryCapacityInGB:   cluster.MemSize,
						RootDiskCapacityInGB: cluster.SysDiskSize,
						DataDiskCapacityInGB: int(cluster.DiskSize),
						Name:                 spec.Name,
					},
					Subnet:          node.SubnetId,
					Engine:          cluster.Engine,
					Count:           1,
					DeploySetID:     getClusterDeploySetId(app.AppId, app.AppName, cluster.ClusterId),
					SecurityGroupID: internalSecurityGroupId,
					EntityIDs:       []string{node.NodeId},
				})
				// 不是私有云才用map合并
				if !skipMergeMap() {
					clusterItemMap[node.Azone] = &(aZoneReqMap[node.Azone].Items[len(aZoneReqMap[node.Azone].Items)-1])
				}
			} else {
				clusterItemMap[node.Azone].Count++
				clusterItemMap[node.Azone].EntityIDs = append(clusterItemMap[node.Azone].EntityIDs, node.NodeId)
			}
		}
		// 填充只读实例的创建请求
		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				clusterToFakeDeleteInstanceMap[node.Azone] = append(clusterToFakeDeleteInstanceMap[node.Azone], node.ResourceId)
			}
			if node.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[node.Azone]; !found {
				aZoneReqMap[node.Azone] = &bccresource.CreateBccResourceParams{
					AppID:                   app.AppId,
					UserID:                  app.UserId,
					Product:                 app.Product,
					ImageID:                 getImageId(app),
					VpcID:                   app.VpcId,
					PAASDeploySetStrategies: PAASDeploySetStrategies,
					LogicalZone:             node.LogicZone,
					Azone:                   node.Azone,
					StoreType:               map[string]string{cluster.Engine: cluster.StoreType},
				}
			} else {
				if aZoneReqMap[node.Azone].StoreType == nil {
					aZoneReqMap[node.Azone].StoreType = make(map[string]string)
				}
				aZoneReqMap[node.Azone].StoreType[cluster.Engine] = cluster.StoreType
			}
			if _, found := clusterItemMap[node.Azone]; !found {
				aZoneReqMap[node.Azone].Items = append(aZoneReqMap[node.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      cluster.AvailableVolume,
						CPUCount:             cluster.Cpu,
						MemoryCapacityInGB:   cluster.MemSize,
						RootDiskCapacityInGB: cluster.SysDiskSize,
						DataDiskCapacityInGB: int(cluster.DiskSize),
					},
					Subnet:          node.SubnetId,
					Engine:          cluster.Engine,
					Count:           1,
					DeploySetID:     getClusterDeploySetId(app.AppId, app.AppName, cluster.ClusterId),
					SecurityGroupID: internalSecurityGroupId,
					EntityIDs:       []string{node.NodeId},
				})
				if !skipMergeMap() {
					clusterItemMap[node.Azone] = &(aZoneReqMap[node.Azone].Items[len(aZoneReqMap[node.Azone].Items)-1])
				}
			} else {
				clusterItemMap[node.Azone].Count++
				clusterItemMap[node.Azone].EntityIDs = append(clusterItemMap[node.Azone].EntityIDs, node.NodeId)
			}
		}

		// for 自愈场景
		if isFailover {
			for azone := range clusterToFakeDeleteInstanceMap {
				aZoneReqMap[azone].Items[len(aZoneReqMap[azone].Items)-1].ReplacedInstanceIDs = clusterToFakeDeleteInstanceMap[azone]
			}
		}
	}
	return nil
}

func getImageId(app *x1model.Application) string {
	imageId, _ := util.GetImageIdAndVersion(app.ImageID)
	return imageId
}

func fillInterfaceToRequestsMap(ctx context.Context, aZoneReqMap map[string]*bccresource.CreateBccResourceParams,
	app *x1model.Application, isFailover bool, PAASDeploySetStrategies []bccresource.PAASDeploySetStrategy) error {
	for _, itf := range app.Interfaces {
		itfItemMap := map[string]*bccresource.CreateBccResourceParamsItem{}
		itfToFakeDeleteInstanceMap := map[string][]string{}
		param := &specification.GetSpecificationParams{
			UserID:    app.UserId,
			Name:      itf.DestSpec,
			Engine:    itf.Engine,
			StoreType: itf.StoreType,
			AppType:   app.Type,
		}
		if len(param.Name) == 0 {
			param.Name = iface.DefaultProxySpec
		}
		spec, err := specification.GetSpecification(ctx, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, fmt.Sprintf("cannot find spec by param %+v", *param), logit.Error("error", err))
			return err
		}
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusToFakeDelete {
				itfToFakeDeleteInstanceMap[proxy.Azone] = append(itfToFakeDeleteInstanceMap[proxy.Azone], proxy.ResourceId)
			}
			if proxy.Status != x1model.NodeOrProxyStatusToCreate {
				continue
			}
			if _, found := aZoneReqMap[proxy.Azone]; !found {
				aZoneReqMap[proxy.Azone] = &bccresource.CreateBccResourceParams{
					AppID:                   app.AppId,
					UserID:                  app.UserId,
					Product:                 app.Product,
					ImageID:                 getImageId(app),
					VpcID:                   app.VpcId,
					PAASDeploySetStrategies: PAASDeploySetStrategies,
					LogicalZone:             proxy.LogicZone,
					Azone:                   proxy.Azone,
					StoreType:               map[string]string{itf.Engine: itf.StoreType},
				}
			} else {
				if aZoneReqMap[proxy.Azone].StoreType == nil {
					aZoneReqMap[proxy.Azone].StoreType = make(map[string]string)
				}
				aZoneReqMap[proxy.Azone].StoreType[itf.Engine] = itf.StoreType
			}
			if _, found := itfItemMap[proxy.Azone]; !found {
				aZoneReqMap[proxy.Azone].Items = append(aZoneReqMap[proxy.Azone].Items, bccresource.CreateBccResourceParamsItem{
					Specification: specification.Specification{
						AvailableVolume:      itf.AvailableVolume,
						CPUCount:             itf.Cpu,
						MemoryCapacityInGB:   itf.MemSize,
						RootDiskCapacityInGB: itf.SysDiskSize,
						DataDiskCapacityInGB: int(itf.DiskSize),
						Name:                 spec.Name,
					},
					Subnet:          proxy.SubnetId,
					Engine:          itf.Engine,
					Count:           1,
					DeploySetID:     getClusterItfDeploySetId(app.AppId, app.AppName, itf.InterfaceId),
					SecurityGroupID: app.SecurityGroupId,
					EntityIDs:       []string{proxy.ProxyId},
				})
				if !skipMergeMap() {
					itfItemMap[proxy.Azone] = &(aZoneReqMap[proxy.Azone].Items[len(aZoneReqMap[proxy.Azone].Items)-1])
				}
			} else {
				itfItemMap[proxy.Azone].Count++
				itfItemMap[proxy.Azone].EntityIDs = append(itfItemMap[proxy.Azone].EntityIDs, proxy.ProxyId)
			}
		}

		// for 自愈场景
		if isFailover {
			for azone := range itfToFakeDeleteInstanceMap {
				aZoneReqMap[azone].Items[len(aZoneReqMap[azone].Items)-1].ReplacedInstanceIDs = itfToFakeDeleteInstanceMap[azone]
			}
		}
	}
	return nil
}

func getReousrcePriority(ctx context.Context, app *x1model.Application) string {
	var priority string = "normal"
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				priority = "high"
				return priority
			}
		}

		for _, node := range cluster.RoNodes {
			if node.Status == x1model.NodeOrProxyStatusToFakeDelete {
				priority = "high"
				return priority
			}
		}
	}

	return priority
}

func bccRequestToX1Request(ctx context.Context, x1_request *x1resource.CreateInstanceParams, request *bccresource.CreateBccResourceParams,
	priority string, param *iface.Parameters, taskID string) {
	inputStoreType := ""
	for _, storeType := range request.StoreType {
		if inputStoreType == "" {
			inputStoreType = storeType
		} else {
			if inputStoreType != storeType {
				resource.LoggerTask.Warning(ctx, "container request has multiple store type", logit.String("all store type",
					base_utils.Format(request.StoreType)))
			}
		}
	}
	resource.LoggerTask.Notice(ctx, "got input store type", logit.String("input store type", inputStoreType),
		logit.String("all store type", base_utils.Format(request.StoreType)))

	*x1_request = x1resource.CreateInstanceParams{
		AppID:        request.AppID,
		UserID:       request.UserID,
		Product:      request.Product,
		ImageID:      request.ImageID,
		VpcID:        request.VpcID,
		LogicalZone:  request.LogicalZone,
		Azone:        request.Azone,
		StoreType:    inputStoreType,
		ResouceType:  "container",
		Priority:     priority,
		X1TaskID:     taskID,
		CustomLabels: param.ResourceLabels,
		NodeType:     param.NodeType,
	}

	for _, item := range request.Items {
		x1_item := &x1resource.CreateInstanceParamsItem{
			Spec: specification.Specification{
				AvailableVolume:      item.Specification.AvailableVolume,
				Name:                 item.Specification.Name,
				CPUCount:             item.Specification.CPUCount,
				MemoryCapacityInGB:   item.Specification.MemoryCapacityInGB,
				RootDiskCapacityInGB: item.Specification.RootDiskCapacityInGB,
				DataDiskCapacityInGB: item.Specification.DataDiskCapacityInGB,
			},
			Engine:          item.Engine,
			Count:           int64(item.Count),
			DeploySetID:     item.DeploySetID,
			SecurityGroupID: item.SecurityGroupID,
			EntityIDs:       item.EntityIDs,
		}

		x1_request.Items = append(x1_request.Items, *x1_item)
	}

	// 检查是否是 poc
	pocFlag, err := resource.CsmasterOpAgent.GetFlag(ctx, "poc_flag", map[string]string{"iam_user_id": request.UserID, "vpc_id": request.VpcID}, "no")
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get flag error", logit.Error("error", err))
		pocFlag = "no"
	}
	if pocFlag == "yes" {
		x1_request.CustomLabels = append(x1_request.CustomLabels, "affinity_ga3=yes")
	}

	if len(param.Priority) != 0 {
		x1_request.Priority = param.Priority
	}
}

func saveReousrceOrderIds(ctx context.Context, aZoneOrderMap map[string]string, app *x1model.Application) error {
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = x1model.ApplicationGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	for aZone, orderId := range aZoneOrderMap {
		for _, cluster := range app.Clusters {
			for _, node := range cluster.Nodes {
				if node.Status == x1model.NodeOrProxyStatusToCreate && node.Azone == aZone {
					node.ResourceOrderId = orderId
				}
			}
			for _, node := range cluster.RoNodes {
				if node.Status == x1model.NodeOrProxyStatusToCreate && node.Azone == aZone {
					node.ResourceOrderId = orderId
				}
			}
		}
		for _, itf := range app.Interfaces {
			for _, proxy := range itf.Proxys {
				if proxy.Status == x1model.NodeOrProxyStatusToCreate && proxy.Azone == aZone {
					proxy.ResourceOrderId = orderId
				}
			}
		}
	}
	return x1model.ApplicationsSave(ctx, []*x1model.Application{app})
}

func getNodeFinder(app *x1model.Application) func(eid string) (*x1model.Node, error) {
	nodesMap := make(map[string]*x1model.Node)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			nodesMap[node.NodeId] = node
		}
	}
	return func(eid string) (*x1model.Node, error) {
		node, found := nodesMap[eid]
		if !found {
			return nil, errors.Errorf("node %s not found", eid)
		}
		return node, nil
	}
}

func getProxyFinder(app *x1model.Application) func(eid string) (*x1model.Proxy, error) {
	proxysMap := make(map[string]*x1model.Proxy)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			proxysMap[proxy.ProxyId] = proxy
		}
	}
	return func(eid string) (*x1model.Proxy, error) {
		proxy, found := proxysMap[eid]
		if !found {
			return nil, errors.Errorf("node %s not found", eid)
		}
		return proxy, nil
	}
}

func getRoNodeFinder(app *x1model.Application) func(eid string) (*x1model.RoNode, error) {
	nodesMap := make(map[string]*x1model.RoNode)
	for _, cluster := range app.Clusters {
		for _, node := range cluster.RoNodes {
			nodesMap[node.NodeId] = node
		}
	}
	return func(eid string) (*x1model.RoNode, error) {
		node, found := nodesMap[eid]
		if !found {
			return nil, errors.Errorf("node %s not found", eid)
		}
		return node, nil
	}
}

func fillResources(ctx context.Context, servers []bccresource.BccResources, nodeFinder func(eid string) (*x1model.Node, error), proxyFinder func(eid string) (*x1model.Proxy, error),
	roNodeFinder func(eid string) (*x1model.RoNode, error)) error {
	fmt.Printf("servers %+v\n", servers)
	for i := range servers {
		server := &servers[i]
		if node, err := nodeFinder(server.EntityID); err == nil {
			node.ResourceId = server.ID
			node.Ip = server.FixIP
			node.FloatingIP = server.FloatingIP
			node.RootPassword = server.RootPassword
			node.IPv6 = server.FixIPv6
			node.HostName = server.Name
			continue
		}

		if node, err := roNodeFinder(server.EntityID); err == nil {
			node.ResourceId = server.ID
			node.Ip = server.FixIP
			node.FloatingIP = server.FloatingIP
			node.RootPassword = server.RootPassword
			node.IPv6 = server.FixIPv6
			node.HostName = server.Name
			continue
		}

		if proxy, err := proxyFinder(server.EntityID); err == nil {
			proxy.ResourceId = server.ID
			proxy.Ip = server.FixIP
			proxy.FloatingIP = server.FloatingIP
			proxy.RootPassword = server.RootPassword
			proxy.IPv6 = server.FixIPv6
			proxy.HostName = server.Name
			continue
		}
		return cerrs.Errorf("entity %s not found", server.EntityID)
	}
	return nil
}

func fillX1Resources(ctx context.Context, servers []x1resource.ResInstance,
	nodeFinder func(eid string) (*x1model.Node, error),
	proxyFinder func(eid string) (*x1model.Proxy, error),
	roNodeFinder func(eid string) (*x1model.RoNode, error)) error {
	fmt.Printf("servers %+v\n", servers)
	for i := range servers {
		server := &servers[i]
		if node, err := nodeFinder(server.EntityID); err == nil {
			node.ResourceId = server.ID
			node.Ip = server.FixIP
			node.FloatingIP = server.FloatingIP
			node.RootPassword = server.RootPassword
			node.IPv6 = server.FixIPv6
			node.HostName = server.Name
			node.Port = server.Port.DbPort
			node.XagentPort = server.Port.XagentPort
			node.ContainerId = server.HostUUID
			node.ContainerName = server.HostName
			continue
		}

		if node, err := roNodeFinder(server.EntityID); err == nil {
			node.ResourceId = server.ID
			node.Ip = server.FixIP
			node.FloatingIP = server.FloatingIP
			node.RootPassword = server.RootPassword
			node.IPv6 = server.FixIPv6
			node.HostName = server.Name
			node.Port = server.Port.DbPort
			node.XagentPort = server.Port.XagentPort
			node.ContainerId = server.HostUUID
			node.ContainerName = server.HostName
			continue
		}

		if proxy, err := proxyFinder(server.EntityID); err == nil {
			proxy.ResourceId = server.ID
			proxy.Ip = server.FixIP
			proxy.FloatingIP = server.FloatingIP
			proxy.RootPassword = server.RootPassword
			proxy.IPv6 = server.FixIPv6
			proxy.HostName = server.Name
			proxy.Port = server.Port.DbPort
			proxy.XagentPort = server.Port.XagentPort
			proxy.McpackPort = server.Port.McpackPort
			proxy.StatPort = server.Port.StatPort
			proxy.ContainerId = server.HostUUID
			proxy.ContainerName = server.HostName
			continue
		}
		return cerrs.Errorf("entity %s not found", server.EntityID)
	}
	return nil
}

// Process 申请bcc资源
// 1. 遍历所有cluster、interface中需要新建的node、proxy; 生成bcc创建请求(对应CreateBccResourceParams)
// 2. 调用 bccresource.CreateBccResources 发送创建请求
// 3. 调用 bccresource.ShowBccResources 查询资源
// 4. 将资源信息写入到X1数据库中
// 5. 将对应的node、proxy写入到csmaster cache_instance表中
func ProcessApplyBccResources(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	// 判断是否是自愈请求，自愈请求会添加上自愈 tag
	isFailover := false
	if strings.Contains(getStep(teu.TaskBatchID), "scs-self-heal-cluster") || strings.Contains(getStep(teu.TaskBatchID), "scs-self-heal-standalone") {
		isFailover = true
	}

	// 获取集群部署集策略
	PAASDeploySetStrategies, err := getPAASDeploySetStrategies(ctx, app.UserId, app.DeploySetIds)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get deployset detail failed", logit.String("deploySetIDs", app.DeploySetIds), logit.Error("error", err))

	}

	aZoneReqMap, aZoneOrderMap, err := generateRequest(ctx, app, isFailover, PAASDeploySetStrategies)
	if err != nil {
		resource.LoggerTask.Error(ctx, "generate request failed", logit.Error("error", err))
		return err
	}

	err = applyResource(ctx, teu, aZoneReqMap, app, aZoneOrderMap, param, err)
	if err != nil {
		return err
	}

	if err := queryResource(ctx, teu, aZoneOrderMap, app); err != nil {
		resource.LoggerTask.Error(ctx, "query bcc resources failed", logit.Error("error", err))
		return err
	}
	return nil
}

func applyResource(ctx context.Context, teu *workflow.TaskExecUnit, aZoneReqMap map[string]*bccresource.CreateBccResourceParams,
	app *x1model.Application, aZoneOrderMap map[string]string, param *iface.Parameters, err error) error {
	var reqErr error
	var errReq *bccresource.CreateBccResourceParams
	for aZone, request := range aZoneReqMap {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("zone %s request is %+v", aZone, *request))
		var orderId string
		var err error
		orderId, err = loadSucceededOrder(ctx, app.AppId, teu.TaskID, aZone)
		if err == nil && len(orderId) != 0 {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("zone %s has succeeded order %s", aZone, orderId))
			aZoneOrderMap[aZone] = orderId
			continue
		}
		request.Retry = getRetry(teu.TaskBatchID)
		request.TaskID = teu.TaskID
		if app.ResourceType == "container" {
			x1Request := &x1resource.CreateInstanceParams{}
			priority := getReousrcePriority(ctx, app)
			bccRequestToX1Request(ctx, x1Request, request, priority, param, teu.TaskID)
			if err := fillLogicalRegion(ctx, x1Request, app); err != nil {
				resource.LoggerTask.Error(ctx, "fill logical region failed", logit.Error("error", err))
				return err
			}
			orderId, err = x1resource.Instance().CreateInstance(ctx, x1Request)
		} else {
			orderId, err = bccresource.BccResourceOp().CreateBccResources(ctx, request)
		}

		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("apply for bcc resources of azone %s failed", aZone), logit.Error("error", err))
			reqErr = err
			errReq = request
			break
		}
		if err := saveSucceededOrder(ctx, app.AppId, teu.TaskID, aZone, orderId); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("save bcc resource order id of azone %s failed", aZone), logit.Error("error", err))
		}
		aZoneOrderMap[aZone] = orderId
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("azone orderid map is %+v", aZoneOrderMap))
	if err := saveReousrceOrderIds(ctx, aZoneOrderMap, app); err != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("save resources orders into db failed, order: %+v", aZoneOrderMap), logit.Error("error", err))
		return err
	}
	if reqErr != nil {
		resource.LoggerTask.Error(ctx, fmt.Sprintf("send bcc resources request failed, request: %+v", errReq),
			logit.Error("error", err), logit.Int("error_code", cerrs.Code(reqErr)))
		// 对于scs-unit-migrate任务，如果创建资源失败，直接返回错误，不会进入manual
		if strings.Contains(getStep(teu.TaskBatchID), "scs-unit-migrate") {
			return fmt.Errorf(reqErr.Error())
		}
		return reqErr
	}
	return nil
}

func generateRequest(ctx context.Context, app *x1model.Application, isFailover bool,
	PAASDeploySetStrategies []bccresource.PAASDeploySetStrategy) (map[string]*bccresource.CreateBccResourceParams, map[string]string, error) {
	aZoneReqMap := make(map[string]*bccresource.CreateBccResourceParams)
	aZoneOrderMap := make(map[string]string)
	if err := fillClusterToRequestsMap(ctx, aZoneReqMap, app, isFailover, PAASDeploySetStrategies); err != nil {
		resource.LoggerTask.Error(ctx, "fill cluster to requests map failed", logit.Error("error", err))
		return nil, nil, err
	}
	if err := fillInterfaceToRequestsMap(ctx, aZoneReqMap, app, isFailover, PAASDeploySetStrategies); err != nil {
		resource.LoggerTask.Error(ctx, "fill interface to requests map failed", logit.Error("error", err))
		return nil, nil, err
	}
	return aZoneReqMap, aZoneOrderMap, nil
}

func queryResource(ctx context.Context, teu *workflow.TaskExecUnit, aZoneOrderMap map[string]string, app *x1model.Application) error {
	aZoneOrderResultMap := make(map[string][]bccresource.BccResources)
	aZoneX1OrderResultMap := make(map[string][]x1resource.ResInstance)
	for azone, orderId := range aZoneOrderMap {
		for {
			var err error
			var x1_servers []x1resource.ResInstance
			var servers []bccresource.BccResources
			if app.ResourceType == "container" {
				x1_servers, err = x1resource.Instance().ShowCreateInstanceByOrder(ctx, &x1resource.ShowInstanceParams{
					UserID:  app.UserId,
					OrderID: orderId,
				})
			} else {
				servers, err = bccresource.BccResourceOp().ShowBccResourcesByOrder(ctx, &bccresource.ShowBccResourcesParams{
					UserID:  app.UserId,
					OrderID: orderId,
				})
			}

			if err != nil {
				if cerrs.Code(err) == bccresource.CODE_BCC_ORDER_IN_OPERATION || err == x1resource.ErrInstanceOrderInOperation {
					resource.LoggerTask.Notice(ctx, fmt.Sprintf("order %s in operation, try next", orderId))
				} else if cerrs.Code(err) == bccresource.CODE_BCC_ORDER_ERROR || strings.Contains(err.Error(), "x1 instance error order") {
					resource.LoggerTask.Error(ctx, fmt.Sprintf("order %s is error", orderId), logit.Error("error", err))
					if err2 := deleteSucceededOrder(ctx, app.AppId, teu.TaskID, azone); err2 != nil {
						resource.LoggerTask.Warning(ctx, fmt.Sprintf("delete bcc resource order id of azone %s from cache failed", azone),
							logit.Error("error", err2))
						return err2
					}
					if !strings.Contains(getStep(teu.TaskBatchID), "scs-unit-migrate") {
						return cerrs.ErrorTaskManual.Wrap(err)
					}
					return err
				} else {
					resource.LoggerTask.Warning(ctx, fmt.Sprintf("order %s unknown status, try next", orderId), logit.Error("error", err))
				}
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(2 * time.Second):
					continue
				}
			} else {
				if app.ResourceType == "container" {
					aZoneX1OrderResultMap[azone] = x1_servers
				} else {
					aZoneOrderResultMap[azone] = servers
				}
				break
			}
		}
	}
	if err := saveResources(ctx, app, aZoneX1OrderResultMap, aZoneOrderResultMap); err != nil {
		return err
	}
	return nil
}

func saveResources(ctx context.Context, app *x1model.Application,
	aZoneX1OrderResultMap map[string][]x1resource.ResInstance, aZoneOrderResultMap map[string][]bccresource.BccResources) error {
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "lock for x1model modify failed", logit.Error("error", err))
		return err
	}
	defer unlock()
	app, err = x1model.ApplicationGetByAppId(ctx, app.AppId)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app model failed", logit.Error("error", err))
		return err
	}
	nodeFinder := getNodeFinder(app)
	proxyFinder := getProxyFinder(app)
	roNodeFinder := getRoNodeFinder(app)
	if app.ResourceType == "container" {
		for azone, x1Servers := range aZoneX1OrderResultMap {
			if err := fillX1Resources(ctx, x1Servers, nodeFinder, proxyFinder, roNodeFinder); err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("fill x1 %s resource error", azone), logit.Error("error", err))
				return err
			}
		}
	} else {
		for azone, servers := range aZoneOrderResultMap {
			if err := fillResources(ctx, servers, nodeFinder, proxyFinder, roNodeFinder); err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("fill %s resource error", azone), logit.Error("error", err))
				return err
			}
		}
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func getStep(batchID string) string {
	chunks := strings.Split(batchID, "|")
	if len(chunks) > 1 {
		return chunks[1]
	}
	return ""
}

func saveSucceededOrder(ctx context.Context, appId string, taskId string, zone string, orderId string) error {
	key := "success_resource_order:" + appId + ":" + taskId + ":" + zone
	if err := resource.RedisClient.SetNX(ctx, key, orderId, 48*time.Hour).Err(); err != nil {
		return err
	}
	return nil
}

func loadSucceededOrder(ctx context.Context, appId string, taskId string, zone string) (string, error) {
	key := "success_resource_order:" + appId + ":" + taskId + ":" + zone
	orderId, err := resource.RedisClient.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return orderId, nil
}

func deleteSucceededOrder(ctx context.Context, appId string, taskId string, zone string) error {
	key := "success_resource_order:" + appId + ":" + taskId + ":" + zone
	if err := resource.RedisClient.Del(ctx, key).Err(); err != nil {
		return err
	}
	return nil
}

func getRetry(batchId string) int {
	if strings.Contains(batchId, "|") {
		if strings.Contains(strings.Split(batchId, "|")[1], "__") {
			if retry, err := strconv.Atoi(strings.Split(strings.Split(batchId, "|")[1], "__")[1]); err == nil {
				return retry
			}
		}
	}
	return 0
}

func fillLogicalRegion(ctx context.Context, request *x1resource.CreateInstanceParams, app *x1model.Application) error {
	if privatecloud.IsPrivateENV() {
		clusterModel, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, app.AppId)
		if err != nil {
			return err
		}
		request.LogicalRegion = clusterModel.LogicalRegion
	}
	return nil
}
