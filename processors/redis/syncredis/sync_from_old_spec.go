/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package syncredis

import (
	"context"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"math"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/sdk/redis/single_redis"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/metaserver"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	MaxOffsetDiff           = 1024 * 1024 // 1MB
	MaxKeyNumberDiffRatio   = 0.1
	NeedComparaMinKeyNumber = 10000
)

func checkAndSetReplication(ctx context.Context, cluster *x1model.Cluster, password string) error {
	var currentMaster *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			switch node.Status {
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete, x1model.NodeOrProxyStatusInUse:
				currentMaster = node
			}
		}
	}
	// 没有找到旧主, 是有问题的
	if currentMaster == nil {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("current master not found for cluster %s", cluster.ClusterId))
		return cerrs.ErrorTaskManual.Errorf("current master not found for cluster %s", cluster.ClusterId)
	}
	if err := setMasterForNewNodes(ctx, cluster, password, currentMaster); err != nil {
		return err
	}
	// 等待一会, 避免fork影响后续检查
	time.Sleep(3 * time.Second)
	if err := checkSyncForNewNodes(ctx, cluster, password, currentMaster); err != nil {
		return err
	}
	return nil
}

func checkSyncForNewNodes(ctx context.Context, cluster *x1model.Cluster, password string, currentMaster *x1model.Node) error {
	// 获取旧主info replication信息, 同时检查旧主存活
	masterReplInfo, err := util.GetReplicationInfo(ctx, currentMaster.FloatingIP, currentMaster.Port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("get replicationInfo from current master %s failed",
			currentMaster.NodeId), logit.Error("error", err))
		return fmt.Errorf("%s, clusterId: %s, err: %w", StringsForMasterDown, cluster.ClusterId, err)
	}
	// 先群发slaveof, 再检查同步状态
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}
		replInfo, err := util.GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("get replicationInfo from node %s failed",
				node.NodeId), logit.Error("error", err))
			if node.Role == x1model.RoleTypeMaster {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("new master %s is down", node.NodeId))
				return fmt.Errorf("new master %s is down", node.NodeId)
			}
			// 如果新建的从跪了, 可以忽略
			continue
		}
		if replInfo.MasterLinkStatus != "up" {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("node %s sync status is %s", node.NodeId, replInfo.MasterLinkStatus))
			return fmt.Errorf("cluster %s sync not complete", cluster.ClusterId)
		}
		// 处理新主节点
		if node.Role == x1model.RoleTypeMaster {
			var newMasterSyncInfo *util.ReplicationInfoSlave
			for _, slave := range masterReplInfo.Slaves {
				if slave.IP == node.Ip && slave.Port == node.Port {
					newMasterSyncInfo = slave
					break
				}
			}
			if newMasterSyncInfo == nil {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("new master %s not found in replica list %s",
					node.NodeId, base_utils.Format(replInfo.Slaves)),
					logit.String("replication_info", base_utils.Format(replInfo)))
				return fmt.Errorf("cluster %s sync not complete", cluster.ClusterId)
			}
			if newMasterSyncInfo.State != "online" || masterReplInfo.MasterReplOffset-newMasterSyncInfo.Offset > MaxOffsetDiff {
				resource.LoggerTask.Notice(ctx, fmt.Sprintf("new master %s sync old master %s, offset diff %d",
					node.NodeId, currentMaster.NodeId, masterReplInfo.MasterReplOffset-newMasterSyncInfo.Offset))
				return fmt.Errorf("cluster %s sync not complete", cluster.ClusterId)
			}
		}
	}
	return nil
}

func setMasterForNewNodes(ctx context.Context, cluster *x1model.Cluster, password string, currentMaster *x1model.Node) error {
	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
		if node.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}
		replInfo, err := util.GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
		if err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("get replicationInfo from node %s failed",
				node.NodeId), logit.Error("error", err))
			if node.Role == x1model.RoleTypeMaster {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("new master %s is down", node.NodeId))
				return fmt.Errorf("new master %s is down", node.NodeId)
			}
			// 如果新建的从跪了, 可以忽略
			continue
		}
		if replInfo.Role != "slave" || replInfo.MasterHost != currentMaster.Ip || replInfo.MasterPort != currentMaster.Port {
			resource.LoggerTask.Notice(ctx, fmt.Sprintf("need to send slave of master %s to node %s",
				currentMaster.NodeId, node.NodeId))
			if err := util.SetSlaveOf(ctx, node.FloatingIP, node.Port, password, currentMaster.Ip, strconv.Itoa(currentMaster.Port)); err != nil {
				resource.LoggerTask.Warning(ctx, fmt.Sprintf("send slave of old master %s to new master %s failed",
					currentMaster.NodeId, node.NodeId), logit.Error("error", err))
				// 节点是通的, 但是发送slaveof失败
				return err
			}
		}
	}
	return nil
}

func checkKeyNumber(ctx context.Context, cluster *x1model.Cluster, password string) error {
	var toDeleteMaster *x1model.Node
	var toCreateMaster *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			switch node.Status {
			case x1model.NodeOrProxyStatusToCreate:
				toCreateMaster = node
			case x1model.NodeOrProxyStatusToDelete, x1model.NodeOrProxyStatusToFakeDelete:
				toDeleteMaster = node
			}
		}
	}
	if toCreateMaster == nil || toDeleteMaster == nil {
		return nil
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("check key number between new master %s and old master %s",
		toCreateMaster.NodeId, toDeleteMaster.NodeId))

	if cluster.Engine == x1model.EnginePegaDB {
		if err := RefreshPegaKeyspace(ctx, toDeleteMaster); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("refresh pega keyspace from old master %s failed",
				toDeleteMaster.NodeId), logit.Error("error", err))
			return err
		}
		if err := RefreshPegaKeyspace(ctx, toCreateMaster); err != nil {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("refresh pega keyspace from new master %s failed",
				toCreateMaster.NodeId), logit.Error("error", err))
			return err
		}
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("refresh pega keyspace from old master %s and new master %s succ",
			toDeleteMaster.NodeId, toCreateMaster.NodeId))
		time.Sleep(2 * time.Second)
	}

	toCreateMasterkeyspaceInfo, err := util.GetKeyspaceInfo(ctx, toCreateMaster.FloatingIP, toCreateMaster.Port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("get KeyspaceInfo from new master %s failed",
			toCreateMaster.NodeId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("get KeyspaceInfo from new master %s succ",
		toCreateMaster.NodeId), logit.String("keyspace_info", base_utils.Format(toCreateMasterkeyspaceInfo)))

	var toCreateMasterkeyNumber int64
	for _, dbinfo := range toCreateMasterkeyspaceInfo.Db {
		toCreateMasterkeyNumber += int64(dbinfo.Keys)
	}

	toDeleteMasterkeyspaceInfo, err := util.GetKeyspaceInfo(ctx, toDeleteMaster.FloatingIP, toDeleteMaster.Port, password)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("get KeyspaceInfo from old master %s failed",
			toDeleteMaster.NodeId), logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("get KeyspaceInfo from old master %s succ",
		toDeleteMaster.NodeId), logit.String("keyspace_info", base_utils.Format(toDeleteMasterkeyspaceInfo)))

	var toDeleteMasterkeyNumber int64
	for _, dbinfo := range toDeleteMasterkeyspaceInfo.Db {
		toDeleteMasterkeyNumber += int64(dbinfo.Keys)
	}

	if toDeleteMasterkeyNumber < NeedComparaMinKeyNumber {
		resource.LoggerTask.Notice(ctx, fmt.Sprintf("old master %s, keynumber %d no need check", toDeleteMaster.NodeId,
			toDeleteMasterkeyNumber))
		return nil
	}

	diff := math.Abs(float64(toDeleteMasterkeyNumber - toCreateMasterkeyNumber))
	min := math.Min(float64(toDeleteMasterkeyNumber), float64(toCreateMasterkeyNumber))
	resource.LoggerTask.Notice(ctx, fmt.Sprintf("new master %s sync old master %s, new keynumber %d old keynumber %d",
		toCreateMaster.NodeId, toDeleteMaster.NodeId, toCreateMasterkeyNumber, toDeleteMasterkeyNumber))

	if diff > min*MaxKeyNumberDiffRatio {
		return fmt.Errorf("cluster %s sync not complete", cluster.ClusterId)
	}
	return nil
}

// ProcessSyncFromOldSpecStandalone
// 用于标准版变配的新老节点数据同步流程
func ProcessSyncFromOldSpecStandalone(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 热活从角色下一步等着
	if len(app.AppGroupID) != 0 {
		if app.Clusters[0].EngineVersion == "3.2" {
			return errors.New("not support 3.2 group modify spec")
		}
		if !metaserver.IsGlobalLeader(ctx, app) {
			resource.LoggerTask.Notice(ctx, "group follower no need to sync from old")
			return nil
		}
	}

	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}

	if err := updateMasterRedisAndSlaverRedisFields(ctx, app); err != nil {
		return err
	}

	var password string
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}

	for {
		complete := true
		for _, cluster := range app.Clusters {
			if err := checkAndSetReplication(ctx, cluster, password); err != nil {
				if !IsSyncError(err) {
					resource.LoggerTask.Warning(ctx, "current master not found or not alive", logit.Error("error", err))
					return err
				}
				complete = false
				resource.LoggerTask.Notice(ctx, "sync not complete", logit.Error("err", err))
			}
			if err := checkKeyNumber(ctx, cluster, password); err != nil {
				complete = false
				resource.LoggerTask.Notice(ctx, "key num sync not complete", logit.Error("err", err))
			}
		}
		if complete {
			// 热活主角色的加入global
			if len(app.AppGroupID) != 0 && metaserver.IsGlobalLeader(ctx, app) {
				if err := metaserver.ProcessAddNodesGlobal(ctx, app, true); err != nil {
					resource.LoggerTask.Warning(ctx, "add nodes global error", logit.Error("error", err))
					return err
				}
			}
			return nil
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}

func updateMasterRedisAndSlaverRedisFields(ctx context.Context, app *x1model.Application) error {
	// 修改csmaster数据库中slaver_redis字段
	creq := &csmaster.SaveInstancesParams{
		AppID:          app.AppId,
		UserID:         app.UserId,
		RequiredFields: []string{"master_redis", "slaver_redis"},
	}
	for _, cluster := range app.Clusters {
		var masterNode *x1model.Node
		var slaveNodes []*x1model.Node
		var slaveNodeIPs []string
		for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
			if node.Status != x1model.NodeOrProxyStatusToDelete && node.Status != x1model.NodeOrProxyStatusToFakeDelete {
				if node.Role == x1model.RoleTypeMaster {
					masterNode = node
				} else if node.Role == x1model.RoleTypeSlave {
					slaveNodes = append(slaveNodes, node)
					slaveNodeIPs = append(slaveNodeIPs, node.Ip)
				}
			}
		}
		creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
			Uuid:        masterNode.ResourceId,
			MasterRedis: "",
			SlaverRedis: strings.Join(slaveNodeIPs, ","),
		})
		for _, slaveNode := range slaveNodes {
			creq.Models = append(creq.Models, &csmaster.CsmasterInstance{
				Uuid:        slaveNode.ResourceId,
				MasterRedis: masterNode.Ip,
				SlaverRedis: "",
			})
		}
	}
	if err := csmaster.CsmasterOp().SaveInstanceModels(ctx, creq); err != nil {
		resource.LoggerTask.Warning(ctx, "save instance models failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessSyncFromOldSpecCluster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	// 热活从角色下一步等着
	if len(app.AppGroupID) != 0 && !metaserver.IsGlobalLeader(ctx, app) {
		if app.Clusters[0].EngineVersion == "3.2" {
			return errors.New("not support 3.2 group modify spec")
		}
		// if err := metaserver.ProcessAddNodesGlobal(ctx, app); err != nil {
		//	resource.LoggerTask.Warning(ctx, "add nodes global error", logit.Error("error", err))
		//	return err
		// }
		resource.LoggerTask.Notice(ctx, "group follower no need to sync from old")
		return nil
	}
	// 3.x 之前的版本，需要先加入metaserver，才能进行同步
	if len(app.AppGroupID) == 0 && app.Clusters[0].EngineVersion == "3.2" {
		if err := metaserver.AddNodesLocal(ctx, app, false, true); err != nil {
			resource.LoggerTask.Warning(ctx, "add nodes local error", logit.Error("error", err))
			return err
		}
	}
	g := gtask.Group{}
	for _, cluster := range app.Clusters {
		cluster := cluster
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				for {
					if err := checkAndSetReplication(ctx, cluster, ""); err != nil {
						if !IsSyncError(err) {
							resource.LoggerTask.Warning(ctx, "current master not found or not alive", logit.Error("error", err))
							return err
						}
						resource.LoggerTask.Notice(ctx, "sync not complete", logit.Error("err", err))
					} else {
						if err := checkKeyNumber(ctx, cluster, ""); err != nil {
							resource.LoggerTask.Notice(ctx, "key num sync not complete", logit.Error("err", err))
						} else {
							return nil
						}
					}
					select {
					case <-ctx.Done():
						return ctx.Err()
					case <-time.After(5 * time.Second):
						continue
					}
				}
			})
		})
	}
	succ, err := g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sync failed", logit.Error("error", err), logit.Int("succ", succ))
		return err
	}
	// 同步数据基本完成后，将节点加入metaserver
	if len(app.AppGroupID) == 0 && app.Clusters[0].EngineVersion != "3.2" {
		if err := metaserver.AddNodesLocal(ctx, app, false, true); err != nil {
			resource.LoggerTask.Warning(ctx, "add nodes local error", logit.Error("error", err))
			return err
		}
	}
	return nil
}

func RefreshPegaKeyspace(ctx context.Context, node *x1model.Node) error {
	c := single_redis.NewClient(node.FloatingIP, node.Port) // pega的node都不带密码，所以这里不设置密码
	defer c.Close()
	if err := c.Do(ctx, "dbsize", "scan").Err(); err != nil {
		return fmt.Errorf("dbsize scan fail, err:%s", err.Error())
	}
	return nil
}
