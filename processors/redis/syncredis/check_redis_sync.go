/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package syncredis

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/crypto_utils"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/handover"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

const (
	StringsForMasterDown = "master for sync is down"
)

func isNodeAvailableSlave(node *x1model.Node) bool {
	return node.Role == x1model.RoleTypeSlave &&
		(node.Status == x1model.NodeOrProxyStatusInUse || node.Status == x1model.NodeOrProxyStatusToCreate)
}

func checkRedisSync(ctx context.Context, cluster *x1model.Cluster, app *x1model.Application, password string,
	allSync bool, withRoNode bool, onlyCheckToCreate bool) error {
	aliveSlave := 0
	syncSlave := 0
	nodesToCheck := cluster.Nodes
	if withRoNode {
		nodesToCheck = util.FetchAllNodesOfCluster(ctx, cluster)
	}
	for _, node := range nodesToCheck {
		if !isNodeAvailableSlave(node) {
			continue
		}

		if onlyCheckToCreate && node.Status != x1model.NodeOrProxyStatusToCreate {
			continue
		}

		aliveSlave++
		replicationInfo, err := util.GetReplicationInfo(ctx, node.FloatingIP, node.Port, password)
		if err != nil {
			resource.LoggerTask.Notice(ctx, "get replication info failed", logit.String("node_id", node.NodeId), logit.Error("error", err))
			continue
		}
		if replicationInfo.Role == "slave" && replicationInfo.MasterLinkStatus == "up" {
			syncSlave++
		} else {
			resource.LoggerTask.Notice(ctx, "master link status is abnormal", logit.String("node_id", node.NodeId),
				logit.String("repl_role", replicationInfo.Role), logit.String("master_link_status", replicationInfo.MasterLinkStatus))
			if allSync && replicationInfo.Role == "slave" && replicationInfo.MasterHost != "" {
				for _, n := range cluster.Nodes {
					if n.Ip == replicationInfo.MasterHost && n.Port == replicationInfo.MasterPort {
						_, err := util.GetReplicationInfo(ctx, n.FloatingIP, n.Port, password)
						if err != nil {
							resource.LoggerTask.Warning(ctx, "get master replication info failed",
								logit.String("node_id", n.NodeId), logit.Error("error", err))
							return fmt.Errorf("%s, clusterId:%s, error: %w", StringsForMasterDown, cluster.ClusterId, err)
						}
					}
				}
			}
		}
	}
	if aliveSlave == 0 || aliveSlave == syncSlave {
		return nil
	}
	if !allSync && syncSlave > 0 {
		return nil
	}
	return fmt.Errorf("cluster %s not sync", cluster.ClusterId)
}

func processCheckRedisSync(ctx context.Context, teu *workflow.TaskExecUnit, allSync bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	// if app.AppGroupID != "" {
	// 	return nil
	// }

	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	for {
		complete := true
		for _, cluster := range app.Clusters {
			if err := checkRedisSync(ctx, cluster, app, password, allSync, true, false); err != nil {
				if !IsSyncError(err) {
					resource.LoggerTask.Warning(ctx, "master for sync is down", logit.Error("err", err))
					return err
				}
				complete = false
				resource.LoggerTask.Notice(ctx, "cluster not sync", logit.Error("err", err),
					logit.String("cluster_id", cluster.ClusterId))
			}
		}
		if complete {
			return nil
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}

func PocessCheckRedisSync(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processCheckRedisSync(ctx, teu, false)
}

func PocessCheckRedisAllSync(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return processCheckRedisSync(ctx, teu, true)
}

// for failover or migrate
/*  仅检查处于 tocreate 状态的实例是否为 up 状态即可
 */
func processCheckShardHasSync(ctx context.Context, app *x1model.Application, clusters []*x1model.Cluster) error {
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	var password string
	if acl != nil && len(acl.Password) != 0 && app.Type != x1model.AppTypeCluster {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	resource.LoggerTask.Notice(ctx, "start check clusters sync", logit.AutoField("clusters", clusters))
	for {
		complete := true
		for _, cluster := range clusters {
			if err := checkRedisSync(ctx, cluster, app, password, true, true, true); err != nil {
				if !IsSyncError(err) {
					resource.LoggerTask.Warning(ctx, "master for sync is down", logit.Error("err", err))
					return err
				}
				complete = false
				resource.LoggerTask.Notice(ctx, "cluster not sync", logit.Error("err", err))
			}
		}
		if complete {
			resource.LoggerTask.Notice(ctx, "all clusters sync", logit.AutoField("clusters", clusters))
			return nil
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			continue
		}
	}
}

func processCheckShardSync(ctx context.Context, app *x1model.Application, cluster *x1model.Cluster, params *iface.ManualFailover) error {
	var password string
	acl, err := x1model.RedisAclGetInUse(ctx, app.AppId, x1model.DefaultACLUser)
	if err != nil && !x1model.IsNotFound(err) {
		resource.LoggerTask.Error(ctx, "get acl fail", logit.Error("err", err))
		return err
	}
	if acl != nil && len(acl.Password) != 0 {
		password, _ = crypto_utils.DecryptKey(acl.Password)
	}
	var master *x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			master = node
		}
	}
	if master == nil {
		return fmt.Errorf("cluster %s not have master", cluster.ClusterId)
	}
	var candidateNodes []*x1model.Node
	for _, node := range cluster.Nodes {
		if node.Role == x1model.RoleTypeMaster {
			continue
		}
		if node.Status == x1model.NodeOrProxyStatusToFakeDelete || node.Status == x1model.NodeOrProxyStatusToDelete {
			continue
		}
		if len(params.Candidates) > 0 {
			if in, _ := base_utils.InArray(node.NodeId, params.Candidates); !in {
				continue
			}
		}
		candidateNodes = append(candidateNodes, node)
	}
	if len(candidateNodes) == 0 {
		return fmt.Errorf("cluster %s not have candidate node", cluster.ClusterId)
	}
	replicationInfo, err := util.GetReplicationInfo(ctx, master.FloatingIP, master.Port, password)
	if err != nil {
		resource.LoggerTask.Notice(ctx, "get replication info failed", logit.String("node_id", master.NodeId), logit.Error("error", err))
		return err
	}
	for _, slaveReplInfo := range replicationInfo.Slaves {
		for _, node := range candidateNodes {
			if node.Ip == slaveReplInfo.IP && node.Port == slaveReplInfo.Port {
				if slaveReplInfo.State == "online" || slaveReplInfo.State == "up" {
					if (params.SyncOffset <= 0) || (replicationInfo.MasterReplOffset-slaveReplInfo.Offset <= params.SyncOffset) {
						return nil
					}
				}
			}
		}
	}
	return fmt.Errorf("cluster %s not sync", cluster.ClusterId)
}

func ProcessCheckShardSyncForManualFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("err", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	var cluster *x1model.Cluster
	for _, c := range app.Clusters {
		if c.ClusterId == params.ManualFailover.ShardID {
			cluster = c
		}
	}
	if cluster == nil {
		return fmt.Errorf("cluster %s not found", params.ManualFailover.ShardID)
	}
	return processCheckShardSync(ctx, app, cluster, params.ManualFailover)
}

func ProcessCheckShardSyncForSwitchMasterSlave(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	if param.ManualSwitchMasterSlave == nil || len(param.ManualSwitchMasterSlave) == 0 {
		err := fmt.Errorf("manual shard failover list is nil")
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	g := gtask.Group{}
	for i, _ := range param.ManualSwitchMasterSlave {
		shardToSwitch := param.ManualSwitchMasterSlave[i]
		for j, _ := range app.Clusters {
			cluster := app.Clusters[j]
			if cluster.ClusterId == shardToSwitch.ShardID {
				// 检查是否需要切换
				masterNode, has := handover.HasMasterInCandidates(ctx, cluster, shardToSwitch.Candidates)
				if has {
					resource.LoggerTask.Error(ctx, "there is master in candidates already and skip switching",
						logit.String("cluster", cluster.ClusterId),
						logit.String("master node", masterNode.NodeId),
						logit.String("candidates", base_utils.Format(shardToSwitch.Candidates)))
					break
				}

				g.Go(func() error {
					return gtask.NoPanic(func() error {
						return processCheckShardSync(ctx, app, cluster, shardToSwitch)
					})
				})
			}
		}
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "process check shard sync fail", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessCheckShardHasSyncForClusterFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("err", err))
		return err
	}

	if len(params.SelfHealFromCsmaster.NodeShortIDs) == 0 {
		resource.LoggerTask.Notice(ctx, "skip sync check when there is no node", logit.Error("error", err))
		return nil
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	clusters := make([]*x1model.Cluster, 0)
	for _, un := range params.SelfHealFromCsmaster.NodeShortIDs {
		_, cluster, err := buildmeta.FindNodeByShortIDInApp(ctx, app, un)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return err
		}
		clusters = append(clusters, cluster)
	}
	if clusters == nil || len(clusters) == 0 {
		resource.LoggerTask.Error(ctx, "clusters is nil", logit.AutoField("params", params))
		return fmt.Errorf("clusters is nil")
	}

	return processCheckShardHasSync(ctx, app, clusters)
}

func ProcessCheckShardHasSyncForStandaloneFailover(ctx context.Context, teu *workflow.TaskExecUnit) error {
	params, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get parameters fail", logit.Error("err", err))
		return err
	}

	if len(params.UnHealthShards) == 0 {
		resource.LoggerTask.Notice(ctx, "skip sync check when there is no node", logit.Error("error", err))
		return nil
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	clusters := make([]*x1model.Cluster, 0)
	for i, c := range app.Clusters {
		for _, us := range params.UnHealthShards {
			if c.ClusterShortID == int(us.ShardShortID) {
				clusters = append(clusters, app.Clusters[i])
			}
		}
	}
	if clusters == nil || len(clusters) == 0 {
		resource.LoggerTask.Error(ctx, "clusters is nil", logit.AutoField("params", params))
		return fmt.Errorf("clusters is nil")
	}

	return processCheckShardHasSync(ctx, app, clusters)
}

func IsSyncError(err error) bool {
	if cerrs.Is(err, cerrs.ErrorTaskManual) {
		return false
	}
	if strings.Contains(err.Error(), StringsForMasterDown) {
		return false
	}
	return true
}

// CheckAllNewGroupNodeSynced 检查热活组新节点是否都已经同步完数据
func CheckAllNewGroupNodeSynced(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取app信息
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}
	if app == nil {
		resource.LoggerTask.Warning(ctx, "app not found", logit.String("appId", teu.Entity))
		return fmt.Errorf("app(%s) not found", teu.Entity)
	}
	// 非热活直接返回
	if len(app.AppGroupID) == 0 {
		return nil
	}
	return processCheckRedisSync(ctx, teu, true)
}
