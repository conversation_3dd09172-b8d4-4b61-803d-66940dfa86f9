/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* switch_service.go */
/*
modification history
--------------------
2022/09/06 , by wangbin<PERSON> (<EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package iface

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"
)

type SwitchServiceParam struct {
	OldAppId string `json:"old_app_id"`
	NewAppId string `json:"new_app_id"`
}

func GetSwitchServiceParameters(ctx context.Context, params string) (param *SwitchServiceParam, err error) {
	param = &SwitchServiceParam{}
	err = errors.Wrap(json.Unmarshal([]byte(params), param), "json decode error")
	return
}
