/* Copyright 2022 Baidu Inc. All Rights Reserved. */
/* global.go */
/*
modification history
--------------------
2022/05/29 , by <PERSON> (ca<PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package iface

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-base/component/metaserver"
	"icode.baidu.com/baidu/scs/x1-base/model/global_model"
	"icode.baidu.com/baidu/scs/x1-global-api/httpserver/structs/business_objects"
)

type GlobalParam struct {
	global_model.AppGroup
	Slot map[int]metaserver.Slots `json:"slot,omitempty"`

	// Global Failover
	business_objects.GlobalFailoverTaskParamas
}

func GetGlobalParameters(ctx context.Context, params string) (param *GlobalParam, err error) {
	param = &GlobalParam{}
	err = errors.Wrap(json.Unmarshal([]byte(params), param), "json decode error")
	return
}
