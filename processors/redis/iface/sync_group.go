/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2022/09/07 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file sync_group.go
 * <AUTHOR>
 * @date 2022/09/07 18:59:36
 * @brief sync group iFace
 *
 **/

package iface

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-global-api/model/sync_group_model"
)

type SyncGroupParam struct {
	sync_group_model.SyncGroup
}

func GetSyncParameters(ctx context.Context, params string) (param *SyncGroupParam, err error) {
	param = &SyncGroupParam{}
	err = errors.Wrap(json.Unmarshal([]byte(params), param), "json decode error")
	return
}
