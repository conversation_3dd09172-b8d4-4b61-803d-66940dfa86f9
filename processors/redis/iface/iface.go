/* Copyright 2021 Baidu Inc. All Rights Reserved. */
/* instance.go - the controller of instance api  */
/*
modification history
--------------------
2021/12/13, by <PERSON><PERSON>(<EMAIL>), create
*/

/*
DESCRIPTION
所有SCS插件通用的用于解析Task Parameters的数据结构
*/

package iface

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/ifaces/csmaster"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/ifaces/readonly"
)

const (
	ActionCreateStandalone string = "create-bcc-standalone"
	DefaultProxySpec       string = "proxy.n1.small"

	TaskParamFromCsmaster = 1
	TaskParamFromXmaster  = 2
)

type Parameters struct {
	Action                  string                   `json:"action"`
	AppID                   string                   `json:"app_id"`  // cache_cluster.cluster_show_id
	Name                    string                   `json:"name"`    // cache_cluster.cluster_name
	UserID                  string                   `json:"user_id"` // userinfo.iam_user_id
	ServicePortList         []int                    `json:"port_list"`
	VPCID                   string                   `json:"vpc_id"`
	ClusterType             string                   `json:"cluster_type"`
	Replicas                []Replica                `json:"replicas"`
	DestReplicas            []Replica                `json:"dest_replicas"`
	DefaultPassword         string                   `json:"default_password"`
	Engine                  string                   `json:"engine"`
	EngineVersion           string                   `json:"engine_version"`
	NodeType                string                   `json:"node_type"`
	ProxyNodeType           string                   `json:"proxy_node_type"`
	ShardCount              int                      `json:"shard_count"`
	DeployIDSet             []string                 `json:"deploy_id_set"`
	Pool                    string                   `json:"pool"`
	Region                  string                   `json:"region"`
	ImageID                 string                   `json:"image_id"`
	ClusterShortID          int                      `json:"cluster_short_id"`
	UserShortID             int                      `json:"user_short_id"`
	BgwGroup                *BgwGroup                `json:"bgw_group"`
	BlbSubnetID             string                   `json:"blb_subnet_id"`
	AclParam                *AclParam                `json:"acl_param"`
	BackupParam             *BackupParams            `json:"backup_param"`
	NewBackupParam          *NewBackupParams         `json:"new_backup_param"`
	UnHealthShards          []*UnHealthShard         `json:"unhealth_shards"`
	ManualHandoverShards    []*ManualHandoverShard   `json:"manual_handover_shards"`
	ConfigList              []*ConfigItem            `json:"config_param"`
	Domain                  string                   `json:"domain"`
	DbIndex                 int                      `json:"db_index"`
	FlushExpiredType        string                   `json:"flush_expired_type"` // flush_expired_type: "default" | "scan"
	FlushExpiredEffort      int                      `json:"flush_expired_effort"`
	ScanBatchSize           int                      `json:"scan_batch_size"` // 扫描过期key的批量大小
	ScanInterval            int                      `json:"scan_interval"`   // 扫描过期key的间隔时间，单位秒
	TargetKernelVersion     string                   `json:"target_kernel_version"`
	ToDeleteShardShortIds   []int                    `json:"to_delete_shard_short_ids"`
	ShardSwitchFromXmaster  *ShardSwitchFromXmaster  `json:"shard_switch_from_xmaster"`
	SelfHealFromXmaster     *SelfHealFromXmaster     `json:"self_heal_from_xmaster"`
	SelfHealFromCsmaster    *SelfHealFromCsmaster    `json:"self_heal_from_csmaster"`
	ShardSwitchFromCsmsater *ShardSwitchFromCsmsater `json:"shard_switch_from_csmsater"`
	TargetOpProxyInfos      []*TargetOpProxyInfo     `json:"target_op_proxy_infos"`
	BnsName                 string                   `json:"bns_name"`
	ForceSpecs              []*ForceSpec             `json:"force_specs"`
	ShrinkCount             int                      `json:"shrink_count"`
	MetaserverID            string                   `json:"metaserver_id"`
	ResourceType            string                   `json:"resource_type"`
	ConfTpl                 string                   `json:"conf_tpl"`
	ManualFailover          *ManualFailover          `json:"manual_failover"`
	ManualSwitchMasterSlave []*ManualFailover        `json:"manual_switch_master_slave"` // 用户手动切换主从，支持多分片
	ManualSwitchConcurrent  int                      `json:"manual_switch_concurrent"`
	ManualFailoverReason    string                   `json:"manual_failover_reason"` // 手动故障转移原因, 用于区分 用户手动切换 和 主机故障切换
	IsForceReplace          bool                     `json:"is_force_replace"`       // 是否强制自愈, 用于 主机故障 等场景强制迁移节点
	Priority                string                   `json:"priority"`
	ResourceLabels          []string                 `json:"resource_labels"`
	SubnetIDForMigrate      string                   `json:"subnet_id_for_migrate"`
	NamespaceParam          *NamespaceParam          `json:"namespace_param"`
	CleanBnsParam           *CleanBnsParam           `json:"clean_bns_param"`
	CollectUsageParam       *CollectUsageParam       `json:"collect_usage_param"`
	BcmInstanceGroups       []*BcmInstanceGroupItem  `json:"bcm_instance_groups"`
	From                    int                      `json:"from"` // 任务来源，0：默认，可取csmaster；1：csmaster；2：xmaster；
	UseNewAgent             string                   `json:"use_new_agent"`
	readonly.GroupParams
	csmaster.EntranceParams
	TargetInterfaceCount       int                     `json:"target_interface_count"`
	AppDefaultEntranceParam    *AppEntranceParams      `json:"app_default_entrance"`
	AppDstDefaultEntranceParam *AppEntranceParams      `json:"app_dst_default_entrance"`
	CleanAppEntranceParam      *CleanAppEntranceParams `json:"clean_app_entrance_param"`
	DeferTaskID                int                     `json:"defer_task_id"`
}

type Replica struct {
	Zone      string   `json:"zone"` // 逻辑可用区
	SubnetIDs []string `json:"subnet_ids"`
	Role      string   `json:"role"`
	Count     int      `json:"count"`
}

type BgwGroup struct {
	BgwGroupId        string `json:"bgw_group_id"`
	BgwGroupExclusive int    `json:"bgw_group_exclusive"`
	BgwGroupMode      string `json:"bgw_group_mode"`
	MasterAZ          string `json:"master_az"`
	SlaveAZ           string `json:"slave_az"`
}

const (
	ActionCreate          = "create"
	ActionUpdate          = "update"
	ActionDelete          = "delete"
	ActionModifyPasswd    = "modify_passwd"
	ActionModifyAuthority = "modify_authority"
	ActionModifyExtra     = "modify_extra"
)

type AclParam struct {
	Action  string      `json:"action"`
	AclList []*RedisAcl `json:"acls"`
}

type RedisAcl struct {
	AccountName    string `json:"account_name"`
	Password       string `json:"password"`
	AllowedCmds    string `json:"allowed_cmds"`
	AllowedSubCmds string `json:"allowed_sub_cmds"`
	KeyPatterns    string `json:"key_patterns"`
	Properties     string `json:"properties"`
}

type BackupItem struct {
	NodeShortID  int64  `json:"node_short_id"`
	ShardShortID int64  `json:"shard_short_id"`
	Access       string `json:"access"`
}

type BackupParams struct {
	BackupID   string        `json:"backup_id"`
	BackupType string        `json:"backup_type"`
	Comment    string        `json:"comment"`
	Items      []*BackupItem `json:"items"`
}

type NewBackupItem struct {
	ShardShortID int64  `json:"shard_short_id"`
	Bucket       string `json:"bucket"`
	ObjectKey    string `json:"object_key"`
}

type NewBackupParams struct {
	BackupID   string           `json:"backup_id"`
	BackupType string           `json:"backup_type"`
	Comment    string           `json:"comment"`
	Items      []*NewBackupItem `json:"items"`
}

type UnHealthShard struct {
	ShardShortID int64   `json:"shard_short_id"`
	NodeShortIDs []int64 `json:"node_short_ids"`
}

type ShardSwitchFromXmaster struct {
	ShardID  string `json:"shard_id"`
	MasterID string `json:"master_id"`
}

type ShardSwitchFromCsmsater struct {
	ShardShortID  int64 `json:"shard_short_id"`
	MasterShortID int64 `json:"master_short_id"`
}

type SelfHealFromXmaster struct {
	Type           string   `json:"type"`
	ShardID        string   `json:"shard_id"`
	NodeOrProxyIDs []string `json:"node_or_proxy_ids"`
}

type SelfHealFromCsmaster struct {
	NodeShortIDs   []int64 `json:"node_short_ids"`
	ProxyShortIDs  []int64 `json:"proxy_short_ids"`
	IsForceReplace bool    `json:"is_force_replace"` // Deprecated: please use Parameters.IsForceReplace from now on
}

type ConfigItem struct {
	AppId string `json:"app_id"`
	Type  string `json:"type"`
	Name  string `json:"name"`
	Value string `json:"value"`
}

type ManualHandoverShard struct {
	ShardShortID int64   `json:"shard_short_id"`
	Candidates   []int64 `json:"candidates"`
}

type TargetOpProxyInfo struct {
	Zone     string `json:"zone"`
	Count    int    `json:"count"`
	SubnetID string `json:"subnet_id"`
}

type ForceSpec struct {
	Engine   string `json:"engine"`
	Cpu      int    `json:"cpu"`
	Memory   int    `json:"memory"`
	SysDisk  int    `json:"sys_disk"`
	DataDisk int    `json:"data_disk"`
	Volume   int    `json:"volume"`
}

type ManualFailover struct {
	AppID      string   `json:"app_id"`
	ShardID    string   `json:"shard_id"`
	Candidates []string `json:"candidates"`
	SyncOffset int64    `json:"sync_offset"`
}

type NamespaceParam struct {
	TaskID          string `json:"task_id"`
	TargetNamespace string `json:"target_namespace"`
	OriginNamespace string `json:"origin_namespace"`
}

type CleanBnsParam struct {
	DaysAgoStart int `json:"days_ago_start"`
	DaysAgoEnd   int `json:"days_ago_end"`
}

type CollectUsageParam struct {
	SchedulePeriodMinutes int `json:"schedule_period_minutes"`
}

type BcmInstanceGroupItem struct {
	Type string `json:"type"`
	IDS  string `json:"ids"`
}

type AppEntranceParams struct {
	AvailabilityZone string `json:"availabilityZone"`
	SubnetID         string `json:"subnetId"`
	ResourceSubnetID string `json:"resourceSubnetId"`
}

type CleanAppEntranceParams struct {
	HoursAgo int `json:"hours_ago"`
}

func GetParameters(ctx context.Context, params string) (param *Parameters, err error) {
	param = &Parameters{}
	err = errors.Wrap(json.Unmarshal([]byte(params), param), "json decode error")
	return
}

func IsTaskFromXmaster(ctx context.Context, params string) (bool, error) {
	param, err := GetParameters(ctx, params)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return false, err
	}
	return param.From == TaskParamFromXmaster, nil
}
