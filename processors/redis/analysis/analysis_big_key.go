/***************************************************************************
 *
 * Copyright (c)  Baidu.com, Inc. All Rights Reserved
 *2023/03/22 <EMAIL> Exp
 *
 **************************************************************************/

/**
 * @file analysis_big_key.go
 * <AUTHOR>
 * @date 2023/03/22 15:07:13
 * @brief
 *
 **/

package analysis

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

const (
	analysisTimeoutSec = 120 * 60
	packagePrefix      = "scs-tools-package"
)

type Result struct {
	ShardName      string    `json:"shard_name,omitempty"`
	ShardID        int       `json:"shard_id"`
	ClusterID      int       `json:"cluster_id"`
	TotalKeys      int       `json:"total_keys"`
	Type           string    `json:"type,omitempty"`
	AnalysisResult []Content `json:"analysis_result,omitempty"`
}

type Content struct {
	Type string `json:"type"`
	Key  string `json:"key"`
	Info string `json:"info"`
}

type BigKeyParams struct {
	WorkDir     string       `json:"workdir"`
	PackageName string       `json:"packageName"`
	PackageUri  string       `json:"packageUri"`
	TopN        int          `json:"topN"`
	Meta        *xagent.Meta `json:"meta"`
}

func ProcessDispatchAnalysisTask(ctx context.Context, teu *workflow.TaskExecUnit) error {
	// 获取node信息
	node, err := x1model.NodeGetByNodeId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get node fail", logit.String("nodeId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	if node == nil {
		resource.LoggerTask.Warning(ctx, "node not found", logit.String("nodeId", teu.Entity))
		return cerrs.ErrNotFound.Errorf("node(%s) not found", teu.Entity)
	}

	if node.Status != x1model.NodeOrProxyStatusToAnalyze {
		return nil
	}

	// 执行分析任务
	if err := processAnalysisBigKey(ctx, node); err != nil {
		resource.LoggerTask.Warning(ctx, "process analysis big key fail", logit.String("nodeId", teu.Entity),
			logit.Error("error", err))
		return err
	}

	return nil
}

func processAnalysisBigKey(ctx context.Context, node *x1model.Node) error {
	if node.Status == x1model.NodeOrProxyStatusToAnalyze {
		xAgentAddr := xagent.Addr{
			Host: node.FloatingIP,
			Port: cast.ToInt32(node.XagentPort),
		}

		packageName := fmt.Sprintf("%s_%s.tar.gz", packagePrefix, conf.ScsMainConf.AnalysisToolVersion)

		analysisReq := xagent.AsyncRequest{
			Addr:   &xAgentAddr,
			Action: "startAnalysisBigKey",
			Params: BigKeyParams{
				WorkDir:     node.Basedir,
				PackageName: packageName,
				PackageUri:  util.GetPackageURI(packageName),
				TopN:        conf.ScsMainConf.AnalysisTopN,
				Meta: &xagent.Meta{
					Engine:        node.Engine,
					EngineVersion: node.EngineVersion,
					Basedir:       node.Basedir,
					Port:          int32(node.Port),
				},
			},
			TimeoutSec: analysisTimeoutSec,
		}

		asyncTask := xagent.Instance().DoAsync(ctx, &analysisReq)
		rsp, err := asyncTask.Wait()
		if err != nil {
			resource.LoggerTask.Warning(ctx, "call x-agent analysis fail", logit.Error("err", err),
				logit.String("nodeId", node.NodeId))
			node.Status = x1model.NodeOrProxyStatusAnalyzeFailed
			if err := x1model.NodesSave(ctx, []*x1model.Node{node}); err != nil {
				if err != nil {
					resource.LoggerTask.Warning(ctx, "save node fail", logit.String("nodeId", node.NodeId),
						logit.Error("dbError", err))
					return err
				}
			}
			// 如果失败，不返回错误，返回nil， 回调判断状态
			return nil
		}

		result := &Result{}
		if err := rsp.ParseResult(result); err != nil {
			resource.LoggerTask.Warning(ctx, "parse x-agent analysis result fail", logit.Error("err", err))
			return err
		}

		// 保存结果到数据库中
		resource.LoggerTask.Notice(ctx, "node analysis suc", logit.String("result", base_utils.Format(result.AnalysisResult)))
		bigKey := make([]*csmaster_model_interface.ClusterAnalysisResult, len(result.AnalysisResult))
		for i, item := range result.AnalysisResult {
			bigKey[i] = &csmaster_model_interface.ClusterAnalysisResult{
				ClusterID: result.ClusterID,
				ShardID:   result.ShardID,
				ShardName: node.ClusterId,
				Type:      0,
				KeyName:   item.Key,
				Info:      item.Info,
				TotalKeys: int64(result.TotalKeys), // 单个分片所有的key数量
			}
		}

		if len(bigKey) != 0 {
			err = resource.CsmasterOpAgent.UpdateAnalysisTaskResult(ctx, bigKey)
			if err != nil {
				resource.LoggerTask.Error(ctx, "update analysis result error", logit.Error("error", err))
				return err
			}
		}

		resource.LoggerTask.Notice(ctx, "node analysis suc", logit.String("nodeId", node.NodeId))
		node.Status = x1model.NodeOrProxyStatusAnalyzed
		if err := x1model.NodesSave(ctx, []*x1model.Node{node}); err != nil {
			if err != nil {
				resource.LoggerTask.Warning(ctx, "save node fail", logit.String("nodeId", node.NodeId),
					logit.Error("dbError", err))
				return err
			}
		}
	}

	return nil
}
