/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* excute.go */
/*
modification history
--------------------
2023/04/18 , by <PERSON> (<PERSON><PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package migratetopkg

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/xagent"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/deploy"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

// 改集群状态为24
func ProcessMigrateToPkgExcute(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app info from db fail", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusMigrateToX1,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}
	resource.LoggerTask.Trace(ctx, "changed status to 24")

	if err := upgradeAllPkgs(ctx, app, teu); err != nil {
		return err
	}
	resource.LoggerTask.Trace(ctx, "upgraded pkgs success")

	return nil
}

func upgradeAllPkgs(ctx context.Context, app *x1model.Application, teu *workflow.TaskExecUnit) error {
	deployMap, err := deploy.GetEachEntityDeployParams(ctx, app, true)
	if err != nil {
		return err
	}
	g := gtask.Group{Concurrent: 200}
	resource.LoggerTask.Trace(ctx, "get deploy map success", logit.String("deployMap", base_utils.Format(deployMap)))

	for serverId, params := range deployMap {
		serverId := serverId
		params := params
		params.TaskID = teu.TaskID
		g.Go(func() error {
			var deployErr error
			start := time.Now()
			resource.LoggerTask.Trace(ctx, "begin to migrate pkg deploy", logit.String("nodeId", serverId), logit.Time("start", start))
			deployErr = UpgradePkgsForMigrateFromLegacyX1(ctx, params)
			end := time.Now()
			resource.LoggerTask.Trace(ctx, "end to migrate deploy", logit.String("nodeId", serverId),
				logit.Time("start", start), logit.Time("end", end), logit.Duration("cost", end.Sub(start)), logit.Error("err", deployErr))
			return deployErr
		})
	}
	_, err = g.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy failed", logit.Error("err", err))
		return err
	}
	return nil
}

// UpgradePkgsForMigrateFromLegacyX1 包管理迁移
func UpgradePkgsForMigrateFromLegacyX1(ctx context.Context, params *deploy.DeployNodeOfAllTypeParams) error {
	params.ForceRestart = false
	params.IsUpgrade = true
	params.IsMigrateTask = true
	toMigratePkgs, err := x1model.GetCurrentPackageRecord(ctx, params.ServerID, deploy.DefaultPkgs, x1model.PkgRecordStatusToMigrate)
	if err != nil {
		return err
	}
	resource.LoggerTask.Trace(ctx, "get to migrate pkgs success", logit.String("pkgs", base_utils.Format(toMigratePkgs)))

	xagentReq, err := deploy.GetXagentRequest(ctx, params, toMigratePkgs)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get xagent req failed", logit.Error("err", err))
		return err
	}

	resource.LoggerTask.Trace(ctx, "get xagent req success", logit.String("xagent reqs", base_utils.Format(xagentReq)))

	aCtx := xagent.Instance().DoAsync(ctx, xagentReq)
	resp, err := aCtx.Wait()
	if err != nil {
		resource.LoggerTask.Warning(ctx, "deploy node failed", logit.Error("err", err),
			logit.String("resp", base_utils.Format(resp)), logit.String("xagentreq", base_utils.Format(xagentReq)))
		return err
	}
	isAlive, err := util.RedisAlivePing(ctx, params.NodeIP, params.NodePort)
	if !isAlive {
		resource.LoggerTask.Warning(ctx, "node is not alive", logit.Error("error", err))
		return err
	}

	x1modelIns, err := x1model.GetDbAgent(ctx)
	if err != nil {
		return err
	}
	var pkgRecords []*x1model.PackageRecord
	if err := x1modelIns.GetAllByCond(ctx, &pkgRecords, "entity = ? and status = ?", params.ServerID, x1model.PkgRecordStatusToMigrate); err != nil {
		return err
	}
	for _, rcd := range pkgRecords {
		rcd.Status = x1model.NodeOrProxyStatusInUse
	}

	if err := x1modelIns.FullSaveAssociationsSave(ctx, pkgRecords); err != nil {
		return err
	}
	return nil
}
