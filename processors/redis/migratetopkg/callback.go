/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* callback.go */
/*
modification history
--------------------
2023/04/18 , by <PERSON> (<PERSON><PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package migratetopkg

import (
	"context"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

func ProcessMigrateToPkgSuccessCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app info from db fail", logit.Error("error", err))
		return err
	}
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusRunning,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		return err
	}
	resource.LoggerTask.Trace(ctx, "changed status to 5")
	app.UseNewPackage = 1
	if util.GetEngine(app) == x1model.EngineRedis {
		app.ImageID = conf.PkgManConf.Image.IsolatedBcc
	} else {
		app.ImageID = conf.PkgManConf.Image.UnIsolatedBcc
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Error(ctx, "save app model failed", logit.Error("error", err))
		return err
	}
	return nil
}

func ProcessMigrateToPkgFailCb(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return nil
}
