/* Copyright 2023 Baidu Inc. All Rights Reserved. */
/* before_check.go */
/*
modification history
--------------------
2023/04/18 , by <PERSON> (ca<PERSON><PERSON><EMAIL>) , create
*/
/*
DESCRIPTION
todo
*/

package migratetopkg

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"gorm.io/gorm"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/iface"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/remotessh"
)

// 包管理todo，需要确认拿md5的路径
var (
	AGENT_PATH                       = "/root/agent"
	MC_BIN_PATH                      = "/bin/memcached"
	REDIS_SERVER_BIN_PATH            = "/bin/redis-server"
	REDIS_SERVER_BIN_SLOT_PATH       = "/bin/redis-server-slot"
	REDIS_SERVER_BIN_NOSLOT_PATH     = "/bin/redis-server-noslot"
	REDIS_SERVER_BIN_OPEN_REDIS_PATH = "/bin/redis-server-open-redis"
	REDIS_CLI_BIN_PATH               = "/bin/redis-cli"
	SENTINEL_BIN_PATH                = "/bin/redis-sentinel"
	NUTCRACKER_BIN_PATH              = "/bin/nutcracker"
	NUTCRACKER_BIN_SLOT_PATH         = "/bin/nutcracker-slot"
	NUTCRACKER_BIN_NOSLOT_PATH       = "/bin/nutcracker-noslot"
	GETQPSBIN_PATH                   = "/bin/getqpsbinary"
	ADAPTER_BIN_PATH                 = "/bin/ks-adapter"
	HSDB_SERVER_BIN_SLOT_PATH        = "/bin/hsdb-server-slot"
	HSDB_SERVER_BIN_PATH             = "/bin/hsdb-server"
	SYNC_AGENT_BIN_PATH              = "/bin/sync-agent"
	PEGADB_SERVER_BIN_PATH           = "/bin/kvrocks"
)

// ProcessMigrateToPkgBeforeCheck Step-1
// * 获取坑内 bin md5，检查发版记录内是否存在这个版本
// * 检查集群是否已经迁移过
// * 检查集群状态是否存活
func ProcessMigrateToPkgBeforeCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app info from db fail", logit.Error("error", err))
		return err
	}
	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return err
	}
	if csmasterModel.Status != csmaster.CsmasterStatusRunning {
		return errors.New("status not running")
	}

	// 重入进来的直接报错，需要人工检查
	if app.UseNewPackage == 1 {
		resource.LoggerTask.Warning(ctx, "has already in migrate process")
		if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
			return err2
		}
		return errors.New("has already in migrate process")
	}

	// 如果有非成功且非失败的任务，则不允许执行
	tasks, err := resource.TaskOperator.RetrieveTasks(ctx, "entity=? and task_id!=? and status not IN ?",
		teu.Entity, teu.TaskID, []string{iface.SubTasksStatusSuccess, iface.SubTasksStatusError})
	if err != nil && err != gorm.ErrRecordNotFound {
		resource.LoggerTask.Warning(ctx, "get running tasks failed failed", logit.Error("error", err))
	}
	if len(tasks) != 0 {
		var taskKeys []string
		for _, t := range tasks {
			taskKeys = append(taskKeys, fmt.Sprintf("%s:%s:%s", t.TaskID, t.WorkFlow, t.Status))
		}
		return fmt.Errorf("has some tasks not done,task infos:%s", strings.Join(taskKeys, ","))
	}
	// 检查当前实例的bin版本我们是否已经通过实现流水线工具获取到，如果没有的话需要先补上这条记录
	if err := checkBinMd5AndRecordPkgs(ctx, app, teu); err != nil {
		return err
	}

	return nil
}

func checkBinMd5AndRecordPkgs(ctx context.Context, app *x1model.Application, teu *workflow.TaskExecUnit) error {
	proxyBinPath := NUTCRACKER_BIN_PATH
	coreBinPath := ""
	if len(app.Clusters) == 0 {
		return errors.New("not has any cluster")
	}
	clusterInfo := app.Clusters[0]
	if clusterInfo.Engine == x1model.EngineRedis {
		coreBinPath = REDIS_SERVER_BIN_PATH
	} else if clusterInfo.Engine == x1model.EnginePegaDB {
		coreBinPath = PEGADB_SERVER_BIN_PATH
	} else {
		return errors.New("not support this server type")
	}
	randNode, err := RandPickOneNode(ctx, app)
	if err != nil {
		return err
	}
	resource.LoggerTask.Trace(ctx, "start to check md5", logit.String("serverpath", coreBinPath), logit.String("proxy path", proxyBinPath))

	serverBinMd5, err := remotessh.GetFileMd5SumBySSH(ctx, filepath.Join(AGENT_PATH, coreBinPath),
		randNode.RootPassword, randNode.FloatingIP, randNode.ResourceId, app.ResourceType)
	if err != nil {
		return fmt.Errorf("get server md5 fail,err:%w", err)
	}
	resource.LoggerTask.Trace(ctx, "get server bin md5", logit.String("md5", serverBinMd5))
	var serverPkg *x1model.Package
	var _ *x1model.Package
	serverPkg, err = GetBinPackageInfoByMd5(ctx, serverBinMd5)
	resource.LoggerTask.Trace(ctx, "get server bin success", logit.String("pkg info", base_utils.Format(serverPkg)))
	if err != nil {
		return fmt.Errorf("get server bin pkg info fail,err:%w", err)
	}

	if app.Type == x1model.AppTypeCluster {
		randProxy, err := RandPickOneProxy(ctx, app)
		if err != nil {
			return err
		}
		proxyBinMd5, err := remotessh.GetFileMd5SumBySSH(ctx, filepath.Join(AGENT_PATH, proxyBinPath),
			randProxy.RootPassword, randProxy.FloatingIP, randProxy.ResourceId, app.ResourceType)
		resource.LoggerTask.Trace(ctx, "get proxy bin md5", logit.String("md5", proxyBinMd5))
		_, err = GetBinPackageInfoByMd5(ctx, proxyBinMd5)
		if err != nil {
			return fmt.Errorf("get proxy bin pkg info fail,err:%w", err)
		}
	}

	// 构造包管理版本记录
	// bin类的因为不需要重新安装，所以状态是inuse
	// 非bin类的，因为都要刷成最新版，所以状态是tomigrate
	// 包管理todo 这里要重构
	// for _, cluster := range app.Clusters {
	//	noBinPkgs, _ := deploy.CommonGetPackageInfos(cluster.Engine, cluster.EngineVersion, app.Type, app.SyncGroupID != "", true, app.ResourceType != "container")
	//	for _, node := range util.FetchAllNodesOfCluster(ctx, cluster) {
	//		_, err = x1model.PackageRecordGetForDeploy(ctx, node.NodeId, noBinPkgs, nil, "task-"+teu.TaskID, x1model.PkgRecordStatusToMigrate)
	//		if err != nil {
	//			resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err))
	//			return err
	//		}
	//		if err := ManualInsertPkgRecord(ctx, node.NodeId, serverPkg, x1model.PkgRecordStatusInuse, teu); err != nil {
	//			return fmt.Errorf("node :%s add bin pkg record fail,err:%w", node.NodeId, err)
	//		}
	//	}
	// }
	//
	// for _, itf := range app.Interfaces {
	//	noBinPkgs, _ := deploy.CommonGetPackageInfos(x1model.EngineBDRPProxy, "", app.Type, app.SyncGroupID != "", true, app.ResourceType != "container")
	//	for _, proxy := range itf.Proxys {
	//		_, err = x1model.PackageRecordGetForDeploy(ctx, proxy.ProxyId, noBinPkgs, nil, "task-"+teu.TaskID, x1model.PkgRecordStatusToMigrate)
	//		if err != nil {
	//			resource.LoggerTask.Warning(ctx, "get package record failed", logit.Error("err", err))
	//			return err
	//		}
	//		if err := ManualInsertPkgRecord(ctx, proxy.ProxyId, proxyPkg, x1model.PkgRecordStatusInuse, teu); err != nil {
	//			return fmt.Errorf("proxy :%s add bin pkg record fail,err:%w", proxy.ProxyId, err)
	//		}
	//	}
	// }

	return nil
}

func ManualInsertPkgRecord(ctx context.Context, entity string, pkg *x1model.Package, defaultStatus string, teu *workflow.TaskExecUnit) error {
	currentPackage, err := x1model.GetCurrentPackageRecord(ctx, entity, []string{pkg.Name}, defaultStatus)
	if err != nil {
		return err
	}
	for _, cp := range currentPackage {
		if cp.Name == pkg.Name {
			if cp.FullVersion == pkg.FullVersion {
				resource.LoggerTask.Trace(ctx, "already recorded,skip", logit.String("pkg name", pkg.Name))
				return nil
			}
			return fmt.Errorf("has %s record,but version not match", pkg.Name)
		}
	}
	var toAddRecords = []*x1model.PackageRecord{{
		Entity:    entity,
		Name:      pkg.Name,
		Type:      getEntityType(entity),
		PackageID: pkg.PackageID,
		Status:    defaultStatus,
		CreatedAt: time.Time{},
		Owner:     "task-" + teu.TaskID,
	}}
	if len(toAddRecords) > 0 {
		x1modelIns, err := x1model.GetDbAgent(ctx)
		if err != nil {
			return err
		}
		if err := x1modelIns.FullSaveAssociationsSave(ctx, &toAddRecords); err != nil {
			return err
		}
	}
	return nil
}

// GetBinPackageInfoByMd5  拿bin的package info，没有的报错
func GetBinPackageInfoByMd5(ctx context.Context, md5 string) (*x1model.Package, error) {
	if md5 == "" {
		return nil, errors.New("core bin md5 is empty")
	}
	x1modelIns, err := x1model.GetDbAgent(ctx)
	if err != nil {
		return nil, err
	}
	var pkgs []*x1model.Package
	if err := x1modelIns.GetAllByCond(ctx, &pkgs, "core_bin_md5 = ?", md5); err != nil {
		return nil, fmt.Errorf("get pkg from db fail,err:%w", err)
	}
	if len(pkgs) != 1 {
		return nil, fmt.Errorf("pkg num not eq to 1,pkg founded:%s", base_utils.Format(pkgs))
	}
	return pkgs[0], nil
}

func RandPickOneNode(ctx context.Context, application *x1model.Application) (*x1model.Node, error) {
	for _, cluster := range application.Clusters {
		if len(cluster.Nodes) == 1 {
			resource.LoggerTask.Trace(ctx, "single replica application")
			return cluster.Nodes[0], nil
		}
		// 尽量选择从节点，其实主也没什么不安全的，因为只是执行一个md5，主要是踏实
		for _, node := range cluster.Nodes {
			if node.Role == x1model.RoleTypeSlave {
				return node, nil
			}
		}
	}
	return nil, errors.New("cant pick a node,pls check")
}

func RandPickOneProxy(ctx context.Context, application *x1model.Application) (*x1model.Proxy, error) {
	for _, itf := range application.Interfaces {
		if len(itf.Proxys) > 0 {
			resource.LoggerTask.Trace(ctx, "proxy num trace", logit.Int("proxy num in this itf", len(itf.Proxys)))
			return itf.Proxys[0], nil
		}
	}
	return nil, errors.New("cant pick a proxy,pls check")
}

func getEntityType(entity string) string {
	if strings.Contains(entity, ".") {
		if strings.Contains(entity, "-itf-") || strings.Contains(entity, "-itfop-") {
			return "proxy"
		}
		return "redis"
	}
	return "app"
}
