package availability

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func IsAppUseXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// check ha has migrated to xmaster or not
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.UseXmaster == csmaster.CsmasterSetHAServicerUseXmaster {
		resource.LoggerTask.Warning(ctx, "HA servicer has been migrated to xmaster")
		return errors.Errorf("HA servicer has been migrated to xmaster")
	}

	return nil
}

func WaitXmasterCheckAppHealthStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	timer := time.NewTimer(35 * time.Second)
	select {
	case <-timer.C:
		resource.LoggerTask.Notice(ctx, "finish to wait xmaster check app health status")
	}

	return nil
}

func CheckAppHealthInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	// check if there is unhealthy ins in xmaster
	if ins, err := util.ListAppUnhealthyInstances(ctx, teu.Entity); err != nil {
		resource.LoggerTask.Error(ctx, "list unhealthy instances failed", logit.Error("error", err))
		return err
	} else if len(ins) > 0 {
		resource.LoggerTask.Error(ctx, "target app has unhealthy instances",
			logit.Error("error", err),
			logit.AutoField("unhealthy ins", ins),
		)
		return errors.Errorf("target app has unhealthy instances: %v", ins)
	}

	return nil
}

func SwitchOnHAInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	// set task fake flag to false，means enable ha in xmaster
	if err := util.SetXMasterTaskFakeSwitch(ctx, teu.Entity, false); err != nil {
		resource.LoggerTask.Error(ctx, "switch on xmaster task fake switch failed", logit.Error("error", err))
		return err
	}

	return nil
}

func SwitchOffHAInXmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	// set task fake flag to true，means disable ha in xmaster
	if err := util.SetXMasterTaskFakeSwitch(ctx, teu.Entity, true); err != nil {
		resource.LoggerTask.Error(ctx, "switch off xmaster task fake switch failed", logit.Error("error", err))
		return err
	}

	return nil
}
