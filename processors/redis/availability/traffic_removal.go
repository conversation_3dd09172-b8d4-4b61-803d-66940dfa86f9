package availability

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-api/httpserver/utils/hi_robot"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskIface "icode.baidu.com/baidu/scs/x1-base/task/iface"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/opmonitor"
)

var (
	ProxyCPUThresholdForTrafficRemoval = 60 // 摘流后 proc_cpu_usage 不超过 60%
	ProxyCntThresholdForTrafficRemoval = 0  // 摘流后节点数量不少于等于 0 节
	TrafficRemovalUnpassReasonProxyCPU = fmt.Sprintf("代理节点CPU容量不足, 摘流后proc_cpu_usage可能超%d%%", ProxyCPUThresholdForTrafficRemoval)
	TrafficRemovalUnpassReasonProxyCnt = fmt.Sprintf("代理节点数量不足, 摘流后节点数量为%d", ProxyCntThresholdForTrafficRemoval)

	NodeCPUThresholdForTrafficRemoval = 60
	NodeCntThresholdForTrafficRemoval = 0
	TrafficRemovalUnpassReasonNodeCPU = fmt.Sprintf("从节点CPU容量不足, 摘流后proc_cpu_usage可能超%d%%", NodeCPUThresholdForTrafficRemoval)
	TrafficRemovalUnpassReasonNodeCnt = fmt.Sprintf("从节点数量不足, 摘流后节点数量为%d", NodeCntThresholdForTrafficRemoval)

	RoNodeCPUThresholdForTrafficRemoval = 60
	RoNodeCntThresholdForTrafficRemoval = 0
	TrafficRemovalUnpassReasonRoNodeCPU = fmt.Sprintf("只读节点CPU容量不足, 摘流后proc_cpu_usage可能超%d%%", RoNodeCPUThresholdForTrafficRemoval)
	TrafficRemovalUnpassReasonRoNodeCnt = fmt.Sprintf("只读节点数量不足, 摘流后节点数量为%d", RoNodeCntThresholdForTrafficRemoval)

	TrafficRemovalMsgTitleSkip = "自愈任务跳过提前摘流"
	TrafficRemovalMsgTitleFail = "自愈任务提前摘流失败"
)

type TrafficRemovalMessageParam struct {
	App     *x1model.Application
	TaskID  string
	Title   string
	Reason  string
	MsgTime string
}

func ProcessClusterCreateSubTaskForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if param.ManualFailoverReason != "iaas_fault" {
		resource.LoggerTask.Trace(ctx, "not iaas_fault, skip traffic removal", logit.String("app", app.AppId))
		return nil
	}

	if len(param.SelfHealFromCsmaster.ProxyShortIDs) <= 0 && len(param.SelfHealFromCsmaster.NodeShortIDs) <= 0 {
		resource.LoggerTask.Trace(ctx, "no fault proxy or node, skip traffic removal")
		return nil
	}

	if len(param.SelfHealFromCsmaster.ProxyShortIDs) > 0 {
		pass, reason, err := checkProxyCpuLoad(ctx, app, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, "check proxy load error", logit.Error("error", err))
			return err
		}
		if !pass {
			resource.LoggerTask.Error(ctx, "not pass check, skip traffic removal",
				logit.String("reason", reason),
				logit.String("app", app.AppId),
				logit.String("proxyShortIDs", base_utils.Format(param.SelfHealFromCsmaster.ProxyShortIDs)),
			)

			msgParam := &TrafficRemovalMessageParam{
				App:     app,
				TaskID:  teu.TaskID,
				Title:   TrafficRemovalMsgTitleSkip,
				Reason:  reason,
				MsgTime: time.Now().Format("2006-01-02 15:04:05"),
			}
			if err := SendMessageForTrafficRemoval(ctx, msgParam); err != nil {
				resource.LoggerTask.Error(ctx, "send message error",
					logit.Error("error", err),
					logit.String("app", app.AppId),
					logit.String("taskID", teu.TaskID),
				)
			}

			return nil
		}
	}

	if len(param.SelfHealFromCsmaster.NodeShortIDs) > 0 {
		pass, reason, err := checkNodeCpuLoad(ctx, app, param)
		if err != nil {
			resource.LoggerTask.Error(ctx, "check node load error", logit.Error("error", err))
			return err
		}
		if !pass {
			resource.LoggerTask.Error(ctx, "not pass check, skip traffic removal",
				logit.String("reason", reason),
				logit.String("app", app.AppId),
				logit.String("nodeShortIDs", base_utils.Format(param.SelfHealFromCsmaster.NodeShortIDs)),
			)

			msgParam := &TrafficRemovalMessageParam{
				App:     app,
				TaskID:  teu.TaskID,
				Title:   TrafficRemovalMsgTitleSkip,
				Reason:  reason,
				MsgTime: time.Now().Format("2006-01-02 15:04:05"),
			}
			if err := SendMessageForTrafficRemoval(ctx, msgParam); err != nil {
				resource.LoggerTask.Error(ctx, "send message error",
					logit.Error("error", err),
					logit.String("app", app.AppId),
					logit.String("taskID", teu.TaskID),
				)
			}

			return nil
		}

		pass, reason, err = checkNoMasterNode(ctx, app, param.SelfHealFromCsmaster.NodeShortIDs)
		if err != nil {
			resource.LoggerTask.Error(ctx, "check no master in fault nodes error", logit.Error("error", err))
			return err
		}
		if !pass {
			resource.LoggerTask.Error(ctx, "not pass check, skip traffic removal",
				logit.String("reason", reason),
				logit.String("app", app.AppId),
				logit.String("nodeShortIDs", base_utils.Format(param.SelfHealFromCsmaster.NodeShortIDs)),
			)

			msgParam := &TrafficRemovalMessageParam{
				App:     app,
				TaskID:  teu.TaskID,
				Title:   TrafficRemovalMsgTitleSkip,
				Reason:  reason,
				MsgTime: time.Now().Format("2006-01-02 15:04:05"),
			}
			if err := SendMessageForTrafficRemoval(ctx, msgParam); err != nil {
				resource.LoggerTask.Error(ctx, "send message error",
					logit.Error("error", err),
					logit.String("app", app.AppId),
					logit.String("taskID", teu.TaskID),
				)
			}

			return nil
		}

	}

	err = createSubTaskForTrafficRemoval(ctx, teu, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "create sub task error", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessStandaloneCreateSubTaskForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit) error {

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	if param.ManualFailoverReason != "iaas_fault" {
		resource.LoggerTask.Trace(ctx, "not iaas_fault, skip traffic removal", logit.String("app", app.AppId))
		return nil
	}

	faultRoNodeShortIDs := make(map[int]struct{})
	for _, uhi := range param.UnHealthShards {
		for _, nodeShortID := range uhi.NodeShortIDs {
			roNode, _, err := buildmeta.FindRoNodeByShortIDInApp(ctx, app, nodeShortID)
			if err == nil && roNode != nil {
				faultRoNodeShortIDs[roNode.NodeShortID] = struct{}{}
			}
		}
	}
	if len(faultRoNodeShortIDs) <= 0 {
		resource.LoggerTask.Trace(ctx, "no fault ronode, skip traffic removal")
		return nil
	}

	pass, reason, err := checkRoNodeCpuLoad(ctx, app, param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "check ronode load error", logit.Error("error", err))
		return err
	}
	if !pass {
		resource.LoggerTask.Error(ctx, "not pass check, skip traffic removal",
			logit.String("reason", reason),
			logit.String("app", app.AppId),
			logit.String("proxyShortIDs", base_utils.Format(param.UnHealthShards)),
		)

		msgParam := &TrafficRemovalMessageParam{
			App:     app,
			TaskID:  teu.TaskID,
			Title:   TrafficRemovalMsgTitleSkip,
			Reason:  reason,
			MsgTime: time.Now().Format("2006-01-02 15:04:05"),
		}
		if err := SendMessageForTrafficRemoval(ctx, msgParam); err != nil {
			resource.LoggerTask.Error(ctx, "send message error",
				logit.Error("error", err),
				logit.String("app", app.AppId),
				logit.String("taskID", teu.TaskID),
			)
		}
		return nil
	}

	err = createSubTaskForTrafficRemoval(ctx, teu, app)
	if err != nil {
		resource.LoggerTask.Error(ctx, "create sub task error", logit.Error("error", err))
		return err
	}

	return nil
}

func createSubTaskForTrafficRemoval(ctx context.Context, teu *workflow.TaskExecUnit, app *x1model.Application) error {
	var (
		subWorkflow string
	)
	switch app.Type {
	case x1model.AppTypeCluster:
		subWorkflow = "scs-self-heal-cluster-traffic-removal"
	case x1model.AppTypeStandalone:
		subWorkflow = "scs-self-heal-standalone-traffic-removal"
	default:
		resource.LoggerTask.Error(ctx, "unsupported app type", logit.String("app", app.AppId))
		return fmt.Errorf("unsupported app type, app id %s", app.AppId)
	}

	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		resource.LoggerTask.Error(ctx, "parse params error", logit.Error("error", err))
		return err
	}

	paramsBytes, err := json.Marshal(param)
	if err != nil {
		resource.LoggerTask.Error(ctx, "fail to marshal params",
			logit.String("app", app.AppId),
			logit.Error("error", err))
		return err
	}
	return resource.TaskOperator.CreateSubTasks(ctx, teu.TaskID, []*taskIface.CreateTaskParams{{
		WorkFlow:   subWorkflow,
		Schedule:   time.Now(),
		Mutex:      "n_sub_" + app.AppId,
		Entity:     app.AppId,
		Parameters: string(paramsBytes),
	}})

}

func ProcessTrafficRemovalCallback(ctx context.Context, teu *workflow.TaskExecUnit) error {

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app model failed", logit.Error("error", err))
		return err
	}

	subTasksStatus, err := resource.TaskOperator.CheckSubTasksStatus(ctx, teu.TaskID)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "check sub tasks status failed", logit.Error("error", err))
		return err
	}
	switch subTasksStatus {
	case taskIface.SubTasksStatusSuccess:
		return nil
	case taskIface.SubTasksStatusError:
		resource.LoggerTask.Error(ctx, "sub tasks failed", logit.Error("error", err))

		msgParam := &TrafficRemovalMessageParam{
			App:     app,
			TaskID:  teu.TaskID,
			Title:   TrafficRemovalMsgTitleFail,
			Reason:  "摘流子任务失败",
			MsgTime: time.Now().Format("2006-01-02 15:04:05"),
		}
		if err := SendMessageForTrafficRemoval(ctx, msgParam); err != nil {
			resource.LoggerTask.Error(ctx, "send message error",
				logit.Error("error", err),
				logit.String("app", app.AppId),
				logit.String("taskID", teu.TaskID),
			)
		}
		return nil
	default:
		resource.LoggerTask.Warning(ctx, "sub tasks status is not finish", logit.Error("error", err))
		return fmt.Errorf("sub tasks status is not finish: %s", subTasksStatus)
	}
}

func checkNoMasterNode(ctx context.Context, app *x1model.Application, nodeShortIDs []int64) (pass bool, reason string, err error) {

	for _, nodeShortID := range nodeShortIDs {
		node, _, err := buildmeta.FindNodeByShortIDInApp(ctx, app, nodeShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return false, "", err
		}
		if node.Role == x1model.RoleTypeMaster {
			return false, "故障节点中存在主节点", nil
		}
	}

	return true, "", nil
}

func checkProxyCpuLoad(ctx context.Context, app *x1model.Application, param *iface.Parameters) (pass bool, reason string, err error) {

	baseProxyShortIDs := make([]int64, 0)
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if proxy.Status == x1model.NodeOrProxyStatusInUse ||
				proxy.Status == x1model.NodeOrProxyStatusToFakeDelete ||
				proxy.Status == x1model.NodeOrProxyStatusToDelete {
				baseProxyShortIDs = append(baseProxyShortIDs, int64(proxy.ProxyShortID))
			}
		}
	}
	faultProxyShortIDs := param.SelfHealFromCsmaster.ProxyShortIDs

	monStartTime, monEndTime, interval := getHistoryTimeForMonquery(time.Now(), 600)
	monParam := &opmonitor.MonqueryLeastInsCntParam{
		BaseInsShortIDs: baseProxyShortIDs,
		Item:            opmonitor.ProcCPUUsage,
		SampleFunc:      opmonitor.SampleFuncAvg,
		Start:           monStartTime,
		End:             monEndTime,
		Interval:        interval,
		Threshold:       ProxyCPUThresholdForTrafficRemoval,
	}
	leastInsCnt, err := opmonitor.GetLeastInsCntByMonquery(ctx, app, monParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get least ins cnt error", logit.Error("error", err))
		return false, "", err
	}
	resource.LoggerTask.Trace(ctx, "get least ins cnt",
		logit.Int("totalProxyCnt", len(baseProxyShortIDs)),
		logit.Int("faultProxyCnt", len(faultProxyShortIDs)),
		logit.Int("leastProxyCnt", leastInsCnt),
		logit.Int("threshold", ProxyCPUThresholdForTrafficRemoval),
		logit.String("appID", app.AppId),
		logit.String("faultProxys", base_utils.Format(faultProxyShortIDs)),
	)

	if len(baseProxyShortIDs)-len(faultProxyShortIDs) < leastInsCnt {
		resource.LoggerTask.Error(ctx, "cpu load is too high, skip traffic removal",
			logit.Int("totalInsCnt", len(baseProxyShortIDs)),
			logit.Int("faultInsCnt", len(faultProxyShortIDs)),
			logit.Int("leastInsCnt", leastInsCnt),
			logit.Int("threshold", ProxyCPUThresholdForTrafficRemoval),
			logit.String("appID", app.AppId),
		)
		return false, TrafficRemovalUnpassReasonProxyCPU, nil
	}
	if len(baseProxyShortIDs)-len(faultProxyShortIDs) <= ProxyCntThresholdForTrafficRemoval {
		resource.LoggerTask.Error(ctx, "remove too many instances, skip traffic removal",
			logit.Int("totalInsCnt", len(baseProxyShortIDs)),
			logit.Int("faultInsCnt", len(faultProxyShortIDs)),
			logit.Int("threshold", ProxyCntThresholdForTrafficRemoval),
			logit.String("appID", app.AppId),
		)
		return false, TrafficRemovalUnpassReasonProxyCnt, nil
	}

	return true, "", nil
}

func checkNodeCpuLoad(ctx context.Context, app *x1model.Application, param *iface.Parameters) (bool, string, error) {

	faultClusters := make(map[string][]int64) // clusterID -> []NodeShortID
	for _, nodeShortID := range param.SelfHealFromCsmaster.NodeShortIDs {
		node, _, err := buildmeta.FindNodeByShortIDInApp(ctx, app, nodeShortID)
		if err != nil {
			resource.LoggerTask.Error(ctx, "node not found", logit.Error("error", err))
			return false, "", err
		}
		clusterID := node.ClusterId
		if _, ok := faultClusters[clusterID]; !ok {
			faultClusters[clusterID] = make([]int64, 0)
		}
		faultClusters[clusterID] = append(faultClusters[clusterID], int64(node.NodeShortID))
	}
	resource.LoggerTask.Trace(ctx, "faultClusters", logit.String("faultClusters", base_utils.Format(faultClusters)))

	for clusterID, faultNodeIDs := range faultClusters {
		pass, reason, err := checkClusterCpuLoad(ctx, app, clusterID, faultNodeIDs)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get least ins cnt error", logit.Error("error", err))
			return false, "", err
		}
		if !pass {
			resource.LoggerTask.Error(ctx, "node do not pass check, skip traffic removal",
				logit.String("clusterID", clusterID),
				logit.String("faultNodes", base_utils.Format(faultNodeIDs)),
				logit.String("appID", app.AppId),
			)
			return false, reason, nil
		}
	}

	return true, "", nil
}

func checkClusterCpuLoad(ctx context.Context, app *x1model.Application, clusterID string, faultNodes []int64) (bool, string, error) {

	var faultCluster *x1model.Cluster
	for i, cluster := range app.Clusters {
		if cluster.ClusterId == clusterID {
			faultCluster = app.Clusters[i]
			break
		}
	}

	baseNodeShortIDs := make([]int64, 0)
	for _, node := range faultCluster.Nodes {
		if node.Status == x1model.NodeOrProxyStatusInUse ||
			node.Status == x1model.NodeOrProxyStatusToFakeDelete ||
			node.Status == x1model.NodeOrProxyStatusToDelete {
			baseNodeShortIDs = append(baseNodeShortIDs, int64(node.NodeShortID))
		}
	}

	monStartTime, monEndTime, interval := getHistoryTimeForMonquery(time.Now(), 600)
	monParam := &opmonitor.MonqueryLeastInsCntParam{
		BaseInsShortIDs: baseNodeShortIDs,
		Item:            opmonitor.ProcCPUUsage,
		SampleFunc:      opmonitor.SampleFuncAvg,
		Start:           monStartTime,
		End:             monEndTime,
		Interval:        interval,
		Threshold:       NodeCPUThresholdForTrafficRemoval,
	}
	leastInsCnt, err := opmonitor.GetLeastInsCntByMonquery(ctx, app, monParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get least ins cnt error", logit.Error("error", err))
		return false, "", err
	}
	resource.LoggerTask.Trace(ctx, "get least ins cnt",
		logit.Int("totalNodeCnt", len(baseNodeShortIDs)),
		logit.Int("faultNodeCnt", len(faultNodes)),
		logit.Int("leastNodeCnt", leastInsCnt),
		logit.Int("threshold", NodeCPUThresholdForTrafficRemoval),
		logit.String("appID", app.AppId),
		logit.String("totalNodes", base_utils.Format(baseNodeShortIDs)),
		logit.String("faultNodes", base_utils.Format(faultNodes)),
	)

	if len(baseNodeShortIDs)-len(faultNodes) < leastInsCnt {
		resource.LoggerTask.Error(ctx, "cpu load is too high, skip traffic removal",
			logit.Int("totalInsCnt", len(baseNodeShortIDs)),
			logit.Int("faultInsCnt", len(faultNodes)),
			logit.Int("leastInsCnt", leastInsCnt),
			logit.Int("threshold", ProxyCPUThresholdForTrafficRemoval),
			logit.String("appID", app.AppId),
			logit.String("clusterID", clusterID),
		)
		return false, TrafficRemovalUnpassReasonNodeCPU, nil
	}
	if len(baseNodeShortIDs)-len(faultNodes) <= NodeCntThresholdForTrafficRemoval {
		resource.LoggerTask.Error(ctx, "remove too many instances, skip traffic removal",
			logit.Int("totalInsCnt", len(baseNodeShortIDs)),
			logit.Int("faultInsCnt", len(faultNodes)),
			logit.String("appID", app.AppId),
			logit.String("clusterID", clusterID),
		)
		return false, TrafficRemovalUnpassReasonNodeCnt, nil
	}

	return true, "", nil
}

func checkRoNodeCpuLoad(ctx context.Context, app *x1model.Application, param *iface.Parameters) (bool, string, error) {

	faultRoGroups := make(map[string][]int64) // roGroupID -> []roNodeShortID
	for _, uhi := range param.UnHealthShards {
		for _, nodeShortID := range uhi.NodeShortIDs {
			roNode, _, err := buildmeta.FindRoNodeByShortIDInApp(ctx, app, nodeShortID)
			if err == nil && roNode != nil {
				roGroupID := roNode.RoGroupID
				if _, ok := faultRoGroups[roGroupID]; !ok {
					faultRoGroups[roGroupID] = make([]int64, 0)
				}
				faultRoGroups[roGroupID] = append(faultRoGroups[roGroupID], int64(roNode.NodeShortID))
			}
		}
	}
	resource.LoggerTask.Trace(ctx, "faultRoGroups", logit.String("faultRoGroups", base_utils.Format(faultRoGroups)))

	for roGroupID, faultRoNodeIDs := range faultRoGroups {
		pass, reason, err := checkRoGroupCpuLoad(ctx, app, roGroupID, faultRoNodeIDs)
		if err != nil {
			resource.LoggerTask.Error(ctx, "get least ins cnt error", logit.Error("error", err))
			return false, "", err
		}
		if !pass {
			resource.LoggerTask.Error(ctx, "ronode do not pass check, skip traffic removal",
				logit.String("roGroupID", roGroupID),
				logit.String("faultRoNodes", base_utils.Format(faultRoNodeIDs)),
				logit.String("appID", app.AppId),
			)
			return false, reason, nil
		}
	}

	return true, "", nil
}

func checkRoGroupCpuLoad(ctx context.Context, app *x1model.Application, roGroupID string, faultRoNodes []int64) (bool, string, error) {
	roGroup, err := resource.CsmasterOpAgent.GetReadonlyGroupByShowID(ctx, roGroupID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get ro group error", logit.Error("error", err))
		return false, "", err
	}
	baseRoNodeShortIDs := make([]int64, 0)
	for _, roCacheInstance := range roGroup.CacheInstances {
		if roCacheInstance.IsReadonly == 1 {
			baseRoNodeShortIDs = append(baseRoNodeShortIDs, int64(roCacheInstance.Id))
		}
	}
	resource.LoggerTask.Trace(ctx, "get ronode in rogroup",
		logit.String("roGroupID", roGroupID),
		logit.String("roGroup", base_utils.Format(roGroup)),
		logit.String("baseRoNodeShortIDs", base_utils.Format(baseRoNodeShortIDs)),
	)

	monStartTime, monEndTime, interval := getHistoryTimeForMonquery(time.Now(), 600)
	monParam := &opmonitor.MonqueryLeastInsCntParam{
		BaseInsShortIDs: baseRoNodeShortIDs,
		Item:            opmonitor.ProcCPUUsage,
		SampleFunc:      opmonitor.SampleFuncAvg,
		Start:           monStartTime,
		End:             monEndTime,
		Interval:        interval,
		Threshold:       RoNodeCPUThresholdForTrafficRemoval,
	}
	leastInsCnt, err := opmonitor.GetLeastInsCntByMonquery(ctx, app, monParam)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get least ins cnt error", logit.Error("error", err))
		return false, "", err
	}
	resource.LoggerTask.Trace(ctx, "get least ins cnt",
		logit.Int("totalRoNodeCnt", len(baseRoNodeShortIDs)),
		logit.Int("faultRoNodeCnt", len(faultRoNodes)),
		logit.Int("leastRoNodeCnt", leastInsCnt),
		logit.Int("threshold", RoNodeCPUThresholdForTrafficRemoval),
		logit.String("appID", app.AppId),
		logit.String("baseRoNodeShortIDs", base_utils.Format(baseRoNodeShortIDs)),
		logit.String("faultRoNode", base_utils.Format(faultRoNodes)),
	)

	if len(baseRoNodeShortIDs)-len(faultRoNodes) < leastInsCnt {
		resource.LoggerTask.Error(ctx, "cpu load is too high, skip traffic removal",
			logit.Int("totalInsCnt", len(baseRoNodeShortIDs)),
			logit.Int("faultInsCnt", len(faultRoNodes)),
			logit.Int("leastInsCnt", leastInsCnt),
			logit.Int("threshold", ProxyCPUThresholdForTrafficRemoval),
			logit.String("appID", app.AppId),
			logit.String("roGroupID", roGroupID),
		)
		return false, TrafficRemovalUnpassReasonRoNodeCPU, nil
	}
	if len(baseRoNodeShortIDs)-len(faultRoNodes) <= RoNodeCntThresholdForTrafficRemoval {
		resource.LoggerTask.Error(ctx, "remove too many instances, skip traffic removal",
			logit.Int("totalInsCnt", len(baseRoNodeShortIDs)),
			logit.Int("faultInsCnt", len(faultRoNodes)),
			logit.String("appID", app.AppId),
			logit.String("roGroupID", roGroupID),
		)
		return false, TrafficRemovalUnpassReasonRoNodeCnt, nil
	}

	return true, "", nil
}

func getHistoryTimeForMonquery(endTime time.Time, intervalSec int) (string, string, string) {
	monStartTime := endTime.Add(-time.Duration(intervalSec) * time.Second).Format("20060102150405")
	monEndTime := endTime.Format("20060102150405")
	return monStartTime, monEndTime, strconv.Itoa(intervalSec)
}

func SendMessageForTrafficRemoval(ctx context.Context, param *TrafficRemovalMessageParam) error {
	robotName := hi_robot.GetHARobotName(ctx)
	msgType := hi_robot.TypeMarkdown
	msgContent := fmt.Sprintf(
		"##### 宿主机故障通知: <font color=\"red\">%s</font> ##### \n"+
			"> **实例**: %s \n"+
			"> **任务ID**: %s \n"+
			"> **时间**: %s \n"+
			"> **原因**: %s \n"+
			"> **运维平台**: %s \n",
		param.Title,
		param.App.AppId,
		param.TaskID,
		param.MsgTime,
		param.Reason,
		GetMisUrlClusterDetail(env.IDC(), param.App.AppId, int(param.App.AppShortID)),
	)
	err := SendHiMessage(ctx, msgType, msgContent, robotName)
	if err != nil {
		resource.LoggerTask.Error(ctx, "send traffic removal message failed", logit.Error("error", err))
		return err
	}
	return nil
}

func SendHiMessage(ctx context.Context, msgType, msgContent, robotName string) error {
	manualMSG := []*hi_robot.RoboMsgItem{{
		Type:    msgType,
		Content: msgContent,
	}}
	if err := hi_robot.CallHiRot(ctx, manualMSG, robotName); err != nil {
		manualMSG := []*hi_robot.RoboMsgItem{{
			Type:    hi_robot.TypeMarkdown,
			Content: "> **信息超长并截断**\n",
		}}
		resource.LoggerTask.Warning(ctx, "fail to call hi bot", logit.Error("error", err))
		_ = hi_robot.CallHiRot(ctx, manualMSG, robotName)
	}
	return nil
}

func GetMisUrlClusterDetail(region string, appID string, appShortId int) string {
	if appShortId == 0 {
		return "x1元数据shortID为0"
	}
	fmtRegion := "尚未支持"
	fmtEndpoint := "dba.baidu.com"
	switch region {
	case "preonline":
		fmtRegion = "preonline"
	case "bjtest":
		fmtRegion = "bj"
		fmtEndpoint = "dbatest.baidu.com:8097"
	case "edgetest":
		fmtRegion = "edgetest"
		fmtEndpoint = "dbatest.baidu.com:8097"
	case "edge", "onlineedge":
		fmtRegion = "edge"
	case "bj", "onlinebj":
		fmtRegion = "bj"
	case "gz", "onlinegz":
		fmtRegion = "gz"
	case "su", "onlinesu":
		fmtRegion = "su"
	case "suvip", "onlinesuvip":
		fmtRegion = "supdd"
	case "bdbl", "onlinebd":
		fmtRegion = "bdbl"
	case "whgg", "onlinewh":
		fmtRegion = "whgg"
	case "nj", "onlinenj":
		fmtRegion = "nj"
	case "hkg", "onlinehkg":
		fmtRegion = "hkg"
	case "cd", "onlinecd":
		fmtRegion = "cd"
	case "yq", "onlineyq":
		fmtRegion = "yq"
	case "fsh", "onlinefsh":
		fmtRegion = "fsh"
	default:
		return "未知region"
	}
	return fmt.Sprintf("http://%s/mis/SCS/scsResource/scsList/%s_%s_%d/detail", fmtEndpoint, fmtRegion, appID, appShortId)
}
