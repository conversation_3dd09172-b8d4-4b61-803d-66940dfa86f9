/**
* @Copyright 2023 COMP Inc. All Rights Reserved.
* <AUTHOR>
* @Description This file contains ...
* @Date 2023/2/27 11:05
**/

package availability

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// imports

// const

// typedefs

// vars

// functions

func InitHAServer(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// check ha has migrated to csmaster or not
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.UseXmaster == csmaster.CsmasterSetHAServicerUseCsmaster {
		resource.LoggerTask.Notice(ctx, "Default HA servicer is csmaster and target HA server is csmaster,"+
			" so there is nothing to do",
			logit.Int32("use_xmaster", clusterModel.UseXmaster))
	} else {
		resource.LoggerTask.Notice(ctx, "Default HA servicer is csmaster and target HA server is xmaster,"+
			" so we should enable xmaster and disable csmaster",
			logit.Int32("use_xmaster", clusterModel.UseXmaster))

		if err := SwitchOnHAInXmaster(ctx, teu); err != nil {
			resource.LoggerTask.Error(ctx, "switch on xmaster ha failed", logit.Error("error", err))
			return err
		}

		if err := SwitchOffHAInCsmaster(ctx, teu); err != nil {
			// If function returns by error, the caller should fix error and retry until success.
			// And the application will have two ha server in a short time.
			resource.LoggerTask.Error(ctx, "switch off csmaster ha failed", logit.Error("error", err))
			return err
		}
	}
	return nil
}
