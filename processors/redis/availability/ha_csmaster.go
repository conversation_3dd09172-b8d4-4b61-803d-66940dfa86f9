package availability

import (
	"context"

	"github.com/pkg/errors"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

func IsAppUseCsmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// check ha has migrated to csmaster or not
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.UseXmaster == csmaster.CsmasterSetHAServicerUseCsmaster {
		resource.LoggerTask.Warning(ctx, "HA servicer has been migrated to csmaster")
		return errors.Errorf("HA servicer has been migrated to csmaster")
	}

	return nil
}

func CheckAndUpdateAppStatus(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get app fail", logit.String("appId", teu.Entity),
			logit.Error("dbError", err))
		return cerrs.ErrDbQueryFail.Wrap(err)
	}

	// check app has migrated to x1 or not
	clusterDetail, err := csmaster.CsmasterOp().CsmasterGetClusterDetail(ctx, &csmaster.ListCacheClusterInstancesParam{
		UserID: app.UserId,
		AppID:  app.AppId,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster detail failed", logit.Error("error", err))
		return err
	}
	if clusterDetail.HitX1 != 1 {
		resource.LoggerTask.Warning(ctx, "cluster has not migrated to x1", logit.Error("error", err))
		return errors.Errorf("cluster has not migrated to x1")
	}

	// check cluster status
	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.Status != csmaster.CsmasterStatusRunning {
		resource.LoggerTask.Warning(ctx, "cluster is in status must not migrate")
		return errors.Errorf("cluster is in status must not migrate")
	}

	// update cluster status to 24
	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			Status: csmaster.CsmasterStatusMigrateToX1,
		},
		UserID: app.UserId,
		AppID:  app.AppId,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "update cache cluster model err", logit.Error("error", err))
		return err
	}
	resource.LoggerTask.Trace(ctx, "success to update cache cluster status to 24 for mig HA servicer")

	return nil
}

func CheckAppInstances(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}
	// todo: 检查proxy创建日期，目前proxy没有记录创建时间；

	return nil
}

func SwitchOnHAInCsmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	if err := setHASwitchInCsmaster(ctx, teu.Entity, true); err != nil {
		resource.LoggerTask.Error(ctx, "switch on csmaster ha failed", logit.Error("error", err))
		return err
	}

	return nil
}

func SwitchOffHAInCsmaster(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if teu == nil {
		resource.LoggerTask.Warning(ctx, "teu is nilptr")
		return errors.Errorf("teu is nilptr")
	}

	if err := setHASwitchInCsmaster(ctx, teu.Entity, false); err != nil {
		resource.LoggerTask.Error(ctx, "switch off csmaster ha failed", logit.Error("error", err))
		return err
	}

	return nil
}

// Deprecated: replaced by setHASwitchInCsmaster
func setHASwitchInCsmasterV1(ctx context.Context, clusterShowID string, enable bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, clusterShowID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}

	// set switcher in mem
	action := csmaster.CsmasterNotEnableReplaceActionAdd
	if enable {
		action = csmaster.CsmasterNotEnableReplaceActionDel // enable 为true，表示开启csmaster高可用，故将集群从内存表中delete
	}
	if err = csmaster.CsmasterOp().CsmasterNotEnableReplace(ctx, &csmaster.NotEnableReplaceParam{
		UserID:    app.UserId,
		Action:    action,
		ClusterID: clusterModel.Id,
		PassWD:    csmaster.CsmasterNotEnableReplacePasswd,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "set csmaster cluster not enable replace err",
			logit.String("action", action),
			logit.String("appID", clusterShowID),
			logit.Error("error", err))
		return err
	}

	// set switcher in db
	var notEnableReplace int32 = 1
	if enable {
		notEnableReplace = 0 // enable 为true，表示开启csmaster高可用，故将集群数据表中notEnableReplace字段设置为0
	}
	if err = csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			NotEnableReplace: notEnableReplace,
		},
		UserID:         app.UserId,
		AppID:          app.AppId,
		RequiredFields: []string{"not_enable_replace"},
	}); err != nil {
		resource.LoggerTask.Error(ctx, "update cluster model err", logit.Error("error", err))
		goto rollback
	}

	return nil

rollback:
	if action == csmaster.CsmasterNotEnableReplaceActionAdd {
		action = csmaster.CsmasterNotEnableReplaceActionDel
	} else {
		action = csmaster.CsmasterNotEnableReplaceActionAdd
	}
	if errR := csmaster.CsmasterOp().CsmasterNotEnableReplace(ctx, &csmaster.NotEnableReplaceParam{
		Action:    action,
		ClusterID: clusterModel.Id,
		PassWD:    csmaster.CsmasterNotEnableReplacePasswd,
	}); errR != nil {
		resource.LoggerTask.Error(ctx, "set csmaster cluster not enable replace err",
			logit.String("action", action),
			logit.String("appID", clusterShowID),
			logit.Error("error", err))
		return errR
	}

	return err
}

// setHASwitchInCsmaster, 由于not_enable_replace字段语义变更为'集群高可用开关'，不再是'集群在csmaster方面的高可用开关'，
// 故这里仅设置csmaster内存态开关，不再更新该字段；该字段应在集群高可用开关设置接口中更新；
func setHASwitchInCsmaster(ctx context.Context, clusterShowID string, enable bool) error {
	app, err := x1model.ApplicationGetByAppId(ctx, clusterShowID)
	if err != nil {
		resource.LoggerTask.Error(ctx, "get app error", logit.Error("error", err))
		return err
	}

	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}

	// set switcher in mem
	action := csmaster.CsmasterNotEnableReplaceActionAdd
	if enable {
		action = csmaster.CsmasterNotEnableReplaceActionDel // enable 为true，表示开启csmaster高可用，故将集群从内存表中delete
	}
	if err = csmaster.CsmasterOp().CsmasterNotEnableReplace(ctx, &csmaster.NotEnableReplaceParam{
		UserID:    app.UserId,
		Action:    action,
		ClusterID: clusterModel.Id,
		PassWD:    csmaster.CsmasterNotEnableReplacePasswd,
	}); err != nil {
		resource.LoggerTask.Error(ctx, "set csmaster cluster not enable replace err",
			logit.String("action", action),
			logit.String("appID", clusterShowID),
			logit.Error("error", err))
		return err
	}

	return err
}
