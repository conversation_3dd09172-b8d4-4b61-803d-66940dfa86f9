/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/25
 * File: check_alive.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"fmt"

	"github.com/bradfitz/gomemcache/memcache"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

// ProcessDeployMcForNewCreate 部署/探活
// 比老版本多一次探活
func ProcessDeployMcForNewCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	return checkMcAlive(ctx, teu.Entity)
}

func checkMcAlive(ctx context.Context, appid string) error {
	app, err := x1model.ApplicationGetByAppId(ctx, appid)
	if err != nil {
		return fmt.Errorf("get app fail,err:%w", err)
	}
	var mcInsHosts []string
	for _, c := range app.Clusters {
		for _, n := range c.Nodes {
			if isMcNodeAvailable(n) {
				mcInsHosts = append(mcInsHosts, fmt.Sprintf("%s:%d", n.FloatingIP, n.Port))
			}
		}
	}
	resource.LoggerTask.Trace(ctx, "before add proxy",
		logit.String("mc ins hosts", base_utils.Format(mcInsHosts)))
	for _, itf := range app.Interfaces {
		for _, p := range itf.Proxys {
			if p.Status == x1model.NodeOrProxyStatusInUse ||
				p.Status == x1model.NodeOrProxyStatusToCreate {
				mcInsHosts = append(mcInsHosts, fmt.Sprintf("%s:%d", p.FloatingIP, p.Port))
			}
		}
	}
	resource.LoggerTask.Trace(ctx, "after add proxy",
		logit.String("mc ins hosts", base_utils.Format(mcInsHosts)))
	mcCli := memcache.New(mcInsHosts...)
	if err := mcCli.Ping(); err != nil {
		resource.LoggerTask.Trace(ctx, "check mc alive fail", logit.Error("Err", err))
		return fmt.Errorf("ping mc ins fail,err:%w", err)
	}
	resource.LoggerTask.Trace(ctx, "check mc alive success")

	return nil
}

func isMcNodeAvailable(node *x1model.Node) bool {
	return node.Status == x1model.NodeOrProxyStatusInUse ||
		node.Status == x1model.NodeOrProxyStatusToCreate
}
