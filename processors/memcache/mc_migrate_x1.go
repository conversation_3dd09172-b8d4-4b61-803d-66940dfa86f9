/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/11/16
 * File: migrate_x1.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/csmaster/csmaster_model_interface"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	taskresource "icode.baidu.com/baidu/scs/x1-base/task/resource"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/migratetox1"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/common"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

func ProcessMigrateMcToX1PreCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processMigrateMcToX1BeforeCheck(ctx, teu); err != nil {
		if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
			return err2
		}
		return err
	}
	return nil
}

func processMigrateMcToX1BeforeCheck(ctx context.Context, teu *workflow.TaskExecUnit) error {
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get cache cluster fail,err:%w", err)
	}
	resource.LoggerTask.Trace(ctx, "trace user info", logit.String("user info", base_utils.Format(cacheCluster.UserInfo)))

	clusterDetail, err := csmaster.CsmasterOp().CsmasterGetClusterDetail(ctx, &csmaster.ListCacheClusterInstancesParam{
		UserID: cacheCluster.UserInfo.IamUserId,
		AppID:  teu.Entity,
	})
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster detail failed", logit.Error("error", err))
		return err
	}
	if clusterDetail.HitX1 == 1 {
		resource.LoggerTask.Warning(ctx, "cluster has been migrated to x1", logit.Error("error", err))
		return err
	}
	if cacheCluster.Status != 5 {
		return errors.New("must 5")
	}

	if err := csmaster.CsmasterOp().UpdateClusterModel(ctx, &csmaster.UpdateClusterModelParams{
		Model: &csmaster.CsmasterCluster{
			ClusterShowId: clusterDetail.CacheClusterShowID,
			Status:        csmaster.CsmasterStatusMigrateToX1,
		},
		UserID: cacheCluster.UserInfo.IamUserId,
		AppID:  clusterDetail.CacheClusterShowID,
	}); err != nil {
		resource.LoggerTask.Warning(ctx, "update cluster model failed", logit.Error("error", err))
		return err
	}

	return nil
}

func ProcessMigrateMcToX1(ctx context.Context, teu *workflow.TaskExecUnit) error {
	if err := processMigrateMcToX1(ctx, teu); err != nil {
		if err2 := taskresource.TaskOperator.UpdateTaskToManual(ctx, teu.TaskID, util.GetStep(teu), err.Error()); err2 != nil {
			return err2
		}
		return err
	}
	return nil
}

func processMigrateMcToX1(ctx context.Context, teu *workflow.TaskExecUnit) error {
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get cache cluster fail,err:%w", err)
	}

	clusterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, cacheCluster.UserInfo.IamUserId, teu.Entity)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get cluster model failed", logit.Error("error", err))
		return err
	}
	if clusterModel.Status != csmaster.CsmasterStatusMigrateToX1 {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("expect cluster status is 24, but got %d", clusterModel.Status))
		return fmt.Errorf("expect cluster status is 24, but got %d", clusterModel.Status)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err == nil && app.Status == x1model.AppStatusInUse {
		resource.LoggerTask.Warning(ctx, "app is in use")
		return errors.New("app is in use")
	}
	app, err = createApp(ctx, cacheCluster, clusterModel)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "create app failed", logit.Error("error", err))
		return err
	}
	instances, err := csmaster.CsmasterOp().GetInstanceModels(ctx, app.UserId, app.AppId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get instance models failed", logit.Error("error", err))
		return err
	}
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, cacheCluster.UserInfo.IamUserId)
	if err != nil {
		resource.LoggerTask.Warning(ctx, "get zone map failed", logit.Error("error", err))
		return err
	}
	if err := createMcClusters(ctx, cacheCluster, app, instances, zoneMapper); err != nil {
		resource.LoggerTask.Warning(ctx, "create clusters failed", logit.Error("error", err))
		return err
	}
	if err := createInterfaces(ctx, cacheCluster, app, instances, zoneMapper); err != nil {
		resource.LoggerTask.Warning(ctx, "create interfaces failed", logit.Error("error", err))
		return err
	}
	if err := createBlbs(ctx, app, clusterModel); err != nil {
		resource.LoggerTask.Warning(ctx, "create blbs failed", logit.Error("error", err))
		return err
	}
	if err := updateSubnet(cacheCluster, app); err != nil {
		resource.LoggerTask.Warning(ctx, "update subnet failed", logit.Error("error", err))
		return err
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		resource.LoggerTask.Warning(ctx, "save applications failed", logit.Error("error", err))
		return err
	}
	return nil
}

func createApp(ctx context.Context, cacheCluster *csmaster_model_interface.CacheCluster, clusterModel *csmaster.CsmasterCluster) (*x1model.Application, error) {
	createTime, err := time.ParseInLocation("2006-01-02 15:04:05", clusterModel.CreateTime, time.UTC)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
		return nil, err
	}
	replicas := []*iface.Replica{
		{
			Zone:      clusterModel.AvailabilityZone,
			SubnetIDs: []string{clusterModel.SubnetId},
			Role:      "master",
			Count:     1,
		}}
	app := &x1model.Application{
		AppId:                   clusterModel.ClusterShowId,
		AppName:                 clusterModel.ClusterName,
		Product:                 x1model.ProductSCS,
		Type:                    x1model.EngineMc,
		Port:                    int(cacheCluster.Port.Int32),
		McpackPort:              int(cacheCluster.Port.Int32 + 1),
		InnerPort:               8080, // csmaster master.conf default_vm_port
		Azone:                   "",
		Rzone:                   "",
		VpcId:                   clusterModel.VpcId,
		UserId:                  cacheCluster.UserInfo.IamUserId,
		Status:                  x1model.AppStatusInUse,
		Replicas:                base_utils.Format(replicas),
		CreateTime:              createTime,
		UpdateTime:              time.Now(),
		SecurityGroupId:         clusterModel.SecurityGroupId,
		InternalSecurityGroupId: clusterModel.ShardSecurityGroupId,
		AppShortID:              int(clusterModel.Id),
		UserShortID:             int(cacheCluster.UserId),
		ImageID:                 conf.MemcacheConf.Image.Bcc,
		DeploySetIds:            clusterModel.DeployIdList,
		Domain:                  clusterModel.Domain,
		LocalMetaserver:         cacheCluster.MetaserverId,
		Region:                  common.GetCsmasterIDC(),
		IpType:                  x1model.Ipv4,
	}

	return app, nil
}

func createMcClusters(ctx context.Context, cacheCluster *csmaster_model_interface.CacheCluster, app *x1model.Application,
	insts []*csmaster.CsmasterInstance, zoneMapper zone.ZoneMapperFunc) error {
	cmSIList := getMcNodes(insts)
	maxNodeId := 0
	clusterIdx := 0
	nodeType := cacheCluster.NodeType
	if cacheCluster.DestNodeType != "" {
		nodeType = cacheCluster.DestNodeType
	}
	for _, cmSI := range cmSIList {
		if cmSI.Instances[0].CacheInstanceType == 0 {
			continue
		}
		clusterPort := 8080 // csmaster master.conf default_vm_port
		cluster := &x1model.Cluster{
			ClusterId:      app.AppId + "-" + fmt.Sprintf("%d", clusterIdx),
			AppId:          app.AppId,
			Engine:         x1model.EngineMc,
			EngineVersion:  cacheCluster.KernelVersion,
			Status:         x1model.ClusterStatusInUse,
			Port:           clusterPort,
			StoreType:      x1model.StoreTypeDRAM,
			Spec:           "",
			DestSpec:       nodeType,
			CreateTime:     time.Now(),
			UpdateTime:     time.Now(),
			ClusterShortID: int(cmSI.ShardId),
		}
		app.Clusters = append(app.Clusters, cluster)
		if err := createNodesForCluster(ctx, cacheCluster, app, cluster,
			cmSI.Instances, &maxNodeId, zoneMapper); err != nil {
			return err
		}
		clusterIdx++
	}
	return nil
}

func getMcNodes(insts []*csmaster.CsmasterInstance) migratetox1.CsmasterShardInstancesList {
	cmSIMap := make(map[int64][]*csmaster.CsmasterInstance)
	for _, inst := range insts {
		if inst.CacheInstanceType == 0 {
			continue
		}
		if _, ok := cmSIMap[inst.Id]; !ok {
			cmSIMap[inst.Id] = make([]*csmaster.CsmasterInstance, 0)
		}
		cmSIMap[inst.Id] = append(cmSIMap[inst.Id], inst)
	}
	cmSIList := make(migratetox1.CsmasterShardInstancesList, 0)
	for shardId, insts := range cmSIMap {
		cmSIList = append(cmSIList,
			&migratetox1.CsmasterShardInstances{ShardId: shardId, Instances: insts})
	}
	sort.Sort(cmSIList)
	return cmSIList
}

func createNodesForCluster(ctx context.Context, cacheCluster *csmaster_model_interface.CacheCluster, app *x1model.Application,
	cluster *x1model.Cluster, insts []*csmaster.CsmasterInstance, maxNodeId *int, zoneMapper zone.ZoneMapperFunc) error {
	for _, inst := range insts {
		role := "master"
		if inst.CacheInstanceType == 2 {
			role = "slave"
		}
		if len(inst.Ipv6) != 0 {
			app.IpType = x1model.Ipv6
		}
		azone, ok := zoneMapper(cacheCluster.AvailabilityZone, true)
		if !ok {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("unsupported availability zone %s", cacheCluster.AvailabilityZone))
			return fmt.Errorf("unsupported availability zone %s", cacheCluster.AvailabilityZone)
		}
		newNode := x1model.Node{
			AppId:         cluster.AppId,
			ClusterId:     cluster.ClusterId,
			NodeId:        cluster.ClusterId + "." + fmt.Sprintf("%d", *maxNodeId),
			Engine:        cluster.Engine,
			EngineVersion: cluster.EngineVersion,
			Port:          int(inst.Port),
			Basedir:       util.DefaultBaseDir,
			Ip:            inst.FixIp,
			XagentPort:    x1model.DefaultXagentPort,
			Region:        common.GetCsmasterIDC(),
			LogicZone:     cacheCluster.AvailabilityZone,
			Azone:         azone,
			VpcId:         cacheCluster.VpcId,
			SubnetId:      inst.SubnetId,
			Role:          role,
			Status:        x1model.NodeOrProxyStatusInUse,
			ResourceId:    inst.Uuid,
			FloatingIP:    inst.FloatingIp,
			IPv6:          inst.Ipv6,
			RootPassword:  inst.Password,
			NodeShortID:   int(inst.Id),
			HostName:      inst.HostName,
			NodeFixID:     inst.NodeShowId,
		}
		if newNode.NodeFixID == "" {
			newNode.NodeFixID = app.AppId + "_redis_" +
				strconv.Itoa(cluster.ClusterShortID) + "_" +
				strconv.Itoa(cluster.MaxNodeIndex)
		}

		cluster.Nodes = append(cluster.Nodes, &newNode)
		cluster.MaxNodeIndex++
		*maxNodeId++
	}

	return nil
}

func createInterfaces(ctx context.Context, cacheCluster *csmaster_model_interface.CacheCluster,
	app *x1model.Application, insts []*csmaster.CsmasterInstance, zoneMapper zone.ZoneMapperFunc) error {
	var proxys []*csmaster.CsmasterInstance
	for _, i := range insts {
		if i.CacheInstanceType == 0 {
			proxys = append(proxys, i)
		}
	}
	if len(proxys) == 0 {
		return errors.New("no proxy")
	}

	maxProxyId := 0

	proxyGetter := getMcProxyGetter(proxys, cacheCluster)
	totalProxyCount := len(proxys)
	proxyCountPerAz := 2
	zoneCount := 2
	itfCount := totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		itfCount++
	}
	for i := 0; i < itfCount; i++ {
		itf := &x1model.Interface{
			InterfaceId:   app.AppId + "-itf-" + fmt.Sprintf("%d", i),
			Engine:        x1model.EngineMcProxy,
			EngineVersion: cacheCluster.KernelVersion,
			Port:          int(proxys[0].Port),
			AppId:         app.AppId,
			StoreType:     x1model.StoreTypeDRAM,
			Spec:          "",
			DestSpec:      "proxy.n1.small",
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		}
		app.Interfaces = append(app.Interfaces, itf)
		if err := createProxyForItf(ctx, cacheCluster, app, itf, proxyGetter, &maxProxyId, proxyCountPerAz, zoneMapper); err != nil {
			return err
		}
	}

	return nil
}

func getMcProxyGetter(insts []*csmaster.CsmasterInstance, cacheCluster *csmaster_model_interface.CacheCluster) func(zone string) (*csmaster.CsmasterInstance, error) {
	type zoneIter struct {
		idx   int
		zone  string
		insts []*csmaster.CsmasterInstance
	}
	zoneMap := make(map[string]*zoneIter)
	cacheClusterZone := cacheCluster.AvailabilityZone
	for _, inst := range insts {
		if inst.CacheInstanceType != 0 {
			continue
		}
		if _, ok := zoneMap[cacheClusterZone]; !ok {
			zoneMap[cacheClusterZone] = &zoneIter{
				idx:   0,
				zone:  cacheClusterZone,
				insts: make([]*csmaster.CsmasterInstance, 0),
			}
		}
		zoneMap[cacheClusterZone].insts = append(zoneMap[cacheClusterZone].insts, inst)
	}
	return func(zone string) (*csmaster.CsmasterInstance, error) {
		if _, ok := zoneMap[zone]; !ok {
			return nil, fmt.Errorf("no instance in zone %s", zone)
		}
		itf := zoneMap[zone]
		if itf.idx >= len(itf.insts) {
			return nil, fmt.Errorf("no instance in zone %s", zone)
		}
		inst := itf.insts[itf.idx]
		itf.idx++
		return inst, nil
	}
}

func createProxyForItf(ctx context.Context, cacheCluster *csmaster_model_interface.CacheCluster,
	app *x1model.Application, itf *x1model.Interface,
	proxyGetter func(zone string) (*csmaster.CsmasterInstance, error),
	maxProxyId *int, proxyCountPerAz int, zoneMapper zone.ZoneMapperFunc) error {
	for i := 0; i < proxyCountPerAz; i++ {
		inst, err := proxyGetter(cacheCluster.AvailabilityZone)
		if err != nil {
			return err
		}
		azone, ok := zoneMapper(cacheCluster.AvailabilityZone, true)
		if !ok {
			resource.LoggerTask.Warning(ctx, fmt.Sprintf("unsupported availability zone %s", cacheCluster.AvailabilityZone))
			return fmt.Errorf("unsupported availability zone %s", cacheCluster.AvailabilityZone)
		}
		newPorxy := x1model.Proxy{
			ProxyId:         itf.InterfaceId + "." + fmt.Sprintf("%d", *maxProxyId),
			Ip:              inst.FixIp,
			Port:            int(inst.Port),
			McpackPort:      int(inst.Port) + 1,
			StatPort:        22222,
			Region:          app.Region,
			LogicZone:       cacheCluster.AvailabilityZone,
			Azone:           azone,
			VpcId:           app.VpcId,
			XagentPort:      x1model.DefaultXagentPort,
			AppId:           app.AppId,
			Basedir:         util.DefaultBaseDir,
			Status:          x1model.NodeOrProxyStatusInUse,
			InterfaceId:     itf.InterfaceId,
			ResourceOrderId: "",
			SubnetId:        inst.SubnetId,
			ResourceId:      inst.Uuid,
			FloatingIP:      inst.FloatingIp,
			IPv6:            inst.Ipv6,
			RootPassword:    inst.Password,
			Engine:          itf.Engine,
			EngineVersion:   itf.EngineVersion,
			ProxyShortID:    int(inst.Id),
			HostName:        inst.HostName,
			NodeFixID:       inst.NodeShowId,
		}
		if newPorxy.NodeFixID == "" {
			chunks := strings.Split(newPorxy.ProxyId, ".")
			offset, err := strconv.Atoi(chunks[len(chunks)-1])
			if err != nil {
				resource.LoggerTask.Warning(ctx, "get proxy offset failed", logit.Error("error", err))
				return err
			}
			newPorxy.NodeFixID = app.AppId + "_proxy_" + strconv.Itoa(offset)
		}
		itf.Proxys = append(itf.Proxys, &newPorxy)
		*maxProxyId++
	}
	return nil
}

func createBlbs(ctx context.Context, app *x1model.Application, clusterModel *csmaster.CsmasterCluster) error {
	createTime, err := time.ParseInLocation("2006-01-02 15:04:05", clusterModel.CreateTime, time.UTC)
	if err != nil {
		resource.LoggerTask.Warning(ctx, fmt.Sprintf("parse create time failed, err: %s", err.Error()))
		return err
	}
	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:             app.AppId,
		Name:              app.AppId,
		Type:              x1model.BLBTypeNormal,
		VpcId:             app.VpcId,
		SubnetId:          clusterModel.SubnetId,
		IpType:            x1model.Ipv4,
		BgwGroupId:        "",
		BgwGroupExclusive: 0,
		BgwGroupMode:      "",
		MasterAZ:          "",
		SlaveAZ:           "",
		BlbId:             clusterModel.ElbId,
		Vip:               "",
		Ovip:              clusterModel.ElbPnetip,
		Ipv6:              "",
		Status:            x1model.BLBStatusAvailable,
		CreateAt:          createTime,
		UpdateAt:          time.Now(),
	})
	if app.IpType == x1model.Ipv6 {
		app.BLBs = append(app.BLBs, &x1model.BLB{
			AppId:             app.AppId,
			Name:              "",
			Type:              x1model.BLBTypeNormal,
			VpcId:             app.VpcId,
			SubnetId:          clusterModel.SubnetId,
			IpType:            x1model.Ipv6,
			BgwGroupId:        "",
			BgwGroupExclusive: 0,
			BgwGroupMode:      "",
			MasterAZ:          "",
			SlaveAZ:           "",
			BlbId:             clusterModel.ElbIpv6Id,
			Vip:               "",
			Ovip:              clusterModel.ElbIpv6,
			Ipv6:              "",
			Status:            x1model.BLBStatusAvailable,
			CreateAt:          createTime,
			UpdateAt:          time.Now(),
		})
	}
	return nil
}

func updateSubnet(clusterModel *csmaster_model_interface.CacheCluster, app *x1model.Application) error {
	replicas := []*iface.Replica{
		{
			Zone:      clusterModel.AvailabilityZone,
			SubnetIDs: []string{clusterModel.SubnetId},
			Role:      "master",
			Count:     1,
		}}

	relicasMap := make(map[string]*iface.Replica)
	for _, replica := range replicas {
		relicasMap[replica.Zone] = replica
	}
	for _, cluster := range app.Clusters {
		for _, node := range cluster.Nodes {
			if len(node.SubnetId) != 0 {
				continue
			}
			replica, ok := relicasMap[node.LogicZone]
			if ok && len(replica.SubnetIDs) > 0 {
				node.SubnetId = replica.SubnetIDs[0]
			}
		}
	}
	for _, itf := range app.Interfaces {
		for _, proxy := range itf.Proxys {
			if len(proxy.SubnetId) != 0 {
				continue
			}
			replica, ok := relicasMap[proxy.LogicZone]
			if ok && len(replica.SubnetIDs) > 0 {
				proxy.SubnetId = replica.SubnetIDs[0]
			}
		}
	}
	return nil
}
