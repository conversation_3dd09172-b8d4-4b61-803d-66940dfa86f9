/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/25
 * File: buildmeta_replace.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/extension/gtask"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"
	"icode.baidu.com/baidu/scs/x1-base/utils/lock"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessBuildMetaForSelfHeal(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		return fmt.Errorf("parse param fail,err:%w", err)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	csmasterModel, err := csmaster.CsmasterOp().GetClusterModel(ctx, app.UserId, app.AppId)
	if err != nil {
		return fmt.Errorf("get csmaster model error:%w", err)
	}

	toHealclusterNodeIdsMap := make(map[string][]string)
	for _, un := range param.SelfHealFromCsmaster.NodeShortIDs {
		node, cluster, err := buildmeta.FindNodeByShortIDInApp(ctx, app, un)
		if err != nil {
			return fmt.Errorf("node not found shortid:%d", un)
		}
		if _, ok := toHealclusterNodeIdsMap[cluster.ClusterId]; !ok {
			toHealclusterNodeIdsMap[cluster.ClusterId] = make([]string, 0)
		}
		toHealclusterNodeIdsMap[cluster.ClusterId] = append(toHealclusterNodeIdsMap[cluster.ClusterId], node.NodeId)
	}
	resource.LoggerTask.Trace(ctx, "Unhealth cluster NodeIds Map",
		logit.String("toHealclusterNodeIdsMap", base_utils.Format(toHealclusterNodeIdsMap)))

	g := gtask.Group{}
	for clusterId, nodeIds := range toHealclusterNodeIdsMap {
		clusterId := clusterId
		nodeIds := nodeIds
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				return markToDeleteNodeInCluster(ctx, clusterId, nodeIds, csmasterModel.ReplicationNum, param)
			})
		})
	}

	for _, up := range param.SelfHealFromCsmaster.ProxyShortIDs {
		proxy, err := buildmeta.FindProxyByShortIDInApp(ctx, app, up)
		if err != nil {
			return fmt.Errorf("proxy not found shortid:%d", up)
		}
		g.Go(func() error {
			return gtask.NoPanic(func() error {
				// todo 可以加Memcache的二次校验拦截，老的没有
				resource.LoggerTask.Notice(ctx, "mark proxy to delete", logit.String("proxy", proxy.ProxyId))
				proxy.Status = x1model.NodeOrProxyStatusToFakeDelete
				if err := x1model.ProxysSave(ctx, []*x1model.Proxy{proxy}); err != nil {
					resource.LoggerTask.Error(ctx, "save proxy error", logit.Error("error", err))
					return err
				}
				return nil
			})
		})
	}
	if _, err = g.Wait(); err != nil {
		return fmt.Errorf("mark to delete node error:%w", err)
	}

	app, err = x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	if err := util.AddNewMcNodesForReplacing(ctx, app, util.GetNewNodeForReplacingAction, ""); err != nil {
		return fmt.Errorf("add new nodes for replcaing failed,err:%w", err)
	}
	if err := util.AddNewMcProxyForReplacing(ctx, app, ""); err != nil {
		return fmt.Errorf("add new proxy for replcaing failed,err:%w", err)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		return fmt.Errorf("save app error,err:%w", err)
	}
	return nil
}

func markToDeleteNodeInCluster(ctx context.Context, clusterId string, toHealNodeIds []string, replicationNum int32, param *iface.Parameters) error {
	unlock, err := lock.BlockLock(ctx, "shardswitch_"+clusterId, 15*time.Second, 15*time.Second)
	if err != nil {
		return fmt.Errorf("get switch shard lock error,err:%w", err)
	}
	defer unlock()

	resource.LoggerTask.Notice(ctx, "mark to delete node in cluster",
		logit.String("cluster", clusterId), logit.String("toHealNodeIds", base_utils.Format(toHealNodeIds)))

	cluster, err := x1model.ClusterGetByClusterId(ctx, clusterId)
	if err != nil {
		return fmt.Errorf("get cluster error,err:%w", err)
	}

	for _, node := range cluster.Nodes {
		if !param.SelfHealFromCsmaster.IsForceReplace {
			// todo 可以加Memcache的二次校验拦截，老的没有
		}
		resource.LoggerTask.Notice(ctx, "mark node to delete", logit.String("node", node.NodeId))
		node.Status = x1model.NodeOrProxyStatusToFakeDelete
	}
	if err := x1model.ClusterSave(ctx, []*x1model.Cluster{cluster}); err != nil {
		return fmt.Errorf("save cluster fail,err:%w", err)
	}
	return nil
}
