/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/11/03
 * File: restart_proxy.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/component/csmaster"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
)

func ProcessRestartProxy(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	if err := csmaster.CsmasterOp().RestartMcProxy(ctx, app.UserId, app.AppId); err != nil {
		return fmt.Errorf("restart mc proxy fail,err:%w", err)
	}
	// 等一下避免心跳没发完
	time.Sleep(5 * time.Second)
	return nil
}
