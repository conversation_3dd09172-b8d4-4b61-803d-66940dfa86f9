/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/25
 * File: buildmeta_modify_shards.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/common/cerrs"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
)

func ProcessBuildMetaForMCModifyShards(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		return fmt.Errorf("parse param fail,err:%w", err)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	unlock, err := util.LockForX1modelModify(ctx, app.AppId)
	if err != nil {
		return fmt.Errorf("lock for x1model modify failed,err:%w", err)
	}
	defer unlock()
	// 防止后续操作把内存态数据变更了，因此在这里固化一个分片数
	shardNum := len(app.Clusters)

	if param.ShardCount > shardNum {
		if err := addShards(ctx, app, param); err != nil {
			return fmt.Errorf("add shards error,err:%w", err)
		}
	}
	if param.ShardCount < shardNum {
		for i := 0; i < len(app.Clusters)-param.ShardCount; i++ {
			toDelcluster := app.Clusters[len(app.Clusters)-1-i]
			for _, node := range toDelcluster.Nodes {
				node.Status = x1model.NodeOrProxyStatusToFakeDelete
			}
		}
	}
	if param.ShardCount == shardNum {
		resource.LoggerTask.Trace(ctx, "no need modify")
		return nil
	}

	if err := modifyProxies(ctx, app, param); err != nil {
		return fmt.Errorf("modify proxies error,err:%w", err)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		return fmt.Errorf("save app error,err:%w", err)
	}
	return nil
}

// addShards 在元数据中增加新分片
func addShards(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	maxClusterIdx, err := util.GetMaxClusterIdx(ctx, app.Clusters)
	if err != nil {
		return fmt.Errorf("get max cluster id fail,err:%w", err)
	}
	maxNodeIdx, err := util.GetMaxNodeIndex(ctx, app)
	if err != nil {
		return fmt.Errorf("get max node index fail,err:%w", err)
	}
	curCount := len(app.Clusters)
	firstCluster := app.Clusters[0]
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("to add %d shards", param.ShardCount-curCount))
	for i := 0; i < param.ShardCount-curCount; i++ {
		maxClusterIdx++
		cluster := &x1model.Cluster{
			ClusterId:       app.AppId + "-" + strconv.Itoa(maxClusterIdx),
			AppId:           app.AppId,
			Engine:          firstCluster.Engine,
			EngineVersion:   firstCluster.EngineVersion,
			Port:            firstCluster.Port,
			Status:          x1model.ClusterStatusInUse,
			CreateTime:      time.Now(),
			UpdateTime:      time.Now(),
			StoreType:       x1model.StoreTypeDRAM,
			Spec:            firstCluster.Spec,
			DestSpec:        firstCluster.DestSpec,
			Cpu:             firstCluster.Cpu,
			ActualCpu:       firstCluster.ActualCpu,
			MemSize:         firstCluster.MemSize,
			ActualMemSize:   firstCluster.ActualMemSize,
			DiskSize:        firstCluster.DiskSize,
			ActualDiskSize:  firstCluster.ActualDiskSize,
			SysDiskSize:     firstCluster.SysDiskSize,
			AvailableVolume: firstCluster.AvailableVolume,
		}
		app.Clusters = append(app.Clusters, cluster)
		resource.LoggerTask.Trace(ctx, fmt.Sprintf("add cluster %s", cluster.ClusterId))
		for _, node := range app.Clusters[0].Nodes {
			maxNodeIdx++
			cluster.Nodes = append(cluster.Nodes, &x1model.Node{
				NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
				ClusterId:     cluster.ClusterId,
				AppId:         app.AppId,
				Engine:        cluster.Engine,
				EngineVersion: cluster.EngineVersion,
				Port:          cluster.Port,
				Region:        firstCluster.Nodes[0].Region,
				LogicZone:     node.LogicZone,
				Azone:         node.Azone,
				Role:          node.Role,
				VpcId:         firstCluster.Nodes[0].VpcId,
				SubnetId:      node.SubnetId,
				Pool:          param.Pool,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       util.DefaultBaseDir,
			})
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("add node %s", cluster.Nodes[len(cluster.Nodes)-1].NodeId))
		}
	}
	return nil
}

func modifyProxies(ctx context.Context, app *x1model.Application, parameters *iface.Parameters) error {
	var curItfCount, targetItfCount int
	var itfs []*x1model.Interface
	/*	与Redis逻辑对齐，因为Mc的rep都是1，所以写死成2
		if len(replicas) > 1 {
			zoneCount = len(replicas)
		} else {
			zoneCount = 2
			proxyCntPerAz = 2
		}*/
	proxyCntPerAz := 2
	zoneCount := 2

	for _, itf := range app.Interfaces {
		if !strings.Contains(itf.InterfaceId, "-itfop-") {
			curItfCount++
			itfs = append(itfs, itf)
		}
	}
	resource.LoggerTask.Trace(ctx, fmt.Sprintf("current interfaces %s", base_utils.SelectColumn(itfs, "InterfaceId")))

	var replicas []*iface.Replica
	if err := json.Unmarshal([]byte(app.Replicas), &replicas); err != nil {
		return err
	}
	if len(replicas) != 1 {
		return fmt.Errorf("memcache only support 1 replica bus receive:%d", len(replicas))
	}
	totalProxyCount, err := buildmeta.GetTotalProxyCount(ctx, &buildmeta.GetTotalProxyCountParams{
		App:        app,
		Engine:     "redis", // memcache算proxy数量方法复用redis的算法
		ShardCount: parameters.ShardCount,
		NodeType:   parameters.NodeType,
		ForceSpecs: parameters.ForceSpecs,
		UserID:     app.UserId,
		VpcID:      app.VpcId,
	})
	if err != nil {
		return fmt.Errorf("get total proxy count fail,err:%w", err)
	}
	targetItfCount = totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		targetItfCount++
	}
	resource.LoggerTask.Trace(ctx, "change proxys",
		logit.String("curItfCount", strconv.Itoa(curItfCount)), logit.String("targetItfCount", strconv.Itoa(targetItfCount)),
		logit.String("zoneCount", strconv.Itoa(zoneCount)), logit.String("proxyCntPerAz", strconv.Itoa(proxyCntPerAz)))

	if targetItfCount > curItfCount {
		maxItfIdx, err := util.GetMaxInterfaceIdx(ctx, itfs)
		if err != nil {
			return fmt.Errorf("get max interface index failed,err:%w", err)
		}
		maxProxyIdx, err := util.GetMaxProxyIndex(ctx, app)
		if err != nil {
			return fmt.Errorf("get max proxy index failed,err:%w", err)
		}
		zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
		if err != nil {
			return fmt.Errorf("get zone map failed,err:%w", err)
		}
		firstItf := itfs[0]
		for i := 0; i < targetItfCount-curItfCount; i++ {
			maxItfIdx++
			itf := &x1model.Interface{
				InterfaceId:    app.AppId + "-itf-" + strconv.Itoa(maxItfIdx),
				AppId:          app.AppId,
				Engine:         x1model.EngineMcProxy,
				EngineVersion:  itfs[0].EngineVersion,
				Port:           app.Port,
				Status:         x1model.ClusterStatusInUse,
				CreateTime:     time.Now(),
				UpdateTime:     time.Now(),
				StoreType:      x1model.StoreTypeDRAM,
				Spec:           firstItf.Spec,
				DestSpec:       firstItf.DestSpec,
				Cpu:            firstItf.Cpu,
				ActualCpu:      firstItf.ActualCpu,
				MemSize:        firstItf.MemSize,
				ActualMemSize:  firstItf.ActualMemSize,
				DiskSize:       firstItf.DiskSize,
				ActualDiskSize: firstItf.ActualDiskSize,
				SysDiskSize:    firstItf.SysDiskSize,
			}
			app.Interfaces = append(app.Interfaces, itf)
			resource.LoggerTask.Trace(ctx, fmt.Sprintf("add interface %s", itf.InterfaceId))
			for _, replica := range replicas {
				for j := 0; j < proxyCntPerAz; j++ {
					maxProxyIdx++
					azone, found := zoneMapper(replica.Zone, true)
					if !found {
						resource.LoggerTask.Warning(ctx, fmt.Sprintf("not found azone for lzone %s", replica.Zone))
						return cerrs.Errorf("not found azone for lzone %s", replica.Zone)
					}
					itf.Proxys = append(itf.Proxys, &x1model.Proxy{
						ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyIdx),
						InterfaceId:   itf.InterfaceId,
						AppId:         app.AppId,
						Engine:        itf.Engine,
						EngineVersion: itf.EngineVersion,
						Port:          itf.Port,
						Region:        itfs[0].Proxys[0].Region,
						LogicZone:     replica.Zone,
						Azone:         azone,
						VpcId:         itfs[0].Proxys[0].VpcId,
						SubnetId:      replica.SubnetIDs[0],
						Status:        x1model.NodeOrProxyStatusToCreate,
						Basedir:       util.DefaultBaseDir,
						McpackPort:    itf.Port + 1,
						StatPort:      22222,
					})
					resource.LoggerTask.Trace(ctx, fmt.Sprintf("add proxy %s", itf.Proxys[len(itf.Proxys)-1].ProxyId))
				}
			}
		}
	} else if targetItfCount < curItfCount {
		for i := 0; i < curItfCount-targetItfCount; i++ {
			itf := itfs[len(itfs)-1-i]
			for _, proxy := range itf.Proxys {
				proxy.Status = x1model.NodeOrProxyStatusToDelete
			}
		}
	}
	return nil
}
