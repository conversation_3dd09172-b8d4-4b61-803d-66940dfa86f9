/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/24
 * File: buildmeta_create.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/zone"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/buildmeta"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/iface"
	"icode.baidu.com/baidu/scs/x1-task/processors/redis/util"
	"icode.baidu.com/baidu/scs/x1-task/utils/conf"
)

func ProcessBuildMetaForCreate(ctx context.Context, teu *workflow.TaskExecUnit) error {
	param, err := iface.GetParameters(ctx, teu.Parameters)
	if err != nil {
		return fmt.Errorf("parse param fail,err:%w", err)
	}
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	// 初始化Application
	if err := fillMemcacheApp(ctx, app, param); err != nil {
		return fmt.Errorf("fill app fail,err:%w", err)
	}
	zoneMapper, err := zone.ZoneOp().GetZoneMap(ctx, app.UserId)
	if err != nil {
		return fmt.Errorf("get zoneMapper,err:%w", err)
	}
	// 初始化Proxy
	if err := fillMemcacheProxies(ctx, app, param, zoneMapper); err != nil {
		return fmt.Errorf("fill proxy fail,err:%w", err)
	}
	// 初始化Cluster
	if err := fillMemcacheClustersAndNodes(ctx, app, param, zoneMapper); err != nil {
		return fmt.Errorf("fill cluster fail,err:%w", err)
	}
	// 初始化BLB
	if err := fillMemcacheBlbs(ctx, app, param); err != nil {
		return fmt.Errorf("fill blb fail,err:%w", err)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		return fmt.Errorf("save app fail,err:%w", err)
	}
	return nil
}

func fillMemcacheApp(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	app.AppName = param.Name
	app.ImageID = conf.MemcacheConf.Image.Bcc
	app.Product = x1model.ProductSCS
	app.UserId = param.UserID
	app.VpcId = param.VPCID
	app.Type = x1model.EngineMc
	app.Port = param.ServicePortList[0]
	app.McpackPort = param.ServicePortList[1]
	app.InnerPort = param.ServicePortList[2]
	app.Domain = param.Domain
	app.AppShortID = param.ClusterShortID
	app.Region = param.Region
	app.DeploySetIds = strings.Join(param.DeployIDSet, ",")
	if len(param.Replicas) != 1 {
		return fmt.Errorf("mc only support single replica,but receive params len:%d", len(param.Replicas))
	}
	rss, err := json.Marshal(param.Replicas)
	if err != nil {
		return fmt.Errorf("marshal replicas failed,err:%w", err)
	}
	app.Replicas = string(rss)
	return nil
}

func fillMemcacheProxies(ctx context.Context, app *x1model.Application, parameters *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Interfaces) > 0 {
		resource.LoggerTask.Trace(ctx, "interfaces has been created")
		return nil
	}
	// Proxy分布策略如下
	// Proxy总数量与ShardCount等同
	// InterfaceCount * 可用区数量（当可用区数量为1时，视为2） = Proxy总数量,
	// 当可用区数量为1时，一个Interface中有2个Proxy，当多个可用区时，Interface中Proxy数量与可用区数量相同
	// 当出现不能整除时，向上去整
	totalProxyCount, err := buildmeta.GetTotalProxyCount(ctx, &buildmeta.GetTotalProxyCountParams{
		App:        app,
		Engine:     "redis", // memcache算proxy数量方法复用redis的算法
		ShardCount: parameters.ShardCount,
		NodeType:   parameters.NodeType,
		ForceSpecs: parameters.ForceSpecs,
		UserID:     app.UserId,
		VpcID:      app.VpcId,
	})
	if err != nil {
		return fmt.Errorf("get total proxy count fail,err:%w", err)
	}
	resource.LoggerTask.Trace(ctx, "get memcache proxy total count success", logit.Int("count", totalProxyCount))

	proxyCountPerAz := 1
	zoneCount := len(parameters.Replicas)
	if zoneCount < 2 {
		proxyCountPerAz = 2
		zoneCount = 2
	}
	itfCount := totalProxyCount / zoneCount
	if totalProxyCount%zoneCount != 0 {
		itfCount++
	}

	for i := 0; i < itfCount; i++ {
		app.Interfaces = append(app.Interfaces, &x1model.Interface{
			InterfaceId:   app.AppId + "-itf-" + strconv.Itoa(i),
			AppId:         app.AppId,
			Engine:        x1model.EngineMcProxy,
			EngineVersion: parameters.EngineVersion,
			Port:          app.Port,
			Status:        x1model.ClusterStatusInUse,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			DestSpec:      "proxy.n1.small",
			StoreType:     x1model.StoreTypeDRAM,
		})
	}

	maxProxyId := 0
	for _, itf := range app.Interfaces {
		for _, replica := range parameters.Replicas {
			azone, found := zoneMapper(replica.Zone, true)
			if !found {
				return fmt.Errorf("not found azone for lzone %s", replica.Zone)
			}
			for i := 0; i < proxyCountPerAz; i++ {
				maxProxyId++
				itf.Proxys = append(itf.Proxys, &x1model.Proxy{
					ProxyId:       itf.InterfaceId + "." + strconv.Itoa(maxProxyId),
					InterfaceId:   itf.InterfaceId,
					AppId:         app.AppId,
					Engine:        itf.Engine,
					EngineVersion: itf.EngineVersion,
					Port:          itf.Port,
					Region:        parameters.Region,
					LogicZone:     replica.Zone,
					Azone:         azone,
					VpcId:         parameters.VPCID,
					SubnetId:      replica.SubnetIDs[0],
					Status:        x1model.NodeOrProxyStatusToCreate,
					Basedir:       util.DefaultBaseDir,
					McpackPort:    itf.Port + 1,
					StatPort:      22222,
				})
			}
		}
	}
	return nil
}

func fillMemcacheClustersAndNodes(ctx context.Context, app *x1model.Application, param *iface.Parameters, zoneMapper zone.ZoneMapperFunc) error {
	if len(app.Clusters) > 0 {
		resource.LoggerTask.Trace(ctx, "clusters has been created")
		return nil
	}
	port := param.ServicePortList[0]
	if len(app.Interfaces) > 0 {
		port = param.ServicePortList[2]
	}
	for i := 0; i < param.ShardCount; i++ {
		app.Clusters = append(app.Clusters, &x1model.Cluster{
			ClusterId:     app.AppId + "-" + strconv.Itoa(i),
			AppId:         app.AppId,
			Engine:        param.Engine,
			EngineVersion: param.EngineVersion,
			Port:          port,
			Status:        x1model.ClusterStatusInUse,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			DestSpec:      param.NodeType,
			StoreType:     x1model.StoreTypeDRAM,
		})
	}
	maxNodeIdx := 0
	for _, cluster := range app.Clusters {
		if len(param.Replicas) != 1 {
			return fmt.Errorf("mc only support single replica,but receive params len:%d", len(param.Replicas))
		}
		replica := param.Replicas[0]
		azone, found := zoneMapper(replica.Zone, true)
		if !found {
			return fmt.Errorf("not found azone for lzone %s", replica.Zone)
		}
		for i := 0; i < replica.Count; i++ {
			cluster.Nodes = append(cluster.Nodes, &x1model.Node{
				NodeId:        cluster.ClusterId + "." + strconv.Itoa(maxNodeIdx),
				ClusterId:     cluster.ClusterId,
				AppId:         app.AppId,
				Engine:        cluster.Engine,
				EngineVersion: cluster.EngineVersion,
				Port:          cluster.Port,
				Region:        param.Region,
				LogicZone:     replica.Zone,
				Azone:         azone,
				Role:          "master",
				VpcId:         param.VPCID,
				SubnetId:      replica.SubnetIDs[0],
				Pool:          param.Pool,
				Status:        x1model.NodeOrProxyStatusToCreate,
				Basedir:       util.DefaultBaseDir,
			})
			maxNodeIdx++
		}
	}
	return nil
}

func fillMemcacheBlbs(ctx context.Context, app *x1model.Application, param *iface.Parameters) error {
	if len(app.BLBs) > 0 {
		resource.LoggerTask.Trace(ctx, "blb has been created")
		return nil
	}
	app.BLBs = append(app.BLBs, &x1model.BLB{
		AppId:             app.AppId,
		Name:              app.AppId,
		VpcId:             app.VpcId,
		SubnetId:          param.BlbSubnetID,
		Type:              x1model.BLBTypeApp,
		IpType:            x1model.Ipv4,
		BgwGroupId:        param.BgwGroup.BgwGroupId,
		BgwGroupMode:      param.BgwGroup.BgwGroupMode,
		BgwGroupExclusive: param.BgwGroup.BgwGroupExclusive,
		MasterAZ:          param.BgwGroup.MasterAZ,
		SlaveAZ:           param.BgwGroup.SlaveAZ,
	})
	return nil
}
