/*
 * Copyright(C) 2023 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2023/10/25
 * File: fillspec.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package memcache TODO package function desc
package memcache

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/scs/x1-base/component/specification"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
	"icode.baidu.com/baidu/scs/x1-base/task/workflow"
	"icode.baidu.com/baidu/scs/x1-base/utils/base_utils"

	"icode.baidu.com/baidu/scs/x1-task/library/resource"
)

var McProxySpec = specification.Specification{
	AvailableVolume:      1,
	Name:                 "proxy.n1.small",
	CPUCount:             1,
	MemoryCapacityInGB:   1,
	RootDiskCapacityInGB: 20,
	DataDiskCapacityInGB: 20,
}

func ProcessFillMcSpec(ctx context.Context, teu *workflow.TaskExecUnit) error {
	app, err := x1model.ApplicationGetByAppId(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get appinfo fail,err:%w", err)
	}
	nodeSpec, err := GetMcNodeSpec(ctx, teu.Entity)
	if err != nil {
		return fmt.Errorf("get spec fail,err:%w", err)
	}
	resource.LoggerTask.Trace(ctx, "get spec success", logit.String("spec", base_utils.Format(nodeSpec)))

	for _, cluster := range app.Clusters {
		cluster.AvailableVolume = nodeSpec.AvailableVolume
		cluster.Cpu = nodeSpec.CPUCount
		cluster.MemSize = nodeSpec.MemoryCapacityInGB
		cluster.SysDiskSize = nodeSpec.RootDiskCapacityInGB
		cluster.DiskSize = int64(nodeSpec.DataDiskCapacityInGB)
	}

	for _, itf := range app.Interfaces {
		itf.AvailableVolume = McProxySpec.AvailableVolume
		itf.Cpu = McProxySpec.CPUCount
		itf.MemSize = McProxySpec.MemoryCapacityInGB
		itf.SysDiskSize = McProxySpec.RootDiskCapacityInGB
		itf.DiskSize = int64(McProxySpec.DataDiskCapacityInGB)
	}
	if err := x1model.ApplicationsSave(ctx, []*x1model.Application{app}); err != nil {
		return fmt.Errorf("save app fail,err:%w", err)
	}
	return nil
}

// GetMcNodeSpec 节点规格计算
// 根据公式计算：
// flavor->set_memory((instance_size + 1 + instance_size / 16) * 1024);
// flavor->set_disk(instance_size * 2 + _memcache_v1_conf->memcache_disk_size());
// 结果：
// flavor:1 , mem:2 , disk:22
// flavor:2 , mem:3 , disk:24
// flavor:4 , mem:5 , disk:28
// flavor:8 , mem:9 , disk:36
// flavor:16 , mem:18 , disk:52
// flavor:32 , mem:35 , disk:84
// flavor:64 , mem:69 , disk:148
// 根据组内讨论，磁盘统一成20GB
func GetMcNodeSpec(ctx context.Context, appID string) (*specification.Specification, error) {
	flavor, err := GetMcFlavor(ctx, appID)
	if err != nil {
		return nil, err
	}
	// BCCS_CSMASTER_ERROR_NO OpenStackSDK::find_closest_bcc_flavor
	// 用stl的lower_bound找最接近的，先找内存，再找核
	// 合法配置：https://console.cloud.baidu-int.com/devops/icode/repos/baidu/bce-conf/scs_newcsmaster/blob/master/online-bj01/openstack_sdk.conf
	var flavorMemMap = map[int]int{
		1:  2,  // 2c2g ok
		2:  4,  // 2c3g => 2c4g
		4:  8,  // 2c5g => 2c8g
		8:  12, // 2c9g => 2c12g
		16: 24, // 2c18g => 2c24g
		32: 48, // 2c35g => 2c48g
		64: 96, // 2c69g => 2c96g
	}
	mem, ok := flavorMemMap[flavor]
	if !ok {
		return nil, fmt.Errorf("un support flavor :%d", flavor)
	}

	return &specification.Specification{
		AvailableVolume:      flavor,
		CPUCount:             2,
		MemoryCapacityInGB:   mem,
		RootDiskCapacityInGB: 20,
		DataDiskCapacityInGB: 20,
	}, nil
}

func GetMcFlavor(ctx context.Context, appID string) (int, error) {
	cacheCluster, err := resource.CsmasterOpAgent.GetClusterModelByAppId(ctx, appID)
	if err != nil {
		return 0, fmt.Errorf("get cache cluster info fail,err:%w", err)
	}
	// flaver只记录了单节点的
	return cast.ToInt(cacheCluster.Flavor.Int32), nil
}
