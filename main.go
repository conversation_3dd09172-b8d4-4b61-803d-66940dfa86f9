// Copyright(C) 2021 Baidu Inc. All Rights Reserved.
// Code Generated By 'gdp' At 2021-11-30, You Can EDIT.
// App Using GDP Framework: http://gdp.baidu-int.com/ .

package main

import (
	"context"
	"flag"
	"log"
	"net/http"
	_ "net/http/pprof"

	_ "icode.baidu.com/baidu/gdp/automaxprocs"

	"icode.baidu.com/baidu/scs/x1-task/bootstrap"
	_ "icode.baidu.com/baidu/scs/x1-task/workflows/memcache"
	_ "icode.baidu.com/baidu/scs/x1-task/workflows/redis"
)

var appConfig = flag.String("conf", "./conf/app.toml", "app config file")

func main() {
	flag.Parse()

	config := bootstrap.MustLoadAppConfig(*appConfig)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		_ = http.ListenAndServe("localhost:6060", nil)
	}()

	bootstrap.MustInit(ctx)

	log.Println("server exit:", bootstrap.Start(ctx, config))
}
