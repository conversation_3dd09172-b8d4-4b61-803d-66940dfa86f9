# Suggested Commands for x1-task Development

## Build Commands
```bash
make build          # Build the application
go build -o bin/x1-task  # Alternative build command
```

## Testing Commands
```bash
make test           # Run tests with race detection and coverage
./runtest.sh        # Full test with Redis server setup
go test -race -v -cover $(go list ./...| grep -vE "vendor")  # Direct test command
```

## Running the Application
```bash
go run main.go                         # Run with default config
go run main.go -conf conf/app.toml     # Run with dev config
go run main.go -conf conf_qa/app.toml  # Run with QA config
```

## Package and Deployment
```bash
make package        # Create deployment package in output/
make clean          # Clean build artifacts
```

## Development Utilities
```bash
make prepare        # Download dependencies
make set-env        # Download Go dependencies
go mod download -x  # Download Go modules
```

## GDP Framework Commands
```bash
go get icode.baidu.com/baidu/gdp/...  # Update all GDP components to latest
```

## System Commands (Darwin/macOS)
- git (version >= 2.17.1 required)
- ls, cd, grep, find (standard macOS versions)
- tree (for viewing directory structure)