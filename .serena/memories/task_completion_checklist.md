# Task Completion Checklist

When completing a development task in x1-task, ensure:

## Code Quality
1. **Format code**: Run go_fmt tool for proper formatting
2. **Follow style guide**: Ensure compliance with Baidu Go Style Guide
3. **Add tests**: Write unit tests for new functionality

## Testing
1. **Run tests locally**: 
   ```bash
   ./runtest.sh  # Full test with Redis
   # or
   make test     # Standard test suite
   ```
2. **Verify race conditions**: Tests include -race flag
3. **Check coverage**: Ensure adequate test coverage

## Build Verification
1. **Build the project**: `make build`
2. **Package if needed**: `make package` for deployment artifacts

## GDP2 Framework Compliance
1. **Use GDP2 components** for logging, configuration, networking
2. **Follow bootstrap patterns** for initialization
3. **Support hot restart** if modifying server components

## Documentation
1. **Update comments** in code
2. **Update CLAUDE.md** if architecture changes significantly

## Git Workflow
1. **Commit with clear messages**
2. **Follow branch naming conventions**
3. **Ensure CI passes** (check ci.yml requirements)