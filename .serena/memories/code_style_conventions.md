# Code Style and Conventions

## Go Code Standards
- Follow [Baidu Go Style Guide](http://gdp.baidu-int.com/go_style_guide)
- Minimum Go version: 1.21
- Use [go_fmt](https://github.com/fsgo/go_fmt) for code formatting (formats comments and imports according to standards)

## Project Structure Conventions
- **workflows/**: High-level business logic for Redis and Memcache operations
- **processors/**: Low-level implementation details, organized by service type
- **bootstrap/**: Application initialization and configuration
- **library/**: Shared utilities and types
- **backend/**: Data persistence layer
- **utils/**: Configuration utilities and helpers

## Naming Conventions
- Go standard naming (camelCase for exported, lowercase for internal)
- Test files follow *_test.go convention
- Configuration files use TOML format

## Configuration Management
- Development config: conf/
- QA config: conf_qa/
- Online/Production config: conf_online/
- Templates stored in processors/redis/newagent/templates/

## Testing Requirements
- All code must have corresponding unit tests
- Tests must pass with race detection enabled
- Coverage reporting is mandatory
- External dependencies (like Redis) handled via runtest.sh

## GDP2 Framework Patterns
- Use GDP2 components for logging (logit), configuration (conf), networking (net)
- Support hot restart via Hestia component
- Follow GDP2 initialization patterns in bootstrap/