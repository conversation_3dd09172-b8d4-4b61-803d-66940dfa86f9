# X1-Task Project Overview

## Purpose
x1-task is a Go-based task management system for Baidu's SCS (Smart Cloud Services), built on the GDP2 framework. It manages Redis and Memcache cluster operations including:
- Cluster creation, deletion, and scaling
- Backup and recovery operations
- High availability and failover management
- Configuration management
- Cross-availability-zone optimizations
- Lifecycle management for Redis and Memcache clusters

## Tech Stack
- **Language**: Go 1.21+
- **Framework**: Baidu GDP2 (http://gdp.baidu-int.com/)
- **Key Libraries**:
  - Redis client: go-redis/redis/v8
  - Web framework: gin-gonic/gin
  - ORM: gorm.io/gorm
  - CLI: urfave/cli/v2
  - Internal Baidu components: x1-base, x1-api, gdp/*

## Architecture
- **Workflow-Processor Pattern**: High-level workflows orchestrate low-level processors
- **Service Integration**: BCC, BLB, BNS, VPC, Security Groups, DNS, BCM, IAM, STS
- **Configuration**: TOML-based with GDP2 framework integration
- **Hot restart**: Supported via He<PERSON><PERSON> (GDP2 component)