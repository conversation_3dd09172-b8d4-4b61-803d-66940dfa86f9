# x1-task

x1-task 是百度智能云 SCS（Smart Cloud Services）的任务管理系统，基于 GDP2 框架开发。主要负责 Redis 和 Memcache 集群的生命周期管理，包括集群创建、扩缩容、备份恢复、故障自愈等运维任务的编排和执行。

项目基于 GDP2 框架开发：http://gdp.baidu-int.com/

## 项目特性

- **多服务支持**：支持 Redis 和 Memcache 集群管理
- **任务编排**：基于 workflow-processor 模式的任务编排系统
- **跨可用区**：支持跨可用区就近任务调度和故障转移
- **自动化运维**：集群自愈、备份恢复、配置管理等自动化操作
- **多环境配置**：支持开发、测试、生产多环境配置管理

## 系统要求

- Go 版本：1.21+
- 依赖：GDP2 框架及相关组件

## 快速开始

### 构建项目

```bash
# 使用 Makefile 构建
make build

# 或直接使用 go build
go build -o bin/x1-task
```

### 配置文件说明

- `conf/`：配置根目录

主要配置文件：
- `conf/app.toml`：应用主配置
- `conf/port.conf`：端口配置
- `conf/component/`：组件配置目录
- `conf/servicer/`：服务配置目录

### 运行应用

```bash
# 使用默认配置运行
go run main.go

# 指定配置文件运行
go run main.go -conf conf/app.toml

```

### 测试

```bash
# 快速测试
make test

# 完整测试（包含 Redis 服务器启动）
./runtest.sh

# 手动运行测试
go test -race -v -cover ./...
```

### 打包部署

```bash
# 打包到 output 目录
make package

# 清理构建产物
make clean
```

## 项目架构

### 核心组件

- **bootstrap/**：应用初始化和配置加载
- **workflows/**：高级任务编排层，定义业务流程
  - `redis/`：Redis 相关工作流
  - `memcache/`：Memcache 相关工作流
- **processors/**：具体操作执行层，实现各种运维操作
  - `redis/`：Redis 集群操作处理器
  - `memcache/`：Memcache 集群操作处理器
- **backend/**：数据持久化层
- **utils/**：工具类和配置管理
- **library/**：共享库和类型定义

### 关键设计模式

**任务编排模式**：
- Workflows 定义高级业务操作流程
- Processors 实现具体的运维操作步骤
- 支持任务状态管理和错误重试

**多环境配置**：
- 基于 TOML 的配置管理
- 组件化配置设计
- 环境隔离和配置继承

## 主要功能

### Redis 管理
- 集群创建、删除、扩缩容
- 主从切换和故障转移
- 备份恢复和数据迁移
- 配置管理和模板渲染
- 跨可用区部署和就近调度

### Memcache 管理
- 集群生命周期管理
- 分片管理和迁移
- 自愈和故障处理

### 运维特性
- 自动故障检测和恢复
- 配置热更新
- 监控数据采集
- 安全组和网络管理

## 开发指南

### 代码规范
* 遵循[百度 Go 代码规范](http://gdp.baidu-int.com/go_style_guide)
* 使用推荐的代码格式化工具：[go_fmt](https://github.com/fsgo/go_fmt)
* 编写对应的单元测试
* 确保代码格式化完成

### 依赖管理
```bash
# 更新 GDP 组件到最新版本
go get icode.baidu.com/baidu/gdp/...

# 下载依赖
go mod download
```

### 热重启功能
如需使用 GDP2 框架的热重启功能，参考文档：
http://wiki.baidu.com/pages/viewpage.action?pageId=1370968292

配置修改：
1. 设置 `conf_online/app.toml.template` 中的 `EnableHestia = true`
2. 使用对应的 pandora 配置文件
